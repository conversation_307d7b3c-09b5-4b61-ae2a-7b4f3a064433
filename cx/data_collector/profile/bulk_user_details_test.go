package profile

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	bcPb "github.com/epifi/gamma/api/bankcust"
	mocksBC "github.com/epifi/gamma/api/bankcust/mocks"
	profilePb "github.com/epifi/gamma/api/cx/data_collector/profile"
	"github.com/epifi/gamma/api/kyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendormapping"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	mockVm "github.com/epifi/gamma/api/vendormapping/mocks"
	mockHelper2 "github.com/epifi/gamma/cx/test/mocks/data_collector/helper"
	mockHelper "github.com/epifi/gamma/cx/test/mocks/helper"
)

var (
	UserDetails1 = UserDetailsMap{
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACTOR_ID:                           "actorId",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_NUMBER:                     "AccountNumber",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_CUSTOMER_ID:                        "CustomerId",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_EMAIL_ID:                           "email@abc",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_FCM_ID:                             "FcmId",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_NAME:                               "Name",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_KYC_LEVEL:                          "FULL_KYC",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_PHONE_NUMBER:                       "+91**********",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED: "YES",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME:     "13-May-2022 17:39:06",
	}
	UserDetails2 = UserDetailsMap{
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACTOR_ID:                           "actorId",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_NUMBER:                     "AccountNumber",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_CUSTOMER_ID:                        "CustomerId",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_EMAIL_ID:                           "email@abc",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_FCM_ID:                             "Error while fetching FCM ID",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_NAME:                               "Name",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_KYC_LEVEL:                          "FULL_KYC",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_PHONE_NUMBER:                       "+91**********",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED: "Error while checking if account closure allowed",
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME:     "13-May-2022 17:39:06",
	}
	requiredAllColsMap = map[profilePb.BulkUserInfoField]bool{
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACTOR_ID:                           true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_NUMBER:                     true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_CUSTOMER_ID:                        true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_EMAIL_ID:                           true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_FCM_ID:                             true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_NAME:                               true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_KYC_LEVEL:                          true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_PHONE_NUMBER:                       true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED: true,
		profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME:     true,
	}
)

func TestService_GetUserDetailsForActorID(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockCustomerIdentifier := mockHelper.NewMockICustomerIdentifier(ctr)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
	mockVendorMappingClient := mockVm.NewMockVendorMappingServiceClient(ctr)
	mockDataCollectorHelper := mockHelper2.NewMockIDataCollectorHelper(ctr)
	mockBcClient := mocksBC.NewMockBankCustomerServiceClient(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks              []interface{}
		ctx                context.Context
		actorId            string
		requiredColumnsMap map[profilePb.BulkUserInfoField]bool
	}

	tests := []struct {
		name     string
		args     args
		want     UserDetailsMap
		wantFail *profilePb.FailureID
	}{
		{
			name: "error while fetching user by actor",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUserByActorId(context.Background(), "actorId").
						Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				actorId:            "actorId",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_ActorId{ActorId: "actorId"}},
				FailureReason: FailureReasonErrorWhileFetchingUserDetails,
			},
		},
		{
			name: "error while fetching account details",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUserByActorId(context.Background(), "actorId").
						Return(&userPb.User{Id: "userId"}, nil),
					mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
						Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				actorId:            "actorId",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_ActorId{ActorId: "actorId"}},
				FailureReason: FailureReasonErrorWhileFetchingAccountDetails,
			},
		},
		{
			name: "error while getting customer info from actorId",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUserByActorId(context.Background(), "actorId").
						Return(&userPb.User{Id: "userId"}, nil),
					mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
						Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber"}}, nil),
					mockBcClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).
						Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				actorId:            "actorId",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_ActorId{ActorId: "actorId"}},
				FailureReason: FailureReasonErrorWhileFetchingCustomerDetails,
			},
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUserByActorId(context.Background(), "actorId").
						Return(&userPb.User{Id: "userId",
							Profile: &userPb.Profile{Email: "email@abc", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, KycName: &commontypes.Name{FirstName: "Name"}}}, nil),
					mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
						Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber",
							CreationInfo: &savingsPb.AccountCreationInfo{VendorCreationSucceededAt: &timestampPb.Timestamp{Seconds: **********}}}}, nil),
					mockVendorMappingClient.EXPECT().GetBEMappingById(context.Background(), &vendormapping.GetBEMappingByIdRequest{Id: "actorId"}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpcPb.StatusOk(), FcmId: "FcmId"}, nil),
					mockBcClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).Return(&bcPb.GetBankCustomerResponse{
						Status: rpcPb.StatusOk(),
						BankCustomer: &bcPb.BankCustomer{
							VendorCustomerId: "CustomerId",
							ActorId:          "actorId",
							Vendor:           commonvgpb.Vendor_FEDERAL_BANK,
							DedupeInfo:       &bcPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
						},
					}, nil),
					mockDataCollectorHelper.EXPECT().IsSavingsAccountClosureAllowed(context.Background(), "userId", "actorId").
						Return(true, nil, nil),
				},
				ctx:                context.Background(),
				actorId:            "actorId",
				requiredColumnsMap: requiredAllColsMap,
			},
			want:     UserDetails1,
			wantFail: nil,
		},
		{
			name: "success but failed to populate fcm id and savings account closure info",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUserByActorId(context.Background(), "actorId").
						Return(&userPb.User{Id: "userId",
							Profile: &userPb.Profile{Email: "email@abc", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, KycName: &commontypes.Name{FirstName: "Name"}}}, nil),
					mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
						Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber",
							CreationInfo: &savingsPb.AccountCreationInfo{VendorCreationSucceededAt: &timestampPb.Timestamp{Seconds: **********}}}}, nil),
					mockVendorMappingClient.EXPECT().GetBEMappingById(context.Background(), &vendormapping.GetBEMappingByIdRequest{Id: "actorId"}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpcPb.StatusInternal()}, nil),
					mockBcClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).Return(&bcPb.GetBankCustomerResponse{
						Status: rpcPb.StatusOk(),
						BankCustomer: &bcPb.BankCustomer{
							VendorCustomerId: "CustomerId",
							ActorId:          "actorId",
							Vendor:           commonvgpb.Vendor_FEDERAL_BANK,
							DedupeInfo:       &bcPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
						},
					}, nil),
					mockDataCollectorHelper.EXPECT().IsSavingsAccountClosureAllowed(context.Background(), "userId", "actorId").
						Return(true, nil, errors.New("mock error")),
				},
				ctx:                context.Background(),
				actorId:            "actorId",
				requiredColumnsMap: requiredAllColsMap,
			},
			want:     UserDetails2,
			wantFail: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mockSavingsClient, nil, nil, nil,
				nil, nil, mockVendorMappingClient, mockCustomerIdentifier, nil, mockDataCollectorHelper, nil, nil, nil, mockBcClient, nil, nil, nil, nil)
			gotDetails, gotFail := s.GetUserDetailsForActorID(tt.args.ctx, tt.args.actorId, tt.args.requiredColumnsMap)
			if !reflect.DeepEqual(gotFail, tt.wantFail) {
				t.Errorf("GetUserDetailsForActorID() gotFail = %v, wantFail %v", gotFail, tt.wantFail)
				return
			}
			if !reflect.DeepEqual(gotDetails, tt.want) {
				t.Errorf("GetUserDetailsForActorID() gotDetails = %v, want %v", gotDetails, tt.want)
			}
		})
	}
}

func TestService_GetUserDetailsForAccountNumber(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockCustomerIdentifier := mockHelper.NewMockICustomerIdentifier(ctr)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
	mockVendorMappingClient := mockVm.NewMockVendorMappingServiceClient(ctr)
	mockDataCollectorHelper := mockHelper2.NewMockIDataCollectorHelper(ctr)
	mockBCClient := mocksBC.NewMockBankCustomerServiceClient(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks              []interface{}
		ctx                context.Context
		accountId          string
		requiredColumnsMap map[profilePb.BulkUserInfoField]bool
	}

	tests := []struct {
		name     string
		args     args
		want     UserDetailsMap
		wantFail *profilePb.FailureID
	}{
		{
			name: "error while fetching account details 1",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(context.Background(), &savingsPb.GetAccountRequest{
						Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
							AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
								PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
								AccountNumber: "AccountNumber",
							},
						},
					}).Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				accountId:          "AccountNumber",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_AccountId{AccountId: "AccountNumber"}},
				FailureReason: FailureReasonErrorWhileFetchingAccountDetails,
			},
		},
		{
			name: "error while fetching account details 2 - account nil",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(context.Background(), &savingsPb.GetAccountRequest{
						Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
							AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
								PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
								AccountNumber: "AccountNumber",
							},
						},
					}).Return(&savingsPb.GetAccountResponse{}, nil),
				},
				ctx:                context.Background(),
				accountId:          "AccountNumber",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_AccountId{AccountId: "AccountNumber"}},
				FailureReason: FailureReasonErrorWhileFetchingAccountDetails,
			},
		},
		{
			name: "error while fetching user by email ID / phone number",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(context.Background(), &savingsPb.GetAccountRequest{
						Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
							AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
								PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
								AccountNumber: "AccountNumber",
							},
						},
					}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber", EmailId: "email@abc",
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}}}, nil),
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), gomock.Any(), gomock.Any()).
						Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				accountId:          "AccountNumber",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_AccountId{AccountId: "AccountNumber"}},
				FailureReason: FailureReasonErrorWhileFetchingUserDetails,
			},
		},
		{
			name: "error while fetching actor by user ID",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(context.Background(), &savingsPb.GetAccountRequest{
						Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
							AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
								PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
								AccountNumber: "AccountNumber",
							},
						},
					}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber", EmailId: "email@abc",
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}}}, nil),
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "email@abc", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{Id: "userId"}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				accountId:          "AccountNumber",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_AccountId{AccountId: "AccountNumber"}},
				FailureReason: FailureReasonErrorWhileFetchingActorDetails,
			},
		},
		{
			name: "error while getting customer info from actorId",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(context.Background(), &savingsPb.GetAccountRequest{
						Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
							AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
								PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
								AccountNumber: "AccountNumber",
							},
						},
					}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber", EmailId: "email@abc",
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}}}, nil),
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "email@abc", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{Id: "userId"}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(&types.Actor{Id: "actorId"}, nil),
					mockBCClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).Return(&bcPb.GetBankCustomerResponse{}, errors.New("fail")),
				},
				ctx:                context.Background(),
				accountId:          "AccountNumber",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_AccountId{AccountId: "AccountNumber"}},
				FailureReason: FailureReasonErrorWhileFetchingCustomerDetails,
			},
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(context.Background(), &savingsPb.GetAccountRequest{
						Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
							AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
								PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
								AccountNumber: "AccountNumber",
							},
						},
					}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber", EmailId: "email@abc",
						PhoneNumber:  &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						CreationInfo: &savingsPb.AccountCreationInfo{VendorCreationSucceededAt: &timestampPb.Timestamp{Seconds: **********}}}}, nil),
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "email@abc", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{
							Id:      "userId",
							Profile: &userPb.Profile{Email: "email@abc", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, KycName: &commontypes.Name{FirstName: "Name"}}}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(&types.Actor{Id: "actorId"}, nil),
					mockBCClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).Return(&bcPb.GetBankCustomerResponse{
						Status: rpcPb.StatusOk(),
						BankCustomer: &bcPb.BankCustomer{
							VendorCustomerId: "CustomerId",
							ActorId:          "actorId",
							DedupeInfo:       &bcPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
						},
					}, nil),
					mockVendorMappingClient.EXPECT().GetBEMappingById(context.Background(), &vendormapping.GetBEMappingByIdRequest{Id: "actorId"}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpcPb.StatusOk(), FcmId: "FcmId"}, nil),
					mockDataCollectorHelper.EXPECT().IsSavingsAccountClosureAllowed(context.Background(), "userId", "actorId").
						Return(true, nil, nil),
				},
				ctx:                context.Background(),
				accountId:          "AccountNumber",
				requiredColumnsMap: requiredAllColsMap,
			},
			want:     UserDetails1,
			wantFail: nil,
		},
		{
			name: "success but failed to populate fcm id and savings account closure info",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(context.Background(), &savingsPb.GetAccountRequest{
						Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
							AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
								PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
								AccountNumber: "AccountNumber",
							},
						},
					}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber", EmailId: "email@abc",
						PhoneNumber:  &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
						CreationInfo: &savingsPb.AccountCreationInfo{VendorCreationSucceededAt: &timestampPb.Timestamp{Seconds: **********}}}}, nil),
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "email@abc", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{
							Id:      "userId",
							Profile: &userPb.Profile{Email: "email@abc", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, KycName: &commontypes.Name{FirstName: "Name"}}}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(&types.Actor{Id: "actorId"}, nil),
					mockBCClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).Return(&bcPb.GetBankCustomerResponse{
						Status: rpcPb.StatusOk(),
						BankCustomer: &bcPb.BankCustomer{
							VendorCustomerId: "CustomerId",
							ActorId:          "actorId",
							DedupeInfo:       &bcPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
						},
					}, nil),
					mockVendorMappingClient.EXPECT().GetBEMappingById(context.Background(), &vendormapping.GetBEMappingByIdRequest{Id: "actorId"}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpcPb.StatusInternal()}, nil),
					mockDataCollectorHelper.EXPECT().IsSavingsAccountClosureAllowed(context.Background(), "userId", "actorId").
						Return(true, nil, errors.New("mock")),
				},
				ctx:                context.Background(),
				accountId:          "AccountNumber",
				requiredColumnsMap: requiredAllColsMap,
			},
			want:     UserDetails2,
			wantFail: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mockSavingsClient, nil, nil, nil,
				nil, nil, mockVendorMappingClient, mockCustomerIdentifier, nil, mockDataCollectorHelper, nil, nil, nil, mockBCClient, nil, nil, nil, nil)
			gotDetails, gotFail := s.GetUserDetailsForAccountNumber(tt.args.ctx, tt.args.accountId, tt.args.requiredColumnsMap)
			if !reflect.DeepEqual(gotFail, tt.wantFail) {
				t.Errorf("GetUserDetailsForAccountID() gotFail = %v, wantFail %v", gotFail, tt.wantFail)
				return
			}
			if !reflect.DeepEqual(gotDetails, tt.want) {
				t.Errorf("GetUserDetailsForAccountID() gotDetails = %v, want %v", gotDetails, tt.want)
			}
		})
	}
}

func TestService_GetUserDetailsForPhoneNumber(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockCustomerIdentifier := mockHelper.NewMockICustomerIdentifier(ctr)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
	mockVendorMappingClient := mockVm.NewMockVendorMappingServiceClient(ctr)
	mockDataCollectorHelper := mockHelper2.NewMockIDataCollectorHelper(ctr)
	mockBCClient := mocksBC.NewMockBankCustomerServiceClient(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks              []interface{}
		ctx                context.Context
		phoneNumber        string
		requiredColumnsMap map[profilePb.BulkUserInfoField]bool
	}

	tests := []struct {
		name     string
		args     args
		want     UserDetailsMap
		wantFail *profilePb.FailureID
	}{
		{
			name: "failed to parse phone number string in request",
			args: args{
				ctx:                context.Background(),
				phoneNumber:        "invalidPhone",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: "invalidPhone"}},
				FailureReason: FailureReasonErrorWhileParsingPhoneNumber,
			},
		},
		{
			name: "non-Indian phone number passed in the request",
			args: args{
				ctx:                context.Background(),
				phoneNumber:        "+************",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: "+************"}},
				FailureReason: FailureReasonNonIndianPhoneNumberPassed,
			},
		},
		// TODO : add the test case of passing a new failure reasons for unsupported country code instead of parsing error
		{
			name: "passing phone number with unsupported country code",
			args: args{
				ctx:                context.Background(),
				phoneNumber:        "+1**********",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: "+1**********"}},
				FailureReason: FailureReasonNonIndianPhoneNumberPassed,
			},
		},
		{
			name: "error while fetching user from phone number",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				phoneNumber:        "**********",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: "**********"}},
				FailureReason: FailureReasonErrorWhileFetchingUserDetails,
			},
		},
		{
			name: "error while fetching actor by user ID",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{Id: "userId"}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				phoneNumber:        "**********",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: "**********"}},
				FailureReason: FailureReasonErrorWhileFetchingActorDetails,
			},
		},
		{
			name: "error while fetching account details",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{Id: "userId"}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(&types.Actor{Id: "actorId"}, nil),
					mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
						Return(nil, errors.New("fail")),
				},
				ctx:                context.Background(),
				phoneNumber:        "**********",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: "**********"}},
				FailureReason: FailureReasonErrorWhileFetchingAccountDetails,
			},
		},
		{
			name: "error while getting customer info from actorId",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{Id: "userId"}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(&types.Actor{Id: "actorId"}, nil),
					mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
						Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber"}}, nil),
					mockBCClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).Return(&bcPb.GetBankCustomerResponse{
						Status: rpcPb.StatusInternal(),
					}, errors.New("fail")),
				},
				ctx:                context.Background(),
				phoneNumber:        "**********",
				requiredColumnsMap: requiredAllColsMap,
			},
			want: nil,
			wantFail: &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: "**********"}},
				FailureReason: FailureReasonErrorWhileFetchingCustomerDetails,
			},
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{
							Id:      "userId",
							Profile: &userPb.Profile{Email: "email@abc", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, KycName: &commontypes.Name{FirstName: "Name"}}}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(&types.Actor{Id: "actorId"}, nil),
					mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
						Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber",
							CreationInfo: &savingsPb.AccountCreationInfo{VendorCreationSucceededAt: &timestampPb.Timestamp{Seconds: **********}}}}, nil),
					mockBCClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).Return(&bcPb.GetBankCustomerResponse{
						Status: rpcPb.StatusOk(),
						BankCustomer: &bcPb.BankCustomer{
							VendorCustomerId: "CustomerId",
							ActorId:          "actorId",
							Vendor:           commonvgpb.Vendor_FEDERAL_BANK,
							DedupeInfo: &bcPb.DedupeInfo{
								KycLevel: kyc.KYCLevel_FULL_KYC,
							},
						},
					}, nil),
					mockVendorMappingClient.EXPECT().GetBEMappingById(context.Background(), &vendormapping.GetBEMappingByIdRequest{Id: "actorId"}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpcPb.StatusOk(), FcmId: "FcmId"}, nil),
					mockDataCollectorHelper.EXPECT().IsSavingsAccountClosureAllowed(context.Background(), "userId", "actorId").
						Return(true, nil, nil),
				},
				ctx:                context.Background(),
				phoneNumber:        "**********",
				requiredColumnsMap: requiredAllColsMap,
			},
			want:     UserDetails1,
			wantFail: nil,
		},
		{
			name: "success but failed to populate fcm id and savings account closure info",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetUser(context.Background(), "", &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}).
						Return(&userPb.User{
							Id:      "userId",
							Profile: &userPb.Profile{Email: "email@abc", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}, KycName: &commontypes.Name{FirstName: "Name"}}}, nil),
					mockCustomerIdentifier.EXPECT().GetActorByUserId(context.Background(), "userId").
						Return(&types.Actor{Id: "actorId"}, nil),
					mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
						Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{AccountNo: "AccountNumber",
							CreationInfo: &savingsPb.AccountCreationInfo{VendorCreationSucceededAt: &timestampPb.Timestamp{Seconds: **********}}}}, nil),
					mockBCClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
						Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: "actorId"},
					}).Return(&bcPb.GetBankCustomerResponse{
						Status: rpcPb.StatusOk(),
						BankCustomer: &bcPb.BankCustomer{
							VendorCustomerId: "CustomerId",
							ActorId:          "actorId",
							Vendor:           commonvgpb.Vendor_FEDERAL_BANK,
							DedupeInfo: &bcPb.DedupeInfo{
								KycLevel: kyc.KYCLevel_FULL_KYC,
							},
						},
					}, nil),
					mockVendorMappingClient.EXPECT().GetBEMappingById(context.Background(), &vendormapping.GetBEMappingByIdRequest{Id: "actorId"}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpcPb.StatusInternal()}, nil),
					mockDataCollectorHelper.EXPECT().IsSavingsAccountClosureAllowed(context.Background(), "userId", "actorId").
						Return(true, nil, errors.New("mock")),
				},
				ctx:                context.Background(),
				phoneNumber:        "**********",
				requiredColumnsMap: requiredAllColsMap,
			},
			want:     UserDetails2,
			wantFail: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mockSavingsClient, nil, nil, nil,
				nil, nil, mockVendorMappingClient, mockCustomerIdentifier, nil, mockDataCollectorHelper, nil, nil, nil, mockBCClient, nil, nil, nil, nil)
			gotDetails, gotFail := s.GetUserDetailsForPhoneNumber(tt.args.ctx, tt.args.phoneNumber, tt.args.requiredColumnsMap)
			if !reflect.DeepEqual(gotFail, tt.wantFail) {
				t.Errorf("GetUserDetailsForPhoneNumber() gotFail = %v, wantFail %v", gotFail, tt.wantFail)
				return
			}
			if !reflect.DeepEqual(gotDetails, tt.want) {
				t.Errorf("GetUserDetailsForPhoneNumber() gotDetails = %v, want %v", gotDetails, tt.want)
			}
		})
	}
}
