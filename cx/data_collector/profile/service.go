package profile

import (
	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	gammanames "github.com/epifi/gamma/pkg/names"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	paginationPkg "github.com/epifi/be-common/pkg/pagination"

	acountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	casbinPb "github.com/epifi/gamma/api/casbin"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/cx/chat/bot/workflow"
	profilePb "github.com/epifi/gamma/api/cx/data_collector/profile"
	"github.com/epifi/gamma/api/cx/data_collector/transaction"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	segmentPb "github.com/epifi/gamma/api/segment"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	dataCollectorHelper "github.com/epifi/gamma/cx/data_collector/helper"
	cxTieringHelper "github.com/epifi/gamma/cx/data_collector/tiering/helper"
	"github.com/epifi/gamma/cx/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"
)

type Service struct {
	savingsClient           savingsPb.SavingsClient
	authEngine              auth_engine.IAuthEngine
	userClient              userPb.UsersClient
	actorClient             actorPb.ActorClient
	cxS3Client              s3.S3Client
	ticketServiceClient     ticketPb.TicketClient
	vendorMappingClient     vmPb.VendorMappingServiceClient
	customerIdentifier      helper.ICustomerIdentifier
	cxConf                  *cxGenConf.Config
	dataCollectorHelper     dataCollectorHelper.IDataCollectorHelper
	commsClient             commsPb.CommsClient
	externalAccountClient   extacct.ExternalAccountsClient
	tieringClient           beTieringPb.TieringClient
	bcClient                bankCustomerPb.BankCustomerServiceClient
	onbClient               onboarding.OnboardingClient
	operationalStatusClient operstatus.OperationalStatusServiceClient
	txnDataCollectorHelper  dataCollectorHelper.ITransactionsDataCollectorHelper
	segmentClient           segmentPb.SegmentationServiceClient
}

func NewService(savingsClient savingsPb.SavingsClient, authEngine auth_engine.IAuthEngine, userClient userPb.UsersClient,
	actorClient actorPb.ActorClient, cxS3Client s3.S3Client, ticketServiceClient ticketPb.TicketClient,
	vendorMappingClient vmPb.VendorMappingServiceClient, customerIdentifier helper.ICustomerIdentifier,
	cxConf *cxGenConf.Config, dataCollectorHelper dataCollectorHelper.IDataCollectorHelper, commsClient commsPb.CommsClient, externalAccountClient extacct.ExternalAccountsClient,
	tieringClient beTieringPb.TieringClient, bcClient bankCustomerPb.BankCustomerServiceClient, onbClient onboarding.OnboardingClient,
	operationalStatusClient operstatus.OperationalStatusServiceClient, txnDataCollectorHelper dataCollectorHelper.ITransactionsDataCollectorHelper, segmentClient segmentPb.SegmentationServiceClient) *Service {
	return &Service{
		savingsClient:           savingsClient,
		authEngine:              authEngine,
		userClient:              userClient,
		actorClient:             actorClient,
		cxS3Client:              cxS3Client,
		ticketServiceClient:     ticketServiceClient,
		vendorMappingClient:     vendorMappingClient,
		customerIdentifier:      customerIdentifier,
		cxConf:                  cxConf,
		dataCollectorHelper:     dataCollectorHelper,
		commsClient:             commsClient,
		externalAccountClient:   externalAccountClient,
		tieringClient:           tieringClient,
		bcClient:                bcClient,
		onbClient:               onbClient,
		operationalStatusClient: operationalStatusClient,
		txnDataCollectorHelper:  txnDataCollectorHelper,
		segmentClient:           segmentClient,
	}
}

var _ profilePb.CustomerProfileServer = &Service{}

const (
	TicketFailureReasonNotFound                               = "ticket details not found in cx db"
	TicketFailureReasonErrorWhileFetchingTicketDetails        = "error while fetching ticket details"
	FailureReasonErrorWhileFetchingUserDetails                = "error while fetching user details"
	FailureReasonErrorWhileFetchingActorDetails               = "error while fetching actor details"
	FailureReasonErrorWhileFetchingAccountDetails             = "error while fetching user account details"
	FailureReasonErrorWhileFetchingCustomerDetails            = "error while fetching bank customer details"
	FailureReasonErrorWhileFetchingFCMDetails                 = "error while fetching FCM ID"
	FailureReasonErrorWhileParsingPhoneNumber                 = "error while parsing phone number"
	FailureReasonNonIndianPhoneNumberPassed                   = "non-Indian phone number passed in request"
	FailureReasonInvalidInputID                               = "invalid input ID"
	FailureReasonErrorWhileVerifyingAccountClosureEligibility = "error while verifying savings account closure eligibility"
	logKeyTotalIdsRequested                                   = "totalIdsRequested"
	logKeyIdsProcessedSoFar                                   = "idsProcessedSoFar"
	logKeyAgentRole                                           = "agentRole"
	FiAccountStatusCreated                                    = "Account created"
	FiAccountStatusNotCreated                                 = "Account not created"
	closureRequestStatusTitle                                 = "Account closure requests"
	closureRequestStatusDetailsLabel                          = "Status details"
	closureRequestSerialNoLabel                               = "S.no"
	closureRequestSerialNoKey                                 = "s_no"
	closureRequestStatusLabel                                 = "Status"
	closureRequestStatusKey                                   = "status"
	closureRequestCreatedAtLabel                              = "Request created at"
	closureRequestCreatedAtKey                                = "created_at"
	closureRequestStatusUpdatedAtLabel                        = "Status updated at"
	closureRequestStatusUpdatedAtKey                          = "updated_at"
	closureRequestFeedbackLabel                               = "User's feedback"
	closureRequestFeedbackKey                                 = "feedback"
)

var (
	mapOfCxProfileToBEBalanceUpdateEnum = map[profilePb.ForceBalanceUpdate]savingsPb.GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate{
		profilePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_UNSPECIFIED: savingsPb.GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_UNSPECIFIED,
		profilePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_NEEDED:      savingsPb.GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_NEEDED,
		profilePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_NOT_NEEDED:  savingsPb.GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_NOT_NEEDED,
	}
)

var saClosureReqFieldMasks = func() []savingsPb.SavingsAccountClosureRequestFieldMask {
	return []savingsPb.SavingsAccountClosureRequestFieldMask{
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_STATUS,
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_USER_FEEDBACK,
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_CREATED_AT,
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_UPDATED_AT,
	}
}

func (s *Service) GetCustomerProfile(ctx context.Context, req *profilePb.GetCustomerProfileRequest) (*profilePb.GetCustomerProfileResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &profilePb.GetCustomerProfileResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetUser() == nil {
		cxLogger.Info(ctx, "user information not present in header")
		return &profilePb.GetCustomerProfileResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found"),
		}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Error(ctx, "actor information not present in header")
		// not returning error from here since right now we are only using this information for
		// tiering related info
	}
	var currentTier beTieringExtPb.Tier
	errGrp, gCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		getTieringResp, err := s.tieringClient.GetTieringPitchV2(gCtx, &beTieringPb.GetTieringPitchV2Request{
			ActorId: req.GetHeader().GetActor().GetId(),
		})
		if getPitchDetailsErr := epifigrpc.RPCError(getTieringResp, err); getPitchDetailsErr != nil {
			return errors.Wrap(getPitchDetailsErr, "error fetching pitch details for the actor")
		}
		currentTier = getTieringResp.GetCurrentTier()
		return nil
	})

	var savingsAccountClosureRequests []*savingsPb.SavingsAccountClosureRequest
	errGrp.Go(func() error {
		pageTokenStruct := &paginationPkg.PageToken{
			Timestamp: timestampPb.Now(),
			Offset:    0,
			IsReverse: true,
		}
		pageToken, marshalErr := pageTokenStruct.Marshal()
		if marshalErr != nil {
			return fmt.Errorf("failed to marshal page token, %w", marshalErr)
		}

		getSaClosureResp, getSaClosureErr := s.savingsClient.GetSaClosureRequestsByFilter(gCtx, &savingsPb.GetSaClosureRequestsByFilterRequest{
			PageContext: &rpcPb.PageContextRequest{
				Token:    &rpcPb.PageContextRequest_AfterToken{AfterToken: pageToken},
				PageSize: 3,
			},
			FieldMasks:    saClosureReqFieldMasks(),
			ActorIdFilter: []string{req.GetHeader().GetActor().GetId()},
		})
		if rpcErr := epifigrpc.RPCError(getSaClosureResp, getSaClosureErr); rpcErr != nil && !getSaClosureResp.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("GetSaClosureRequestsByFilter rpc failed, %w", rpcErr)
		}

		savingsAccountClosureRequests = getSaClosureResp.GetSavingsAccountClosureRequests()
		return nil
	})

	accResp, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
		PrimaryUserId: req.GetHeader().GetUser().GetId(),
	}})
	// since user service fetch customer details API can timeout due to federal we are setting max timeout to 10 seconds
	newCtxWithTimeout, cancelFunc := context.WithTimeout(ctx, 10*time.Second)
	defer cancelFunc()
	userResp, userErr := s.userClient.GetCustomerDetails(newCtxWithTimeout, &userPb.GetCustomerDetailsRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		UserId:     req.GetHeader().GetUser().GetId(),
		ActorId:    req.GetHeader().GetActor().GetId(),
		Provenance: userPb.Provenance_SHERLOCK,
	})
	if userErr = epifigrpc.RPCError(userResp, userErr); userErr != nil {
		cxLogger.Error(newCtxWithTimeout, "cannot fetch customer details from user service", zap.Error(userErr))
		// Do not return from here with error since we want to display partial data on sherlock
	}

	rpcCtxWithTimeOut, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	opResp, opErr := s.operationalStatusClient.GetOperationalStatus(rpcCtxWithTimeOut, &operstatus.GetOperationalStatusRequest{
		DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
		AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: accResp.GetAccount().GetId(),
		},
	})
	if opErr = epifigrpc.RPCError(opResp, opErr); opErr != nil {
		cxLogger.Error(rpcCtxWithTimeOut, "cannot fetch sign status from bank", zap.Error(opErr))
	}

	var accountOperationalStatus string
	operationalStatus := opResp.GetOperationalStatusInfo().GetOperationalStatus()
	switch operationalStatus {
	case acountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE:
		accountOperationalStatus = "Active"
	case acountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_INACTIVE:
		accountOperationalStatus = "Inactive"
	case acountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_DORMANT:
		accountOperationalStatus = "Dormant"
	case acountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED:
		accountOperationalStatus = "Closed"
	}

	onbDetailsRes, onbDetailsErr := s.onbClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
		ActorId:    req.GetHeader().GetActor().GetId(),
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		CachedData: true,
	})
	if grpcErr := epifigrpc.RPCError(onbDetailsRes, onbDetailsErr); grpcErr != nil {
		cxLogger.Error(ctx, "error while fetching onboarding details for user", zap.Error(grpcErr))
		// Do not return from here with error since we want to display partial data on sherlock
	}
	residentialStatus := lo.Ternary(onbDetailsRes.GetDetails().GetFeature().IsNonResidentUserOnboarding(), "NRI", "Indian")
	userDevicePropertiesResp, userDevicePropertiesErr := s.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		PropertyTypes: []types.DeviceProperty{
			types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO,
			types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
		},
	})
	if te := epifigrpc.RPCError(userDevicePropertiesResp, userDevicePropertiesErr); te != nil {
		cxLogger.Error(ctx, "error while fetching device properties for user", zap.Error(te))
		// Do not return from here with error since we want to display partial data on sherlock
	}
	appVersionInfo := userDevicePropertiesResp.GetPropValue(types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO).GetAppVersionInfo()
	modelInfo := userDevicePropertiesResp.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo()

	// GetAccount RPC does not return rpc status hence we have to do this check
	// Fix this when the corresponding rpc is fixed
	if err != nil || accResp == nil || accResp.GetAccount() == nil {
		cxLogger.Error(ctx, "cannot fetch account information from savings service", zap.Error(err))
		// Do not return from here with error since we want to display partial data on sherlock
	}

	var closedAccData *savingsPb.ClosedAccountBalanceTransfer
	if isAccountClosedByCompliance(accResp.GetAccount().GetConstraints()) {
		cbt, cbtErr := s.savingsClient.GetClosedAccountBalTransferData(ctx, &savingsPb.GetClosedAccountBalTransferDataRequest{
			SavingsAccountId: accResp.GetAccount().GetId(),
		})
		if err = epifigrpc.RPCError(cbt, cbtErr); err != nil {
			cxLogger.Error(ctx, "cannot fetch closed account info from savings service", zap.Error(err))
			// do not return from here with error since we want to display partial data on sherlock
		}
		closedAccData = getLatestClosedAccountInfo(cbt.GetEntries())
	}

	externalAccountsResp, extacctErr := s.externalAccountClient.GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if grpcErr := epifigrpc.RPCError(externalAccountsResp, extacctErr); grpcErr != nil {
		cxLogger.Error(ctx, "error while fetching external account validation for user", zap.Error(grpcErr))
		// Do not return from here with error since we want to display partial data on sherlock
	}
	getCurrentTierErr := errGrp.Wait()
	if getCurrentTierErr != nil {
		cxLogger.Error(ctx, "error while fetching external account validation for user", zap.Error(getCurrentTierErr))
		// Do not return from here with error since we want to display partial data on sherlock
	}
	bcResp, bcErr := s.bcClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
			ActorId: req.GetHeader().GetActor().GetId(),
		},
	})
	if grpcErr := epifigrpc.RPCError(bcResp, bcErr); grpcErr != nil {
		cxLogger.Error(ctx, "error while fetching bank customer data for user", zap.Error(grpcErr))
		// Do not return from here with error since we want to display partial data on sherlock
	}

	featResp, featErr := s.onbClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		Feature: onboarding.Feature_FEATURE_SA,
	})
	if grpcErr := epifigrpc.RPCError(featResp, featErr); grpcErr != nil {
		cxLogger.Error(ctx, "error while fetching feature details for user", zap.Error(grpcErr))
		// Do not return from here with error since we want to display partial data on sherlock
	}

	resp := convertToProto(accResp, req.GetHeader().GetUser(), userResp.GetAddresses(), externalAccountsResp,
		currentTier, closedAccData, bcResp.GetBankCustomer().GetVendorCustomerId(), featResp,
		opResp.GetOperationalStatusInfo().GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetSignCount(),
		accountOperationalStatus, residentialStatus, savingsAccountClosureRequests, modelInfo, appVersionInfo)

	balanceRefreshData, err := s.createBalanceRefreshData(ctx, accResp, req.GetForceBalanceUpdate())
	if err != nil {
		cxLogger.Error(ctx, "failed to get balance refresh data for the user", zap.Error(err))
	} else {
		resp.BalanceRefreshData = balanceRefreshData
	}
	return resp, nil
}

func isAccountClosedByCompliance(constraints *savingsPb.AccountConstraints) bool {
	return constraints.GetAccessLevel() == savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS &&
		(constraints.GetUpdateDetails().GetReason() == savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_MIN_KYC_ACCOUNT_EXPIRY ||
			constraints.GetUpdateDetails().GetReason() == savingsPb.ConstraintsUpdateReason_CONSTRAINTS_UPDATE_REASON_LSO_USER_VKYC_PENDING)
}

func getLatestClosedAccountInfo(cbts []*savingsPb.ClosedAccountBalanceTransfer) *savingsPb.ClosedAccountBalanceTransfer {
	if len(cbts) == 0 {
		return nil
	}
	sort.Slice(cbts, func(i, j int) bool {
		return cbts[i].GetUpdatedAt().AsTime().After(cbts[j].GetUpdatedAt().AsTime())
	})
	return cbts[0]
}

// nolint: funlen
func convertToProto(acc *savingsPb.GetAccountResponse, user *userPb.User, addresses map[string]*postaladdress.PostalAddress, externalAccountsResp *extacct.GetBankAccountsResponse, currentTier beTieringExtPb.Tier,
	closedAccData *savingsPb.ClosedAccountBalanceTransfer, vendorCustomerId string, featResp *onboarding.GetFeatureDetailsResponse, signCount int32,
	accountOperationalStatus, residentialStatus string, saClosureRequests []*savingsPb.SavingsAccountClosureRequest, deviceModelInfo *commontypes.DeviceModelInfo, appVersionInfo *types.AppVersionInfo) *profilePb.GetCustomerProfileResponse {
	var account *profilePb.CustomerAccount
	var signStatus string
	if acc != nil {
		num := acc.GetAccount().GetAccountNo()
		account = &profilePb.CustomerAccount{
			AccountCreationDate: acc.GetAccount().GetCreatedAt(),
			State:               acc.GetAccount().GetState(),
			IfscCode:            acc.GetAccount().GetIfscCode(),
			PhoneNumber:         user.GetProfile().GetPhoneNumber(),
			TransactionLimit:    acc.GetAccount().GetConstraints().GetTransactionLimit(),
			WithdrawalLimit:     acc.GetAccount().GetConstraints().GetWithdrawalLimit(),
			Email:               user.GetProfile().GetEmail(),
			PartnerBank:         acc.GetAccount().GetPartnerBank().String(),
		}
		if len(num) > 4 {
			account.AccountNumberLastFourDigits = num[len(num)-4:]
		}
		addressMap := make(map[string]*types.PostalAddress)
		for addressName, addressValue := range addresses {
			addressMap[addressName] = convertToClientPostalAddressType(addressValue)
		}
		account.AddressMap = addressMap
	}
	if signCount == 0 {
		signStatus = "Not present at Bank"
	} else {
		signStatus = "Present at Bank"
	}
	ret := &profilePb.GetCustomerProfileResponse{
		Status:            rpcPb.StatusOk(),
		Account:           account,
		CustomerId:        vendorCustomerId,
		LegalName:         user.GetProfile().GetKycName(),
		FatherName:        user.GetProfile().GetFatherName(),
		AccessRevokeState: user.GetAccessRevokeState().String(),
		AccessRevokeDetails: &profilePb.AccessRevokeDetails{
			AccessRevokeStatus: user.GetAccessRevokeDetails().GetAccessRevokeStatus().String(),
			Reason:             user.GetAccessRevokeDetails().GetReason().String(),
			Remarks:            user.GetAccessRevokeDetails().GetRemarks(),
			UpdatedAt:          user.GetAccessRevokeDetails().GetUpdatedAt(),
			UpdatedBy:          user.GetAccessRevokeDetails().GetUpdatedBy(),
		},
		SignStatus: signStatus,
	}
	ret.LevelValues = append(ret.LevelValues, &webui.LabelValue{
		Label: "Account Operational Status",
		Value: []string{accountOperationalStatus},
	})
	ret.LevelValues = append(ret.LevelValues, &webui.LabelValue{
		Label: "Residential Status",
		Value: []string{residentialStatus},
	})

	if saClosureReqSection := getSaClosureRequestsSection(saClosureRequests); saClosureReqSection != nil {
		ret.AccountClosureRequestSection = saClosureReqSection
	}

	if isAccountClosedByCompliance(acc.GetAccount().GetConstraints()) {
		switch {
		case closedAccData == nil, closedAccData.GetReportedClosureBalance() == nil:
			ret.ClosedAccountBalance = "NA"
		case closedAccData.GetReportedClosureBalance().GetUnits() == 1 && closedAccData.GetReportedClosureBalance().GetNanos() == 0:
			ret.ClosedAccountBalance = "Equal to 1 INR"
		case closedAccData.GetReportedClosureBalance().GetUnits() < 1:
			ret.ClosedAccountBalance = "Less than 1 INR"
		case closedAccData.GetReportedClosureBalance().GetUnits() >= 1:
			ret.ClosedAccountBalance = "More than 1 INR"
		}
	}
	// if kyc name is not there i.e user is stuck in onboarding and kyc is not done yet
	// fallback to profile name
	if ret.GetLegalName() == nil {
		ret.LegalName = gammanames.BestNameFromProfile(context.Background(), user.GetProfile())
	}
	if deviceModelInfo != nil {
		ret.DeviceDetails = &profilePb.DeviceDetails{
			Manufacturer: deviceModelInfo.GetManufacturer(),
			Model:        deviceModelInfo.GetModel(),
			SwVersion:    deviceModelInfo.GetSwVersion(),
			OsApiVersion: deviceModelInfo.GetOsApiVersion(),
		}
	}
	if appVersionInfo != nil {
		if ret.GetDeviceDetails() == nil {
			ret.DeviceDetails = &profilePb.DeviceDetails{AppVersion: appVersionInfo.GetAppVersionCode()}
		} else {
			ret.DeviceDetails.AppVersion = appVersionInfo.GetAppVersionCode()
		}
	}
	if externalAccountsResp != nil {
		ret.ExternalAccountDetails = convertToProfileExtAcctDetails(externalAccountsResp)
	}
	if currentTier != 0 {
		ret.CurrentTierPlanName = cxTieringHelper.ConvertTierNameToDisplayName(currentTier)
	}

	ret.FiAccountStatus = FiAccountStatusNotCreated
	// if fi lite or SA account is created, show account created
	if account.GetState() == savingsPb.State_CREATED || featResp.GetIsFiLiteUser() {
		ret.FiAccountStatus = FiAccountStatusCreated
	}
	return ret
}

func getSaClosureRequestsSection(saClosureRequests []*savingsPb.SavingsAccountClosureRequest) *profilePb.Section {
	if len(saClosureRequests) == 0 {
		return nil
	}

	saClosureTable := &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				Label:     closureRequestSerialNoLabel,
				HeaderKey: closureRequestSerialNoKey,
				IsVisible: true,
			},
			{
				Label:     closureRequestStatusLabel,
				HeaderKey: closureRequestStatusKey,
				IsVisible: true,
			},
			{
				Label:     closureRequestCreatedAtLabel,
				HeaderKey: closureRequestCreatedAtKey,
				IsVisible: true,
			},
			{
				Label:     closureRequestStatusUpdatedAtLabel,
				HeaderKey: closureRequestStatusUpdatedAtKey,
				IsVisible: true,
			},
			{
				Label:     closureRequestFeedbackLabel,
				HeaderKey: closureRequestFeedbackKey,
				IsVisible: true,
			},
		},
		TableName: closureRequestStatusTitle,
	}

	for idx, saClosureRequest := range saClosureRequests {
		row := &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				closureRequestSerialNoKey:        webui.GetStringValueTableCell(fmt.Sprintf("%d", idx+1)),
				closureRequestStatusKey:          webui.GetStringValueTableCell(getClosureReqStatusText(saClosureRequest.GetStatus())),
				closureRequestCreatedAtKey:       webui.GetStringValueTableCell(saClosureRequest.GetCreatedAt().AsTime().Format(time.DateOnly)),
				closureRequestStatusUpdatedAtKey: webui.GetStringValueTableCell(saClosureRequest.GetUpdatedAt().AsTime().Format(time.DateOnly)),
				closureRequestFeedbackKey:        webui.GetStringValueTableCell(saClosureRequest.GetUserFeedback().GetFeedbackText()),
			},
		}

		saClosureTable.TableRows = append(saClosureTable.GetTableRows(), row)
	}

	return &profilePb.Section{
		Title: closureRequestStatusTitle,
		LabelValues: []*webui.LabelValueV2{
			{
				Label:    closureRequestStatusDetailsLabel,
				DataType: webui.LabelValueV2_DATA_TYPE_TABLE,
				Value:    &webui.LabelValueV2_TableValue{TableValue: saClosureTable},
			},
		},
	}
}

func getClosureReqStatusText(status savingsPb.SAClosureRequestStatus) string {
	statusStr := strings.TrimPrefix(status.String(), "SA_CLOSURE_REQUEST_STATUS_")
	return strings.ToLower(strings.ReplaceAll(statusStr, "_", " "))
}

func convertToProfileExtAcctDetails(externalAccountsResp *extacct.GetBankAccountsResponse) *profilePb.ExternalAccountDetails {
	var (
		hasVerifiedAccounts      = false
		verifiedAccounts         []*profilePb.BankAccount
		bankAccountVerifications []*profilePb.BankAccountVerification
	)
	if len(externalAccountsResp.GetBankAccounts()) > 0 {
		hasVerifiedAccounts = true
		for _, verifiedAccount := range externalAccountsResp.GetBankAccounts() {
			verifiedAccounts = append(verifiedAccounts, &profilePb.BankAccount{
				Ifsc:          verifiedAccount.GetIfsc(),
				AccountNumber: mask.GetMaskedAccountNumber(verifiedAccount.GetAccountNumber(), ""),
				Name:          verifiedAccount.GetName(),
			})
		}
	}
	for _, bankAccountVerification := range externalAccountsResp.GetBankAccountVerifications() {
		bankAccountVerifications = append(bankAccountVerifications, &profilePb.BankAccountVerification{
			Id:            bankAccountVerification.GetId(),
			ActorId:       bankAccountVerification.GetActorId(),
			AccountNumber: mask.GetMaskedAccountNumber(bankAccountVerification.GetAccountNumber(), ""),
			Ifsc:          bankAccountVerification.GetIfsc(),
			NameAtBank:    bankAccountVerification.GetNameAtBank(),
			OverallStatus: bankAccountVerification.GetOverallStatus().String(),
			VendorStatus: &profilePb.VendorStatus{
				Code:        bankAccountVerification.GetVendorStatus().GetCode(),
				Description: bankAccountVerification.GetVendorStatus().GetDescription(),
			},
			VendorReqId:   bankAccountVerification.GetVendorReqId(),
			Vendor:        bankAccountVerification.GetVendor().String(),
			FailureReason: bankAccountVerification.GetFailureReason().String(),
			NameMatchData: &profilePb.NameMatchData{
				KycName:                 bankAccountVerification.GetNameMatchData().GetKycName(),
				UserGivenName:           bankAccountVerification.GetNameMatchData().GetUserGivenName(),
				UserGivenNameMatchScore: bankAccountVerification.GetNameMatchData().GetUserGivenNameMatchScore().GetScore(),
				NameAtBank:              bankAccountVerification.GetNameMatchData().GetNameAtBank(),
				NameAtBankMatchScore:    bankAccountVerification.GetNameMatchData().GetNameAtBankMatchScore().GetScore(),
			},
			Caller: &profilePb.Caller{
				Source: bankAccountVerification.GetCaller().GetSource().String(),
				Email:  bankAccountVerification.GetCaller().GetEmail(),
			},
			CreatedAt:     bankAccountVerification.GetCreatedAt(),
			UpdatedAt:     bankAccountVerification.GetUpdatedAt(),
			DeletedAtUnix: bankAccountVerification.GetDeletedAtUnix(),
		})
	}
	return &profilePb.ExternalAccountDetails{
		HasVerifiedAccounts:        hasVerifiedAccounts,
		VerifiedAccounts:           verifiedAccounts,
		BankAccountVerifications:   bankAccountVerifications,
		NameMatchRetriesLeft:       externalAccountsResp.GetNameMatchRetriesLeft(),
		AccVerificationRetriesLeft: externalAccountsResp.GetAccVerificationRetriesLeft(),
	}
}

func convertToClientPostalAddressType(postalAddr *postaladdress.PostalAddress) *types.PostalAddress {
	if postalAddr == nil {
		return nil
	}
	// Mask first address line
	if len(postalAddr.GetAddressLines()) != 0 && len(postalAddr.GetAddressLines()[0]) > 6 {
		postalAddr.GetAddressLines()[0] = mask.MaskLastNDigits(postalAddr.GetAddressLines()[0], len(postalAddr.GetAddressLines()[0])-6, "X")
	}
	if len(postalAddr.GetAddressLines()) > 1 {
		for index, val := range postalAddr.GetAddressLines() {
			// First line should not be masked
			if index == 0 {
				continue
			}
			postalAddr.GetAddressLines()[index] = mask.GetMaskedString(mask.MaskAllChars, val)
		}
	}
	return &types.PostalAddress{
		Revision:           postalAddr.GetRevision(),
		RegionCode:         postalAddr.GetRegionCode(),
		LanguageCode:       postalAddr.GetLanguageCode(),
		PostalCode:         postalAddr.GetPostalCode(),
		SortingCode:        postalAddr.GetSortingCode(),
		AdministrativeArea: postalAddr.GetAdministrativeArea(),
		Locality:           postalAddr.GetLocality(),
		Sublocality:        postalAddr.GetSublocality(),
		AddressLines:       postalAddr.GetAddressLines(),
		Recipients:         postalAddr.GetRecipients(),
		Organization:       postalAddr.GetOrganization(),
	}
}

// nolint: funlen
func (s *Service) GetUserByBankInfo(ctx context.Context, req *profilePb.GetUserByBankInfoRequest) (*profilePb.GetUserByBankInfoResponse, error) {
	// validate request
	if req.GetAccountNumber() == "" {
		cxLogger.Info(ctx, "invalid request: account number not passed")
		return &profilePb.GetUserByBankInfoResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("account number is mandatory for fetching user details"),
		}, nil
	}
	accountResponse, err := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
			AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
				AccountNumber: req.GetAccountNumber(),
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if te := epifigrpc.RPCError(accountResponse, err); te != nil {
		cxLogger.Error(ctx, "error while fetching savings account essential details", zap.Error(err))
		return &profilePb.GetUserByBankInfoResponse{Status: accountResponse.GetStatus()}, nil
	}
	// fetch user details
	userResponse, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: accountResponse.GetAccount().GetActorId(),
		},
	})
	if err = epifigrpc.RPCError(userResponse, err); err != nil {
		cxLogger.Error(ctx, "error while fetching user from user service", zap.Error(err))
		return &profilePb.GetUserByBankInfoResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching user info"),
		}, nil
	}
	// fetch actor details
	actorResp, err := s.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: userResponse.GetUser().GetId(),
	})
	if err = epifigrpc.RPCError(actorResp, err); err != nil {
		cxLogger.Error(ctx, "error while fetching user from user service", zap.Error(err))
		return &profilePb.GetUserByBankInfoResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching user info"),
		}, nil
	}

	return &profilePb.GetUserByBankInfoResponse{
		Status: rpcPb.StatusOk(),
		UserDetails: &profilePb.GetUserByBankInfoResponse_UserDetails{
			Email:       userResponse.GetUser().GetProfile().GetEmail(),
			Name:        userResponse.GetUser().GetProfile().GetKycName(),
			PhoneNumber: userResponse.GetUser().GetProfile().GetPhoneNumber(),
			ActorId:     actorResp.GetActor().GetId(),
		},
	}, nil
}

func (s *Service) GetBulkUserInfoByTicketIds(ctx context.Context, req *profilePb.GetBulkUserInfoByTicketIdsRequest) (*profilePb.GetBulkUserInfoByTicketIdsResponse, error) {
	ticketIdList := req.GetTicketIdList()
	totalIdsRequested := len(ticketIdList)
	toEmailId := req.GetHeader().GetAgentEmail()
	// log for tracking a request on kibana
	logger.Info(ctx, "Received bulk user details request by ticket Ids", zap.String(logger.AGENT_EMAIL, toEmailId), zap.Int(logKeyTotalIdsRequested, totalIdsRequested))
	if totalIdsRequested == 0 {
		logger.Info(ctx, "empty ticket list passed in request")
		return &profilePb.GetBulkUserInfoByTicketIdsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket id list can't be empty"),
		}, nil
	}

	if totalIdsRequested > s.cxConf.MaxCountThresholdForFetchingBulkUserInfo() {
		logger.Error(ctx, "ticket count is higher than max allowed threshold")
		return &profilePb.GetBulkUserInfoByTicketIdsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket count is higher than max allowed threshold"),
		}, nil
	}
	fieldToCsvColNameMap, err := getCsvColNameMapFromConfig(s.cxConf.BulkUserInfoViaEmailConfig())
	if err != nil {
		logger.Error(ctx, "failed to read CSV column names map from config", zap.Error(err))
		return &profilePb.GetBulkUserInfoByTicketIdsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to read CSV column names map from config"),
		}, nil
	}
	logger.Info(ctx, "Triggering async processing of bulk user details for ticket Ids to send via email to agent", zap.String(logger.AGENT_EMAIL, toEmailId), zap.Int(logKeyTotalIdsRequested, totalIdsRequested))
	// not cloning ctx here to avoid timeout due to ctx
	newCtx := epificontext.CtxWithTraceId(context.Background(), epificontext.TraceIdFromContext(ctx))
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		func(emailId string) {
			userDetailsList, ticketFailureList := s.GetUserDetailsForTickets(newCtx, req.GetTicketIdList(), emailId)

			s.SendBulkUserDetailsMail(newCtx, emailId, []profilePb.BulkUserInfoField{
				profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_FRESHDESK_TICKET_ID, profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_CUSTOMER_ID,
				profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_NUMBER, profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACTOR_ID,
			}, fieldToCsvColNameMap, userDetailsList, ticketFailureList)
		}(toEmailId)
	})

	return &profilePb.GetBulkUserInfoByTicketIdsResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) GetBulkUserInfo(ctx context.Context, request *profilePb.GetBulkUserInfoRequest) (*profilePb.GetBulkUserInfoResponse, error) {
	idList := request.GetIdList()
	totalIdsRequested := len(idList)
	agentRole := request.GetHeader().GetAccessLevel()
	toEmailId := request.GetHeader().GetAgentEmail()
	logger.Info(ctx, "Received bulk user details request by Ids", zap.String(logKeyAgentRole, agentRole.String()), zap.String(logger.AGENT_EMAIL, toEmailId), zap.Int(logKeyTotalIdsRequested, totalIdsRequested))

	if totalIdsRequested == 0 {
		logger.Info(ctx, "empty ID list passed in request")
		return &profilePb.GetBulkUserInfoResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ID list can't be empty"),
		}, nil
	}

	if totalIdsRequested > s.cxConf.BulkUserInfoViaEmailConfig().MaxCountThreshold() {
		logger.Info(ctx, "IDs count is higher than max allowed threshold")
		return &profilePb.GetBulkUserInfoResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("IDs count is higher than max allowed threshold"),
		}, nil
	}
	csvColumnsOrderedList, csvColumnsMap, err := getResponseColumnsForRole(s.cxConf.BulkUserInfoViaEmailConfig(), agentRole)
	if err != nil {
		logger.Error(ctx, "failed to read response columns for role", zap.String(logKeyAgentRole, agentRole.String()), zap.Error(err))
		return &profilePb.GetBulkUserInfoResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to read response columns for role"),
		}, nil
	}
	fieldToCsvColNameMap, err := getCsvColNameMapFromConfig(s.cxConf.BulkUserInfoViaEmailConfig())
	if err != nil {
		logger.Error(ctx, "failed to read CSV column names map from config", zap.Error(err))
		return &profilePb.GetBulkUserInfoResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to read CSV column names map from config"),
		}, nil
	}
	logger.Info(ctx, "Triggering async processing of bulk user details for Ids to send via email", zap.String(logKeyAgentRole, agentRole.String()), zap.String(logger.AGENT_EMAIL, toEmailId), zap.Int(logKeyTotalIdsRequested, totalIdsRequested))
	// not cloning ctx here to avoid timeout due to ctx
	newCtx := epificontext.CtxWithTraceId(context.Background(), epificontext.TraceIdFromContext(ctx))
	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		func(emailId string, idList []*profilePb.BulkUserIdentifier) {
			userDetailsList, failedIdList := s.GetUserDetailsForIDs(newCtx, idList, csvColumnsMap, emailId)

			s.SendBulkUserDetailsMail(newCtx, emailId, csvColumnsOrderedList, fieldToCsvColNameMap, userDetailsList, failedIdList)
		}(toEmailId, idList)
	})

	return &profilePb.GetBulkUserInfoResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func getCsvColNameMapFromConfig(conf *cxGenConf.BulkUserInfoViaEmailConfig) (UserDetailsMap, error) {
	colNameMap := conf.FieldToCsvColNameMap()
	if len(colNameMap) == 0 {
		return nil, errors.New("CSV columns name map not found in config")
	}
	resColMap := make(UserDetailsMap)
	for key, val := range colNameMap {
		field, ok := profilePb.BulkUserInfoField_value[strings.ToUpper(key)]
		if !ok {
			return nil, errors.New("unknown Field name from config")
		}
		resColMap[profilePb.BulkUserInfoField(field)] = val
	}
	return resColMap, nil
}

func getResponseColumnsForRole(conf *cxGenConf.BulkUserInfoViaEmailConfig, role casbinPb.AccessLevel) ([]profilePb.BulkUserInfoField, map[profilePb.BulkUserInfoField]bool, error) {
	colMap := conf.RoleToAllowedRespFieldsMap()[role.String()]
	if len(colMap) == 0 {
		return nil, nil, errors.New("No response fields config found for this role")
	}
	var respColList []profilePb.BulkUserInfoField
	respColMap := make(map[profilePb.BulkUserInfoField]bool)
	for key, val := range colMap {
		if !val {
			continue
		}
		field, ok := profilePb.BulkUserInfoField_value[strings.ToUpper(key)]
		if !ok {
			return nil, nil, errors.New("unknown Field name from config")
		}
		respColList = append(respColList, profilePb.BulkUserInfoField(field))
		respColMap[profilePb.BulkUserInfoField(field)] = true
	}
	sort.Slice(respColList, func(i, j int) bool {
		return int(respColList[i]) < int(respColList[j])
	})
	return respColList, respColMap, nil
}

func (s *Service) createBalanceRefreshData(ctx context.Context, savingAccountDetail *savingsPb.GetAccountResponse,
	foreBalanceUpdate profilePb.ForceBalanceUpdate) (*profilePb.GetCustomerProfileResponse_BalanceRefreshData, error) {
	var (
		balanceRefreshData = &profilePb.GetCustomerProfileResponse_BalanceRefreshData{}
		creditSuccessTxns  []*transaction.OrderWithTransaction
		debitSuccessTxns   []*transaction.OrderWithTransaction
		actorId            string
		err                error
	)
	actorId = savingAccountDetail.GetAccount().GetActorId()

	creditSuccessTxns, err = s.txnDataCollectorHelper.GetRecentTransactions(ctx, actorId,
		1, workflow.TransactionType_TRANSACTION_TYPE_CREDIT_SUCCESSFUL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch successful incoming transaction for actor: %w", err)
	}

	if len(creditSuccessTxns) != 0 {
		balanceRefreshData.LastIncomingTransaction = &profilePb.GetCustomerProfileResponse_BalanceRefreshData_Transaction{
			Timestamp: creditSuccessTxns[0].GetTxnUpdatedAt().AsTime().String(),
		}
	}

	debitSuccessTxns, err = s.txnDataCollectorHelper.GetRecentTransactions(ctx, actorId,
		1, workflow.TransactionType_TRANSACTION_TYPE_DEBIT_SUCCESSFUL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch successful outgoing transaction for actor: %w", err)
	}
	if len(debitSuccessTxns) != 0 {
		balanceRefreshData.LastOutgoingTransaction = &profilePb.GetCustomerProfileResponse_BalanceRefreshData_Transaction{
			Timestamp: debitSuccessTxns[0].GetTxnUpdatedAt().AsTime().String(),
		}
	}

	if foreBalanceUpdate == profilePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_NEEDED {
		getAccountBalanceWithSummaryResp, err := s.savingsClient.GetAccountBalanceWithSummary(ctx, &savingsPb.GetAccountBalanceWithSummaryRequest{
			Identifier:         &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: savingAccountDetail.GetAccount().GetId()},
			ActorId:            savingAccountDetail.GetAccount().GetActorId(),
			TimeRange:          savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
			ForceBalanceUpdate: mapOfCxProfileToBEBalanceUpdateEnum[foreBalanceUpdate],
		})
		if gRPCErr := epifigrpc.RPCError(getAccountBalanceWithSummaryResp, err); gRPCErr != nil {
			return nil, fmt.Errorf("failed to fetch account balance summary: %w", gRPCErr)
		}

		// assigning "-" to begin with in case we don't have a valid timestamp to display
		balanceRefreshData.Timestamp = "-"
		if !datetime.TimestampToTime(getAccountBalanceWithSummaryResp.GetBalanceAt()).IsZero() {
			// changing the timestamp to IST to reduce confusion for agents
			balanceRefreshData.Timestamp = getAccountBalanceWithSummaryResp.GetBalanceAt().AsTime().In(datetime.IST).Format("02-01-2006 03:04:05 PM")
		}
	} else {
		// assigning "-" to begin with in case we don't have a valid timestamp to display
		balanceRefreshData.Timestamp = "-"
		if !datetime.TimestampToTime(savingAccountDetail.GetAccount().GetBalanceFromPartenerV1().GetLastUpdatedAt()).IsZero() {
			// changing the timestamp to IST to reduce confusion for agents
			balanceRefreshData.Timestamp = savingAccountDetail.GetAccount().GetBalanceFromPartenerV1().GetLastUpdatedAt().AsTime().In(datetime.IST).Format("02-01-2006 03:04:05 PM")
		}
	}
	return balanceRefreshData, nil
}

// GetSegmentsWithUserCounts fetches all segments and their user counts
func (s *Service) GetSegmentsWithUserCounts(ctx context.Context, req *profilePb.GetSegmentsWithUserCountsRequest) (*profilePb.GetSegmentsWithUserCountsResponse, error) {
	// Parse duration from request
	duration := req.GetDuration()
	if duration == "" {
		return &profilePb.GetSegmentsWithUserCountsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("duration is required"),
		}, nil
	}

	// Parse the duration string to timestamp
	durationTime, err := time.Parse(time.RFC3339, duration)
	if err != nil {
		return &profilePb.GetSegmentsWithUserCountsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid duration format, expected RFC3339"),
		}, nil
	}

	// Call segment service to get segments
	segmentsResp, responseErr := s.segmentClient.GetSegmentsBetweenUpdatedAt(ctx, &segmentPb.GetSegmentsBetweenUpdatedAtRequest{
		Duration: timestampPb.New(durationTime),
	})
	if err = epifigrpc.RPCError(segmentsResp, responseErr); err != nil {
		logger.Error(ctx, "failed to get segments from segment service", zap.Error(err))
		return &profilePb.GetSegmentsWithUserCountsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to get segments"),
		}, nil
	}

	segments := segmentsResp.GetSegmentMetadata()
	if len(segments) == 0 {
		return &profilePb.GetSegmentsWithUserCountsResponse{
			Status:     rpcPb.StatusOk(),
			Segments:   []*segmentPb.SegmentMetadata{},
			UserCounts: make(map[string]int64),
		}, nil
	}

	// Get user counts for all segments
	userCounts := make(map[string]int64)
	for _, segment := range segments {
		userCountResp, responseErr := s.segmentClient.GetNumberOfUsersInSegment(ctx, &segmentPb.GetNumberOfUsersInSegmentRequest{
			SegmentId: segment.GetId(),
		})
		if err = epifigrpc.RPCError(userCountResp, responseErr); err != nil {
			logger.Error(ctx, "failed to get user count for segment", zap.String("segment_id", segment.GetId()), zap.Error(err))
			userCounts[segment.GetId()] = 0
			continue
		}

	}

	return &profilePb.GetSegmentsWithUserCountsResponse{
		Status:     rpcPb.StatusOk(),
		Segments:   segments,
		UserCounts: userCounts,
	}, nil
}
