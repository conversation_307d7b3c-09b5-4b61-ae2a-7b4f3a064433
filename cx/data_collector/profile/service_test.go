package profile

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"flag"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/typesv2/webui"

	"github.com/epifi/be-common/api/rpc"
	mockS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/mask"

	acountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"
	mockOper "github.com/epifi/gamma/api/accounts/operstatus/mocks"
	"github.com/epifi/gamma/api/bankcust"
	mocksBC "github.com/epifi/gamma/api/bankcust/mocks"
	casbinPb "github.com/epifi/gamma/api/casbin"
	"github.com/epifi/gamma/api/cx"
	"github.com/epifi/gamma/api/cx/chat/bot/workflow"
	profilePb "github.com/epifi/gamma/api/cx/data_collector/profile"
	"github.com/epifi/gamma/api/cx/data_collector/transaction"
	"github.com/epifi/gamma/api/kyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/savings/extacct/mocks"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	mocks2 "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/user/onboarding"
	mocks3 "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts/status"
	mockVm "github.com/epifi/gamma/api/vendormapping/mocks"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
	mockAuthEngine "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
	mockHelper2 "github.com/epifi/gamma/cx/test/mocks/data_collector/helper"
	mockTxnCollectorHelper "github.com/epifi/gamma/cx/test/mocks/data_collector/helper"
	mockHelper "github.com/epifi/gamma/cx/test/mocks/helper"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, genConf, _, teardown := test.InitTestServer(false)
	conf = genConf
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

var (
	conf                    *cxGenConf.Config
	GetBulkUserInfoRequest1 = &profilePb.GetBulkUserInfoRequest{
		IdList: []*profilePb.BulkUserIdentifier{
			{
				Id: &profilePb.BulkUserIdentifier_ActorId{
					ActorId: "actor1",
				},
			},
			{
				Id: &profilePb.BulkUserIdentifier_ActorId{
					ActorId: "actor2",
				},
			},
			{
				Id: &profilePb.BulkUserIdentifier_ActorId{
					ActorId: "actor3",
				},
			},
		},
	}
	GetBulkUserInfoRequest2 = &profilePb.GetBulkUserInfoRequest{
		IdList: []*profilePb.BulkUserIdentifier{
			{
				Id: &profilePb.BulkUserIdentifier_ActorId{
					ActorId: "actor1",
				},
			},
		},
	}
	GetBulkUserInfoRequestSuccess = &profilePb.GetBulkUserInfoRequest{
		Header: &cx.Header{
			AccessLevel: casbinPb.AccessLevel_BIZ_ADMIN,
		},
		IdList: []*profilePb.BulkUserIdentifier{
			{
				Id: &profilePb.BulkUserIdentifier_ActorId{
					ActorId: "actor1",
				},
			},
		},
	}

	saClosureRequests = []*savingsPb.SavingsAccountClosureRequest{
		{
			Status:       savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SUBMITTED,
			UserFeedback: &savingsPb.SaClosureRequestUserFeedback{FeedbackText: "feedback"},
			CreatedAt:    timestamppb.Now(),
			UpdatedAt:    timestamppb.Now(),
		},
	}
)

func TestService_GetBulkUserInfoByTicketIds(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockCustomerIdentifier := mockHelper.NewMockICustomerIdentifier(ctr)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
	mockVendorMappingClient := mockVm.NewMockVendorMappingServiceClient(ctr)
	mockDataCollectorHelper := mockHelper2.NewMockIDataCollectorHelper(ctr)
	mockBCClient := mocksBC.NewMockBankCustomerServiceClient(ctr)

	defer func() {
		ctr.Finish()
	}()

	mockCxS3Client := mockS3.NewMockS3Client(ctr)

	type fields struct {
		cxConfig *cxGenConf.Config
	}
	type args struct {
		ctx context.Context
		req *profilePb.GetBulkUserInfoByTicketIdsRequest
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *profilePb.GetBulkUserInfoByTicketIdsResponse
		wantErr bool
	}{
		{
			name:   "empty ID list passed in request",
			fields: fields{cxConfig: conf},
			args: args{
				ctx: context.Background(),
				req: &profilePb.GetBulkUserInfoByTicketIdsRequest{},
			},
			want: &profilePb.GetBulkUserInfoByTicketIdsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("ticket id list can't be empty"),
			},
			wantErr: false,
		},
		{
			name:   "IDs count is higher than max allowed threshold",
			fields: fields{cxConfig: conf},
			args: args{
				ctx: context.Background(),
				req: &profilePb.GetBulkUserInfoByTicketIdsRequest{TicketIdList: []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11}},
			},
			want: &profilePb.GetBulkUserInfoByTicketIdsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("ticket count is higher than max allowed threshold"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mockSavingsClient, nil, nil, nil,
				mockCxS3Client, nil, mockVendorMappingClient, mockCustomerIdentifier, tt.fields.cxConfig, mockDataCollectorHelper, nil, nil, nil, mockBCClient, nil, nil, nil, nil)
			got, err := s.GetBulkUserInfoByTicketIds(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBulkUserInfoByTicketIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.Status, tt.want.Status) {
				t.Errorf("GetBulkUserInfoByTicketIds() got Status = %v, want status %v", got.Status, tt.want.Status)
			}
		})
	}
}

func TestService_GetBulkUserInfo(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockCustomerIdentifier := mockHelper.NewMockICustomerIdentifier(ctr)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
	mockVendorMappingClient := mockVm.NewMockVendorMappingServiceClient(ctr)
	mockDataCollectorHelper := mockHelper2.NewMockIDataCollectorHelper(ctr)
	mockBCClient := mocksBC.NewMockBankCustomerServiceClient(ctr)

	defer func() {
		ctr.Finish()
	}()

	mockCxS3Client := mockS3.NewMockS3Client(ctr)

	type fields struct {
		cxConfig *cxGenConf.Config
	}
	type args struct {
		ctx context.Context
		req *profilePb.GetBulkUserInfoRequest
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *profilePb.GetBulkUserInfoResponse
		wantErr bool
	}{
		{
			name:   "empty ID list passed in request",
			fields: fields{cxConfig: conf},
			args: args{
				ctx: context.Background(),
				req: &profilePb.GetBulkUserInfoRequest{},
			},
			want: &profilePb.GetBulkUserInfoResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("ID list can't be empty"),
			},
			wantErr: false,
		},
		{
			name:   "IDs count is higher than max allowed threshold",
			fields: fields{cxConfig: conf},
			args: args{
				ctx: context.Background(),
				req: GetBulkUserInfoRequest1,
			},
			want: &profilePb.GetBulkUserInfoResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("IDs count is higher than max allowed threshold"),
			},
			wantErr: false,
		},
		{
			name:   "failed to read response columns for role",
			fields: fields{cxConfig: conf},
			args: args{
				ctx: context.Background(),
				req: GetBulkUserInfoRequest2,
			},
			want: &profilePb.GetBulkUserInfoResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to read response columns for role"),
			},
			wantErr: false,
		},
		// TODO: avoid panic in goroutine to make this work
		// {
		//	name:   "Success",
		//	fields: fields{cxConfig: conf},
		//	args: args{
		//		ctx: context.Background(),
		//		req: GetBulkUserInfoRequestSuccess,
		//	},
		//	want: &profilePb.GetBulkUserInfoResponse{
		//		Status: rpc.StatusOk(),
		//	},
		//	wantErr: false,
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(mockSavingsClient, nil, nil, nil,
				mockCxS3Client, nil, mockVendorMappingClient, mockCustomerIdentifier, tt.fields.cxConfig, mockDataCollectorHelper, nil, nil, nil, mockBCClient, nil, nil, nil, nil)
			got, err := s.GetBulkUserInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBulkUserInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.Status, tt.want.Status) {
				t.Errorf("GetBulkUserInfo() got Status = %v, want status %v", got.Status, tt.want.Status)
			}
			if !reflect.DeepEqual(got.FailedIdList, tt.want.FailedIdList) {
				t.Errorf("GetBulkUserInfo() got FailedIdList = %v, want FailedIdList %v", got.FailedIdList, tt.want.FailedIdList)
			}
		})
	}
}

var (
	customerId        = "vendor-customer-id"
	getCustProfileReq = &profilePb.GetCustomerProfileRequest{
		Header: &cx.Header{
			AgentEmail:  "<EMAIL>",
			AccessToken: "",
			TicketId:    0,
			Identifier:  nil,
			Ticket:      nil,
			User: &user.User{
				Id:      "user1",
				Profile: nil,
			},
			Actor: &types.Actor{
				Id: "actor-1",
			},
			InformationLevel:            0,
			AccessLevel:                 0,
			MonorailId:                  0,
			CustomerDetails:             nil,
			AuthVersion:                 0,
			CustomerAuthIdentifierType:  0,
			CustomerAuthIdentifierValue: "",
		},
	}
	getCustProfileReq1 = &profilePb.GetCustomerProfileRequest{
		Header: &cx.Header{
			AgentEmail:  "<EMAIL>",
			AccessToken: "",
			TicketId:    0,
			Identifier:  nil,
			Ticket:      nil,
			User: &user.User{
				Id:      "user1",
				Profile: nil,
			},
			Actor: &types.Actor{
				Id: "actor-1",
			},
			InformationLevel:            0,
			AccessLevel:                 0,
			MonorailId:                  0,
			CustomerDetails:             nil,
			AuthVersion:                 0,
			CustomerAuthIdentifierType:  0,
			CustomerAuthIdentifierValue: "",
		},
		ForceBalanceUpdate: profilePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_NEEDED,
	}
	getBankAccountsRes = &extacct.GetBankAccountsResponse{
		Status: rpc.StatusOk(),
		BankAccounts: []*extacct.BankAccount{
			{
				Ifsc:          "ACBD0001234",
				AccountNumber: "**************",
				Name:          "John Doe",
			},
		},
		NameMatchRetriesLeft:       3,
		AccVerificationRetriesLeft: 2,
		BankAccountVerifications: []*extacct.BankAccountVerification{
			{
				Id:            "bavid1",
				ActorId:       "actor1",
				AccountNumber: "ACBD0001234",
				Ifsc:          "**************",
				NameAtBank:    "John Doe",
				OverallStatus: extacct.OverallStatus_OVERALL_STATUS_SUCCESS,
				VendorStatus: &commonvgpb.VendorStatus{
					Code:        "101",
					Description: "Transaction Successful",
				},
				VendorReqId:   "vendorReq1",
				Vendor:        extacct.Vendor_KARZA,
				FailureReason: extacct.FailureReason_FAILURE_REASON_UNSPECIFIED,
				NameMatchData: &extacct.NameMatchData{
					KycName: &commontypes.Name{
						FirstName:  "John",
						MiddleName: "",
						LastName:   "Doe",
						Honorific:  "",
					},
					UserGivenName: "John Doe",
					UserGivenNameMatchScore: &kyc.NameMatchScore{
						Score: 1,
					},
					NameAtBank: "John Doe",
					NameAtBankMatchScore: &kyc.NameMatchScore{
						Score: 1,
					},
				},
				Caller: &extacct.Caller{
					Source: extacct.Source_SOURCE_USER,
				},
				CreatedAt:     timestamppb.New(time.Time{}),
				UpdatedAt:     timestamppb.New(time.Time{}),
				DeletedAtUnix: 0,
			},
			{
				Id:            "bavid2",
				ActorId:       "actor1",
				AccountNumber: "*********",
				Ifsc:          "**************",
				OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
				VendorStatus: &commonvgpb.VendorStatus{
					Code:        "101",
					Description: "IMPS Service not available for the selected bank",
				},
				VendorReqId:   "vendorReq2",
				Vendor:        extacct.Vendor_KARZA,
				FailureReason: extacct.FailureReason_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK,
				NameMatchData: &extacct.NameMatchData{
					KycName: &commontypes.Name{
						FirstName:  "John",
						MiddleName: "",
						LastName:   "Doe",
						Honorific:  "",
					},
					UserGivenName: "John Doe",
					UserGivenNameMatchScore: &kyc.NameMatchScore{
						Score: 1,
					},
				},
				Caller: &extacct.Caller{
					Source: extacct.Source_SOURCE_USER,
				},
				CreatedAt:     timestamppb.New(time.Time{}),
				UpdatedAt:     timestamppb.New(time.Time{}),
				DeletedAtUnix: 0,
			},
			{
				Id:            "bavid3",
				ActorId:       "actor1",
				AccountNumber: "ACBD0001234",
				Ifsc:          "**************",
				OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
				FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH,
				NameMatchData: &extacct.NameMatchData{
					KycName: &commontypes.Name{
						FirstName:  "John",
						MiddleName: "",
						LastName:   "Doe",
						Honorific:  "",
					},
					UserGivenName: "Dee Jay",
					UserGivenNameMatchScore: &kyc.NameMatchScore{
						Score: 0.3,
					},
				},
				Caller: &extacct.Caller{
					Source: extacct.Source_SOURCE_USER,
				},
				CreatedAt:     timestamppb.New(time.Time{}),
				UpdatedAt:     timestamppb.New(time.Time{}),
				DeletedAtUnix: 0,
			},
			{
				Id:            "bavid4",
				ActorId:       "actor1",
				AccountNumber: "ACBD1234",
				Ifsc:          "************",
				OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
				FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH,
				NameMatchData: &extacct.NameMatchData{
					KycName: &commontypes.Name{
						FirstName:  "John",
						MiddleName: "",
						LastName:   "Doe",
						Honorific:  "",
					},
					UserGivenName: "Ash Ketchup",
					UserGivenNameMatchScore: &kyc.NameMatchScore{
						Score: 0.25,
					},
				},
				Caller: &extacct.Caller{
					Source: extacct.Source_SOURCE_USER,
				},
				CreatedAt:     timestamppb.New(time.Time{}),
				UpdatedAt:     timestamppb.New(time.Time{}),
				DeletedAtUnix: 0,
			},
		},
	}
	getCustProfileResp = &profilePb.GetCustomerProfileResponse{
		Status: rpc.StatusOk(),
		Account: &profilePb.CustomerAccount{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK.String(),
		},
		FiAccountStatus:   "Account created",
		SignStatus:        "Present at Bank",
		CustomerId:        customerId,
		LegalName:         getCustProfileReq.GetHeader().GetUser().GetProfile().GetKycName(),
		AccessRevokeState: getCustProfileReq.GetHeader().GetUser().GetAccessRevokeState().String(),
		AccessRevokeDetails: &profilePb.AccessRevokeDetails{
			AccessRevokeStatus: getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetAccessRevokeStatus().String(),
			Reason:             getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetReason().String(),
			Remarks:            getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetRemarks(),
			UpdatedAt:          getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetUpdatedAt(),
			UpdatedBy:          getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetUpdatedBy(),
		},
		ExternalAccountDetails: &profilePb.ExternalAccountDetails{
			HasVerifiedAccounts: true,
			VerifiedAccounts: []*profilePb.BankAccount{
				{
					Ifsc:          "ACBD0001234",
					AccountNumber: mask.GetMaskedAccountNumber("**************", ""),
					Name:          "John Doe",
				},
			},
			BankAccountVerifications: []*profilePb.BankAccountVerification{
				{
					Id:            "bavid1",
					ActorId:       "actor1",
					AccountNumber: mask.GetMaskedAccountNumber("ACBD0001234", ""),
					Ifsc:          "**************",
					NameAtBank:    "John Doe",
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_SUCCESS.String(),
					VendorStatus: &profilePb.VendorStatus{
						Code:        "101",
						Description: "Transaction Successful",
					},
					VendorReqId:   "vendorReq1",
					Vendor:        commonvgpb.Vendor_KARZA.String(),
					FailureReason: extacct.FailureReason_FAILURE_REASON_UNSPECIFIED.String(),
					NameMatchData: &profilePb.NameMatchData{
						KycName: &commontypes.Name{
							FirstName:  "John",
							MiddleName: "",
							LastName:   "Doe",
							Honorific:  "",
						},
						UserGivenName:           "John Doe",
						UserGivenNameMatchScore: 1,
						NameAtBank:              "John Doe",
						NameAtBankMatchScore:    1,
					},
					Caller: &profilePb.Caller{
						Source: extacct.Source_SOURCE_USER.String(),
					},
					CreatedAt:     timestamppb.New(time.Time{}),
					UpdatedAt:     timestamppb.New(time.Time{}),
					DeletedAtUnix: 0,
				},
				{
					Id:            "bavid2",
					ActorId:       "actor1",
					AccountNumber: mask.GetMaskedAccountNumber("*********", ""),
					Ifsc:          "**************",
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(),
					VendorStatus: &profilePb.VendorStatus{
						Code:        "101",
						Description: "IMPS Service not available for the selected bank",
					},
					VendorReqId:   "vendorReq2",
					Vendor:        commonvgpb.Vendor_KARZA.String(),
					FailureReason: extacct.FailureReason_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK.String(),
					NameMatchData: &profilePb.NameMatchData{
						KycName: &commontypes.Name{
							FirstName:  "John",
							MiddleName: "",
							LastName:   "Doe",
							Honorific:  "",
						},
						UserGivenName:           "John Doe",
						UserGivenNameMatchScore: 1,
					},
					Caller: &profilePb.Caller{
						Source: extacct.Source_SOURCE_USER.String(),
					},
					CreatedAt:     timestamppb.New(time.Time{}),
					UpdatedAt:     timestamppb.New(time.Time{}),
					DeletedAtUnix: 0,
				},
				{
					Id:            "bavid3",
					ActorId:       "actor1",
					AccountNumber: mask.GetMaskedAccountNumber("ACBD0001234", ""),
					Ifsc:          "**************",
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(),
					VendorStatus:  &profilePb.VendorStatus{},
					Vendor:        commonvgpb.Vendor_VENDOR_UNSPECIFIED.String(),
					FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH.String(),
					NameMatchData: &profilePb.NameMatchData{
						KycName: &commontypes.Name{
							FirstName:  "John",
							MiddleName: "",
							LastName:   "Doe",
							Honorific:  "",
						},
						UserGivenName:           "Dee Jay",
						UserGivenNameMatchScore: 0.3,
					},
					Caller: &profilePb.Caller{
						Source: extacct.Source_SOURCE_USER.String(),
					},
					CreatedAt:     timestamppb.New(time.Time{}),
					UpdatedAt:     timestamppb.New(time.Time{}),
					DeletedAtUnix: 0,
				},
				{
					Id:            "bavid4",
					ActorId:       "actor1",
					AccountNumber: mask.GetMaskedAccountNumber("ACBD1234", ""),
					Ifsc:          "************",
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(),
					VendorStatus:  &profilePb.VendorStatus{},
					Vendor:        commonvgpb.Vendor_VENDOR_UNSPECIFIED.String(),
					FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH.String(),
					NameMatchData: &profilePb.NameMatchData{
						KycName: &commontypes.Name{
							FirstName:  "John",
							MiddleName: "",
							LastName:   "Doe",
							Honorific:  "",
						},
						UserGivenName:           "Ash Ketchup",
						UserGivenNameMatchScore: 0.25,
					},
					Caller: &profilePb.Caller{
						Source: extacct.Source_SOURCE_USER.String(),
					},
					CreatedAt:     timestamppb.New(time.Time{}),
					UpdatedAt:     timestamppb.New(time.Time{}),
					DeletedAtUnix: 0,
				},
			},
			NameMatchRetriesLeft:       3,
			AccVerificationRetriesLeft: 2,
		},
		BalanceRefreshData: &profilePb.GetCustomerProfileResponse_BalanceRefreshData{
			Timestamp: "-",
		},
		LevelValues: []*webui.LabelValue{
			{
				Label: "Account Operational Status",
				Value: []string{"Active"},
			},
			{
				Label: "Residential Status",
				Value: []string{"NRI"},
			},
		},
	}
	getCustProfileResp1 = &profilePb.GetCustomerProfileResponse{
		Status: rpc.StatusOk(),
		Account: &profilePb.CustomerAccount{
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK.String(),
		},
		FiAccountStatus:   "Account created",
		SignStatus:        "Present at Bank",
		CustomerId:        customerId,
		LegalName:         getCustProfileReq.GetHeader().GetUser().GetProfile().GetKycName(),
		AccessRevokeState: getCustProfileReq.GetHeader().GetUser().GetAccessRevokeState().String(),
		AccessRevokeDetails: &profilePb.AccessRevokeDetails{
			AccessRevokeStatus: getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetAccessRevokeStatus().String(),
			Reason:             getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetReason().String(),
			Remarks:            getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetRemarks(),
			UpdatedAt:          getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetUpdatedAt(),
			UpdatedBy:          getCustProfileReq.GetHeader().GetUser().GetAccessRevokeDetails().GetUpdatedBy(),
		},
		ExternalAccountDetails: &profilePb.ExternalAccountDetails{
			HasVerifiedAccounts: true,
			VerifiedAccounts: []*profilePb.BankAccount{
				{
					Ifsc:          "ACBD0001234",
					AccountNumber: mask.GetMaskedAccountNumber("**************", ""),
					Name:          "John Doe",
				},
			},
			BankAccountVerifications: []*profilePb.BankAccountVerification{
				{
					Id:            "bavid1",
					ActorId:       "actor1",
					AccountNumber: mask.GetMaskedAccountNumber("ACBD0001234", ""),
					Ifsc:          "**************",
					NameAtBank:    "John Doe",
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_SUCCESS.String(),
					VendorStatus: &profilePb.VendorStatus{
						Code:        "101",
						Description: "Transaction Successful",
					},
					VendorReqId:   "vendorReq1",
					Vendor:        commonvgpb.Vendor_KARZA.String(),
					FailureReason: extacct.FailureReason_FAILURE_REASON_UNSPECIFIED.String(),
					NameMatchData: &profilePb.NameMatchData{
						KycName: &commontypes.Name{
							FirstName:  "John",
							MiddleName: "",
							LastName:   "Doe",
							Honorific:  "",
						},
						UserGivenName:           "John Doe",
						UserGivenNameMatchScore: 1,
						NameAtBank:              "John Doe",
						NameAtBankMatchScore:    1,
					},
					Caller: &profilePb.Caller{
						Source: extacct.Source_SOURCE_USER.String(),
					},
					CreatedAt:     timestamppb.New(time.Time{}),
					UpdatedAt:     timestamppb.New(time.Time{}),
					DeletedAtUnix: 0,
				},
				{
					Id:            "bavid2",
					ActorId:       "actor1",
					AccountNumber: mask.GetMaskedAccountNumber("*********", ""),
					Ifsc:          "**************",
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(),
					VendorStatus: &profilePb.VendorStatus{
						Code:        "101",
						Description: "IMPS Service not available for the selected bank",
					},
					VendorReqId:   "vendorReq2",
					Vendor:        commonvgpb.Vendor_KARZA.String(),
					FailureReason: extacct.FailureReason_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK.String(),
					NameMatchData: &profilePb.NameMatchData{
						KycName: &commontypes.Name{
							FirstName:  "John",
							MiddleName: "",
							LastName:   "Doe",
							Honorific:  "",
						},
						UserGivenName:           "John Doe",
						UserGivenNameMatchScore: 1,
					},
					Caller: &profilePb.Caller{
						Source: extacct.Source_SOURCE_USER.String(),
					},
					CreatedAt:     timestamppb.New(time.Time{}),
					UpdatedAt:     timestamppb.New(time.Time{}),
					DeletedAtUnix: 0,
				},
				{
					Id:            "bavid3",
					ActorId:       "actor1",
					AccountNumber: mask.GetMaskedAccountNumber("ACBD0001234", ""),
					Ifsc:          "**************",
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(),
					VendorStatus:  &profilePb.VendorStatus{},
					Vendor:        commonvgpb.Vendor_VENDOR_UNSPECIFIED.String(),
					FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH.String(),
					NameMatchData: &profilePb.NameMatchData{
						KycName: &commontypes.Name{
							FirstName:  "John",
							MiddleName: "",
							LastName:   "Doe",
							Honorific:  "",
						},
						UserGivenName:           "Dee Jay",
						UserGivenNameMatchScore: 0.3,
					},
					Caller: &profilePb.Caller{
						Source: extacct.Source_SOURCE_USER.String(),
					},
					CreatedAt:     timestamppb.New(time.Time{}),
					UpdatedAt:     timestamppb.New(time.Time{}),
					DeletedAtUnix: 0,
				},
				{
					Id:            "bavid4",
					ActorId:       "actor1",
					AccountNumber: mask.GetMaskedAccountNumber("ACBD1234", ""),
					Ifsc:          "************",
					OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(),
					VendorStatus:  &profilePb.VendorStatus{},
					Vendor:        commonvgpb.Vendor_VENDOR_UNSPECIFIED.String(),
					FailureReason: extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH.String(),
					NameMatchData: &profilePb.NameMatchData{
						KycName: &commontypes.Name{
							FirstName:  "John",
							MiddleName: "",
							LastName:   "Doe",
							Honorific:  "",
						},
						UserGivenName:           "Ash Ketchup",
						UserGivenNameMatchScore: 0.25,
					},
					Caller: &profilePb.Caller{
						Source: extacct.Source_SOURCE_USER.String(),
					},
					CreatedAt:     timestamppb.New(time.Time{}),
					UpdatedAt:     timestamppb.New(time.Time{}),
					DeletedAtUnix: 0,
				},
			},
			NameMatchRetriesLeft:       3,
			AccVerificationRetriesLeft: 2,
		},
		BalanceRefreshData: &profilePb.GetCustomerProfileResponse_BalanceRefreshData{
			Timestamp: "01-10-2023 09:30:00 AM",
			LastIncomingTransaction: &profilePb.GetCustomerProfileResponse_BalanceRefreshData_Transaction{
				Timestamp: "2023-05-01 04:00:00 +0000 UTC",
			},
			LastOutgoingTransaction: &profilePb.GetCustomerProfileResponse_BalanceRefreshData_Transaction{
				Timestamp: "2023-06-01 04:00:00 +0000 UTC",
			},
		},
		LevelValues: []*webui.LabelValue{
			{
				Label: "Account Operational Status",
				Value: []string{"Active"},
			},
			{
				Label: "Residential Status",
				Value: []string{"Indian"},
			},
		},
		AccountClosureRequestSection: getSaClosureRequestsSection(saClosureRequests),
	}
)

func TestService_GetCustomerProfile(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *profilePb.GetCustomerProfileRequest
	}
	type mockClients struct {
		mockAuthEngine    *mockAuthEngine.MockIAuthEngine
		mockSavingsClient *mocks2.MockSavingsClient
		mockUsersClient   *mockUser.MockUsersClient
		mockExtAcctClient *mocks.MockExternalAccountsClient
		mockBCClient      *mocksBC.MockBankCustomerServiceClient
		mockOnbClient     *mocks3.MockOnboardingClient
		mockOperClient    *mockOper.MockOperationalStatusServiceClient
		mockTxnCollector  *mockTxnCollectorHelper.MockITransactionsDataCollectorHelper
	}

	tests := []struct {
		name      string
		args      args
		mockSetup func(clients *mockClients)
		want      *profilePb.GetCustomerProfileResponse
		wantErr   error
	}{
		{
			name: "successful fetch with external account details",
			args: args{
				ctx: context.Background(),
				req: getCustProfileReq,
			},
			mockSetup: func(clients *mockClients) {
				clients.mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), getCustProfileReq.GetHeader(),
					getCustProfileReq.GetHeader().GetInformationLevel()).Return(false, nil)
				clients.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Eq(&savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: getCustProfileReq.GetHeader().GetUser().GetId(),
					},
				})).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						Id:          "acc-id",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						ActorId:     getCustProfileReq.GetHeader().GetActor().GetId(),
					},
				}, nil)
				clients.mockUsersClient.EXPECT().GetCustomerDetails(gomock.Any(), gomock.Eq(&user.GetCustomerDetailsRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					UserId:     getCustProfileReq.GetHeader().GetUser().GetId(),
					ActorId:    getCustProfileReq.GetHeader().GetActor().GetId(),
					Provenance: user.Provenance_SHERLOCK,
				})).Return(nil, nil)
				clients.mockUsersClient.EXPECT().GetUserDeviceProperties(gomock.Any(), &user.GetUserDevicePropertiesRequest{
					ActorId: getCustProfileReq.GetHeader().GetActor().GetId(),
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				}).Return(nil, nil)
				clients.mockExtAcctClient.EXPECT().GetBankAccounts(gomock.Any(), gomock.Eq(&extacct.GetBankAccountsRequest{
					ActorId: getCustProfileReq.GetHeader().GetActor().GetId(),
				})).Return(getBankAccountsRes, nil)
				clients.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: getCustProfileReq.GetHeader().GetActor().GetId(),
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						VendorCustomerId: customerId,
					},
				}, nil)
				clients.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboarding.GetFeatureDetailsRequest{
					Feature: onboarding.Feature_FEATURE_SA,
					ActorId: getCustProfileReq.GetHeader().GetActor().GetId(),
				}).Return(&onboarding.GetFeatureDetailsResponse{
					IsFiLiteUser: true,
					Status:       rpc.StatusOk(),
				}, nil)
				clients.mockOperClient.EXPECT().GetOperationalStatus(gomock.Any(), &operstatus.GetOperationalStatusRequest{
					DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
					AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: "acc-id",
					},
				}).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operstatus.OperationalStatusInfo{
						VendorResponse: &operstatus.OperationalStatusVendorResponse{
							VendorResponse: &operstatus.OperationalStatusVendorResponse_FederalAccountStatusEnquiryResponse{
								FederalAccountStatusEnquiryResponse: &status.AccountStatusEnquiryResponse{
									SignCount: 1,
								},
							},
						},
						OperationalStatus: acountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
					},
				}, nil)
				clients.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    getCustProfileReq.GetHeader().GetActor().GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status:  rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{Feature: onboarding.Feature_FEATURE_NON_RESIDENT_SA},
				}, nil)
				clients.mockTxnCollector.EXPECT().GetRecentTransactions(gomock.Any(), getCustProfileReq.GetHeader().GetActor().GetId(),
					1, workflow.TransactionType_TRANSACTION_TYPE_CREDIT_SUCCESSFUL).Return(
					[]*transaction.OrderWithTransaction{}, nil)
				clients.mockTxnCollector.EXPECT().GetRecentTransactions(gomock.Any(), getCustProfileReq.GetHeader().GetActor().GetId(),
					1, workflow.TransactionType_TRANSACTION_TYPE_DEBIT_SUCCESSFUL).Return(
					[]*transaction.OrderWithTransaction{}, nil)
				clients.mockSavingsClient.EXPECT().GetSaClosureRequestsByFilter(gomock.Any(), gomock.Any()).
					Return(&savingsPb.GetSaClosureRequestsByFilterResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			want:    getCustProfileResp,
			wantErr: nil,
		},
		{
			name: "successful fetch with external account details with force balance refresh",
			args: args{
				ctx: context.Background(),
				req: getCustProfileReq1,
			},
			mockSetup: func(clients *mockClients) {
				clients.mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), getCustProfileReq.GetHeader(),
					getCustProfileReq.GetHeader().GetInformationLevel()).Return(false, nil)
				clients.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Eq(&savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: getCustProfileReq.GetHeader().GetUser().GetId(),
					},
				})).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						Id:          "acc-id",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						ActorId:     getCustProfileReq.GetHeader().GetActor().GetId(),
					},
				}, nil)
				clients.mockUsersClient.EXPECT().GetCustomerDetails(gomock.Any(), gomock.Eq(&user.GetCustomerDetailsRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					UserId:     getCustProfileReq.GetHeader().GetUser().GetId(),
					ActorId:    getCustProfileReq.GetHeader().GetActor().GetId(),
					Provenance: user.Provenance_SHERLOCK,
				})).Return(nil, nil)
				clients.mockUsersClient.EXPECT().GetUserDeviceProperties(gomock.Any(), gomock.Eq(&user.GetUserDevicePropertiesRequest{
					ActorId: getCustProfileReq.GetHeader().GetActor().GetId(),
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO,
						types.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO,
					},
				})).Return(nil, nil)
				clients.mockExtAcctClient.EXPECT().GetBankAccounts(gomock.Any(), gomock.Eq(&extacct.GetBankAccountsRequest{
					ActorId: getCustProfileReq.GetHeader().GetActor().GetId(),
				})).Return(getBankAccountsRes, nil)
				clients.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: getCustProfileReq.GetHeader().GetActor().GetId(),
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						VendorCustomerId: customerId,
					},
				}, nil)
				clients.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboarding.GetFeatureDetailsRequest{
					Feature: onboarding.Feature_FEATURE_SA,
					ActorId: getCustProfileReq.GetHeader().GetActor().GetId(),
				}).Return(&onboarding.GetFeatureDetailsResponse{
					IsFiLiteUser: true,
					Status:       rpc.StatusOk(),
				}, nil)
				clients.mockOperClient.EXPECT().GetOperationalStatus(gomock.Any(), &operstatus.GetOperationalStatusRequest{
					DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
					AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: "acc-id",
					},
				}).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operstatus.OperationalStatusInfo{
						VendorResponse: &operstatus.OperationalStatusVendorResponse{
							VendorResponse: &operstatus.OperationalStatusVendorResponse_FederalAccountStatusEnquiryResponse{
								FederalAccountStatusEnquiryResponse: &status.AccountStatusEnquiryResponse{
									SignCount: 1,
								},
							},
						},
						OperationalStatus: acountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
					},
				}, nil)
				clients.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    getCustProfileReq.GetHeader().GetActor().GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status:  rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{Feature: onboarding.Feature_FEATURE_SA},
				}, nil)
				clients.mockTxnCollector.EXPECT().GetRecentTransactions(gomock.Any(), getCustProfileReq.GetHeader().GetActor().GetId(),
					1, workflow.TransactionType_TRANSACTION_TYPE_CREDIT_SUCCESSFUL).Return(
					[]*transaction.OrderWithTransaction{
						{
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        245,
							},
							TxnUpdatedAt: timestamppb.New(time.Date(2023, 05, 1, 0, 0, 0, 0, dateTimePkg.EST5EDT)),
						},
					}, nil)
				clients.mockTxnCollector.EXPECT().GetRecentTransactions(gomock.Any(), getCustProfileReq.GetHeader().GetActor().GetId(),
					1, workflow.TransactionType_TRANSACTION_TYPE_DEBIT_SUCCESSFUL).Return(
					[]*transaction.OrderWithTransaction{
						{
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        300,
							},
							TxnUpdatedAt: timestamppb.New(time.Date(2023, 06, 1, 0, 0, 0, 0, dateTimePkg.EST5EDT)),
						},
					}, nil)
				clients.mockSavingsClient.EXPECT().GetAccountBalanceWithSummary(gomock.Any(), &savingsPb.GetAccountBalanceWithSummaryRequest{
					Identifier:         &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: "acc-id"},
					ActorId:            getCustProfileReq.GetHeader().GetActor().GetId(),
					TimeRange:          savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
					ForceBalanceUpdate: savingsPb.GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_NEEDED,
				}).Return(
					&savingsPb.GetAccountBalanceWithSummaryResponse{
						Status:    rpc.StatusOk(),
						BalanceAt: timestamppb.New(time.Date(2023, 10, 1, 0, 0, 0, 0, dateTimePkg.EST5EDT)),
					}, nil)
				clients.mockSavingsClient.EXPECT().GetSaClosureRequestsByFilter(gomock.Any(), gomock.Any()).
					Return(&savingsPb.GetSaClosureRequestsByFilterResponse{
						Status:                        rpc.StatusOk(),
						SavingsAccountClosureRequests: saClosureRequests,
					}, nil)
			},
			want:    getCustProfileResp1,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockSavingsClient := mocks2.NewMockSavingsClient(ctr)
			mockAuthEng := mockAuthEngine.NewMockIAuthEngine(ctr)
			mockUsersClient := mockUser.NewMockUsersClient(ctr)
			mockExtAcctClient := mocks.NewMockExternalAccountsClient(ctr)
			mockBCClient := mocksBC.NewMockBankCustomerServiceClient(ctr)
			mockOnb := mocks3.NewMockOnboardingClient(ctr)
			mockOperClient := mockOper.NewMockOperationalStatusServiceClient(ctr)
			mockTxnCollector := mockTxnCollectorHelper.NewMockITransactionsDataCollectorHelper(ctr)
			s := NewService(mockSavingsClient, mockAuthEng, mockUsersClient, nil,
				nil, nil, nil, nil, nil, nil, nil, mockExtAcctClient, nil, mockBCClient, mockOnb, mockOperClient, mockTxnCollector, nil)
			defer func() {
				ctr.Finish()
				s = nil
			}()

			if tt.mockSetup != nil {
				tt.mockSetup(&mockClients{
					mockAuthEngine:    mockAuthEng,
					mockSavingsClient: mockSavingsClient,
					mockUsersClient:   mockUsersClient,
					mockExtAcctClient: mockExtAcctClient,
					mockBCClient:      mockBCClient,
					mockOnbClient:     mockOnb,
					mockOperClient:    mockOperClient,
					mockTxnCollector:  mockTxnCollector,
				})
			}
			got, err := s.GetCustomerProfile(tt.args.ctx, tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if err == nil {
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetCustomerProfile() \ngot = %v, \nwant= %v", got, tt.want)
				}
			}
		})
	}
}

func Test_getLatestClosedAccountInfo(t *testing.T) {
	t.Parallel()
	type args struct {
		cbts []*savingsPb.ClosedAccountBalanceTransfer
	}
	tests := []struct {
		name string
		args args
		want *savingsPb.ClosedAccountBalanceTransfer
	}{
		{
			name: "returned latest",
			args: args{
				cbts: []*savingsPb.ClosedAccountBalanceTransfer{
					{
						Id:        "250 sec",
						UpdatedAt: timestamppb.New(time.Unix(500, 0)),
					},
					{
						Id:        "100 sec",
						UpdatedAt: timestamppb.New(time.Unix(100, 0)),
					},
					{
						Id:        "750 sec",
						UpdatedAt: timestamppb.New(time.Unix(500, 0)),
					},
					{
						Id:        "1000 sec",
						UpdatedAt: timestamppb.New(time.Unix(1000, 0)),
					},
					{
						Id:        "500 sec",
						UpdatedAt: timestamppb.New(time.Unix(500, 0)),
					},
				},
			},
			want: &savingsPb.ClosedAccountBalanceTransfer{
				Id:        "1000 sec",
				UpdatedAt: timestamppb.New(time.Unix(1000, 0)),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, getLatestClosedAccountInfo(tt.args.cbts), "getLatestClosedAccountInfo(%v)", tt.args.cbts)
		})
	}
}

func Test_getClosureReqStatusText(t *testing.T) {
	t.Parallel()
	type args struct {
		status savingsPb.SAClosureRequestStatus
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "submitted",
			args: args{status: savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SUBMITTED},
			want: "submitted",
		},
		{
			name: "support ticket created and closed",
			args: args{status: savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SUPPORT_TICKET_CREATED_AND_CLOSED},
			want: "support ticket created and closed",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, getClosureReqStatusText(tt.args.status), "getClosureReqStatusText(%v)", tt.args.status)
		})
	}
}
