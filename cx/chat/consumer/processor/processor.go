package processor

import (
	"context"

	"google.golang.org/protobuf/types/known/structpb"

	chatConsumerPb "github.com/epifi/gamma/api/cx/chat/consumer"
)

// IFreshchatActionProcessor interface which has to be implemented by individual processor to process specific freshchat action
type IFreshchatActionProcessor interface {
	// ProcessFreshchatAction provides queue event request as input
	// expects queue response (trans, success, failure) header in output
	ProcessFreshchatAction(ctx context.Context, req *chatConsumerPb.ProcessFreshchatEventRequest) (*chatConsumerPb.ProcessFreshchatEventResponse, error)
}

// INuggetEventProcessor interface which has to be implemented by individual processor to process specific nugget events
type INuggetEventProcessor interface {
	// ProcessEvent expects field to process and publishes the fields in a rudder event
	ProcessEvent(ctx context.Context, fields map[string]*structpb.Value) (*chatConsumerPb.ProcessNuggetEventResponse, error)
}
