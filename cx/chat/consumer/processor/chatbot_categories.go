package processor

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/api/queue"
	event "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	chatConsumerPb "github.com/epifi/gamma/api/cx/chat/consumer"
	nuggetEvent "github.com/epifi/gamma/cx/events/nugget"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

type ChatbotShowCategoriesEventProcessor struct {
	eventBroker event.Broker
}

func NewChatbotShowCategoriesEventProcessor(eventBroker event.Broker) *ChatbotShowCategoriesEventProcessor {
	return &ChatbotShowCategoriesEventProcessor{
		eventBroker: eventBroker,
	}
}

var _ INuggetEventProcessor = &ChatbotShowCategoriesEventProcessor{}

func (c *ChatbotShowCategoriesEventProcessor) ProcessEvent(ctx context.Context, fields map[string]*structpb.Value) (*chatConsumerPb.ProcessNuggetEventResponse, error) {
	// Remove info log once prod testing is done
	logger.Info(ctx, "Extracted Fields", zap.Any("fieldMap", fields))
	zomatoId, botType, ticketId, categoriesList := fields[zomato_id].GetStringValue(), fields[bot_type].GetStringValue(), fields[ticket_id].GetStringValue(), fields[categories].GetListValue().GetValues()

	var categoriesStr []string
	for _, category := range categoriesList {
		categoriesStr = append(categoriesStr, category.GetStringValue())
	}
	// Validate required fields
	if zomatoId == "" || botType == "" || ticketId == "" {
		cxLogger.Error(ctx, "Missing required fields for chatbot routing event",
			zap.String("zomatoId", zomatoId),
			zap.String("botType", botType),
			zap.String("ticketId", ticketId))
		return &chatConsumerPb.ProcessNuggetEventResponse{
			ResponseHeader: &queue.ConsumerResponseHeader{
				Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}

	c.eventBroker.AddToBatch(ctx, nuggetEvent.NewChatbotShowCategoriesResponseSentEvent(zomatoId, botType, ticketId, categoriesStr))

	return &chatConsumerPb.ProcessNuggetEventResponse{
		ResponseHeader: &queue.ConsumerResponseHeader{
			Status: queue.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}
