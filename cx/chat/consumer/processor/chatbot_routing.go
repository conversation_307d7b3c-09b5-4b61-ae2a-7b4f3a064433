package processor

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/api/queue"
	event "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	chatConsumerPb "github.com/epifi/gamma/api/cx/chat/consumer"
	nuggetEvent "github.com/epifi/gamma/cx/events/nugget"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

const (
	zomato_id         = "user_id"
	bot_type          = "bot_type"
	ticket_id         = "ticket_id"
	transaction_id    = "transaction_id"
	categories        = "categories"
	user_query        = "user_query"
	query_type        = "query_type"
	escalation_reason = "escalation_reason"
	agent_assigned_at = "agent_assigned_at"
)

type ChatbotRoutingEventProcessor struct {
	eventBroker event.Broker
}

func NewChatbotRoutingProcessor(eventBroker event.Broker) *ChatbotRoutingEventProcessor {
	return &ChatbotRoutingEventProcessor{
		eventBroker: eventBroker,
	}
}

var _ INuggetEventProcessor = &ChatbotRoutingEventProcessor{}

func (c *ChatbotRoutingEventProcessor) ProcessEvent(ctx context.Context, fields map[string]*structpb.Value) (*chatConsumerPb.ProcessNuggetEventResponse, error) {
	// Remove info log once prod testing is done
	logger.Info(ctx, "Extracted Fields", zap.Any("fieldMap", fields))
	zomatoId, botType, ticketId := fields[zomato_id].GetStringValue(), fields[bot_type].GetStringValue(), fields[ticket_id].GetStringValue()

	// Validate required fields
	if zomatoId == "" || botType == "" || ticketId == "" {
		cxLogger.Error(ctx, "Missing required fields for chatbot routing event",
			zap.String("zomatoId", zomatoId),
			zap.String("botType", botType),
			zap.String("ticketId", ticketId))
		return &chatConsumerPb.ProcessNuggetEventResponse{
			ResponseHeader: &queue.ConsumerResponseHeader{
				Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}

	c.eventBroker.AddToBatch(ctx, nuggetEvent.NewChatbotRoutingEvent(zomatoId, botType, ticketId))

	return &chatConsumerPb.ProcessNuggetEventResponse{
		ResponseHeader: &queue.ConsumerResponseHeader{
			Status: queue.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}
