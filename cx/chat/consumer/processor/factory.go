package processor

import (
	chatPb "github.com/epifi/gamma/api/cx/chat"
	nuggetEvent "github.com/epifi/gamma/cx/events/nugget"

	"github.com/pkg/errors"
)

type IFreshchatActionFactory interface {
	GetFreshchatActionProcessor(fcAction chatPb.FreshchatCallbackAction) (IFreshchatActionProcessor, error)
}

type FreshchatActionFactory struct {
	messageCreateProcessor          *MessageCreateProcessor
	conversationAssignmentProcessor *ConversationAssignmentProcessor
	conversationResolutionProcessor *ConversationResolutionProcessor
}

func NewFreshchatActionFactory(messageCreateProcessor *MessageCreateProcessor, conversationAssignmentProcessor *ConversationAssignmentProcessor,
	conversationResolutionProcessor *ConversationResolutionProcessor) *FreshchatActionFactory {
	return &FreshchatActionFactory{
		messageCreateProcessor:          messageCreateProcessor,
		conversationAssignmentProcessor: conversationAssignmentProcessor,
		conversationResolutionProcessor: conversationResolutionProcessor,
	}
}

var _ IFreshchatActionFactory = &FreshchatActionFactory{}

// GetFreshchatActionProcessor method provides processor for specific freshchat action callbacks
// FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE when a message is created
// FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_ASSIGNMENT when a conversation is assigned to an agent
// FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_RESOLUTION when a conversation is resolved
func (f *FreshchatActionFactory) GetFreshchatActionProcessor(eventType chatPb.FreshchatCallbackAction) (IFreshchatActionProcessor, error) {
	switch eventType {
	case chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE:
		return f.messageCreateProcessor, nil
	case chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_ASSIGNMENT:
		return f.conversationAssignmentProcessor, nil
	case chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_RESOLUTION:
		return f.conversationResolutionProcessor, nil
	default:
		return nil, errors.New("no processor for this action")
	}
}

type INuggetEventProcessorFactory interface {
	GetProcessor(eventName string) (INuggetEventProcessor, error)
}

type NuggetEventProcessorFactory struct {
	chatbotRoutingEventProcessor            *ChatbotRoutingEventProcessor
	chatbotFetchMetadataEventProcessor      *ChatbotFetchMetadataEventProcessor
	chatbotShowCategoriesEventProcessor     *ChatbotShowCategoriesEventProcessor
	chatbotProcessQueryEventProcessor       *ChatbotProcessQueryEventProcessor
	chatbotEvaluateEscalationEventProcessor *ChatbotEvaluateEscalationEventProcessor
}

func NewNuggetEventProcessorFactory(chatbotRoutingEventProcessor *ChatbotRoutingEventProcessor,
	chatbotFetchMetadataEventProcessor *ChatbotFetchMetadataEventProcessor,
	chatbotShowCategoriesEventProcessor *ChatbotShowCategoriesEventProcessor,
	chatbotProcessQueryEventProcessor *ChatbotProcessQueryEventProcessor,
	chatbotEvaluateEscalationEventProcessor *ChatbotEvaluateEscalationEventProcessor) *NuggetEventProcessorFactory {
	return &NuggetEventProcessorFactory{
		chatbotRoutingEventProcessor:            chatbotRoutingEventProcessor,
		chatbotFetchMetadataEventProcessor:      chatbotFetchMetadataEventProcessor,
		chatbotShowCategoriesEventProcessor:     chatbotShowCategoriesEventProcessor,
		chatbotProcessQueryEventProcessor:       chatbotProcessQueryEventProcessor,
		chatbotEvaluateEscalationEventProcessor: chatbotEvaluateEscalationEventProcessor,
	}
}

var _ INuggetEventProcessorFactory = &NuggetEventProcessorFactory{}

// GetProcessor method provides processor for specific nugget event types
// Creates a generic processor with the event configuration
func (f *NuggetEventProcessorFactory) GetProcessor(eventName string) (INuggetEventProcessor, error) {
	switch eventName {
	case nuggetEvent.ChatbotRoutingEventName:
		return f.chatbotRoutingEventProcessor, nil
	case nuggetEvent.ChatbotFetchMetadataRequestReceivedEventName:
		return f.chatbotFetchMetadataEventProcessor, nil
	case nuggetEvent.ChatbotShowCategoriesResponseSentEventName:
		return f.chatbotShowCategoriesEventProcessor, nil
	case nuggetEvent.ChatbotProcessQueryRequestReceivedEventName:
		return f.chatbotProcessQueryEventProcessor, nil
	case nuggetEvent.ChatbotEvaluateEscalationRequestReceivedEventName:
		return f.chatbotEvaluateEscalationEventProcessor, nil
	default:
		return nil, errors.New("no processor for this action")
	}
}
