package consumer

import (
	"context"
	"fmt"

	"github.com/tidwall/gjson"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/queue"

	"google.golang.org/protobuf/types/known/structpb"

	chatConsumerPb "github.com/epifi/gamma/api/cx/chat/consumer"
	"github.com/epifi/gamma/cx/chat/consumer/helper"
	"github.com/epifi/gamma/cx/chat/consumer/processor"
	"github.com/epifi/gamma/cx/config/genconf"
	cxLogger "github.com/epifi/gamma/cx/logger"

	"go.uber.org/zap"
)

type Service struct {
	freshChatActionFactory      processor.IFreshchatActionFactory
	nuggetEventProcessorFactory processor.INuggetEventProcessorFactory
	genConf                     *genconf.Config
}

func NewService(freshChatActionFactory processor.IFreshchatActionFactory, nuggetEventProcessorFactory processor.INuggetEventProcessorFactory, genConf *genconf.Config) *Service {
	return &Service{
		freshChatActionFactory:      freshChatActionFactory,
		nuggetEventProcessorFactory: nuggetEventProcessorFactory,
		genConf:                     genConf,
	}
}

var _ chatConsumerPb.CxChatEventConsumerServer = &Service{}

func (s *Service) ProcessFreshchatEvent(ctx context.Context, req *chatConsumerPb.ProcessFreshchatEventRequest) (*chatConsumerPb.ProcessFreshchatEventResponse, error) {
	fcAction := req.GetAction()
	if req.GetData() == nil {
		cxLogger.Error(ctx, "Freshchat event data is nil", zap.Any("freshChatAction", fcAction))
		return helper.FcEventPermanentErrResp(), nil
	}
	// identify the processor for the call stage
	// if not found than return permanent failure in such cases
	fcEventProcessor, err := s.freshChatActionFactory.GetFreshchatActionProcessor(fcAction)
	if err != nil {
		cxLogger.Error(ctx, "error while fetching processor for Freshchat event", zap.Error(err), zap.Any("freshchatAction", fcAction))
		return helper.FcEventPermanentErrResp(), nil
	}

	// process call details by relevant processor
	return fcEventProcessor.ProcessFreshchatAction(ctx, req)
}

func (s *Service) ProcessNuggetEvent(ctx context.Context, req *chatConsumerPb.ProcessNuggetEventRequest) (*chatConsumerPb.ProcessNuggetEventResponse, error) {
	// Find matching event configuration
	var eventName string
	var eventConfig *genconf.EventDetails
	s.genConf.NuggetEventConfig().EventMap().Range(func(eventNameKey string, eventDetails *genconf.EventDetails) bool {
		if eventDetails.NuggetEventName() == req.GetEventName() && eventDetails.NuggetSubEventName() == req.GetSubEventName() {
			eventName = eventNameKey
			eventConfig = eventDetails
			return false // stop once event to be consumed is found
		}
		return true // continue iteration
	})

	if eventName == "" {
		cxLogger.Error(ctx, "No matching event configuration found",
			zap.String("requestEventName", req.GetEventName()),
			zap.String("requestSubEventName", req.GetSubEventName()))
		return &chatConsumerPb.ProcessNuggetEventResponse{
			ResponseHeader: &queue.ConsumerResponseHeader{
				Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}

	eventDataJSON, err := extractEventDataAsJSON(req.GetEventData())
	if err != nil {
		cxLogger.Error(ctx, "Failed to extract event data as JSON", zap.Error(err))
		return &chatConsumerPb.ProcessNuggetEventResponse{
			ResponseHeader: &queue.ConsumerResponseHeader{
				Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}

	// Extract fields using JSON paths from config
	fields, err := extractFieldsFromJSONPaths(eventDataJSON, eventConfig)
	if err != nil {
		cxLogger.Error(ctx, "Failed to extract fields using JSON paths",
			zap.Error(err))
		return &chatConsumerPb.ProcessNuggetEventResponse{
			ResponseHeader: &queue.ConsumerResponseHeader{
				Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}

	// Find the implementation of EventProcessor using EventName
	eventProcessor, err := s.nuggetEventProcessorFactory.GetProcessor(eventName)
	if err != nil {
		cxLogger.Error(ctx, "Failed to get event processor",
			zap.Error(err),
			zap.String("eventName", eventName))
		return &chatConsumerPb.ProcessNuggetEventResponse{
			ResponseHeader: &queue.ConsumerResponseHeader{
				Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}

	return eventProcessor.ProcessEvent(ctx, fields)
}

// extractEventDataAsJSON converts structpb.Value to JSON string for gjson processing
func extractEventDataAsJSON(eventData *structpb.Value) (string, error) {
	if eventData == nil {
		return "{}", nil
	}

	// Use the structpb.Struct.MarshalJSON method which is more appropriate
	if eventData.GetStructValue() != nil {
		jsonBytes, err := eventData.GetStructValue().MarshalJSON()
		if err != nil {
			return "", fmt.Errorf("failed to marshal event data: %w", err)
		}
		return string(jsonBytes), nil
	}

	// Fallback to protojson for other types
	jsonBytes, err := protojson.Marshal(eventData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal event data: %w", err)
	}

	return string(jsonBytes), nil
}

// extractFieldsFromJSONPaths extracts multiple fields from JSON data using provided paths
func extractFieldsFromJSONPaths(eventDataJSON string, eventConfig *genconf.EventDetails) (map[string]*structpb.Value, error) {
	var err error
	fields := make(map[string]*structpb.Value)

	eventConfig.FieldPathMap().Range(func(fieldName, fieldJsonPath string) bool {
		value := gjson.Get(eventDataJSON, fieldJsonPath)
		if !value.Exists() {
			err = fmt.Errorf("field not found at path: %s", fieldJsonPath)
			return false
		}
		protoVal, pErr := structpb.NewValue(value.Value())
		if pErr != nil {
			err = fmt.Errorf("failed to convert value to proto %s: %w, value: %s", fieldName, pErr, value.Value())
			return false
		}
		fields[fieldName] = protoVal
		return true
	})
	if err != nil {
		return nil, err
	}
	return fields, nil
}
