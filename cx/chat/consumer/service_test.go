package consumer

import (
	"context"
	"flag"
	"os"
	"reflect"
	"testing"

	chatPb "github.com/epifi/gamma/api/cx/chat"
	chatConsumerPb "github.com/epifi/gamma/api/cx/chat/consumer"
	"github.com/epifi/gamma/cx/chat/consumer/helper"
	"github.com/epifi/gamma/cx/test"
	mockProcessor "github.com/epifi/gamma/cx/test/mocks/chat/consumer/processor"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, teardown = test.InitTestServer(false)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_ProcessFreshchatEvent(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)

	defer func() {
		ctr.Finish()
	}()

	mockFcActionFactory := mockProcessor.NewMockIFreshchatActionFactory(ctr)
	mockFcActionProcessor := mockProcessor.NewMockIFreshchatActionProcessor(ctr)

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *chatConsumerPb.ProcessFreshchatEventRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *chatConsumerPb.ProcessFreshchatEventResponse
		wantErr bool
	}{
		{
			name: "invalid input param",
			args: args{
				mocks: []interface{}{},
				ctx:   context.Background(),
				req:   &chatConsumerPb.ProcessFreshchatEventRequest{},
			},
			want:    helper.FcEventPermanentErrResp(),
			wantErr: false,
		},
		{
			name: "no processor found",
			args: args{
				mocks: []interface{}{
					mockFcActionFactory.EXPECT().GetFreshchatActionProcessor(chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_ASSIGNMENT).Return(nil, errors.New("no valid processor found")),
				},
				ctx: context.Background(),
				req: &chatConsumerPb.ProcessFreshchatEventRequest{
					Action: chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_CONVERSATION_ASSIGNMENT,
					Data:   &chatConsumerPb.ProcessFreshchatEventRequest_Message{},
				},
			},
			want:    helper.FcEventPermanentErrResp(),
			wantErr: false,
		},
		{
			name: "error from processor",
			args: args{
				mocks: []interface{}{
					mockFcActionFactory.EXPECT().GetFreshchatActionProcessor(chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE).Return(mockFcActionProcessor, nil),
					mockFcActionProcessor.EXPECT().ProcessFreshchatAction(gomock.Any(), gomock.Any()).Return(helper.FcEventTransErrResp(), nil),
				},
				ctx: context.Background(),
				req: &chatConsumerPb.ProcessFreshchatEventRequest{
					Action: chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE,
					Data:   &chatConsumerPb.ProcessFreshchatEventRequest_Message{},
				},
			},
			want:    helper.FcEventTransErrResp(),
			wantErr: false,
		},
		{
			name: "successful",
			args: args{
				mocks: []interface{}{
					mockFcActionFactory.EXPECT().GetFreshchatActionProcessor(chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE).Return(mockFcActionProcessor, nil),
					mockFcActionProcessor.EXPECT().ProcessFreshchatAction(gomock.Any(), gomock.Any()).Return(helper.FcEventSuccessResp(), nil),
				},
				ctx: context.Background(),
				req: &chatConsumerPb.ProcessFreshchatEventRequest{
					Action: chatPb.FreshchatCallbackAction_FRESHCHAT_CALLBACK_ACTION_MESSAGE_CREATE,
					Data:   &chatConsumerPb.ProcessFreshchatEventRequest_Message{},
				},
			},
			want:    helper.FcEventSuccessResp(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := NewService(mockFcActionFactory, nil, nil)
			got, err := a.ProcessFreshchatEvent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessFreshchatEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessFreshchatEvent() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

// (TODO:ANUBHAV) Add the updated benchmark and unit tests
