// nolint:goconst
package nudge_parser

import (
	"bytes"
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/samber/lo"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	nudgeParserPb "github.com/epifi/gamma/api/cx/nudge_parser"
	nudgePb "github.com/epifi/gamma/api/nudge"
	segmentPb "github.com/epifi/gamma/api/segment"
)

// Constants for Excel column mapping
const (
	serialNo                     = "serialNo"
	Description                  = "Description"
	EntrySegmentExpression       = "EntrySegmentExpression"
	EntryEvent                   = "EntryEvent"
	EntryEventExpression         = "EntryEventExpression"
	ExitEvent                    = "ExitEvent"
	ExitExpression               = "ExitExpression"
	AutoDismissalTimePeriod      = "AutoDismissalTimePeriod"
	SnoozeTime                   = "SnoozeTime"
	ActiveSince                  = "ActiveSince"
	ActiveTill                   = "ActiveTill"
	SetupBy                      = "SetupBy"
	Area                         = "Area"
	Category                     = "Category"
	SubCategory                  = "SubCategory"
	Urgency                      = "Urgency"
	UserInactionConsequence      = "UserInactionConsequence"
	DisplayInScreens             = "DisplayInScreens"
	DisplayConfigs               = "DisplayConfigs"
	LoadScreen                   = "LoadScreen"
	IsEntryActivationEventDriven = "IsEntryActivationEventDriven"
	NudgeType                    = "NudgeType"
	NudgeUserType                = "NudgeUserType"
	NudgeAdditionalDetails       = "NudgeAdditionalDetails"
)

var (
	nudgeExcelColumnNameToIndexMap = map[string]int{
		serialNo:                     0,
		Description:                  1,
		EntrySegmentExpression:       2,
		EntryEvent:                   3,
		EntryEventExpression:         4,
		ExitEvent:                    5,
		ExitExpression:               6,
		AutoDismissalTimePeriod:      7,
		SnoozeTime:                   8,
		ActiveSince:                  9,
		ActiveTill:                   10,
		SetupBy:                      11,
		Area:                         12,
		Category:                     13,
		SubCategory:                  14,
		Urgency:                      15,
		UserInactionConsequence:      16,
		DisplayInScreens:             17,
		DisplayConfigs:               18,
		LoadScreen:                   19,
		IsEntryActivationEventDriven: 20,
		NudgeType:                    21,
		NudgeUserType:                22,
		NudgeAdditionalDetails:       23,
	}
	noOfColumnsInNudgeExcel = len(nudgeExcelColumnNameToIndexMap)
)

type Service struct {
	nudgeParserPb.UnimplementedNudgeParserServer
	segmentClient segmentPb.SegmentationServiceClient
}

func NewService(segmentClient segmentPb.SegmentationServiceClient) *Service {
	return &Service{
		segmentClient: segmentClient,
	}
}

func (s *Service) ParseNudgesFromExcel(ctx context.Context, req *nudgeParserPb.ParseNudgesFromExcelRequest) (*nudgeParserPb.ParseNudgesFromExcelResponse, error) {
	// Validate request
	if len(req.GetExcelFileContent()) == 0 {
		return &nudgeParserPb.ParseNudgesFromExcelResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("excel file content is required"),
		}, nil
	}

	if req.GetCreatedBy() == "" {
		return &nudgeParserPb.ParseNudgesFromExcelResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("created_by is required"),
		}, nil
	}

	sheetName := req.GetSheetName()
	if sheetName == "" {
		sheetName = "Sheet1"
	}

	// Read Excel file
	nudgeExcelReader := bytes.NewReader(req.GetExcelFileContent())
	nudgeExcelFile, err := excelize.OpenReader(nudgeExcelReader)
	if err != nil {
		logger.Error(ctx, "failed to read excel file", zap.Error(err))
		return &nudgeParserPb.ParseNudgesFromExcelResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to read excel file"),
		}, nil
	}

	nudgeExcelFileRows, err := nudgeExcelFile.GetRows(sheetName)
	if err != nil {
		logger.Error(ctx, "failed to get excel rows", zap.Error(err))
		return &nudgeParserPb.ParseNudgesFromExcelResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to get excel rows"),
		}, nil
	}

	var parsedNudges []*nudgeParserPb.ParsedNudgeResult

	for idx, nudgeExcelFileRow := range nudgeExcelFileRows {
		// first row contains excel column labels
		if idx == 0 || len(nudgeExcelFileRow) == 0 {
			continue
		}

		nudgeSerialNo := s.getSerialNoFromExcelRow(nudgeExcelFileRow)

		if len(nudgeExcelFileRow) < noOfColumnsInNudgeExcel {
			parsedNudges = append(parsedNudges, &nudgeParserPb.ParsedNudgeResult{
				SerialNo:      nudgeSerialNo,
				ParseStatus:   "INVALID_DATA",
				ErrorMessages: []string{"not enough columns in row"},
			})
			continue
		}

		parsedNudge, nudgeValidationErrors := s.generateParsedNudgeResponse(nudgeExcelFileRow, req.GetCreatedBy())
		if len(nudgeValidationErrors) != 0 {
			parsedNudge.ParseStatus = "INVALID_DATA"
			parsedNudge.ErrorMessages = nudgeValidationErrors
		} else {
			parsedNudge.ParseStatus = "SUCCESS"
		}

		parsedNudges = append(parsedNudges, parsedNudge)
	}

	return &nudgeParserPb.ParseNudgesFromExcelResponse{
		Status:       rpcPb.StatusOk(),
		ParsedNudges: parsedNudges,
	}, nil
}

func (s *Service) generateParsedNudgeResponse(nudgeExcelFileRow []string, nudgeCreatedBy string) (*nudgeParserPb.ParsedNudgeResult, []string) {
	var (
		parsedNudge           nudgeParserPb.ParsedNudgeResult
		nudgeValidationErrors []string
	)

	// Set the created by field
	parsedNudge.CreatedBy = nudgeCreatedBy

	// Serial Number
	parsedNudge.SerialNo = s.getSerialNoFromExcelRow(nudgeExcelFileRow)

	// Description
	descriptionValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[Description]])
	if descriptionValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "description cannot be nil")
	} else {
		parsedNudge.Description = descriptionValue
	}

	// Entry Event
	entryEventValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[EntryEvent]])
	if entryEventValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "entry event cannot be nil")
	} else {
		if !lo.Contains(lo.Keys(nudgePb.NudgeEventDataType_value), entryEventValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, fmt.Sprintf("entry event is not valid: %s", entryEventValue))
		} else {
			parsedNudge.EntryEvent = entryEventValue
		}
	}

	// Entry Segment Expression
	entrySegmentExpressionValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[EntrySegmentExpression]])
	if entrySegmentExpressionValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "entry segment expression cannot be nil")
	} else {
		parsedNudge.EntrySegmentExpression = entrySegmentExpressionValue
	}

	// Entry Event Expression
	entryEventExpressionValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[EntryEventExpression]])
	if entryEventExpressionValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "entry event expression cannot be nil")
	} else {
		parsedNudge.EntryEventExpression = entryEventExpressionValue
	}

	// Exit Event
	exitEventValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[ExitEvent]])
	if exitEventValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "exit event cannot be nil")
	} else {
		if !lo.Contains(lo.Keys(nudgePb.NudgeEventDataType_value), exitEventValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, "exit event is not valid")
		} else {
			parsedNudge.ExitEvent = exitEventValue
		}
	}

	// Exit Expression
	exitExpressionValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[ExitExpression]])
	if exitExpressionValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "exit expression cannot be nil")
	} else {
		parsedNudge.ExitExpression = exitExpressionValue
	}

	// Auto Dismiss Duration
	autoDismissDurationInDaysValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[AutoDismissalTimePeriod]])
	if autoDismissDurationInDaysValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "auto dismiss duration in days cannot be nil")
	} else {
		autoDismissDurationInDaysInt, err := strconv.ParseFloat(autoDismissDurationInDaysValue, 32)
		if err != nil {
			nudgeValidationErrors = append(nudgeValidationErrors, "unable to convert auto dismiss duration in days to integer, err: "+err.Error())
		} else {
			parsedNudge.AutoDismissDurationInSeconds = uint64(autoDismissDurationInDaysInt) * 86400
		}
	}

	// Snooze Duration
	snoozeDurationInDaysValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[SnoozeTime]])
	if snoozeDurationInDaysValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "snooze duration in days cannot be nil")
	} else {
		snoozeDurationInDaysInt, err := strconv.ParseFloat(snoozeDurationInDaysValue, 32)
		if err != nil {
			nudgeValidationErrors = append(nudgeValidationErrors, "unable to convert snooze duration in days to integer, err: "+err.Error())
		} else {
			parsedNudge.SnoozeDurationInSeconds = uint64(snoozeDurationInDaysInt) * 86400
		}
	}

	// Active Since
	activeSinceValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[ActiveSince]])
	if activeSinceValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "active since cannot be nil")
	} else {
		parsedNudge.ActiveSince = activeSinceValue
	}

	// Active Till
	activeTillValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[ActiveTill]])
	if activeTillValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "active till cannot be nil")
	} else {
		parsedNudge.ActiveTill = activeTillValue
	}

	// Setup By
	setupByValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[SetupBy]])
	if setupByValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "setup by cannot be nil")
	} else {
		parsedNudge.SetupBy = setupByValue
	}

	// Area
	areaValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[Area]])
	if areaValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "area cannot be nil")
	} else {
		allowedAreaValues := lo.Filter(lo.Keys(nudgePb.NudgeArea_value), func(val string, _ int) bool {
			return val != "NUDGE_AREA_UNSPECIFIED"
		})
		if !lo.Contains(allowedAreaValues, areaValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, "area is not valid")
		} else {
			parsedNudge.Area = areaValue
		}
	}

	// Category
	categoryValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[Category]])
	if categoryValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "category cannot be nil")
	} else {
		allowedCategoryValues := lo.Filter(lo.Keys(nudgePb.NudgeCategory_value), func(val string, _ int) bool {
			return !lo.Contains([]string{"NUDGE_CATEGORY_UNSPECIFIED", "ONBOARDING", "SUGGESTED_ACTION", "FEATURE_PROMOTIONS"}, val)
		})
		if !lo.Contains(allowedCategoryValues, categoryValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, "category is not valid")
		} else {
			parsedNudge.Category = categoryValue
		}
	}

	// Sub Category
	subCategoryValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[SubCategory]])
	if subCategoryValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "sub category cannot be nil")
	} else {
		allowedSubcatValues := lo.Filter(lo.Keys(nudgePb.NudgeSubCategory_value), func(val string, _ int) bool {
			return val != "NUDGE_SUB_CATEGORY_UNSPECIFIED"
		})
		if !lo.Contains(allowedSubcatValues, subCategoryValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, "sub category is not valid")
		} else {
			parsedNudge.SubCategory = subCategoryValue
		}
	}

	// Urgency
	urgencyValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[Urgency]])
	if urgencyValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "urgency cannot be nil")
	} else {
		allowedUrgencyValues := lo.Filter(lo.Keys(nudgePb.NudgeUrgency_value), func(val string, _ int) bool {
			return val != "NUDGE_URGENCY_UNSPECIFIED"
		})
		if !lo.Contains(allowedUrgencyValues, urgencyValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, "urgency is not valid")
		} else {
			parsedNudge.Urgency = urgencyValue
		}
	}

	// User Inaction Consequence
	userInactionConsequenceValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[UserInactionConsequence]])
	if userInactionConsequenceValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "user inaction consequence cannot be nil")
	} else {
		allowedUserInactionConsequenceValues := lo.Filter(lo.Keys(nudgePb.NudgeUserInactionConsequence_value), func(val string, _ int) bool {
			return val != "NUDGE_USER_INACTION_CONSEQUENCE_UNSPECIFIED"
		})
		if !lo.Contains(allowedUserInactionConsequenceValues, userInactionConsequenceValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, "user inaction consequence is not valid")
		} else {
			parsedNudge.UserInactionConsequence = userInactionConsequenceValue
		}
	}

	// Display In Screens
	displayInScreensValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[DisplayInScreens]])
	if displayInScreensValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "display in screens cannot be nil")
	} else {
		displayScreenStrings := strings.Split(displayInScreensValue, ",")
		var displayScreens []string
		for _, displayScreenString := range displayScreenStrings {
			displayScreenFormatted := strings.ToUpper(strings.TrimSpace(displayScreenString))
			if !lo.Contains([]string{"HOME", "INVESTMENT_LANDING_SCREEN", "MUTUAL_FUND_DETAILS_SCREEN", "DC_DASHBOARD_V2_SCREEN"}, displayScreenFormatted) {
				nudgeValidationErrors = append(nudgeValidationErrors, "display in screen is not valid")
				continue
			}
			displayScreens = append(displayScreens, displayScreenFormatted)
		}
		parsedNudge.DisplayInScreens = displayScreens
	}

	// Display Configs
	displayConfigsValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[DisplayConfigs]])
	if displayConfigsValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "display configs cannot be nil")
	} else {
		parsedNudge.DisplayConfigs = displayConfigsValue
	}

	// Load Screen
	loadScreenValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[LoadScreen]])
	if loadScreenValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "load screen cannot be nil")
	} else {
		parsedNudge.LoadScreen = loadScreenValue
	}

	// Is Entry Activation Event Driven
	isEntryActivationEventDrivenValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[IsEntryActivationEventDriven]])
	if isEntryActivationEventDrivenValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "is entry activation driven cannot be nil")
	} else {
		isEntryActivationEventDrivenFormatted := strings.ToUpper(strings.TrimSpace(isEntryActivationEventDrivenValue))
		switch isEntryActivationEventDrivenFormatted {
		case "YES":
			parsedNudge.IsEntryActivationEventDriven = true
		case "NO":
			parsedNudge.IsEntryActivationEventDriven = false
		default:
			nudgeValidationErrors = append(nudgeValidationErrors, "is entry activation driven must be YES or NO")
		}
	}

	// Nudge Type
	nudgeTypeValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[NudgeType]])
	if nudgeTypeValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "nudge type cannot be nil")
	} else {
		allowedNudgeTypeValues := lo.Filter(lo.Keys(nudgePb.NudgeType_value), func(val string, _ int) bool {
			return val != "NUDGE_TYPE_UNSPECIFIED"
		})
		if !lo.Contains(allowedNudgeTypeValues, nudgeTypeValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, "nudge type is not valid")
		} else {
			parsedNudge.NudgeType = nudgeTypeValue
		}
	}

	// Nudge User Type
	nudgeUserTypeValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[NudgeUserType]])
	if nudgeUserTypeValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "nudge user type cannot be nil")
	} else {
		allowedNudgeUserTypeValues := lo.Filter(lo.Keys(nudgePb.NudgeUserType_value), func(val string, _ int) bool {
			return val != "NUDGE_USER_TYPE_UNSPECIFIED"
		})
		if !lo.Contains(allowedNudgeUserTypeValues, nudgeUserTypeValue) {
			nudgeValidationErrors = append(nudgeValidationErrors, "nudge user type is not valid")
		} else {
			parsedNudge.NudgeUserType = nudgeUserTypeValue
		}
	}

	// Additional Details
	additionalDetailsValue := strings.TrimSpace(nudgeExcelFileRow[nudgeExcelColumnNameToIndexMap[NudgeAdditionalDetails]])
	if additionalDetailsValue == "nil" {
		nudgeValidationErrors = append(nudgeValidationErrors, "additional details cannot be nil")
	} else {
		parsedNudge.NudgeAdditionalDetails = additionalDetailsValue
	}

	return &parsedNudge, nudgeValidationErrors
}

func (s *Service) getSerialNoFromExcelRow(row []string) string {
	return row[nudgeExcelColumnNameToIndexMap[serialNo]]
}
