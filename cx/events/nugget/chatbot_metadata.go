package nugget

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const (
	ChatbotFetchMetadataRequestReceivedEventName      = "ChatbotFetchMetadataRequestReceivedEvent"
	ChatbotShowCategoriesResponseSentEventName        = "ChatbotShowCategoriesResponseSentEvent"
	ChatbotProcessQueryRequestReceivedEventName       = "ChatbotProcessQueryRequestReceivedEvent"
	ChatbotEvaluateEscalationRequestReceivedEventName = "ChatbotEvaluateEscalationRequestReceivedEvent"
)

type ChatbotFetchMetadataRequestReceivedEvent struct {
	Timestamp     time.Time
	EventId       string
	EventType     string
	EventName     string
	ServiceName   string
	BotType       string
	TicketId      string
	ZomatoId      string
	TransactionId string
	Properties    map[string]string
}

func (a *ChatbotFetchMetadataRequestReceivedEvent) GetEventId() string {
	return a.EventId
}

func (a *ChatbotFetchMetadataRequestReceivedEvent) GetUserId() string {
	return a.ZomatoId
}

func (a *ChatbotFetchMetadataRequestReceivedEvent) GetProspectId() string {
	return ""
}

func (a *ChatbotFetchMetadataRequestReceivedEvent) GetEventName() string {
	return a.EventName
}

func (a *ChatbotFetchMetadataRequestReceivedEvent) GetEventType() string {
	return a.EventType
}

func (a *ChatbotFetchMetadataRequestReceivedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(a, properties)
	return properties
}

func (a *ChatbotFetchMetadataRequestReceivedEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func NewChatbotFetchMetadataRequestReceivedEvent(zomatoId, botType, ticketId, transactionId string) *ChatbotFetchMetadataRequestReceivedEvent {
	return &ChatbotFetchMetadataRequestReceivedEvent{
		Timestamp:     time.Now(),
		EventId:       uuid.New().String(),
		EventType:     events.EventTrack,
		EventName:     ChatbotFetchMetadataRequestReceivedEventName,
		ZomatoId:      zomatoId,
		BotType:       botType,
		TicketId:      ticketId,
		TransactionId: transactionId,
	}
}

type ChatbotShowCategoriesResponseSentEvent struct {
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string
	BotType     string
	TicketId    string
	ZomatoId    string
	Categories  []string
	Properties  map[string]string
}

func (a *ChatbotShowCategoriesResponseSentEvent) GetEventId() string {
	return a.EventId
}

func (a *ChatbotShowCategoriesResponseSentEvent) GetUserId() string {
	return a.ZomatoId
}

func (a *ChatbotShowCategoriesResponseSentEvent) GetProspectId() string {
	return ""
}

func (a *ChatbotShowCategoriesResponseSentEvent) GetEventName() string {
	return a.EventName
}

func (a *ChatbotShowCategoriesResponseSentEvent) GetEventType() string {
	return a.EventType
}

func (a *ChatbotShowCategoriesResponseSentEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(a, properties)
	return properties
}

func (a *ChatbotShowCategoriesResponseSentEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func NewChatbotShowCategoriesResponseSentEvent(zomatoId, botType, ticketId string, categories []string) *ChatbotShowCategoriesResponseSentEvent {
	return &ChatbotShowCategoriesResponseSentEvent{
		Timestamp:  time.Now(),
		EventId:    uuid.New().String(),
		EventType:  events.EventTrack,
		EventName:  ChatbotShowCategoriesResponseSentEventName,
		ZomatoId:   zomatoId,
		BotType:    botType,
		TicketId:   ticketId,
		Categories: categories,
	}
}

type ChatbotProcessQueryRequestReceivedEvent struct {
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string
	BotType     string
	TicketId    string
	ZomatoId    string
	QueryType   string
	UserQuery   string
	Properties  map[string]string
}

func (a *ChatbotProcessQueryRequestReceivedEvent) GetEventId() string {
	return a.EventId
}

func (a *ChatbotProcessQueryRequestReceivedEvent) GetUserId() string {
	return a.ZomatoId
}

func (a *ChatbotProcessQueryRequestReceivedEvent) GetProspectId() string {
	return ""
}

func (a *ChatbotProcessQueryRequestReceivedEvent) GetEventName() string {
	return a.EventName
}

func (a *ChatbotProcessQueryRequestReceivedEvent) GetEventType() string {
	return a.EventType
}

func (a *ChatbotProcessQueryRequestReceivedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(a, properties)
	return properties
}

func (a *ChatbotProcessQueryRequestReceivedEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func NewChatbotProcessQueryRequestReceivedEvent(zomatoId, ticketId, botType, queryType, userQuery string) *ChatbotProcessQueryRequestReceivedEvent {
	return &ChatbotProcessQueryRequestReceivedEvent{
		Timestamp: time.Now(),
		EventId:   uuid.New().String(),
		EventType: events.EventTrack,
		EventName: ChatbotProcessQueryRequestReceivedEventName,
		ZomatoId:  zomatoId,
		BotType:   botType,
		QueryType: queryType,
		UserQuery: userQuery,
		TicketId:  ticketId,
	}
}

type ChatbotEvaluateEscalationRequestReceivedEvent struct {
	Timestamp        time.Time
	EventId          string
	EventType        string
	EventName        string
	ServiceName      string
	BotType          string
	TicketId         string
	ZomatoId         string
	EscalationReason string
	AgentAssignedAt  time.Duration
	Properties       map[string]string
}

func (a *ChatbotEvaluateEscalationRequestReceivedEvent) GetEventId() string {
	return a.EventId
}

func (a *ChatbotEvaluateEscalationRequestReceivedEvent) GetUserId() string {
	return a.ZomatoId
}

func (a *ChatbotEvaluateEscalationRequestReceivedEvent) GetProspectId() string {
	return ""
}

func (a *ChatbotEvaluateEscalationRequestReceivedEvent) GetEventName() string {
	return a.EventName
}

func (a *ChatbotEvaluateEscalationRequestReceivedEvent) GetEventType() string {
	return a.EventType
}

func (a *ChatbotEvaluateEscalationRequestReceivedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(a, properties)
	return properties
}

func (a *ChatbotEvaluateEscalationRequestReceivedEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func NewChatbotEvaluateEscalationRequestReceivedEvent(zomatoId, ticketId, botType, escalationReason string, agentAssignedAt time.Duration) *ChatbotEvaluateEscalationRequestReceivedEvent {
	return &ChatbotEvaluateEscalationRequestReceivedEvent{
		Timestamp:        time.Now(),
		EventId:          uuid.New().String(),
		EventType:        events.EventTrack,
		EventName:        ChatbotEvaluateEscalationRequestReceivedEventName,
		ZomatoId:         zomatoId,
		BotType:          botType,
		TicketId:         ticketId,
		EscalationReason: escalationReason,
		AgentAssignedAt:  agentAssignedAt,
	}
}
