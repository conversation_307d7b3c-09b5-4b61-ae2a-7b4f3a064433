package nugget

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const (
	ChatbotRoutingEventName = "ChatbotRoutingRequestReceivedEvent"
)

type ChatbotRoutingEvent struct {
	ActorId     string
	Timestamp   time.Time
	EventId     string
	EventType   string
	EventName   string
	ServiceName string
	BotType     string
	TicketId    string
	ZomatoId    string
	Properties  map[string]string
}

func (a *ChatbotRoutingEvent) GetEventId() string {
	return a.EventId
}

func (a *ChatbotRoutingEvent) GetUserId() string {
	return a.ZomatoId
}

func (a *ChatbotRoutingEvent) GetProspectId() string {
	return ""
}

func (a *ChatbotRoutingEvent) GetEventName() string {
	return a.EventName
}

func (a *ChatbotRoutingEvent) GetEventType() string {
	return a.EventType
}

func (a *ChatbotRoutingEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(a, properties)
	return properties
}

func (a *ChatbotRoutingEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func NewChatbotRoutingEvent(zomatoId, botType, ticketId string) *ChatbotRoutingEvent {
	return &ChatbotRoutingEvent{
		ZomatoId:  zomatoId,
		Timestamp: time.Now(),
		EventId:   uuid.New().String(),
		EventType: events.EventTrack,
		EventName: ChatbotRoutingEventName,
		BotType:   botType,
		TicketId:  ticketId,
	}
}
