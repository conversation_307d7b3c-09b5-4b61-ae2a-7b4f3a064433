// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxcountthresholdforfetchingbulkuserinfo":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxCountThresholdForFetchingBulkUserInfo\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxCountThresholdForFetchingBulkUserInfo, nil
	case "usepkgratelimiter":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UsePkgRateLimiter\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UsePkgRateLimiter, nil
	case "isredactionenabledfordbstates":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRedactionEnabledForDBStates\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRedactionEnabledForDBStates, nil
	case "enablebalancemetricsoncasemanagement":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableBalanceMetricsOnCaseManagement\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableBalanceMetricsOnCaseManagement, nil
	case "enableoutcalldataincasemanagementforriskops":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableOutCallDataInCaseManagementForRiskOps\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableOutCallDataInCaseManagementForRiskOps, nil
	case "isnewoperationalstatusapienabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsNewOperationalStatusAPIEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsNewOperationalStatusAPIEnabled, nil
	case "watchlistreasons":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WatchlistReasons\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WatchlistReasons, nil
	case "cxfreshdeskticketbaseurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CXFreshdeskTicketBaseURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CXFreshdeskTicketBaseURL, nil
	case "emailverification":
		return obj.EmailVerification.Get(dynamicFieldPath[1:])
	case "mobilepromptverification":
		return obj.MobilePromptVerification.Get(dynamicFieldPath[1:])
	case "customerauth":
		return obj.CustomerAuth.Get(dynamicFieldPath[1:])
	case "freshdeskticketsubscriber":
		return obj.FreshdeskTicketSubscriber.Get(dynamicFieldPath[1:])
	case "freshdeskcontactsubscriber":
		return obj.FreshdeskContactSubscriber.Get(dynamicFieldPath[1:])
	case "watsonincidentreportingsubscriber":
		return obj.WatsonIncidentReportingSubscriber.Get(dynamicFieldPath[1:])
	case "watsonincidentresolutionsubscriber":
		return obj.WatsonIncidentResolutionSubscriber.Get(dynamicFieldPath[1:])
	case "watsonticketeventsubscriber":
		return obj.WatsonTicketEventSubscriber.Get(dynamicFieldPath[1:])
	case "watsoncreateticketsubscriber":
		return obj.WatsonCreateTicketSubscriber.Get(dynamicFieldPath[1:])
	case "disputesubscriber":
		return obj.DisputeSubscriber.Get(dynamicFieldPath[1:])
	case "disputecreateticketsubscriber":
		return obj.DisputeCreateTicketSubscriber.Get(dynamicFieldPath[1:])
	case "disputeupdateticketsubscriber":
		return obj.DisputeUpdateTicketSubscriber.Get(dynamicFieldPath[1:])
	case "disputeaddnoteticketsubscriber":
		return obj.DisputeAddNoteTicketSubscriber.Get(dynamicFieldPath[1:])
	case "devactionsubscriber":
		return obj.DevActionSubscriber.Get(dynamicFieldPath[1:])
	case "freshdeskticketdataeventsubscriberfifo":
		return obj.FreshdeskTicketDataEventSubscriberFifo.Get(dynamicFieldPath[1:])
	case "freshdeskticketdataeventsubscriber":
		return obj.FreshdeskTicketDataEventSubscriber.Get(dynamicFieldPath[1:])
	case "crmissuetrackerintegrationsubscriber":
		return obj.CrmIssueTrackerIntegrationSubscriber.Get(dynamicFieldPath[1:])
	case "dispute":
		return obj.Dispute.Get(dynamicFieldPath[1:])
	case "applog":
		return obj.AppLog.Get(dynamicFieldPath[1:])
	case "payout":
		return obj.Payout.Get(dynamicFieldPath[1:])
	case "payoutstatuschecksubscriber":
		return obj.PayoutStatusCheckSubscriber.Get(dynamicFieldPath[1:])
	case "waitlistsubscriber":
		return obj.WaitlistSubscriber.Get(dynamicFieldPath[1:])
	case "bulkuserinfoviaemailconfig":
		return obj.BulkUserInfoViaEmailConfig.Get(dynamicFieldPath[1:])
	case "upidisputeautoupdateeventsubscriber":
		return obj.UpiDisputeAutoUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "updateticketsubscriber":
		return obj.UpdateTicketSubscriber.Get(dynamicFieldPath[1:])
	case "createticketsubscriber":
		return obj.CreateTicketSubscriber.Get(dynamicFieldPath[1:])
	case "ticketconfig":
		return obj.TicketConfig.Get(dynamicFieldPath[1:])
	case "callconfig":
		return obj.CallConfig.Get(dynamicFieldPath[1:])
	case "ozonetelcalleventsubscriber":
		return obj.OzonetelCallEventSubscriber.Get(dynamicFieldPath[1:])
	case "callroutingconfig":
		return obj.CallRoutingConfig.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "issueresolutionfeedbackconfig":
		return obj.IssueResolutionFeedbackConfig.Get(dynamicFieldPath[1:])
	case "chatbotconfig":
		return obj.ChatBotConfig.Get(dynamicFieldPath[1:])
	case "freshchateventsubscriber":
		return obj.FreshchatEventSubscriber.Get(dynamicFieldPath[1:])
	case "nuggeteventsubscriber":
		return obj.NuggetEventSubscriber.Get(dynamicFieldPath[1:])
	case "riskconfig":
		return obj.RiskConfig.Get(dynamicFieldPath[1:])
	case "internationalfundtransfer":
		return obj.InternationalFundTransfer.Get(dynamicFieldPath[1:])
	case "salaryopsconfig":
		return obj.SalaryOpsConfig.Get(dynamicFieldPath[1:])
	case "landingpageconfig":
		return obj.LandingPageConfig.Get(dynamicFieldPath[1:])
	case "ticketreconciliationeventsubscriber":
		return obj.TicketReconciliationEventSubscriber.Get(dynamicFieldPath[1:])
	case "devactionhelperconfig":
		return obj.DevActionHelperConfig.Get(dynamicFieldPath[1:])
	case "overridebankactions":
		return obj.OverrideBankActions.Get(dynamicFieldPath[1:])
	case "reviewaction":
		return obj.ReviewAction.Get(dynamicFieldPath[1:])
	case "vendoraccountpennydropviaemailconfig":
		return obj.VendorAccountPennyDropViaEmailConfig.Get(dynamicFieldPath[1:])
	case "employerdbconfig":
		return obj.EmployerDbConfig.Get(dynamicFieldPath[1:])
	case "grpcwebserverconfig":
		return obj.GRPCWebServerConfig.Get(dynamicFieldPath[1:])
	case "sherlockbannersconfig":
		return obj.SherlockBannersConfig.Get(dynamicFieldPath[1:])
	case "ruddereventkafkaconsumergroup":
		return obj.RudderEventKafkaConsumerGroup.Get(dynamicFieldPath[1:])
	case "erroractivityconfig":
		return obj.ErrorActivityConfig.Get(dynamicFieldPath[1:])
	case "agentpromptconfig":
		return obj.AgentPromptConfig.Get(dynamicFieldPath[1:])
	case "riskopsinstalledappsconfig":
		return obj.RiskOpsInstalledAppsConfig.Get(dynamicFieldPath[1:])
	case "riskfennelconfig":
		return obj.RiskFennelConfig.Get(dynamicFieldPath[1:])
	case "stagewisecommsconfig":
		return obj.StageWiseCommsConfig.Get(dynamicFieldPath[1:])
	case "issueconfigserviceconfig":
		return obj.IssueConfigServiceConfig.Get(dynamicFieldPath[1:])
	case "s3eventsubscriber":
		return obj.S3EventSubscriber.Get(dynamicFieldPath[1:])
	case "s3eventconsumerconfig":
		return obj.S3EventConsumerConfig.Get(dynamicFieldPath[1:])
	case "risktxnreviewrolloutconfig":
		return obj.RiskTxnReviewRolloutConfig.Get(dynamicFieldPath[1:])
	case "riskoutcallformrolloutconfig":
		return obj.RiskOutcallFormRolloutConfig.Get(dynamicFieldPath[1:])
	case "callivrconfig":
		return obj.CallIvrConfig.Get(dynamicFieldPath[1:])
	case "casemanagementactoractivities":
		return obj.CaseManagementActorActivities.Get(dynamicFieldPath[1:])
	case "whitelistaccesslevelforactorenrichmentbyphoneoremail":
		return obj.WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail.Get(dynamicFieldPath[1:])
	case "contactusmodelresponseconfig":
		return obj.ContactUsModelResponseConfig.Get(dynamicFieldPath[1:])
	case "s3bucketnameforfilegenerator":
		return obj.S3BucketNameForFileGenerator.Get(dynamicFieldPath[1:])
	case "dbstateconfig":
		return obj.DbStateConfig.Get(dynamicFieldPath[1:])
	case "escalationconfig":
		return obj.EscalationConfig.Get(dynamicFieldPath[1:])
	case "federalescalationupdateeventsubscriber":
		return obj.FederalEscalationUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "federalescalationcreationeventsubscriber":
		return obj.FederalEscalationCreationEventSubscriber.Get(dynamicFieldPath[1:])
	case "federalescalationconfig":
		return obj.FederalEscalationConfig.Get(dynamicFieldPath[1:])
	case "saclosureeligibilityconf":
		return obj.SaClosureEligibilityConf.Get(dynamicFieldPath[1:])
	case "nuggeteventconfig":
		return obj.NuggetEventConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EmailVerification) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "verificationurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VerificationUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VerificationUrl, nil
	case "fromemail":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FromEmail\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FromEmail, nil
	case "fromemailname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FromEmailName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FromEmailName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EmailVerification", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MobilePromptVerification) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "validity":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Validity\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Validity, nil
	case "notificationtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NotificationTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NotificationTitle, nil
	case "notificationbody":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NotificationBody\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NotificationBody, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MobilePromptVerification", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CustomerAuth) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isagentcachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAgentCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsAgentCachingEnabled, nil
	case "isinappnotificationenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsInAppNotificationEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsInAppNotificationEnabled, nil
	case "isskippingauthenabledafterexternalauth":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsSkippingAuthEnabledAfterExternalAuth\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsSkippingAuthEnabledAfterExternalAuth, nil
	case "authfactorcachevalidityduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AuthFactorCacheValidityDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AuthFactorCacheValidityDuration, nil
	case "authfactorprioritybyplatform":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AuthFactorPriorityByPlatform, nil
		case len(dynamicFieldPath) > 1:

			return obj.AuthFactorPriorityByPlatform[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.AuthFactorPriorityByPlatform, nil
	case "authfactorcachekey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AuthFactorCacheKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AuthFactorCacheKey, nil
	case "inappnotificationtemplate":
		return obj.InAppNotificationTemplate.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CustomerAuth", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AuthFactorPriorityByPlatform) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minappversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAppVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAppVersion, nil
	case "prioritymap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.PriorityMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"PriorityMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.PriorityMap[dynamicFieldPath[1]], nil

		}
		return obj.PriorityMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AuthFactorPriorityByPlatform", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InAppNotificationTemplate) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "notificationtimestampfontcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NotificationTimestampFontColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NotificationTimestampFontColor, nil
	case "iconurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconUrl, nil
	case "iconbgcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconBGColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconBGColor, nil
	case "titlefontcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TitleFontColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TitleFontColor, nil
	case "bgcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BGColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BGColor, nil
	case "notificationdismissiconbgcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NotificationDismissIconBgColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NotificationDismissIconBgColor, nil
	case "notificationdismissiconcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NotificationDismissIconColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NotificationDismissIconColor, nil
	case "notificationshadowcolor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NotificationShadowColor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NotificationShadowColor, nil
	case "mobileprompttitlepn":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MobilePromptTitlePN\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MobilePromptTitlePN, nil
	case "mobileprompttitleinapp":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MobilePromptTitleInApp\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MobilePromptTitleInApp, nil
	case "mobilepromptbody":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MobilePromptBody\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MobilePromptBody, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InAppNotificationTemplate", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Dispute) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isissueresolutionfeedbackcommsenabledfordispute":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsIssueResolutionFeedbackCommsEnabledForDispute\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsIssueResolutionFeedbackCommsEnabledForDispute, nil
	case "isgetnextquestionsforappv2enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsGetNextQuestionsForAppV2Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsGetNextQuestionsForAppV2Enabled, nil
	case "isgetnextquestionsv2enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsGetNextQuestionsV2Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsGetNextQuestionsV2Enabled, nil
	case "isupiexternalprovenanceevaluationenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsUpiExternalProvenanceEvaluationEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsUpiExternalProvenanceEvaluationEnabled, nil
	case "disputeidempotencyttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisputeIdempotencyTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisputeIdempotencyTTL, nil
	case "disputejobconfig":
		return obj.DisputeJobConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Dispute", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DisputeJobConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "reverseprocessingdelaydurationbetweentickets":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ReverseProcessingDelayDurationBetweenTickets\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ReverseProcessingDelayDurationBetweenTickets, nil
	case "reverseprocessingjobtimeout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ReverseProcessingJobTimeout\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ReverseProcessingJobTimeout, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DisputeJobConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AppLog) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "logttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LogTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LogTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AppLog", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Payout) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "cashpayout":
		return obj.CashPayout.Get(dynamicFieldPath[1:])
	case "ficoinspayout":
		return obj.FiCoinsPayout.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Payout", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CashPayout) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableb2ctransactionviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableB2CTransactionViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableB2CTransactionViaCelestial, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CashPayout", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FiCoinsPayout) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ispayoutviaficoinsenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsPayoutViaFiCoinsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsPayoutViaFiCoinsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FiCoinsPayout", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *BulkUserInfoViaEmailConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxcountthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxCountThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxCountThreshold, nil
	case "fromemailid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FromEmailId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FromEmailId, nil
	case "fromemailname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FromEmailName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FromEmailName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for BulkUserInfoViaEmailConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TicketConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isticketlistloggingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsTicketListLoggingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsTicketListLoggingEnabled, nil
	case "isticketeventloggingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsTicketEventLoggingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsTicketEventLoggingEnabled, nil
	case "isdefaulttitleanddescriptionenabledforinappticket":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsDefaultTitleAndDescriptionEnabledForInAppTicket\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsDefaultTitleAndDescriptionEnabledForInAppTicket, nil
	case "isticketupdateeventpublishingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsTicketUpdateEventPublishingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsTicketUpdateEventPublishingEnabled, nil
	case "ticketfieldcachevalidityduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TicketFieldCacheValidityDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TicketFieldCacheValidityDuration, nil
	case "latestticketcachevalidityduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LatestTicketCacheValidityDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LatestTicketCacheValidityDuration, nil
	case "defaultapptickettitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultAppTicketTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultAppTicketTitle, nil
	case "defaultappticketdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultAppTicketDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultAppTicketDescription, nil
	case "showticketsinappconfig":
		return obj.ShowTicketsInAppConfig.Get(dynamicFieldPath[1:])
	case "slaconfig":
		return obj.SLAConfig.Get(dynamicFieldPath[1:])
	case "csatconfig":
		return obj.CsatConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TicketConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ShowTicketsInAppConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "timelimitforupdatingticketdetails":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TimeLimitForUpdatingTicketDetails\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TimeLimitForUpdatingTicketDetails, nil
	case "whitelistedproductcategories":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WhitelistedProductCategories\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WhitelistedProductCategories, nil
	case "mandatoryfieldsrequiredtoshowticket":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MandatoryFieldsRequiredToShowTicket\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MandatoryFieldsRequiredToShowTicket, nil
	case "cutoffdatetoshowtickets":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CutOffDateToShowTickets\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CutOffDateToShowTickets, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ShowTicketsInAppConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SLAConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isslacalculationenabledinticketconsumer":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsSLACalculationEnabledInTicketConsumer\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsSLACalculationEnabledInTicketConsumer, nil
	case "isexpectedresolutionbyfielddeterminedusingsla":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsExpectedResolutionByFieldDeterminedUsingSLA\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsExpectedResolutionByFieldDeterminedUsingSLA, nil
	case "iseventpublishedtoupdateexpectedresolutiontimefieldonfreshdesk":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SLAConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CsatConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "pagelimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PageLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PageLimit, nil
	case "pagesize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PageSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PageSize, nil
	case "iscsatcollectionenabledviaweb":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCsatCollectionEnabledViaWeb\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCsatCollectionEnabledViaWeb, nil
	case "csateligibilitywindow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CsatEligibilityWindow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CsatEligibilityWindow, nil
	case "allowedticketstatusesforcsat":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowedTicketStatusesForCsat\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowedTicketStatusesForCsat, nil
	case "allowedcommstypeforcsat":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowedCommsTypeForCsat\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowedCommsTypeForCsat, nil
	case "pushnotificationtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PushNotificationTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PushNotificationTitle, nil
	case "pushnotificationdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PushNotificationDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PushNotificationDescription, nil
	case "commsexternalrefidprefix":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CommsExternalRefIdPrefix\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CommsExternalRefIdPrefix, nil
	case "webformurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WebFormUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WebFormUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CsatConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CallConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isconsumereventloggingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsConsumerEventLoggingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsConsumerEventLoggingEnabled, nil
	case "iscallblockerenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCallBlockerEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCallBlockerEnabled, nil
	case "iscallblockingenabledviaivrflow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCallBlockingEnabledViaIvrFlow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCallBlockingEnabledViaIvrFlow, nil
	case "abandonedcallconfig":
		return obj.AbandonedCallConfig.Get(dynamicFieldPath[1:])
	case "callblockertestconfig":
		return obj.CallBlockerTestConfig.Get(dynamicFieldPath[1:])
	case "callblockerconfig":
		return obj.CallBlockerConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CallConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AbandonedCallConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isabandonedcallcommsenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAbandonedCallCommsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsAbandonedCallCommsEnabled, nil
	case "notificationtemplate":
		return obj.NotificationTemplate.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AbandonedCallConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CallBlockerTestConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "unregistereduser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UnRegisteredUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UnRegisteredUser, nil
	case "appaccessblocked":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AppAccessBlocked\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AppAccessBlocked, nil
	case "userreportedissue":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UserReportedIssue\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UserReportedIssue, nil
	case "iscreditfreeze":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCreditFreeze\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCreditFreeze, nil
	case "isstandardtier":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsStandardTier\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsStandardTier, nil
	case "isriskblocked":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRiskBlocked\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRiskBlocked, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CallBlockerTestConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CallBlockerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscallblockerenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCallBlockerEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCallBlockerEnabled, nil
	case "iscallblockingenabledviaivrflow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCallBlockingEnabledViaIvrFlow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCallBlockingEnabledViaIvrFlow, nil
	case "requesttimeout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RequestTimeout\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RequestTimeout, nil
	case "blocktierlist":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BlockTierList\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BlockTierList, nil
	case "triagedtierlist":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TriagedTierList\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TriagedTierList, nil
	case "calldropoffnotificationtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CallDropOffNotificationTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CallDropOffNotificationTitle, nil
	case "calldropoffnotificationbody":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CallDropOffNotificationBody\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CallDropOffNotificationBody, nil
	case "contactusflowsmslink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ContactUsFlowSmsLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ContactUsFlowSmsLink, nil
	case "fiappdownloadlink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FiAppDownloadLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FiAppDownloadLink, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CallBlockerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CallRoutingConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "defaultpriorityvalue":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultPriorityValue\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultPriorityValue, nil
	case "maxpriorityvalue":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxPriorityValue\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxPriorityValue, nil
	case "calldropoffcountthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CallDropOffCountThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CallDropOffCountThreshold, nil
	case "halvingfactor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HalvingFactor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HalvingFactor, nil
	case "pastcallrecordslookupsize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PastCallRecordsLookupSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PastCallRecordsLookupSize, nil
	case "ismanualroutingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsManualRoutingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsManualRoutingEnabled, nil
	case "issalaryprogramroutingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsSalaryProgramRoutingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsSalaryProgramRoutingEnabled, nil
	case "iscreditcardroutingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCreditCardRoutingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCreditCardRoutingEnabled, nil
	case "isactiveloanroutingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsActiveLoanRoutingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsActiveLoanRoutingEnabled, nil
	case "ispriorityroutingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsPriorityRoutingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsPriorityRoutingEnabled, nil
	case "isrecordedmessageeventenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRecordedMessageEventEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRecordedMessageEventEnabled, nil
	case "calllangpreferenceslist":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CallLangPreferencesList\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CallLangPreferencesList, nil
	case "calllangsuggestionslist":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CallLangSuggestionsList\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CallLangSuggestionsList, nil
	case "queuewaittimedurationthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QueueWaitTimeDurationThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QueueWaitTimeDurationThreshold, nil
	case "pastcallrecordslookupduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PastCallRecordsLookupDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PastCallRecordsLookupDuration, nil
	case "blockedaccountcallroutingconfig":
		return obj.BlockedAccountCallRoutingConfig.Get(dynamicFieldPath[1:])
	case "prerecordedmessageconfig":
		return obj.PreRecordedMessageConfig.Get(dynamicFieldPath[1:])
	case "issueprioritycacheconfig":
		return obj.IssuePriorityCacheConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CallRoutingConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *BlockedAccountCallRoutingConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isroutingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRoutingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRoutingEnabled, nil
	case "freezestatustoreasoncodemap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.FreezeStatusToReasonCodeMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"FreezeStatusToReasonCodeMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.FreezeStatusToReasonCodeMap[dynamicFieldPath[1]], nil

		}
		return obj.FreezeStatusToReasonCodeMap, nil
	case "accessrevokereasonmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AccessRevokeReasonMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AccessRevokeReasonMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AccessRevokeReasonMap[dynamicFieldPath[1]], nil

		}
		return obj.AccessRevokeReasonMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for BlockedAccountCallRoutingConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PreRecordedMessageConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ishighriskmessageenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsHighRiskMessageEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsHighRiskMessageEnabled, nil
	case "isscreenerrejectedmessageenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsScreenerRejectedMessageEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsScreenerRejectedMessageEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PreRecordedMessageConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IssuePriorityCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "allowmaxpriorityforissue":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowMaxPriorityForIssue\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowMaxPriorityForIssue, nil
	case "key":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Key\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Key, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IssuePriorityCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IssueResolutionFeedbackConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "disputeconfig":
		return obj.DisputeConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IssueResolutionFeedbackConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DisputeFeedbackConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "waitdurationafterfinalcomms":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WaitDurationAfterFinalComms\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WaitDurationAfterFinalComms, nil
	case "issuecategoryidtotransactiontype":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.IssueCategoryIdToTransactionType, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"IssueCategoryIdToTransactionType\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.IssueCategoryIdToTransactionType[dynamicFieldPath[1]], nil

		}
		return obj.IssueCategoryIdToTransactionType, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DisputeFeedbackConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ChatBotConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxclientsidefailurecountallowedforautoretry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxClientSideFailureCountAllowedForAutoRetry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxClientSideFailureCountAllowedForAutoRetry, nil
	case "numoftxnstobefetched":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NumOfTxnsToBeFetched\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NumOfTxnsToBeFetched, nil
	case "numofchargestobedisplayed":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NumOfChargesToBeDisplayed\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NumOfChargesToBeDisplayed, nil
	case "isforcefallbacktodefaultenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsForceFallbackToDefaultEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsForceFallbackToDefaultEnabled, nil
	case "isreleaseevaluationenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsReleaseEvaluationEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsReleaseEvaluationEnabled, nil
	case "isextraloggingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsExtraLoggingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsExtraLoggingEnabled, nil
	case "iscontextcodepassingfromclientenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsContextCodePassingFromClientEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsContextCodePassingFromClientEnabled, nil
	case "isfreshchatexperimentenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsFreshChatExperimentEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsFreshChatExperimentEnabled, nil
	case "maxtimedurationthresholdforlastsuccessfulsessiontime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxTimeDurationThresholdForLastSuccessfulSessionTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxTimeDurationThresholdForLastSuccessfulSessionTime, nil
	case "actoridsenabledforforcenuggetchatbot":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ActorIdsEnabledForForceNuggetChatbot, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"ActorIdsEnabledForForceNuggetChatbot\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.ActorIdsEnabledForForceNuggetChatbot[dynamicFieldPath[1]], nil

		}
		return obj.ActorIdsEnabledForForceNuggetChatbot, nil
	case "actoridsenabledfreshchatissuetreeexperiment":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ActorIdsEnabledFreshchatIssueTreeExperiment\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ActorIdsEnabledFreshchatIssueTreeExperiment, nil
	case "defaultinappchatview":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultInAppChatView\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultInAppChatView, nil
	case "nuggetdeeplinkuri":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NuggetDeeplinkUri\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NuggetDeeplinkUri, nil
	case "nuggetnamespace":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NuggetNamespace\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NuggetNamespace, nil
	case "livechatfallbackconfig":
		return obj.LiveChatFallbackConfig.Get(dynamicFieldPath[1:])
	case "senseforthchatinitinfo":
		return obj.SenseforthChatInitInfo.Get(dynamicFieldPath[1:])
	case "txnlistdisplayformat":
		return obj.TxnListDisplayFormat.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ChatBotConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LiveChatFallbackConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "channelid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ChannelId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ChannelId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LiveChatFallbackConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SenseforthChatInitInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "webviewurlmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.WebViewURLMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"WebViewURLMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.WebViewURLMap[dynamicFieldPath[1]], nil

		}
		return obj.WebViewURLMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SenseforthChatInitInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ChatBotDisplayFormat) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "fieldsorder":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FieldsOrder\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FieldsOrder, nil
	case "delimiter":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Delimiter\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Delimiter, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ChatBotDisplayFormat", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RiskConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablebackenddrivencharts":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableBackendDrivenCharts\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableBackendDrivenCharts, nil
	case "devactionconfig":
		return obj.DevActionConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RiskConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UploadRiskCaseDevActionConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "riskcaseeventbatchsize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RiskCaseEventBatchSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RiskCaseEventBatchSize, nil
	case "supportedpayloadtypes":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SupportedPayloadTypes\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SupportedPayloadTypes, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UploadRiskCaseDevActionConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InternationalFundTransfer) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablelrscheckfromvendor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableLRSCheckFromVendor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableLRSCheckFromVendor, nil
	case "documentsbucketname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DocumentsBucketName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DocumentsBucketName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InternationalFundTransfer", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SalaryOpsConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxbepaginatedcallsforfiltering":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxBEPaginatedCallsForFiltering\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxBEPaginatedCallsForFiltering, nil
	case "salarytransactionfilters":
		return obj.SalaryTransactionFilters.Get(dynamicFieldPath[1:])
	case "salaryprogramhealthinsuranceconfig":
		return obj.SalaryProgramHealthInsuranceConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SalaryOpsConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SalaryTransactionFilters) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minsalaryamount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinSalaryAmount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinSalaryAmount, nil
	case "minreqdurationfromlastverification":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinReqDurationFromLastVerification\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinReqDurationFromLastVerification, nil
	case "maxalloweddurationfromlastverification":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxAllowedDurationFromLastVerification\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxAllowedDurationFromLastVerification, nil
	case "allowedtransactionprotocols":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowedTransactionProtocols\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowedTransactionProtocols, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SalaryTransactionFilters", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SalaryProgramHealthInsuranceConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "policyfaqsdocs3path":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PolicyFAQsDocS3Path\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PolicyFAQsDocS3Path, nil
	case "policyclaimprocessdocs3path":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PolicyClaimProcessDocS3Path\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PolicyClaimProcessDocS3Path, nil
	case "inclusionexclusionandhowitworksdocs3path":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InclusionExclusionAndHowItWorksDocS3Path\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InclusionExclusionAndHowItWorksDocS3Path, nil
	case "tncsdocs3path":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TncsDocS3Path\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TncsDocS3Path, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SalaryProgramHealthInsuranceConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LandingPageConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isexpectedresolutiontimefieldpopulatedinlandingpageservice":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsExpectedResolutionTimeFieldPopulatedInLandingPageService\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsExpectedResolutionTimeFieldPopulatedInLandingPageService, nil
	case "recentuserqueryconfig":
		return obj.RecentUserQueryConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LandingPageConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RecentUserQueryConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "numberofqueriestodisplay":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NumberOfQueriesToDisplay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NumberOfQueriesToDisplay, nil
	case "dateformat":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DateFormat\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DateFormat, nil
	case "timeformat":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TimeFormat\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TimeFormat, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RecentUserQueryConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DevActionHelperConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isbulkresourceaccessibilitycheckenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsBulkResourceAccessibilityCheckEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsBulkResourceAccessibilityCheckEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DevActionHelperConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OverrideBankActions) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxrequests":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxRequests\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxRequests, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OverrideBankActions", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReviewActionConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "commonquestionstouser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CommonQuestionsToUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CommonQuestionsToUser, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReviewActionConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VendorAccountPennyDropViaEmailConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "toemailname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ToEmailName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ToEmailName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VendorAccountPennyDropViaEmailConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EmployerDbConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "indexingtimeduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IndexingTimeDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IndexingTimeDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EmployerDbConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *GRPCWebServerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "jarvisinterceptorconf":
		return obj.JarvisInterceptorConf.Get(dynamicFieldPath[1:])
	case "httpcorsoptions":
		return obj.HttpCorsOptions.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for GRPCWebServerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SherlockBannersConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isserviceenabledfordynamicfetching":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.IsServiceEnabledForDynamicFetching, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"IsServiceEnabledForDynamicFetching\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.IsServiceEnabledForDynamicFetching[dynamicFieldPath[1]], nil

		}
		return obj.IsServiceEnabledForDynamicFetching, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SherlockBannersConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ErrorActivityConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ispipingerroreventtowatsonenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsPipingErrorEventToWatsonEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsPipingErrorEventToWatsonEnabled, nil
	case "defaultincidentcreationcooloffperiod":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultIncidentCreationCoolOffPeriod\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultIncidentCreationCoolOffPeriod, nil
	case "issuecategoryideventpayloadkey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IssueCategoryIdEventPayloadKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IssueCategoryIdEventPayloadKey, nil
	case "clientrequestideventpayloadkey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ClientRequestIdEventPayloadKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ClientRequestIdEventPayloadKey, nil
	case "isresolutioneventbooleaneventpayloadkey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsResolutionEventBooleanEventPayloadKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsResolutionEventBooleanEventPayloadKey, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ErrorActivityConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AgentPromptConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "agentpromptinfomap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AgentPromptInfoMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.AgentPromptInfoMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.AgentPromptInfoMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AgentPromptConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AgentPromptInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ispromptenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsPromptEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsPromptEnabled, nil
	case "promptvalueforagent":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PromptValueForAgent\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PromptValueForAgent, nil
	case "description":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Description\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Description, nil
	case "promptcommstemplate":
		return obj.PromptCommsTemplate.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AgentPromptInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PromptCommsTemplate) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "description":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Description\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Description, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PromptCommsTemplate", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RiskOpsInstalledAppsConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RiskOpsInstalledAppsConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RiskFennelConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "apiversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"APIVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.APIVersion, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RiskFennelConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *StageWiseCommsConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isgenericcommsenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsGenericCommsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsGenericCommsEnabled, nil
	case "isissueconfigspecificcommsenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsIssueConfigSpecificCommsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsIssueConfigSpecificCommsEnabled, nil
	case "ispublishingmanualticketcreationeventenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsPublishingManualTicketCreationEventEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsPublishingManualTicketCreationEventEnabled, nil
	case "ispublishingmanualticketupdateeventenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsPublishingManualTicketUpdateEventEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsPublishingManualTicketUpdateEventEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for StageWiseCommsConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IssueConfigServiceConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "usenewcategorymappingforllmscreen":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseNewCategoryMappingForLLMScreen\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseNewCategoryMappingForLLMScreen, nil
	case "iscacheenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCacheEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCacheEnabled, nil
	case "issueconfiglevelcachevalidityduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IssueConfigLevelCacheValidityDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IssueConfigLevelCacheValidityDuration, nil
	case "configtypemapping":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ConfigTypeMapping, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"ConfigTypeMapping\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.ConfigTypeMapping[dynamicFieldPath[1]], nil

		}
		return obj.ConfigTypeMapping, nil
	case "issueconfiglevelcachekey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IssueConfigLevelCacheKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IssueConfigLevelCacheKey, nil
	case "issuecategorycreatedfromtime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IssueCategoryCreatedFromTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IssueCategoryCreatedFromTime, nil
	case "issuecategorycreatedtotime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IssueCategoryCreatedToTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IssueCategoryCreatedToTime, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IssueConfigServiceConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *S3EventConsumerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscallsummarizationprocessingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCallSummarizationProcessingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCallSummarizationProcessingEnabled, nil
	case "bucketname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BucketName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BucketName, nil
	case "callsummarizationfilepath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CallSummarizationFilePath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CallSummarizationFilePath, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for S3EventConsumerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RiskTxnReviewRolloutConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isselectedorderrpcenabledforall":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsSelectedOrderRpcEnabledForAll\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsSelectedOrderRpcEnabledForAll, nil
	case "selectedorderrpcwhitelistedemails":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SelectedOrderRpcWhitelistedEmails\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SelectedOrderRpcWhitelistedEmails, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RiskTxnReviewRolloutConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RiskOutcallFormRolloutConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxformsperuser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxFormsPerUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxFormsPerUser, nil
	case "disableforallagents":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableForAllAgents\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableForAllAgents, nil
	case "whitelistedreviewtypes":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WhitelistedReviewTypes\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WhitelistedReviewTypes, nil
	case "whitelistedquestionnairetemplates":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WhitelistedQuestionnaireTemplates\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WhitelistedQuestionnaireTemplates, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RiskOutcallFormRolloutConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CallIvrConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ivrpollcountthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IvrPollCountThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IvrPollCountThreshold, nil
	case "maxinvalidinputcount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxInvalidInputCount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxInvalidInputCount, nil
	case "maxrepeatinputcount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxRepeatInputCount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxRepeatInputCount, nil
	case "isivrenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsIvrEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsIvrEnabled, nil
	case "iscardblockingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCardBlockingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCardBlockingEnabled, nil
	case "ivrpollcountcacheduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IvrPollCountCacheDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IvrPollCountCacheDuration, nil
	case "ivrpollcountcachekey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IvrPollCountCacheKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IvrPollCountCacheKey, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CallIvrConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CaseManagementActorActivities) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "timeout":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Timeout\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Timeout, nil
	case "allowedreviewtypes":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowedReviewTypes\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowedReviewTypes, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CaseManagementActorActivities", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "allowedaccesslevels":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AllowedAccessLevels\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AllowedAccessLevels, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ContactUsModelResponseConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "responsecachevalidityduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ResponseCacheValidityDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ResponseCacheValidityDuration, nil
	case "responsecachekey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ResponseCacheKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ResponseCacheKey, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ContactUsModelResponseConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Filegenerator) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "camss3bucket":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CamsS3Bucket\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CamsS3Bucket, nil
	case "karvys3bucket":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"KarvyS3Bucket\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.KarvyS3Bucket, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Filegenerator", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DbStateConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isrbacenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRbacEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRbacEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DbStateConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EscalationConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isescalationenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEscalationEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEscalationEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EscalationConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FederalEscalationConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "qphratelimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QPHRateLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QPHRateLimit, nil
	case "isupdateconsumerenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsUpdateConsumerEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsUpdateConsumerEnabled, nil
	case "queueid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QueueId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QueueId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FederalEscalationConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SaClosureEligibilityConf) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "qpsratelimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QpsRateLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QpsRateLimit, nil
	case "maxconcurrentworkers":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxConcurrentWorkers\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxConcurrentWorkers, nil
	case "maxactorsallowedincsv":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxActorsAllowedInCsv\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxActorsAllowedInCsv, nil
	case "maxprocessingduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxProcessingDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxProcessingDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SaClosureEligibilityConf", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LienConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "liendurationinhours":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LienDurationInHours\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LienDurationInHours, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LienConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NuggetEventConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "eventmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.EventMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.EventMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.EventMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NuggetEventConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EventDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "fieldpathmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.FieldPathMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"FieldPathMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.FieldPathMap[dynamicFieldPath[1]], nil

		}
		return obj.FieldPathMap, nil
	case "nuggeteventname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NuggetEventName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NuggetEventName, nil
	case "nuggetsubeventname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NuggetSubEventName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NuggetSubEventName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EventDetails", strings.Join(dynamicFieldPath, "."))
	}
}
