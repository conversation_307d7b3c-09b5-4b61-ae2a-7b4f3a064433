Application:
  Environment: "prod"
  Name: "cx"
  IsSecureRedis: true

Server:
  Ports:
    GrpcPort: 8095
    GrpcSecurePort: 9508
    HttpPort: 9999
    HttpPProfPort: 9990
    HttpSecurePort: 9785

EpifiDb:
  AppName: "cx"
  StatementTimeout: 10s
  Name: "sherlock"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Aws:
  Region: "ap-south-1"

Cognito:
  UserPoolId: "ap-south-1_fhSn37Fds"
  ClientId: "7325m0ladi9b3na81hn8cus3jc"

EmailVerification:
  VerificationUrl: "https://fi.money/customer-auth/email-verification"
  FromEmail: "<EMAIL>"
  FromEmailName: "Fi support"

MobilePromptVerification:
  NotificationTitle: "Verify Yourself"
  NotificationBody: "Please choose yes if the request was initiated by you"
  Validity: 300 #validity of mobile prompt in seconds

AuthFactorRetryLimit:
  DOB: 3
  MobilePrompt: 3
  EmailVerification: 3
  TransactionAmount: 3
  LastFivePanCharacters: 3
  PermanentAddressPinCode: 3
  FathersName: 3
  MothersName: 3
  Default: 3

CustomerAuth:
  AuthValidityDuration: "10m"
  EmailValidityDuration: "5m"
  MobilePromptValidityDuration: "2m"
  MaxResetCount: 1
  AgentCacheValidityDuration: "72h"
  IsInAppNotificationEnabled: true
  IsAgentCachingEnabled: true
  IsSkippingAuthEnabledAfterExternalAuth: true

CallIvrConfig:
  IsIvrEnabled: true
  IsCardBlockingEnabled: false

FreshdeskTicketPublisher:
  QueueName: "prod-freshdesk-ticket-queue"

FreshdeskTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-freshdesk-ticket-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed hourly for 10 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 10
          TimeUnit: "Hour"
      MaxAttempts: 15
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1m
    Namespace: "cx"

FreshdeskContactPublisher:
  QueueName: "prod-freshdesk-contact-queue"

FreshdeskContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-freshdesk-contact-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed hourly for 10 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 10
          TimeUnit: "Hour"
      MaxAttempts: 15
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputePublisher:
  QueueName: "prod-cx-dispute-queue"

DisputeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-dispute-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed hourly for 10 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 10
          TimeUnit: "Hour"
      MaxAttempts: 15
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeCreateTicketPublisher:
  QueueName: "prod-cx-dispute-create-ticket-queue"

DisputeCreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-dispute-create-ticket-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed hourly for 10 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 10
          TimeUnit: "Hour"
      MaxAttempts: 15
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

WatsonIncidentReportingPublisher:
  QueueName: "prod-cx-watson-incident-reporting-queue"

WatsonIncidentReportingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-watson-incident-reporting-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "cx"

WatsonIncidentResolutionPublisher:
  QueueName: "prod-cx-watson-incident-resolution-queue"

WatsonIncidentResolutionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-watson-incident-resolution-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "cx"

WatsonTicketEventPublisher:
  QueueName: "prod-cx-watson-ticket-event-queue"

WatsonTicketEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-watson-ticket-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

WatsonCreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-watson-create-ticket-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

CrmIssueTrackerIntegrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-crm-issue-tracker-integration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

CrmIssueTrackerIntegrationPublisher:
  QueueName: "prod-cx-crm-issue-tracker-integration-queue"

DisputeUpdateTicketPublisher:
  QueueName: "prod-cx-dispute-update-ticket-queue"

DisputeUpdateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-dispute-update-ticket-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed hourly for 10 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 10
          TimeUnit: "Hour"
      MaxAttempts: 15
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1     # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 6s  # 10 per minute
    Namespace: "cx"

DisputeAddNoteTicketPublisher:
  QueueName: "prod-cx-dispute-add-note-ticket-queue"

DisputeAddNoteTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-dispute-add-note-ticket-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed hourly for 10 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 10
          TimeUnit: "Hour"
      MaxAttempts: 15
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeExternalPublisher:
  QueueName: "prod-cx-dispute-events-external-queue"

RMSEventPublisher:
  QueueName: "prod-rms-event-queue"

RewardsManualGiveawayEventPublisher:
  QueueName: "prod-rewards-manual-giveaway-event-queue"

DevActionPublisher:
  QueueName: "prod-dev-action-delay-queue"

IFTFileProcessorEventPublisher:
  QueueName: "prod-pay-international-fund-transfer-process-file-queue"

DevActionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-dev-action-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 60
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskTicketDataEventSubscriberFifo:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "data-prod-fd-events-cx-queue.fifo"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

FreshdeskTicketDataEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-freshdesk-events"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 500
        Period: 1m
    Namespace: "cx"

AuditLog:
  DefaultLimit: 10

Sherlock:
  SherlockCallbackURL: "https://sherlock.epifi.in/external-api/v1/customer-auth"
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30

Secrets:
  Ids:
    SherlockApiKey: "prod/cx-sherlock/internal-api-key"
    DbUsernamePassword: "prod/rds/epifimetis/sherlock"
    FreshchatAppId: "prod/cx/freshchat-sdk-app-id"
    FreshchatAppKey: "prod/cx/freshchat-sdk-app-key"
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    FederalPoolAccountNo: "prod/cx/federal-pool-acc-no"
    MonorailServiceAccountKey: "prod/monorail/service-account-private-key"
    AirflowUsernamePassword: "prod/data/airflow"
    KafkaCredentials: "prod/kafka/cx"
    KafkaCaCertificate: "prod/kafka/cert"
    IFTReportsSlackBotOauthToken: "prod/ift/slack-bot-oauth-token"
    StrapiApiKey: "prod/cx/strapi"

Transaction:
  ToleranceValue: 10
  NumTxnToFetch: 3
  PageSize: 20

Comms:
  PageSize: 20

Dispute:
  DefaultDisputeConfigVersion: "DISPUTE_CONFIG_VERSION_V3"
  IsRestrictedReleaseEnabledForConfigVersion: true
  DisputeJobConfig:
    ReverseProcessingJobTimeout: 60m
  DisputeConfigVersionToSherlockReleaseConfigMap:
    # Enabling V8 (V7 + Updated Unauthorised Questions)
    DISPUTE_CONFIG_VERSION_V8:
      IsGroupCheckEnabled: false
      EnabledGroupMap:
        INTERNAL: true
    # Enabling V7(Eligible disputes going to DMP,FD and UDIR, this config has DMP escalation for UPI, CARD and IMPS channel and UDIR escalation for eligible UPI txns)
    DISPUTE_CONFIG_VERSION_V7:
      IsGroupCheckEnabled: false
      EnabledGroupMap:
        INTERNAL: true
    DISPUTE_CONFIG_VERSION_V6:
      IsGroupCheckEnabled: false
      EnabledGroupMap:
        INTERNAL: true
    DISPUTE_CONFIG_VERSION_V3:
      IsGroupCheckEnabled: false
  DisputeConfigVersionAndPlatformToReleaseConfMap:
    # Enabling V8 (V7 + Updated Unauthorised Questions)
    DISPUTE_CONFIG_VERSION_V8:
      ANDROID:
        IsGroupCheckEnabled: false
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 240
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: false
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 335
        MaxAppVersion: 2147483647
    # Enabling V7(Eligible disputes going to DMP,FD and UDIR, this config has DMP escalation for UPI, CARD and IMPS channel and UDIR escalation for eligible UPI txns)
    DISPUTE_CONFIG_VERSION_V7:
      ANDROID:
        IsGroupCheckEnabled: false
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 240
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: false
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 335
        MaxAppVersion: 2147483647
    # Enabling V6(Eligible disputes going to DMP and FD, this config has DMP escalation for UPI, CARD and IMPS channel)
    DISPUTE_CONFIG_VERSION_V6:
      ANDROID:
        IsGroupCheckEnabled: false
        EnabledGroupMap:
          CX_INTERNAL: true
        MinAppVersion: 240
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: false
        EnabledGroupMap:
          CX_INTERNAL: true
        MinAppVersion: 335
        MaxAppVersion: 2147483647
    # Enabling V5(UDIR + UPI External going to non-UDIR flow + retry attempt on UDIR failure going to non-UDIR flow)
    DISPUTE_CONFIG_VERSION_V5:
      ANDROID:
        IsGroupCheckEnabled: true
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 240
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: true
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 335
        MaxAppVersion: 2147483647
    # Enabling V4(UDIR + New questionnaire flow for internal users on android + IOS)
    DISPUTE_CONFIG_VERSION_V4:
      ANDROID:
        IsGroupCheckEnabled: true
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 185
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: true
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 275
        MaxAppVersion: 2147483647
    # Enabling V3(New questionnaire flow for all users on prod)
    DISPUTE_CONFIG_VERSION_V3:
      ANDROID:
        IsGroupCheckEnabled: false
        MinAppVersion: 185
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: false
        MinAppVersion: 275
        MaxAppVersion: 2147483647
  S3BucketName: "epifi-prod-federal-disputes"
  MaxThresholdDurationForEscalation: "2h"
  MaxAttemptCountForReverseProcessing: 5
  DisputeConfigVersion: "DISPUTE_CONFIG_VERSION_V1"
  IsIssueResolutionFeedbackCommsEnabledForDispute: true
  MaxPageSize: 30
  MaximumDaysDuration: 2160h
  IsGetNextQuestionsForAppV2Enabled: true
  IsUpiExternalProvenanceEvaluationEnabled: true

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-12395.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12395"
  AuthDetails:
    SecretPath: "prod/redis/cx/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"

AppLog:
  LogTTL: "360h"
  MaxLogCountPerUser: 5
  # RPC size limit is 4 MB. So using 3.5MB as chunk size
  LogChunkSize: 3500000

Flags:
  TrimDebugMessageFromStatus: false

Payout:
  StatusCheckDelay: "30m"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"
  CashPayout:
    FromActorIdForOrder: "actor-epifi-business-account"
    FromPiIdForOrder: "paymentinstrument-epifi-business-account"
    MinPayoutValueAllowedPerTicket: 1
    MaxPayoutValueAllowedPerTicket: 5500
    SingleAmountLimitForApproval: 100
    MaxPayoutAllowedInTimeframe: 5000
    TimeframeDuration: "2160h"
    EnableB2CTransactionViaCelestial: true
  FiCoinsPayout:
    MaxPayoutValueAllowedPerTicket: 10001
    SingleAmountLimitForApproval: 2501
    MaxAmountDisbursalInDurationByRole:
      AGENT:
        PAYOUT_CONSTRAINT_TIMEFRAME_MONTH:
          MaxAmountAllowedToBeDisbursed: 50001
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_WEEK:
          MaxAmountAllowedToBeDisbursed: 25001
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_DAY:
          MaxAmountAllowedToBeDisbursed: 10001
          MaxAmountBreachFallbackAction: "ESCALATE"
      ADMIN:
        PAYOUT_CONSTRAINT_TIMEFRAME_MONTH:
          MaxAmountAllowedToBeDisbursed: 50001
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_WEEK:
          MaxAmountAllowedToBeDisbursed: 25001
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_DAY:
          MaxAmountAllowedToBeDisbursed: 10001
          MaxAmountBreachFallbackAction: "ESCALATE"
      SUPER_ADMIN:
        PAYOUT_CONSTRAINT_TIMEFRAME_MONTH:
          MaxAmountAllowedToBeDisbursed: 25000000
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_WEEK:
          MaxAmountAllowedToBeDisbursed: 15000000
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_DAY:
          MaxAmountAllowedToBeDisbursed: 5000000
          MaxAmountBreachFallbackAction: "ESCALATE"
    MaxAmountDisbursalPerUser:
      MaxAmountAllowedToBeDisbursed: 30000
      MaxAmountBreachFallbackAction: "ESCALATE"
    IsPayoutViaFiCoinsEnabled: true
    FiCoinsRewardOfferId: "b955f421-1534-471d-bfaf-42a77df2ec2a"
    AllowedValues:
      - 2500
      - 5000
      - 7500
      - 10000

PayoutStatusCheckPublisher:
  QueueName: "prod-cx-payout-status-check-event-queue"
PayoutStatusCheckSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-payout-status-check-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"

WaitlistSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-freelancer-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

RateLimit:
  MaxRequestsPerMinPerUser: 1000
  MaxRequestPerMinPerUserPerApiDefault: 50
  # replace all occurrences of . and / with _ in the full method name before adding it here
  # need to do this because . and / are interpreted differently by config loader and should not be used in key
  MaxRequestPerMinPerUserPerApiMap:
    _cx_app_log_AppLog_GetLogsData: 1000
    _cx_developer_ticket_summary_TicketSummary_GetTicketDetails: 10
    _cx_data_collector_preapprovedloan_PreApprovedLoan_GetLoanDetails: 10
    _cx_data_collector_preapprovedloan_PreApprovedLoan_GetLoanUserDetails: 10

RlConfig:
  ResourceMap:
    sherlock_user:
      Rate: 1000
      Period: 1m
    _cx_app_log_AppLog_GetLogsData:
      Rate: 1000
      Period: 1m
    _cx_developer_ticket_summary_TicketSummary_GetTicketDetails:
      Rate: 20
      Period: 1m
    api_default:
      Rate: 100
      Period: 1m
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP:
      Rate: 10
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN:
      Rate: 15
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION:
      Rate: 30
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_CHECK_FAILURE:
      Rate: 10
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL:
      Rate: 20
      Period: 1s
    watson_incidents:
      # Incident creation rate can never exceed this limit, as this matches subscriber rate for Watson incident creation queue
      Rate: 80
      Period: 1s
  Namespace: "cx"

UsePkgRateLimiter: false

CallRecording:
  CallRecordingBucketName: "epifi-data-prod-ozonetel-call-recs"
  CallTranscriptionBucketName: "prod-epifi-ozonetel-transcription"

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

OrderConfig:
  TxnCountForLastNTxns: 5

LivenessVideoConfig:
  S3BucketName: "epifi-prod-liveness"

SupportTicketFreshdeskConfig:
  GroupEnumToGroupIdMapping:
    GROUP_CALLBACK: ***********
    GROUP_EPIFI_ESCALATION: ***********
    GROUP_ESCALATED_CASES_CLOSURE: ***********
    GROUP_FEDERAL_ESCALATIONS: 82000080120
    GROUP_L1_SUPPORT: ***********
    GROUP_L2_SUPPORT: 82000080153
    GROUP_NON_SFTP_ESCALATIONS: 82000080122
    GROUP_SFTP_ESCALATIONS: 82000080152
    GROUP_SFTP_PENDING_GROUP: ***********
    GROUP_FEDERAL_UPDATES: ***********
    GROUP_L1_SUPPORT_WAITLIST: ***********
    GROUP_RISK_OPS: ***********
    GROUP_L1_SUPPORT_CALL: ***********
    GROUP_L1_SUPPORT_CHAT: ***********
    GROUP_L1_SUPPORT_EMAIL: ***********
    GROUP_L1_SUPPORT_SOCIAL_MEDIA: ***********
    GROUP_OUTBOUND_CALL_BACK: ***********
    GROUP_LOAN_OUTBOUND_CALL: ***********
  ProductCategoryEnumToValueMapping:
    PRODUCT_CATEGORY_TRANSACTION: "Transactions"
    PRODUCT_CATEGORY_ACCOUNTS: "Accounts"
    PRODUCT_CATEGORY_ONBOARDING: "Onboarding"
    PRODUCT_CATEGORY_SAVE: "Save"
    PRODUCT_CATEGORY_WAITLIST: "Waitlist"
    PRODUCT_CATEGORY_RE_ONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_REWARDS: "Rewards"
    PRODUCT_CATEGORY_FIT: "FIT"
    PRODUCT_CATEGORY_DEBIT_CARD: "Debit Card"
    PRODUCT_CATEGORY_REFERRALS: "Referrals"
    PRODUCT_CATEGORY_CONNECTED_ACCOUNTS: "Connected Accounts"
    PRODUCT_CATEGORY_FRAUD_AND_RISK: "Fraud & Risk"
    PRODUCT_CATEGORY_JUMP_P2P: "Jump P2P"
    PRODUCT_CATEGORY_PROFILE: "Profile"
    PRODUCT_CATEGORY_SALARY_ACCOUNT: "Salary account"
    PRODUCT_CATEGORY_SEARCH: "Search"
    PRODUCT_CATEGORY_WEALTH_ONBOARDING: "Wealth Onboarding"
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS: "Wealth Mutual Funds"
    PRODUCT_CATEGORY_APP_CRASH: "App Crash"
    PRODUCT_CATEGORY_DATA_DELETION: "Data deletion"
    PRODUCT_CATEGORY_SCREENER: "Screener"
    PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED: "Google Token expired"
    PRODUCT_CATEGORY_LANGUAGE_CALLBACK: "Language callback"
    PRODUCT_CATEGORY_CATEGORY_NOT_FOUND: "Category not found"
    PRODUCT_CATEGORY_KYC_OUTCALL: "KYC Outcall"
    PRODUCT_CATEGORY_TRANSACTION_ISSUES: "Transaction Issues"
    PRODUCT_CATEGORY_REWARDS_NEW: "Rewards New"
    PRODUCT_CATEGORY_REFERRALS_NEW: "Referrals New"
    PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI: "General enquiries about Fi"
    PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT: "No response/ Blank chat"
    PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED: "Call drop/ disconnected"
    PRODUCT_CATEGORY_INSTANT_LOANS: "Instant Loans"
    PRODUCT_CATEGORY_TIERING_PLANS: "Tiering plans"
    PRODUCT_CATEGORY_CREDIT_CARD: "Credit Card"
    PRODUCT_CATEGORY_US_STOCKS: "US stocks"
    PRODUCT_CATEGORY_DEVICE: "Device"
    PRODUCT_CATEGORY_RISK: "Risk"
    PRODUCT_CATEGORY_ON_APP_TRANSACTIONS: "In-App Transactions"
    PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS: "Off-App Transactions"
    PRODUCT_CATEGORY_INSTANT_SALARY : "Instant Salary"
    PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD: "SimpliFi Credit Card"
    PRODUCT_CATEGORY_LAMF: "LAMF"
    PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD: "MagniFi Credit Card"
    PRODUCT_CATEGORY_SALARY_LITE: "Salary Lite"
    PRODUCT_CATEGORY_FI_STORE: "Fi-Store"
    PRODUCT_CATEGORY_GENERAL_ENQUIRY: "General Enquiry"
    PRODUCT_CATEGORY_APP_RELATED: "App Related"
    PRODUCT_CATEGORY_DEPOSITS_AND_INVESTMENTS: "Deposits & Investments"
    PRODUCT_CATEGORY_INCOMPLETE_CONVERSATION: "Incomplete Conversation"
    PRODUCT_CATEGORY_LOANS: "Loans"
    PRODUCT_CATEGORY_NET_WORTH: "Net Worth"
    PRODUCT_CATEGORY_SERVICE_REQUESTS: "Service Requests"
  TransactionTypeEnumToValueMapping:
    TRANSACTION_TYPE_DEBIT_CARD: "DEBIT CARD"
    TRANSACTION_TYPE_IMPS: "IMPS"
    TRANSACTION_TYPE_NEFT: "NEFT"
    TRANSACTION_TYPE_RTGS: "RTGS"
    TRANSACTION_TYPE_UPI: "UPI"
    TRANSACTION_TYPE_ECOM: "ECOM"
    TRANSACTION_TYPE_POS_ATM: "POS/ATM"
    TRANSACTION_TYPE_UNKNOWN: "Unknown"
  DisputeStatusEnumToValueMapping:
    DISPUTE_STATUS_ACCEPTED: "Accepted"
    DISPUTE_STATUS_REJECTED: "Rejected"
  StatusEnumToValueMapping:
    STATUS_UNSPECIFIED: 0
    STATUS_OPEN: 2
    STATUS_PENDING: 3
    STATUS_RESOLVED: 4
    STATUS_CLOSED: 5
    STATUS_WAITING_ON_THIRD_PARTY: 7
    STATUS_ESCALATED_TO_L2: 8
    STATUS_ESCALATED_TO_FEDERAL: 11
    STATUS_SEND_TO_PRODUCT: 24
    STATUS_WAITING_ON_PRODUCT: 25
    STATUS_REOPEN: 26
    STATUS_NEEDS_CLARIFICATION_FROM_CX: 27
    STATUS_WAITING_ON_CUSTOMER: 12
  ProductCategoryDetailsEnumToValueMapping:
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_MANUAL_WHITELISTING: "Manual Whitelisting"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE: "App download issue"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_CHECK_FAILURE: "Device check failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_PHONE_NUMBER_OTP: "Phone number OTP"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_EMAIL_SELECTION_FAILURE: "Email selection failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_MOTHER_FATHER_NAME: "Mother Father Name"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_PAN_NAME_VALIDATION_FAILURE: "PAN Name validation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_EXISTING_FEDERAL_ACCOUNT: "Existing Federal account"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_KYC: "KYC"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_LIVENESS: "Liveness"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_FACEMATCH_FAIL: "Face-match fail"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UN_NAME_CHECK: "UN Name check"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CONFIRM_CARD_MAILING_ADDRESS: "Confirm Card Mailing address"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_CONSENT_FAILURE: "UPI Consent failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CUSTOMER_CREATION_FAILURE: "Customer creation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED: "Account opening delayed"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_CREATION_FAILURE: "Card creation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_PIN_SET_FAILURE: "Card PIN set failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_SETUP_FAILURE: "UPI setup failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_VKYC: "VKYC"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_REONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN: "PIN"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION: "Activation"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY: "Delivery"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP: "Debited via Fi app  but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP: "Debited from FI account (via Other App) but"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY: "Min KYC expiry"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM: "Cards - ATM"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT: "UPI - Unable to transact"
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL: "Investment Transaction Successful"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CLOSURE_REQUEST: "Account Closure Request"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_OPENING_ISSUES: "Account Opening Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UPGRADE_DOWNGRADE: "Account Upgrade/Downgrade"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_BALANCE_TRANSFER: "Balance Transfer"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CATEGORY_NOT_FOUND: "Category Not Found"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CHEQUEBOOK_RELATED: "Chequebook Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_DORMANT: "Dormant"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_FEES_AND_CHARGES: "Fees & Charges"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_KYC_RELATED: "KYC Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_LIEN_ON_ACCOUNT: "Lien On Account"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_RE_LOGIN_ISSUES: "Re Login Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_SALARY_PROGRAM: "Salary Program"
    PRODUCT_CATEGORY_DETAILS_APP_RELATED_ISSUES: "App Related Issues"
    PRODUCT_CATEGORY_DETAILS_BANK_INCOMING: "Bank Incoming"
    PRODUCT_CATEGORY_DETAILS_CX_INCOMING: "CX Incoming"
    PRODUCT_CATEGORY_DETAILS_BLOCK_PERMANENTLY: "Block Permanently"
    PRODUCT_CATEGORY_DETAILS_CARD_REQUEST: "Card Request"
    PRODUCT_CATEGORY_DETAILS_CARD_SETTINGS: "Card Settings"
    PRODUCT_CATEGORY_DETAILS_FIT_RULES: "FIT Rules"
    PRODUCT_CATEGORY_DETAILS_JUMP: "Jump"
    PRODUCT_CATEGORY_DETAILS_MF_INVESTMENTS: "MF Investments"
    PRODUCT_CATEGORY_DETAILS_MF_ONBOARDING: "MF Onboarding"
    PRODUCT_CATEGORY_DETAILS_MF_WITHDRAWALS: "MF Withdrawals"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS: "US Stocks"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS_WALLET_ISSUES: "US Stocks Wallet Issues"
    PRODUCT_CATEGORY_DETAILS_DEPRECATED_PRODUCT: "Deprecated Product"
    PRODUCT_CATEGORY_DETAILS_BLANK_CHAT: "Blank Chat"
    PRODUCT_CATEGORY_DETAILS_CALL_DROP_DISCONNECTED: "Call Drop/Disconnected"
    PRODUCT_CATEGORY_DETAILS_INCOMPLETE_EMAIL: "Incomplete Email"
    PRODUCT_CATEGORY_DETAILS_LOAN_APPLICATION_DISBURSAL: "Application/Disbursal Issue"
    PRODUCT_CATEGORY_DETAILS_LOAN_BUREAU_CIBIL: "Bureau/Cibil"
    PRODUCT_CATEGORY_DETAILS_LOAN_COLLECTIONS: "Collections"
    PRODUCT_CATEGORY_DETAILS_LOAN_EMI_RELATED: "EMI Related Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_LAMF_SHORTFALL: "LAMF Shortfall"
    PRODUCT_CATEGORY_DETAILS_LOAN_CLOSURE_REQUEST: "Loan Closure Request/Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_OUTCALLING: "Outcalling"
    PRODUCT_CATEGORY_DETAILS_LOAN_PERSONAL_DETAILS: "Personal Details Updatation"
    PRODUCT_CATEGORY_DETAILS_LOAN_REFUND_WAIVER: "Refund/Waiver Request"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_CONNECT: "Unable To Connect"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_DISCONNECT: "Unable to Disconnect"
    PRODUCT_CATEGORY_DETAILS_REWARDS_FI_POINTS_NOT_REFUNDED: "Fi Points Not Refunded"
    PRODUCT_CATEGORY_DETAILS_REWARDS_GIFT_CARDS: "Gift Cards"
    PRODUCT_CATEGORY_DETAILS_REWARDS_INCORRECT_REWARD: "Incorrect Reward Received"
    PRODUCT_CATEGORY_DETAILS_REWARDS_NOT_RECEIVED: "Reward Not Received"
    PRODUCT_CATEGORY_DETAILS_BANK_INITIATED_FREEZE: "Bank Initated Freeze"
    PRODUCT_CATEGORY_DETAILS_INVESTMENT_WITHDRAWALS: "Investment Withdrawals"
    PRODUCT_CATEGORY_DETAILS_LEA_NPCI_COMPLAINT: "LEA/NPCI Complaint"
    PRODUCT_CATEGORY_DETAILS_CALLBACK_REQUEST: "Callback Request"
    PRODUCT_CATEGORY_DETAILS_DATA_DELETION: "Data Deletion"
    PRODUCT_CATEGORY_DETAILS_NACH_AND_MANDATES: "Nach & Mandates"
    PRODUCT_CATEGORY_DETAILS_REVOKE_APP_ACCESS: "Revoke App Access"
    PRODUCT_CATEGORY_DETAILS_STOP_SERVICES: "Stop Services"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED: "Amount Debited"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED_NOT_CREDITED: "Amount Debited But Not Credited"
    PRODUCT_CATEGORY_DETAILS_AUTOMATED_PAYMENTS: "Automated Payments"
    PRODUCT_CATEGORY_DETAILS_UNAUTHORISED_FRAUD_TRANSACTIONS: "Unauthorised/Fraud Transactions"
    PRODUCT_CATEGORY_DETAILS_CHEQUE_TRANSACTION: "Cheque Transaction"
    PRODUCT_CATEGORY_DETAILS_DATA_NOT_REFRESHED: "Data Not Refreshed"
    PRODUCT_CATEGORY_DETAILS_BUYING_US_STOCKS: "Buying US Stocks"
    PRODUCT_CATEGORY_DETAILS_BUSINESS_COLLABORATION: "Business Collaboration"
    PRODUCT_CATEGORY_DETAILS_NET_BANKING: "Net Banking"
    PRODUCT_CATEGORY_DETAILS_UNREGISTERED_USER: "Unregistered User"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_PAY: "Unable To Pay"
    PRODUCT_CATEGORY_DETAILS_CREDIT_PENDING_TO_FI: "Credit Pending To Fi"
    PRODUCT_CATEGORY_DETAILS_DOCUMENT_REQUEST: "Document Request"
  SubCategoryDetailsEnumToValueMapping:
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_NEW: "New"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_APPROVED: "Approved"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_REJECTED: "Rejected"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_ON_HOLD: "On-Hold"
    SUB_CATEGORY_PIN_UPI_PIN: "UPI PIN"
    SUB_CATEGORY_PIN_DEVICE_PIN: "Device PIN"
    SUB_CATEGORY_PIN_APP_PIN: "App PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING: "QR code not working"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN: "Unable to set PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS: "Cannot enable POS"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS: "Cannot enable Contactless"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL: "Cannot enable ATM withdrawal"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED: "OTP not received"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE: "How to activate"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING: "Tracking"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD: "Did not receive card"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND: "Balance refund"
    SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE: "Debited but not dispensed at machine"
    SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED: "UPI pin tries exceeded"
    SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED: "Units not allotted"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FREEZE_ON_ACCOUNT: "Freeze On Account"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_IN_APP_REQUEST_RECEIVED: "In App Request Received (Auto ID)"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_MANUAL_REQUEST: "Manual Request"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_NOT_CLOSABLE_DUE_TO_PENDING_CHARGES: "Not Closable Due To Pending Charges"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_REDIRECTED_TO_APP: "Redirected To App"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FULL_KYC_ACCOUNT_CLOSED: "Full Kyc Account Closed"
    SUB_CATEGORY_ACCOUNTS_OPENING_ADD_FUNDS_ON_APP: "Add Funds On App"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_DOWNLOAD: "App Download"
    SUB_CATEGORY_ACCOUNTS_OPENING_DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CARD_CREATION_PIN_SETUP_FAILURE: "Card Creation & Pin Setup Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CONSENT_RELATED: "Consent Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_CUSTOMER_CREATION: "Customer Creation"
    SUB_CATEGORY_ACCOUNTS_OPENING_EXISTING_FEDERAL_ACCOUNT: "Existing Federal Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_KYC_RELATED: "KYC Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_LIVENESS_FACEMATCH_ISSUE: "Liveness & Facematch Issue"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_SCREENING: "App Screening"
    SUB_CATEGORY_ACCOUNTS_OPENING_REFUND_FOR_ADD_FUNDS: "Refund For Add Funds"
    SUB_CATEGORY_ACCOUNTS_OPENING_REOPEN_CLOSED_ACCOUNT: "Reopen Closed Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_STUCK_AT_EMAIL_VERIFICATION: "Stuck At Email Verfication"
    SUB_CATEGORY_ACCOUNTS_OPENING_VKYC_ISSUES: "Vkyc Issues"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_FUNDS_ADDED_BUT_ACCOUNT_NOT_UPGRADED: "Funds Added But A/C Not Upgraded"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_TIER_DOWNGRADE: "Tier Downgrade"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_WITHIN_COOL_OFF_PERIOD: "Within Cool Off Period"
    SUB_CATEGORY_ACCOUNTS_KYC_MIN_KYC_EXPIRED: "Min Kyc Expired"
    SUB_CATEGORY_ACCOUNTS_KYC_UNABLE_TO_SUBMIT_FORM: "Unable To Submit Form"
    SUB_CATEGORY_ACCOUNTS_KYC_NOT_UPDATED: "KYC Not Updated"
    SUB_CATEGORY_ACCOUNTS_KYC_UPDATED_BUT_ACCOUNT_NOT_ACTIVATED: "KYC Updated but A/C Not Activated"
    SUB_CATEGORY_ACCOUNTS_KYC_SIGNATURE_NOT_UPDATED: "Signature Not Updated"
    SUB_CATEGORY_CARD_REQUEST_CARD_REPLACEMENT: "Card Replacement"
    SUB_CATEGORY_CARD_REQUEST_CARD_UPGRADE: "Card Upgrade"
    SUB_CATEGORY_CARD_REQUEST_CARD_VARIANT_CHANGE: "Card Variant Change"
    SUB_CATEGORY_CARD_REQUEST_NEW_CARD: "New Card"
    SUB_CATEGORY_CARD_REQUEST_VIRTUAL_CARD: "Virtual Card"
    SUB_CATEGORY_CARD_SETTINGS_CARD_ACTIVATION: "Card Activation"
    SUB_CATEGORY_CARD_SETTINGS_CARD_BLOCK: "Card Block"
    SUB_CATEGORY_CARD_SETTINGS_CARD_DELIVERY: "Card Delivery"
    SUB_CATEGORY_CARD_SETTINGS_CARD_LIMIT: "Card Limit"
    SUB_CATEGORY_CARD_SETTINGS_CARD_PIN: "Card Pin"
    SUB_CATEGORY_CARD_SETTINGS_CARD_UNBLOCK: "Card Unblock"
    SUB_CATEGORY_CARD_SETTINGS_INTERNATIONAL_TRANSACTIONS: "International Transactions"
    SUB_CATEGORY_CARD_CHARGES_ANNUAL_CHARGES: "Annual Charges"
    SUB_CATEGORY_CARD_CHARGES_CARD_REPLACEMENT_CHARGES: "Card Replacement Charges"
    SUB_CATEGORY_CARD_CHARGES_FOREIGN_MARKUP_FEE: "Foreign Markup Fee"
    SUB_CATEGORY_CARD_CHARGES_JOINING_FEE: "Joining Fee"
    SUB_CATEGORY_CARD_CHARGES_LATE_PAYMENT_CHARGES: "Late Payment Charges"
    SUB_CATEGORY_CARD_CHARGES_OVERLIMIT_CHARGES: "Overlimit Charges"
    SUB_CATEGORY_CARD_CHARGES_PROCESSING_FEE: "Processing Fee"
    SUB_CATEGORY_CARD_INFO_CARD_BENEFITS: "Card Benefits"
    SUB_CATEGORY_CARD_INFO_CARD_ELIGIBILITY: "Card Eligibility"
    SUB_CATEGORY_CARD_INFO_CARD_FEATURES: "Card Features"
    SUB_CATEGORY_CARD_INFO_CARD_OFFERS: "Card Offers"
    SUB_CATEGORY_CARD_INFO_CARD_REWARDS: "Card Rewards"
    SUB_CATEGORY_CARD_INFO_CARD_TYPES: "Card Types"
    SUB_CATEGORY_ATM_TRANSACTION_AMOUNT_DEBITED_NOT_DISPENSED: "Amount Debited Not Dispensed"
    SUB_CATEGORY_ATM_TRANSACTION_AMOUNT_DISPENSED_NOT_DEBITED: "Amount Dispensed Not Debited"
    SUB_CATEGORY_ATM_TRANSACTION_CARD_BLOCKED: "Card Blocked"
    SUB_CATEGORY_ATM_TRANSACTION_CARD_CAPTURED: "Card Captured"
    SUB_CATEGORY_ATM_TRANSACTION_INCORRECT_AMOUNT_DISPENSED: "Incorrect Amount Dispensed"
    SUB_CATEGORY_ATM_TRANSACTION_LIMIT_EXCEEDED: "Limit Exceeded"
    SUB_CATEGORY_ATM_TRANSACTION_PIN_BLOCKED: "Pin Blocked"
    SUB_CATEGORY_ATM_TRANSACTION_TRANSACTION_DECLINED: "Transaction Declined"
    SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_DEBITED_NOT_CREDITED: "Amount Debited Not Credited"
    SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_CREDITED_NOT_DEBITED: "Amount Credited Not Debited"
    SUB_CATEGORY_TRANSACTION_ISSUES_DUPLICATE_TRANSACTION: "Duplicate Transaction"
    SUB_CATEGORY_TRANSACTION_ISSUES_FAILED_TRANSACTION: "Failed Transaction"
    SUB_CATEGORY_TRANSACTION_ISSUES_INCORRECT_AMOUNT: "Incorrect Amount"
    SUB_CATEGORY_TRANSACTION_ISSUES_MERCHANT_DISPUTE: "Merchant Dispute"
    SUB_CATEGORY_TRANSACTION_ISSUES_REFUND_NOT_RECEIVED: "Refund Not Received"
    SUB_CATEGORY_TRANSACTION_ISSUES_UNAUTHORIZED_TRANSACTION: "Unauthorized Transaction"
    SUB_CATEGORY_FIXED_DEPOSIT_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_FIXED_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_FIXED_DEPOSIT_MATURITY_AMOUNT: "Maturity Amount"
    SUB_CATEGORY_FIXED_DEPOSIT_PREMATURE_WITHDRAWAL: "Premature Withdrawal"
    SUB_CATEGORY_FIXED_DEPOSIT_RENEWAL: "Renewal"
    SUB_CATEGORY_FIXED_DEPOSIT_TDS: "TDS"
    SUB_CATEGORY_SMART_DEPOSIT_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_SMART_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_SMART_DEPOSIT_MATURITY_AMOUNT: "Maturity Amount"
    SUB_CATEGORY_SMART_DEPOSIT_PREMATURE_WITHDRAWAL: "Premature Withdrawal"
    SUB_CATEGORY_SMART_DEPOSIT_RENEWAL: "Renewal"
    SUB_CATEGORY_SMART_DEPOSIT_TDS: "TDS"
    SUB_CATEGORY_FIT_RULES_CREATION: "Creation"
    SUB_CATEGORY_FIT_RULES_DELETION: "Deletion"
    SUB_CATEGORY_FIT_RULES_EXECUTION: "Execution"
    SUB_CATEGORY_FIT_RULES_MODIFICATION: "Modification"
    SUB_CATEGORY_JUMP_ELIGIBILITY: "Eligibility"
    SUB_CATEGORY_JUMP_EXECUTION: "Execution"
    SUB_CATEGORY_JUMP_LIMIT: "Limit"
    SUB_CATEGORY_JUMP_SETTLEMENT: "Settlement"
    SUB_CATEGORY_MUTUAL_FUNDS_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_MUTUAL_FUNDS_INVESTMENT: "Investment"
    SUB_CATEGORY_MUTUAL_FUNDS_KYC: "KYC"
    SUB_CATEGORY_MUTUAL_FUNDS_REDEMPTION: "Redemption"
    SUB_CATEGORY_MUTUAL_FUNDS_SIP: "SIP"
    SUB_CATEGORY_MUTUAL_FUNDS_STATEMENT: "Statement"
    SUB_CATEGORY_MUTUAL_FUNDS_SWITCH: "Switch"
    SUB_CATEGORY_MUTUAL_FUNDS_TAX: "Tax"
    SUB_CATEGORY_US_STOCKS_ACCOUNT_OPENING: "Account Opening"
    SUB_CATEGORY_US_STOCKS_BUYING: "Buying"
    SUB_CATEGORY_US_STOCKS_KYC: "KYC"
    SUB_CATEGORY_US_STOCKS_SELLING: "Selling"
    SUB_CATEGORY_US_STOCKS_STATEMENT: "Statement"
    SUB_CATEGORY_US_STOCKS_TAX: "Tax"
    SUB_CATEGORY_US_STOCKS_TRANSFER: "Transfer"
    SUB_CATEGORY_US_STOCKS_WALLET: "Wallet"
    SUB_CATEGORY_FI_STORE_ACTIVATION: "Activation"
    SUB_CATEGORY_FI_STORE_CANCELLATION: "Cancellation"
    SUB_CATEGORY_FI_STORE_DELIVERY: "Delivery"
    SUB_CATEGORY_FI_STORE_PAYMENT: "Payment"
    SUB_CATEGORY_FI_STORE_REFUND: "Refund"
    SUB_CATEGORY_FI_STORE_RETURN: "Return"
    SUB_CATEGORY_SALARY_PROGRAMS_ACTIVATION: "Activation"
    SUB_CATEGORY_SALARY_PROGRAMS_BENEFITS: "Benefits"
    SUB_CATEGORY_SALARY_PROGRAMS_ELIGIBILITY: "Eligibility"
    SUB_CATEGORY_SALARY_PROGRAMS_EMPLOYER: "Employer"
    SUB_CATEGORY_SALARY_PROGRAMS_SALARY_CREDIT: "Salary Credit"
    SUB_CATEGORY_LOANS_APPLICATION: "Application"
    SUB_CATEGORY_LOANS_DISBURSEMENT: "Disbursement"
    SUB_CATEGORY_LOANS_DOCUMENTATION: "Documentation"
    SUB_CATEGORY_LOANS_EMI: "EMI"
    SUB_CATEGORY_LOANS_FORECLOSURE: "Foreclosure"
    SUB_CATEGORY_LOANS_INTEREST: "Interest"
    SUB_CATEGORY_LOANS_PREPAYMENT: "Prepayment"
    SUB_CATEGORY_LOANS_STATEMENT: "Statement"
    SUB_CATEGORY_ASSETS_BALANCE: "Balance"
    SUB_CATEGORY_ASSETS_CATEGORIZATION: "Categorization"
    SUB_CATEGORY_ASSETS_LINKING: "Linking"
    SUB_CATEGORY_ASSETS_REFRESH: "Refresh"
    SUB_CATEGORY_ASSETS_SYNC: "Sync"
    SUB_CATEGORY_ASSETS_UNLINKING: "Unlinking"
    SUB_CATEGORY_REWARDS_CASHBACK: "Cashback"
    SUB_CATEGORY_REWARDS_FI_COINS: "Fi Coins"
    SUB_CATEGORY_REWARDS_GIFT_CARDS: "Gift Cards"
    SUB_CATEGORY_REWARDS_OFFERS: "Offers"
    SUB_CATEGORY_REWARDS_POINTS: "Points"
    SUB_CATEGORY_REWARDS_VOUCHERS: "Vouchers"
    SUB_CATEGORY_ACCOUNT_SECURITY_BIOMETRIC: "Biometric"
    SUB_CATEGORY_ACCOUNT_SECURITY_DEVICE: "Device"
    SUB_CATEGORY_ACCOUNT_SECURITY_EMAIL: "Email"
    SUB_CATEGORY_ACCOUNT_SECURITY_MOBILE: "Mobile"
    SUB_CATEGORY_ACCOUNT_SECURITY_PASSWORD: "Password"
    SUB_CATEGORY_ACCOUNT_SECURITY_PIN: "PIN"
    SUB_CATEGORY_LANGUAGE_SUPPORT_APP: "App"
    SUB_CATEGORY_LANGUAGE_SUPPORT_COMMUNICATION: "Communication"
    SUB_CATEGORY_LANGUAGE_SUPPORT_DOCUMENTS: "Documents"
    SUB_CATEGORY_LANGUAGE_SUPPORT_SUPPORT: "Support"
    SUB_CATEGORY_DATA_STATEMENTS_ACCOUNT_STATEMENT: "Account Statement"
    SUB_CATEGORY_DATA_STATEMENTS_INTEREST_CERTIFICATE: "Interest Certificate"
    SUB_CATEGORY_DATA_STATEMENTS_LOAN_STATEMENT: "Loan Statement"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT: "Tax Statement"
    SUB_CATEGORY_DATA_STATEMENTS_TDS_CERTIFICATE: "TDS Certificate"
    SUB_CATEGORY_MANDATES_ACTIVATION: "Activation"
    SUB_CATEGORY_MANDATES_CANCELLATION: "Cancellation"
    SUB_CATEGORY_MANDATES_MODIFICATION: "Modification"
    SUB_CATEGORY_MANDATES_REGISTRATION: "Registration"
    SUB_CATEGORY_MANDATES_STATUS: "Status"
    SUB_CATEGORY_PROFILE_UPDATES_ADDRESS: "Address"
    SUB_CATEGORY_PROFILE_UPDATES_DOB: "DOB"
    SUB_CATEGORY_PROFILE_UPDATES_EMAIL: "Email"
    SUB_CATEGORY_PROFILE_UPDATES_MOBILE: "Mobile"
    SUB_CATEGORY_PROFILE_UPDATES_NAME: "Name"
    SUB_CATEGORY_PROFILE_UPDATES_PAN: "PAN"
    SUB_CATEGORY_DEVICE_ISSUES_APP_CRASH: "App Crash"
    SUB_CATEGORY_DEVICE_ISSUES_APP_HANG: "App Hang"
    SUB_CATEGORY_DEVICE_ISSUES_BIOMETRIC: "Biometric"
    SUB_CATEGORY_DEVICE_ISSUES_CAMERA: "Camera"
    SUB_CATEGORY_DEVICE_ISSUES_LOCATION: "Location"
    SUB_CATEGORY_DEVICE_ISSUES_NOTIFICATION: "Notification"
    SUB_CATEGORY_TRANSACTION_TYPES_ATM: "ATM"
    SUB_CATEGORY_TRANSACTION_TYPES_BILL_PAYMENT: "Bill Payment"
    SUB_CATEGORY_TRANSACTION_TYPES_CARD_PAYMENT: "Card Payment"
    SUB_CATEGORY_TRANSACTION_TYPES_CASH_DEPOSIT: "Cash Deposit"
    SUB_CATEGORY_TRANSACTION_TYPES_CASH_WITHDRAWAL: "Cash Withdrawal"
    SUB_CATEGORY_TRANSACTION_TYPES_CHEQUE: "Cheque"
    SUB_CATEGORY_TRANSACTION_TYPES_FD: "FD"
    SUB_CATEGORY_TRANSACTION_TYPES_IMPS: "IMPS"
    SUB_CATEGORY_TRANSACTION_TYPES_NEFT: "NEFT"
    SUB_CATEGORY_TRANSACTION_TYPES_RTGS: "RTGS"
    SUB_CATEGORY_TRANSACTION_TYPES_UPI: "UPI"
    SUB_CATEGORY_UPI_ISSUES_ACTIVATION: "Activation"
    SUB_CATEGORY_UPI_ISSUES_DEACTIVATION: "Deactivation"
    SUB_CATEGORY_UPI_ISSUES_LIMIT: "Limit"
    SUB_CATEGORY_UPI_ISSUES_LINKING: "Linking"
    SUB_CATEGORY_UPI_ISSUES_PIN: "PIN"
    SUB_CATEGORY_UPI_ISSUES_QR: "QR"
    SUB_CATEGORY_UPI_ISSUES_TRANSACTION: "Transaction"
    SUB_CATEGORY_UPI_ISSUES_VPA: "VPA"

  OsTypeEnumToValueMapping:
    ANDROID: "Android"
    IOS: "iOS"
  ResolutionModeEnumToValueMapping:
    RESOLUTION_MODE_AUTO_RESOLUTION: "Auto Resolution"
    RESOLUTION_MODE_BULK_RESOLUTION: "Bulk Resolution"
    RESOLUTION_MODE_MANUAL_RESOLUTION: "Manual Resolution"
    RESOLUTION_MODE_WATSON_RESOLUTION: "Watson Resolution"

  SavingsAccountBalanceEnumToValueMapping:
    SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1: "Less than 1"
    SAVINGS_ACCOUNT_BALANCE_OTHER: "Other"
  MonorailRaisedEnumToValueMapping:
    MONORAIL_RAISED_YES: "Yes"
    MONORAIL_RAISED_NO: "No"
  BooleanEnumToYesNoMapping:
    TRUE: "Yes"
    FALSE: "No"

ProcessTicketJobConfig:
  MaxTicketsThresholdMap:
    ONBOARDING: 2500
    RE_ONBOARDING: 5000
    UPI_PINSET: 10
    DEBIT_CARD: 10
  JobStatsEmailParam:
    FromEmailId: "<EMAIL>"
    FromEmailName: "Process Ticket Automation"
    ReceiverMailIdList:
      ONBOARDING:
        ReceiverMailInfo1:
          EmailName: "CX Ops Slack"
          EmailId: "<EMAIL>"
        ReceiverMailInfo2:
          EmailName: "Onboarding On-call"
          EmailId: "<EMAIL>"
        ReceiverMailInfo3:
          EmailName: "CX Ops Jenkins Slack"
          EmailId: "<EMAIL>"
      RE_ONBOARDING:
        ReceiverMailInfo1:
          EmailName: "CX Ops Slack"
          EmailId: "<EMAIL>"
        ReceiverMailInfo2:
          EmailName: "Onboarding On-call"
          EmailId: "<EMAIL>"
        ReceiverMailInfo3:
          EmailName: "CX Ops Jenkins Slack"
          EmailId: "<EMAIL>"
    EmailMsg:
      ONBOARDING: "Please find troubleshooting details for Onboarding pending tickets in attachment."
      RE_ONBOARDING: "Please find troubleshooting details for Re-Onboarding pending tickets in attachment."

  NumberOfDays: 89
  IsTicketProcessorEnabled:
    RE_ONBOARDING: true

CxS3Config:
  BucketName: "epifi-prod-cx"
  DataExtractionFolderName: "data-extraction"

RiskS3Config:
  BucketName: "epifi-prod-risk"

EpifiIconS3Config:
  BucketName: "epifi-icons"

DataS3Config:
  BucketName: "epifi-raw"
  S3Prefix: "vendor/segmentation_service"
  StaticSegmentSrcFolderPath: "manual_dump/static_segment"
  StaticSegmentDestFolderPath: "static_segments"

BigQueryConfig:
  SegmentUserAttributeProject: "common-tech-434709"
  SegmentUserAttributeDataset: "data_services_internal"
  SegmentUserAttributeTable: "segmentation_user_attributes"
  SegmentMasterTableRequiredField: "actor_id"

MaxCountThresholdForFetchingBulkUserInfo: 5000

BulkUserInfoViaEmailConfig:
  MaxCountThreshold: 5000

UpiDisputeAutoUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-upi-dispute-auto-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "cx"

AuthValidation:
  MethodListForSkippingAccessControlValidation: [
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
  ]

UpdateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-update-ticket-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~10 min post that regular interval is followed hourly for 10 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 5
          TimeUnit: "Hour"
      MaxAttempts: 6
      CutOff: 5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 80    # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 1m
    Namespace: "cx"

UpdateTicketPublisher:
  QueueName: "prod-cx-update-ticket-queue"

CreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-create-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

CreateTicketPublisher:
  QueueName: "prod-cx-create-ticket-queue"

BulkTicketJobConfig:
  MaxTicketThreshold: 5000

TicketConfig:
  URL: "https://ficare.freshdesk.com/a/tickets/%d"
  SLAConfig:
    IsSLACalculationEnabledInTicketConsumer: true
    IsExpectedResolutionByFieldDeterminedUsingSLA: false
    IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk: true
  ProductCategoryFieldId: "82000138916"
  TicketFieldCacheValidityDuration: "10m"
  IsTicketEventLoggingEnabled: false
  CsatConfig:
    AllowedTicketStatusesForCsat: [
      "STATUS_RESOLVED"
    ]
    WebFormUrl: "https://fi.money/feedback/feedback-collector?token=%s"
  LatestTicketCacheValidityDuration: "6h"

LandingPageConfig:
  IsExpectedResolutionTimeFieldPopulatedInLandingPageService: true

OzonetelCallEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-vn-ozonetel-call-details-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1000
        Period: 1m
    Namespace: "cx"

TicketReconciliationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-ticket-data-reconciliation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"

FreshchatEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-vn-freshchat-action-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

NuggetEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-nugget-event-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

Tracing:
  Enable: true

CallRoutingConfig:
  IsManualRoutingEnabled: true
  CallLangPrefReleaseConfig:
    IsRestrictedReleaseEnabled: true
    # if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
    IsEnabledForPlatform:
      ANDROID: false
      IOS: false
    IsEnabledForUserGroup:
      INTERNAL: true
  BlockedAccountCallRoutingConfig:
    IsRoutingEnabled: false
  SegmentIdToPriorityMapForRoutingChannel:
    ROUTING_CHANNEL_ACCOUNT_HOLDERS:
      # salary tier users
      # We have taken a decision to move salary program users to a separate routing channel
      # hence commenting out this segment, as well as removing it from RoutingChannelToSegmentIdList
      # refer : https://epifi.slack.com/archives/C054Q2HFN6L/p1713789696483769
      # 127b7c75-93bc-4d13-a7c4-************: 1
      # infinite users
      851c5b55-8610-46de-8b98-71ed406949ec: 2
      # plus tier users
      3818ed67-1a4c-4a2f-bea2-684690c7dc81: 3
      # credit card holders
      e281888e-bccc-4123-bcc6-0e1f4d918177: 4
      # standard tier users
      d5cdc7c7-d1ad-4641-9969-a8401ed20bb9: 5
  RoutingChannelToSegmentIdList:
    ROUTING_CHANNEL_ACCOUNT_HOLDERS: [ "851c5b55-8610-46de-8b98-71ed406949ec",
                                       "3818ed67-1a4c-4a2f-bea2-684690c7dc81", "e281888e-bccc-4123-bcc6-0e1f4d918177",
                                       "d5cdc7c7-d1ad-4641-9969-a8401ed20bb9" ]
  IsPriorityRoutingEnabled: true
  IsSalaryProgramRoutingEnabled: true
  IsCreditCardRoutingEnabled: false
  QueueWaitTimeDurationThreshold: "45s"
  CallDropOffCountThreshold: 2
  HalvingFactor: 2
  PastCallRecordsLookupSize: 10
  PastCallRecordsLookupDuration: "24h"
  PreRecordedMessageConfig:
    IsHighRiskMessageEnabled: true
    IsScreenerRejectedMessageEnabled: true
  IsRecordedMessageEventEnabled: true

FeatureReleaseConfig:
  FeatureConstraints:
    DISPUTE_FOR_OFF_APP_TXN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 1
        MinIOSVersion: 1
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    SENSEFORTH_CHATBOT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 210
        MinIOSVersion: 294
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
          - 1 # INTERNAL
    FRESHDESK_MONORAIL_INTEGRATION:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
    FEATURE_CX_CALL_IVR:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CX_CALL_BLOCKER:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
    FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET:
      AppVersionConstraintConfig:
        MinAndroidVersion: 402
        MinIOSVersion: 560
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CX_CALL_RISK_IVR_FLOW:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_CX_TICKET_RESOLUTION_CSAT_COMMS:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_FRESHCHAT_CHATBOT_SDK_ENABLED:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: true
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 40
    MemoryPercentageLimit: 40
    ProfileInterval: 5s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CelestialSignalWorkflowPublisher:
  QueueName: "prod-celestial-signal-workflow-queue"

IssueResolutionFeedbackConfig:
  IsEnabled: true
  DisputeConfig:
    WaitDurationAfterFinalComms: 48h #2 days

ChatBotConfig:
  ActorIdsEnabledForForceNuggetChatbot:
    ACVmxH5hoAq5240731: true # Amitkumar
    AC27MAFtFpsr250512: true # Divyansh
    AC220117UAbqlIuXQTCpGxAWkoXr0w==: true # Chetan
    AC2BeAxG3CTT250529: true # Raghav
    AC2pAHvmDbGp240826: true # Anubhav
    AC210823TWShpD1iRx6ulgzYTvUK8Q==: true #Kunal
  NuggetNamespace: "NUGGET_FIMONEY"
  LiveChatFallbackConfig:
    ChannelId: "8d568061-f760-4c04-9103-5929b5ed0695"
  SenseforthChatInitInfo:
    WebViewURLMap:
      ANDROID: "https://epifi-cx-production-chatbot.s3.ap-south-1.amazonaws.com/chatbot/chatsdk/v1/index.html?userAgent=android"
      IOS: "https://epifi-cx-production-chatbot.s3.ap-south-1.amazonaws.com/chatbot/chatsdk/v1/index.html?userAgent=ios"
  IsReleaseEvaluationEnabled: true
  IsForceFallbackToDefaultEnabled: false
  DefaultInAppChatView: "IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK"
  MaxClientSideFailureCountAllowedForAutoRetry: 3
  MaxTimeDurationThresholdForLastSuccessfulSessionTime: 30m #ShortToken inactivity expiry duration is 1 hr.
  IsExtraLoggingEnabled: true
  NumOfTxnsToBeFetched: 5 #keeping this as five so that CP to QA/merge to master doesn't reflect in prod servers accidentally
  NumberOfATMTxnsToBeFetched: 5
  IsContextCodePassingFromClientEnabled: true
  IsFreshChatExperimentEnabled: true
  ActorIdsEnabledFreshchatIssueTreeExperiment: ["ACVmxH5hoAq5240731","AC210721BAKDIpfuSV6avTCqaNqs/w==","AC7gaZFirbTKe5T7OyItlOOA230402==","AC2203148jJ0uKVrS4GHjF+IIZ5DjA==","AC220618XNsRvqO5TyWPSIQPxAUjbA==","AC220801o0omyflETpGjSt5G9BJA0g=="]
  NuggetDeeplinkUri: "epifi://unified-support/builder?flowType=ticketing&omniTicketingFlow=true"

AccountFreezeStatusConfig:
  CreditFreezeBannerElementId: "c9dc8794-e959-4bfc-931c-df637f086b62"

SherlockFeedbackDetailsConfig:
  PageSize: 20

RiskCasePublisher:
  QueueName: "prod-risk-cases-ingestion-queue"

RiskDisputePublisher:
  QueueName: "prod-risk-dispute-upload-queue"

InternationalFundTransfer:
  DocumentsBucketName: "epifi-prod-pay-international-fund-transfer"
  EnableLRSCheckFromVendor: false

BulkAccValidationViaEmailConfig:
  FromEmailId: "<EMAIL>"
  FromEmailName: "Bulk Account Validations"
  ToEmailId: "<EMAIL>"
  ToEmailName: "Account Closure Settlements"

VendorAccountPennyDropViaEmailConfig:
  FromEmailId: "<EMAIL>"
  FromEmailName: "Vendor Account Penny Drop Verification"
  ToEmailId: "<EMAIL>"
  ToEmailName: "Vendor Account Verification"

IsRedactionEnabledForDBStates: false

DevActionHelperConfig:
  IsBulkResourceAccessibilityCheckEnabled: true

OverrideBankActions:
  MaxRequests: 5000

WatsonConfig:
  IsWatsonSystemEnabled: true
  IncidentCategoryDetailsConfig:
    PRODUCT_CATEGORY_ACCOUNTS:
      PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY:
        IsProcessingEnabled: true
    PRODUCT_CATEGORY_ONBOARDING:
      PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED:
        IsProcessingEnabled: true
    PRODUCT_CATEGORY_TRANSACTIONS:
      PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT:
        IsProcessingEnabled: true
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS:
      PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL:
        IsProcessingEnabled: true

SalaryOpsConfig:
  SalaryProgramS3BucketName: "epifi-prod-salaryprogram"

MonorailConfig:
  ServiceAccountEmail: "<EMAIL>"
  AuthScopes: ["https://www.googleapis.com/auth/userinfo.email"]
  AuthorEmail: "<EMAIL>"
  IssueURL: "https://monorail-prod-321008.el.r.appspot.com/_ah/api/monorail/v1/projects/%s/issues"
  CommentURL: "https://monorail-prod-321008.el.r.appspot.com/_ah/api/monorail/v1/projects/%s/issues/%s/comments"
  ProjectId: "fi-app"

EmployerDbConfig:
  EsHostUrl: "https://vpc-prod-search-access-bans26ocf6pstioabfospdyrxi.ap-south-1.es.amazonaws.com"

UsStocksOpsConfig:
  UseAccountActivities: true

InternationalFundsTransferConfig:
  ForexRateReportSlackChannelId: "C04RTFQCBPZ" # us-stocks-ops-alerts

AirflowConfig:
  TriggerDagUrl: "http://***********:8080/api/v1/dags/%s/dagRuns"

FreshdeskMonorailIntegrationConfig:
  # Monorail attachments for prod project (fi-app) are stored in https://drive.google.com/drive/folders/1vQhhDyGIOwYKbWZgJb8r8nOGgBTSkXIv
  GoogleDriveFolderIdForAttachments: "1vQhhDyGIOwYKbWZgJb8r8nOGgBTSkXIv"

GRPCWebServerConfig:
  JarvisInterceptorConf:
    DisableAuth: false
  HttpCorsOptions:
    AllowAll: true
  KeycloakAuth:
    TokenURL: "https://keycloak-prod.pointz.in/realms/InternalProd/protocol/openid-connect/token"
    OIDC:
      ProviderURL: "https://keycloak-prod.pointz.in/realms/InternalProd"
      GoOIDC:
        ClientID: "jarvis-prod"
        SkipClientIDCheck: true

SherlockUserRequestsConfig:
  AccountTypeEnumToString:
    ACCOUNT_TYPE_SAVINGS: "Federal Savings"
  DateTimePickerConfig:
    MinValue:
      Year: 2019
      Month: 01
      Day: 01
    DateFormat: "dd MMMM, yyyy"
    IsTimeAllowed: false
    IsDisabled: false

SalaryProgramLeadManagementConfig:
  SalaryProgramS3BucketName: "epifi-salaryprogram"
  SalaryProgramB2BS3BucketName: "epifi-prod-salaryprogram-b2b"
  LeadDetailsExcelSheetPathB2B: "LeadDetails.xlsx"

SherlockBannersConfig:
  IsServiceEnabledForDynamicFetching:
    CX_SERVICE: true
    PRE_APPROVED_LOAN_SERVICE: true
    RISK_SERVICE: true
    TIERING_SERVICE: true
  PriorityOrder:
    # in decreasing order of priority. eg: A banner belonging to RISK_SERVICE gets higher priority than compared to CX_SERVICE
    ServiceName: ["PRE_APPROVED_LOAN_SERVICE", "TIERING_SERVICE", "RISK_SERVICE", "CX_SERVICE"]

RewardsOrderUpdateEventQueuePublisher:
  QueueName : "prod-rewards-order-update-queue"

RewardsCreditCardTxnEventQueuePublisher:
  QueueName: "prod-rewards-credit-card-txn-event-queue"

CallRoutingEventPublisher:
  TopicName: "prod-cx-call-routing-event-topic"

TicketUpdateEventPublisher:
  TopicName: "prod-cx-ticket-update-event-topic"

CreateTicketEventPublisher:
  TopicName: "prod-cx-ticket-create-event-topic"

FederalEscalationCreateEventPublisher:
  QueueName: "prod-cx-escalation-creation-queue"

FederalEscalationUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-escalation-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FederalEscalationCreationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-escalation-creation-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

CasperItcDownloadFileQueuePublisher:
  QueueName: "prod-casper-itc-download-file-queue"

OrderUpdateEventForTxnCategorizationPublisher:
  QueueName: "prod-categorizer-update-order-queue"

AATxnCategorizationPublisher:
  QueueName: "prod-categorizer-aa-txn-queue"

CCTxnCategorizationPublisher:
  QueueName: "prod-categorizer-cc-transaction-event-queue"


RudderEventKafkaConsumerGroup:
  StartOnServerStart: true
  GroupID: "cx_activity_prod"
  Brokers:
    - "kafka-4.data-prod.epifi.in:9094"
    - "kafka-5.data-prod.epifi.in:9094"
    - "kafka-6.data-prod.epifi.in:9094"
  Topic: "prod.events.tech"
  RateLimitConfig:
    RedisOptions:
      IsSecureRedis: true
      Options:
        Addr: "redis-12395.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12395"
      AuthDetails:
        SecretPath: "prod/redis/cx/prefixaccess"
        Environment: "prod"
        Region: "ap-south-1"
    ResourceMap:
      consumer_group:
        Rate: 100
        Period: 1s
    Namespace: "cx"

ErrorActivityConfig:
  IsPipingErrorEventToWatsonEnabled: true
  DefaultIncidentCreationCoolOffPeriod: "24h"

StageWiseCommsConfig:
  IsGenericCommsEnabled: true
  IsIssueConfigSpecificCommsEnabled: true
  IsPublishingManualTicketCreationEventEnabled: true
  IsPublishingManualTicketUpdateEventEnabled: true

S3EventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cx-s3-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

S3EventConsumerConfig:
  BucketName: "epifi-data-services"
  CallSummarizationFilePath: "cx/ticket-categorization/call_categorization/"
  IsCallSummarizationProcessingEnabled: true

StrapiConfig:
  BaseURL: "https://strapi.epifi.in/api/"
  HttpClientConfig:
    Transport:
      DialContext:
        Timeout: 30s
        KeepAlive: 30s
      TLSHandshakeTimeout: 10s
      MaxIdleConns: 100
      IdleConnTimeout: 90s
    Timeout: 10s

RiskOutcallFormRolloutConfig:
  MaxFormsPerUser: 1
  WhitelistedReviewTypes: ["REVIEW_TYPE_TRANSACTION_REVIEW"]
  WhitelistedQuestionnaireTemplates: ["QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_AMOUNT", "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT",
  "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT_AND_AMOUNT", "QUESTIONNAIRE_TEMPLATE_STOP_BUSINESS_ACTIVITY_ACK",
  "QUESTIONNAIRE_TEMPLATE_INCOME_DISCREPANCY"]

CallConfig:
  AbandonedCallConfig:
    IsAbandonedCallCommsEnabled: false
    NotificationTemplate:
      Title: "Looks like you tried to reach us 📲"
      Body: "Sorry, we missed it! We can still help — tap this to chat with us instantly."
  IsCallBlockerEnabled: true
  CallBlockerConfig:
    IsCallBlockerEnabled: true
    IsCallBlockingEnabledViaIvrFlow: true
    CallDropOffNotificationTitle: "%s, did you try to call us?"
    CallDropOffNotificationBody: "You can chat with us 24x7. For faster resolutions, tap here to chat with us."
    ContactUsFlowSmsLink: "https://fi.onelink.me/FiMony/rpb03lqc"
    FiAppDownloadLink: "https://fi.onelink.me/FiMony/rpb03lqc"

CaseManagementActorActivities:
  IsEnabled: true
  Timeout: 3s
  AllowedReviewTypes: ["REVIEW_TYPE_LEA_COMPLAINT_REVIEW"]

RiskConfig:
  EnableBackendDrivenCharts: true

WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail:
  AllowedAccessLevels: ["SALARY_WHITELIST_B2B","SALARY_DATA_OPS","DATA_RETRIEVER","SALARY_ADMIN"]

RiskTxnReviewRolloutConfig:
  IsSelectedOrderRpcEnabledForAll: true

CXFreshdeskTicketBaseURL: "https://ficare.freshdesk.com/a/tickets/%s"

WatchlistReasons:
  - "WATCHLIST_REASON_RISKY_AFU_REVIEW"
  - "WATCHLIST_REASON_RISKY_TM_REVIEW"
  - "WATCHLIST_REASON_UNFROZEN_BUSINESS_ACTIVITY"
  - "WATCHLIST_REASON_INACTIVE_ACCOUNT"
  - "WATCHLIST_REASON_FAILED_TRX_VERIFICATION"
  - "WATCHLIST_REASON_LEA_WITH_NOC"

S3BucketNameForFileGenerator:
  CamsS3Bucket: "epifi-prod-mutualfund"
  KarvyS3Bucket: "epifi-prod-mutualfund-karvy"

EnableBalanceMetricsOnCaseManagement: true
EnableOutCallDataInCaseManagementForRiskOps: true

FederalEscalationConfig:
  FederalEscalationAttachmentBucketName: "epifi-prod-cx-ticket-attachments"
  QueueId: "***************"

RiskFennelConfig:
  APIVersion: 3

LienConfig:
  LienAmount:
    CurrencyCode: "INR"
    Units: 1000000
  LienDurationInHours: 48
  AllowedReasons:
  LienRolloutPercentage: 0

IsNewOperationalStatusAPIEnabled: false

IssueConfigServiceConfig:
  IsCacheEnabled: true
  UseNewCategoryMappingForLLMScreen: true
  IssueCategoryCreatedFromTime: "2025-06-15T00:00:00Z"
  IssueCategoryCreatedToTime: "2026-06-15T00:00:00Z"

EsConfig:
  RoleArn: "arn:aws:iam::************:role/es-auth-readonly"
  ESEndpoint: "https://prod-logs.pointz.in/"
