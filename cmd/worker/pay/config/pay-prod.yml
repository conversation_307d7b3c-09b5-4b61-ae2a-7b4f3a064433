Application:
  Environment: "prod"
  Namespace: "prod-pay"
  TaskQueue: "prod-pay-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: pay-worker

OffAppUpiApplication:
  Environment: "prod"
  Namespace: "prod-pay"
  TaskQueue: "prod-pay-off-app-upi-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: pay-worker

BillpayApplication:
  Environment: "prod"
  Namespace: "prod-pay"
  TaskQueue: "prod-pay-billpay-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: pay-worker

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  LIQUILOANS_PL:
    DbType: "CRDB"
    AppName: "pay-worker"
    StatementTimeout: 1s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  EPIFI_TECH_V2:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  STOCK_GUARDIAN_TSP:
    DBType: "CRDB"
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 200ms
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  LIQUILOANS_PL:
    DbType: "CRDB"
    AppName: "pay-worker"
    StatementTimeout: 1s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  EPIFI_TECH_V2:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
  STOCK_GUARDIAN_TSP:
    DBType: "CRDB"
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 200ms
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 30
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

BillpayPGDB:
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  AppName: "pay-worker"
  StatementTimeout: 5m
  Name: "billpay"
  EnableDebug: false
  SSLMode: "verify-full"
  SecretName: "prod/rds/prod-plutus/billpay_dev_user"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  GormV2:
    LogLevelGormV2: "SILENT"
    UseInsecureLog: false

OrderUpdatePublisher:
  TopicName: "prod-order-update-topic"

TxnDetailedStatusUpdatePublisher:
   TopicName: "prod-txn-detailed-status-update-topic"

WorkflowUpdatePublisher:
  TopicName: "prod-celestial-workflow-update-topic"

WorkflowParamsList:
  - WorkflowName: "RechargePayment"
    ActivityParamsList:
      - ActivityName: "UpdateRechargeOrderStatus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "CreateRechargeOrderStage"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "UpdateRechargeOrderStage"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "GetRechargeOrderDetails"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "FetchRechargePaymentInitiationDetails"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "EnquireRechargePaymentStatus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "4s"
                MaxAttempts: 5
            RetryStrategy2:
              # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
              # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "10m"
                BackoffCoefficient: 2.0
                MaxAttempts: 14
            MaxAttempts: 14
            CutOff: 5
      - ActivityName: "ShouldInitiateRechargeFulfilment"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "InitiateRechargeWithVendor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "EnquireRechargeStatusWithVendor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "4s"
                MaxAttempts: 5
            RetryStrategy2:
              # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
              # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "10m"
                BackoffCoefficient: 2.0
                MaxAttempts: 14
            MaxAttempts: 14
            CutOff: 5
      - ActivityName: "ShouldInitiateRechargeRefundOrder"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "InitiateRechargeRefundOrder"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "EnquireRechargeRefundOrderStatus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "4s"
                MaxAttempts: 5
            RetryStrategy2:
              # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
              # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "10m"
                BackoffCoefficient: 2.0
                MaxAttempts: 14
            MaxAttempts: 14
            CutOff: 5
  - WorkflowName: "B2CFundTransfer"
    ActivityParamsList:
      - ActivityName: "ProcessB2CPayment"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
  - WorkflowName: "ExecutionReportGenerator"
    ActivityParamsList:
      - ActivityName: "GetWorkflowCount"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
  - WorkflowName: "InternationalFundTransfer"
    ActivityParamsList:
      - ActivityName: "ProcessFundTransfer"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "30s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      - ActivityName: "GenerateA2Form"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "GenerateSOFDocument"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "UploadFileToS3"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "SendInternationalFundTransferStatus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "GetOrderAndTransactionDetailsForTxn"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "UploadSOFDocument"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # TODO(Brijesh): Understand how long it takes to generate a statement
      - ActivityName: "GetStatement"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      # Updates orders and transactions
      - ActivityName: "ProcessRefundNotification"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Updates orders and transactions
      - ActivityName: "CreateIFTSecondLegTransaction"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Updates forex rate consumption
      - ActivityName: "RevertForexRateAmountInUse"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Registers LRS limit inquiry
      - ActivityName: "InitiateLRSCheck"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Reports the partner bank about tax collected at the source by Fi on behalf of the bank
      - ActivityName: "ReportTcsCharges"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "GetRemittanceEligibilityBySof"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for ~17 mins with max cap between retries at 10min
          # Retry interval - 2s 4s 8s 16s 32s ... 10 attempts
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
      # report gst with partner bank
      - ActivityName: "ReportGstCollectedFromUser"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "GenerateGstReportingVendorRequestId"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "GetGstCollectedFromUserStatus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
  - WorkflowName: "FundTransfer"
    ActivityParamsList:
      - ActivityName: "ProcessFundTransfer"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
  - WorkflowName: "FundTransferV1"
    ActivityParamsList:
      - ActivityName: "ProcessFundTransfer"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      - ActivityName: "GetFundTransferNotificationTemplate"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 42m40s 1h25m20s 2h50m40s 3h 3h 3h 3h 3h 3h 3h
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      - ActivityName: "GetTxnStatus"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
  - WorkflowName: "GenerateSwiftReports"
    ActivityParamsList:
      - ActivityName: "VerifyWFStatusUpdate"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Generates file for a batch of international outward fund transfers
      - ActivityName: "GenerateMt199MessageAttachment"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Generates file for a batch of international outward fund transfers
      - ActivityName: "GenerateLRSFile"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Generates file for a batch of international outward fund transfers
      - ActivityName: "GenerateTTMFile"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Generates file for a batch of international outward fund transfers
      - ActivityName: "GenerateGSTFile"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # Generates file for a batch of international outward fund transfers
      - ActivityName: "GenerateTCSFile"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      # since account statement take some time to reflect transactions
      - ActivityName: "GetOutwardBulkTransactionInfo"
        ScheduleToCloseTimeout: "168h"
        StartToCloseTimeout: "5m"
        RetryParams:
        # Exponential retry strategy that runs for 7 daya  with max cap between retries at 6hr
          ExponentialBackOff:
            BaseInterval: "30m"
            MaxInterval: "6h"
            BackoffCoefficient: 2.0
            MaxAttempts: 35
  - WorkflowName: "MonitorForexRate"
    ActivityParamsList:
      - ActivityName: "GetForexRateExpiryWaitTime"
        # since there is no db or network call, keeping the timeout and retries very low
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "5s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 30
      - ActivityName: "UpdateForexRate"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "SendForexRateReport"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "PublishForexRateMetricToPrometheus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "GetForexRate"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
  - WorkflowName: "OffAppUpiV2Flow"
    ActivityParamsList:
      - ActivityName: "AcquireStatelessLock"
        ScheduleToCloseTimeout: "12m"
        StartToCloseTimeout: "10s"
        # retry interval should exceed the lease duration so that the lock can be acquired (again OR by a new client)
        RetryParams:
          RegularInterval:
            Interval: "4s"
            MaxAttempts: 40
      - ActivityName: "ReleaseStatelessLock"
        ScheduleToCloseTimeout: "4m"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "3s"
            MaxAttempts: 10
      - ActivityName: "EnquireUpiTxnStatus"
        ScheduleToCloseTimeout: "40s"
        StartToCloseTimeout: "11s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 2
      - ActivityName: "EnquireUpiTxnStatusLastAttempt"
        ScheduleToCloseTimeout: "30s"
        StartToCloseTimeout: "11s"
        RetryParams:
          RegularInterval:
            Interval: "3s"
            MaxAttempts: 2
      - ActivityName: "DedupeTxn"
        # we can't proceed without this activity succeeding. Thus, keeping the retries on the higher side.
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "20s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "4s"
                MaxAttempts: 20
            RetryStrategy2:
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "5m"
                BackoffCoefficient: 2.0
                MaxAttempts: 40
            MaxAttempts: 60
            CutOff: 20
      - ActivityName: "RecordOffAppPayment"
        ScheduleToCloseTimeout: "6m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "4s"
            MaxAttempts: 5
      - ActivityName: "RecordOffAppPaymentLastAttempt"
        # we can't proceed without this activity succeeding. Thus, keeping the retries on the higher side.
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "30s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "4s"
                MaxAttempts: 20
            RetryStrategy2:
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "5m"
                BackoffCoefficient: 2.0
                MaxAttempts: 40
            MaxAttempts: 60
            CutOff: 20
      - ActivityName: "TriggerRemitterBackfill"
        ScheduleToCloseTimeout: "25h"
        StartToCloseTimeout: "65s"
        RetryParams:
          RegularInterval:
            Interval: "15m"
            MaxAttempts: 90
      - ActivityName: "EnquireAndUpdateUpiTxn"
        ScheduleToCloseTimeout: "450h" # 15 days + buffer
        StartToCloseTimeout: "65s"
        RetryParams:
          # attempts to add up to 15 days
          Hybrid:
            RetryStrategy1:
              # 1m, 1m, 1m, 1m, 1m
              RegularInterval:
                Interval: "1m"
                MaxAttempts: 5
            RetryStrategy2:
              # 1m, 2m, 4m, 8m, 16m, 32m, 64m, 128m, 3h, 3h, ... till 15 days.
              ExponentialBackOff:
                BaseInterval: "1m"
                MaxInterval: "3h"
                BackoffCoefficient: 2.0
                MaxAttempts: 125
            MaxAttempts: 130
            CutOff: 5
      - ActivityName: "ResolveOtherActorEntities"
        ScheduleToCloseTimeout: "17m" # 15m + buffer
        StartToCloseTimeout: "30s"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 64s, 128s, 3m, 3m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "3m"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
      - ActivityName: "UpdateOrderAndTxn"
        ScheduleToCloseTimeout: "30h" # 24h + buffer
        StartToCloseTimeout: "60s"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 64s, 128s, 256s, 5m, 5m, ... till 24h
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 295
      - ActivityName: "PublishOrderUpdateEventWithTxn"
        ScheduleToCloseTimeout: "30h" # 24h + buffer
        StartToCloseTimeout: "60s"
        RetryParams:
          # 1s, 2s, 4s, 8s, 16s, 32s, 64s, 128s, 256s, 5m, 5m, ... till 24h
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 295
  - WorkflowName: "UpdateLrsLimitsWorkflow"
    ActivityParamsList:
      - ActivityName: "UpdateLRSLimits"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "30s"
        HeartbeatTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
  - WorkflowName: "CheckAndUpdatePgFundTransferStatus"
    ActivityParamsList:
      - ActivityName: "GetPgFundTransferOrderDetails"
        HeartbeatTimeout: "10s"
        # TODO(Sundeep): Revisit the aggressive retry strategy once we start integrating webhooks.
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "20s"
        RetryParams:
          Hybrid:
            # A combination of both linear and exponential retry strategy. For the first 6 mins we poll every 10s
            # since there is a high chance of the payment being complete within that interval, and the user
            # is unblocked quickly. If the payment is still not complete then we fall back to exponential re-tries
            # for 3 days with max cap between retries at 30 min
            RetryStrategy1:
              RegularInterval:
                Interval: "10s"
                MaxAttempts: 36
            RetryStrategy2:
              # First exponential retry interval 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 21m20s 60m 60m 60m 60m 60m 60m
              ExponentialBackOff:
                BaseInterval: "10s"
                MaxInterval: "1h"
                BackoffCoefficient: 2.0
                MaxAttempts: 150
            MaxAttempts: 186
            CutOff: 36
      - ActivityName: "UpdateOrderAndTxnForPG"
        HeartbeatTimeout: "10s"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "20s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
  - WorkflowName: "ProcessRecurringOutwardRemittance"
    ActivityParamsList:
      - ActivityName: "UpdateForexDealUsage"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20m"
            BackoffCoefficient: 2.0
            MaxAttempts: 15
      - ActivityName: "ValidateLRSLimit"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20m"
            BackoffCoefficient: 2.0
            MaxAttempts: 15
      - ActivityName: "ExecuteRecurringPoolTransfer"
        ScheduleToCloseTimeout: "6h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20m"
            BackoffCoefficient: 2.0
            MaxAttempts: 30
      - ActivityName: "InitiateIFTWorkflow"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20m"
            BackoffCoefficient: 2.0
            MaxAttempts: 15
  - WorkflowName: "ReconcilePgPayments"
    ActivityParamsList:
      # TODO(Sundeep): Revisit the config based on how long the activities run on prod.
      - ActivityName: "FetchPgReconPaymentsFromVendor"
        ScheduleToCloseTimeout: "6h"
        StartToCloseTimeout: "1m"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "ReconcilePgPaymentEntities"
        ScheduleToCloseTimeout: "6h"
        StartToCloseTimeout: "10m"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
  - WorkflowName: "InitiatePgPaymentsRecon"
    ChildWorkflowParamsList:
      # TODO(Sundeep): Revisit the config based on how long the workflows run on prod.
      # It doesn't make sense to keep WorkflowExecutionTimeout to be more than 24h, since recon is run on a daily basis,
      # so need to revisit the values based on how long it takes to run the recon workflows.
      - WorkflowName: "ReconcilePgPayments"
        WorkflowExecutionTimeout: "31h"
        WorkflowRunTimeout: "9h"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 3

ExecutionReportGenerationParams:
  ReportStalenessDuration: "5s"

Secrets:
  Ids:
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    IFTReportsSlackBotOauthToken: "prod/ift/slack-bot-oauth-token"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: true
  PyroscopeProfiling:
    EnablePyroscope: false

InternationalFundTransfer:
  EnableFederalSherlock: false
  SherlockHost: "https://federal.epifi.in"
  FederalSherlockHost: "https://federal-sherlock.epifi.in"
  DocumentsBucketName: "epifi-prod-pay-international-fund-transfer"
  ForexRateReportSlackChannelId: "C04RTFQCBPZ" # us-stocks-ops-alerts
  ReportTcsChargesFromApi: true
  IsSofAnalysisFlowActive: true

IsGSTReportedWithNewInvoiceNumber: true

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "48h" # 2 days
    # CacheConfig for OrderCache used in GetById for storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2h"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
      ClientName: order

PayOrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "48h" # 2 days
    # CacheConfig for OrderCache used in GetById fpr storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2h"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: pay

LockRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "redis-10653.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10653"
  AuthDetails:
    SecretPath: "prod/redis/common/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: pay

PayTransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: true
        CacheTTL: "2h"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "redis-15404.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15404"
    AuthDetails:
      SecretPath: "prod/redis/pay/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: pay

PaymentEnquiryParams:
  InProgressToSuccessMap:
    "FEDERAL_BANK":
      PaymentProtocolToDeemedEnquiryDurationMap:
        UPI:
          # NPCI has started returning U48 within 110h of enquiry. So, we can consider the DEEMED payment as success after 105h.
          P2P: 105h
          P2M: 105h
      OffAppPaymentProtocolToDeadlineExceededDuration:
        UPI: 120h
      OffAppPaymentProtocolToNotFoundDuration:
        UPI: 130h

EnableEntitySegregation: true

PgProgramToAuthSecretMap:
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "prod/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-stockguardian_MbZRPgHhafW":
    AuthParam: "prod/vendorgateway/razorpay-stock-guardian-loans-api-key"

# todo[obed]: update to the correct actor and pi ids once they are created
BillpayParams:
  RechargePoolAccountActorId: "actor-recharges-federal-pool-account"
  RechargePoolAccountPiId: "paymentinstrument-recharges-federal-pool-account"
