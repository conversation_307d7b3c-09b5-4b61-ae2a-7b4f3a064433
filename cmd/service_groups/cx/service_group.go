package cx

import (
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/cx/data_collector/savings"
	cxvkyccallpb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	esPb "github.com/epifi/gamma/api/cx/escalations"
	issuePb "github.com/epifi/gamma/api/cx/issue_category"
	"github.com/epifi/gamma/api/cx/nudge_parser"
	cxSgKycPb "github.com/epifi/gamma/api/cx/stockguardian/kyc"

	callIvrPb "github.com/epifi/gamma/api/cx/call_ivr"
	"github.com/epifi/gamma/api/cx/sherlock_sop"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/servergen/meta"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	issueTrackerConsumer "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
	alfredCxPb "github.com/epifi/gamma/api/cx/data_collector/alfred"
	"github.com/epifi/gamma/api/cx/data_collector/investment/usstocks"
	cxSalaryProgramSalaryb2bPb "github.com/epifi/gamma/api/cx/data_collector/salaryprogram/salaryb2b"
	sherlockAAPb "github.com/epifi/gamma/api/cx/data_collector/sherlock_actor_activity"
	tieringPb "github.com/epifi/gamma/api/cx/data_collector/tiering"
	userReqPb "github.com/epifi/gamma/api/cx/data_collector/user_requests"
	watsonInfoProviderPb "github.com/epifi/gamma/api/cx/error_activity/watson_info_provider"
	cxQuestionResponseSubscriptionPb "github.com/epifi/gamma/api/cx/inapphelp_feedback_engine_clients/question_response_subscription"
	icPb "github.com/epifi/gamma/api/cx/issue_config"
	cxLvPb "github.com/epifi/gamma/api/cx/liveness_video"
	stageWiseCommsPb "github.com/epifi/gamma/api/cx/manual_ticket_stage_wise_comms"
	sherlockRiskChartPb "github.com/epifi/gamma/api/cx/risk_ops/chart"
	sprinklrPb "github.com/epifi/gamma/api/cx/sprinklr"
	ticketpb "github.com/epifi/gamma/api/cx/ticket"
	ticketConsumerPb "github.com/epifi/gamma/api/cx/ticket/consumer"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	watsonConsumer "github.com/epifi/gamma/api/cx/watson/consumer"
	mockWatsonClient "github.com/epifi/gamma/api/cx/watson/mock_client"
	"github.com/epifi/gamma/cx/dispute"

	adminactionspb "github.com/epifi/gamma/api/cx/admin_actions"
	applog "github.com/epifi/gamma/api/cx/app_log"
	alpb "github.com/epifi/gamma/api/cx/audit_log"
	callpb "github.com/epifi/gamma/api/cx/call"
	consumer "github.com/epifi/gamma/api/cx/call/consumer"
	callroutingpb "github.com/epifi/gamma/api/cx/call_routing"
	chatpb "github.com/epifi/gamma/api/cx/chat"
	livechatfallback "github.com/epifi/gamma/api/cx/chat/bot/livechatfallback"
	chatbotworkflowpb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	consumer2 "github.com/epifi/gamma/api/cx/chat/consumer"
	cxconnectedaccountpb "github.com/epifi/gamma/api/cx/connected_account"
	consumer3 "github.com/epifi/gamma/api/cx/consumer"
	cxcapb "github.com/epifi/gamma/api/cx/customer_auth"
	cxaccountpb "github.com/epifi/gamma/api/cx/data_collector/account"
	cxcardpb "github.com/epifi/gamma/api/cx/data_collector/card"
	communications "github.com/epifi/gamma/api/cx/data_collector/communications"
	cxccpb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	cxfitttpb "github.com/epifi/gamma/api/cx/data_collector/fittt"
	mutualfund "github.com/epifi/gamma/api/cx/data_collector/investment/mutualfund"
	kyc "github.com/epifi/gamma/api/cx/data_collector/kyc"
	obpb "github.com/epifi/gamma/api/cx/data_collector/onboarding"
	cxp2pinvpb "github.com/epifi/gamma/api/cx/data_collector/p2pinvestment"
	cxinternationalfiletransferdatacollectorpb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	paymentinstruments "github.com/epifi/gamma/api/cx/data_collector/payment_instruments"
	cxpreapprovedloanpb "github.com/epifi/gamma/api/cx/data_collector/preapprovedloan"
	profilepb "github.com/epifi/gamma/api/cx/data_collector/profile"
	referrals "github.com/epifi/gamma/api/cx/data_collector/referrals"
	repb "github.com/epifi/gamma/api/cx/data_collector/rewards"
	riskopswealthpb "github.com/epifi/gamma/api/cx/data_collector/risk_ops_wealth"
	salarydataops "github.com/epifi/gamma/api/cx/data_collector/salarydataops"
	transaction "github.com/epifi/gamma/api/cx/data_collector/transaction"
	wealthonboarding "github.com/epifi/gamma/api/cx/data_collector/wealth_onboarding"
	developer "github.com/epifi/gamma/api/cx/developer"
	actionpb "github.com/epifi/gamma/api/cx/developer/actions"
	consumer4 "github.com/epifi/gamma/api/cx/developer/actions/consumer"
	dbstatepb "github.com/epifi/gamma/api/cx/developer/db_state"
	ticketsummary "github.com/epifi/gamma/api/cx/developer/ticket_summary"
	dipb "github.com/epifi/gamma/api/cx/dispute"
	consumer5 "github.com/epifi/gamma/api/cx/dispute/consumer"
	job "github.com/epifi/gamma/api/cx/dispute/job"
	cxfederalpb "github.com/epifi/gamma/api/cx/federal"
	cxdevconsolepb "github.com/epifi/gamma/api/cx/fittt/devconsole"
	issueresolutionfeedbackpb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	lppb "github.com/epifi/gamma/api/cx/landing_page"
	payoutpb "github.com/epifi/gamma/api/cx/payout"
	consumer6 "github.com/epifi/gamma/api/cx/payout/consumer"
	riskopspb "github.com/epifi/gamma/api/cx/risk_ops"
	sbpb "github.com/epifi/gamma/api/cx/sherlock_banners"
	sbpf "github.com/epifi/gamma/api/cx/sherlock_feedback"
	sherlockScriptsPb "github.com/epifi/gamma/api/cx/sherlock_scripts"
	sherlockuserpb "github.com/epifi/gamma/api/cx/sherlock_user"
	userissueinfopb "github.com/epifi/gamma/api/cx/user_issue_info"
	wire "github.com/epifi/gamma/cx/wire"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     wire.InitializeSherlockUserService,
		GRPCRegisterMethods: []any{sherlockuserpb.RegisterSherlockUserServiceServer},
	},
	{
		WireInitializer:     wire.InitializeProfileService,
		GRPCRegisterMethods: []any{profilepb.RegisterCustomerProfileServer},
	},
	{
		WireInitializer:     wire.InitializeLandingPageService,
		GRPCRegisterMethods: []any{lppb.RegisterLandingPageServer},
	},
	{
		WireInitializer:     wire.InitializeAccountService,
		GRPCRegisterMethods: []any{cxaccountpb.RegisterAccountServer},
	},
	{
		WireInitializer:     wire.IntializeCustomerCardService,
		GRPCRegisterMethods: []any{cxcardpb.RegisterCardsServer},
	},
	{
		WireInitializer:     wire.InitializeCustomerAuth,
		GRPCRegisterMethods: []any{cxcapb.RegisterCustomerAuthenticationServer},
	},
	{
		WireInitializer:     wire.InitializeAuthCallbackService,
		GRPCRegisterMethods: []any{cxcapb.RegisterCustomerAuthCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeChatInitInformationService,
		GRPCRegisterMethods: []any{chatpb.RegisterChatsServer},
	},
	{
		WireInitializer:     wire.InitializeAuditLogService,
		GRPCRegisterMethods: []any{alpb.RegisterAuditLogsServer},
	},
	{
		WireInitializer:     wire.InitializeKycService,
		GRPCRegisterMethods: []any{kyc.RegisterCustomerKYCServer},
	},
	{
		WireInitializer:     wire.InitializeFitttService,
		GRPCRegisterMethods: []any{cxfitttpb.RegisterFitttServer},
	},
	{
		WireInitializer:     wire.InitializeTransactionService,
		GRPCRegisterMethods: []any{transaction.RegisterCustomerTransactionsServer},
	},
	{
		WireInitializer:     wire.InitializePiService,
		GRPCRegisterMethods: []any{paymentinstruments.RegisterCustomerPIServer},
	},
	{
		WireInitializer:     wire.InitializeRewardsService,
		GRPCRegisterMethods: []any{repb.RegisterRewardsServer},
	},
	{
		WireInitializer:     wire.InitializeDisputeService,
		GRPCRegisterMethods: []any{dipb.RegisterDisputeServer},
	},
	{
		WireInitializer:     wire.InitializeDisputeJobService,
		GRPCRegisterMethods: []any{job.RegisterDisputeProcessingJobServer},
	},
	{
		WireInitializer:     wire.InitializeOnboardingService,
		GRPCRegisterMethods: []any{obpb.RegisterOnboardingServer},
	},
	{
		WireInitializer:     wire.InitializeWealthOnboardingService,
		GRPCRegisterMethods: []any{wealthonboarding.RegisterWealthOnboardingServer},
	},
	{
		WireInitializer:     wire.InitializeInvestmentService,
		GRPCRegisterMethods: []any{mutualfund.RegisterInvestmentServer},
	},
	{
		WireInitializer:     wire.InitializeCommsService,
		GRPCRegisterMethods: []any{communications.RegisterCustomerCommunicationServer},
	},
	{
		WireInitializer:     wire.InitializeAppLogService,
		GRPCRegisterMethods: []any{applog.RegisterAppLogServer},
	},
	{
		WireInitializer:     wire.InitializeTicketSummaryService,
		GRPCRegisterMethods: []any{ticketsummary.RegisterTicketSummaryServer},
	},
	{
		WireInitializer:     wire.InitializeDbStatesService,
		GRPCRegisterMethods: []any{dbstatepb.RegisterDBStateServer},
	},
	{
		WireInitializer:     wire.InitializePayoutService,
		GRPCRegisterMethods: []any{payoutpb.RegisterPayoutServer},
	},
	{
		WireInitializer:     wire.InitializeDevActionsService,
		GRPCRegisterMethods: []any{actionpb.RegisterDevActionsServer},
	},
	{
		WireInitializer:     wire.InitializeLivenessVideoService,
		GRPCRegisterMethods: []any{cxLvPb.RegisterLivenessVideoServer},
	},
	{
		WireInitializer:     wire.InitializeTicketService,
		GRPCRegisterMethods: []any{ticketpb.RegisterTicketServer},
	},
	{
		WireInitializer:     wire.InitializeCxDevService,
		GRPCRegisterMethods: []any{developer.RegisterDevCXServer},
	},
	{
		WireInitializer:     wire.InitializeFederalService,
		GRPCRegisterMethods: []any{cxfederalpb.RegisterFederalServer},
	},
	{
		WireInitializer:     wire.InitializeAdminActionService,
		GRPCRegisterMethods: []any{adminactionspb.RegisterAdminActonsServer},
	},
	{
		WireInitializer:     wire.InitializeReferralService,
		GRPCRegisterMethods: []any{referrals.RegisterReferralsServer},
	},
	{
		WireInitializer:     wire.InitializeFITDevConsoleService,
		GRPCRegisterMethods: []any{cxdevconsolepb.RegisterCxFitDeveloperConsoleServiceServer},
	},
	{
		WireInitializer:     wire.InitializeCallRoutingService,
		GRPCRegisterMethods: []any{callroutingpb.RegisterCallRoutingServer},
	},
	{
		WireInitializer:     wire.InitializeRiskOpsService,
		GRPCRegisterMethods: []any{riskopspb.RegisterRiskOpsServer},
	},
	{
		WireInitializer:     wire.InitializeRiskOpsWealthService,
		GRPCRegisterMethods: []any{riskopswealthpb.RegisterRiskOpsWealthServer},
	},
	{
		WireInitializer:     wire.InitializeSalaryDataOpsService,
		GRPCRegisterMethods: []any{salarydataops.RegisterSalaryDataOpsServer},
	},
	{
		WireInitializer:     wire.InitializeConnectedAccountService,
		GRPCRegisterMethods: []any{cxconnectedaccountpb.RegisterConnectedAccountServer},
	},
	{
		WireInitializer:     wire.InitializeCallService,
		GRPCRegisterMethods: []any{callpb.RegisterCallServer},
	},
	{
		WireInitializer:     wire.InitializeP2PInvestmentService,
		GRPCRegisterMethods: []any{cxp2pinvpb.RegisterP2PInvestmentServer},
	},
	{
		WireInitializer:     wire.InitializePreApprovedLoanService,
		GRPCRegisterMethods: []any{cxpreapprovedloanpb.RegisterPreApprovedLoanServer},
	},
	{
		WireInitializer:     wire.InitializeFireflyService,
		GRPCRegisterMethods: []any{cxccpb.RegisterFireflyServer},
	},
	{
		WireInitializer:     wire.InitializeSherlockBannersService,
		GRPCRegisterMethods: []any{sbpb.RegisterSherlockBannersServer},
	},
	{
		WireInitializer:     wire.InitializeSherlockFeedbackDetailsService,
		GRPCRegisterMethods: []any{sbpf.RegisterSherlockFeedbackServer},
	},
	{
		WireInitializer:     wire.InitializeIssueResolutionFeedbackService,
		GRPCRegisterMethods: []any{issueresolutionfeedbackpb.RegisterIssueResolutionFeedbackServiceServer},
	},
	{
		WireInitializer:     wire.InitializeLiveChatFallbackService,
		GRPCRegisterMethods: []any{livechatfallback.RegisterLiveChatFallbackServer},
	},
	{
		WireInitializer:     wire.InitializeChatbotWorkflowService,
		GRPCRegisterMethods: []any{chatbotworkflowpb.RegisterWorkflowServer},
	},
	{
		WireInitializer:     wire.InitializeUserIssueInfoService,
		GRPCRegisterMethods: []any{userissueinfopb.RegisterUserIssueInfoServiceServer},
	},
	{
		WireInitializer:     wire.NewInternationalFundTransferService,
		GRPCRegisterMethods: []any{cxinternationalfiletransferdatacollectorpb.RegisterInternationalFundTransferServer},
	},
	{
		WireInitializer:     wire.InitializeVKYCCallService,
		GRPCRegisterMethods: []any{cxvkyccallpb.RegisterVkycCallServer},
	},
	{
		WireInitializer:     wire.InitialiseSGKycService,
		GRPCRegisterMethods: []any{cxSgKycPb.RegisterKYCServer},
	},
	{
		WireInitializer:     wire.InitializeNudgeParserService,
		GRPCRegisterMethods: []any{nudge_parser.RegisterNudgeParserServer},
	},
	{
		WireInitializer: wire.InitializeOzonetelConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer.AddOrUpdateOzonetelCallDetailsMethod,
				ConfigField: "OzonetelCallEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitializeCxChatConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer2.ProcessFreshchatEventMethod,
				ConfigField: "FreshchatEventSubscriber",
			},
			{
				MethodName:  consumer2.ProcessNuggetEventMethod,
				ConfigField: "NuggetEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitializeConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer3.ProcessTicketEventMethod,
				ConfigField: "FreshdeskTicketSubscriber",
			},
			{
				MethodName:  consumer3.ProcessContactEventMethod,
				ConfigField: "FreshdeskContactSubscriber",
			},
			{
				MethodName:  consumer3.UpdateTicketEventMethod,
				ConfigField: "UpdateTicketSubscriber",
			},
			{
				MethodName:  consumer3.CreateTicketEventMethod,
				ConfigField: "CreateTicketSubscriber",
			},
			{
				MethodName:  consumer3.ProcessS3EventMethod,
				ConfigField: "S3EventSubscriber",
			},
			{
				MethodName:  consumer3.ProcessFederalEscalationEventMethod,
				ConfigField: "FederalEscalationUpdateEventSubscriber",
			},
			{
				MethodName:  consumer3.ProcessFederalEscalationCreationEventMethod,
				ConfigField: "FederalEscalationCreationEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitializeWatsonConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  watsonConsumer.ProcessReportIncidentEventMethod,
				ConfigField: "WatsonIncidentReportingSubscriber",
			},
			{
				MethodName:  watsonConsumer.ProcessResolveIncidentEventMethod,
				ConfigField: "WatsonIncidentResolutionSubscriber",
			},
			{
				MethodName:  watsonConsumer.ProcessTicketEventForWatsonMethod,
				ConfigField: "WatsonTicketEventSubscriber",
			},
			{
				MethodName:  watsonConsumer.ProcessCreateTicketEventMethod,
				ConfigField: "WatsonCreateTicketSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitializeCrmIssueTrackerIntegrationConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  issueTrackerConsumer.ProcessCrmIssueTrackerIntegrationEventMethod,
				ConfigField: "CrmIssueTrackerIntegrationSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitializeTicketConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  ticketConsumerPb.ProcessFreshdeskTicketEventMethod,
				ConfigField: "FreshdeskTicketDataEventSubscriberFifo",
			},
			{
				MethodName:  ticketConsumerPb.ProcessFreshdeskTicketEventMethod,
				ConfigField: "FreshdeskTicketDataEventSubscriber",
			},
		},
		InitCondition: func(c *config.Config) bool {
			return !cfg.IsTestTenantEnabled()
		},
	},
	{
		WireInitializer: wire.InitializeTicketConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  ticketConsumerPb.ProcessTicketReconciliationEventMethod,
				ConfigField: "TicketReconciliationEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitializeDevActionConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer4.ProcessDelayedActionMethod,
				ConfigField: "DevActionSubscriber",
			},
		},
		InitCondition: func(config *config.Config) bool {
			return !cfg.IsTestTenantEnabled()
		},
	},
	{
		WireInitializer: wire.InitializeDisputeConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer5.CreateDisputeTicketMethod,
				ConfigField: "DisputeCreateTicketSubscriber",
			},
			{
				MethodName:  consumer5.UpdateDisputeTicketMethod,
				ConfigField: "DisputeUpdateTicketSubscriber",
			},
			{
				MethodName:  consumer5.AddPrivateNoteTicketMethod,
				ConfigField: "DisputeAddNoteTicketSubscriber",
			},
			{
				MethodName:  consumer5.ProcessDisputeMethod,
				ConfigField: "DisputeSubscriber",
			},
			{
				MethodName:  consumer5.ProcessUpiDisputeAutoUpdateEventMethod,
				ConfigField: "UpiDisputeAutoUpdateEventSubscriber",
			},
		},
	},
	{
		WireInitializer: wire.InitializePayoutConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumer6.CheckPayoutStatusMethod,
				ConfigField: "PayoutStatusCheckSubscriber",
			},
		},
	},
	{
		WireInitializer:     wire.InitializeAlfredService,
		GRPCRegisterMethods: []any{alfredCxPb.RegisterAlfredServer},
	},
	{
		WireInitializer:     wire.InitializeCrmIssueTrackerIntegrationService,
		GRPCRegisterMethods: []any{citPb.RegisterCrmIssueTrackerIntegrationServer},
	},
	{
		WireInitializer:     wire.InitializeQuestionResponseSubscriptionClient,
		GRPCRegisterMethods: []any{cxQuestionResponseSubscriptionPb.RegisterFeedbackSubscriptionServiceServer},
	},
	{
		WireInitializer:     wire.InitializeWatsonClientService,
		GRPCRegisterMethods: []any{mockWatsonClient.RegisterMockWatsonClientServiceServer},
	},
	{
		WireInitializer:     wire.InitializeSalaryB2BService,
		GRPCRegisterMethods: []any{cxSalaryProgramSalaryb2bPb.RegisterSalaryB2BServer},
	},
	{
		WireInitializer:     wire.InitSherlockActorActivityService,
		GRPCRegisterMethods: []any{sherlockAAPb.RegisterSherlockActorActivityServer},
	},
	{
		WireInitializer:     wire.InitializeSprinklrEventsService,
		GRPCRegisterMethods: []any{sprinklrPb.RegisterSprinklrServer},
	},
	{
		WireInitializer:     wire.InitializeTieringService,
		GRPCRegisterMethods: []any{tieringPb.RegisterTieringServer},
	},
	{
		WireInitializer:     wire.InitializeUsStockService,
		GRPCRegisterMethods: []any{usstocks.RegisterUsStocksInvestmentServer},
	},
	{
		WireInitializer:     wire.InitializeUserRequestsService,
		GRPCRegisterMethods: []any{userReqPb.RegisterUserRequestsServer},
	},
	{
		WireInitializer:     wire.InitializeWatsonService,
		GRPCRegisterMethods: []any{watsonPb.RegisterWatsonServer},
	},
	{
		WireInitializer:     wire.InitializeWatsonInfoProvider,
		GRPCRegisterMethods: []any{watsonInfoProviderPb.RegisterWatsonInfoProviderServer},
	},
	{
		WireInitializer:     wire.InitializeManualTicketStageWiseCommsService,
		GRPCRegisterMethods: []any{stageWiseCommsPb.RegisterManualTicketStageWiseCommsServer},
	},
	{
		WireInitializer:     wire.InitializeIssueConfigService,
		GRPCRegisterMethods: []any{icPb.RegisterIssueConfigManagementServer},
	},
	{
		WireInitializer:     wire.InitializeSherlockScriptsService,
		GRPCRegisterMethods: []any{sherlockScriptsPb.RegisterSherlockScriptsServer},
	},
	{
		WireInitializer:     wire.InitializeSherlockSopService,
		GRPCRegisterMethods: []any{sherlock_sop.RegisterSherlockSopServiceServer},
	},
	{
		WireInitializer:     wire.InitializeRiskChartsService,
		GRPCRegisterMethods: []any{sherlockRiskChartPb.RegisterChartServiceServer},
	},
	{
		WireInitializer:     wire.InitializeCallIvrService,
		GRPCRegisterMethods: []any{callIvrPb.RegisterIvrServer},
	},
	{
		WireInitializer:     wire.InitializeIssueCategoryService,
		GRPCRegisterMethods: []any{issuePb.RegisterIssueCategoryServiceServer},
	},
	{
		WireInitializer:     wire.InitialiseEscalationsService,
		GRPCRegisterMethods: []any{esPb.RegisterEscalationServiceServer},
	},
	{
		WireInitializer:     wire.InitializeSavingsService,
		GRPCRegisterMethods: []any{savings.RegisterSavingsServer},
	},
}

func AfterServiceGroupInit(disputeSvc *dispute.Service) error {
	// init the dispute decision tree
	err := disputeSvc.InitDisputeDecisionTree()
	if err != nil {
		// we should panic if build decision tree fails to catch any config error before service is deployed
		logger.Panic("error while building dispute decision tree from config", zap.Error(err))
	}
	return nil
}
