//nolint:gosec
package cibil

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	structPb "google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/gamma/api/vendors/cibil"
)

type AtlasFulfillOfferRequest struct {
	Req                                                        *credit_report.AtlasFulfillOfferRequest
	SiteName, AccountName, AccountCode, ProductConfigurationId string
	Url                                                        string
	ApiKey, MemberRefId                                        string
	ClientSecret                                               string
	DisableDefaultValuesInFulfillOffer                         bool
}

func (a *AtlasFulfillOfferRequest) Add(req *http.Request) *http.Request {
	req.Header.Add("member-ref-id", a.MemberRefId)
	req.Header.Add("apikey", a.ApiKey)
	if a.ClientSecret != "" {
		req.Header.Add("client-secret", a.ClientSecret)
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Accept", "application/json")
	return req
}

func (a *AtlasFulfillOfferRequest) Marshal() ([]byte, error) {
	details := a.Req.GetUserDetails()
	req := &cibil.AtlasFulfillOfferRequest{
		FulfillOfferRequest: &cibil.FulfillOfferRequest{
			SiteName:               a.SiteName,
			AccountName:            a.AccountName,
			AccountCode:            a.AccountCode,
			ClientKey:              a.Req.GetClientKey(),
			RequestKey:             a.Req.GetRequestKey(),
			ProductConfigurationId: a.ProductConfigurationId,
			PartnerCustomerId:      a.Req.GetClientKey(),
			CustomerInfo: &cibil.CustomerInfo{
				Name: &cibil.Name{
					Forename: details.GetName().GetFirstName(),
					Surname:  details.GetName().GetLastName(),
				},
				IdentificationNumber: &cibil.IdentificationNumber{
					IdentifierName: "TaxId",
					Id:             details.GetPan(),
				},
				DateOfBirth: datetime.DateToTime(details.GetDateOfBirth(), time.UTC).Format("2006-01-02"),
				PhoneNumber: &cibil.PhoneNumber{
					Number: strconv.FormatUint(details.GetPhoneNumber().GetNationalNumber(), 10),
				},
				Email: details.GetEmail(),
				// Add default address and gender only if not disabled
				Address: func() *cibil.Address {
					if !a.DisableDefaultValuesInFulfillOffer {
						return &cibil.Address{
							StreetAddress: "Default",
							City:          "Default",
							PostalCode:    "400001",
							Region:        "27",
							AddressType:   "1",
						}
					}
					return nil
				}(),
				Gender: func() string {
					if !a.DisableDefaultValuesInFulfillOffer {
						return "Male"
					}
					return ""
				}(),
			},
			LegalCopyStatus:           "Accept",
			UserConsentForDataSharing: "true",
		},
	}
	marshaller := protojson.MarshalOptions{EmitUnpopulated: true}
	return marshaller.Marshal(req)
}

// URL provides the URL to send the request to
func (a *AtlasFulfillOfferRequest) URL() string {
	return a.Url
}

// HTTPMethod returns the http method to use for the API call.
func (a *AtlasFulfillOfferRequest) HTTPMethod() string {
	return http.MethodPost
}

// GetResponse returns Response struct that can deserialize the vendor response
func (a *AtlasFulfillOfferRequest) GetResponse() vendorapi.Response {
	return &AtlasFulfillOfferResponse{}
}

type AtlasFulfillOfferResponse struct {
}

func (a *AtlasFulfillOfferResponse) UnmarshalV2(ctx context.Context, b []byte) (proto.Message, error) {
	return a.handleUnmarshal(ctx, b)
}

func (a *AtlasFulfillOfferResponse) handleUnmarshal(ctx context.Context, b []byte) (proto.Message, error) {
	// In some failure cases, the failure field value comes as a string value instead of a json value due to which the unmarshalling of failure response into vendor response proto object fails,
	// added a custom unmarshalling logic to unmarshalled such responses.
	// Sample vendor payload with failure field having json value :
	// {"FulfillOfferResponse":{"FulfillOfferError":{"Failure":{"FailureEnum":"FAILURE","Message":"4000100-Connection Error with ICRS: SocketTimeoutException - Read timed out"}},"ResponseKey":"930600b2bc6a46bb:73c29ca6:18fd7b93d54:-3814","ResponseStatus":"Failure"}}
	// Sample vendor payload with failure field having string value :
	// {"FulfillOfferResponse":{"FulfillOfferError":{"ErrorStatus":"ValidationError","Failure":"cvc-complex-type.2.4.b: The content of element 'Name' is not complete. One of '{Forename, Surname}' is expected."},"ResponseStatus":"ServiceError"}}
	var possibleFailureRes structPb.Struct
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, &possibleFailureRes)
	if err != nil {
		logger.Error(ctx, "failed to unmarshall AtlasFulfillOfferResponse from vendor", zap.Error(err), zap.String(logger.PAYLOAD, string(b)))
		return &credit_report.AtlasFulfillOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to unmarshall AtlasFulfillOfferResponse"),
		}, nil
	}

	checkRes, errString := checkErrorByStringMatch(possibleFailureRes.GetFields()["FulfillOfferResponse"].GetStructValue().GetFields()["FulfillOfferError"].GetStructValue().GetFields())
	if checkRes != credit_report.AtlasFulfillOfferResponse_OK {
		logger.Error(ctx, "atlas FulfillOffer response not OK due to vendor failures", zap.String("failureMsg", errString), zap.String(logger.PAYLOAD, string(b)))
		return &credit_report.AtlasFulfillOfferResponse{
			Status: rpc.NewStatusWithoutDebug(uint32(checkRes), errString),
		}, nil
	}

	var m cibil.AtlasFulfillOfferResponse
	err = protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, &m)
	if err != nil {
		logger.Error(ctx, "failed to unmarshall AtlasFulfillOfferResponse", zap.Error(err), zap.String(logger.PAYLOAD, string(b)))
		return &credit_report.AtlasFulfillOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to unmarshall AtlasFulfillOfferResponse"),
		}, nil
	}

	resp := m.GetFulfillOfferResponse()
	if resp.GetResponseStatus() != successStatus {
		logger.Error(ctx, "atlas FulfillOffer response not OK", zap.String(logger.RESPONSE_CODE, resp.GetResponseStatus()),
			zap.String("response_key", resp.GetResponseKey()), zap.String(logger.PAYLOAD, string(b)))

		atlasErrorResponse := buildAtlasErrorObject(resp.GetFulfillOfferError())
		return &credit_report.AtlasFulfillOfferResponse{
			Status:             getRpcStatusBasedOnAtlasFailureEnum(atlasErrorResponse.GetAtlasFailureResponse().GetFailureEnum(), resp.GetResponseStatus()),
			ResponseKey:        resp.GetResponseKey(),
			AtlasErrorResponse: atlasErrorResponse,
		}, nil
	}

	vendorStatus := atlasStatusToEnum[strings.ToUpper(resp.GetFulfillOfferSuccess().GetStatus())]
	if vendorStatus == credit_report.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_UNSPECIFIED {
		logger.Error(ctx, "unhandled fulfillOffer success status", zap.String(logger.STATUS, resp.GetFulfillOfferSuccess().GetStatus()), zap.String(logger.PAYLOAD, string(b)))
		return &credit_report.AtlasFulfillOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("unhandled fulfillOffer success status: %s", resp.GetFulfillOfferSuccess().GetStatus())),
		}, nil
	}

	return &credit_report.AtlasFulfillOfferResponse{
		Status:       rpc.StatusOk(),
		ResponseKey:  resp.GetResponseKey(),
		VendorStatus: vendorStatus,
	}, nil
}

func (a *AtlasFulfillOfferResponse) Unmarshal(b []byte) (proto.Message, error) {
	return a.handleUnmarshal(context.Background(), b)
}

// nolint: dupl
func (a *AtlasFulfillOfferResponse) HandleHttpError(ctx context.Context, httpStatusCode int, responseBody []byte) (proto.Message, error) {
	logger.Error(ctx, "atlas FulfillOffer response not OK http error", zap.Int(logger.HTTP_STATUS, httpStatusCode), zap.String("response", string(responseBody)))
	var m cibil.NotOkResponseJson
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(responseBody, &m)
	if err != nil {
		logger.Error(ctx, "failed to unmarshall not ok cibil response", zap.Error(err), zap.String(logger.PAYLOAD, string(responseBody)))
		return &credit_report.AtlasFulfillOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to unmarshall not ok cibil response"),
		}, nil
	}
	return &credit_report.AtlasFulfillOfferResponse{
		Status: getRpcStatusFromNotOkResponse(m.GetHttpResponseCode(), m.GetDescription()),
	}, nil
}

// RedactRequestBody overrides the redaction method. Redacted logging is not required for this API hence returning
// empty response to avoid logging redacted request
func (a *AtlasFulfillOfferRequest) RedactRequestBody(_ context.Context, _ []byte, _ string) ([]byte, error) {
	return nil, nil
}

// RedactResponseBody overrides the redaction method. Redacted logging is not required for this API hence returning
// empty response to avoid logging redacted response
func (a *AtlasFulfillOfferResponse) RedactResponseBody(_ context.Context, _ []byte, _ string) ([]byte, error) {
	return nil, nil
}

func getRpcStatusBasedOnAtlasFailureEnum(failureEnum credit_report.AtlasFailureEnum, responseStatus string) *rpc.Status {
	switch failureEnum {
	case credit_report.AtlasFailureEnum_ATLAS_FAILURE_ENUM_SSN_EXISTS:
		return rpc.NewStatusWithoutDebug(uint32(credit_report.AtlasFulfillOfferResponse_CONFLICT_SSN_EXISTS), responseStatus)
	case credit_report.AtlasFailureEnum_ATLAS_FAILURE_ENUM_NO_HIT:
		return rpc.NewStatusWithoutDebug(uint32(credit_report.AtlasFulfillOfferResponse_NOT_FOUND_NO_HIT), responseStatus)
	default:
		return rpc.StatusInternalWithDebugMsg(fmt.Sprintf("got status: %s", responseStatus))
	}
}

var atlasStringMatchToFailureMap = map[string]credit_report.AtlasFulfillOfferResponse_Status{
	"the content of element 'name' is not complete": credit_report.AtlasFulfillOfferResponse_INVALID_ARGUMENT_INCOMPLETE_USER_NAME, // permanent
	"ssn_mismatch": credit_report.AtlasFulfillOfferResponse_SSN_MISMATCH, // permanent
	// "no_hit":       credit_report.AtlasFulfillOfferResponse_NOT_FOUND_NO_HIT, // terminal // handled in later checks in unmarshal
	"4000100-connection error with icrs: socketexception - connection reset": credit_report.AtlasFulfillOfferResponse_SOCKET_TIMEOUT, // transient
	// "ssn_exists": credit_report.AtlasFulfillOfferResponse_CONFLICT_SSN_EXISTS, // handled in later checks in unmarshal since data needs to be passed back to credit report workflow
	"bureau error:4000100-invalid enquiry data in name under name segment": credit_report.AtlasFulfillOfferResponse_INVALID_ARGUMENT_USER_NAME_INCORRECT_FORMAT, // permanent
	"identifier can not be updated for already enrolled customer":          credit_report.AtlasFulfillOfferResponse_INVALID_UPDATE_ALREADY_ENROLLED_CUSTOMER,
}

func checkErrorByStringMatch(ffoErrFields map[string]*structPb.Value) (credit_report.AtlasFulfillOfferResponse_Status, string) {
	if ffoErrFields == nil {
		return credit_report.AtlasFulfillOfferResponse_OK, ""
	}
	for _, ffoErrField := range ffoErrFields {
		if ffoErrField.String() != "" {
			for k, v := range atlasStringMatchToFailureMap {
				if strings.Contains(strings.ToLower(ffoErrField.String()), k) {
					return v, ffoErrField.String()
				}
			}
		}
		// The error message for invalid name format contains the same string as that of invalid enquiry data already.
		// Hence, checking for invalid enquiry data only if the error message does not contain invalid name format message.
		if strings.Contains(strings.ToLower(ffoErrField.String()), "4000100-invalid enquiry data") {
			return credit_report.AtlasFulfillOfferResponse_INVALID_ENQUIRY_DATA, ffoErrField.String()
		}
	}
	return credit_report.AtlasFulfillOfferResponse_OK, ""
}
