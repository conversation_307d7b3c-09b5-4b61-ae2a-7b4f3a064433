package credit_report

import (
	"fmt"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	creditReportVgPb "github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/gamma/vendorgateway/credit_report/cibil"

	"google.golang.org/protobuf/proto"
)

// NewCIBILCreditReportRequest creates a new request for cibil's credit report APIs depending on the type of the proto message.
// nolint: funlen
func (s *Service) NewCIBILCreditReportRequest(req proto.Message) vendorapi.SyncRequest {
	conf := s.Conf.Cibil()
	secrets := conf.CibilSecrets

	// Enabling cibil V2 credentials behind a flag
	apiKey := secrets.ApiKey
	clientSecret := secrets.ClientSecret
	if s.FlagsConf.EnableCibilV2Secrets() {
		apiKey = secrets.ApiKeyV2
		clientSecret = secrets.ClientSecretV2
	}

	switch v := req.(type) {
	case *creditReportVgPb.AtlasPingRequest:
		return &cibil.AtlasPingRequest{
			Req:          req.(*creditReportVgPb.AtlasPingRequest),
			SiteName:     secrets.SiteName,
			AccountName:  secrets.AccountName,
			AccountCode:  secrets.AccountCode,
			Url:          conf.PingUrl,
			ApiKey:       apiKey,
			MemberRefId:  secrets.MemberRefId,
			ClientSecret: clientSecret,
		}
	case *creditReportVgPb.AtlasFulfillOfferRequest:
		return &cibil.AtlasFulfillOfferRequest{
			Req:                                req.(*creditReportVgPb.AtlasFulfillOfferRequest),
			SiteName:                           secrets.SiteName,
			AccountName:                        secrets.AccountName,
			AccountCode:                        secrets.AccountCode,
			ProductConfigurationId:             secrets.ProductConfigurationId,
			Url:                                conf.FulfillOfferUrl,
			ApiKey:                             apiKey,
			MemberRefId:                        secrets.MemberRefId,
			ClientSecret:                       clientSecret,
			DisableDefaultValuesInFulfillOffer: conf.DisableDefaultValuesInFulfillOffer,
		}
	case *creditReportVgPb.AtlasGetAuthQuestionsRequest:
		return &cibil.AtlasGetAuthQuestionsRequest{
			Req:          req.(*creditReportVgPb.AtlasGetAuthQuestionsRequest),
			SiteName:     secrets.SiteName,
			AccountName:  secrets.AccountName,
			AccountCode:  secrets.AccountCode,
			Url:          conf.GetAuthQuestionsUrl,
			ApiKey:       apiKey,
			MemberRefId:  secrets.MemberRefId,
			ClientSecret: clientSecret,
		}
	case *creditReportVgPb.AtlasVerifyAuthAnswersRequest:
		return &cibil.AtlasVerifyAuthenticationAnswersRequest{
			Req:          req.(*creditReportVgPb.AtlasVerifyAuthAnswersRequest),
			SiteName:     secrets.SiteName,
			AccountName:  secrets.AccountName,
			AccountCode:  secrets.AccountCode,
			Url:          conf.VerifyAuthAnswersUrl,
			ApiKey:       apiKey,
			MemberRefId:  secrets.MemberRefId,
			ClientSecret: clientSecret,
		}
	case *creditReportVgPb.AtlasGetCustomerAssetsRequest:
		return &cibil.AtlasGetCustomerAssetsRequest{
			Req:          req.(*creditReportVgPb.AtlasGetCustomerAssetsRequest),
			SiteName:     secrets.SiteName,
			AccountName:  secrets.AccountName,
			AccountCode:  secrets.AccountCode,
			Url:          conf.GetCustomerAssetsUrl,
			ApiKey:       apiKey,
			MemberRefId:  secrets.MemberRefId,
			ClientSecret: clientSecret,
		}
	case *creditReportVgPb.AtlasGetProductWebUrlRequest:
		return &cibil.AtlasGetProductWebTokenRequest{
			Req:              req.(*creditReportVgPb.AtlasGetProductWebUrlRequest),
			SiteName:         secrets.SiteName,
			AccountName:      secrets.AccountName,
			AccountCode:      secrets.AccountCode,
			ProductUrlPrefix: conf.ProductUrlPrefix,
			Url:              conf.GetProductTokenUrl,
			ApiKey:           apiKey,
			MemberRefId:      secrets.MemberRefId,
			ClientSecret:     clientSecret,
		}
	default:
		logger.ErrorNoCtx(fmt.Sprintf("Unsupported request type %v", v))
		return nil
	}
}
