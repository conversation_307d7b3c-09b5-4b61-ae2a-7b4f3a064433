package aml

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/crypto"
)

type SecureExchange struct {
	cryptor crypto.Cryptor
}

func NewSecureExchange(
	cryptor crypto.Cryptor,
) *SecureExchange {
	return &SecureExchange{
		cryptor: cryptor,
	}
}

func (s *SecureExchange) GetCryptor() crypto.Cryptor {
	return s.cryptor
}

func (s *SecureExchange) GetRequestProcessingMethod() commonvgpb.RequestProcessingMethod {
	return commonvgpb.RequestProcessingMethod_SIGN_AND_ENCRYPT
}

func (s *SecureExchange) GetResponseProcessingMethod() commonvgpb.ResponseProcessingMethod {
	return commonvgpb.ResponseProcessingMethod_DECRYPT_AND_VERIFY
}
