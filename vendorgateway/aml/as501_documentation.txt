Screenzaa By TrackWizz 
 

Multi-Purpose Customer Information  

API- AS501 Document 

 
 

Version No Date Prepared By Reviewed & Approved By 

1.7 15-07-2025 Susheel R <PERSON> 

 
 

 

This Document is marked as L1(Open) 

 1 



 

 

Contents 

1. Overview 5 

2. HTTP Header Fields 5 

3. Request Field Description 6 

3.1. Main Request Data Fields 6 

3.2. Customer Data Fields (Individual & Legal Entity) 8 

3.3. Related Person Data Fields (Only when Legal Entity) 45 

*******.1 Table 1.1 regAmlRiskSpecialCategoryDtoList 62 

*******.2 Table 1.2  gstDtoList 62 

*******.3 Table 1.3 taxDetailDtoList 63 

3.4. Relation Data Fields (Only when Legal Entity) 64 

4. Sample Decrypted Request & Response for Individual: 65 

4.1. For Initial Screening (Purpose: 01) 65 

4.1.1. Sample No Hits Found (Request & Response) 65 

4.2. Initial & Continuous Screening (Purpose: 01 & 04) 71 

4.2.1. Sample No Hits Found (Request & Response) 71 

5. Response Field Description 78 

5.1. Main Data Fields 78 

5.2. Screening Specific Fields (Customer & Related Person) 78 

6. Steps for Encryption & Decryption 80 

6.1 Encryption 80 

6.1.1. Sample Encrypted Request 80 

6.1.2. Sample Encrypted Response 80 

6.2 Decryption 81 

7 Enumerations: 82 

7.1 Segment Enum 82 

7.2 Customer and RelatedParty Status Enum 82 

7.3 Constitution Type Enum 83 

7.4 Gender Enum 85 

7.5 Marital Status Enum 85 

7.6 Country Enum 86 

This Document is marked as L1(Open) 

 2 



 

7.7 Occupation Type Enum 102 

7.8 Nature Of Business Enum 102 

7.9 Product Enum 105 

7.10 Income Range Enum 106 

7.11 Currency Enum 107 

7.12 PEP Enum 107 

7.13 PEP Classification Enum 107 

7.14 AdverseReputation Classification Enum 108 

7.15 Tags Enum 109 

7.16 Channel Enum 109 

7.17 RegulatoryAMLRisk Enum 110 

7.18 State Enum 110 

7.19 Qualification Enum 112 

7.20 RegAMLSpecialCategory Enum 112 

7.21 Agency Enum 113 

7.22 Prefix Enum 113 

7.23 Document Enum 114 

7.24 RM Type Enum 116 

7.25 Purpose Enum 117 

7.25.1 Purpose-wise Mandatory Fields 118 

7.26 Relation Enum 120 

7.27 KYCAttestation Enum 122 

8 Variations 123 

8.1 Decrypted Request and Response Variations for Initial Screening (Individual) 123 

8.1.1 Confirm & Probable Hit (Request) 123 

8.1.2 Confirm & Probable Hit (Response) 127 

8.1.3 Probable Hits (Request) 130 

8.1.4 Probable Hits (Response) 135 

8.2 Decrypted Request and Response Variations for Continuous Screening (Individual) 138 

8.2.1 Confirm & Probable Hits (Request) 138 

8.2.2 Confirm & Probable Hit (Response) 142 

8.2.3 Probable Hits (Request) 146 

8.2.4 Probable Hits (Response) 151 

8.2.5 Validation Failure (Request) 154 

This Document is marked as L1(Open) 

 3 



 

8.2.6 Validation Failure (Response) 158 

8. Request And Response XSD 161 

8.1 Request XSD 161 

8.2 Response XSD (Purpose 01) 178 

8.3 Response XSD (Purpose 04) 181 

9. Validations 184 

9.1 Main Request Validations: 184 

9.2 Request Data Validations: 186 

9.3 Relational Validations 190 

9.3.1. Applicable for Screening 190 

9.3.2. General Validations 192 
 
 
 
 
 
 
  
 
 
 
 
 
 
 
 
 
  

This Document is marked as L1(Open) 

 4 



 

1. Overview 

This Restful API helps FI applications to create new and update existing individual and legal entities in 

TrackWizz. These FI applications will request AS501 API with all the valid customer details in the 

following JSON format and TrackWizz will be able perform any of the below actions for the customers 

basis the data sent in the API request to TrackWizz app. All the API requests and responses will be end 

to-end encrypted. 

1.1 Initial Screening 

1.2 Continuous Screening 

 

If the application encounters a request with a new combination of the SourceSystemName and 

SourceSystemCustomerCode, then the record/customer details would be added into the TrackWizz 

application. 

If the application encounters a request with a existing combination of the SourceSystemName and 

SourceSystemCustomerCode, then the record/customer details would be updated into the TrackWizz 

application 

 
 
2. HTTP Header Fields 

 

Field Name Mandatory Data Type Remarks 

ApiToken Yes String Static Value will be shared by the TrackWizz team during 
implementation. 
Provide the API Token of AS501 from the API Subscription 
Master 

Cluster Yes String Static Value: CL1_User should be passed. 

Domain Yes String The Domain header is a critical component of our API requests, 
allowing our clients to specify the originating domain of their 
requests. 
Ex: https://tenant3-sb.trackwizz.com/CustomerInfo/as501 

This Document is marked as L1(Open) 

 5 



 

3. Request Field Description 
 

3.1. Main Request Data Fields 
 
 

        
      Mandatory  
   Single/   for  
  Applicable Multiple   Purpose  
Field Name / XML  For Data  (01,02,03,  
Tag Mandatory IND/LE/Bot Type Length 04,05) Remarks 

h 

        
       Unique serial number that is 
       used to identify the record for 
       processing. RequestId should 
      01,02,03, be different for every record. 
requestId Yes Both Single String 200 04,05 

        
      Name of the Source System 
      from where the data is being 
      passed. The source system 
      mentioned in this field should 
      be according to TrackWizz 
     01,02,03, Code as per the Agency Enum 
sourceSystemName Yes Both Single Enum 04,05 values. 

        
       Static Value will be shared by 
      01,02,03, the TrackWizz team during 
apiToken Yes Both Single String 100 04,05 implementation. 

        
      The action to be performed 
      on the request provided by 
      the customer. Multiple 
      purposes can be passed based 
      on the Enum values provided, 
      which will be used to 
      customize the response. 
       
      Should be according to 
purpose Yes Both Multiple Enum 2 TrackWizz Code. 

This Document is marked as L1(Open) 

 6 



 

 
        
       Each request gets encrypted 
       using two types of 
       encryptions. First, the data 
       will get encrypted using a 
       symmetric session key, this 
       key will be unique for each 
       request. This key will also be 
       sent with the request after 
      01,02,03, encrypting it using an 
sessionKey Yes Both Single String 100 04,05 asymmetric key. 

customerList No Both  Array   Customer Data Fields: Refer 
(Complex) Table 3.2 

 

 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 

This Document is marked as L1(Open) 

 7 



 

 
3.2. Customer Data Fields (Individual & Legal Entity) 

 
        

Field Name / JSON Mandatory Applicable Values Data Lengt 01,02,03,  
(Yes/No) for 04,05 

Tag  IND/LE/Both Allowed Type h  Remarks 

        
SourceSystemCustome
rCode 

 Yes      is a unique ID assigned 
01,02, 

      03,04,05 by the source system 
to 

sourceSystemCustome  Both Single String Max  the customer. 
rCode 
        
      Unique numbers 
      assigned to the 
uniqueIdentifier No Both Single String Max customer to uniquely 

identify the record. 

        
The date on which the 

      customer record was 

      created in the clients 

      Source System or the 

 No Both Single String 01,02, client was on boarded. 

sourceSystemCustome     03,04,  
rCreationDate 
     05 Format should be "DD- 

This Document is marked as L1(Open) 

 8 



 

       MMM-YYYY". Should not 
be a Future Date. 

        
      If accounts are opened 
      through EKYC or biometric 
      process , pass the values as 
      "0" or "1". 
       
ekycOTPbased No IND Single String 100 0 : No and 1: Yes 

        
     Defines the segment of a 
     business that generates its 
     own revenues and creates 
     its own product, product 
     lines, or service offerings. 
      
 No    Should be as per 
segment Both Single Enum TrackWizz Code. 

        
     The date on which the 
     customer was linked to the 
     segment. Multiple comma 
     separated values are 
     allowed. 
      
     Should be as per 
segmentStartDate No Both Single String TrackWizz Code. 

        
     List of products the 
     customer opted for. 
     Multiple comma separated 
     values are allowed. 
      
     Should be as per 
     TrackWizz Code. 
products No Both Multiple Enum 

 

This Document is marked as L1(Open) 

 9 



 

 
        
      
      
     It will correspond to the 
     current status of the 
     customer in its source 
     system whether it is 
     Active, Inactive,Closed, 
     Dormant or Suspended. 
      
     Should be as per 
status No Both Single Enum TrackWizz Code. 

        
     User needs to enter the 
     specific date as of which 
     the Customer owns the 
     status mentioned in the 
     source system. 
      
     Format should be "DD- 
     MMM-YYYY". Should not 
effectiveDate No Both Single String be a Future Date. 

        
      Defines the record type of 
      the Customer. 
     01,02,  
     03,04, Should be as per 
constitutionType Yes Both Single Enum 05 TrackWizz Code. 

        
     Specify the title of the 
     Customer Name. 
      
 No    Should be as per 
prefix IND Single Enum TrackWizz Code. 

This Document is marked as L1(Open) 

 10 



 

        
       Specify the First Name of 
       the Customer. 
        
       Criteria for passing First 
       Name is as follows : 
        
       First Character cannot be 
      01,02, special character. Value 
      03,04, passed cannot have only 
firstName No Both Single String Max 05 special character. 

 
Consecutive Special 
Character are not 
allowed. The value 
passed should contain at 
one alphabet 

        
      Specify the Middle Name 
      of the Customer. 
       
      Criteria for passing 
      Middle Name is as follows 
      : 
       
      First Character cannot be 
      special character. Value 
      passed cannot have only 
      special character. 
      Consecutive Special 
middleName No IND Single String Max Character are not 

allowed. The value 
passed should contain at 
one alphabet 

        
      Specify the Last Name of 
      the Customer. 
       
      Criteria for passing Last 
lastName  No IND  Single String  Max Name is as follows: 
      
     First Character cannot be 
     special character. Value 
     passed cannot have only 
     special character. 
     Consecutive Special 

  Character are not 
  allowed. The value 

 passed should contain at 
one alphabet 

This Document is marked as L1(Open) 

 11 



 

        
      Specify the Alias name of 
alias No Both Single String 200 the Customer if any. 

        
     Specify the title of 
     Customer's Father. 
      
     Should be as per 
fatherPrefix No IND Single Enum Trackwizz Code. 

        
      Specify the First Name of 
      the Customer's Father. 
       
      Criteria for passing 
      Father First Name is as 
      follows : 
       
      First Character cannot be 
      special character. Value 
      passed cannot have only 
 No IND Single String Max special character. 
 Consecutive Special 
 Character are not 
 allowed. The value 
 passed should contain at 
fatherFirstName one alphabet 

This Document is marked as L1(Open) 

 12 



 

 
 

        
       First Character cannot 
       be special character. 
       Value passed cannot 
       have only special 
       character. 
fatherLastName No IND Single String Max IND Consecutive Special 

Character are not 
allowed. The value 
passed should contain at 
one alphabet 

        
     Specify the title of the 
     Customer's Mother. 
      
     Should be as per 
motherPrefix No IND Single Enum TrackWizz Codes. 

        
      Specify the First Name 
      of the Customer's 
      Mother. 
       
      The criteria for passing 
      Mother First Name are 
      as follows: 
       
      First Character cannot 
      be special character. 
      Value passed cannot 
      have only special 
      character. 
motherFirstName No IND Single String Max Consecutive Special 

Character are not 
allowed. The value 
passed should contain at 
one alphabet 

This Document is marked as L1(Open) 

 13 



 

       Specify the Middle Name 
      of the Customer's 
      Mother. 
       
      Criteria for passing 
      Mother Middle Name is 
      as follows: 

       
      First Character cannot 
      be special character. 
      Value passed cannot 
motherMiddleName No IND Single String Max have only special 

character. 
Consecutive Special 
Character are not 
allowed. The value 
passed should 

This Document is marked as L1(Open) 

 14 



 

 
       contain at one alphabet 

        
      Specify the Last Name 
      of the Customer's 
      Mother. 
       
      Criteria for passing 
      Mother Last Name is as 
      follows: 
       
motherLastName No IND Single String Max First Character cannot 

be special character. 
Value passed cannot 
have only special 
character. 
Consecutive Special 
Character are not 
allowed. The value 
passed should contain at 
one alphabet 

        
     If the customer is 
     minor(age less than 18) 
     consider "Yes" or else 
     consider "No". 
      
     Values should be 
     passed as "0" and "1" 
      
minor No IND Single String 0 : No and 1: Yes". 

        
     Specify the gender of 
     the customer. 
      
     Should be as 
gender No IND Single Enum per TrackWizz 

Codes. 

        
     Specify the legally 
     defined marital status of 
maritalStatus No IND Single Enum the customer. 

This Document is marked as L1(Open) 

 15 



 

        
     Specify the Job 
     or Profession 
     of the 
     customer. 
      
occupationType No IND Single Enum Should be as 

per TrackWizz 
Codes. 

        
      User will be required to 
      enter the value under 
      this field only if the 
      value under the 
      Occupation Type field is 
      passed as "Oth". 
       
occupationTypeOther No IND Text Field String 200 This is a text field 

wherein the user will be 
required to type in the 
Occupation Type. 

        
     The nature of the 
     business with which the 
     customer is involved 
     with.Multiple comma 
     separated values can be 
     passed. 
      
natureOfBusiness No Both Single Enum Should be as per 

TrackWizz Codes. 
        
      User will be required to 
      enter the value under 
      this field only if the 
      value under the 
natureOfBusinessOther      NatureofBusiness field 

No Both Text Field String 200 is passed as "Others". 
 
This is a text field 
wherein the user will be 
required to type in the 
Nature of business. 

        
     The document to be 
     submitted by the 
     customer as Proof of 
     ID. 
      
proofOfIDSubmitted No Both Single Enum Should be as per 

TrackWizz Codes. 

This Document is marked as L1(Open) 

 16 



 

        
      Date on which 
     01,02, the customer was 
dateofBirth/     03,04, born. 
Incorporation No Both Single String 05  

Format should be "DD- 
MMM-YYYY". 
 
For Non-Ind pass date in 
 
"Date of Incorporation" 
field. 

        
      01,02,  
      03,04, Specify the Work email of 
workEmail No Both Single String Max 05 the customer. 

        
      01,02,  
      03,04, Specify the Personal 
personalEmail No Both Single String Max 05 email of the customer. 

        
      Define ISD code of the 
      WorkMobileNumber 
      related to the 
      customer. 
       
workMobileISD No Both Single String 2 Should be a 2 digit code 

in all the ISD fields . 

        
      01,02,  
      03,04, Specify the Work mobile 
workMobileNumber No Both Single String 100 05 number of the customer. 

This Document is marked as L1(Open) 

 17 



 

        
       
       
      Define the ISD code of 
      the 
      PersonalMobileNumber. 
       
personalMobileISD No Both Single String 2 Should be a 2 digit code 

in all the ISD fields 
        
      01,02, Specify the Personal 
      03,04, Mobile Number of the 
personalMobileNumber No Both Single String 100 05 customer. 

        
     Country where the 
     customer 
     permanently 
permanentAddressCoun No    resides. 
try Both Single Enum  

Should be as per 
TrackWizz Codes 

        
     Specify the ZipCode of the 
permanentAddressZipC     PermanentAddress of the 
ode No Both Single String customer. 

        
      Specify the 
      Permanent Address 
      details of the 
permanentAddressLine No Both Single String Max Customer. 
1      
     First Character cannot be 
     special character Value 
     passed cannot have only 
     special character. The 
    value passed should 
    contain at least one 

 alphabet. 
 

This Document is marked as L1(Open) 

 18 



 

        
      Specify the 
      Permanent Address 
      details of the 
      Customer. 
       
      First Character cannot be 
      special character Value 
      passed cannot have only 
      special character. The 
permanentAddressLine      value passed should 
2 No Both Single String Max contain at least one 

alphabet. 
        
      Specify the 
      Permanent Address 
      details of the 
      Customer. 
       
      First Character cannot be 
      special character Value 
      passed cannot have only 
      special character. The 
permanentAddressLine No     value passed should 
3 Both Single String Max contain at least one 

alphabet. 
        
permanentAddressDistr      Specify the District where 
ict No Both Single String 100 the Customer resides. 

        
      Specify the City where 
      the Customer 
permanentAddressCity No Both Single String 100 permanently resides. 

        
     Specify the State where 
     the customer 
     permanently resides. 
permanentAddressState No     

   Should be as per 
Both Single Enum TrackWizz Codes. 

This Document is marked as L1(Open) 

 19 



 

        
     Specify the State where 
     the customer resides if its 
permanentAddressOthe No    other than the TrackWizz 
rState Both Single 200 provided Codes. 

        
     The Address Proof 
     document code 
     submitted by the 
     customer for 
     verification of 
     permanent address. 
permanentAddressDocu No     
ment Both Single Enum Should be as per 

TrackWizz Codes. 
        
      Users will be required to 
      enter the value under this 
      field only if the value 
permanentAddressDocu      under the 
mentOthersValue No Both Text Field String Max PermanentAddressDocu

ment is passed as 
"OthersPOA". 

        
     Specify the 
     Correspondence 
     Address Country of the 
     Customer. 
correspondenceAddress No     
Country Both Single Enum Should be as per 

TrackWizz Codes. 

        
     Specify the ZipCode of 
correspondenceAddress     Correspondence Address 
ZipCode No Both Single String of the Customer. 

        
      Specify the 
      Correspondence 
      Address details of the 
 

     
 Customer. 

       
      First Character cannot be 
      special character Value 
correspondenceAddressLi      passed cannot have only 

This Document is marked as L1(Open) 

 20 



 

ne1      special character. The 
No     value passed should 

Both Single String Max contain at least one 
alphabet. 

        
      Specify the 
      Correspondence 
      Address details of the 
      Customer. 
       
      First Character cannot be 
      special character Value 
      passed cannot have only 
      special character. The 
correspondenceAddres No     value passed should 
sLine2 Both Single String Max contain at least one 

alphabet. 
        
      Specify the 
      Correspondence 
      Address details of the 
      Customer. 
       
      First Character cannot be 
      special character Value 
      passed cannot have only 
      special character. The 
correspondenceAddres No     value passed should 
sLine3 Both Single String Max contain at least one 

alphabet. 
        
     Specify the 
correspondenceAddres     Correspondence Address 
sDistrict No Both Single String District of the Customer. 

        
     Specify the 
correspondenceAddres     Correspondence Address 
sCity No Both Single String City of the Customer. 

This Document is marked as L1(Open) 

 21 



 

        
     Specify the 
     Correspondence 
     Address State of the 
     Customer. 
correspondenceAddres No     
sState Both Single  Should be as per 

Enum TrackWizz Codes. 

        
      Specify the 
      Correspondence State of 
      the Customer if other 
correspondenceAddres      than TrackWizz provided 
sOtherState No Both Single String 200 values. 

This Document is marked as L1(Open) 

 22 



 

 
        
     Address Proof 
     document code 
     submitted by the 
     Customer for 
     verification of 
     correspondence 
correspondenceAddr     address. 
essDocument No Both Single Enum  

Should be as 
per TrackWizz 
Codes. 

        
     Specify the Country for 
     which the customer 
     holds the citizenship. 
     Multiple commas 
     separated values can be 
     passed. 
      
citizenships No IND Multiple Enum Should be according 

to TrackWizz Codes. 

        
     The Nationality of the 
     Customer will be 
     passed. Multiple 
     comma separated 
     values can be passed. 
      
     Should be according 
nationalities No IND Multiple Enum to TrackWizz Codes. 

This Document is marked as L1(Open) 

 23 



 

 
        
     Country in which 
     the customer 
     resides. 
      
countryOfResidence No Both Single Enum Should be according 

to TrackWizz Codes. 

        
     Country where the 
     customer was born. 
     For Non-Ind pass 
     country in the 
     "Country of 
     Incorporation" field. 
      
countryOfBirth No Both Single Enum Should be as 

per TrackWizz 
Codes. 

       Country where the 
     customer was employed. 
      
     Should be as per 
countryOfEmployment No IND Single Enum TrackWizz Codes. 

       The country where the 
     customer received their 
countryOfEducation No IND Single Enum highest or primary 

formal education. 
 
Should be as per 
TrackWizz Codes. 

This Document is marked as L1(Open) 

 24 



 

        
     The city where 
     the customer 
     was born. 
      
     For Non-Ind pass city in 
      
     "City of 
birthCity No Both   Incorporation" 

Single String field. 

taxDetailDtoList No   Array   Refer Table 1.3 
 (Complex) 

        
     The name of the country 
     that issued the 
     customer's passport. 
passportIssueCountry No Both Single Enum  

Should be as per 
TrackWizz Code. 

 No       
      Specify the Customer's 
     01,02, passport number if 
     03,04, passport is available with 
passportNumber Both Single String Max 05 him. 

        
     Specify the date from 
     which the Customer's 
     passport will no longer 
     be applicable. Should be 
     of "DD-MMM-YYYY" 
passportExpiryDate  Both Single String format. 

No  
Should always be a 
Future Date. 

gstinDtoList No   Array   Refer Table 1.2 
(Complex) 

 

This Document is marked as L1(Open) 

 25 



 

       Specify the Customer's 
      Voter Id number if 
      available. 
voterIdNumber No Both Single String Max 

       Specify the Customer’s 
      01,02, Driving License Number 
      03,04, if available. 
drivingLicenseNumber No IND Single String Max 05 

       Specify the date from 
     which the Customer's 
     Driving License will no 
     longer be applicable. 
     Should be of "DD-MMM- 
     YYYY" format. Should 
drivingLicenseExpiryDa     always be a Future Date. 
te No IND Single Date 
       Aadhaar is a 12-digit 
      individual identification 
aadhaarNumber      number issued by the 
 No Both Single String 100 Unique Identification 
     Authority of India on 
     behalf of the 
     Government of India. 
     Pass 12-digit Aadhaar 
     number 
     (123456789121) or 
     with pass 8X with last 4 
     digits of the customer’s 
     Aadhaar number 
     (XXXXXXXX1234) OR 

   last 4 digits of the 
 customer’s 

Aadhaar number (1234 
       Aadhaar Data Vault is a 
      centralized storage for all 
      the Aadhaar numbers 
      collected by the 
      AUAs/KUAs/Sub-AUAs/ 
aadhaarVaultReference      or any other agency for 
Number No Both Single String 100 specific purposes under 

Aadhaar Act and 
regulation. 

       NREGA Number of the 
      Customer. 
nregaNumber No Both Single String 100 

This Document is marked as L1(Open) 

 26 



 

       The value of the CKYC 
      Identification type NPR 
      Letter. 
nprLetterNumber No Both Single String 100 

       Director Identification 
       Number or DIN is a 
       registration required for 
directorIdentificationNu  IND Single String 100 01,02, any person who wishes 
mber No 03,04, to register a company. 

05 

This Document is marked as L1(Open) 

 27 



 

 
 
 

        
     Form for declaration to 
     be filed by an individual 
     or a person (not being a 
     company or firm) who 
     does not have a 
     permanent account 
     number(PAN). 
      
     If FormSixty is passed, 
     the value passed in this 
     field should be "1" and 
formSixty No Both Single String if FormSixty is not 

passed, the value 
passed here should be 
"0" . 

        
      01,02, Specify the Pan number 
pan No Both Single String 100 03,04, of the customer. If Pan 

05  number is passed the 
value in the Form Sixty 
field should be "0" and if 
Pan is not passed, the 
value passed here 
should be "1". 

legalEntityIdentifier No LE Single String 100 01,02, A Legal Entity Identifier 
03,04, (LEI) is a unique 
05 identifier assigned to 

legal entities. Legal Entity 
Identifier accepts 20 
character alpha numeric 
values.   

UdhyamRC No LE Single String 100 01,02,0 Udhyam Registration 
3,04,05 Certification is issued to 

MSMEs to obtain loans 
from banks and benefits. 
Its of the following 
format: UDYAM-XX-00-
0000000 

ISIN No LE Single String 100 01,02,0 ISIN, short for 
3,04,05 International Securities 

Identification Number of 
the Legal Entity is a 12 
digit alphanumeric code. 

       Identification number 
ckycNumber No Both Single String 100 provided to the customer 

who has completed the 
KYC process. 

This Document is marked as L1(Open) 

 28 



 

        
       Company Identification 
companyIdentification     100 01,02, Number issued by the 
Number No LE Single String 03,04, Ministry of Corporate 

05 Affairs India. 
 No       
companyRegistrationN     Provide the registration 
umber LE Single String 100 number of the company. 

        
companyRegistrationC      The name of the country 
ountry No LE Single String 100 where the company was 

registered. 

This Document is marked as L1(Open) 

 29 



 

 
        
      GIIN means the 
      Global 
      Intermediary 
      Identification 
      Number which is a 
      19- character 
globalIntermediaryIde      identification 
ntificationNumber No LE Single String 100 number in the 

format 
XXXXXX.XXXXX.XX
.XXX 
assigned to the 
reporting entity 
by the USA. 

        
     The type of 
     Attestation 
     done for the 
 No    customer. 
kycAttestationType Both Single Enum  

Should 
be as per 
TrackWiz
z Codes. 

        
     

The date on which 
     the KYC was 
kycDateOfDeclaration No Both Single String initiated for the on 

boarded customer 
by the institution. 
 
Format should be 
“DD- MMM-YYYY”. 

        
      The location where 
kycPlaceOfDeclaration      the KYC Was 

No Both Single string 100 initiated for the on 
boarded customer. 

        
     The date when the 
     KYC details given 
kycVerificationDate No Both Single String were successfully 

verified by a KYC 
Employee of the 
Institution. 

        
      The employee who 
      verified the KYC 
      details of the on 
      boarded customer. 

This Document is marked as L1(Open) 

 30 



 

       
kycEmployeeName No Both Single String Max Criteria for 
     passing 
     KYCEmployee 
     Name is as 
     follows: 
      
     First Character 
     cannot be special 
     character. Value 
    passed cannot 

  have only special 
character. 
Consecutive Special 
Character are not 
allowed. The value 
passed should 
contain at one 
alphabet 

        
      The designation 
      of the verified 
      employee in the 
      company’s 
      hierarchy. 
       
      Criteria for 
      passing KYC 
      Employee 
      Designation is as 
      follows : 
kycEmployeeDesignati No      
on Both Single String 100 First Character 

cannot be special 
character. Value 
passed cannot 
have only special 
character. 
Consecutive Special 
Character are not 
allowed. The value 
passed should 

        
      The branch of the 
      institution which 
      initiated and 
      verified the KYC 
      Details of the on 
      boarded 
      customer. 
       
      Criteria for 
      passing KYC 

This Document is marked as L1(Open) 

 31 



 

      Verification 
      Branch Name is as 
  Both Single String 100 follows: 
kycVerificationBranch No      
     First Character 
     cannot be special 
    character. Value 
 passed cannot 

have only special 
character. 
Consecutive 
Special Character 
are not allowed. 
The value passed 
should contain at 
one alphabet 
 

       The Unique 
      Identification Code 
kycEmployeeCode No Both Single String Max of the Employee 

who carried out the 
KYC Verification. 
 
Criteria for 
passing KYC 
Employee Code 
is as follows : 
 
First Character 
cannot be special 
character. Cannot 
have only special 
characters 
Alphanumeric 

        
     Document to be 
     submitted to 
     establish the 
     Identity of the 
identityDocument No Both Single Enum Customer. 

 
Should be as per 
TrackWizz Code. 

        
     Specify if the 
     company is Listed 
     or Not, It will be 
     applicable for 
listed No LE Single String Non Ind.Values 

passed should be 
"0" or "1". 
 
0: Listed 1: Not 

This Document is marked as L1(Open) 

 32 



 

Listed 

        
     Country where 
     the company 
     operates from. 
     Multiple 
     commas 
countryOfOperations  No Both Multiple Enum separated 

   values can be 
 passed. Should 
 be as per 

TrackWizz Code. 
 
 

        
      The Initial Id 
      assigned to a 
applicationRefNumber No Both Single String 500 prospect before 

Onboarding in a 
Client’s Source 
System. 

        
      Holder details to be 
      passed. Eg. 
documentRefNumber No Both Single String 500 Applicant, Co- 

Applicant, etc. 

        
regAMLRiskSpecialCate  Refer Table1.1 
goryDtoList Complex 

 

This Document is marked as L1(Open) 

 33 



 

        
     A Regulatory AML 
     risk assessment 
     process that 
     analyzes a 
     customer's risk of 
regAMLRisk No Both Single Enum exposure to 

financial crime. 
Should be as per 
TrackWizz Code. 

This Document is marked as L1(Open) 

 34 



 

 
 
 

        
     The date on which the 
     AML Risk Review was 
     done previously. It 
     should be a past date 
     value . 
regAMLRiskLastRiskR No     
eviewDate Both Single String Format should be 

“DD- MMM-
YYYY” 

        
     The date on which 
     the next AML Risk 
     Review will be done. 
     It should be a future 
     date value. 
regAMLRiskNextRisk No     
ReviewDate Both Single String Format should be 

“DD- MMM-
YYYY” 

        
      Provide the Income 
      Range of the 
      customer. 
 No      
incomeRange Both Single Enum 100 Should be as 

per 
TrackWizz 
Codes. 

        
      Provide the Exact 
exactIncome No Both Single Decimal (13,2) Income earned by 

the customer. 
        
     Provide the Income 
     currency of the 
     customer. 
      
incomeCurrency No Both Single Enum Should be 

as per 
TrackWizz 
Code. 

        
     The date from 
     which the 
incomeEffectiveDate No Both Single String customer's income 

became active. 

This Document is marked as L1(Open) 

 35 



 

        
      Describe any additional 
incomeDescription No Both Single String 200 details of Income of the 

customer. 

        
     The document to be 
     submitted that 
     contains the Income 
     details of the 
     customer. 
 No     
incomeDocument Both Single Enum Should be as per 

TrackWizz Code. 
        
      Provide the Exact Net 
exactNetworth No Both Single Decimal (13,2) Worth of the Customer. 

        
     Provide the Net 
     Worth currency of 
     the Customer. 
      
networthCurrency No Both Single Enum Should be as per 

TrackWizz Codes. 
        
     The date from 
     which the 
     customer's Net 
     Worth Income was 
     active. It should be a 
     Past Date value. 
networthEffectiveDate No Both Single String  

Format should be “DD- 
MMM-YYYY” 

        
      Describe any additional 
networthDescription      details about the Net 

No Both Single String 200 worth Income of the 
customer. 

This Document is marked as L1(Open) 

 36 



 

 
        
     Document to be 
     provided that contains 
     the Net worth Income 
     details of the customer 
     

Multiple comma 
     

separated values can be 
networthDocument No Both Single Enum 

passed. 
 
Should be as per 
TrackWizz Code. 

        
     Specify if the customer 
     is Politically Exposed or 
     not. Values should be 
     passed as "0" and "1" 
      
politicallyExposed No Both Single Enum 0: No and 1: Yes". 

        
     Define the 
     classification type of 
     PEP customer. 
     Multiple comma 
     separated values are 
     allowed. 
politicallyExposedClass      
ification No Both Multiple Enum Should be as per 

TrackWizz Code. 
        
     In-order to capture the 
     customer’s adverse 
     reputation value needs 
     to be passed under this 
     field. 
      
     Values should be 
     passed as "0" and "1" 
adverseReputation No Both Single String  

0 : No and 1: Yes". 

This Document is marked as L1(Open) 

 37 



 

 
        
     Multiple commas 
     separated values can 
     be passed. 
      
adverseReputationCla No    Should be as 
ss ification Both Multiple Enum per TrackWizz 

Code. 

        
adverseReputationDet No  Text   Additional details of 
ails Both Field String 400 Adverse Reputation if 

any. 
       screening would 
      be performed. It is 
screeningProfile No Both Single String 100 a set of watchlist 

sources/feeds 
against which the 
customers record 
would be 
screened. User can 
pass the value 
under this field or 
not. If the user 
passes the value 
under this field, 
then the record 
will be screened 
against this 
specified 
screening profile. 
If the user does 
not pass the value 
under this then the 
default profile will 
be mapped to this 
customers record 
and screening will 
be performed 
against it. 

This Document is marked as L1(Open) 

 38 



 

 
        
      This will be used for 
      Screening to send the 
      document in the 
      response when there 
screeningReportwhen No     are no hits. Values 
Nil Both Single String 100 passed will be "0:No" or 

"1:Yes". 

        
      The type of Risk 
riskProfile No Both Single String 100 Profile used for the 

Customer. 
        
      This will be used for 
      RiskRating to send the 
      document in response 
      when the risk is low. 
      Pass value as "0" or 
      "1" 0: No and 1:Yes 
riskRatingReportWhen No     
Low Both Single String 100 

        
      Should be as 
tags No Both Multiple Enum 100 per 

TrackWizz 
Code. 

        
      Customer FamilyCode 
familyCode No IND Text String Max in the Source System 

Field is captured here. 
        
     The medium through 
     which the customer is 
channel No    getting on boarded. 

Both Single Enum Should be as per 
TrackWizz Code. 

        
      Specify the First Name of 
contactPersonFirstNamNo LE Single String Max the customer's contact 
e1 person. 

This Document is marked as L1(Open) 

 39 



 

 
        
      

Specify the Middle 
     

contactPersonMiddle No Name of the customer's 
Name1 LE Single String Max contact person. 

        
      Specify the Last Name 
contactPersonLastNa No LE Single String Max of the customer's 
me1 contact person. 

        
      Specify the First Name 
contactPersonFirstNa No LE Single String Max of the customer's 
me2 contact person. 

        
      Specify the Middle 
contactPersonMiddle      Name of the customer's 
Name2 No LE Single String Max contact person. 

        
      Specify the Last Name 
contactPersonLastNa      of the customer's 
me2 No LE Single String Max contact person. 

        
      Specify the Contact 
contactPersonDesigna      Person Designation 
tion1 No LE Single String Max mentioned by the 

customer 

        
      Specify the Contact 
contactPersonDesigna      Person of Designation 
tion2 No LE Single String Max mentioned by the 

customer 

This Document is marked as L1(Open) 

 40 



 

 
        
      Specify the Contact 
contactPersonMobileI No     Person Mobile ISD of the 
SD LE Single String Max customer 

        
      Specify the 
contactPersonMobile      ContactPerson Mobile 
No2 No LE Single String Max number of the customer 

        
      Specify the 
contactPersonMobileI No     ContactPerson Mobile 
SD2 LE Single String Max ISD of the customer 

        
      Specify the Contact 
contactPersonMobile No     Person Mobile number 
No2 LE Single String Max of the customer 

        
      Specify the Contact 
contactPersonEmailId No LE Single String Max Person Email Id of the 
1 customer 
        
      Specify the Contact 
contactPersonEmailId No LE Single String Max Person Email Id of the 
2 customer 
        
     Mention the 
     Educational 
     background details of 
     the Customer. Multiples 
educationalQualificat No    comma separated 
ion IND Multiple Enum values can be passed. 

Should be as per 
TrackWizz Code. 

        
     The date on which the 
     Business started. It 
     should be a past date 
     value. 
      
commencementDate No LE Single String Format should be 

“DD- MMM-YYYY” 

This Document is marked as L1(Open) 

 41 



 

        
      Specify the Maiden 
      First Name. 
       
      

Criteria for passing 
maidenFirstName No IND Single String Max 

Maiden First Name is as 
follows : 
 
First Character cannot 
be special character. 
Value passed cannot 
have only special 
character. 
Consecutive Special 
Character are not 
allowed. The value 
passed should 
contain at one 
alphabet 

        
      Specify the Maiden 
      Middle Name. 
       
      Criteria for passing 
      Maiden Middle Name is 
      as follows : 
       
      First Character cannot 
      be special character. 
      Value passed cannot 
      have only special 
      character. 
      Consecutive Special 
      Character are not 
maidenMiddleName No IND Single String Max allowed. The value 

passed should contain at 
one alphabet 

        
      Specify the Maiden 
      Last Name. 
       
      Criteria for passing 
      Maiden Last Name is 
      as follows : 
maidenLastName No    Max  
 IND Single String  First Character cannot 
     be special character. 
     Value passed cannot 
     have only special 
     character. 
     Consecutive Special 
     Character are not 

  allowed. The value 

This Document is marked as L1(Open) 

 42 



 

passed should contain at 
one alphabet 

        
      Specify the Maiden 
      Middle Name. 
       
      Criteria for passing 
      Maiden Middle Name is 
      as follows : 
       
      First Character cannot 
      be special character. 
      Value passed cannot 
      have only special 
      character. 
      Consecutive Special 
      Character are not 
maidenMiddleName No IND Single String Max allowed. The value 

passed should contain at 
one alphabet 

        
      Specify the Maiden 
      Last Name. 
       
      Criteria for passing 
      Maiden Last Name is 
      as follows : 
       
      First Character cannot 
      be special character. 
      Value passed cannot 
      have only special 
      character. 
      Consecutive Special 
      Character are not 
maidenLastName No IND Single String Max allowed. The value 

passed should contain at 
one alphabet 

This Document is marked as L1(Open) 

 43 



 

 
        
relatedPersonCountf     Specify the count of 
orCKYC No Both Text Field String Related people related 

to customer 

rmName No IND Multiple String 200  Name of the 
 Relationship Manager 

associated with the 
client 

employeeCode No IND Text Field String 50  Employee Code of the 
Relationship Manager 
associated with the 
client.  

rmType No IND     The designation of the 
Enum RM in the organization 

fromDate No IND Single String   The start date since 
the RM Relation 
started with the client 

toDate  No IND Single String   The End date on which 
the RM Relation ended 
with the client 

 No  Single String   Specify the branch 
branchCode code of the branch the 

customer is associated 
with. 

nationalId No IND Single String   Will search across all 
the identification 
numbers present 

        
      Description about the 
      customer or any other 
notes No Both Text Field String 800 additional information to 

be described here. 

This Document is marked as L1(Open) 

 44 



 

 
 
 

3.3. Related Person Data Fields (Only when Legal Entity) 
 

       
     Mandatory  
     for  
     Purpose  
 Mandatory Values  Length (01,02,03,  

Field Name / JSON Tag (Yes/No) Allowed Datatype 04,05) Remarks 

       
      SourceSystemCustomerCo
sourceSystemCustomerCode Yes     de is a unique ID assigned 

   01,02,03,0 by the source system to 
Single String Max 4,05 the 

customer. 
       
    The date on which the 
    customer record was 
    created in the client’s 
    Source System, or the 
    client was on boarded. 
     
    Format should be "DD-

sourceSystemCustomerCrea  Single String MMM- 
tionDate No YYYY". Should not be a 

Future Date. 
       
     Unique number 
uniqueIdentifier     assigned to the 

No Single String Max customer to uniquely 
identify the record. 

      The Initial Id assigned to a 
ApplicationRefNumber No    Both   Single  String prospect before Onboarding 

in a Client’s Source System. 

       
    01,02,03,0 Defines the record type of 

constitutionType No Single Enum 4,05 the Customer. 
 
Should be as per 
TrackWizz Code. 

This Document is marked as L1(Open) 

 45 



 

 
       
    Specify the title of 
    the Customer 
    Name. 
     

prefix No Single Enum Should be as per 
TrackWizz Code. 

       
      Specify the First Name of 
      the Customer. 
       
      Criteria for passing First 
      Name is as follows : 
       
      First Character cannot be 
      special character. Value 
      passed cannot have only 
      special character. 
      Consecutive Special 
     Character are not allowed. 

No 01,02,03,0 The value passed should 
firstName Single String Max 4,05 contain at one 

alphabet 

       
     Specify the Middle Name 
     of the Customer. 
      
     Criteria for passing Middle 
     Name is as follows: 
      
     First Character cannot be 
     special character. Value 
     passed cannot have only 
     special character. 
     Consecutive Special 

 Character are not allowed. 
middleName No Single String Max The value passed should 

contain at one 
alphabet 

       
     Specify the Last Name of 
     the Customer. 
      
     Criteria for passing Last 
     Name is as follows : 
      
     First Character cannot be 

 special character. Value 
lastName No Single String Max passed cannot have only 

special character. 
Consecutive Special 

This Document is marked as L1(Open) 

 46 



 

Character are not allowed. 
The value passed should 
contain at one 
alphabet. 

       
     Specify the Alias name of the 
alias No Single String 200 customer if any. 

       
    Specify the title of 
    Customer's Father. 
     
fatherPrefix No Single Enum Should be as per Trackwizz 

Code. 
       
     Specify the First Name of 
     the Customer's Father. 
      
     Criteria for passing Father 
     First Name is as follows : 
      
     First Character cannot be 
     special character. Value 
     passed cannot have only 
     special character. 
fatherFirstName  Single String Max Consecutive Special 

 Character are not allowed. 
No The value passed should 

contain at one 
alphabet 

This Document is marked as L1(Open) 

 47 



 

 
       
     Specify the Middle Name 
     of the Customer's Father. 
      
     Criteria for passing Father 
     Middle Name is as follows: 
      
     First Character cannot be 
     special character. Value 
     passed cannot have only 
  special character. 

No Single String Max Consecutive Special 
fatherMiddleName Character are not allowed. 

The value passed should 
contain at one 
alphabet 

       
     Specify the Last Name of 
     the Customer's Father. 
      
     Criteria for   passing   Father 
     Last Name is as follows : 
      
     First Character cannot be 

fatherLastName No Single String Max special character. Value 
passed cannot have only 
special character. 
Consecutive Special 
Character are not allowed. 
The value passed should 
contain at one 
alphabet 

       
     Specify the First Name of 
     the Customer's Spouse. 
      
     Criteria for passing Spouse 
     First Name is as follows: 
     First Character cannot be 
     special character. Value 
     passed cannot have only 
spouseFirstName No Single String Max special character. 

Consecutive Special 
Character are not allowed. 
The value passed should 
contain at one alphabet 

This Document is marked as L1(Open) 

 48 



 

 
       
     Specify the Middle Name 
     of the Customer's Spouse. 
      
     Criteria for passing Spouse 
     Middle Name is as follows : 
      
  First Character cannot be 

No Single String Max special character. Value 
spouseMiddleName passed cannot have only 

special character. 
Consecutive Special 
Character are not allowed. 
The value passed should 
contain at one alphabet 

       
     Specify the Last Name of 
     the Customer's Spouse. 
      
     Criteria for passing Spouse 
 No  String Max Last Name is as follows : 
spouseLastName Single  

First Character cannot be 
special character. Value 
passed cannot have only 
special character. 
Consecutive Special 
Character are not allowed. 
The value passed should 
contain at one 
alphabet 

       
    Specify the title of 
    the Customer's 
    Mother. 
motherPrefix No Single Enum  

Should be as per TrackWizz 
Codes. 

This Document is marked as L1(Open) 

 49 



 

 
       
     Specify the First Name of 
     the Customer's Mother. 
      
     Criteria for passing Mother 
     First Name is as follows : 
      
     First Character cannot be 
     special character. Value 

 passed cannot have only 
motherFirstName  Single String Max special character. 

No Consecutive Special 
Character are not allowed. 
The value passed should 
contain at one 
alphabet 

       
     Specify the Middle Name of 
     the Customer's Mother. 
      
     Criteria for passing Mother 
     Middle Name is as follows : 
      
     First Character cannot be 
     special character. Value 

 passed cannot have only 
motherMiddleName  Single String Max special character. 

No Consecutive Special 
Character are not allowed. 
The value passed should 
contain at one 
alphabet 

       
     Specify the Last Name of 
     the Customer's Mother. 
      
     Criteria for passing Mother 
     Last Name is as follows: 
      
     First Character cannot be 
motherLastName  Single String Max special character. Value 

No passed cannot have only 
special character. 
Consecutive Special 
Character are not allowed. 
The value passed should 
contain at one alphabet 

 
 

This Document is marked as L1(Open) 

 50 



 

       
gender No Single Enum Specify the Gender of 

the customer. 

      Date on which the 
     customer was born. 
     Format should be "DD-
     MMM- YYYY" 
      
 dateofBirth No Single String 01,02,03, For Non-Ind pass date in 

04,05 "Date of Incorporation" 
field. 

       
workEmail No Single String Max 01,02,03, Specify the Work email of 

04,05 the customer. 

       
personalEmail No Single String  01,02,03, Specify the Personal email 

Max 04,05 of the customer. 

       
    Country where the 
    customer permanently 
    resides. 
permanentAddressCountry No Single Enum  

Should be as per 
TrackWizz Codes. 

       
permanentAddressOtherState No Single String 200 

       
  Single  Specify the ZipCode of 

permanentAddressZipCode No  PermanentAddress of 
String the Customer. 

       
     Specify the Permanent 
     Address details of the 
     Customer.  
 No    Criteria for passing 
permanentAddressLine1 Single String Max Permanent Address Line1 is 

as follows : 
 

First Character 
cannot be special 
character Value 
passed cannot have 
only special 
character. The value 
passed should 
contain at least one 
alphabet. 

This Document is marked as L1(Open) 

 51 



 

 
       
     Specify the 
     Permanent Address 
     details of the 
     Customer. 
      
     Criteria for passing 
     Permanent Address Line2 is 
     as follows : 
      
  First Character cannot be 

No Single String Max special character Value 
permanentAddressLine2 passed cannot have only 

special character. The value 
passed should contain at 
least one alphabet. 

       
     Specify the 
     Permanent Address 
     details of the 
     Customer. 
      
     Criteria for passing 
     Permanent Address Line3 is 
     as follows : 
      
     First Character cannot be 
     special character Value 
     passed cannot have only 

No special character. The value 
permanentAddressLine3 Single String Max passed should contain at 

least 
one alphabet. 

       
permanentAddressDistrict No Single String Specify the District where 

the Customer resides. 

       
 No Single String Specify the City where 

permanentAddressCity the Customer 
permanently resides. 

This Document is marked as L1(Open) 

 52 



 

 
       
    Specify the State where 
    the customer 
 No Single Enum permanently resides. 

permanentAddressState  
Should be as per 
TrackWizz Codes. 

      Specify the State where 
    the customer resides if its 
    other then the TrackWizz 

permanentAddressOtherState No Single 200 provided Codes. 

       
    The Address Proof 
    document code submitted 
    by the customer for 
    verification of permanent 
    address. 
 No    

permanentAddressDocument Single Enum Should be as per 
TrackWizz Codes. 

       
     Users will be required to 
     enter the value under this 
     field only if the value under 
     the 

permanentAddressDocumentOthers No Text   PermanentAddressDocume
Value Field String Max nt 

is passed as "OthersPOA". 
       
    Specify the 
    CorrespondenceAddressCoun 
    try of the Customer. 

correspondenceAddressCountry No Single Enum  
Should be as per 
TrackWizz Codes. 

       
    Specify the ZipCode of 

correspondenceAddressZipCode No Single String Correspondence Address 
of the Customer. 

This Document is marked as L1(Open) 

 53 



 

       
     Specify the 
     Correspondence Address 
     details of the Customer. 
      
     Criteria for passing 
     Correspondence Address 
     Line1 is as follows : 
      
  First Character cannot be 

No Single String Max special character Value 
correspondenceAddressLine1 passed cannot have only 

special character. The value 
passed should contain at 
least 
one alphabet. 

       
     Specify the 
     Correspondence Address 
     details of the Customer. 
      
     Criteria for passing 
     Correspondence Address 
     Line2 is as follows: 
      
     First Character cannot be 
correspondenceAddressLine2     special character Value 

No Single String Max passed cannot have only 
special character. The value 
passed should contain at 
least 

one alphabet. 
       
     Specify the 
     Correspondence Address 
     details of the Customer. 
      
     Criteria for passing 
     Correspondence Address 
     Line3 is as follows: 
correspondenceAddressLine3 No Single String Max  

First Character cannot be 
special character Value 
passed cannot have only 
special character. The value 
passed should contain at 
least 

one alphabet. 

This Document is marked as L1(Open) 

 54 



 

 
       
 No   Specify the District of 
correspondenceAddressDistrict Single String Correspondence Address 

of the Customer. 

       
 No Single  Specify the City of 
correspondenceAddressCity String Correspondence Address 

the Customer. 

       
    Specify the State of 
    Correspondence Address 
 No  Enum of the Customer. 
correspondenceAddressState Single  

Should be as per 
TrackWizz Codes. 

      Specify the 
     Correspondence State of 
     the Customer if other 
correpondenceAddressStateOther No Single String 200 than 

TrackWizz provided values. 

       
    Address Proof document 
    code submitted by the 
    Customer for verification of 
    correspondence address. 
correspondenceAddressDocument No Single Enum  

Should be as per 
TrackWizz Codes. 

       
    Specify the Country for 
    which the customer holds 
    the citizenship. Multiple 
    commas separated values 
citizenships No Multiple Enum can be passed. 

 
Should be according to 
TrackWizz Codes 

This Document is marked as L1(Open) 

 55 



 

 
       
    The Nationality of the 
    Customer will be passed. 
    Multiple comma 
    separated values can be 
nationalities No Multiple Enum passed. 

 
Should be according 
to TrackWizz Codes 

       
    Country in which 
    the customer 
    resides. 

countryOfResidence No Single Enum  
Should be according 
to TrackWizz Codes 

       
    Country where the 
    customer was born. For 
    Non-Ind pass country in 
    the "Country of 

countryOfBirth No Single Enum Incorporation" field. 
 

Should be as per 
TrackWizz Codes.. 

      The city where the 
    customer was born. 
    For Non-Ind pass city in "City 
    of Incorporation" field. 

birthCity No Single Enum Should be as per TrackWizz 
Codes. 

taxDetailDtoList  No  Array   Refer Table 1.3 
 (Complex) 

This Document is marked as L1(Open) 

 56 



 

 
       
    The name of the country 
    that issued the customer's 
    passport. 
 No Single Enum  
passportIssueCountry Should be as per 

TrackWizz Code.. 
       
      Specify the 
     01,02,03, Customer's passport 
passportNumber No Single String 100 04,05 number if its 

available. 
       
    Specify the date from 
    which the Customer's 
    passport will no longer be 
    applicable. 
    Should be of "DD-
    MMM- YYYY" format . 
passportExpiryDate No Single String  

Should always be a 
Future Date value. 

gstinDtoList  No   Array      Refer Table 1.2 
  (Complex)  

This Document is marked as L1(Open) 

 57 



 

 
       
     Specify the Customer's 

voterIdNumber No Single String Max Voter Id number if 
available. 

       
      Specify the Customer’s 

drivingLicenseNumber No    01,02,03, Driving License 
Single String Max 04,05 Number if available 

       
    Specify the date from which 
    the Customer's Driving 
    License will no longer be 
    applicable. Should be of 
    "DD- MMM-YYYY" format . 

drivingLicenseExpiryDate No Single String Should 
always be a Future Date. 

       
     Aadhaar is a 12 digit 
     individual identification 
     number issued by the 
     Unique Identification 
     Authority of India on behalf 
     of the Government of India. 
      
     Pass 12 digit Aadhaar 
     number (123456789121) 
     or with pass 8X with last 4 

aadhaarNumber No Single String Max digits of the 
customer’s Aadhaar number 
(XXXXXXXX1234) OR last 4 
digits of the customer’s 
Aadhaar number (1234) 

       
     Aadhaar Data Vault is a 
     centralized storage for all 
     the Aadhaar numbers 
     collected by the 
     AUAs/KUAs/Sub-AUAs/ or 
     any other agency for specific 
aadhaarVaultReferenceNumber  Single String Max purposes under Aadhaar Act 

No and regulation. 
       
     NREGA Number of the 
nregaNumber No Single String Max Customer. 

This Document is marked as L1(Open) 

 58 



 

       
     The value of the 
     CKYC Identification 
nprLetterNumber No Single String Max type NPR 

Letter. 

       
      Director Identification 
      Number or DIN is a 
      registration required for 
     01,02,03, any person who wishes to 
directorIdentificationNumber No Single String Max 04,05 register 

a company. 

       
    Form for declaration to be 
    filed by an individual or a 
    person (not being a 
    company or firm) who does 
    not have a permanent 
    account number (PAN). 
    If FormSixty is passed, the 
    value passed in this field 
    should be "1" and if 
    FormSixty is not passed, the 
 No   value 
formSixty Single String passed here should be "0" 

This Document is marked as L1(Open) 

 59 



 

 
       
      Specify the Pan 
      number of the 
      customer. If Pan 
      number is passed 
      the value in the 
     01,02,03, Form Sixty field 
pan No Single String Max 04,05 should be "0" and if 

Pan is not passed, 
the value passed 
here should be "0". 

       
     Identification number 
     provided to the customer 
 No    who has completed the 
ckycNumber Single String Max KYC 

process. 
       
     Document to be submitted 
     to establish the Identity of 
 No    the Customer. 
identityDocument Single Enum   

 Should be as per TrackWizz 
100 Code. 

       
    Specify if   the   customer   is 
 No Single  Politically Exposed or not. 
politicallyExposed Enum Values should be passed as 

"0" and "1" 
0 : No and 1: Yes". 

       
    Define the classification type of 
    PEP customer. Multiple comma 
 No   separated values can be 
 Multiple Enum passed. Should be as per 
politicallyExposedClassification TrackWizz Code. 
      

Define if the customer is 
    

known for some 
    

unfavorable act or 
    

quality.Values should be 
adverseReputation No Single String 

passed as "0" and "1" 
0 : No and 1: Yes". 

This Document is marked as L1(Open) 

 60 



 

 
       
    Classify the 
    customer according 
    to the type of bad 
    reputation 
    customer has. 
     
adverseReputationClassification  Multiple Enum Multiple commas 

No and separated values 
can be passed. 
Should be as per 
TrackWizz Code. 

       
     Additional details of 
 No Text   customer if under 
adverseReputationDetails Field String 400 classified as adverse 

reputation if any. 
       
     The type of 
     Screening Profile 
screeningProfile No Single String 100 used for the 

Customer for 
Screening. 

       
     This will be used for 
     Screening to send the 
     document in the 
     response when there 
screeningReportwhenNil No Single String 100 are no hits. Values 

passed will be "0" or 
"1". 

       
     The type of Risk Profile 
riskProfile No Single String 100 used for the Customer 

       
     This will be used 
     for RiskRating to 
     send the 
     document in the 
riskRatingReportWhenLow No Single String 100 response when 

the risk is low. 
Values should be passed 
as 0" and "1" 
0 : No and 1: Yes". 

       
    Should be as per 
tags No Multiple Enum TrackWizz Code. 

This Document is marked as L1(Open) 

 61 



 

       
     Description about the 
     customer or any other 
 No Text Field   additional information 
notes String 800 to be described here. 

 
 
 
 
 

*******.1  Table 1.1 regAmlRiskSpecialCategoryDtoList 
 
 Field Name/Json Tag Applicable Data Remarks 
 for Both Type 

 IND/LE 

 RegAMLRiskSpecialCategory Both Enum Define the category of AML Risk assigned 
to the on boarding customer. 

 
Should be as per TrackWizz Code. 

 
 

regAMLRiskSpecialCategoryStartDat Both String The date on which the AML Risk Category 
 e was assigned to the on boarding customer. 
 Format should be “DD- MMM-YYYY” 
 
 

*******.2  Table 1.2  gstDtoList 
 

Field Name/Json Tag Applicabl Data Length Remarks 
e for Both Type 
IND/LE 

 Both String 15 GSTIN (Goods and Services Tax Identification 
Number) is a unique 15-digit identification 

gstinNumber 
number assigned to every taxpayer (primarily 
dealer or supplier or any business entity) 
registered the GST regime. 

GSTINStartDate Both String  Specify the date on which the customer was 
allotted the GSTIN (Goods and Tax Identification 
Number) 
It should be the DD-MMM- YYYY format.  Should 
always be a Past Date or Current Date 

GSTINEndDate  Both String  Specify the end date of the GSTIN allocated to the 
customer. 
It should be the DD-MMM- YYYY format. 
Should always be a Future Date. 

 

 

 

 

This Document is marked as L1(Open) 

 62 



 

 
*******.3 Table 1.3 taxDetailDtoList 

 
Field Name/Json Tag Applicabl Data Length Remarks 

e for Both Type 
IND/LE 

taxResidencyCountry Both Enum  Country for which the customer pays the 
taxes.Should be as per TrackWizz Code. 

taxResidencyStartDate Both String  The date from which the customer is eligible 
to pay Tax in the residence country. 

taxResidencyEndDate Both String  The date from which the customer is ending 
their business/ operations in the residing 
country. 

taxIdentificationNumber Both String Max Specify the Tax Identification Number of the 
customer. 

This Document is marked as L1(Open) 

 63 



 

 
 

3.4. Relation Data Fields (Only when Legal Entity) 
 

 Mandatory     
(Yes/No) Value    

Field Name / JSON s Datatyp Length Remarks 
Tag Allow e 

ed 
 Yes     

customerSourceSys    Code of the customer in the specific source 
temCode Single String Max system from where the information flows in. 

 Yes     
relatedPersonSource    Code of the related person in the specific 
SystemCode Single String Max source system from where the information 

flows in. 
customerApplication No Single Single  The Initial Id assigned to a prospect before 
RefNumber  Onboarding in a Client’s Source System. 

relatedPersonApplic No Single Single  The Initial Id assigned to a Related person before 
ationRefNumber  Onboarding in a Client’s Source System. 

 Yes     
relationCode   Code that defines the relation of the related 

Single Enum person with the customer. 

 No     
    Date when the relation of the customer was 

relationStartDate   established with the Related person Format 
Single String should be "DD-MMM-YYYY". 

 No     
    Date when the relation of the customer ended 

relationEndDate Single String with the Related person. 

 No     
shareHoldingPercen      
tage Single String 100 ShareHoldingPercentage for a given Relation. 

 No     
     Describe any additional information for relation of 

relationNotes Text String 500 the customer and related person if any. 

This Document is marked as L1(Open) 

 64 



 

 

4. Sample Decrypted Request & Response for Individual: 

Content-Type: application/json 
 

4.1. For Initial Screening (Purpose: 01) 

4.1.1. Sample No Hits Found (Request & Response) 
Request 

{ 
 

"requestId": "IND2550", 

"sourceSystemName": "FINACLE", 

"purpose": "01", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", "segmentStartDate": "11-

Feb-2022", "status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 
 
"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", "kycDateOfDeclaration": "11-

Mar-2021", "kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

 



 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", "regAMLRisk": 

"1", "regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month", 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", 

 

"networthEffectiveDate": "11-Feb-2019", "networthDescription": "Total 

networth income of a year", "networthDocument": 

"NetworthCertificate, BalanceSheet", "familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

 



 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", "educationalQualification": "1, 4", 

"countryOfOperations": "IND, USA", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019" 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"constitutionTypeId": 0, 

"sourceSystemCustomerCode": "2550", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2550", 

"prefix": "Dr", 

"firstName": "Hansraj", "middleName": 

"Gitesh", "lastName": "Hemani", 

"fatherPrefix": "Mr", 

"fatherFirstName": "Gitesh", 

"fatherMiddleName": "Sambhaji", 

"fatherLastName": "Hemani", 

"spousePrefix": "Mrs", 

"spouseFirstName": "Sonali", 

"spouseMiddleName": "Hansraj", 

"spouseLastName": "Hemani", 

"motherPrefix": "Mrs", 

 



 

"motherFirstName": "Jayaprabha", 

"motherMiddleName": "Gitesh", 

"motherLastName": "Hemani", 

"gender": "01", 

"dateofBirth": "11-Feb-1995", 

"workEmail": "<EMAIL>", 

"personalEmail": "<EMAIL>", 

"personalMobileISD": "91", 

"personalMobileNumber": "9950438478", 

"workMobileISD": "91", 

"workMobileNumber": "7330067911", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "Gokulnagri, Chawl no 15, Room no- 101", 

"permanentAddressLine2": "Near MJ College", "permanentAddressLine3": "Behind 

RK Hotel, Mumbai", "permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", 

"correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", "correspondenceAddressLine3": 

"Mahim West", "correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", "correspondenceAddressState": "MH", 

"correspondenceAddressDocument": "UtilityBill2m", "countryOfResidence": 

"IND", 

"countryOfBirth": "IND", "birthCity": 

"Mumbai", "passportIssueCountry": 

"IND", "passportNumber": 

"PASS38142", 

"passportExpiryDate": "02-Feb-2025", 

"voterIdNumber": "VOTE78456", 

 



 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", "nprLetterNumber": 

"NPR25689", "directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", "ckycNumber": 

"80080070068592", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "Involved in Money Laundering Crimes", 

 

"notes": "Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": "SP1", 

"screeningreportwhennil": "1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2550", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026” 

} 

], 
 

"politicallyExposedClassification": "1, 2", 

"citizenships": "IND, GBR", "nationalities": 

"IND, GBR", "documents": null 

} 

], 

 



 

 
"GUID": null 

} 

 
Response  

{ 
"RequestId": "IND2550", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2550", " 

applicationRefNumber":"AJNPC4556", 

   "ValidationOutcome": "Success", 
 
   "SuggestedAction": "Proceed",  
  "PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", "PurposeCode": "01", 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{ 

"HitsDetected": "No", 

"HitsCount": 0 "ConfirmedHit": 

"No", "ReportData": Base 64 

Data, 

} 

] 

}, 
"ValidationCode": "", 

"ValidationDescription": "", 

 



 

"ValidationFailureCount": 0 

}, 

], 

"RelatedPersonResponse": null, 

"RelatedPersonRelationResponse": null 

} 

] 

} 

} 
 
 

4.2. Initial & Continuous Screening (Purpose: 01 & 04) 
 

4.2.1. Sample No Hits Found (Request & Response) 
 Request : 

{ 
 

"requestId": "IND2550", 

"sourceSystemName": "FINACLE", 

"purpose": "01,04", "customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", "segmentStartDate": "11-

Feb-2022", "status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", "kycDateOfDeclaration": "11-

 



 

Mar-2021", "kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", "regAMLRisk": 

"1", "regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month",  
 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", "networthEffectiveDate": 

"11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

 



 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", "educationalQualification": "1, 4", 

"countryOfOperations": "IND, USA", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019" 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2550", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2550", 

"prefix": "Dr", 

"firstName": "Hansraj", 

"middleName": "Gitesh", 

"lastName": "Hemani", 

"fatherPrefix": "Mr", 

"fatherFirstName": "Gitesh", 
 



 

"fatherMiddleName": "Sambhaji", 

"fatherLastName": "Hemani", 

"spousePrefix": "Mrs", 

"spouseFirstName": "Sonali", 

"spouseMiddleName": "Hansraj", 

"spouseLastName": "Hemani", 

"motherPrefix": "Mrs", 

"motherFirstName": "Jayaprabha", 

"motherMiddleName": "Gitesh", 

"motherLastName": "Hemani", 

"gender": "01", 

"dateofBirth": "11-Feb-1995", 

"workEmail": "<EMAIL>", 

"personalEmail": "<EMAIL>", 

"personalMobileISD": "91", 

"personalMobileNumber": "9950438478", 

"workMobileISD": "91", 

"workMobileNumber": "7330067911", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "Gokulnagri, Chawl no 15, Room no- 101", 

"permanentAddressLine2": "Near MJ College", "permanentAddressLine3": "Behind 

RK Hotel, Mumbai", "permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", "correspondenceAddressZipCode": 

"403702", "correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", "correspondenceAddressLine3": 

"Mahim West", "correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", "correspondenceAddressState": "MH", 

"correspondenceAddressDocument": "UtilityBill2m", "countryOfResidence": 

"IND", 
 



 

"countryOfBirth": "IND", 

"birthCity": "Mumbai", "passportIssueCountry": 

"IND", "passportNumber": "PASS38142", 

"passportExpiryDate": "02-Feb-2025", 

"voterIdNumber": "VOTE78456", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", "nprLetterNumber": 

"NPR25689", "directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", "ckycNumber": 

"80080070068592", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "Involved in Money Laundering Crimes", "notes": 

"Onboarding of Customer", 

"tags": "2,3", "screeningProfile": 

"SP1", 

"screeningreportwhenNil":"1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2550", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026” 

}  
], 

 
"politicallyExposedClassification": "1, 2", 

 



 

"citizenships": "IND, GBR", "nationalities": 

"IND, GBR", "documents": null 

}  
], 

 
"GUID": null 

} 
 

Response  

{ 

"RequestId": "IND2550", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2550",  

   “applicationRefNumber” : “AJNPC45568” 
"ValidationOutcome": "Success", 

"SuggestedAction": "Proceed", 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", "PurposeCode": "01", 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{ 

"HitsDetected": "No", 

"HitsCount": 0 "ConfirmedHit": 

"No", "ReportData": Base 64 

Data, 

} 

 



 

] 

}, 
 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

{ 
 

"Purpose": "Continuous Screening with email linked cases", 

"PurposeCode": "04", 

"Data": null, "ValidationCode": 

"", "ValidationDescription": "", 

"ValidationFailureCount": 0 

} 

], 
 

"RelatedPersonResponse": null, 

"RelatedPersonRelationResponse": null 

} 

] 

} 

} 
 
 
 
 
 
 
 
 
 
 
 
 
  

 



 

5. Response Field Description 
5.1. Main Data Fields 

 
 Datatype  
Field Name Remarks 

 String  
RequestId As available in the request 

 String TrackWizz codes for validations 
ValidationCode 

 String Description of the validation message 
that the user receives for a particular 

ValidationDescription validation 

 String  
OverallStatus Possible Values: 

AcceptedbyTW/RejectedbyTW 
 

5.2. Screening Specific Fields (Customer & Related Person) 
 

 Datatype  
Field Name Remarks 

   
SourceSystemCustomerCode String As available in the request 

   
ValidationOutcome String Possible Values: Success/Failure 

 
  
Purpose String Defines the name of the purpose 

 
  
PurposeCode String As available in the request 

   
ValidationCode String TrackWizz codes for validations 

 
  

String 
 Description of the validation message that the user receives for a 
ValidationDescription particular validation 

   
ValidationFailureCount number The total number of failure Count in a request 

String 
  
 Specifies if hits are detected for a customer. Possible values are 
  
HitsDetected Yes/No 

number 
  
 The total number of hits detected for a customer who is screened 
HitsCount against various sources. 

 A serial number generated by the system when any hits are  
 

String found for a given customer or a related person. 
 
CaseId 

 



 

 A Case URL generated by the system through which specific  
 

String actions can be taken when any hits are found for a given  
 

customer or a related person. 
CaseURL 
  Specifies whether the alert generated is a Confirmed Hit or Not.  
 String Possible values are  
 Yes/No 
ConfirmedHit 

 TrackWizz suggested action to be taken for the customer or  
String related person based on the hit generated. Possible values are  

Stop/Review/Proceed  
1. Stop- When it’s a confirmed hit.  
2.Review- When it’s a probable hit.  

SuggestedAction 3. Proceed- When there are no hits 
 Will have a Base64 Encoded Report link of the case details. 
String Base64 

 
ReportData 

 Contains the name of the Source against which the hits are detected  
String for the customer. 

 
 
Source 

 TrackWizz Id for that Watchlist record. 
String 

WatchlistSourceID 
 Defines the match type of the hit detected for the customer or  
String related party. Possible values are Probable/Confirmed 

 
 
MatchType 

 A system generated score depending on the configurations in the  
number selected Screening Profile. 

 
 
Score 

 List of attributes which were found under Confirmed Type Match. 
String 

 
ConfirmedMatchingAttributes 

 
 
 
 
 
 
 
 
 
 
 
 

 



 

6. Steps for Encryption & Decryption 
6.1 Encryption  

Following are the steps to be followed for encryption: 

1. Generate a Session Key of 256 bits. 
2. Encrypt the request data using AES (Symmetric 128 bits) algorithm with the above generated Session Key 
3. Encode the above encrypted data to Base64 string. 
4. Encrypt the session key using the Public Key of the Receiver, using RSA (Asymmetric Algorithm). 
5. Encode the above encrypted Session Key to Base64 string. 
6. Combine the encrypted data and encrypted session key into a string and then sign that string using the sender's 

private key. 
7. Add this encrypted and encoded data, session Key and signature to the request. 
8. Note: Key sizes acceptable: - 1024,2048 bits 

 
6.1.1. Sample Encrypted Request 

“EncryptedData”: 

“zIVIfsGjG7P/RlXY3LjkpeGs74K1RuGp/ktICXE9fTJsjDpWoPRea1n0syu5OtxjEvCzDF6/qpZz2LHyZWKrL/Yjbn 
JVdywL3z+WbKg2lOT9zgZKQ7pYCv7az5YReWNICztU/grRYAfDe1K0nBVG/biCn0C6Q64vL7PIR9yfimrkpkjh/ 
6mF8H4i0HfvldRWEBq84FZqU8QlLr+Kid6JlV0OBVnUaokb3z9EeshdbSMJtN1F2D8INCCUzlPdpWX+69rD1y  
K0tSd48PEicFpTgPmHlZ46obTZv8Z2fx+YHiLAKEZAFSwtyDE1/ykBLX4W1ep8XlcXTMkmAhZtY/Xbaq5hR9zd 
ErZQcskaSrz+dVW/DoCpYlbieElUNqiPpp4KUQbeSS9O224IVuAlK0V1Hf0TwuhqU1o5DW17e+u3JD1+6hlcX 
ZFIcSJ2xljnMzKafF+F+ +xwpC 
6OZLtYsj403qucKZPluEOrUYIugcqcFNBR6ry6whd3nszw6EqrzUfV9T/bssyuRCKJw05m4v2PXbEWG3pG+u/ 
eYCf0dtXTLfDNvjtaW2+IxBafa9oSDxJo/GYiQdI4a5HMHZi4ZHBHiKzhJ24FpeiVsaDF8TtPwJcXKj0VMV+CHgu  
EBICRPgl4VP3hv8Eod1AY7QHW86Wd4qNzBVZkXI6N9iDRrQLxUFLi2wLlnRFgeYq1yuiaVRQEnl8Os1j3 
bZz6vLJCBjFBLK4uRpe+nJX+M4naEGdtxS+kqVy9sD5S4RrscHD5vZG9OWVXZM17v84tuvF5qCA6bhMh 
TIBirR7g66sFFDpTvrgSi8N65eqSm5fJNgYiR3cJon 
YKb2urvkAaoZpxraPSwa+Lr2BK2fCrS5b7Jmndnf8abSDFUH3RdpeHuW3maehRHJ+wZac47Gjhj3yxePNbdE 
pMGHhyNMnfOIFxoshCbUEpScbztXt6FzuqEH0UsUOH8bLjfEOk2LseWswn3OuHtLY9K7VApM/Ddj0f2Pgi0v ", 

"EncryptionKey": "heevc+FOSSf4CBwmkkvU11KFXljS3u+mxMbPewez/8QU30hVo96db44KMRYPuENvNYGCWGqJSdltNQxB 
/k3gA3eBkDtCYmgYn0ptAUiGY/N5VSID13U6zjqWLmWcDLfgitzaguPmcjSGyVTQ99aNNVBvmxx+Jq54V2Pa 
2cCUODee3l7Efz3xMIZ3PqXKW3Dzb1UFPoyZROdekXa9U7NGDsSssPNL+6KLDLf+WqgZQnOb+x/wnHkBV3 
GQtR00edh6Wyioo5Rf7VZs69+L2CNAcu9ERw621cIrh+GNxaOcbQE67bjPorGkSojW5GLBFvoGByeC5ojViPZ 
wsyzfkCHfJ0BIZyMaKYL7Dsh/ /q8iTb/kqpjjereBeIh 
gMHY6Rfrs5u65gmFR57elcwVeZSOYfs0oFhulpkyNpiveoCuAJ8UggpVPTYERZcWsZlVr2+HmL7UEiBMq01lk 
Ph+5765xGzuFOSf9kGXqTQUd+KYROyU+HFxYZ4e5QY+T0NC+ogJnQvu2YqvtjMDJ5jbuCuMqeEsSh3gM3u 
3qfW/bSw7+eK6Rw841XPZdLwxl9YRWuopcxXBOTXNVPg29kVm8MQ6Pa33N9k7Ya+kQYNVJ16QmJyN4n 
CkzPs4N29kkRr9X3jrCylRBfWUIDjT1avxQyJDsyT/xsKrEi8n3qwCnwV9W7y3EH0Ne4iTEYQCg0twgntsAWYh 
UWWm1yPr9p+P6JZOyEFzJcSZMVlqAAd/EnLRa5Ve5xKHWhyO778/w8/yFs88NMFWoFrZi1Ej6GLyaOgMt3 
g+cMMtf0iXYLqpLuSraieeguFiTFkKPGwRaqpGX91qktXjd1NigLN3w=" 
 

 
6.1.2. Sample Encrypted Response 

"EncryptedData": "JnurJtyWpzvSzBgCcVzX6KhAMHXlgqB0WrA85OVciMJNIRx2S25URNaWujRQYbnHQgg57RnQaSRWEIN8q 
FX/W+r0lS41g8dzTd92VDmShQoDQU/pq6vD9mxkkRWEG/seSvNkFJiZpcDrOXFqDTy++3p25OSF/0WUpQc 
+iS2/po1qjnNvN3Uw5k+onZTV8f3DrpeKWqoqg4TiVlgV51dHrhdTTGgAGBDPTmsBo+2Onmxmc6oeJQu7jC 
U3HOpFw6yjPQIDgf4ogVfmdfqKnojEsyNMUGxS6zam9fqTI24AQ4Ox3Ni8IyI+PtXttn8JgKr4+ajKxrdmZuUQ 
+0/GsuEBzIOdaiwaLSWefqaFSiS+BdBCINrW7ZzIhGMOdRbLXqMx38cP8b71jUKhgWbkd3Gcxclxd5Ueepb/y 

 



 

baMbUgZwuZpIpJPmaMbZpTMMjCLSh/c3oEERygOCXLbr4e66srMs1vBL77QG3hhf8idO9xW545FpUGU/j4 
48ca8+4ykSsI8tNvIu500xT2mpiC1cij257Qfqc9lM12r7TRYcPPJr5G466+Owl56fWPpdopCZjIwOX+Q1kF7Ay 
dF9avPVZzRnERjUYYJx4C7i29jjGXLLm6crEq+hQX8lEfkYyiGwNP0UUEDVY2cxOIaoLI1noz2sYga5Cfg+JpkIvc 
KPIYYruus+7VQgNA14vzTN50BB8RpKbAp/zTBDSQ0lXnW+iYLFvVfSDk5tNlMnWlGFPraG6VYMvqPN8oG1v 
CzKTAngaj6WOa1JVBuSmvvOVqHiQSJdA==", 

"EncryptionKey": 
"GB9XHuWfTdyz/f6yhrKLQg6oO20lFlpsGa64DKO5rbAzvQXB4ArKdqvR5WI4VR9CT+hHzyqvCSNyWZY6no2 
7xWsywlATvWku0tNsW7dwFurr188jIj68RIXsmfYfMjFbeBwWV4cwlDvnJXEfaTYs+Q6t5cIINXk0ZhYx/656pd 
OZHsleX0ghdCcyLdYBoe/xzrrvPNK2djIApkEN5tQc58BxOD5HSlt3rTzSLhv4vWi4OlK2jeXk2uzH/ffK3WAmR7 
CojnEqXw59AHrXnt00syT1XkCVJX/yI4YXEis+fQoU3PqnoexlG+APh1ZpD9DhqbhdmmukHA/zLuhuuQCFW0 
3d7sjXxJymJdz4eQux610T65DTGN0quf2mLcuioNrLrEAlE+t0rlTUwPnEBoKsl5BCtWGaC7ZRs89e5yd9P8Rgy 
+gl3rODD+ GUycJ4lrJy17BMJ0JdGRdDfopnJiqJkcDGWgwu/s/hGtcYe7oKcTGj8bh3Ipqiwlnlrew3Xq5w8LRPMyJvGwTEp 
TgILi1U/oVdXvSfbz+vgyZ4Edip4v7A24Om8iVOvXdOn7CeJ7J2oNene9MtiA2sZ1wMIK2jFItpAIElEyr3pywLrj 
dJHX1rzViSQ8117B8omGCb5ugiLtpaBx1MCGHBfw4qBK/SND28vZ6JctZ/lcpXntm7rUgUU2qsTdV4ln6XEG2 
7j2NLW46uryM+wkoU+4gcZU+ZIwCtsUFImpHj2EL1TSgvWPUQw/0YDRWrNQbStg6dlQQA7CiDCTLDD/ZKE 
LQ5WrE6JSwgO6CLhfsnw3nFFvPu7W3t0IucupBSWjN1tO5JLGIVrKTNN7hyORHC1ry/3a+v+GOGpmzHL/dq 
9rLytC1yJg+JZRfK+eqorZlpbKFyoWjU+iik8yGZQ115g4n9DJQ2kR+oLfuHWAoyudrWQmuFd4A8dcizaw5OsB 
NQIhyy+/cQ=", 

"Signature": 
"HTItYvIPL2bvYhP4zyO/BJhHBRu1hS9Yigj2CUBoS2aysyiXFePGIyGB1ZoTerjJYerg/R3k5Xj9U/hl9JC76kAueyjkGae3a7
GT2qIXha3CSuwdYjlVSscFw9yVbSCGEIgn6eB7epUC3aKwwO3Vjaf9GEbogh/ 
ez6dbPYjlknW2pb+UUbMmGFe1JZ7M0Y7Z/pXkaHKgepx8bqbKEltZaOu0fKRU0FxoSw0E9J4DhHZHRRnwPd7HwZPM
bDBKUM6sb175ivxF1rUdHLcr95I1gGYRms9faEVBuuAjtaBs88IoDyBszXobo9iFahmHVvu0jLy5/AZjXQLPadAH+ 
+XwFhXakv3bRhoQuhKWLoj85vEgtoPM1dFNkdlhUU4vFrlNokueCIRwuKuCkevurod55XCp4tqzmFIoSyRKaX4KiEKTnx
1YPbC6Apz8n5sGOAwOL78hMSTXGyNMo26Dd/y9Gzb0o7B49tB4XzLI/a/8di7pp/ 
/HIKAtcyjeUlOyxfarX2AFX95XvqoFteGN3Ohc/dqswVaxKkZu59cRQOmkSah12lylzZVTEvb2PlRzHBeiSdNOonLvFkYGz
KTQE0vJZnFnRoWuGf1IzvsGD8hszmiWGrnrwmpQqau3dpWiG0F3Z9DeQNO59VW6naYeffeV/IXRLdG9imD0cm1qZ19i
dnWZ/MI+Fto=" 

 
6.2 Decryption 

Following are the steps to decrypt and read the response: 
1. Verify the signature in Response using the Public Key of the Sender. 
2. Decode the Session Key from the Response. 
3. Decrypt the above Session Key by Receiver’s Private Key using RSA (Asymmetric Algorithm) 
4. Decode the data in Response. 
5. Decrypt the data by Session Key (derived from above) using AES (Symmetric Algorithm). 

  

 



 

7 Enumerations: 
7.1 Segment Enum 

 
  

  
Code Name 

  
1 Retail 

  
2 Wholesale 

  
3 Institutional 

  
4 PrivateBanking 

  
99 Categorized 

 
7.2    Customer and RelatedParty Status Enum 

 
  

Code Customer Status 

  
Active Active 

  
Closed Closed 

  
Dormant Dormant 

  
InActive InActive 

  
Suspended Suspended 

 

 

 

 

 

 

 

 

 

 

 



 

7.3    Constitution Type Enum 

 
  

Code Name 

  
1 Individual 

  
2 Hindu Undivided Family 

  
5 Non Resident Indian 

  
7 Foreign National 

  
8 Mutual Fund 

  
9 Trust 

  
10 Public Sector Banks 

  
11 Limited Liability Partnership 

  
13 Foreign portfolio investment Ind 

  
14 Foreign Portfolio Investors LE 

  
17 Partnership Firm 

  
18 Association of Persons (AOP)/ Body of Individuals (BOI) 

  
25 Not Specified but Ind 

  
26 Society 

  
27 Person of Indian Origin 

  
29 Public Limited Company 

  
30 Private Limited Company 

  
81 Scheduled Cooperative Bank 

  
83 Venture Capital Fund 

 



 

  
101 Sole Proprietorship 

  
102 Section 8 Company India 

  
103 Foreign Embassy or Counselor Office 

  
104 International Organization or Agency 

  
58 Central/State Government Departments/Agency 

  
59 Non Profit Organization 

  
60 Not Specified but LE 

  
68 Liquidator 

  
69 Artificial Juridical Person 

  
75 Foreign Bank 

  
78 Private Sector Bank 

110 QFI 

111  
Eligible Foreign Investor - Category I 
 

112  
Eligible Foreign Investor - Category II 
 

113 Eligible Foreign Investor - Category III 

114 Alternative Investment Fund 

115 QIB 

116 Other 

117 Charity/ NGO/ Self Help Group 

 



 

 
7.4    Gender Enum 

                          
  

Code Name 

  
01 Male 

  
02 Female 

  
03 Transgender 

04 Unknown 

 
7.5    Marital Status Enum 

                              
  

Code Name 

  
M Married 

  
U UnMarried 

  
O Others 

  
W Widow / Widower 

  
SP Separated 

  
D Divorcee 

 



 

 
7.6    Country Enum 

                         
  
Iso3DigitCode Name 

  
AFG Afghanistan 

  
ALA Aland Islands 

  
ALB Albania 

  
DZA Algeria 

  
ASM American Samoa 

  
AND Andorra 

  
AGO Angola 

  
AIA Anguilla 

  
ATA Antarctica 

  
ATG Antigua and Barbuda 

  
ARG Argentina 

  
ARM Armenia 

 



 

 
  
ABW Aruba 

  
AUS Australia 

  
AUT Austria 

  
AZE Azerbaijan 

  
BHS Bahamas 

  
BHR Bahrain 

  
BGD Bangladesh 

  
BRB Barbados 

  
BLR Belarus 

  
BEL Belgium 

  
BLZ Belize 

  
BEN Benin 

  
BMU Bermuda 

  
BTN Bhutan 

  
BOL Bolivia 

  
BES Bonaire, Sint Eustatius and Saba 

 



 

 
  
BIH Bosnia and Herzegovina 

  
BWA Botswana 

  
BVT Bouvet Island 

  
BRA Brazil 

  
IOT British Indian Ocean Territory 

  
BRN Brunei Darussalam 

  
BGR Bulgaria 

  
BFA Burkina Faso 

  
BDI Burundi 

  
CPV Cape Verde 

  
KHM Cambodia 

  
CMR Cameroon 

  
CAN Canada 

  
CYM Cayman Islands 

  
CAF Central African Republic 

  
TCD Chad 

 



 

 
   
CHL Chile 

  
CHN China 

  
CXR Christmas Island 

  
CCK Cocos Keeling Islands 

  
COL Colombia 

  
COM Comoros 

  
COD The Democratic Republic of the Congo 

  
COG The Republic of the Congo 

  
COK Cook Islands 

  
CRI Costa Rica 

  
HRV Croatia 

  
CUB Cuba 

  
CUW Curacao 

  
CYP Cyprus 

  
CZE Czech Republic 

  
CIV Ivory Coast 

 



 

 
  
DNK Denmark 

  
DJI Djibouti 

  
DMA Dominica 

  
DOM Dominican Republic 

  
ECU Ecuador 

  
EGY Egypt 

  
SLV El Salvador 

  
GNQ Equatorial Guinea 

  
ERI Eritrea 

  
EST Estonia 

  
SWZ Eswatini 

  
ETH Ethiopia 

  
FLK Falkland Islands 

  
FRO Faroe Islands 

  
FJI Fiji 

  
FIN Finland 

 



 

 
  
FRA France 

  
GUF French Guiana 

  
PYF French Polynesia 

  
ATF French Southern Territories 

  
GAB Gabon 

  
GMB Gambia 

  
GEO Georgia 

  
DEU Germany 

  
GHA Ghana 

  
GIB Gibraltar 

  
GRC Greece 

  
GRL Greenland 

  
GRD Grenada 

  
GLP Guadeloupe 

  
GUM Guam 

  
GTM Guatemala 

 



 

 
   
GGY Guernsey 

  
GIN Guinea 

  
GNB Guinea-Bissau 

  
GUY Guyana 

  
HTI Haiti 

  
HMD Heard Island and McDonald Islands 

  
VAT Holy See 

  
HND Honduras 

  
HKG Hong Kong 

  
HUN Hungary 

  
ISL Iceland 

  
IND India 

  
IDN Indonesia 

  
IRN Iran 

  
IRQ Iraq 

  
IRL Ireland 

 



 

 
  
IMN Isle of Man 

  
ISR Israel 

  
ITA Italy 

  
JAM Jamaica 

  
JPN Japan 

  
JEY Jersey 

  
JOR Jordan 

  
KAZ Kazakhstan 

XKX  Kosovo 
  
  
KEN Kenya 

  
KIR Kiribati 

  
PRK North Korea 

  
KOR South Korea 

  
KWT Kuwait 

  
KGZ Kyrgyzstan 

  
LAO Lao People's Democratic Republic 

  
LVA Latvia 

 



 

 
  
LBN Lebanon 

  
LSO Lesotho 

  
LBR Liberia 

  
LBY Libya 

  
LIE Liechtenstein 

  
LTU Lithuania 

  
LUX Luxembourg 

  
MAC Macao 

  
MDG Madagascar 

  
MWI Malawi 

  
MYS Malaysia 

  
MDV Maldives 

  
MLI Mali 

  
MLT Malta 

  
MHL Marshall Islands 

  
MTQ Martinique 

 



 

 
  
MRT Mauritania 

  
MUS Mauritius 

  
MYT Mayotte 

  
MEX Mexico 

  
FSM Micronesia 

  
MDA Moldova 

  
MCO Monaco 

  
MNG Mongolia 

  
MNE Montenegro 

  
MSR Montserrat 

  
MAR Morocco 

  
MOZ Mozambique 

  
MMR Myanmar 

  
NAM Namibia 

  
NRU Nauru 

  
NPL Nepal 

 



 

 
  
NLD Netherlands 

  
NCL New Caledonia 

  
NZL New Zealand 

  
NIC Nicaragua 

  
NER Niger 

  
NGA Nigeria 

  
NIU Niue 

  
NFK Norfolk Island 

  
MKD North Macedonia 

  
MNP Northern Mariana Islands 

  
ANT Netherlands Antilles 

  
NOR Norway 

  
OMN Oman 

  
PAK Pakistan 

  
PLW Palau 

  
PSE Palestine 

 



 

 
  
PAN Panama 

  
PNG Papua New Guinea 

  
PRY Paraguay 

  
PER Peru 

  
PHL Philippines 

  
PCN Pitcairn 

  
POL Poland 

  
PRT Portugal 

  
PRI Puerto Rico 

  
QAT Qatar 

  
ROU Romania 

  
RUS Russian Federation 

  
RWA Rwanda 

  
REU Reunion 

  
BLM Saint Barthelemy 

  
SHN Saint Helena, Ascension and Tristan da Cunha 

 



 

 
  
KNA Saint Kitts and Nevis 

  
LCA Saint Lucia 

  
MAF Saint Martin 

  
SPM Saint Pierre and Miquelon 

  
VCT Saint Vincent and the Grenadines 

  
WSM Samoa 

  
SMR San Marino 

  
STP Sao Tome and Principe 

  
SAU Saudi Arabia 

  
SEN Senegal 

  
SRB Serbia 

  
SYC Seychelles 

  
SLE Sierra Leone 

  
SGP Singapore 

  
SXM Sint Maarten 

  
SVK Slovakia 

 



 

 
   
SVN Slovenia 

  
SLB Solomon Islands 

  
SOM Somalia 

  
ZAF South Africa 

  
SGS South Georgia and the South Sandwich Islands 

SCG  Serbia and Montenegro 
  
CRQ  Sark 

 
  
SSD South Sudan 

  
ESP Spain 

  
LKA Sri Lanka 

  
SDN Sudan 

  
SUR Suriname 

  
SJM Svalbard and Jan Mayen 

  
SWE Sweden 

  
CHE Switzerland 

  
SYR Syrian Arab Republic 

  
TWN Taiwan 

  
TJK Tajikistan 

 



 

 
   
TZA Tanzania 

  
THA Thailand 

  
TLS Timor-Leste 

  
TGO Togo 

   
TKL Tokelau 

  
TON Tonga 

  
TTO Trinidad and Tobago 

  
TUN Tunisia 

  
TUR Turkey 

  
TKM Turkmenistan 

  
TCA Turks and Caicos Islands 

  
TUV Tuvalu 

  
UGA Uganda 

  
UKR Ukraine 

  
ARE United Arab Emirates 

  
GBR United Kingdom 

 



 

 
  
UMI United States Minor Outlying Islands 

  
USA United States of America 

  
URY Uruguay 

  
UZB Uzbekistan 

  
VUT Vanuatu 

  
VEN Venezuela 

  
VNM Vietnam 

  
VGB British Virgin Islands 

  
VIR U.S. Virgin Islands 

SUN  USSR 
 
  
WLF Wallis and Futuna 

  
ESH Western Sahara 

  
YMD Yemen 

YUG Yugoslavia 
 

  
ZMB Zambia 

  
ZWE Zimbabwe 

OTH  Others 
 

 

 



 

7.7    Occupation Type Enum 

    
  

Code Name 

  
SE Business or Self employed 

  
SAL Salaried 

  
Prof Professional 

  
Agri Agriculturist 

  
Ret Retired 

  
HWF Housewife 

  
STUD Student 

  
Oth Others 

PVS Private Sector 
 

PUS Public Sector 
 

GVS Government Services 
 

 

 
7.8   Nature Of Business Enum 

                           

Code Name 

AdultIndustries Adult Industries (pornography, online dating 
etc) 

AdvertisingBroadcastingMarketingPollingPublishing Advertising, broadcasting, marketing, polling 
and publishing 

AlcoholTobaccoProducts Alcohol/tobacco products 

ABPs Alternative Banking Platforms (ABPs) 

ArchitectureDesignandLandscapping Architecture, design and landscapping 

 



 

Auctioneers Auctioneers 

Launderettes Cash Intensive - Launderettes 

ParkingGarages Cash Intensive - Parking garages 

PrivatelyownedleasedATMs Cash Intensive - Privately owned/leased 
ATMs 

RetailConvenienceStoresMarketstalls Cash Intensive - Retail & convenience stores 

SecondhandGoods/PawnBrokers Cash Intensive - Second Hand goods/Pawn 
Brokers 

TaxiFirms Cash Intensive - Taxi firms 

VendingMachineOperators Cash Intensive - Vending machine operators 

BarsPublicHouseNightclubsTakeawayrestaurants Cash Intensive - Bars, Public House, 
Nightclubs, Takeaway restaurants 

BeautyMassageParloursHairdressingSalons Cash Intensive - Beauty/Massage 
Parlours/Hair dresser salons/Nail bars 

FuelStationsGaragesCarwashfacilities Cash Intensive - Fuel Stations/Garages/Car 
wash facilities 

ChemicalProduction Chemical Production and distribution 

CollectionorTreatmentofHazardousorNonhazardousWaste Collection & /or treatment of hazardous or 
non-hazardous waste 

ComputerHightechnologyTelecomMobilePhonesales&distribution Computer / High technology / Telecom / 
Mobile Phone sales & distribution 

ConstructionBuildingCivilengineeringPublicworks Construction /Building/ Civil engineering 

CulinaryIndividuals Culinary individuals (e.g. chef/sommeliers, 
dentist) 

DealerinusedCarsTrucksBoatsPlanesMachinemotorPartsorMachinerydealer Dealer in used cars, trucks, boats, planes, 
machine/motor parts or machinery dealers 

ArtsAntiquesdealers Dealers in high value precious goods - Arts & 
Antiques dealers 

GemPreciousMetalsOrStoneDealers Dealers in high value precious goods - Gem & 
Precious metals  
or stone dealers 

Jewellers Dealers in high value precious goods 
Jewellers 

ScrapMetaldealers Dealers in high value precious goods - Scrap 
Metal dealers 

Defence Defence/Military 
(Arms/Explosives/Nuclear) 

DigitalVirtualCurrencyProviders Digital/virtual currency providers 

Emoney E-money 

Education Education such schools, universities, other 
tertiary institutions(individual professions 
such as administrator, professor, teacher,  
lecturer, researcher) 

 



 

Energysector Energy sector(extraction, refining, 
distribution including petroleum and gas / 
power generation and transmission) 

EntertainmentandArts Entertainment and arts e.g. musician, TV 
personality, photography, creative arts, 
motion pictures, radio 

EstateAgentsRealEstateBrokers Estate Agents/Real Estate Brokers/Property 
developers 
/Property Management companies 

ExtractionofPetroleumNaturalgasPreciousMetalsPreciousstones Extraction of Petroleum,Natural gas,Precious 
UraniumNonferrousMetalOres Metals,Precious stones, Uranium,Non-ferrous 

Metal Ores 

FinancialIndustries Financial industries e.g. banks, fund/asset 
managers, insurance companies (excluding 
Private equity, Wealth Management & 
Private Banking) 

Fisheries Fisheries 

GamingGamblingBusinesses Gaming, Betting & Gambling businesses 
including Casinos 

GatekeeperServiceProviders Gatekeeper service providers such as 
accountants, lawyers or  
other professionals who hold customers 
money where the identity of the underlying 
customers is not disclosed 

HealthcareManufactureofMedicalSupplies Healthcare / Manufacture of medical 
supplies 

ImportExportCompanies Import/Export companies  
(particularly dealing in high value goods) 

LargeRetailersWholesalersofHouseholdGoodsandFurniture Large retailers & wholesalers of household 
goods and furniture 

MediaActivities Media activities (e.g. journalist, newspaper) 

MedicalDentalandHospitalactivities Medical Dental activities (including 
individual professions 
such as administrator, dental technician, 
hygienist, orthodontist,  
dentist) 

MiningofPreciousMetalsStonesUranium Mining precious metals/stones, 
uranium 

MoneyServiceBusiness Money Service Business - Bureaux de Change 

ChequeEncashmentAgency Money Service Business - Cheque 
Encashment Agency 

MoneyTransmitter Money Service Business - Money Transmitter 

OtherCashIncentive Other Cash Incentive businesses 

PaperPrintingCompanies Paper/printing companies 

PharmaceuticalsBiotechnology Pharmaceuticals/Biotechnology 

 



 

PoliticalandReligiousOrganisations Political and Religious organizations 

PoliticianGovernmentalReligiousorganisations Politician/Governmental staff/officers of 
international organizations 

PrivateEquity Private Equity 

PrivateSecurityServices Private Security Services 

RegulatorsandGoverningBodies Regulators and governing bodies 

ResearchExperimentalDevelopmentinBiotechnology Research & experimental development in 
biotechnology 

ResidentialCareProviders Residential Care providers (Elderly / 
Disabled) 

RetiredUnemployedIndividual Retired/unemployed individual 

SportsEntertainment Sports Entertainment (agents, committees, 
senior officials) 

TradeFinance Trade Finance 

TransportandWarehousing Transport and warehousing (including air, 
rail, shipping, truck, transit, taxi firms) 

TravelAgencyActivities Travel Agency activities 

UnregulatedCharitiesOrOtherUnregulatedNonprofitOrganisation Unregulated charities or other unregulated 
non-profit organizations(particularly if 
centered on certain target group) 

Utilities Utilities 

WealthManagementPrivateBanking Wealth Management / Private Banking 

Oth Others 

 
 
 

7.9    Product Enum 

 
  
Code Name 

  
Depository Depository 

  
EQ Equities 

  
CDX Currency Derivatives 

  
EDX Equity Derivatives 

 



 

  
COMDX Commodities 

  
Loan Loan 

  
MF Mutual Funds 

  
IA Investment Advisory 

  
 Portfolio Management - 
PMSD Discretionary 

  
 Portfolio Management - Non 
PMSND Discretionary 

  
CC Credit Card 

  
 Current or Saving 
CASA Account 

  
LI Life Insurance 

  
GI General Insurance 

 
7.10 Income Range Enum 

                       
Code Name 

1 Upto 1 lac 

2 1 - 5 lacs 

3 5 - 10 lacs 

4 10 - 25 lacs 

5 25 lacs - 1 Crore 

 



 

6 Above 1 Crore 

 
7.11 Currency Enum 

                        
  
Code Name 

  
INR Indian Rupees 

  
USD US Dollar 

  
ZAR South African Rand 

 

7.12    PEP Enum                      
 

  
Code Name 

  
PEP PEP 

  
NotAPEP Not a PEP 

  
RelatedToPEP Related to PEP 

 
7.13    PEP Classification Enum 

                        
  
Code Name 

  
1 Domestic PEP 

  
2 Foreign PEP 

 



 

  
3 UBO / AP is domestic PEP 

  
4 UBO / AP is foreign PEP 

5 BUREAUCRAT 

6 CIVIL SERVANT 

7 CURRENT OR FORMER HEAD OF STATE 
 

8 CURRENT OR FORMER MP, MLA OR MLC 
 

 

 
7.14    AdverseReputation Classification Enum 

 
  
Code Name 

  
1 Corruption 

  
2 Financial crime 

  
3 Organised crime 

  
4 Trafficking 

  
5 Terrorism 

  
6 Terrorist Financing 

  
7 Tax crime 

  
8 War crimes 

 



 

 

 
7.15    Tags Enum 

 
  
Code Name 

  
1 IUGuarantor 

  
2 STR 

  
3 Section 28 

  
4 Section 34 

  
5 Section 35 

  
6 SOW Available 

 
7.16    Channel Enum 

                        
  
Code Name 

  
1 Direct 

  
2 Intermediary 

 

 

 

 

 

 

 



 

 
7.17    RegulatoryAMLRisk Enum 

                         
  
Code Name 

  
1 Low 

  
2 Medium 

  
3 High 

  
4 Very High 

 

 
7.18    State Enum 

                       
  
Code Name 

AN Andaman & Nicobar 

AP Andhra Pradesh 

AR Arunachal Pradesh 

AS Assam 

BR Bihar 

CH Chandigarh 

CG Chhattisgarh 

DNHDD Dadra and Nagar Haveli And Daman and Diu 

DL Delhi 

GA Goa 

 



 

GJ Gujarat 

HR Haryana 

  
HP Himachal Pradesh 

JK Jammu & Kashmir 

JH Jharkhand 

KA Karnataka 

KL Kerala 

LD Lakshadweep 

MP Madhya Pradesh 

MH Maharashtra 

MN Manipur 

ML Meghalaya 

MZ Mizoram 

NL Nagaland 

OR Odisha 

PY Puducherry 

PB Punjab 

RJ Rajasthan 

SK Sikkim 

TN Tamil Nadu 

TR Tripura 

TS Telangana 

UP Uttar Pradesh 

 



 

 

UA Uttarakhand 

WB West Bengal 

LA Ladakh 

OTH Others 

 

                         
7.19    Qualification Enum 

 

Code Name 

1 B.Com  

2 BCA 

3 Graduate 

4 MBA 

5 MCA 

6 Post Graduate 

7 Diploma 

8 Undergraduate 

9 Doctorate 

10 Other 

 

 
7.20 RegAMLSpecialCategory Enum 

      
         

  
Code Name 

  
1 NRI 

  
2 HNI 

  
3 Trust, Charity, NGO 

 



 

  
4 Close family shareholdings or Beneficial Ownership 

  
5 Politically Exposed person 

  
6 Company Offering foreign exchange offerings 

  
7 Client in high risk Country 

  
8 Non Face to face client 

  
9 Client with dubious public reputation 

 
7.21    Agency Enum 

                         
  
Code Name 

  
FINACLE Finacle 

  
FLEXCUBE FlexCube 

  
FINNONE FINNONE 

 
7.22    Prefix Enum 

                          
  
Code Name 

  
Mr Mr 

  
Ms Ms 

  
Mrs Mrs 

 



 

  
Miss Miss 

  
Dr Dr 

 
7.23    Document Enum 

                          
  
Code Name 

  
Photograph Photograph 

  
PAN PAN 

  
CIN Company ID Number 

  
CRN Registration Certificate 

  
OthersPOA Others 

  
Passport Passport 

  
DrivingLicence Driving Licence 

  
VoterID Voter ID 

  
NREGA NREGA 

  
NPRLetter NPR Letter 

  
AadhaarOffline Aadhaar Offline 

  
MOA MOA 

 



 

  
PD PD 

  
TRUSTDEED TRUST DEED 

  
BoardResolution Board Resolution 

  
POATOM POATOM 

  
OVDIRP OVDIRP 

  
AadhaarCard Aadhaar Card 

  
EKYCAuthImage EKYC Auth Image 

  
UtilityBill2m Utility Bill 2m 

  
PropertyTax Property Tax 

  
PensionOrder Pension Order 

  
EmployerHouseLetter Employer House Letter 

  
CKYCSelfDeclaration CKYC Self Declaration 

  
SMA-AttestedPhoto SMA-Attested Photo 

  
BankStatement Bank Statement 

  
ForeignEmbassyLetter Foreign Embassy Letter 

  
AP1 AP1 

  
AP2 AP2 

 



 

  
SMA-IDCard SMA-ID Card 

  
PublicKey Public Key 

  
PrivateKey Private Key 

  
NetworthCertificate Networth Certificate 

  
SelfCertification Self Certification 

  
BalanceSheet Balance Sheet 

  
TaxReturns Tax Returns 

  
SalarySlip Salary Slip 

  
CashFlowStatement Cash Flow Statement 

  
ProfitandLossStatement Profit and Loss Statement 

  
AnnualReport Annual Report 

 
 
 

7.24 RM Type Enum 
 

Code Name 

BM Business Manager 

MA Marketing Assistant 

PBE PBE 

PCE Private Client Executive-PCE 

RM Relationship Manager 

TL Team Leader 

WA Wealth Advisor 

PRM Primary RM 

SRM Secondary RM 

 



 

 
 
 

7.25    Purpose Enum  

                          
  
Code Name 

  
01 Initial Screening with API Response and No Storage 

  
03 Initial Screening with API Response and TW Workflow 

  
04 Continuous Screening with TW Workflow. 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 



 

 
7.25.1 Purpose-wise Mandatory Fields 

While passing purpose code in the request user can either pass a single purpose or combination of purpose i.e 
● Purpose 01 & Purpose 04 or 

● Purpose 03 & Purpose 04 
 

At given point user cannot pass Purpose 01 ,03 together. 

Purpose: 01-Initial Screening with API Response and No Storage. 
 

  
Description List of Mandatory Fields 

  
 1. Source System 
 Customer Code 

Purpose 01 will be used when the user wants to perform just initial 2. Source System Name 
screening on the Customer. Along with the SourceSystemCustomerCode and 
SourceSystemName. 3. Purpose 

 4. Constitution Type 
User can pass value in either one of this fields.  

 AND 
For IND: Name, Mobile Number , Email Id ,PAN , Passport, DIN , Driving  
License 1. Name OR 

 2. Mobile Number OR 
For LE: Name, Mobile Number , Email ID , PAN, CIN, DIN, Date of Incorporation. 3. Email-Id OR 

 4. PAN OR 
Basis the data passed in these fields the user will receive a response for the 
Initial Screening requested for customer. 5. Passport OR 

Data of the screened customer will not be stored in the TW System. 6. Driving License (DL) OR 
7. Director Identification 

Number (DIN) OR 
8. Company Identification 

Number (CIN) 

 



 

Purpose: 03-Initial Screening with API Response and TW Workflow 
 

  
Description List of Mandatory Fields 

  
 1. Source System Customer 
 Code 
 2. Source System Name 
 

Purpose 03 will be used when the user wants to perform initial screening on 3. Purpose 

customer and receive an email for hits detected with the cases linked to these 4. Constitution Type 
hits.  
This email would consist URL of the case mapped to the hit detected, which AND 
would redirect the user to the TW workflow on which the user will be able to  
take necessary actions of approving and rejecting a customer. 1. Name OR 
Only the details regarding case would be saved in TW system. 2. Mobile Number OR 

3. Email ID OR 
4. PAN OR 
5. Passport OR 
6. Driving License (DL) OR 
7. Director Identification 

Number (DIN) OR 
8. Company Identification 

Number (CIN) 

 

Purpose: 04-Continuous Screening with TW Workflow. 
 

  
Description List of Mandatory Fields 

  
 1. Source System Customer 
 Code 

2. Source System Name 
Purpose 04 will be used when the user wants to perform continuous screening 
on customer and receive an email for hits detected with the cases linked to these 3. Constitution Type 
hits. 4. Purpose 
This email would consist of link to the TW case manager corresponding AND 
to the case. The user will be able to take relevant actions on the case. 1. Name OR 

 2. Mobile Number OR 
Customers data is saved in TW system only once after all the validations are 3. Email ID OR 
passed. Customer Data is saved to perform continuous screening on customers 
data. 4. PAN OR 

5. Passport OR 
6. Driving License (DL) OR 

7. Director Identification 
Number (DIN) OR 

8. Company Identification 
Number (CIN) 

 

 



 

 
7.26    Relation Enum 

                         
Code Name 

101 Accountant 

171 Appointer 

179 Assignee 

AuthorisedRepresentative Authorised Representative 

103 Authorized Signatory 

163 Beneficial Owner 

159 Board Member - Foundation 

133 Compliance Officer 

161 Controller 

140 Coparcener 

106 Director 

165 Donor 

138 Employee 

166 Founder 

155 Fund Administrator 

152 Fund Manager 

136 Group Concern 

Guardian Guardian 

111 Karta 

 



 

 

112 KeyRepresentative 

144 Other 

119 Partner 

139 Principal Officer 

120 Promoter 

151 Protector 

137 Reference person 

170 Security Provider 

122 Settlor 

123 Shareholder 

135 Sister Company 

156 Sub-advisor of Fund 

154 Sub-manager of fund 

160 Tax Controlling Person 

157 Transfer Agent 

129 Trustee 

130 UBO 

162 Whole Time Director 

215 Court Appointed Official 

Beneficiary Beneficiary 

JointHolder Joint Holder 

CompanySecretary Company Secretary 

Proprietor Proprietor 

 



 

SleepingPartner Sleeping Partner 

CoApplicant Co-Applicant 

 

 
7.27    KYCAttestation Enum 

 
  
Code Name 

  
1 Certified Copies 

  
2 E-KYC Data received from UIDAI 

  
3 Data received from offline verification 

  
4 Digital KYC Process 

  
5 Equivalent E-Document 

  
6 Video KYC 

 
 
 
 
 
 
  

 



 

8 Variations 
8.1 Decrypted Request and Response Variations for Initial Screening (Individual) 

8.1.1 Confirm & Probable Hit (Request) 

{ 
 

"requestId": "IND2550", 

"sourceSystemName": "FINACLE", 

"purpose": "01", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", "segmentStartDate": "11-

Feb-2022", "status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", "natureOfBusinessOther": 

"Marketing Firm", "companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", "kycDateOfDeclaration": "11-

Mar-2021", "kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", "regAMLRisk": 

"1", "regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

 



 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month", 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", "networthEffectiveDate": 

"11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

 



 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", "educationalQualification": "1, 4", 

"countryOfOperations": "IND, SRI", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019" 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2551", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2551", 

"prefix": "Mr", 

"firstName": "Thuraisamy ", 

"middleName": "", "lastName": " 

Selvakumar ", "fatherPrefix": "", 

"fatherFirstName": "", 

"fatherMiddleName": "", 

"fatherLastName": "", 

"spousePrefix": "", 

"spouseFirstName": "", 

"spouseMiddleName": "", 

"spouseLastName": "", 

"motherPrefix": "", 

"motherFirstName": "", 

"motherMiddleName": "", 

"motherLastName": "", "gender": 

"01", "dateofBirth": "11-Feb-

1995", "workEmail": "", 

"personalEmail": "", 

 



 

"personalMobileISD": "", 

"personalMobileNumber": "", 

"workMobileISD": "", "workMobileNumber": 

"", "permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "BHARAT DIAMOND BOURSE, TOWER – A(EAST) – 4050”, 

"permanentAddressLine2": "Near MJ College", 

"permanentAddressLine3": "BANDRA KURLA COMPLEX, BANDRA (EAST), MUMBAI", 

"permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", "correspondenceAddressLine1": 

"Mamta Nagar, Gavdevi, Flat 101", "correspondenceAddressLine2": "Koliwada", 

"correspondenceAddressLine3": "Mahim West", 

"correspondenceAddressDistrict": "Mumbai", "correspondenceAddressCity": 

"Mumbai", "correspondenceAddressState": "MH", 

"correspondenceAddressDocument": "UtilityBill2m", "countryOfResidence": 

"IND", 

"countryOfBirth": "IND", "birthCity": 

"Mumbai", "passportIssueCountry": 

"IND", "passportNumber": " ********* 

", 

"passportExpiryDate": "02-Feb-2025", 

"voterIdNumber": "VOTE78456", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", "nprLetterNumber": 

"NPR25689", "directorIdentificationNumber": "", 

"formSixty": "0", 

 



 

"pan": "**********", "ckycNumber": 

"80080070068592", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "Involved in Money Laundering Crimes", "notes": 

"Onboarding of Customer", 

"tags": "2,3", "screeningProfile": 

"STDSP1", 

"screeningreportwhenNil": "1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2550", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026” 

} 

], 
 

"politicallyExposedClassification": "1, 2", 

"citizenships": "IND, GBR", "nationalities": 

"IND, GBR", "documents": null 

} 

], 
 

"GUID": null  
} 
 

8.1.2 Confirm & Probable Hit (Response) 

{ 

"RequestId": "IND2551", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

 



 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2551",  

"applicationRefNumber":"AJNPC4556", 

"ValidationOutcome": "Success", 

"SuggestedAction": "Stop", 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", "PurposeCode": "01", 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{ 

"HitsDetected": "Yes", 

"HitsCount": 2 

"ConfirmedHit": "Yes", 

"ReportData": 

"JVBERi0xLjMNCjEgMCBvYmoNClsvUERGIC9UZXh0IC9JbWFnZUIgL0ltYWdlQyAvSW1hZ2VJXQ0KZ 
W5kb2JqDQo3IDAgb2JqDQo8PCAvTGVuZ3RoIDU4NDMgL0ZpbHRlciAvRmxhdGVEZWNvZGUgPj4g 
c3RyZWFtDQpYCbVdbW/cOJL+vsD8B37cPbgVkRQlyrg7wOMkEyOJk409OzjcHhZytxxr0y+efok3++u         
vKPFNEtWkWj0YIElzqvg8VawiKb5IP/3pdxSjJI9ixPI8ylKU5Rn82JboN7T+Cf4vZhFDnKRRkhPEUh5hw 
hDOIhZzLfXzPXr1liJMozzP0f0jVCn+235FKaURjxm6/4AITSPOEac4opig+wX68918W5brav31H/flbg9/ 
wXd/xPd/wd6c//Tn/4q0EmcRQlGPOZRlhCEOQU2GEcZa4MniPewE6GbpDU2BuwUhDCJKBjZAkdf 
yufNdn+JcDy7OnydkZgAErlk+SWjHUZxFCcUxREFJ8QR4wIHzE5ycBzPIoxYRsBDCQJA+BM4PtZmYC 
mSC5FUihApYpuBY2UHrv/r2hExwqGNoD7QFWb8XOyqOXpd7otquev5D1qvBgbIjIP70iiDdiPgBdJFz 
o85UNQDuFkaxbhx35fy9wM0Grp5DY5ztBvYHyfTgZuKutDzzXaBbhblel89VuX2EuWPPEuzBZ7RhzK 
dJY95ObM5jUJKk4izBonPy3IW8/SBlkWSMd43lOZplEM4Tza0roimLUOvnp+X1bzYV5s1BOkjut1cosE 
mThKbAE6sLB7RwtAVZFQmyOawnZfo7sduX67QbbEqL9Hbm9ur6w9vhtt7IgvdCBaPz9vN4jDfo7vy 
6wpafOfwgW6FifC6ESz4++JrH7LXD3z5RfyNXtD//h/UuhCdQgZ18phGcY5WiHEGPY/6vUR3J9Qhf2d 
gXQ4d4GAlGKzuVtL0TppH8zOMRpu6VcUxGqpfBDnIBcLzYz2jEEoaoel9IwxVqewbrw+7/WZVbtH7/ 
7k+3kUCAxF5wGBqAsHgRGT6Ninz+uq3T59eo5ufv1y9u/mI3l/dvb/6gvox3IQ+m05E55BF5brYl1832 
x+X6Ob29UD60OnQOn8s6Jv1vtyuykVVbH+g682iHOrDMpRyfo42SHOq0T9f3TrwlK8nAypf25Cvb1y 
QyseTIZWPW5Db6ruYyXyo5uV6d8zDWXwWD2fMeLjY7Zr507Cbp6JqN1u4t/W4WCyrPUT15+Jbtds 
X62G3T6Wg3W5R+KVcL8Ts42OxLAc9zshZPM4y09qQzGjziH6utvunY26fCq3dboFfbw7rPWTyEXzt8 

 



 

6n42ucWfg2KrutWH/Q5Tc7ic5pr2DcrGDyO+VpC0kw8sKQTfG2BXm/W+wJmPreH1YOIs0F7cSpHT 
96MnifaS0yv/Rn67GIN0y10tVhsy51r1iXRWc7Pgh5Ty/AtYD5vIL3WMAUdohA2g4KxnQLDzMxdsC4 
ZMRGzq1EFMLXB8Fg5YiZm5kMWGVkyZjqGXUYdpdPMyBjMw8QcWc3HxKM8YfZ0TEikjUTaSFApM 
Xouxngc4biZi72r9jsxByvn+3KB/guxgLbsug+o5SSKqWwCY8uy9kFKHAVSYYl2AoOTDNl/msd3xoTRH 
FF4/IrBIt4Ece0V8FEjwhuRuBHpxTl1xLm1AqKqSWkEs4DmGWsLD3U9TzhYwgOJzZLD02act1ky2mIp 
GlGIjGWpqrFZ1k+CITQxJXV3qHhCOELwdN1JUynUMMXwdy002qGiIuhxbK6ft9VKzDQ/Fvv5UwhlA 
oNMwoxrYVpVd+A2YxrHUqZhzIc6eQ9hXY9FuCaK7n88BzmY8jziGTFsxUpcx78UTGhkGrY5bmRGsx 
X1JI5IQL+uq98PJbpZBIUueItm1FDm/ZBIgGMjIymT0yJC1NNmfL8t5t9eqn//G930H3xURkLUJqlO7B 
OWx2Q9CdO4/aUilVgSTObneDBVjwX26+3d9T8wSbO+hSrXJKrKtRNsVMlm4V6RGM3Qm3/VkxR42 
kVX6x/o0xYmxWjs8puuHiZSYuzQqeFaFtZ5JK2S+XjCqpuqyDLq83bzUDw4ZvQ6rySqzKtT1vqaxLJA// 
q6ijDtDopWYihMcmLzycywIAnGGRlMCZiO4uQMKYFh4Iol4GBKSLDJKWGB/Xr1+Wo4GyTgGbLBgv 
wjsiEGgkloNkirpmeDZZQ/GyTq5GywQJN0OBEU3MREsNAymJazfn8tIzjJYMQl0zMhATNjua/Q3VbS 
maDApmaCDQaDNUy530PkLDaru7GRqCrkOOJ5U2Gxnoullw/Vbj+YYcqQ6Rlmm/IHZJhtmDfDlFWT 
M8w2ypthCnVqhtmgGU/7QahyTANOyzEbj5AsZoOjTQJRxuJseo6lkDdyfzcZzDEJNjnHLLBPb6+uh3N 
BAp4hFyzIPyIXGMwOcGguSKum54JllD8XJOrkXLBA84zx4VxQgBNzwcLDSUrzwVQgWYT5GVIhifUZg 
f60UoWwBJucChbYm8N281wWaxh2NuuThxuSRyRN7OFmOL2kEWdIL8uMPyK9LKP86SWtmp5ell 
H+9JKok9PLAsVx1o92nV4KcGJ6WXgk4znpJnSjOrDDLlYNGeVoJZbZUoJ1QbOSGqaqClgapSntqLq25 
NV6iEGVBUdU1TKgTVitC/ZRW4RBUiwS24TB2XTA1gmoXdUaVduqUP226rXEFmO1uuixVojC9N7oi 
mU+xoeadhJwT1kAa4M1sN9ivRTZYq0WJz0WC1FK7QYWa4W52KDxmzwWuadcI2ubNbLfZr2g2aKt 
ljg9NgtRsadiRaboL3MeYvNY5J5yjWySWCEHZLFaEW3RVmukvjwGUbEkYJSb/Z8kKJNHIveUa2Rts0Z 
22uzYtkpctM/dYzr7adbsSZt+GiZYNAlCBUmx9GZQZUFAj6lRVYEftbPT01JtUHVv1C/R/th5O7dWj9yu 
Tud+v8QP0BVdOarTidYv8QN0RVeO6nRU90v8AGPj3J1hpuktLr62V8g65DSyM+bcGaaRbeWzxrozw 
0CS5XaGxUkUkyBUkBRregZVFgSkiUZVBX7UTkK1VBtUnRL9Eu0Pf4bp+vvV6ZTol/gBuqIrR3U6Jfolfo 
Cu6MpRnY7qfokfYGycuzPMNL3Fxdf2ClmHnEZ2xpw7wzSyrXzWWHdmGEiSzMqwJMvguT0EVUiKF 
T2NqgoC0kSjyoIA1E5CtVQbVJUS/RLjD3+G6fr71amU6JcEAHRFV47qVEr0SwIAuqIrR3UqqvslAQBj49 
ydYabpLS6+tpfIJuQUsjvm3BmmkW3ls8a6K8OEZM7sDGM4opyEoDJcrxMaVFngTxODqgr8qJ2Eaqk 
2qDol+iXaH94MM/X3q9Mp0S/xA3RFV47qdEr0S/wAXdGVozod1f0SP8DYOHdmmNX0Fhdf2ytkHXI 
a2RlzzgwzyLbyWWPdmWEgmVJiZRhhUUKDVKWkQXWqOtNEo6qCIFWxcmVslStXI/I6DNW1SmdsV 
ah+wnaGKhfLkjDl2A4ptWwVYO5YYNdCnTFYA/tJ211GA6xLwpTFqpUxWa1aBZg8Ftm5Umds1sh+2 
nYv1iDrkjBlsWplJbBctQqweSyyc6XOSmKFHJCKVt8oE0qVhCmP6S0nITtX6ozNGtmvbJNskN3KwxeSr 
yAxNC4wLjAuMCkNCi9Qcm9kdWNlciAoTWljcm9zb2Z0IFJlcG9ydGluZyBTZXJ2aWNlcyBQREYgUmV 
uZGVyaW5nIEV4dGVuc2lvbiAxNC4wLjAuMCkNCi9DcmVhdGlvbkRhdGUgKEQ6MjAyMjA4MTAxMj 
U5NTUrMDUnMzAnKQ0KPj4NCmVuZG9iag0KeHJlZg0KMCAyNw0KMDAwMDAwMDAwMCA2NTU 
zNSBmDQowMDAwMDAwMDEwIDAwMDAwIG4NCjAwMDAwMDU5ODYgMDAwMDAgbg0KMDA 
wMDExNzYwNiAwMDAwMCBuDQowMDAwMTE3NzExIDAwMDAwIG4NCjAwMDAxMTc4MTEgM 
DAwMDAgbg0KMDAwMDAwNjE4MSAwMDAwMCBuDQowMDAwMDAwMDY1IDAwMDAwIG4NC 
jAwMDAxMTgzNzcgMDAwMDAgbg0KMDAwMDAyMDgzNSAwMDAwMCBuDQowMDAwMDE0Mz 
A3IDAwMDAwIG4NCjAwMDAwMjcxODMgMDAwMDAgbg0KMDAwMDAyMTAyMSAwMDAwMCB 
uDQowMDAwMDM0OTkwIDAwMDAwIG4NCjAwMDAwMjczODAgMDAwMDAgbg0KMDAwMDA0 
MjEzNyAwMDAwMCBuDQowMDAwMDM1MTc3IDAwMDAwIG4NCjAwMDAwNDU2MTUgMDAw 
MDAgbg0KMDAwMDA0MjMzNCAwMDAwMCBuDQowMDAwMDQ5MDc0IDAwMDAwIG4NCjAw 
MDAwNDU3OTIgMDAwMDAgbg0KMDAwMDA0OTI2MSAwMDAwMCBuDQowMDAwMTE3MjcwI 
DAwMDAwIG4NCjAwMDAxMTc5NTMgMDAwMDAgbg0KMDAwMDExODE4MCAwMDAwMCBuD 
QowMDAwMTE4NDgwIDAwMDAwIG4NCjAwMDAxMTg1MzMgMDAwMDAgbg0KdHJhaWxlciA8P 
CAvU2l6ZSAyNyAvUm9vdCAyNSAwIFIgL0luZm8gMjYgMCBSID4+DQpzdGFydHhyZWYNCjExODgzN 
g0KJSVFT0Y=", 

"HitResponse": [ 

{ 
 

"Source": " FBI Wanted Person ", 

 



 

"WatchlistSourceId": "20", 

"MatchType": "KeyActivity", "Score": 

100.0, 

"ConfirmedMatchingAttributes": "Passport" 

}, 

{ 
 

"Source": " Sri Lanka Terrorist Sanctions ", 

"WatchlistSourceId": "7", 

"MatchType": "Probable", 

"Score":65, 

"ConfirmedMatchingAttributes": "" 

} 

] 

}, 
 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

], 

} 

] 
 

}  
} 
 
 
 
 

 
8.1.3 Probable Hits (Request) 

 

                                   { 

"requestId": "IND2552", 

"sourceSystemName": "FINACLE", 

"purpose": "01", 

 



 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", "segmentStartDate": "11-

Feb-2022", "status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Diamond Merchant", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", "kycDateOfDeclaration": "", 

"kycPlaceOfDeclaration": "", "kycVerificationDate": "", 

"kycEmployeeName": "", 

"kycEmployeeDesignation": “", 

"kycVerificationBranch": "", 

"kycEmployeeCode": "", "listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", 

"regAMLRisk": "", 

"regAMLRiskLastRiskReviewDate": ", 

"regAMLRiskNextRiskReviewDate": "", 

"incomeRange": "", 

"exactIncome": , 

"incomeCurrency": "", 

"incomeEffectiveDate": "", 

"incomeDescription": "", 

"incomeDocument": "", 

"exactNetworth": , 

 



 

"networthCurrency": "", 

"networthEffectiveDate": "", 

"networthDescription": "", 

"networthDocument": "", 

"familyCode": "", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", "educationalQualification": "", 

"countryOfOperations": "", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "", 

"regAMLRiskSpecialCategoryStartDate": "" 

 



 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2552", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2552", 

"prefix": "Mr", 

"firstName": " RIYAZ ISMAIL SHAHBANDR", 

"middleName": "", 

"lastName": "", 

"fatherPrefix": "", 

"fatherFirstName": "", 

"fatherMiddleName": "",  

"fatherLastName": "", 

"spousePrefix": "", 

"spouseFirstName": "", 

"spouseMiddleName":""     

"spouseLastName": "", 

"motherPrefix": "", 

"motherFirstName": "", 

"motherMiddleName":"","m

otherLastName": "", 

"gender": "01", 

"dateofBirth": "19-May-1976", 

"workEmail": "", 

"personalEmail": "", 

"personalMobileISD": "", 

"personalMobileNumber": "", 

"workMobileISD": "", 

"workMobileNumber": "", 

"permanentAddressCountry": "", 

 



 

"permanentAddressZipCode": "", 

"permanentAddressLine1": "", 

"permanentAddressLine2": "", 

"permanentAddressLine3": "", 

"permanentAddressDistrict": "", 

"permanentAddressCity": "Lahore", 

"permanentAddressState": "", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "",  

"correspondenceAddressCountry": "",  

"correspondenceAddressZipCode": "", 

 "correspondenceAddressLine1": "", 

 "correspondenceAddressLine2": "",  

"correspondenceAddressLine3": "",  

"correspondenceAddressDistrict": "",  

"correspondenceAddressCity": "", 
 
 "correspondenceAddressState": "", 
 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "", 

"countryOfBirth": "", 

"birthCity": "", 

"passportIssueCountry": "", 

"passportNumber": "", 

"passportExpiryDate": "", 

"voterIdNumber": "", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", "nprLetterNumber": 

"NPR25689", "directorIdentificationNumber": 

"00190509", 

 



 

"formSixty": "0", 

"pan": "**********", 

"ckycNumber": "", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "", "notes": 

"Onboarding of Customer", "tags": "2,3", 

"screeningProfile": "STDSP3", 

"screeningreportwhenNil":"1" 

"riskProfile": null, "adverseReputation": 

"1", "adverseReputationClassification": "", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "", 

"taxIdentificationNumber": "", 

"taxResidencyStartDate": "", 

"taxResidencyEndDate": "” 

} 

], 
 

"politicallyExposedClassification": "", 
 
 

"citizenships": "IND, GBR", 

"nationalities": "IND, GBR", 

"documents": null 

} 

], 
 

"GUID": null 

} 

 
8.1.4 Probable Hits (Response)  

 

{ 

 



 

"RequestId": "IND2552",  

"OverallStatus": "AcceptedByTW",  

"ValidationCode": "null",  

"ValidationDescription": "null",  

"CustomerResponse":  

[ 

 { 

  "SourceSystemCustomerCode": "2552",  

  "applicationRefNumber": "AJNPC45568", 

  "ValidationOutcome": "Success",  

  "SuggestedAction": "Review",  

  "PurposeResponse": 

   [ 

   { 

    "Purpose": "Initial Screening with API Response and No Storage", 

    "PurposeCode": "01", 

    "ValidationCode": "",  

    "ValidationDescription": "", 

    "ValidationFailureCount": 0  

    "Data": 

    { 

     "HitsDetected": "Yes",  

     "HitsCount": 2,  

     "ConfirmedHit": "No",  

     "ReportData": "Base 64 Data", 

     "HitResponse" 

      { 

      "Source": "MHA_Designated",  

      "WatchlistsourceID": "68",  

      "MatchType":"Probable",  

      "Score": 80, 

 



 

      "ConfirmedMatchingAttributes": "", 

      } 

      { 

      "Source": " MHA_Designated ", 

      "WatchlistsourceID": "72", 

      "MatchType":"Probable", 

      "Score": 80, 

      "ConfirmedMatchingAttributes": "", 

     } 

    } 

   }   

  ] 

 }, 

 

"ValidationCode": "", 

"ValidationDescription": "",  

"ValidationFailureCount": 0 

}, 

], 

 

"RelatedPersonResponse": null,  

"RelatedPersonRelationResponse": null 

} 

] 

} 
 
                   
 
  

 



 

8.2 Decrypted Request and Response Variations for Continuous Screening (Individual) 
8.2.1 Confirm & Probable Hits (Request) 

 

{ 
 

"requestId": "IND2550", 

"sourceSystemName": "FINACLE", 

"purpose": "01,04", 

"customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", "segmentStartDate": "11-

Feb-2022", "status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", "kycDateOfDeclaration": "11-

Mar-2021", "kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", 

"listed": "", 

"applicationRefNumber": "AJNPC45568", 
 

"documentRefNumber": "DOCREF5722", "regAMLRisk": 

"1", "regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

 



 

"incomeRange": "2", 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month", 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", "networthEffectiveDate": 

"11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

 



 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", 

"educationalQualification": "1, 4", 

"countryOfOperations": "IND, USA", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019" 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2551", 
 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2551", 

"prefix": "Mr", 

"firstName": " Thuraisamy Selvakumar ", 

"middleName": "", 

"lastName": "", 

"fatherPrefix": "", 

"fatherFirstName": "", 

"fatherMiddleName": "", 

"fatherLastName": "", 

"spousePrefix": "", 

"spouseFirstName": "", 

"spouseMiddleName": "", 

"spouseLastName": "", 

"motherPrefix": "", 

"motherFirstName": "", 

"motherMiddleName": "", 

"motherLastName": "", "gender": 

 



 

"01", "dateofBirth": "11-Feb-

1995", "workEmail": "", 

"personalEmail": "", 

"personalMobileISD": "", 

"personalMobileNumber": "", 

"workMobileISD": "", 

"workMobileNumber": "", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "BHARAT DIAMOND BOURSE, TOWER – A(EAST) – 4050”, 
"permanentAddressLine2": "Near MJ College", 

"permanentAddressLine3": "BANDRA KURLA COMPLEX, BANDRA (EAST), MUMBAI", 

"permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", 

"correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", "correspondenceAddressLine3": 

"Mahim West", "correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", "correspondenceAddressState": "MH", 

"correspondenceAddressDocument": "UtilityBill2m", "countryOfResidence": 

"IND", 

"countryOfBirth": "IND", "birthCity": 

"Mumbai", "passportIssueCountry": 

"IND", "passportNumber": " ********* 

", 

"passportExpiryDate": "02-Feb-2025", 

"voterIdNumber": "VOTE78456", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

 



 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", "nprLetterNumber": 

"NPR25689", "directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", "ckycNumber": 

"80080070068592", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationDetails": "Involved in Money Laundering Crimes", "notes": 

"Onboarding of Customer", 

"tags": "2,3", "screeningProfile": 

"STDSP1", 

"screeningreportwhenNil": "1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2550", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026” 

} 
 

], 

"politicallyExposedClassification": "1, 2", 

"citizenships": "IND, GBR", "nationalities": 

"IND, GBR", "documents": null 

} 

], 
 

"GUID": null 

} 
8.2.2 Confirm & Probable Hit (Response)  

 



 

{ 

"RequestId": "IND2551", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2551",  

  "applicationRefNumber": "AJNPC45568", 
"ValidationOutcome": "Success", 

"SuggestedAction": "Stop", 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 
 

"PurposeCode": "01", 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data": 

{ 

"HitsDetected": "Yes", 

"HitsCount": 2 

"ConfirmedHit": "Yes", 

"ReportData": Base 64 

"JVBERi0xLjMNCjEgMCBvYmoNClsvUERGIC9UZXh0IC9JbWFnZUIgL0ltYWdlQyAvSW1hZ2VJXQ0KZ 
W5kb2JqDQo3IDAgb2JqDQo8PCAvTGVuZ3RoIDU4NDMgL0ZpbHRlciAvRmxhdGVEZWNvZGUgPj4g 
c3RyZWFtDQpYCbVdbW/cOJL+vsD8B37cPbgVkRQlyrg7wOMkEyOJk409OzjcHhZytxxr0y+efok3++u 
vKPFNEtWkWj0YIElzqvg8VawiKb5IP/3pdxSjJI9ixPI8ylKU5Rn82JboN7T+Cf4vZhFDnKRRkhPEUh5hw 
hDOIhZzLfXzPXr1liJMozzP0f0jVCn+235FKaURjxm6/4AITSPOEac4opig+wX68918W5brav31H/flbg9/ 
/wXd/xPd/wd6c//Tn/4q0EmcRQlGPOZRlhCEOQU2GEcZa4MniPewE6GbpDU2BuwUhDCJKBjZAkdf 
yufNdn+JcDy7OnydkZgAErlk+SWjHUZxFCcUxREFJ8QR4wIHzE5ycBzPIoxYRsBDCQJA+BM4PtZmYC 
mSC5FUihApYpuBY2UHrv/r2hExwqGNoD7QFWb8XOyqOXpd7otquev5D1qvBgbIjIP70iiDdiPgBdJFz 
o85UNQDuFkaxbhx35fy9wM0Grp5DY5ztBvYHyfTgZuKutDzzXaBbhblel89VuX2EuWPPEuzBZ7RhzK 

 



 

dJY95ObM5jUJKk4izBonPy3IW8/SBlkWSMd43lOZplEM4Tza0roimLUOvnp+X1bzYV5s1BOkjut1cosE 
mThKbAE6sLB7RwtAVZFQmyOawnZfo7sduX67QbbEqL9Hbm9ur6w9vhtt7IgvdCBaPz9vN4jDfo7vy 
6wpafOfwgW6FifC6ESz4++JrH7LXD3z5RfyNXtD//h/UuhCdQgZ18phGcY5WiHEGPY/6vUR3J9Qhf2d 
gXQ4d4GAlGKzuVtL0TppH8zOMRpu6VcUxGqpfBDnIBcLzYz2jEEoaoel9IwxVqewbrw+7/WZVbtH7/ 
7k+3kUCAxF5wGBqAsHgRGT6Ninz+uq3T59eo5ufv1y9u/mI3l/dvb/6gvox3IQ+m05E55BF5brYl1832 
x+X6Ob29UD60OnQOn8s6Jv1vtyuykVVbH+g682iHOrDMpRyfo42SHOq0T9f3TrwlK8nAypf25Cvb1y 
QyseTIZWPW5Db6ruYyXyo5uV6d8zDWXwWD2fMeLjY7Zr507Cbp6JqN1u4t/W4WCyrPUT15+Jbtds 
X62G3T6Wg3W5R+KVcL8Ts42OxLAc9zshZPM4y09qQzGjziH6utvunY26fCq3dboFfbw7rPWTyEXzt8 
6n42ucWfg2KrutWH/Q5Tc7ic5pr2DcrGDyO+VpC0kw8sKQTfG2BXm/W+wJmPreH1YOIs0F7cSpHT 
96MnifaS0yv/Rn67GIN0y10tVhsy51r1iXRWc7Pgh5Ty/AtYD5vIL3WMAUdohA2g4KxnQLDzMxdsC4 
ZMRGzq1EFMLXB8Fg5YiZm5kMWGVkyZjqGXUYdpdPMyBjMw8QcWc3HxKM8YfZ0TEikjUTaSFApM 
Xouxngc4biZi72r9jsxByvn+3KB/guxgLbsug+o5SSKqWwCY8uy9kFKHAVSYYl2AoOTDNl/msd3xoTRH 
FF4/IrBIt4Ece0V8FEjwhuRuBHpxTl1xLm1AqKqSWkEs4DmGWsLD3U9TzhYwgOJzZLD02act1ky2mIp 
GlGIjGWpqrFZ1k+CITQxJXV3qHhCOELwdN1JUynUMMXwdy002qGiIuhxbK6ft9VKzDQ/Fvv5UwhlA 
oNMwoxrYVpVd+A2YxrHUqZhzIc6eQ9hXY9FuCaK7n88BzmY8jziGTFsxUpcx78UTGhkGrY5bmRGsx 
X1JI5IQL+uq98PJbpZBIUueItm1FDm/ZBIgGMjIymT0yJC1NNmfL8t5t9eqn//G930H3xURkLUJqlO7B 
OWx2Q9CdO4/aUilVgSTObneDBVjwX26+3d9T8wSbO+hSrXJKrKtRNsVMlm4V6RGM3Qm3/VkxR42 
kVX6x/o0xYmxWjs8puuHiZSYuzQqeFaFtZ5JK2S+XjCqpuqyDLq83bzUDw4ZvQ6rySqzKtT1vqaxLJA// 
q6ijDtDopWYihMcmLzycywIAnGGRlMCZiO4uQMKYFh4Iol4GBKSLDJKWGB/Xr1+Wo4GyTgGbLBgv 
wjsiEGgkloNkirpmeDZZQ/GyTq5GywQJN0OBEU3MREsNAymJazfn8tIzjJYMQl0zMhATNjua/Q3VbS 
maDApmaCDQaDNUy530PkLDaru7GRqCrkOOJ5U2Gxnoullw/Vbj+YYcqQ6Rlmm/IHZJhtmDfDlFWT 
M8w2ypthCnVqhtmgGU/7QahyTANOyzEbj5AsZoOjTQJRxuJseo6lkDdyfzcZzDEJNjnHLLBPb6+uh3N 
BAp4hFyzIPyIXGMwOcGguSKum54JllD8XJOrkXLBA84zx4VxQgBNzwcLDSUrzwVQgWYT5GVIhifUZg 
f60UoWwBJucChbYm8N281wWaxh2NuuThxuSRyRN7OFmOL2kEWdIL8uMPyK9LKP86SWtmp5ell 
H+9JKok9PLAsVx1o92nV4KcGJ6WXgk4znpJnSjOrDDLlYNGeVoJZbZUoJ1QbOSGqaqClgapSntqLq25 
NV6iEGVBUdU1TKgTVitC/ZRW4RBUiwS24TB2XTA1gmoXdUaVduqUP226rXEFmO1uuixVojC9N7oi 
mU+xoeadhJwT1kAa4M1sN9ivRTZYq0WJz0WC1FK7QYWa4W52KDxmzwWuadcI2ubNbLfZr2g2aKt 
ljg9NgtRsadiRaboL3MeYvNY5J5yjWySWCEHZLFaEW3RVmukvjwGUbEkYJSb/Z8kKJNHIveUa2Rts0Z 
22uzYtkpctM/dYzr7adbsSZt+GiZYNAlCBUmx9GZQZUFAj6lRVYEftbPT01JtUHVv1C/R/th5O7dWj9yu 
Tud+v8QP0BVdOarTidYv8QN0RVeO6nRU90v8AGPj3J1hpuktLr62V8g65DSyM+bcGaaRbeWzxrozw 
0CS5XaGxUkUkyBUkBRregZVFgSkiUZVBX7UTkK1VBtUnRL9Eu0Pf4bp+vvV6ZTol/gBuqIrR3U6Jfolfo 
Cu6MpRnY7qfokfYGycuzPMNL3Fxdf2ClmHnEZ2xpw7wzSyrXzWWHdmGEiSzMqwJMvguT0EVUiKF 
T2NqgoC0kSjyoIA1E5CtVQbVJUS/RLjD3+G6fr71amU6JcEAHRFV47qVEr0SwIAuqIrR3UqqvslAQBj49 
ydYabpLS6+tpfIJuQUsjvm3BmmkW3ls8a6K8OEZM7sDGM4opyEoDJcrxMaVFngTxODqgr8qJ2Eaqk 
2qDol+iXaH94MM/X3q9Mp0S/xA3RFV47qdEr0S/wAXdGVozod1f0SP8DYOHdmmNX0Fhdf2ytkHXI 
a2RlzzgwzyLbyWWPdmWEgmVJiZRhhUUKDVKWkQXWqOtNEo6qCIFWxcmVslStXI/I6DNW1SmdsV 
ah+wnaGKhfLkjDl2A4ptWwVYO5YYNdCnTFYA/tJ211GA6xLwpTFqpUxWa1aBZg8Ftm5Umds1sh+2 
nYv1iDrkjBlsWplJbBctQqweSyyc6XOSmKFHJCKVt8oE0qVhCmP6S0nITtX6ozNGtmvbJNskN3KwxeSr 
yAxNC4wLjAuMCkNCi9Qcm9kdWNlciAoTWljcm9zb2Z0IFJlcG9ydGluZyBTZXJ2aWNlcyBQREYgUmV 
uZGVyaW5nIEV4dGVuc2lvbiAxNC4wLjAuMCkNCi9DcmVhdGlvbkRhdGUgKEQ6MjAyMjA4MTAxMj 
U5NTUrMDUnMzAnKQ0KPj4NCmVuZG9iag0KeHJlZg0KMCAyNw0KMDAwMDAwMDAwMCA2NTU 
zNSBmDQowMDAwMDAwMDEwIDAwMDAwIG4NCjAwMDAwMDU5ODYgMDAwMDAgbg0KMDA 
wMDExNzYwNiAwMDAwMCBuDQowMDAwMTE3NzExIDAwMDAwIG4NCjAwMDAxMTc4MTEgM 
DAwMDAgbg0KMDAwMDAwNjE4MSAwMDAwMCBuDQowMDAwMDAwMDY1IDAwMDAwIG4NC 
jAwMDAxMTgzNzcgMDAwMDAgbg0KMDAwMDAyMDgzNSAwMDAwMCBuDQowMDAwMDE0Mz 
A3IDAwMDAwIG4NCjAwMDAwMjcxODMgMDAwMDAgbg0KMDAwMDAyMTAyMSAwMDAwMCB 
uDQowMDAwMDM0OTkwIDAwMDAwIG4NCjAwMDAwMjczODAgMDAwMDAgbg0KMDAwMDA0 
MjEzNyAwMDAwMCBuDQowMDAwMDM1MTc3IDAwMDAwIG4NCjAwMDAwNDU2MTUgMDAw 
MDAgbg0KMDAwMDA0MjMzNCAwMDAwMCBuDQowMDAwMDQ5MDc0IDAwMDAwIG4NCjAw 
MDAwNDU3OTIgMDAwMDAgbg0KMDAwMDA0OTI2MSAwMDAwMCBuDQowMDAwMTE3MjcwI 
DAwMDAwIG4NCjAwMDAxMTc5NTMgMDAwMDAgbg0KMDAwMDExODE4MCAwMDAwMCBuD 

 



 

QowMDAwMTE4NDgwIDAwMDAwIG4NCjAwMDAxMTg1MzMgMDAwMDAgbg0KdHJhaWxlciA8P 
CAvU2l6ZSAyNyAvUm9vdCAyNSAwIFIgL0luZm8gMjYgMCBSID4+DQpzdGFydHhyZWYNCjExODgzN 
g0KJSVFT0Y=", 

"HitResponse": [ 

{ 
 

"Source": " FBI Wanted Person ", 

"WatchlistSourceId": "20", 

"MatchType": "KeyActivity", "Score": 

100.0, 

"ConfirmedMatchingAttributes": "Passport" 

}, 

{ 
 

"Source": " Sri Lanka Terrorist Sanctions ", 

"WatchlistSourceId": "7", 

"MatchType": "Probable", 

"Score":65, 

"ConfirmedMatchingAttributes": "" 

} 

] 

}, 
 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

{ 

"Purpose": "Continuous Screening with email linked cases", 

"PurposeCode": "04", 

"Data": null, "ValidationCode": 

"", "ValidationDescription": "", 

"ValidationFailureCount": 0 

} 
 

], 

 



 

} 

] 

} } 

 

 
8.2.3 Probable Hits (Request) 

 

{ 

"requestId": "IND2552", 

"sourceSystemName": "FINACLE", 

"purpose": "01,04", "customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", "segmentStartDate": "11-

Feb-2022", "status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Diamond Merchant", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", "kycDateOfDeclaration": 

"", "kycPlaceOfDeclaration": "", "kycVerificationDate": 

"", "kycEmployeeName": "", 

"kycEmployeeDesignation": “", 

"kycVerificationBranch": "", 

"kycEmployeeCode": "", "listed": "", 

"applicationRefNumber": "AJNPC45568", 

"documentRefNumber": "DOCREF5722", 

"regAMLRisk": "", 

 



 

"regAMLRiskLastRiskReviewDate": ", 

"regAMLRiskNextRiskReviewDate": "", 

"incomeRange": "", 

"exactIncome": , 

"incomeCurrency": "", 

"incomeEffectiveDate": "", 

"incomeDescription": "", 

"incomeDocument": "", 

"exactNetworth": , 

"networthCurrency": "", 

"networthEffectiveDate": "", 

"networthDescription": "", 

"networthDocument": "", 

"familyCode": "", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

 



 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers,Oth", 

"educationalQualification": "", 

"countryOfOperations": "", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 

"regAMLRiskSpecialCategory": "", 

"regAMLRiskSpecialCategoryStartDate": "" 

} 

], 
 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"sourceSystemCustomerCode": "2552", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2552", 

"prefix": "Mr", 

"firstName": " RIYAZ ISMAIL SHAHBANDR", 

"middleName": "", 

"lastName": "", 

"fatherPrefix": "", 

"fatherFirstName": "", 

"fatherMiddleName": "", 

"fatherLastName": "", 

"spousePrefix": "", 

"spouseFirstName": "", 

"spouseMiddleName": "", 

"spouseLastName": "", 

"motherPrefix": "", 

"motherFirstName": "", 

"motherMiddleName": "", 

 



 

"motherLastName": "", 

"gender": "01", 

"dateofBirth": "19-May-1976", 

"workEmail": "", 

"personalEmail": "", 

"personalMobileISD": "", 

"personalMobileNumber": "", 

"workMobileISD": "", 

"workMobileNumber": "", 

"permanentAddressCountry": "", 

"permanentAddressZipCode": "", 

"permanentAddressLine1": "", 

"permanentAddressLine2": "", 

"permanentAddressLine3": "", 

"permanentAddressDistrict": "", 

"permanentAddressCity": "Lahore", 

"permanentAddressState": "", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "", 

"correspondenceAddressZipCode": "", 

"correspondenceAddressLine1": “", 

"correspondenceAddressLine2": "", 

"correspondenceAddressLine3": "", 

"correspondenceAddressDistrict": "", 

"correspondenceAddressCity": "", 

"correspondenceAddressState": "", 

"correspondenceAddressDocument": "UtilityBill2m", 

"countryOfResidence": "", 

"countryOfBirth": "", 

"birthCity": "", 

"passportIssueCountry": "", 

"passportNumber": "", 

"passportExpiryDate": "", 

 



 

"voterIdNumber": "", 

"drivingLicenseNumber": "DL935152", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

"aadhaarVaultReferenceNumber": "11483398602", 

"nregaNumber": "MH-02-12038/90", "nprLetterNumber": 

"NPR25689", "directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "**********", 

"ckycNumber": "", "identityDocument": 

null, "politicallyExposed": "PEP", 

"adverseReputationDetails": "", "notes": 

"Onboarding of Customer", "tags": "2,3", 

"screeningProfile": "SP3", 

"screeningreportwhenNil":"1" 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "", 

"taxDetailDtoList": [ 

{ 
 

"taxResidencyCountry": "", 

"taxIdentificationNumber": "", 

"taxResidencyStartDate": "", 

"taxResidencyEndDate": "” 

} 

], 
 

"politicallyExposedClassification": "", 
 
 

"citizenships": "IND, GBR", 

"nationalities": "IND, GBR", 

"documents": null 

} 

 



 

], 
 

"GUID": null 

} 

 
8.2.4 Probable Hits (Response) 

 

{ 
 

"RequestId": "IND2552", 

"OverallStatus": "AcceptedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

"CustomerResponse": [ 

{ 
 

"SourceSystemCustomerCode": "2552",  

"applicationRefNumber":"AJNPC45568"Valid

ationOutcome": "Success", "SuggestedAction": 

"Review", "PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 

"PurposeCode": "01", 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

"Data":

{  
 
 "HitsD
 etecte
 
 d": 
 
 "Yes", 

"Hit": "HitsC

{ ount": 

 



 

2, 

"Confi

rmed

Hit": 

"No", 

"Repo

rtData

": 

"Base 

64 

Data",

 



 

 
"Source": "MHA_Designated", 

"WatchlistsourceID": "68", 

"MatchType":"Probable", "Score": 

80, 

"ConfirmedMatchingAttributes": "", 

} 
 

{ 

"Source": " MHA_Designated ", 

"WatchlistsourceID": "72", 

"MatchType":"Probable", "Score": 

80, 

"ConfirmedMatchingAttributes": "", 

} 

} 

] 

}, 
 

"ValidationCode": "", 

"ValidationDescription": "", 

"ValidationFailureCount": 0 

}, 

{ 
 

"Purpose": "Continuous Screening with email linked cases", 

"PurposeCode": "04", 

"Data": null, "ValidationCode": 

"", "ValidationDescription": "", 

"ValidationFailureCount": 0 

} 

], 
 

"RelatedPersonResponse": null, 

"RelatedPersonRelationResponse": null 

} 

 



 

] 
    

8.2.5 Validation Failure (Request) 
 

{ 
 

"requestId": "IND2241", 

"sourceSystemName": "FINACLE", 

"purpose": "01,04", "customerList": [ 

{ 
 

"ekycOTPbased": "0", 

"segment": "3", "segmentStartDate": "11-

Feb-2022", "status": "Active", 

"effectiveDate": "15-Nov-2022", 

"minor": "0", 

"maritalStatus": "M", 

"occupationType": "SE", 

"occupationTypeOther": "", 

"natureOfBusinessOther": "Marketing Firm", 

"companyIdentificationNumber": "", 

"companyRegistrationNumber": "", 

"companyRegistrationCountry": "", 

"globalIntermediaryIdentificationNumber": "", 

"kycAttestationType": "1", "kycDateOfDeclaration": "11-

Mar-2021", "kycPlaceOfDeclaration": "Mumbai", 

"kycVerificationDate": "11-Mar-2021", 

"kycEmployeeName": "Aditi Jadhav", 

"kycEmployeeDesignation": "Manager", 

"kycVerificationBranch": "Mumbai", 

"kycEmployeeCode": "6546514", "listed": "", 

"applicationRefNumber": "AJNPC45569", 

"documentRefNumber": "DOCREF5726", "regAMLRisk": 

"1", "regAMLRiskLastRiskReviewDate": "21-Jan-2019", 

"regAMLRiskNextRiskReviewDate": "21-Mar-2025", 

"incomeRange": "2", 

 



 

"exactIncome": 250000.5, 

"incomeCurrency": "INR", 

"incomeEffectiveDate": "11-Feb-2022", 

"incomeDescription": "Total income of a month", 

"incomeDocument": "TaxReturns,CashFlowStatement", 

"exactNetworth": 1000000.0, 

"networthCurrency": "INR", "networthEffectiveDate": 

"11-Feb-2019", 

"networthDescription": "Total networth income of a year", 

"networthDocument": "NetworthCertificate, BalanceSheet", 

"familyCode": "FMC18779", 

"channel": "2", 

"contactPersonFirstName1": "", 

"contactPersonMiddleName1": "", 

"contactPersonLastName1": "", 

"contactPersonDesignation1": "", 

"contactPersonFirstName2": "", 

"contactPersonMiddleName2": "", 

"contactPersonLastName2": "", 

"contactPersonDesignation2": "", 

"contactPersonMobileISD": "", 

"contactPersonMobileNo": "", 

"contactPersonMobileISD2": "", 

"contactPersonMobileNo2": "", 

"contactPersonEmailId1": "", 

"contactPersonEmailId2": "", 

"commencementDate": "", 

"maidenPrefix": "", 

"maidenFirstName": "", 

"maidenMiddleName": "", 

"maidenLastName": "", 

"relatedPersonCountforCKYC": 0, 

"proofOfIdSubmitted": "Passport", 

"products": "MF,LI", 

 



 

"natureOfBusiness": " Jewellers ,GemPreciousMetalsOrStoneDealers\n,Oth", 

"educationalQualification": "1, 4", 

"countryOfOperations": "IND, USA", 

"regAMLRiskSpecialCategoryDtoList": [ 

{ 
 

"regAMLRiskSpecialCategory": "2", 

"regAMLRiskSpecialCategoryStartDate": "10-Jan-2019", 

"regAMLRiskSpecialCategoryStartDateDateTime": null 

} 

], 

"relatedPersonList": [], 

"customerRelationDtoList": [], 

"constitutionType": "1", 

"constitutionTypeId": 0, 

"sourceSystemCustomerCode": "2241", 

"sourceSystemCustomerCreationDate": "11-Feb-2022", 

"uniqueIdentifier": "UI2241", 

"prefix": "Mr", "firstName": 

"Ganesh", "middleName": 

"Gitesh", "lastName": 

"Hemani", "fatherPrefix": 

"Mr", 

"fatherFirstName": "Gitesh", 

"fatherMiddleName": "Sambhaji", 

"fatherLastName": "Hemani", 

"spousePrefix": "Mrs", 

"spouseFirstName": "Gita", 

"spouseMiddleName": "Hansraj", 

"spouseLastName": "Hemani", 

"motherPrefix": "Mrs", 

"motherFirstName": "Jayaprabha", 

"motherMiddleName": "Gitesh", 

"motherLastName": "Hemani", 

 



 

"gender": "01", 

"dateofBirth": "11-Feb-2024", 
 
"workEmail": "<EMAIL>", 

"personalEmail": "<EMAIL>", 

"personalMobileISD": "91", 

"personalMobileNumber": "9950238478", 

"workMobileISD": "91", 

"workMobileNumber": "7330067912", 

"permanentAddressCountry": "IND", 

"permanentAddressZipCode": "403707", 

"permanentAddressLine1": "Gokulnagri, Chawl no 15, Room no- 101", 

"permanentAddressLine2": "Near MJ College", "permanentAddressLine3": "Behind 

RK Hotel, Mumbai", "permanentAddressDistrict": "Mumbai", 

"permanentAddressCity": "Mumbai", 

"permanentAddressState": "MH", 

"permanentAddressDocument": "Passport", 

"permanentAddressDocumentOthersValue": "", 

"correspondenceAddressCountry": "IND", 

"correspondenceAddressZipCode": "403702", 

"correspondenceAddressLine1": "Mamta Nagar, Gavdevi, Flat 101", 

"correspondenceAddressLine2": "Koliwada", "correspondenceAddressLine3": 

"Mahim West", "correspondenceAddressDistrict": "Mumbai", 

"correspondenceAddressCity": "Mumbai", "correspondenceAddressState": "", 

"correspondenceAddressDocument": "UtilityBill2m", "countryOfResidence": 

"IND", 

"countryOfBirth": "IND", 
 

"birthCity": "Mumbai", "passportIssueCountry": 

"IND", "passportNumber": "PASS38147", 

"passportExpiryDate": "02-Feb-2023", 

"voterIdNumber": "VOTE78455", 

"drivingLicenseNumber": "DL935156", 

"drivingLicenseExpiryDate": "20-Sep-2025", 

"aadhaarNumber": "************", 

 



 

"aadhaarVaultReferenceNumber": "11483398605", 

"nregaNumber": "NREGA6965@", 

"nprLetterNumber": "1234567891234567890ABCDEFGHIJKLMONPQRSTUVWXYZ112233", 

"directorIdentificationNumber": "", 

"formSixty": "0", 

"pan": "ADDPP95475", "ckycNumber": 

"80080070068542", 

"identityDocument": null, 

"politicallyExposed": "PEP", 

"adverseReputationstring": null, 

"adverseReputationDetails": "Involved in Money Laundering Crimes", "notes": 

"Onboarding of Customer", 

"tags": "2,3", 

"screeningProfile": null, 

"riskProfile": null, 

"adverseReputation": "1", 

"adverseReputationClassification": "1, 2", 

"taxDetailDtoList": [ 

"taxResidencyCountry": "IND", 

"taxIdentificationNumber": "TIN2241", 

"taxResidencyStartDate": "01-Oct-2022", 

"taxResidencyEndDate": "01-Oct-2026", 

"taxResidencyEndDateDateTime": null 

} 

], 
 

"politicallyExposedClassification": "1, 3", 

"citizenships": "IND, GBR", "nationalities": 

"IND, GBR", "documents": null 

} 

], 
 

"GUID": null 

} 
8.2.6 Validation Failure (Response) 

 



 

 
{ 

"RequestId": "IND2241", 

"OverallStatus": "RejectedByTW", 

"ValidationCode": "null", 

"ValidationDescription": "null", 

 

"CustomerResponse": [ 
"SourceSystemCustomerCode": "2241",  

"applicationRefNumber":"AJNPC4556", 

"ValidationOutcome": "Failure", 

"PurposeResponse": 

[ 
 

{ 

"Purpose": "Initial Screening with API Response and No Storage", 

"PurposeCode": "01", 

"ValidationCode": "DateofBirth - FDV, Gender - SV, NPRLetterNumber - Max-L, 
NREGANumber - SC, Pan - FOR, PassportExpiryDate - PDV", 

"ValidationDescription": "DateofBirth - FDV: Future Date or Current Date not allowed, Gender - SV: Value passed 
should be as per specified values only, NPRLetterNumber - Max-L: Length should not be greater than 50 characters, 
NREGANumber - SC: Only Specific Characters are allowed, Pan - FOR: Invalid PAN as the pan provided does not fulfills 
the criteria of having length of 10 characters, first 5 characters as alphabets, 4th character as 'P' and last character is an 
alphabet., PassportExpiryDate - PDV: Past Date or Current Date is not allowed", 

"ValidationFailureCount": 6 

"Data": "null", 

{ 

}, 
 
 

}, 

{ 
 

"Purpose": "Continuous Screening with TW Workflow.", 

"PurposeCode": "04", 

"ValidationCode": "CorrespondenceAddressState - M, DateofBirth - FDV, Gender - SV, 
NPRLetterNumber - Max-L, NREGANumber - SC, Pan - FOR, PassportExpiryDate - PDV", 

"ValidationDescription": "CorrespondenceAddressState - M: is mandatory, DateofBirth - FDV: Future Date or 

 



 

Current Date not allowed, Gender - SV: Value passed should be as per specified values only, NPRLetterNumber - Max-
L: Length should not be greater than 50 characters, NREGANumber - SC: Only Specific Characters are allowed, Pan - 
FOR: Invalid PAN as the pan provided does not fulfills the  criteria of having length of 10 characters, first 5 characters 
as alphabets, 4th character as 'P' and last character is an alphabet., PassportExpiryDate - PDV: Past Date or Current 
Date is not allowed", 

"ValidationFailureCount": 7 

"Data":"null", 

} 

], 

"RelatedPersonResponse": null, 

"RelatedPersonRelationResponse": null 

} 

] 

} 

} 
 
 
 
 
 
 
 

 

 

 

 

 

 

 

 

 

 

 

 
 
 
 

 



 

8. Request And Response XSD 
8.1 Request XSD 

 
{ 
  "$schema": "http://json-schema.org/draft-04/schema#", 
  "type": "object", 
  "properties": { 
    "requestId": { 
      "type": "string" 
    }, 
    "sourceSystemName": { 
      "type": "string" 
    }, 
    "purpose": { 
      "type": "string" 
    }, 
    "customerList": { 
      "type": "array", 
      "items": [ 
        { 
          "type": "object", 
          "properties": { 
            "ekycOTPbased": { 
              "type": "string" 
            }, 
            "ekycOTPbasedId": { 
              "type": "null" 
            }, 
            "segment": { 
              "type": "string" 
            }, 
            "segmentId": { 
              "type": "null" 
            }, 
            "segmentStartDate": { 
              "type": "string" 
            }, 
            "segmentStartDateDateTime": { 
              "type": "null" 
            }, 
            "status": { 
              "type": "string" 
            }, 
            "statusId": { 
              "type": "null" 
            }, 
            "effectiveDate": { 
              "type": "string" 
            }, 
            "effectiveDateDateTime": { 
              "type": "null" 
            }, 
            "minor": { 
              "type": "string" 
            }, 

 



 

            "minorId": { 
              "type": "null" 
            }, 
            "maritalStatus": { 
              "type": "string" 
            }, 
            "maritalStatusId": { 
              "type": "null" 
            }, 
            "occupationType": { 
              "type": "string" 
            }, 
            "occupationTypeOther": { 
              "type": "string" 
            }, 
            "occupationTypeId": { 
              "type": "null" 
            }, 
            "natureOfBusinessOther": { 
              "type": "string" 
            }, 
            "companyIdentificationNumber": { 
              "type": "string" 
            }, 
            "companyRegistrationNumber": { 
              "type": "string" 
            }, 
            "companyRegistrationCountry": { 
              "type": "string" 
            }, 
            "companyRegistrationCountryId": { 
              "type": "null" 
            }, 
            "globalIntermediaryIdentificationNumber": { 
              "type": "string" 
            }, 
            "kycAttestationType": { 
              "type": "string" 
            }, 
            "kycAttestationTypeId": { 
              "type": "null" 
            }, 
            "kycDateOfDeclaration": { 
              "type": "string" 
            }, 
            "kycDateOfDeclarationDateTime": { 
              "type": "null" 
            }, 
            "kycPlaceOfDeclaration": { 
              "type": "string" 
            }, 
            "kycVerificationDate": { 
              "type": "string" 
            }, 
            "kycVerificationDateDateTime": { 

 



 

              "type": "null" 
            }, 
            "kycEmployeeName": { 
              "type": "string" 
            }, 
            "kycEmployeeDesignation": { 
              "type": "string" 
            }, 
            "kycVerificationBranch": { 
              "type": "string" 
            }, 
            "kycEmployeeCode": { 
              "type": "string" 
            }, 
            "listed": { 
              "type": "string" 
            }, 
            "listedId": { 
              "type": "null" 
            }, 
            "applicationRefNumber": { 
              "type": "string" 
            }, 
            "documentRefNumber": { 
              "type": "string" 
            }, 
            "regAMLRisk": { 
              "type": "string" 
            }, 
            "regAMLRiskId": { 
              "type": "null" 
            }, 
            "regAMLRiskLastRiskReviewDate": { 
              "type": "string" 
            }, 
            "regAMLRiskLastRiskReviewDateDateTime": { 
              "type": "null" 
            }, 
            "regAMLRiskNextRiskReviewDate": { 
              "type": "string" 
            }, 
            "regAMLRiskNextRiskReviewDateDateTime": { 
              "type": "null" 
            }, 
            "incomeRange": { 
              "type": "string" 
            }, 
            "incomeRangeId": { 
              "type": "null" 
            }, 
            "exactIncome": { 
              "type": "null" 
            }, 
            "incomeCurrency": { 
              "type": "string" 

 



 

            }, 
            "incomeCurrencyId": { 
              "type": "null" 
            }, 
            "incomeEffectiveDate": { 
              "type": "string" 
            }, 
            "incomeEffectiveDateDateTime": { 
              "type": "null" 
            }, 
            "incomeDescription": { 
              "type": "string" 
            }, 
            "incomeDocument": { 
              "type": "string" 
            }, 
            "incomeDocumentId": { 
              "type": "null" 
            }, 
            "exactNetworth": { 
              "type": "null" 
            }, 
            "networthCurrency": { 
              "type": "string" 
            }, 
            "networthCurrencyId": { 
              "type": "null" 
            }, 
            "networthEffectiveDate": { 
              "type": "string" 
            }, 
            "networthEffectiveDateDateTime": { 
              "type": "null" 
            }, 
            "networthDescription": { 
              "type": "string" 
            }, 
            "networthDocument": { 
              "type": "string" 
            }, 
            "networthDocumentId": { 
              "type": "null" 
            }, 
            "familyCode": { 
              "type": "string" 
            }, 
            "channel": { 
              "type": "string" 
            }, 
            "channelId": { 
              "type": "null" 
            }, 
            "contactPersonFirstName1": { 
              "type": "string" 
            }, 

 



 

            "contactPersonMiddleName1": { 
              "type": "string" 
            }, 
            "contactPersonLastName1": { 
              "type": "string" 
            }, 
            "contactPersonDesignation1": { 
              "type": "string" 
            }, 
            "contactPersonFirstName2": { 
              "type": "string" 
            }, 
            "contactPersonMiddleName2": { 
              "type": "string" 
            }, 
            "contactPersonLastName2": { 
              "type": "string" 
            }, 
            "contactPersonDesignation2": { 
              "type": "string" 
            }, 
            "contactPersonMobileISD": { 
              "type": "string" 
            }, 
            "contactPersonMobileNo": { 
              "type": "string" 
            }, 
            "contactPersonMobileISD2": { 
              "type": "string" 
            }, 
            "contactPersonMobileNo2": { 
              "type": "string" 
            }, 
            "contactPersonEmailId1": { 
              "type": "string" 
            }, 
            "contactPersonEmailId2": { 
              "type": "string" 
            }, 
            "commencementDate": { 
              "type": "string" 
            }, 
            "commencementDateDateTime": { 
              "type": "null" 
            }, 
            "maidenPrefix": { 
              "type": "string" 
            }, 
            "maidenPrefixId": { 
              "type": "null" 
            }, 
            "maidenFirstName": { 
              "type": "string" 
            }, 
            "maidenMiddleName": { 

 



 

              "type": "string" 
            }, 
            "maidenLastName": { 
              "type": "string" 
            }, 
            "relatedPersonCountforCKYC": { 
              "type": "integer" 
            }, 
            "proofOfIdSubmitted": { 
              "type": "string" 
            }, 
            "proofOfIdSubmittedId": { 
              "type": "null" 
            }, 
            "products": { 
              "type": "string" 
            }, 
            "productsId": { 
              "type": "null" 
            }, 
            "natureOfBusiness": { 
              "type": "string" 
            }, 
            "natureOfBusinessId": { 
              "type": "null" 
            }, 
            "educationalQualification": { 
              "type": "string" 
            }, 
            "educationalQualificationId": { 
              "type": "null" 
            }, 
            "countryOfOperations": { 
              "type": "string" 
            }, 
            "countryOfOperationsId": { 
              "type": "null" 
            }, 
            "personalMobileISD": { 
              "type": "string" 
            }, 
            "personalMobileNumber": { 
              "type": "string" 
            }, 
            "workMobileISD": { 
              "type": "string" 
            }, 
            "workMobileNumber": { 
              "type": "string" 
            }, 
            "regAMLRiskSpecialCategoryDtoList": { 
              "type": "array", 
              "items": [ 
                { 
                  "type": "object", 

 



 

                  "properties": { 
                    "Id": { 
                      "type": "null" 
                    }, 
                    "regAMLRiskSpecialCategory": { 
                      "type": "string" 
                    }, 
                    "regAMLRiskSpecialCategoryId": { 
                      "type": "null" 
                    }, 
                    "regAMLRiskSpecialCategoryStartDate": { 
                      "type": "string" 
                    }, 
                    "regAMLRiskSpecialCategoryStartDateDateTime": { 
                      "type": "null" 
                    } 
                  }, 
                  "required": [ 
                    "Id", 
                    "regAMLRiskSpecialCategory", 
                    "regAMLRiskSpecialCategoryId", 
                    "regAMLRiskSpecialCategoryStartDate", 
                    "regAMLRiskSpecialCategoryStartDateDateTime" 
                  ] 
                } 
              ] 
            }, 
            "relatedPersonList": { 
              "type": "array", 
              "items": {} 
            }, 
            "customerRelationDtoList": { 
              "type": "array", 
              "items": {} 
            }, 
            "constitutionType": { 
              "type": "string" 
            }, 
            "constitutionTypeId": { 
              "type": "integer" 
            }, 
            "sourceSystemName": { 
              "type": "string" 
            }, 
            "sourceSystemCustomerCode": { 
              "type": "string" 
            }, 
            "sourceSystemCustomerCreationDate": { 
              "type": "string" 
            }, 
            "sourceSystemCustomerCreationDateDateTime": { 
              "type": "null" 
            }, 
            "uniqueIdentifier": { 
              "type": "string" 

 



 

            }, 
            "SystemGeneratedId": { 
              "type": "null" 
            }, 
            "prefix": { 
              "type": "string" 
            }, 
            "prefixId": { 
              "type": "null" 
            }, 
            "firstName": { 
              "type": "string" 
            }, 
            "middleName": { 
              "type": "string" 
            }, 
            "lastName": { 
              "type": "string" 
            }, 
            "fatherPrefix": { 
              "type": "string" 
            }, 
            "fatherPrefixId": { 
              "type": "null" 
            }, 
            "fatherFirstName": { 
              "type": "string" 
            }, 
            "fatherMiddleName": { 
              "type": "string" 
            }, 
            "fatherLastName": { 
              "type": "string" 
            }, 
            "spousePrefix": { 
              "type": "string" 
            }, 
            "spousePrefixId": { 
              "type": "null" 
            }, 
            "spouseFirstName": { 
              "type": "string" 
            }, 
            "spouseMiddleName": { 
              "type": "string" 
            }, 
            "spouseLastName": { 
              "type": "string" 
            }, 
            "motherPrefix": { 
              "type": "string" 
            }, 
            "motherPrefixId": { 
              "type": "null" 
            }, 

 



 

            "motherFirstName": { 
              "type": "string" 
            }, 
            "motherMiddleName": { 
              "type": "string" 
            }, 
            "motherLastName": { 
              "type": "string" 
            }, 
            "gender": { 
              "type": "string" 
            }, 
            "genderId": { 
              "type": "null" 
            }, 
            "dateofBirth": { 
              "type": "string" 
            }, 
            "dateofBirthDateTime": { 
              "type": "null" 
            }, 
            "workEmail": { 
              "type": "string" 
            }, 
            "personalEmail": { 
              "type": "string" 
            }, 
            "permanentAddressCountry": { 
              "type": "string" 
            }, 
            "permanentAddressCountryId": { 
              "type": "null" 
            }, 
            "permanentAddressZipCode": { 
              "type": "string" 
            }, 
            "permanentAddressZipCodeId": { 
              "type": "null" 
            }, 
            "permanentAddressLine1": { 
              "type": "string" 
            }, 
            "permanentAddressLine2": { 
              "type": "string" 
            }, 
            "permanentAddressLine3": { 
              "type": "string" 
            }, 
            "permanentAddressDistrict": { 
              "type": "string" 
            }, 
            "permanentAddressDistrictId": { 
              "type": "null" 
            }, 
            "permanentAddressCity": { 

 



 

              "type": "string" 
            }, 
            "permanentAddressCityId": { 
              "type": "null" 
            }, 
            "permanentAddressState": { 
              "type": "string" 
            }, 
            "permanentAddressOtherState": { 
              "type": "string" 
            }, 
            "permanentAddressStateId": { 
              "type": "null" 
            }, 
            "permanentAddressDocument": { 
              "type": "string" 
            }, 
            "permanentAddressDocumentId": { 
              "type": "null" 
            }, 
            "permanentAddressDocumentOthersValue": { 
              "type": "string" 
            }, 
            "correspondenceAddressCountry": { 
              "type": "string" 
            }, 
            "correspondenceAddressCountryId": { 
              "type": "null" 
            }, 
            "correspondenceAddressZipCode": { 
              "type": "string" 
            }, 
            "correspondenceAddressZipCodeId": { 
              "type": "null" 
            }, 
            "correspondenceAddressLine1": { 
              "type": "string" 
            }, 
            "correspondenceAddressLine2": { 
              "type": "string" 
            }, 
            "correspondenceAddressLine3": { 
              "type": "string" 
            }, 
            "correspondenceAddressDistrict": { 
              "type": "string" 
            }, 
            "correspondenceAddressDistrictId": { 
              "type": "null" 
            }, 
            "correspondenceAddressCity": { 
              "type": "string" 
            }, 
            "correspondenceAddressCityId": { 
              "type": "null" 

 



 

            }, 
            "correspondenceAddressState": { 
              "type": "string" 
            }, 
            "correspondenceAddressOtherState": { 
              "type": "string" 
            }, 
            "correspondenceAddressStateId": { 
              "type": "null" 
            }, 
            "correspondenceAddressDocument": { 
              "type": "string" 
            }, 
            "correspondenceAddressDocumentId": { 
              "type": "null" 
            }, 
            "countryOfResidence": { 
              "type": "string" 
            }, 
            "countryOfResidenceId": { 
              "type": "null" 
            }, 
            "countryOfBirth": { 
              "type": "string" 
            }, 
            "countryOfBirthId": { 
              "type": "null" 
            }, 
            "birthCity": { 
              "type": "string" 
            }, 
            "birthCityId": { 
              "type": "null" 
            }, 
            "passportIssueCountry": { 
              "type": "string" 
            }, 
            "passportIssueCountryId": { 
              "type": "null" 
            }, 
            "passportNumber": { 
              "type": "string" 
            }, 
            "passportExpiryDate": { 
              "type": "string" 
            }, 
            "passportExpiryDateDateTime": { 
              "type": "null" 
            }, 
            "voterIdNumber": { 
              "type": "string" 
            }, 
            "drivingLicenseNumber": { 
              "type": "string" 
            }, 

 



 

            "drivingLicenseExpiryDate": { 
              "type": "string" 
            }, 
            "drivingLicenseExpiryDateDateTime": { 
              "type": "null" 
            }, 
            "aadhaarNumber": { 
              "type": "string" 
            }, 
            "aadhaarVaultReferenceNumber": { 
              "type": "string" 
            }, 
            "nregaNumber": { 
              "type": "string" 
            }, 
            "nprLetterNumber": { 
              "type": "string" 
            }, 
            "directorIdentificationNumber": { 
              "type": "string" 
            }, 
            "formSixty": { 
              "type": "string" 
            }, 
            "formSixtyId": { 
              "type": "null" 
            }, 
            "pan": { 
              "type": "string" 
            }, 
            "ckycNumber": { 
              "type": "string" 
            }, 
            "identityDocument": { 
              "type": "null" 
            }, 
            "identityDocumentId": { 
              "type": "null" 
            }, 
            "politicallyExposed": { 
              "type": "string" 
            }, 
            "politicallyExposedId": { 
              "type": "null" 
            }, 
            "adverseReputationstring": { 
              "type": "null" 
            }, 
            "adverseReputationDetails": { 
              "type": "string" 
            }, 
            "notes": { 
              "type": "string" 
            }, 
            "tags": { 

 



 

              "type": "string" 
            }, 
            "tagsId": { 
              "type": "null" 
            }, 
            "screeningProfile": { 
              "type": "string" 
            }, 
            "screeningReportWhenNil": { 
              "type": "string" 
            }, 
            "screeningReportWhenNilId": { 
              "type": "null" 
            }, 
            "riskProfile": { 
              "type": "null" 
            }, 
            "adverseReputation": { 
              "type": "string" 
            }, 
            "adverseReputationId": { 
              "type": "null" 
            }, 
            "adverseReputationClassification": { 
              "type": "string" 
            }, 
            "adverseReputationClassificationId": { 
              "type": "null" 
            }, 
            "taxDetailDtoList": { 
              "type": "array", 
              "items": [ 
                { 
                  "type": "object", 
                  "properties": { 
                    "Id": { 
                      "type": "integer" 
                    }, 
                    "taxResidencyCountry": { 
                      "type": "string" 
                    }, 
                    "taxResidencyCountryId": { 
                      "type": "null" 
                    }, 
                    "taxIdentificationNumber": { 
                      "type": "string" 
                    }, 
                    "taxResidencyStartDate": { 
                      "type": "string" 
                    }, 
                    "taxResidencyStartDateDateTime": { 
                      "type": "null" 
                    }, 
                    "taxResidencyEndDate": { 
                      "type": "string" 

 



 

                    }, 
                    "taxResidencyEndDateDateTime": { 
                      "type": "null" 
                    } 
                  }, 
                  "required": [ 
                    "Id", 
                    "taxResidencyCountry", 
                    "taxResidencyCountryId", 
                    "taxIdentificationNumber", 
                    "taxResidencyStartDate", 
                    "taxResidencyStartDateDateTime", 
                    "taxResidencyEndDate", 
                    "taxResidencyEndDateDateTime" 
                  ] 
                } 
              ] 
            }, 
            "politicallyExposedClassification": { 
              "type": "string" 
            }, 
            "politicallyExposedClassificationId": { 
              "type": "null" 
            }, 
            "citizenships": { 
              "type": "string" 
            }, 
            "citizenshipsId": { 
              "type": "null" 
            }, 
            "nationalities": { 
              "type": "string" 
            }, 
            "nationalitiesId": { 
              "type": "null" 
            }, 
            "documents": { 
              "type": "null" 
            } 
          }, 
          "required": [ 
            "ekycOTPbased", 
            "ekycOTPbasedId", 
            "segment", 
            "segmentId", 
            "segmentStartDate", 
            "segmentStartDateDateTime", 
            "status", 
            "statusId", 
            "effectiveDate", 
            "effectiveDateDateTime", 
            "minor", 
            "minorId", 
            "maritalStatus", 
            "maritalStatusId", 

 



 

            "occupationType", 
            "occupationTypeOther", 
            "occupationTypeId", 
            "natureOfBusinessOther", 
            "companyIdentificationNumber", 
            "companyRegistrationNumber", 
            "companyRegistrationCountry", 
            "companyRegistrationCountryId", 
            "globalIntermediaryIdentificationNumber", 
            "kycAttestationType", 
            "kycAttestationTypeId", 
            "kycDateOfDeclaration", 
            "kycDateOfDeclarationDateTime", 
            "kycPlaceOfDeclaration", 
            "kycVerificationDate", 
            "kycVerificationDateDateTime", 
            "kycEmployeeName", 
            "kycEmployeeDesignation", 
            "kycVerificationBranch", 
            "kycEmployeeCode", 
            "listed", 
            "listedId", 
            "applicationRefNumber", 
            "documentRefNumber", 
            "regAMLRisk", 
            "regAMLRiskId", 
            "regAMLRiskLastRiskReviewDate", 
            "regAMLRiskLastRiskReviewDateDateTime", 
            "regAMLRiskNextRiskReviewDate", 
            "regAMLRiskNextRiskReviewDateDateTime", 
            "incomeRange", 
            "incomeRangeId", 
            "exactIncome", 
            "incomeCurrency", 
            "incomeCurrencyId", 
            "incomeEffectiveDate", 
            "incomeEffectiveDateDateTime", 
            "incomeDescription", 
            "incomeDocument", 
            "incomeDocumentId", 
            "exactNetworth", 
            "networthCurrency", 
            "networthCurrencyId", 
            "networthEffectiveDate", 
            "networthEffectiveDateDateTime", 
            "networthDescription", 
            "networthDocument", 
            "networthDocumentId", 
            "familyCode", 
            "channel", 
            "channelId", 
            "contactPersonFirstName1", 
            "contactPersonMiddleName1", 
            "contactPersonLastName1", 
            "contactPersonDesignation1", 

 



 

            "contactPersonFirstName2", 
            "contactPersonMiddleName2", 
            "contactPersonLastName2", 
            "contactPersonDesignation2", 
            "contactPersonMobileISD", 
            "contactPersonMobileNo", 
            "contactPersonMobileISD2", 
            "contactPersonMobileNo2", 
            "contactPersonEmailId1", 
            "contactPersonEmailId2", 
            "commencementDate", 
            "commencementDateDateTime", 
            "maidenPrefix", 
            "maidenPrefixId", 
            "maidenFirstName", 
            "maidenMiddleName", 
            "maidenLastName", 
            "relatedPersonCountforCKYC", 
            "proofOfIdSubmitted", 
            "proofOfIdSubmittedId", 
            "products", 
            "productsId", 
            "natureOfBusiness", 
            "natureOfBusinessId", 
            "educationalQualification", 
            "educationalQualificationId", 
            "countryOfOperations", 
            "countryOfOperationsId", 
            "personalMobileISD", 
            "personalMobileNumber", 
            "workMobileISD", 
            "workMobileNumber", 
            "regAMLRiskSpecialCategoryDtoList", 
            "relatedPersonList", 
            "customerRelationDtoList", 
            "constitutionType", 
            "constitutionTypeId", 
            "sourceSystemName", 
            "sourceSystemCustomerCode", 
            "sourceSystemCustomerCreationDate", 
            "sourceSystemCustomerCreationDateDateTime", 
            "uniqueIdentifier", 
            "SystemGeneratedId", 
            "prefix", 
            "prefixId", 
            "firstName", 
            "middleName", 
            "lastName", 
            "fatherPrefix", 
            "fatherPrefixId", 
            "fatherFirstName", 
            "fatherMiddleName", 
            "fatherLastName", 
            "spousePrefix", 
            "spousePrefixId", 

 



 

            "spouseFirstName", 
            "spouseMiddleName", 
            "spouseLastName", 
            "motherPrefix", 
            "motherPrefixId", 
            "motherFirstName", 
            "motherMiddleName", 
            "motherLastName", 
            "gender", 
            "genderId", 
            "dateofBirth", 
            "dateofBirthDateTime", 
            "workEmail", 
            "personalEmail", 
            "permanentAddressCountry", 
            "permanentAddressCountryId", 
            "permanentAddressZipCode", 
            "permanentAddressZipCodeId", 
            "permanentAddressLine1", 
            "permanentAddressLine2", 
            "permanentAddressLine3", 
            "permanentAddressDistrict", 
            "permanentAddressDistrictId", 
            "permanentAddressCity", 
            "permanentAddressCityId", 
            "permanentAddressState", 
            "permanentAddressOtherState", 
            "permanentAddressStateId", 
            "permanentAddressDocument", 
            "permanentAddressDocumentId", 
            "permanentAddressDocumentOthersValue", 
            "correspondenceAddressCountry", 
            "correspondenceAddressCountryId", 
            "correspondenceAddressZipCode", 
            "correspondenceAddressZipCodeId", 
            "correspondenceAddressLine1", 
            "correspondenceAddressLine2", 
            "correspondenceAddressLine3", 
            "correspondenceAddressDistrict", 
            "correspondenceAddressDistrictId", 
            "correspondenceAddressCity", 
            "correspondenceAddressCityId", 
            "correspondenceAddressState", 
            "correspondenceAddressOtherState", 
            "correspondenceAddressStateId", 
            "correspondenceAddressDocument", 
            "correspondenceAddressDocumentId", 
            "countryOfResidence", 
            "countryOfResidenceId", 
            "countryOfBirth", 
            "countryOfBirthId", 
            "birthCity", 
            "birthCityId", 
            "passportIssueCountry", 
            "passportIssueCountryId", 

 



 

            "passportNumber", 
            "passportExpiryDate", 
            "passportExpiryDateDateTime", 
            "voterIdNumber", 
            "drivingLicenseNumber", 
            "drivingLicenseExpiryDate", 
            "drivingLicenseExpiryDateDateTime", 
            "aadhaarNumber", 
            "aadhaarVaultReferenceNumber", 
            "nregaNumber", 
            "nprLetterNumber", 
            "directorIdentificationNumber", 
            "formSixty", 
            "formSixtyId", 
            "pan", 
            "ckycNumber", 
            "identityDocument", 
            "identityDocumentId", 
            "politicallyExposed", 
            "politicallyExposedId", 
            "adverseReputationstring", 
            "adverseReputationDetails", 
            "notes", 
            "tags", 
            "tagsId", 
            "screeningProfile", 
            "screeningReportWhenNil", 
            "screeningReportWhenNilId", 
            "riskProfile", 
            "adverseReputation", 
            "adverseReputationId", 
            "adverseReputationClassification", 
            "adverseReputationClassificationId", 
            "taxDetailDtoList", 
            "politicallyExposedClassification", 
            "politicallyExposedClassificationId", 
            "citizenships", 
            "citizenshipsId", 
            "nationalities", 
            "nationalitiesId", 
            "documents" 
          ] 
        } 
      ] 
    } 
  }, 
  "required": [ 
    "requestId", 
    "sourceSystemName", 
    "purpose", 
    "customerList" 
  ] 
} 

8.2 Response XSD (Purpose 01) 
{ 

 



 

  "$schema": "http://json-schema.org/draft-04/schema#", 
  "type": "object", 
  "properties": { 
    "RequestId": { 
      "type": "string" 
    }, 
    "ValidationCode": { 
      "type": "null" 
    }, 
    "ValidationDescription": { 
      "type": "null" 
    }, 
    "DecryptedData": { 
      "type": "object", 
      "properties": { 
        "OverallStatus": { 
          "type": "string" 
        }, 
        "CustomerResponse": { 
          "type": "array", 
          "items": [ 
            { 
              "type": "object", 
              "properties": { 
                "SourceSystemCustomerCode": { 
                  "type": "string" 
                }, 
                "ValidationOutcome": { 
                  "type": "string" 
                }, 
                "PurposeResponse": { 
                  "type": "array", 
                  "items": [ 
                    { 
                      "type": "object", 
                      "properties": { 
                        "Purpose": { 
                          "type": "string" 
                        }, 
                        "PurposeCode": { 
                          "type": "string" 
                        }, 
                        "Data": { 
                          "type": "object", 
                          "properties": { 
                            "SuggestedAction": { 
                              "type": "string" 
                            }, 
                            "HitsDetected": { 
                              "type": "string" 
                            }, 
                            "HitsCount": { 
                              "type": "integer" 
                            }, 
                            "ConfirmedHits": { 

 



 

                              "type": "string" 
                            }, 
                            "ReportData": { 
                              "type": "string" 
                            }, 
                            "HitResponse": { 
                              "type": "array", 
                              "items": [ 
                                { 
                                  "type": "object", 
                                  "properties": { 
                                    "Source": { 
                                      "type": "string" 
                                    }, 
                                    "WatchlistSourceId": { 
                                      "type": "string" 
                                    }, 
                                    "MatchType": { 
                                      "type": "string" 
                                    }, 
                                    "Score": { 
                                      "type": "number" 
                                    }, 
                                    "ConfirmedMatchingAttributes": { 
                                      "type": "string" 
                                    } 
                                  }, 
                                  "required": [ 
                                    "Source", 
                                    "WatchlistSourceId", 
                                    "MatchType", 
                                    "Score", 
                                    "ConfirmedMatchingAttributes" 
                                  ] 
                                } 
                              ] 
                            } 
                          }, 
                          "required": [ 
                            "SuggestedAction", 
                            "HitsDetected", 
                            "HitsCount", 
                            "ConfirmedHits", 
                            "ReportData", 
                            "HitResponse" 
                          ] 
                        }, 
                        "ValidationCode": { 
                          "type": "string" 
                        }, 
                        "ValidationDescription": { 
                          "type": "string" 
                        }, 
                        "ValidationFailureCount": { 
                          "type": "integer" 

 



 

                        } 
                      }, 
                      "required": [ 
                        "Purpose", 
                        "PurposeCode", 
                        "Data", 
                        "ValidationCode", 
                        "ValidationDescription", 
                        "ValidationFailureCount" 
                      ] 
                    } 
                  ] 
                }, 
                "RelatedPersonResponse": { 
                  "type": "null" 
                }, 
                "RelatedPersonRelationResponse": { 
                  "type": "null" 
                } 
              }, 
              "required": [ 
                "SourceSystemCustomerCode", 
                "ValidationOutcome", 
                "PurposeResponse", 
                "RelatedPersonResponse", 
                "RelatedPersonRelationResponse" 
              ] 
            } 
          ] 
        } 
      }, 
      "required": [ 
        "OverallStatus", 
        "CustomerResponse" 
      ] 
    } 
  }, 
  "required": [ 
    "RequestId", 
    "ValidationCode", 
    "ValidationDescription", 
    "DecryptedData" 
  ] 
} 
 

8.3 Response XSD (Purpose 04) 
 
 
{ 
  "$schema": "http://json-schema.org/draft-04/schema#", 
  "type": "object", 
  "properties": { 
    "RequestId": { 
      "type": "string" 
    }, 

 



 

    "ValidationCode": { 
      "type": "null" 
    }, 
    "ValidationDescription": { 
      "type": "null" 
    }, 
    "DecryptedData": { 
      "type": "object", 
      "properties": { 
        "OverallStatus": { 
          "type": "string" 
        }, 
        "CustomerResponse": { 
          "type": "array", 
          "items": [ 
            { 
              "type": "object", 
              "properties": { 
                "SourceSystemCustomerCode": { 
                  "type": "string" 
                }, 
                "ValidationOutcome": { 
                  "type": "string" 
                }, 
                "PurposeResponse": { 
                  "type": "array", 
                  "items": [ 
                    { 
                      "type": "object", 
                      "properties": { 
                        "Purpose": { 
                          "type": "string" 
                        }, 
                        "PurposeCode": { 
                          "type": "string" 
                        }, 
                        "Data": { 
                          "type": "null" 
                        }, 
                        "ValidationCode": { 
                          "type": "string" 
                        }, 
                        "ValidationDescription": { 
                          "type": "string" 
                        }, 
                        "ValidationFailureCount": { 
                          "type": "integer" 
                        } 
                      }, 
                      "required": [ 
                        "Purpose", 
                        "PurposeCode", 
                        "Data", 
                        "ValidationCode", 
                        "ValidationDescription", 

 



 

                        "ValidationFailureCount" 
                      ] 
                    } 
                  ] 
                }, 
                "RelatedPersonResponse": { 
                  "type": "null" 
                }, 
                "RelatedPersonRelationResponse": { 
                  "type": "null" 
                } 
              }, 
              "required": [ 
                "SourceSystemCustomerCode", 
                "ValidationOutcome", 
                "PurposeResponse", 
                "RelatedPersonResponse", 
                "RelatedPersonRelationResponse" 
              ] 
            } 
          ] 
        } 
      }, 
      "required": [ 
        "OverallStatus", 
        "CustomerResponse" 
      ] 
    } 
  }, 
  "required": [ 
    "RequestId", 
    "ValidationCode", 
    "ValidationDescription", 
    "DecryptedData" 
  ] 
} 
  

 



 

 

9. Validations  
9.1 Main Request Validations: 

 
 

Validation Code Description 

MRV1 API Token is mandatory 

MRV2 Invalid API Token or API Token is not recognized. 

MRV3 The User does not have access to API in Subscription Master 

MRV4 API Subscription is not proper in API token. 

MRV5 API Subscription details not found in API token. 

MRV6 Encryption Details are not configured properly 

MRV7 Purpose 01, 02 and 03 together is not allowed in one single request 

MRV8 Purpose 04 and 05 together is not allowed in one single request 

MRV9 Validations are not configured for purpose "X" 

MRV10 SourceSystemName is mandatory 

MRV11 SourceSystemCustomerCode is mandatory 

MRV12 At least one purpose should be passed 

MRV13 Purpose Code passed is unavailable 

MRV14 ConstitutionType is mandatory 

MRV15 Agency in API token is not matching with Agency in Header Request 

 RelatedPersonSourceSystemCode, CustomerSourceSystemCode and RelationCode in 
 Relations are mandatory if SourceSystemCustomerCode is passed in Related Person 
MRV16 

 RelatedPersonSourceSystemCode in Relations should match with 
MRV17 SourceSystemCustomerCode of Related Person 

MRV18 SourceSystemCustomerCode in Relations should match with 

 



 

 
 SourceSystemCustomerCode of Customer 

MRV19 RelatedPerson SourceSystemCustomerCode is mandatory 

MRV20 ConstitutionType of RelatedPerson should be as per specified values 

MRV21 ConstitutionType of RelatedPerson is mandatory 

MRV22 ConstitutionType of Customer should be as per specified values 

MRV23 Symmetric key for publishing to Kafka not found 

MRV24 An Unexpected Error Occurred <exception>. Contact TSS Support 

MRV25 Since there is no purpose of screening present, hence screening profile SP1 is not 
valid” 

MRV26 ScreeningReportwhenNil value is accepted only if the purpose is 01 

MRV27 Kindly Verify the SourceSystemName present under Request File and Validation 
Configuration 

MRV28 No Special Characters are allowed other than hyphen, underscore, dot and space in 
RequestId 

MRV29 RequestID is mandatory 

MRV42 Request ID for records with Record Type customer and RP should must be identical 

MRV43 Customer Record must exist if a request is for the record type RP 

MRV48 The Value under ApplicationRefNumber for record type Customer and 
RelatedPerson belonging to a particular request should be unique 

MRV49 Value under SourceSystemCustomerCode or ApplicationRefNumber is Mandatory if 
the Purpose passed is 01 or 03 for Customer 

MRV50 Value under SourceSystemCustomerCode or ApplicationRefNumber is Mandatory if 
the Purpose passed is 01 or 03 for Related Person 

MRV51 Value under CustomerApplicationRefNumber, RelatedPersonApplicationRefNumber 
and RelationCode in Relations are mandatory if ApplicationRefNumber is passed in 
Related Person 

MRV52 Value under CustomerApplicationRefNumber in Relation should match the 
ApplicationRefNumber mapped to Record Type Customer 

MRV53 Value under RelatedPersonApplicationRefNumber in Relation should match the 
ApplicationRefNumber mapped to the Record Type RelatedPerson 

MRV54 Api secret is mandatory 

MRV55 Api secret is invalid 

MRV56 ClientSecret is mandatory 

 



 

MRV57 Client secret is invalid 

MRV58 Api not configured in Developerhub for Dynamic ApiToken Generation 

MRV59 Purpose 01, 02, 03, 04 ,05 and 06 can not be allowed in one single request 

MRV64 The Value under SourceSystemCustomerCode for record type Customer and 
RelatedPerson belonging to a particular request should be unique 

MRV65 The constitution type passed for RelatedPerson should be of type Individual only 

MRV66 Request ID for records with Record Type customer and RP should must be identical 

MRV67 Customer Record must exist if a request is for the recordtype RP 

MRV68 Each Related Person should have a minimum of one relation present with the Customer 

MRV69 RelatedPersonSourceSystemCustomerCode should not be passed for Customer and 
should be passed only for RelatedPerson. 

MRV70 The Allowed number of related person is limited to 50 in a single request 

MRV71 AppRefNumber for all Related Persons should be unique 

 
 

9.2 Request Data Validations: 
 
 

    
Validation Type Validation Code Validation Description Examples 

    
   For ex: The field “First 
   Name” is a Mandatory 
   field and if the user leaves 
  If the user does not input 

it blank the user will 
  anything in a required field, 
  receive a validation as 

then Mandatory validation 
M "FirstName-M” is 

Mandatory will occur 
mandatory 

 



 

    
   For ex : The field “PAN” 
   minimum length defined is 
  1 and maximum length 
  If the user does not input the 

  data as specified in Minimum defined is 12 and if the 

  length or exceeds user inputs 16 characters, 

  the value beyond the defined then the user will get the 
  maximum length, Minimum or following validation 

MinLength Min-L Maximum Length message "PAN-Max-L"- 
MaxLength Max-L 

validation will occur. Length should not be 
greater than 12 
characters 

 



 

 
    
   For ex: The permitted 
   allowed characters in 
   VoterIdNumber is / 
   

[ ] { } ( ) - but the user 
   
   inputs special characters 

   &*% in the data then the 
  user will get the following 
  If the user inputs any special validation 
  character in the data other than 

 
  the Special characters allowed, “VoterIdNumber-SC"- 
  then Special Characters Only Specific Characters 
  validation will are allowed 

SpecialCharacters SC occur. 

    
   For ex : The field 
   DateofBirth does not 
   accept a future date, if the 
   

user inputs a date such as 
   
   09-Jun-2027” the user 

   will get the following 
  validation message. 
  If the user inputs a future date 

  value in a field which doesn't “DateofBirth -FDV"- 
  accept a Future date , then Future Future Date or Current 
 Date validation Date is not allowed 

FutureDateValue FDV 
will occur. 

    
   For ex : Format for the 
   field PAN is First 5 
   characters as alphabets, 
   

4th character as 'P' and 
   
   last character is an 

   alphabet, if the user does 
   not input the data 
  If the user does not input the according to the specified 
  data in a specified format , format he will 
  Format Check validation will get the following 

FormatCheck FOR 
occur. 

 



 

 
   validation message 

“PAN-FOR”-First 5 
characters as 
alphabets, 4th 
character as 'P' and 
last character is an 
alphabet. 

    
   For ex : The field 
   SourceSystemName will 
   only accept values from 
   

the Agency Enum, if the 
   
   user inputs values other 

   than the values these, the 
   user will get the following 
   validation message 
    
  If the user inputs data that’s not "SourceSystemName- 
  according to the list of specific SV"-Value passed 
  values mentioned, Specified should be as per 
  

Values validation 
SpecifiedValues SV specified values only 

will occur. 

    
   For ex: The field 
   DateofBirth does not 
   accept a future date, if the 
   

user inputs a date such as 
   
   09-Jun-2027” the user 

   will get the following 
  validation message. 
  If the user inputs a past date 

 
  value in a field which doesn't “DateofBirth -PDV"- 
  accept a Past date the user will Past Date or Current 
 get a Past Date Date is not allowed 

PastDateValue PDV 
Validation. 

 



 

9.3 Relational Validations 
9.3.1. Applicable for Screening 

 
  Validation Validation Applicable 

Validation Validation Name Description Message For 
Code (IND/LE/RP) 

     
  PAN/FormSixty - VS1 - PAN/FormSixty - VS1 -  
  Either value to be provided Either value to be  
  in provided in  

VS1 PAN and FormSixty IND,LE & RP 
PAN or FormSixty. PAN or FormSixty. 

     
  FirstName & Prefix FirstName & Prefix- VS5  
  - VS5 - If Prefix has been - If Prefix has been  
  provided then FirstName is provided then FirstName  
   

mandatory. is 
VS5 FirstName & Prefix IND & RP 

mandatory. 

     
  FirstName & Prefix FirstName & Prefix  
  - VS6 - If FirstName is - VS6 - If FirstName is  
  provided then Prefix is provided then Prefix is  
   

mandatory. mandatory. 
VS6 Prefix & FirstName IND & RP 

     
  FormSixty/Pan - VS18 - FormSixty/Pan - VS18  
  FormSixty should be Yes if - FormSixty should be  
  PAN is not Yes if PAN is not  
   

provided. provided. 
VS18 FormSixty/Pan IND, LE & RP 

     
  Pan/FormSixty - VS19 - Pan/FormSixty - VS19 -  
  FormSixty should be No if PAN FormSixty should be No  
  is provided. if PAN  

VS19 Pan/FormSixty IND, LE & RP 
is provided. 

     
  Kindly pass value in one of "Mandatory fields for  
  these fields: Name, Mobile Screening-  
 Mandatory fields for Number, Email ID, IND"-VS45-Kindly  

VS45 Screening- IND IND 
pass value in one of 

 



 

  PAN, Passport, DIN  or Driving these fields: Name,  
License Number Mobile Number, Email 

ID, PAN, Passport, DIN 
or Driving License 

Number 

   "Mandatory fields for  
    Screening- IND"-  
  Kindly pass value in one of VS46-Kindly pass value  
  these fields: Name, PAN, CIN,  

in one of these fields: 
   Mobile Number or       Email-Id  
 Mandatory fields for Name, PAN, CIN, Mobile  
VS46 Screening- LE Number or Email-Id LE 

  Since there is no active profile "a. If there is no active IND, LE, RP 
  set for the Screening Profile to profile set for the 
  be used, please activate anyone Screening Profile to be 

VS51 Active&DefaultProfile screening profile. used, please activate any 
 one screening profile. 
Screening profile passed is not b. Screening profile 
active, please select an active 

passed is not active, 
profile. 

please select an active 
profile 

VS55 PassportNumber is  VS55 : PassportNumber is IND 
Mandatory if value is passed  If the value is passed in either required if value is passed 
under Passport Issue Country PassportIssueCountry or in either 
or Passport Expiry Date PassportExpiry Date or both  PassportIssueCountry or 

without the Passport Number PassportExpiryDate 
then in that case the validation 
would occur 

VS56 Driving License Number is If the value is passed in just VS56 : IND 
Mandatory if value is passed Driving License Expiry Date and DrivingLicenseNumber is 
under not in the Driving License required if the value in 
DrivingLicenseExpiryDate Number, then in that case this passed in just 

validation would occur DrivingLicenseExpiryDat
e 

 



 

 
 

9.3.2. General Validations 
 

     
    Applicab 
Valid    le For 
ation    (IND/LE/ 
Code Validation Name Validation Description Validation Message RP) 

     
  IncomeEffectiveDate - VS3 - IncomeEffectiveDate - VS3 -  
  IncomeEffectiveDate is IncomeEffectiveDate is  
  mandatory if ExactIncome is mandatory if ExactIncome is  
VS3 IncomeEffectiveDate provided. provided. IND & LE 

     
  FatherPrefix & FatherPrefix &  
  FatherFirstName - VS7 - If FatherFirstName - VS7 - If  
  Father prefix has been Father prefix has been  
  provided then provided then  
 FatherPrefix & FatherFirstName is FatherFirstName is  
VS7 FatherFirstName mandatory. mandatory. IND & RP 

     
  FatherFirstName & FatherPrefix FatherFirstName & FatherPrefix  
  - VS8- If FatherFirstName is - VS8 - If FatherFirstName is  
  provided then Father prefix is provided then Father prefix is  
 FatherFirstName & mandatory mandatory  
VS8 FatherPrefix IND & RP 

 
 
 
 

 



 

     
  SpousePrefix & SpousePrefix &  
  SpouseFirstName - VS9 - If SpouseFirstName - VS9 - If  
  SpousePrefix has been SpousePrefix has been  
  provided, then provided, then  
 SpousePrefix & SpouseFirstName is SpouseFirstName is  
VS9 SpouseFirstName mandatory. mandatory. IND & RP 

     
  SpouseFirstName & SpousePrefix SpouseFirstName & SpousePrefix  
  - VS10 - If SpouseFirstName is - VS10 - If SpouseFirstName is  
  provided then SpousePrefix is provided then SpousePrefix is  
 SpouseFirstName & mandatory. mandatory.  
VS10 SpousePrefix IND & RP 

     
  MaidenPrefix & MaidenPrefix &  
  MaidenFirstName - VS11 - If MaidenFirstName - VS11 - If  
  MaidenPrefix has been MaidenPrefix has been  
  provided, then provided, then  
 MaidenPrefix & MaidenFirstName is mandatory. MaidenFirstName is mandatory.  
VS11 MaidenFirstName IND & RP 

     
  MotherPrefix & MotherPrefix &  
  MotherFirstName - VS12 - If MotherFirstName - VS12 - If  
  MotherPrefix has been MotherPrefix has been  
  provided, then provided, then  
 MotherPrefix & MotherFirstName is mandatory. MotherFirstName is mandatory.  
VS12 MotherFirstName IND & RP 

     
  KYCAttestationType & KYCAttestationType &  
  IsEKYCOTPBased - VS13 - IsEKYCOTPBased - VS13 -  
  KYCAttestationType is invalid as KYCAttestationType is invalid as  
 KYCAttestationType & value passed in EKYCOTPbased value passed in EKYCOTPbased  
VS13 IsEKYCOTPBased is Yes. is Yes. IND 

     
  PersonalMobileNumber & ISD PersonalMobileNumber & ISD  
  - VS14 - - VS14 -  
  PersonalMobileNumber PersonalMobileNumber  
  should not be Less than or should not be Less than or  
 PersonalMobileNumber & greater than 10 digits if greater than 10 digits if IND, LE 
VS14 PersonalMobileNumberISD PersonalMobileISD = 91. PersonalMobileISD = 91. & RP 

     
 WorkMobileNumber & WorkMobileNumber & ISD - WorkMobileNumber & ISD - IND, LE 
VS15 WorkMobileNumberISD VS15 - Length of the VS15 - Length of the & RP 

 

 



 

 
  IncomeRange IncomeRange  

is provided is provided 

     
  OccupationType & OccupationType &  
  OccupationTypeOther - VS25 OccupationTypeOther - VS25  
  -If OccupationType is Others -If OccupationType is Others  
 OccuptationType & then OccuptationTypeOther is then OccuptationTypeOther is IND, LE 
VS25 OccupationTypeOther required required & RP 

     
  NatureOfBusiness & NatureOfBusiness &  
  NatureOfBusinessOthers - NatureOfBusinessOthers -  
  VS30 - VS30 -  
  NatureOfBusinessOthers is NatureOfBusinessOthers is  
 NatureOfBusiness & mandatory if mandatory if IND, LE 
VS30 NatureOfBusinessOthers NatureOfBusiness is Others NatureOfBusiness is Others & RP 

     
  CorrespondenceAddressLine 1 is CorrespondenceAddressLine 1 is  
 CorrespondenceAddressLine 1 & passed then passed then  
 PermanentAddressLine1 PermanentAddressLine1 is PermanentAddressLine1 is IND, LE 
VS31 mandatory mandatory & RP 

     
  AdverseReputation&Adverse AdverseReputation&Adverse  
  ReputationClassification- VS32 - ReputationClassification- VS32 -  
  If AdverseReputation value is If AdverseReputation value is  
  "No" then do not accept value in "No" then do not accept value in  
  AdverseReputationClassificati on AdverseReputationClassificati on  
  and AdverseReputationDetails and AdverseReputationDetails  
 AdverseReputation&Adverse  
VS32 ReputationClassification IND 

     
  PEP&PEPClassification-VS33- If PEP&PEPClassification-VS33- If  
  PEP value is "NotAPEP" then do PEP value is "NotAPEP" then do  
  not accept values in not accept values in  
VS33 PEP &PEPClassification PEPClassification PEPClassification IND 

     
  DIN should be available in the DIN - VS2 - DIN should be  
  Identification details section with available in the Identification  
  ID Type as Director Id Number details section with ID Type as  
  for related party with Director Id Number for related  
  relationship as Directors. party with relationship as Related 
VS2 DIN Directors. Person 

 
 

 



 

 
     
   IncomeRange IncomeExactValue  
   IncomeCurrency  
   IncomeEffectiveDate  
   IncomeDescription  
   IncomeDocumentValue-VS36  
 IncomeRange  -Value under  
 IncomeExactValue Value under IncomeExactValue  
 IncomeCurrency IncomeExactValue IncomeCurrency &  
 IncomeEffectiveDate IncomeCurrency & IncomeEffectiveDate is  
 IncomeDescription IncomeEffectiveDate is Mandatory IND,LE& 
VS36 IncomeDocument Mandatory RP 

     
   ExactNetworth  
   NetworthCurrency  
   NetworthEffectiveDate  
   NetworthDescription  
 ExactNetworth  NetworthDocument-VS37-  
 NetworthCurrency Value under ExactNetworth, Value under ExactNetworth,  
 NetworthEffectiveDate NetworthCurrency and NetworthCurrency and  
 NetworthDescription NetworthEffectiveDate is NetworthEffectiveDate is IND,LE& 
V37 NetworthDocument Mandatory Mandatory RP 

 



 

     
   TaxResidencyCountry  
   TaxIdentificationNumber  
   TaxResidencyStartDate  
   TaxResidencyLastDate -VS38-  
  Value under Value under  
 TaxResidencyCountry TaxIdentificationNumber, TaxIdentificationNumber,  
 TaxIdentificationNumber TaxResidencyCountry & TaxResidencyCountry &  
 TaxResidencyStartDate TaxResidencyStartDate is TaxResidencyStartDate is IND , LE 
V38 TaxResidencyLastDate Mandatory Mandatory &RP 

     
   RegAMLSpecialCategory  
   RegAMLSpecialCategoryStart  
  Value under Date-VS39-Value under  
 RegAMLSpecialCategory RegAMLSpecialCategory & RegAMLSpecialCategory &  
 RegAMLSpecialCategoryStart RegAMLSpecialCategoryStart RegAMLSpecialCategoryStart IND , LE 
V39 Date Date is Mandatory Date is Mandatory &RP 

     
 RegAML Value under RegAML & RegAML  
 RegAMLLastReviewDate(Effe RegAMLLastReviewDate is RegAMLLastReviewDate(Effe IND , LE 
V40 ctiveDate) Mandatory ctiveDate)-VS40- Value under &RP 

 
 
 

 

 



 

   RegAML &  
RegAMLLastReviewDate is 
Mandatory 

     
   Segment StartDate-VS41-  
 Segment Value under Segment & Value under Segment & IND , LE 
V41 StartDate StartDate is Mandatory StartDate is Mandatory &RP 

     
   Status EffectiveDate-VS42-  
 Status Value under Status & Value under Status & IND , LE 
V42 EffectiveDate EffectiveDate is Mandatory EffectiveDate is Mandatory &RP 

     
   PersonalMobileISD &  
   PersonalMobileNumber-  
  Value under VS43- Value under  
  PersonalMobileISD and PersonalMobileISD and  
 PersonalMobileISD & PersonalMobileNumber is PersonalMobileNumber is IND , LE 
V43 PersonalMobileNumber mandatory mandatory &RP 

     
   WorkMobileISD&  
   WorkMobileNumber-VS44- Value  
   under CorrespondanceMobileISD  
   and CorrespondanceMobileNumb  
  Value under WorkMobileISD er is mandatory  
 WorkMobileISD& and WorkMobileNumberis IND , LE 
V44 WorkMobileNumber mandatory &RP 

 PermanantAddressState & If the value under "PermanantAddressState & IND, LE & RP 
VS53 PermanentAddressOtherState PermanentAddressState is "Oth", PermanentAddressOtherState" - 

 then the value under VS53 - Value in 
PermanentAddressOtherState is PermanentAddressOtherState is 
mandatory.  mandatory If the value in 

 PermanentAddressState is Others 

 

VS54 CorrepondenceAddressState & If the value under "CorrespondenceAddressState & IND, LE,RP 

CorrespondenceOtherState CorrespondenceAddressState is CorrespondenceAddressOtherStat

 "Oth", then the value under e" - VS54 - Value in 
CorrespondenceAddressOtherStat CorrespondenceAddressOtherStat
e is mandatory.  e is mandatory if the value in 

 CorrespondenceAddressState is 
Others 

 



 

VS57 EmployeeCode & RMType If EmployeeCode has been EmployeeCode & RMType - VS57 - IND 

provided then RMType is If EmployeeCode has been 
mandatory provided then RMType is 

mandatory. 

VS58 RMType & EmployeeCode If RMType has been provided then RMType & EmployeeCode - VS58 - IND 

EmployeeCode is mandatory If RMType has been provided then 
EmployeeCode is mandatory. 

VS59 RMName,RMType and If RMName has been provided RMName,RMType and IND 

EmployeeCode then EmployeeCode and RMType EmployeeCode - VS59 - If RMName 
is mandatory has been provided then 

EmployeeCode and RMType is 
mandatory 

VS60 RMFromDate If FromDate has been provided RMFromDate - VS60 - If FromDate IND 

then (EmployeeCode & RMType) has been provided then 
or (RMName, EmployeeCode & (EmployeeCode & RMType) or 
RMType) is mandatory (RMName, EmployeeCode & 

RMType) is mandatory.