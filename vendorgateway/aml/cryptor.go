package aml

import (
	"bytes"
	"context"
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha512"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/text/encoding/unicode"
	"golang.org/x/text/transform"

	cryptoPkg "github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendorgateway/config"
)

type Cryptor struct {
	cryptoPkg.Cryptor
	publicKey        *rsa.PublicKey
	tenantPrivateKey *rsa.PrivateKey
	requestId        string
}

type EncryptedRequest struct {
	RequestId     string `json:"requestId"`
	EncryptionKey string `json:"encryptionKey"`
	EncryptedData string `json:"encryptedData"`
	Signature     string `json:"signature"`
}

type EncryptedResponse struct {
	RequestID             string `json:"requestId"`
	ValidationCode        string `json:"validationCode"`
	ValidationDescription string `json:"validationDescription"`
	Signature             string `json:"signature"`
	EncryptionKey         string `json:"encryptionKey"`
	EncryptedData         string `json:"encryptedData"`
}

func NewCryptor(
	commonConf *config.TSSCloudCommon,
	tenantConf *config.TSSCloudTenant,
	requestId string,
) (*Cryptor, error) {
	if commonConf == nil {
		return nil, errors.New("common config is nil")
	}
	if tenantConf == nil {
		return nil, errors.New("tenant config is nil")
	}
	if requestId == "" {
		return nil, errors.New("request id is empty")
	}
	publicKey, err := getPublicKey(commonConf.PublicKeyCertPEMBase64)
	if err != nil {
		return nil, errors.Wrap(err, "error getting public key from base-64 encoded public key certificate")
	}
	privateKey, err := getPrivateKeyForTenant(tenantConf.PrivateKeyPEMBase64)
	if err != nil {
		return nil, errors.Wrap(err, "error getting private key for tenant")
	}
	return &Cryptor{
		publicKey:        publicKey,
		tenantPrivateKey: privateKey,
		requestId:        requestId,
	}, nil
}

func getPublicKey(publicKeyCertBase64 string) (*rsa.PublicKey, error) {
	publicKeyPEMBytes, err := base64.StdEncoding.DecodeString(publicKeyCertBase64)
	if err != nil {
		return nil, errors.Wrap(err, "error decoding base-64 encoded public key")
	}
	publicKeyPEMBlock, _ := pem.Decode(publicKeyPEMBytes)
	if publicKeyPEMBlock == nil {
		return nil, errors.New("error decoding public key PEM bytes into PEM block")
	}
	cert, err := x509.ParseCertificate(publicKeyPEMBlock.Bytes)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing public key pem as x509 certificate")
	}
	rsaPublicKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		return nil, errors.New("error casting public key of x509 certificate as RSA public key")
	}
	return rsaPublicKey, nil
}

func getPrivateKeyForTenant(privateKeyPEMBase64 string) (*rsa.PrivateKey, error) {
	privateKeyPEMBytes, err := base64.StdEncoding.DecodeString(privateKeyPEMBase64)
	if err != nil {
		return nil, errors.Wrap(err, "error decoding base-64 encoded private key")
	}
	privateKeyPEMBlock, _ := pem.Decode(privateKeyPEMBytes)
	if privateKeyPEMBlock == nil {
		return nil, errors.New("error decoding key PEM bytes into PEM block")
	}
	privateKey, err := x509.ParsePKCS8PrivateKey(privateKeyPEMBlock.Bytes)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing key PEM in PKCS8 form")
	}
	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return nil, errors.New("error casting private key as RSA private key")
	}
	return rsaPrivateKey, nil
}

func (c *Cryptor) EncryptAndSign(ctx context.Context, data []byte, _ string) ([]byte, error) {
	// TODO: Disable for simulation
	// Encrypt data using a random AES session key
	sessionKey := make([]byte, 16)
	_, err := rand.Read(sessionKey)
	if err != nil {
		return nil, errors.Wrap(err, "error generating random bytes for session key")
	}

	// Encrypt session key
	sessionKeyBase64 := base64.StdEncoding.EncodeToString(sessionKey)
	encryptedSessionKey, err := rsa.EncryptPKCS1v15(rand.Reader, c.publicKey, []byte(sessionKeyBase64))
	if err != nil {
		return nil, errors.Wrap(err, "error encrypting base-64 encoded session key with RSA using PKCS #1 v1.5 padding scheme")
	}
	encryptedSessionKeyBase64 := base64.StdEncoding.EncodeToString(encryptedSessionKey)

	// Encrypt data using session key
	encryptedData, err := aesEncryptDataUsingSessionKey(data, sessionKey)
	if err != nil {
		return nil, errors.Wrap(err, "error encrypting data with RSA using session key")
	}
	encryptedDataBase64 := base64.StdEncoding.EncodeToString(encryptedData)

	// Sign encrypted data and session key
	signature, err := c.signData([]byte(encryptedDataBase64 + encryptedSessionKeyBase64))
	if err != nil {
		return nil, errors.Wrap(err, "error signing encrypted and base-64 encoded data and session key appended")
	}
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)
	encryptedReq := &EncryptedRequest{
		RequestId:     c.requestId,
		EncryptionKey: encryptedSessionKeyBase64,
		EncryptedData: encryptedDataBase64,
		Signature:     signatureBase64,
	}
	encryptedReqBytes, err := json.Marshal(encryptedReq)
	if err != nil {
		return nil, errors.Wrap(err, "error marshaling encrypted request")
	}
	return encryptedReqBytes, nil
}

func aesEncryptDataUsingSessionKey(data, key []byte) ([]byte, error) {
	data = padPKCS7(data, aes.BlockSize)
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	encryptedData := make([]byte, len(data))
	mode := newECBEncrypter(block)
	mode.CryptBlocks(encryptedData, data)
	return encryptedData, nil
}

func padPKCS7(input []byte, blockSize int) []byte {
	padding := blockSize - (len(input) % blockSize)
	pad := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(input, pad...)
}

// ecbEncrypter encrypts a single block using ECB mode
type ecbEncrypter struct {
	block     cipher.Block
	blockSize int
}

func newECBEncrypter(block cipher.Block) *ecbEncrypter {
	return &ecbEncrypter{
		block:     block,
		blockSize: block.BlockSize(),
	}
}

func (x *ecbEncrypter) BlockSize() int { return x.blockSize }

func (x *ecbEncrypter) CryptBlocks(dst, src []byte) {
	if len(dst) < len(src) {
		// CryptBlocks interface implementations are expected to panic if dst is smaller than src.
		panic("output smaller than input")
	}
	if len(src)%x.blockSize != 0 {
		panic("input not full blocks")
	}
	for len(src) > 0 {
		x.block.Encrypt(dst, src[:x.blockSize])
		src = src[x.blockSize:]
		dst = dst[x.blockSize:]
	}
}

func (c *Cryptor) signData(data []byte) ([]byte, error) {
	utf16LEEncodedData, err := utf16LEEncode(data)
	if err != nil {
		return nil, errors.Wrap(err, "error converting string to UTF-16LE bytes")
	}
	checksum := sha512.Sum512(utf16LEEncodedData)
	signature, err := rsa.SignPKCS1v15(nil, c.tenantPrivateKey, crypto.SHA512, checksum[:])
	if err != nil {
		return nil, errors.Wrap(err, "error signing data checksum with RSA PKCS #1 v1.5 using tenant private key and SHA-512")
	}
	return signature, nil
}

func utf16LEEncode(data []byte) ([]byte, error) {
	utf16leEncoder := unicode.UTF16(unicode.LittleEndian, unicode.IgnoreBOM).NewEncoder()
	encodedBytes, _, err := transform.Bytes(utf16leEncoder, data)
	if err != nil {
		return nil, errors.Wrap(err, "error encoding string to UTF-16LE bytes")
	}
	return encodedBytes, nil
}

func (c *Cryptor) DecryptAndVerify(_ context.Context, encryptedResBytes []byte, _ string) ([]byte, error) {
	// TODO: Disable for simulation
	encryptedRes := &EncryptedResponse{}
	err := json.Unmarshal(encryptedResBytes, &encryptedRes)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling encrypted response")
	}
	logger.InfoNoCtx("encrypted response", zap.Any("encryptedRes", encryptedRes))
	// {
	//    "requestId": "1235467",
	//    "validationCode": "MRV10",
	//    "validationDescription": "SourceSystemName is mandatory",
	//    "signature": "",
	//    "encryptionKey": "",
	//    "encryptedData": ""
	// }

	// {
	//    "requestId": "1235467",
	//    "validationCode": "MRV49",
	//    "validationDescription": "Value under SourceSystemCustomerCode or ApplicationRefNumber is Mandatory if the Purpose passed is 01 or 03 for Customer",
	//    "signature": "",
	//    "encryptionKey": "",
	//    "encryptedData": ""
	// }

	if encryptedRes.ValidationCode != "" {
		return encryptedResBytes, nil
	}

	err = validateResponse(encryptedRes)
	if err != nil {
		return nil, errors.Wrap(err, "error validating encrypted response")
	}
	signingParams := encryptedRes.EncryptedData + encryptedRes.EncryptionKey
	verified, err := c.verifySignature([]byte(signingParams), encryptedRes.Signature)
	if err != nil {
		return nil, errors.Wrap(err, "error verifying signature")
	}
	if !verified {
		return nil, errors.New("signature verification failed")
	}
	sessionKey, err := c.decryptEncryptionKey(encryptedRes.EncryptionKey)
	if err != nil {
		return nil, errors.Wrap(err, "error decrypting encryption key")
	}
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedRes.EncryptedData)
	if err != nil {
		return nil, errors.Wrap(err, "error decoding base-64 encoded encrypted data")
	}
	decryptedData, err := c.decryptData(encryptedData, sessionKey)
	if err != nil {
		return nil, errors.Wrap(err, "error decrypting data")
	}
	return decryptedData, nil
}

func validateResponse(res *EncryptedResponse) error {
	if res.Signature == "" {
		return errors.New("empty signature in encrypted response")
	}
	if res.EncryptionKey == "" {
		return errors.New("empty encryption key in encrypted response")
	}
	if res.EncryptedData == "" {
		return errors.New("empty encrypted data in encrypted response")
	}
	return nil
}

func (c *Cryptor) verifySignature(data []byte, base64EncodedSignature string) (bool, error) {
	utf16LEEncodedData, err := utf16LEEncode(data)
	if err != nil {
		return false, errors.Wrap(err, "error converting data to UTF-16LE bytes")
	}
	signature, err := base64.StdEncoding.DecodeString(base64EncodedSignature)
	if err != nil {
		return false, errors.Wrap(err, "error decoding base-64 encoded signature")
	}
	checksum := sha512.Sum512(utf16LEEncodedData)
	err = rsa.VerifyPKCS1v15(c.publicKey, crypto.SHA512, checksum[:], signature)
	if err != nil {
		return false, errors.Wrap(err, "error verifying signature with RSA PKCS #1 v1.5 using public key and SHA-512")
	}
	return true, nil
}

func (c *Cryptor) decryptEncryptionKey(encryptionKey string) ([]byte, error) {
	encryptedSessionKey, err := base64.StdEncoding.DecodeString(encryptionKey)
	if err != nil {
		return nil, errors.Wrap(err, "error decoding base-64 encoded session key")
	}
	sessionKeyBase64, err := rsa.DecryptPKCS1v15(rand.Reader, c.tenantPrivateKey, encryptedSessionKey)
	if err != nil {
		return nil, errors.Wrap(err, "error decrypting session key with RSA PKCS #1 v1.5 using tenant private key")
	}
	sessionKey, err := base64.StdEncoding.DecodeString(string(sessionKeyBase64))
	if err != nil {
		return nil, errors.Wrap(err, "error decoding base-64 encoded session key")
	}
	return sessionKey, nil
}

func unPadPKCS7(data []byte) ([]byte, error) {
	length := len(data)
	paddingSize := int(data[length-1])
	if paddingSize > length {
		return nil, errors.Errorf("padding size: %d is greater than data length: %d", paddingSize, length)
	}
	return data[:length-paddingSize], nil
}

func (c *Cryptor) decryptData(encryptedData, sessionKey []byte) ([]byte, error) {
	block, err := aes.NewCipher(sessionKey)
	if err != nil {
		return nil, errors.Wrap(err, "error creating a new cipher")
	}
	if len(encryptedData)%aes.BlockSize != 0 {
		return nil, errors.New("encrypted data is not a multiple of the block size")
	}
	decrypter := NewECBDecrypter(block)
	decryptedData := make([]byte, len(encryptedData))
	decrypter.CryptBlocks(decryptedData, encryptedData)
	decryptedData, err = unPadPKCS7(decryptedData)
	if err != nil {
		return nil, errors.Wrap(err, "error removing padding from decrypted data")
	}
	return decryptedData, nil
}

func NewECBDecrypter(block cipher.Block) cipher.BlockMode {
	return ecbDecrypter{block: block}
}

type ecbDecrypter struct {
	block cipher.Block
}

func (x ecbDecrypter) BlockSize() int {
	return x.block.BlockSize()
}

func (x ecbDecrypter) CryptBlocks(dst, src []byte) {
	if len(dst) < len(src) {
		// CryptBlocks interface implementations are expected to panic if dst is smaller than src.
		panic("crypto/cipher: output smaller than input")
	}
	if len(src)%x.block.BlockSize() != 0 {
		panic("crypto/cipher: input not full blocks")
	}
	for len(src) > 0 {
		x.block.Decrypt(dst, src[:x.block.BlockSize()])
		src = src[x.block.BlockSize():]
		dst = dst[x.block.BlockSize():]
	}
}
