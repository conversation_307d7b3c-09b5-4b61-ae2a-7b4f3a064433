package aml

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendorgateway/config"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

const (
	AS501DateFormat       = "02-Jan-2006"
	AS501Purpose01        = "01" // Initial Screening
	AS501Purpose04        = "04" // Continuous Screening
	AS501CountryCodeIndia = "IND"
)

type as501ScreenCustomerReq struct {
	req        *amlVgPb.InitiateScreeningRequest
	tenantConf *config.TSSCloudTenant
	*SecureExchange
}

func (s *as501ScreenCustomerReq) URL() string {
	return s.tenantConf.URL + "/customerinfo/as501"
}

func (s *as501ScreenCustomerReq) HTTPMethod() string {
	return http.MethodPost
}

func (s *as501ScreenCustomerReq) Add(req *http.Request) *http.Request {
	req.Header.Add("Cluster", "CL1_User")
	req.Header.Add("Domain", "https://epifitechnologiespvtltd-sb.trackwizz.app/customerinfo/as501")
	req.Header.Add("ApiToken", s.tenantConf.APIToken)
	logger.InfoNoCtx("AS501 request header", zap.Any("header", req.Header))
	return req
}

func (s *as501ScreenCustomerReq) GetResponse() vendorapi.Response {
	return &as501ScreenCustomerRes{}
}

func (s *as501ScreenCustomerReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (s *as501ScreenCustomerReq) Marshal() ([]byte, error) {
	customerData, err := s.convertToAS501CustomerData()
	if err != nil {
		return nil, errors.Wrap(err, "error converting customer data")
	}
	req := &tss.AS501Request{
		RequestId:        s.req.GetVendorRequestId(),
		SourceSystemName: s.tenantConf.Name,
		// TODO: Change purpose code
		Purpose:      "03",
		CustomerList: []*tss.CustomerData{customerData},
	}
	reqJson, err := protojson.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal request data to JSON")
	}
	return reqJson, nil
}

func (s *as501ScreenCustomerReq) convertToAS501CustomerData() (*tss.CustomerData, error) {
	gender, err := getGenderCode(s.req.GetCustomerDetails().GetGender())
	if err != nil {
		return nil, errors.Wrap(err, "error converting gender to code")
	}
	// TODO: Map on-prem products to correct cloud products / screening lists
	product, err := getProductCode(s.req.GetProduct())
	if err != nil {
		return nil, errors.Wrap(err, "error converting product to code")
	}
	nationality, err := getNationalityCode(s.req.GetCustomerDetails().GetNationality())
	if err != nil {
		return nil, errors.Wrap(err, "error converting nationality to code")
	}
	permanentAddress := s.req.GetCustomerDetails().GetPermanentAddress()
	correspondenceAddress := s.req.GetCustomerDetails().GetCorrespondenceAddress()
	dateOfBirth := ""
	if s.req.GetCustomerDetails().GetDateOfBirth() != nil {
		dateOfBirth = datetime.DateToString(s.req.GetCustomerDetails().GetDateOfBirth(), AS501DateFormat, datetime.IST)
		// TODO: error if date of birth is in future
	}

	// Convert address lines
	permanentAddressLines := permanentAddress.GetAddressLines()
	correspondenceAddressLines := correspondenceAddress.GetAddressLines()
	permanentAddressLine1 := ""
	permanentAddressLine2 := ""
	permanentAddressLine3 := ""
	if len(permanentAddressLines) > 0 {
		permanentAddressLine1 = permanentAddressLines[0]
		if len(permanentAddressLines) > 1 {
			permanentAddressLine2 = permanentAddressLines[1]
			if len(permanentAddressLines) > 2 {
				permanentAddressLine3 = permanentAddressLines[2]
			}
		}
	}
	correspondenceAddressLine1 := ""
	correspondenceAddressLine2 := ""
	correspondenceAddressLine3 := ""
	if len(correspondenceAddressLines) > 0 {
		correspondenceAddressLine1 = correspondenceAddressLines[0]
		if len(correspondenceAddressLines) > 1 {
			correspondenceAddressLine2 = correspondenceAddressLines[1]
			if len(correspondenceAddressLines) > 2 {
				correspondenceAddressLine3 = correspondenceAddressLines[2]
			}
		}
	}
	return &tss.CustomerData{
		// SourceSystemName: "EPIFITech",
		// // TODO: Understand diff b/w actor id and request id and send it in appropriate fields to TSS
		// SourceSystemCustomerCode:         "808145456465121",
		// UniqueIdentifier:                 "LE8081",
		// SourceSystemCustomerCreationDate: "11-Nov-2020",
		// // TODO: Find corresponding fields for
		// // 					RequestId:        s.req.GetVendorRequestId(),
		// //			RecordIdentifier: s.req.GetRecordIdentifier(),
		// //			SystemName:       tssConfigForOwner.SystemName,
		// //			ParentCompany:    tssConfigForOwner.ParentCompany,
		// //			Products: &Products{
		// //				Product: []string{product},
		// //			},
		//
		// FirstName: "Nirav modi deepak",
		// // MiddleName:           customerDetails.GetName().GetMiddleName(),
		// // LastName:             customerDetails.GetName().GetLastName(),
		// KycVerificationBranch: "Mumbai",
		// Products:              "MF,LI",
		// ConstitutionType:      "1", // Individual
		// Gender:                "01",
		// Pan:                   "**********",
		//
		// DirectorIdentificationNumber:  "00190509",
		// DrivingLicenseNumber:          "DL935152",
		// PassportNumber:                "M110522",
		// CompanyIdentificationNumber:   "U71236MH2015PLC748631",
		// DateOfBirth:                   "12-Dec-2003",
		// Nationalities:                 "IND, GBR",
		// CorrespondenceAddressCountry:  "IND",
		// CorrespondenceAddressZipCode:  "403702",
		// CorrespondenceAddressLine1:    "Mamta Nagar, Gavdevi, Flat 101",
		// CorrespondenceAddressLine2:    "Koliwada",
		// CorrespondenceAddressLine3:    "Mahim West",
		// CorrespondenceAddressDistrict: "Mumbai",
		// CorrespondenceAddressCity:     "Mumbai",
		// CorrespondenceAddressState:    "MH",
		// PermanentAddressCountry:       "IND",
		// PermanentAddressZipCode:       "403707",
		// PermanentAddressLine1:         "Gokulnagri, Chawl no 15, Room no- 101",
		// PermanentAddressLine2:         "Near MJ College",
		// PermanentAddressLine3:         "Behind RK Hotel, Mumbai",
		// PermanentAddressDistrict:      "Mumbai",
		// PermanentAddressCity:          "Mumbai",
		// PermanentAddressState:         "MH",

		SourceSystemName: s.tenantConf.Name,
		// TODO: Understand diff b/w actor id and request id and send it in appropriate fields to TSS
		SourceSystemCustomerCode: s.req.GetUserRecordId(),
		UniqueIdentifier:         s.req.GetVendorRequestId(),
		// SourceSystemCustomerCreationDate: datetime.DateToString(s.req.GetCustomerDetails().GetDateOfBirth(), AS501DateFormat, datetime.IST),
		// TODO: Add other fields if FL1-FL43 fields if mandatory
		FirstName:            s.req.GetCustomerDetails().GetName().GetFirstName(),
		DateOfBirth:          dateOfBirth,
		Nationalities:        strings.Join([]string{nationality}, ","),
		PassportNumber:       s.req.GetCustomerDetails().GetPassportNumber(),
		DrivingLicenseNumber: s.req.GetCustomerDetails().GetDrivingLicenseNumber(),

		// Correspondence address
		CorrespondenceAddressLine1:   correspondenceAddressLine1,
		CorrespondenceAddressLine2:   correspondenceAddressLine2,
		CorrespondenceAddressLine3:   correspondenceAddressLine3,
		CorrespondenceAddressCity:    correspondenceAddress.GetLocality(),
		CorrespondenceAddressState:   correspondenceAddress.GetAdministrativeArea(),
		CorrespondenceAddressCountry: AS501CountryCodeIndia, // Default to India
		CorrespondenceAddressZipCode: correspondenceAddress.GetPostalCode(),

		// Permanent address
		PermanentAddressLine1:   permanentAddressLine1,
		PermanentAddressLine2:   permanentAddressLine2,
		PermanentAddressLine3:   permanentAddressLine3,
		PermanentAddressCity:    permanentAddress.GetLocality(),
		PermanentAddressState:   permanentAddress.GetAdministrativeArea(),
		PermanentAddressCountry: AS501CountryCodeIndia, // Default to India
		PermanentAddressZipCode: permanentAddress.GetPostalCode(),
		Gender:                  gender,
		Pan:                     s.req.GetCustomerDetails().GetPanNumber(),
		Products:                product,
		ConstitutionType:        "1", // Individual

		// TODO: Find AS501 fields for the below fields
		// CreateAlert:                  "Yes",
		// ScreeningCategory:            ScreeningCategory,

		// TODO: Add other fields if FL1-FL43 fields if mandatory
	}, nil
}

func getGenderCode(gender common.Gender) (string, error) {
	switch gender {
	case common.Gender_MALE:
		return "01", nil
	case common.Gender_FEMALE:
		return "02", nil
	case common.Gender_TRANSGENDER:
		return "03", nil
	default:
		return "", errors.New(fmt.Sprintf("unsupported gender type: %s", gender.String()))
	}
}

func getProductCode(product amlVgPb.Product) (string, error) {
	switch product {
	case amlVgPb.Product_PRODUCT_MUTUAL_FUND:
		return "MF", nil
	default:
		return "", errors.Errorf("unsupported product type: %s", product.String())
	}
}

func getNationalityCode(nationality common.Nationality) (string, error) {
	switch nationality {
	case common.Nationality_NATIONALITY_INDIAN:
		return "IND", nil
	default:
		return "", errors.Errorf("unsupported nationality type: %s", nationality.String())
	}
}

type as501ScreenCustomerRes struct{}

func (s *as501ScreenCustomerRes) Unmarshal(b []byte) (proto.Message, error) {
	logger.InfoNoCtx("AS501 response", zap.String("response", string(b)))
	as501Res := &tss.AS501Response{}
	err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, as501Res)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling decrypted AS501 response")
	}
	return s.convertAS501ResToVGRes(as501Res)
}

func (s *as501ScreenCustomerRes) convertAS501ResToVGRes(as501Response *tss.AS501Response) (*amlVgPb.InitiateScreeningResponse, error) {
	// {
	//    "requestId": "1235467",
	//    "validationCode": "MRV10",
	//    "validationDescription": "SourceSystemName is mandatory",
	//    "signature": "",
	//    "encryptionKey": "",
	//    "encryptedData": ""
	// }

	// {
	//    "OverallStatus": "AcceptedByTW",
	//    "CustomerResponse":
	//    [
	//        {
	//            "SourceSystemCustomerCode": "1235467",
	//            "ValidationOutcome": "Success",
	//            "PurposeResponse":
	//            [
	//                {
	//                    "Purpose": "Initial Screening with API Response and TW Workflow",
	//                    "PurposeCode": "03",
	//                    "Data":
	//                    {
	//                        "CaseUrl": null,
	//                        "CaseId": null,
	//                        "SuggestedAction": "Proceed",
	//                        "ProfileCode": "CUSSP1",
	//                        "HitsDetected": "No",
	//                        "HitsCount": 0,
	//                        "ConfirmedHits": "No",
	//                        "ReportData": null,
	//                        "HitResponse":
	//                        []
	//                    },
	//                    "ValidationCode": "",
	//                    "ValidationDescription": "",
	//                    "ValidationFailureCount": 0
	//                }
	//            ],
	//            "RelatedPersonResponse": null,
	//            "RelatedPersonRelationResponse": null
	//        }
	//    ]
	// }

	// {
	//    "OverallStatus": "AcceptedByTW",
	//    "CustomerResponse":
	//    [
	//        {
	//            "SourceSystemCustomerCode": "1235467",
	//            "ValidationOutcome": "Success",
	//            "PurposeResponse":
	//            [
	//                {
	//                    "Purpose": "Initial Screening with API Response and TW Workflow",
	//                    "PurposeCode": "03",
	//                    "Data":
	//                    {
	//                        "CaseUrl": "https://epifitechnologiespvtltd-sb.trackwizz.app/screeningcases/casedetails/10/?usertypeid=2",
	//                        "CaseId": "10",
	//                        "SuggestedAction": "Stop",
	//                        "ProfileCode": "CUSSP1",
	//                        "HitsDetected": "Yes",
	//                        "HitsCount": 1,
	//                        "ConfirmedHits": "Yes",
	//                        "HitResponse":
	//                        [
	//                            {
	//                                "Source": "UNSC Consolidated List",
	//                                "WatchlistSourceId": "QDi.135",
	//                                "MatchType": "Confirm Hit",
	//                                "Score": 100.0,
	//                                "ConfirmedMatchingAttributes": "Passport"
	//                            }
	//                        ]
	//                    },
	//                    "ValidationCode": "",
	//                    "ValidationDescription": "",
	//                    "ValidationFailureCount": 0
	//                }
	//            ],
	//            "RelatedPersonResponse": null,
	//            "RelatedPersonRelationResponse": null
	//        }
	//    ]
	// }

	if as501Response.GetOverallStatus() != "AcceptedByTW" {
		return &amlVgPb.InitiateScreeningResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("AS501 API error: %s", as501Response.GetValidationDescription())),
		}, nil
	}
	if len(as501Response.GetCustomerResponse()) == 0 {
		return &amlVgPb.InitiateScreeningResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("No customer response received from AS501 API"),
		}, nil
	}

	customerResponse := as501Response.GetCustomerResponse()[0]
	if customerResponse.GetValidationOutcome() != "Success" {
		return &amlVgPb.InitiateScreeningResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("Customer validation failed: %s", customerResponse.GetValidationDescription())),
		}, nil
	}

	// Process purpose responses
	// var matchStatus amlVgPb.MatchStatus
	// var alertCount uint64
	// var matchDetails []*aml.MatchDetails
	//
	// for _, purposeResponse := range customerResponse.GetPurposeResponse() {
	// 	if purposeResponse.GetPurposeCode() == AS501Purpose01 {
	// 		data := purposeResponse.GetData()
	// 		if data.GetHitsDetected() == "Yes" {
	// 			matchStatus = amlVgPb.MatchStatus_MATCH_STATUS_MATCHED
	// 			alertCount = uint64(data.GetHitsCount())
	//
	// 			// Parse report data if available
	// 			if data.GetReportData() != "" {
	// 				// TODO: Parse base64 report data to extract match details
	// 				// This would require additional parsing logic based on the report format
	// 			}
	// 		} else {
	// 			matchStatus = amlVgPb.MatchStatus_MATCH_STATUS_NOT_MATCHED
	// 		}
	// 		break
	// 	}
	// }

	// RejectionMessage: xmlRes.ScreeningResult.RejectionMessage,
	// 	RejectionCode:    convertToBERejectionCode(xmlRes.ScreeningResult.RejectionCode),
	// 		RecordIdentifier: xmlRes.ScreeningResult.RecordIdentifier,
	// 		MatchStatus:      ConvertToBEMatchStatus(xmlRes.ScreeningResult.Matched),
	// 		CaseId:           xmlRes.ScreeningResult.CaseId,
	// 		CaseLink:         xmlRes.ScreeningResult.Link,
	// 		AlertCount:       alertCount,
	// 		MatchDetails:     matchDetails,

	return &amlVgPb.InitiateScreeningResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *as501ScreenCustomerRes) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in AS501 screening API", zap.String("error", string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
