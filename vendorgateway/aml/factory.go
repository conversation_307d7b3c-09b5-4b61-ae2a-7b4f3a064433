package aml

import (
	"net/http"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/vendorgateway/config"

	"google.golang.org/protobuf/proto"
)

type Factory struct {
	conf *config.Aml
}

func NewAmlFactory(conf *config.Aml) *Factory {
	return &Factory{conf: conf}
}

func (f *Factory) getRequestFactoryMap(vendor commonvgpb.Vendor, req proto.Message) (map[commonvgpb.Vendor]vendorapi.SyncRequestFactory, error) {
	switch vendor {
	case commonvgpb.Vendor_TSS:
		tssReqFactory, err := f.NewTssRequestFactory(req)
		if err != nil {
			return nil, errors.Wrap(err, "error creating TSS request")
		}
		return map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
			commonvgpb.Vendor_TSS: tssReqFactory,
		}, nil
	default:
		return nil, errors.Errorf("unsupported vendor: %v", vendor)
	}
}

func (f *Factory) NewTssRequestFactory(m proto.Message) (vendorapi.SyncRequestFactory, error) {
	switch req := m.(type) {
	case *aml.ScreenCustomerRequest:
		return func(m proto.Message) vendorapi.SyncRequest {
			return &screenCustomerReq{
				method: http.MethodPost,
				req:    req,
				config: f.conf.Tss,
			}
		}, nil
	case *aml.InitiateScreeningRequest:
		tenantConf, err := getTSSCloudTenantConfig(f.conf.TSSCloud, req.GetOwner())
		if err != nil {
			return nil, errors.Wrap(err, "error getting TSS cloud tenant config")
		}
		cryptor, err := NewCryptor(f.conf.TSSCloud.Common, tenantConf, req.GetVendorRequestId())
		if err != nil {
			return nil, errors.Wrap(err, "error getting cryptor for AS501 screening")
		}
		return func(m proto.Message) vendorapi.SyncRequest {
			return &as501ScreenCustomerReq{
				req:            req,
				tenantConf:     tenantConf,
				SecureExchange: NewSecureExchange(cryptor),
			}
		}, nil
	default:
		return nil, errors.Errorf("unsupported request type: %T", req)
	}
}

func getTSSCloudTenantConfig(conf *config.TSSCloud, owner common.Owner) (*config.TSSCloudTenant, error) {
	switch owner {
	case common.Owner_OWNER_EPIFI_TECH:
		return conf.Tenants["EpifiTech"], nil
	case common.Owner_OWNER_STOCK_GUARDIAN_TSP:
		return conf.Tenants["LMS"], nil
	default:
		return nil, errors.Errorf("unexpected owner: %s", owner.String())
	}
}
