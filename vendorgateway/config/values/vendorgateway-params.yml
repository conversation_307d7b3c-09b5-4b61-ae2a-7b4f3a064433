Application:
  Cibil:
    DisableDefaultValuesInFulfillOffer: false

  Nps:
    ApiHost: "https://npscra.nsdl.co.in/download"
  EnachTransactionStatusCodeJson: "./mappingJson/enachTransactionStatusCodes.json"

  # UPI config
  PspHandleToUpiOrgIdMap:
    default: "159049"
    fede: "400021"
    fbl: "159049"
    fedepsp: "159049"
    fifederal: "180031"
  SenseforthEventURL: "https://chatbotapi.uat.pointz.in/getmessages"

  #Ozonetel service
  OzonetelManualDialUrl: "https://in1-ccaas-api.ozonetel.com/CAServices/AgentManualDial.php"
  OzonetelUserName: "epifi_ccaas"
  OzonetelCampaignName: "Progressive_918047485490"

  PanProfileURL: "https://testapi.karza.in/v3/pan-profile"

  SetU:
    AuthTokenUrl: "api/v2/auth/token"
    GetCategoriesUrl: "api/v2/bbps/categories"
    GetBillersUrl: "api/v2/bbps/billers"
    GetHealthUrl: "api/v2/health"
    GetDisputesUrl: "api/v2/bbps/disputes"
    GetTxnsUrl: "api/v2/bbps/transactions"
    RaiseDisputeUrl: "api/v2/bbps/bills/complaint/request"
    CheckDisputeUrl: "api/v2/bbps/bills/complaint/response"
    FetchBillUrl: "api/v2/bbps/bills/fetch/request"
    FetchedBillUrl: "api/v2/bbps/bills/fetch/response"
    PaymentRequestUrl: "api/v2/bbps/bills/payment/request"
    PaymentResponseUrl: "api/v2/bbps/bills/payment/response"
    MobileRechargeFetchAccessTokenUrl: "v1/users/login"
    MobileRechargeFetchOperatorUrl: "fetch-operator-details"
    MobileRechargeFetchPlansUrl: "fetch-plans"
    MobileRechargeInitiateRechargeUrl: "recharge/request"
    MobileRechargeEnquireStatusUrl: "recharge/status"
    MobileRechargeGetTransactionsUrl: "transaction"
    MobileRechargeGetWalletBalanceUrl: "wallet/balance"

  Seon:
    GetUserSocialMediaInformationUrl: "https://api.seon.io/SeonRestService/email-api/v2.1"

  Karvy:
    XMLTagReplaceFlag: true # This is for a bug at karvy side where xml tags are getting replaced by random symbols, this can be set to false once karvy fixes the bug.

  SmallCase:
    EnableNotFoundInInitHoldingsImport: true

  Karza:
    UpdateCustomerV3UATUrl: "https://app.karza.in/test/videokyc/api/v3/customers"
    AddNewCustomerV3UATUrl: "https://app.karza.in/test/videokyc/api/v3/customers"
    GenerateCustomerTokenUATUrl: "https://app.karza.in/test/videokyc/api/v2/generate-usertoken"
    TransactionStatusEnquiryUATUrl: "https://app.karza.in/test/videokyc/api/v2/transaction-events"
    GenerateWebLinkUATUrl: "https://app.karza.in/test/videokyc/api/v2/link"
    GenerateSessionTokenUATUrl: "https://testapi.karza.in/v3/get-jwt"

  # Employment Service
  Employment:
    TartanUpdateEmployeeBankDetailsURL: "https://tnode.tartanhq.com/api/update_bank/"
    TartanGetHrmsDetailsURL: "https://tnode.tartanhq.com/api/hrms_list/"
    TartanGetEmployerDetailsURL: "https://tnode.tartanhq.com/api/org_details/"

  PAYUAffluenceURL: "https://mars-dev.payu.in/api/v3/daas/"

  AA:
    GetBulkConsentRequestURL: "/Consent/status/bulk"
    GenerateFinvuJwtTokenURL: "/web/token"
    ConsentArtefactV2URL: "/Consent/fetch"
    AaSecretsVersionToUse: "V1"
    FinvuFipMetricsURL: "/fip/latest-metrics-all"
    IsOnemoneyV2Enabled: false
    IsFinvuV2Enabled: false

  Tiering:
    SchemeChangeURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/schemeChangeAddEnq"

  Alpaca:
    StreamApiHost: "stream.data.sandbox.alpaca.markets"
    StreamApiPath: "v2/iex"
    StreamApiScheme: "wss"
    ShouldUseSimulatedEnvForWs: false
    FundingConfig:
      FundTransferMode: "wire"

  Credgenics:
    BaseUrl: "https://apiprod.credgenics.com/recovery"

  Federal:
    CheckDebitCardIssuanceFeeStatusUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcation"
    DebitCardCollectIssuanceFeeUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcation"

  Scienaptic:
    WhitelistedFeatures:
      totIncEver: true
      totIncL1M: true
      totIncL3M: true
      totIncL6M: true
      totIncL12M: true
      Income_current: true
      Income1M: true
      Income2M: true
      Income3M: true
      Income4M: true
      Income5M: true
      Income6M: true
      Income7M: true
      Income8M: true
      Income9M: true
      Income10M: true
      Income11M: true
      Income12M: true
      totSpendEver: true
      totSpendL1M: true
      totSpendL3M: true
      totSpendL6M: true
      totSpendL12M: true
      Spend_current: true
      Spend1M: true
      Spend2M: true
      Spend3M: true
      Spend4M: true
      Spend5M: true
      Spend6M: true
      Spend7M: true
      Spend8M: true
      Spend9M: true
      Spend10M: true
      Spend11M: true
      Spend12M: true
      totCashwithdrawlEver: true
      totCashwithdrawl_current: true
      totCashwithdrawlL1M: true
      totCashwithdrawlL3M: true
      totCashwithdrawlL6M: true
      totCashWithdrawlL12M: true
      totTimesCashwithdrawlever: true
      totTimesCashwithdrawl_current: true
      totTimesCashwithdrawlL1M: true
      totTimesCashwithdrawlL3M: true
      totTimesCashwithdrawlL6M: true
      totTimesCashwithdrawlL12M: true
      maxCashwithdrawlEver: true
      maxCashwithdrawl_current: true
      maxCashwithdrawlL1M: true
      maxCashwithdrawlL3M: true
      maxCashwithdrawlL6M: true
      maxCashwithdrawlL12M: true
      totOnlineTxnEver: true
      totOnlineTxn_current: true
      totOnlineTxnL1M: true
      totOnlineTxnL3M: true
      totOnlineTxnL6M: true
      totOnlineTxnL12M: true
      totTimesonlineTxnEver: true
      totTimesonlineTxn_current: true
      totTimesonlineTxnL1M: true
      TottimesonlineTxnL3M: true
      TottimesonlineTxnL6M: true
      totTimesonlineTxnL12M: true
      MaxonlineTxnEver: true
      MaxonlineTxn_current: true
      MaxonlineTxnL1M: true
      maxOnlinetxnL3M: true
      maxOnlinetxnL6M: true
      maxOnlinetxnL12M: true
      totOnlinedebitEver: true
      totOnlinedebit_current: true
      totOnlinedebitL1M: true
      totonlinedebitL3M: true
      totOnlinedebitL6M: true
      totOnlinedebitL12M: true
      totTimesonlinedebitever: true
      totTimesonlinedebit_current: true
      TotTimesonlinedebitL1M: true
      totTimesonlinedebitL3M: true
      TotTimesonlinedebitL6M: true
      totTimesonlinedebitL12M: true
      maxOnlinedebitever: true
      maxOnlinedebit_current: true
      maxOnlinedebitL1M: true
      maxOnlinedebitL3M: true
      maxOnlinedebitL6M: true
      maxOnlinedebitL12M: true
      totOnlinecreditEver: true
      totOnlinecredit_current: true
      totOnlinecreditL1M: true
      totOnlinecreditL3M: true
      totOnlinecreditL6M: true
      totonlinecreditL12M: true
      totTimesonlinecreditever: true
      totTimesonlinecredit_current: true
      totTimesonlinecreditL1M: true
      totTimesonlinecreditL3M: true
      totTimesonlinecreditL6M: true
      totTimesonlinecreditL12M: true
      maxOnlinecreditever: true
      maxOnlinecredit_current: true
      maxOnlinecreditL1M: true
      maxOnlinecreditL3M: true
      maxOnlinecreditL6M: true
      maxOnlinecreditL12M: true
      totCardTxnEver: true
      totCardTxn_current: true
      totCardTxnL1M: true
      totCardTxnL3M: true
      totCardTxnL6M: true
      totCardTxnL12M: true
      totTimesCardTxnEver: true
      totTimesCardTxn_current: true
      totTimesCardTxnL1M: true
      totTimesCardTxnL3M: true
      totTimesCardTxnL6M: true
      totTimescardtxnL12M: true
      maxCardTxnEver: true
      maxCardTxn_current: true
      maxCardTxnL1M: true
      maxCardTxnL3M: true
      maxCardTxnL6M: true
      maxCardTxnL12M: true
      totCardDebitEver: true
      totCardDebit_current: true
      totCardDebitL1M: true
      totCardDebitL3M: true
      totCardDebitL6M: true
      totCardDebitL12M: true
      totTimescardDebitEver: true
      totTimescardDebit_current: true
      totTimesCardDebitL1M: true
      totTimescardDebitL3M: true
      TotTimesCardDebitL6M: true
      totTimescarddebitL12M: true
      maxCardDebitEver: true
      maxCardDebit_current: true
      maxCardDebitL1M: true
      maxCardDebitL3M: true
      maxCardDebitL6M: true
      maxCardDebitL12M: true
      totCardCreditEver: true
      totCardCredit_current: true
      totCardCreditL1M: true
      totCardCreditL3M: true
      totCardCreditL6M: true
      totCardCreditL12M: true
      totTimescardcreditever: true
      totTimescardcredit_current: true
      totTimesCardCreditL1M: true
      totTimescardcreditL3M: true
      totTimesCardCreditL6M: true
      totTimescardcreditL12M: true
      maxCardCreditEver: true
      maxCardCredit_current: true
      maxCardCreditL1M: true
      maxCardCreditL3M: true
      maxCardCreditL6M: true
      maxCardCreditL12M: true
      avgSalary: true
      salary_current: true
      salary1M: true
      salary2M: true
      salary3M: true
      salary4M: true
      salary5M: true
      salary6M: true
      salary7M: true
      salary8M: true
      salary9M: true
      salary10M: true
      salary11M: true
      salary12M: true
      maxSpend: true
      totWalletdebitEver: true
      totWalletdebit_current: true
      totWalletdebitL1M: true
      totWalletdebitL3M: true
      totWalletdebitL6M: true
      totWalletdebitL12M: true
      totTimeswalletdebitever: true
      totTimeswalletdebit_current: true
      totTimeswalletdebitL1M: true
      totTimeswalletdebitL3M: true
      totTimeswalletdebitL6M: true
      totTimeswalletdebitL12M: true
      maxWalletdebitever: true
      maxWalletdebit_current: true
      maxWalletdebitL1M: true
      maxWalletdebitL3M: true
      maxWalletdebitL6M: true
      maxWalletdebitL12M: true
      totWalletCreditEver: true
      totWalletCredit_current: true
      totWalletcreditL1M: true
      totWalletcreditL3M: true
      totWalletcreditL6M: true
      totWalletcreditL12M: true
      totTimeswalletcreditEver: true
      totTimeswalletcredit_current: true
      totTimeswalletcreditL1M: true
      totTimeswalletcreditL3M: true
      totTimeswalletcreditL6M: true
      totTimeswalletcreditL12M: true
      maxWalletcreditever: true
      maxWalletcredit_current: true
      maxwalletcreditL1M: true
      maxWalletcreditL3M: true
      maxWalletcreditL6M: true
      maxWalletcreditL12M: true
      countSMSEMI: true
      activeEMIFlag: true
      totInvestmentDebitEver: true
      totInvestmentDebit_current: true
      totInvestmentdebitL1M: true
      totInvestmentdebitL3M: true
      totInvestmentdebitL6M: true
      totInvestmentdebitL12M: true
      totTimesinvestmentdebitever: true
      totTimesinvestmentdebit_current: true
      totTimesinvestmentdebitL1M: true
      totTimesinvestmentdebitL3M: true
      totTimesinvestmentdebitL6M: true
      totTimesinvestmentdebitL12M: true
      maxInvestmentdebitever: true
      maxInvestmentdebit_current: true
      maxInvestmentdebitL1M: true
      maxInvestmentdebitL3M: true
      maxInvestmentdebitL6M: true
      maxInvestmentdebitL12M: true
      totInvestmentcreditEver: true
      totInvestmentcredit_current: true
      totInvestmentcreditL1M: true
      totInvestmentcreditL3M: true
      totInvestmentcreditL6M: true
      totInvestmentcreditL12M: true
      totTimesinvestmentcreditever: true
      totTimesinvestmentcredit_current: true
      TotTimesinvestmentcreditL1M: true
      totTimesinvestmentcreditL3M: true
      TotTimesinvestmentcreditL6M: true
      totTimesinvestmentcreditL12M: true
      MaxInvestmentcreditever: true
      MaxInvestmentcredit_current: true
      maxInvestmentcreditL1M: true
      MaxInvestmentcreditL3M: true
      maxInvestmentcreditL6M: true
      MaxInvestmentcreditL12M: true
      timesAccountsBlocked: true
      timesAccountsBlocked_current: true
      timesAccountsBlockedL1M: true
      timesAccountsBlockedL3M: true
      timesAccountsBlockedL6M: true
      timesAccountsBlockedL12M: true
      timesInsuffBalSavAcct: true
      timesInsuffBalSavAcct_current: true
      timesInsuffBalSavAcctL1M: true
      timesInsuffBalSavAcctL3M: true
      timesInsuffBalSavAcctL6M: true
      timesInsuffBalSavAcctL12M: true
      EPFO_current: true
      EPFO_1M: true
      EPFO_2M: true
      EPFO_3M: true
      EPFO_4M: true
      EPFO_5M: true
      EPFO_6M: true
      timesOverdueLoanPmnt: true
      timesOverdueLoanPmnt_current: true
      timesOverdueLoanPmntL1M: true
      timesOverdueLoanPmntL3M: true
      timesOverdueLoanPmntL6M: true
      timesOverdueLoanPmntL12M: true
      timesCCDPmntMiss: true
      timesCCDPmntMiss_current: true
      timesCCDPmntMissL1M: true
      timesCCDPmntMissL3M: true
      timesCCDPmntMissL6M: true
      timesCCDPmntMissL12M: true
      timesChqDishonoured: true
      timesChqDishonoured_current: true
      timesChqDishonouredL1M: true
      timesChqDishonouredL3M: true
      timesChqDishonouredL6M: true
      timesChqDishonouredL12M: true
      maxCCDutilizationwarning: true
      maxCCDutilizationwarning_current: true
      maxCCDutilizationwarningL1M: true
      maxCCDutilizationwarningL3M: true
      maxCCDutilizationwarningL6M: true
      maxCCDutilizationwarningL12M: true
      numberOfTradelines: true
      numberOfCCDTradelines: true
      spendIncomeRatio_current: true
      spendIincomeRatioL1M: true
      spendIncomeRatioL3M: true
      spendIncomeRatioL6M: true
      spendIncomeRatioL12M: true
      cashWithdrawRatio_current: true
      cashWithdrawRatioL1M: true
      cashWithdrawRatioL3M: true
      cashWithdrawRatioL6M: true
      cashWithdrawRatioL12M: true
      avg_Inc_current: true
      avg_Inc_L1M: true
      avg_Inc_L3M: true
      avg_Inc_L6M: true
      avg_Inc_L12M: true
      avg_spend_current: true
      avg_spend_L1M: true
      avg_spend_L3M: true
      avg_spend_L6M: true
      avg_spend_L12M: true
      total_bank_accounts: true
      avg_balance_current: true
      avg_balance_1M: true
      avg_balance_2M: true
      avg_balance_3M: true
      avg_balance_4M: true
      avg_balance_5M: true
      avg_balance_6M: true
      avg_balance_7M: true
      avg_balance_8M: true
      avg_balance_9M: true
      avg_balance_10M: true
      avg_balance_11M: true
      avg_balance_12M: true
      tot_EMI_current: true
      tot_EMI_1M: true
      tot_EMI_2M: true
      tot_EMI_3M: true
      tot_EMI_4M: true
      tot_EMI_5M: true
      tot_EMI_6M: true
      tot_EMI_7M: true
      tot_EMI_8M: true
      tot_EMI_9M: true
      tot_EMI_10M: true
      tot_EMI_11M: true
      tot_EMI_12M: true
      Nach_ECS_Bounced_current: true
      Nach_ECS_Bounced_1M: true
      Nach_ECS_Bounced_2M: true
      Nach_ECS_Bounced_3M: true
      Nach_ECS_Bounced_4M: true
      Nach_ECS_Bounced_5M: true
      Nach_ECS_Bounced_6M: true
      Nach_ECS_Bounced_7M: true
      Nach_ECS_Bounced_8M: true
      Nach_ECS_Bounced_9M: true
      Nach_ECS_Bounced_10M: true
      Nach_ECS_Bounced_11M: true
      Nach_ECS_Bounced_12M: true
      avg_balance_L1M: true
      avg_balance_L3M: true
      avg_balance_L6M: true
      avg_balance_L12M: true
      count_balance_is_10000_current: true
      count_balance_is_10000_L1M: true
      count_balance_is_10000_L3M: true
      count_balance_is_10000_L6M: true
      count_balance_is_10000_L12M: true
      tot_EMI_L1M: true
      tot_EMI_L3M: true
      tot_EMI_L6M: true
      tot_EMI_L12M: true
      max_EMI_current: true
      max_EMI_L1M: true
      max_EMI_L3M: true
      max_EMI_L6M: true
      max_EMI_L12M: true
      Salary_flag_final: true
      salary1M_date: true
      salary2M_date: true
      salary3M_date: true
      salary4M_date: true
      salary5M_date: true
      salary6M_date: true
      salary7M_date: true
      salary8M_date: true
      salary9M_date: true
      salary10M_date: true
      salary11M_date: true
      salary12M_date: true
      salary_current_date: true
      bank_account_details: true
      # below metadata features are to be removed post cug testing
      salary_current_metadata: true
      salary1M_metadata: true
      salary2M_metadata: true
      salary3M_metadata: true
      salary4M_metadata: true
      salary5M_metadata: true
      salary6M_metadata: true
      salary7M_metadata: true
      salary8M_metadata: true
      salary9M_metadata: true
      salary10M_metadata: true
      salary11M_metadata: true
      salary12M_metadata: true
      EPFO_current_metadata: true
      EPFO_1M_metadata: true
      EPFO_2M_metadata: true
      EPFO_3M_metadata: true
      EPFO_4M_metadata: true
      EPFO_5M_metadata: true
      EPFO_6M_metadata: true
      tot_EMI_current_metadata: true
      tot_EMI_1M_metadata: true
      tot_EMI_2M_metadata: true
      tot_EMI_3M_metadata: true
      tot_EMI_4M_metadata: true
      tot_EMI_5M_metadata: true
      tot_EMI_6M_metadata: true
      tot_EMI_7M_metadata: true
      tot_EMI_8M_metadata: true
      tot_EMI_9M_metadata: true
      tot_EMI_10M_metadata: true
      tot_EMI_11M_metadata: true
      tot_EMI_12M_metadata: true
      Nach_ECS_Bounced_current_metadata: true
      Nach_ECS_Bounced_1M_metadata: true
      Nach_ECS_Bounced_2M_metadata: true
      Nach_ECS_Bounced_3M_metadata: true
      Nach_ECS_Bounced_4M_metadata: true
      Nach_ECS_Bounced_5M_metadata: true
      Nach_ECS_Bounced_6M_metadata: true
      Nach_ECS_Bounced_7M_metadata: true
      Nach_ECS_Bounced_8M_metadata: true
      Nach_ECS_Bounced_9M_metadata: true
      Nach_ECS_Bounced_10M_metadata: true
      Nach_ECS_Bounced_11M_metadata: true
      Nach_ECS_Bounced_12M_metadata: true

  InhouseOCR:
    ConfidenceThreshold: 0.5

  Lending:
    PreApprovedLoan:
      Lentra:
        Url: "https://ssguat.serviceurl.in/"
      Finflux:
        BaseUrl: "https://epifi.lms-uat.pointz.in"
        Auth:
          Username: "post"
          Password: "Test@123"
          IsPasswordEncrypted: false
          TokenValidityDuration: "14m"
        Charges:
          ProcessingFeeChargeId: 1
         #  This is a map against Vendor Id and Value is the stringified version of api.vendorgateway.lending.lms.finflux.types.LoanStatus
        LoanStatusVendorIdToEnumValue:
          100: "LOAN_STATUS_SUBMITTED_AND_AWAITING_APPROVAL"
          200: "LOAN_STATUS_APPROVED"
          300: "LOAN_STATUS_ACTIVE"
          303: "LOAN_STATUS_TRANSFER_IN_PROGRESS"
          304: "LOAN_STATUS_TRANSFER_ON_HOLD"
          400: "LOAN_STATUS_WITHDRAWN_BY_CLIENT"
          500: "LOAN_STATUS_REJECTED"
          600: "LOAN_STATUS_CLOSED"
          601: "LOAN_STATUS_WRITTEN_OFF"
          602: "LOAN_STATUS_RESCHEDULED"
          700: "LOAN_STATUS_OVERPAID"

  Poshvine:
    BaseUrl: "https://sandbox-api.poshvine.com"
    SSOUrl: "/cs/v1/sso/token_login"
    UpdateUrl: "/cs/external/users/update"
    FiClientId: "9d1207af-b3c1-4096-8cea-8858acf5e27b"
    ExpiresAt: "24h"
    RedirectionUrl: "https://sandbox-fi-money.poshvine.com/sso_login"

  NetCoreEpifi:
    URL: "https://bulkpush.mytoday.com/BulkSms/SingleMsgApi"
    FeedId: "393957"

  AirtelFedSMS:
    URL: "https://iqsms.airtel.in/api/v2/send-sms-cm"
    SenderId: "FedFiB"
    CustomerId: "EPIFI_TECH_Toj5Cm6v3x9qMYBCy3RR"
    RedirectSMSDlrConfigMap:
      prod:
        DlrUrl: "https://vnotificationgw.epifi.in/sms/callback/airtel/RealTimeDLR/requestListener"
      qa:
        DlrUrl: "https://vnotificationgw.qa.pointz.in/sms/callback/airtel/RealTimeDLR/requestListener"
      staging:
        DlrUrl: "https://vnotificationgw.staging.pointz.in/sms/callback/airtel/RealTimeDLR/requestListener"
      uat:
        DlrUrl: "https://vnotificationgw.uat.pointz.in/sms/callback/airtel/RealTimeDLR/requestListener"
  FederalEscalation:
    BaseURL: "https://uatgateway.federalbank.co.in"
    CreateEscalationURL: "fedbnk/uat/CRM/v1.0.0/cxiosrcreation"
    BulkFetchURL: "fedbnk/uat/CRM/v1.0.0/cxsrbulkretrieval"

  AirtelEpifiSMS:
    URL: "https://iqsms.airtel.in/api/v2/send-sms-cm"
    SenderId: "FiMony"
    CustomerId: "EPIFI_TECH_Toj5Cm6v3x9qMYBCy3RR"

Flags:
  TrimDebugMessageFromStatus: true
  UseAsyncNotificationCallback: true
  UseCustomTrustedCertPool: true
  EnableFederalCardDecryptionByFallbackKey: true
  UseNewSolID: false
  UseNewOccupationInCifCreation: true
  UseNewFieldsInCifCreation: false
  UseNewFieldsInAccountCreation: false
  EnableTransactionEnquiryNewApi: true
  EnableUATForVKYC: true
  EnableFennelClusterV3: true
  EnableInstrumentBillingInterceptor: true
  EnableCibilV2Secrets: true

# HTTP client config inspired from DefaultTransport of http package
# https://golang.org/src/net/http/transport.go?h=DefaultTransport#L42
HttpClientConfig:
  Transport:
    DialContext:
      Timeout: 30s
      KeepAlive: 30s
    TLSHandshakeTimeout: 10s
    MaxIdleConns: 100
    IdleConnTimeout: 90s
    MaxConnsPerHost: 500
    MaxIdleConnsPerHost: 50

# Need this special config for EPFGetPassbook api which can take upto 4m to respond
KarzaEPFPassbookHttpClientConfig:
  Transport:
    DialContext:
      Timeout: 4m
      KeepAlive: 4m
    TLSHandshakeTimeout: 10s
    MaxIdleConns: 100
    IdleConnTimeout: 90s
    MaxConnsPerHost: 500
    MaxIdleConnsPerHost: 50

# Setting the timeout for all the dispute APIs post confirmation from federal where
# createDispute API in DMP system times out if the default time of 30 seconds is kept
# refer mail thread: DMP API Failure Scenarios
# increasing the timeout to 2minutes to check for timeouts
DisputeHTTPClientConfig:
  Transport:
    DialContext:
      Timeout: 120s
      KeepAlive: 120s
    TLSHandshakeTimeout: 10s
    MaxIdleConns: 100
    IdleConnTimeout: 90s
    MaxConnsPerHost: 500
    MaxIdleConnsPerHost: 50


Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/vendorgateway/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

HystrixConfig:
  DefaultServiceTemplate: "Q0"
  Commands:
    FEDERAL_BANK:
      - CommandName: "openbanking_upi_upi_check_txn_status"
        TemplateName: "Q0"
      - CommandName: "openbanking_accounts_accounts_get_account_statement"
        TemplateName: "Q0"
      - CommandName: "openbanking_savings_savings_get_opening_balance"
        TemplateName: "Q0"
      - CommandName: "openbanking_savings_savings_get_balance"
        TemplateName: "Q0"
      - CommandName: "openbanking_upi_upi_validate_address"
        TemplateName: "Q0"
      - CommandName: "namecheck_namecheck_un_name_check"
        TemplateName: "Q0"
      - CommandName: "openbanking_payment_payment_pay_deposit_add_funds"
        TemplateName: "Q0"
      - CommandName: "pan_pan_validate"
        TemplateName: "Q0"
        OverrideTemplateConfig:
          Disabled: true
      - CommandName: "openbanking_customer_customer_dedupe_check"
        TemplateName: "Q0"
        OverrideTemplateConfig:
          ForcedClosed: true
      - CommandName: "ekyc_ekyc_name_dob_validation_for_ekyc"
        TemplateName: "Q0"
    FRESHDESK:
      - CommandName: "cx_freshdesk_freshdesk_get_ticket_by_ticket_id"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_get_all_tickets"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_update_ticket"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_get_contacts"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_create_ticket"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_get_agent"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_add_private_note_in_ticket"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_fetch_ticket_conversations"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_update_ticket_raw"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_get_ticket_field"
        TemplateName: "Q0"

TimeoutConfig:
  DefaultTimeout: 30s
  Vendors:
    FEDERAL_BANK:
      # Setting the timeout for all the dispute APIs post confirmation from federal where
      # createDispute API in DMP system times out if the default time of 30 seconds is kept
      # refer mail thread: DMP API Failure Scenarios
      vendorgateway_openbanking_dispute_dispute_createdispute:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_getdisputestatus:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_senddocument:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_channelquestionnaire:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_accounttransactions:
        Timeout: 120s
    FIFTYFIN:
      vendorgateway_lending_securedloans_fiftyfin_fiftyfin_getloansoastatement:
        Timeout: 120s
      vendorgateway_lending_securedloans_fiftyfin_fiftyfin_getloanforeclosurestatement:
        Timeout: 120s

XMLDigitalSignatureSigner:
  KeyParams:
    EpifiFederalUPIPrivateKey:
      ValidFrom: "02 Dec 22 10:00 +0530"
      ValidTill: "02 Dec 23 10:00 +0530"

Tracing:
  Enable: false

Freshdesk:
  GroupEnumToGroupIdMapping:
    CALLBACK: ***********
    EPIFI_ESCALATION: ***********
    ESCALATED_CASES_CLOSURE: ***********
    FEDERAL_ESCALATIONS: ***********
    L1_SUPPORT: ***********
    L2_SUPPORT: ***********
    NON_SFTP_ESCALATIONS: ***********
    SFTP_ESCALATIONS: ***********
    SFTP_PENDING_GROUP: ***********
    FEDERAL_UPDATES: ***********
    L1_SUPPORT_WAITLIST: ***********
    GROUP_RISK_OPS: ***********
    GROUP_ACCOUNT_CLOSURE_RISK_BLOCK: ***********
    GROUP_L1_SUPPORT_CALL: ***********
    GROUP_L1_SUPPORT_CHAT: ***********
    GROUP_L1_SUPPORT_EMAIL: ***********
    GROUP_L1_SUPPORT_SOCIAL_MEDIA: ***********
    GROUP_OUTBOUND_CALL_BACK: ***********
    GROUP_LOAN_OUTBOUND_CALL: ***********
  ProductCategoryEnumToValueMapping:
    TRANSACTION: "Transactions"
    ACCOUNTS: "Accounts"
    ONBOARDING: "Onboarding"
    SAVE: "Save"
    WAITLIST: "Waitlist"
    RE_ONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_REWARDS: "Rewards"
    PRODUCT_CATEGORY_FIT: "FIT"
    PRODUCT_CATEGORY_DEBIT_CARD: "Debit Card"
    PRODUCT_CATEGORY_REFERRALS: "Referrals"
    PRODUCT_CATEGORY_CONNECTED_ACCOUNTS: "Connected Accounts"
    PRODUCT_CATEGORY_FRAUD_AND_RISK: "Fraud & Risk"
    PRODUCT_CATEGORY_JUMP_P2P: "Jump P2P"
    PRODUCT_CATEGORY_PROFILE: "Profile"
    PRODUCT_CATEGORY_SALARY_ACCOUNT: "Salary account"
    PRODUCT_CATEGORY_SEARCH: "Search"
    PRODUCT_CATEGORY_WEALTH_ONBOARDING: "Wealth Onboarding"
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS: "Wealth Mutual Funds"
    PRODUCT_CATEGORY_APP_CRASH: "App Crash"
    PRODUCT_CATEGORY_DATA_DELETION: "Data deletion"
    PRODUCT_CATEGORY_SCREENER: "Screener"
    PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED: "Google Token expired"
    PRODUCT_CATEGORY_LANGUAGE_CALLBACK: "Language callback"
    PRODUCT_CATEGORY_CATEGORY_NOT_FOUND: "Category not found"
    PRODUCT_CATEGORY_KYC_OUTCALL: "KYC Outcall"
    PRODUCT_CATEGORY_TRANSACTION_ISSUES: "Transaction Issues"
    PRODUCT_CATEGORY_REWARDS_NEW: "Rewards New"
    PRODUCT_CATEGORY_REFERRALS_NEW: "Referrals New"
    PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI: "General Enquiries about Fi"
    PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT: "No response/ Blank chat"
    PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED: "Call drop/ disconnected"
    PRODUCT_CATEGORY_INSTANT_LOANS: "Instant Loans"
    PRODUCT_CATEGORY_TIERING_PLANS: "Tiering plans"
    PRODUCT_CATEGORY_CREDIT_CARD: "Credit Card"
    PRODUCT_CATEGORY_SAVE: "Save"
    PRODUCT_CATEGORY_US_STOCKS: "US stocks"
    PRODUCT_CATEGORY_DEVICE: "Device"
    PRODUCT_CATEGORY_RISK: "Risk"
    PRODUCT_CATEGORY_ON_APP_TRANSACTIONS: "In-App Transactions"
    PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS: "Off-App Transactions"
    PRODUCT_CATEGORY_INSTANT_SALARY: "Instant Salary"
    PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD: "SimpliFi Credit Card"
    PRODUCT_CATEGORY_LAMF: "LAMF"
    PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD: "MagniFi Credit Card"
    PRODUCT_CATEGORY_SALARY_LITE: "Salary Lite"
    PRODUCT_CATEGORY_FI_STORE: "Fi-Store"
    PRODUCT_CATEGORY_GENERAL_ENQUIRY: "General Enquiry"
    PRODUCT_CATEGORY_APP_RELATED: "App Related"
    PRODUCT_CATEGORY_DEPOSITS_AND_INVESTMENTS: "Deposits & Investments"
    PRODUCT_CATEGORY_INCOMPLETE_CONVERSATION: "Incomplete Conversation"
    PRODUCT_CATEGORY_LOANS: "Loans"
    PRODUCT_CATEGORY_NET_WORTH: "Net Worth"
    PRODUCT_CATEGORY_SERVICE_REQUESTS: "Service Requests"
  TransactionTypeEnumToValueMapping:
    DEBIT_CARD: "Debit Card"
    IMPS: "IMPS"
    NEFT: "NEFT"
    RTGS: "RTGS"
    UPI: "UPI"
    INTRA_BANK: "Intra Bank"
    TRANSACTION_TYPE_UNKNOWN: "Unknown"
  DisputeStatusEnumToValueMapping:
    ACCEPTED: "Accepted"
    REJECTED: "Rejected"
  StatusEnumToValueMapping:
    STATUS_UNSPECIFIED: 0
    OPEN: 2
    PENDING: 3
    RESOLVED: 4
    CLOSED: 5
    WAITING_ON_THIRD_PARTY: 7
    ESCALATED_TO_L2: 8
    ESCALATED_TO_FEDERAL: 9
    SEND_TO_PRODUCT: 10
    WAITING_ON_PRODUCT: 11
    REOPEN: 12
    NEEDS_CLARIFICATION_FROM_CX: 13
    WAITING_ON_CUSTOMER: 6
  ProductCategoryDetailsEnumToValueMapping:
    MANUAL_WHITELISTING: "Manual Whitelisting"
    APP_DOWNLOAD_ISSUE: "App download issue"
    DEVICE_CHECK_FAILURE: "Device check failure"
    PHONE_NUMBER_OTP: "OTP"
    EMAIL_SELECTION_FAILURE: "Email selection failure"
    MOTHER_FATHER_NAME: ""
    PAN_NAME_VALIDATION_FAILURE: "PAN Name validation failure"
    EXISTING_FEDERAL_ACCOUNT: "Existing Federal account"
    KYC: "Manual KYC"
    LIVENESS: "Liveness"
    FACEMATCH_FAIL: "Face-match fail"
    UN_NAME_CHECK: "UN Name blacklist"
    CONFIRM_CARD_MAILING_ADDRESS: "Confirm Card Mailing address"
    UPI_CONSENT_FAILURE: "UPI Consent failure"
    DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    CUSTOMER_CREATION_FAILURE: "Customer creation failure"
    ACCOUNT_OPENING_DELAYED: "Account opening delayed"
    CARD_CREATION_FAILURE: "Card creation failure"
    CARD_PIN_SET_FAILURE: ""
    UPI_SETUP_FAILURE: "UPI setup failure"
    VKYC: "VKYC"
    REONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN: "PIN"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION: "Activation"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY: "Delivery"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP: "Debited via Fi app but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP: "Debited from FI account (via Other App) but"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY: "Min KYC expiry"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM: "Cards - ATM"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT: "UPI - Unable to transact"
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL: "Investment Transaction Successful"
    PRODUCT_CATEGORY_DETAILS_SAVE_FIXED_DEPOSIT: "Fixed Deposit"
    PRODUCT_CATEGORY_DETAILS_SAVE_SMART_DEPOSIT: "Smart Deposit"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CLOSURE_REQUEST: "Account Closure Request"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_OPENING_ISSUES: "Account Opening Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UPGRADE_DOWNGRADE: "Account Upgrade/Downgrade"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_BALANCE_TRANSFER: "Balance Transfer"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CATEGORY_NOT_FOUND: "Category Not Found"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CHEQUEBOOK_RELATED: "Chequebook Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_DORMANT: "Dormant"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_FEES_AND_CHARGES: "Fees & Charges"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_KYC_RELATED: "KYC Related"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_LIEN_ON_ACCOUNT: "Lien On Account"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_RE_LOGIN_ISSUES: "Re Login Issues"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_SALARY_PROGRAM: "Salary Program"
    PRODUCT_CATEGORY_DETAILS_APP_RELATED_ISSUES: "App Related Issues"
    PRODUCT_CATEGORY_DETAILS_APP_BANK_INCOMING: "Bank Incoming"
    PRODUCT_CATEGORY_DETAILS_APP_CX_INCOMING: "CX Incoming"
    PRODUCT_CATEGORY_DETAILS_APP_BLOCK_PERMANENTLY: "Block Permanently"
    PRODUCT_CATEGORY_DETAILS_CARD_REQUEST: "Card Request"
    PRODUCT_CATEGORY_DETAILS_CARD_SETTINGS: "Card Settings"
    PRODUCT_CATEGORY_DETAILS_FIT_RULES: "FIT Rules"
    PRODUCT_CATEGORY_DETAILS_JUMP: "Jump"
    PRODUCT_CATEGORY_DETAILS_MF_INVESTMENTS: "MF Investments"
    PRODUCT_CATEGORY_DETAILS_MF_ONBOARDING: "MF Onboarding"
    PRODUCT_CATEGORY_DETAILS_MF_WITHDRAWALS: "MF Withdrawals"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS_GENERAL: "US Stocks"
    PRODUCT_CATEGORY_DETAILS_US_STOCKS_WALLET_ISSUES: "US Stocks Wallet Issues"
    PRODUCT_CATEGORY_DETAILS_DEPRECATED_PRODUCT: "Deprecated Product"
    PRODUCT_CATEGORY_DETAILS_BLANK_CHAT: "Blank Chat"
    PRODUCT_CATEGORY_DETAILS_CALL_DROP_DISCONNECTED: "Call Drop/Disconnected"
    PRODUCT_CATEGORY_DETAILS_INCOMPLETE_EMAIL: "Incomplete Email"
    PRODUCT_CATEGORY_DETAILS_LOAN_APPLICATION_DISBURSAL: "Application/Disbursal Issue"
    PRODUCT_CATEGORY_DETAILS_LOAN_BUREAU_CIBIL: "Bureau/Cibil"
    PRODUCT_CATEGORY_DETAILS_LOAN_COLLECTIONS: "Collections"
    PRODUCT_CATEGORY_DETAILS_LOAN_EMI_RELATED: "EMI Related Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_LAMF_SHORTFALL: "LAMF Shortfall"
    PRODUCT_CATEGORY_DETAILS_LOAN_CLOSURE_REQUEST: "Loan Closure Request/Issues"
    PRODUCT_CATEGORY_DETAILS_LOAN_OUTCALLING: "Outcalling"
    PRODUCT_CATEGORY_DETAILS_LOAN_PERSONAL_DETAILS: "Personal Details Updatation"
    PRODUCT_CATEGORY_DETAILS_LOAN_REFUND_WAIVER: "Refund/Waiver Request"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_CONNECT: "Unable To Connect"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_DISCONNECT: "Unable to Disconnect"
    PRODUCT_CATEGORY_DETAILS_REWARDS_FI_POINTS_NOT_REFUNDED: "Fi Points Not Refunded"
    PRODUCT_CATEGORY_DETAILS_REWARDS_GIFT_CARDS: "Gift Cards"
    PRODUCT_CATEGORY_DETAILS_REWARDS_INCORRECT_REWARD: "Incorrect Reward Received"
    PRODUCT_CATEGORY_DETAILS_REWARDS_NOT_RECEIVED: "Reward Not Received"
    PRODUCT_CATEGORY_DETAILS_BANK_INITIATED_FREEZE: "Bank Initated Freeze"
    PRODUCT_CATEGORY_DETAILS_INVESTMENT_WITHDRAWALS: "Investment Withdrawals"
    PRODUCT_CATEGORY_DETAILS_LEA_NPCI_COMPLAINT: "LEA/NPCI Complaint"
    PRODUCT_CATEGORY_DETAILS_CALLBACK_REQUEST: "Callback Request"
    PRODUCT_CATEGORY_DETAILS_DATA_DELETION: "Data Deletion"
    PRODUCT_CATEGORY_DETAILS_NACH_AND_MANDATES: "Nach & Mandates"
    PRODUCT_CATEGORY_DETAILS_REVOKE_APP_ACCESS: "Revoke App Access"
    PRODUCT_CATEGORY_DETAILS_STOP_SERVICES: "Stop Services"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED: "Amount Debited"
    PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED_NOT_CREDITED: "Amount Debited But Not Credited"
    PRODUCT_CATEGORY_DETAILS_AUTOMATED_PAYMENTS: "Automated Payments"
    PRODUCT_CATEGORY_DETAILS_UNAUTHORISED_FRAUD_TRANSACTIONS: "Unauthorised/Fraud Transactions"
    PRODUCT_CATEGORY_DETAILS_CHEQUE_TRANSACTION: "Cheque Transaction"
    PRODUCT_CATEGORY_DETAILS_DATA_NOT_REFRESHED: "Data Not Refreshed"
    PRODUCT_CATEGORY_DETAILS_BUYING_US_STOCKS: "Buying US Stocks"
    PRODUCT_CATEGORY_DETAILS_BUSINESS_COLLABORATION: "Business Collaboration"
    PRODUCT_CATEGORY_DETAILS_NET_BANKING: "Net Banking"
    PRODUCT_CATEGORY_DETAILS_UNREGISTERED_USER: "Unregistered User"
    PRODUCT_CATEGORY_DETAILS_UNABLE_TO_PAY: "Unable To Pay"
    PRODUCT_CATEGORY_DETAILS_CREDIT_PENDING_TO_FI: "Credit Pending To Fi"
    PRODUCT_CATEGORY_DETAILS_DOCUMENT_REQUEST: "Document Request"
  SubCategoryDetailsEnumToValueMapping:
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_NEW: "New"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_APPROVED: "Approved"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_REJECTED: "Rejected"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_ON_HOLD: "On-Hold"
    SUB_CATEGORY_PIN_UPI_PIN: "UPI PIN"
    SUB_CATEGORY_PIN_DEVICE_PIN: "Device PIN"
    SUB_CATEGORY_PIN_APP_PIN: "App PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING: "QR code not working"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN: "Unable to set PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS: "Cannot enable POS"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS: "Cannot enable Contactless"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL: "Cannot enable ATM withdrawal"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED: "OTP not received"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE: "How to activate"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING: "Tracking"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD: "Did not receive card"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND: "Balance refund"
    SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE: "Debited but not dispensed at machine"
    SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED: "UPI pin tries exceeded"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_PRE_CLOSURE: "Pre-Closure FD"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_MATURITY: "Mature FD"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_PRE_CLOSURE: "Pre-Closure SD"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_MATURITY: "Mature SD"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_ONBOARDING_VKYC_VKYC_REVIEW_STATUS: "VKYC Review Status"

    # Account Closure Request L3 categories
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FREEZE_ON_ACCOUNT: "Freeze On Account"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_IN_APP_REQUEST_RECEIVED: "In App Request Received (Auto ID)"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_MANUAL_REQUEST: "Manual Request"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_NOT_CLOSABLE_DUE_TO_PENDING_CHARGES: "Not Closable Due To Pending Charges"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_REDIRECTED_TO_APP: "Redirected To App"
    SUB_CATEGORY_ACCOUNTS_CLOSURE_FULL_KYC_ACCOUNT_CLOSED: "Full Kyc Account Closed"

    # Account Opening Issues L3 categories
    SUB_CATEGORY_ACCOUNTS_OPENING_ADD_FUNDS_ON_APP: "Add Funds On App"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_DOWNLOAD: "App Download"
    SUB_CATEGORY_ACCOUNTS_OPENING_DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CARD_CREATION_PIN_SETUP_FAILURE: "Card Creation & Pin Setup Failure"
    SUB_CATEGORY_ACCOUNTS_OPENING_CONSENT_RELATED: "Consent Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_CUSTOMER_CREATION: "Customer Creation"
    SUB_CATEGORY_ACCOUNTS_OPENING_EXISTING_FEDERAL_ACCOUNT: "Existing Federal Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_KYC_RELATED: "KYC Related"
    SUB_CATEGORY_ACCOUNTS_OPENING_LIVENESS_FACEMATCH_ISSUE: "Liveness & Facematch Issue"
    SUB_CATEGORY_ACCOUNTS_OPENING_APP_SCREENING: "App Screening"
    SUB_CATEGORY_ACCOUNTS_OPENING_REFUND_FOR_ADD_FUNDS: "Refund For Add Funds"
    SUB_CATEGORY_ACCOUNTS_OPENING_REOPEN_CLOSED_ACCOUNT: "Reopen Closed Account"
    SUB_CATEGORY_ACCOUNTS_OPENING_STUCK_AT_EMAIL_VERIFICATION: "Stuck At Email Verfication"
    SUB_CATEGORY_ACCOUNTS_OPENING_VKYC_ISSUES: "Vkyc Issues"

    # Account Upgrade/Downgrade L3 categories
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_FUNDS_ADDED_BUT_ACCOUNT_NOT_UPGRADED: "Funds Added But A/C Not Upgraded"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_TIER_DOWNGRADE: "Tier Downgrade"
    SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_WITHIN_COOL_OFF_PERIOD: "Within Cool Off Period"

    # Account KYC Related L3 categories
    SUB_CATEGORY_ACCOUNTS_KYC_MIN_KYC_EXPIRED: "Min Kyc Expired"
    SUB_CATEGORY_ACCOUNTS_KYC_UNABLE_TO_SUBMIT_FORM: "Unable To Submit Form"
    SUB_CATEGORY_ACCOUNTS_KYC_NOT_UPDATED: "KYC Not Updated"
    SUB_CATEGORY_ACCOUNTS_KYC_UPDATED_BUT_ACCOUNT_NOT_ACTIVATED: "KYC Updated but A/C Not Activated"
    SUB_CATEGORY_ACCOUNTS_KYC_SIGNATURE_NOT_UPDATED: "Signature Not Updated"

    # Chequebook Related L3 categories
    SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_CHARGES_WAIVER: "Chequebook Charges Waiver"
    SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_NOT_RECEIVED: "Chequebook Not Received"
    SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_UNABLE_TO_UPLOAD_SIGNATURE: "Unable To Upload Signature"

    # Account Fees and Charges L3 categories
    SUB_CATEGORY_ACCOUNTS_FEES_CHARGES_AMB: "AMB"

    # Account Information L3 categories
    SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_INFO: "Account Info"
    SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_TIER_DETAILS_BENEFITS: "Account Tier Details/ Benefits"
    SUB_CATEGORY_ACCOUNTS_INFO_APP_SETTINGS: "App Settings"
    SUB_CATEGORY_ACCOUNTS_INFO_NR_ACCOUNT: "NR Account"
    SUB_CATEGORY_ACCOUNTS_INFO_RE_KYC_ISSUES: "Re KYC Issues"
    SUB_CATEGORY_ACCOUNTS_INFO_INFORMATION_REGARDING_LIEN: "Information Regarding Lien"

    # App Login Issues L3 categories
    SUB_CATEGORY_ACCOUNTS_APP_LOGIN_ISSUES: "App Login Issues"
    SUB_CATEGORY_ACCOUNTS_APP_LOGIN_DEVICE_PASSWORD_NOT_ACCEPTED: "Device Password Not Accepted"
    SUB_CATEGORY_ACCOUNTS_APP_LOGIN_RE_LOGIN_BEFORE_SIGNUP: "Re Login Before Signup"

    # App Related Issues L3 categories
    SUB_CATEGORY_APP_RELATED_GENERAL_QUERIES: "General Queries"
    SUB_CATEGORY_APP_RELATED_INSURANCE_RELATED: "Insurance Related"
    SUB_CATEGORY_APP_RELATED_REGISTRATION: "Registration"
    SUB_CATEGORY_APP_RELATED_UPGRADE_DOWNGRADE_ISSUE: "Upgrade/ Downgrade Issue"
    SUB_CATEGORY_APP_RELATED_APP_CRASH: "App Crash"
    SUB_CATEGORY_APP_RELATED_FEATURE_NOT_LOADING: "Feature Not Loading"
    SUB_CATEGORY_APP_RELATED_USER_FEEDBACK: "User Feedback"
    SUB_CATEGORY_APP_RELATED_FULFILLMENT_RELATED: "Fulfillment Related"
    SUB_CATEGORY_APP_RELATED_REWARDS_RELATED: "Rewards Related"
    SUB_CATEGORY_APP_RELATED_REDIRECTED_TO_BANK: "Redirected To Bank"

    # Card Request L3 categories
    SUB_CATEGORY_CARD_REQUEST_CARD_DAMAGED: "Card Damaged"
    SUB_CATEGORY_CARD_REQUEST_CARD_NOT_REQUIRED: "Card Not Required"
    SUB_CATEGORY_CARD_REQUEST_LOST_STOLEN: "Lost/ Stolen"
    SUB_CATEGORY_CARD_REQUEST_DIGITAL_CARD: "Digital Card"

    # Card Settings L3 categories
    SUB_CATEGORY_CARD_SETTINGS_ACTIVATE_CARD: "Activate Card"
    SUB_CATEGORY_CARD_SETTINGS_CHANGE_USAGE_SETTINGS: "Change Usage Settings"
    SUB_CATEGORY_CARD_SETTINGS_PIN_FAILING: "PIN Failing"
    SUB_CATEGORY_CARD_SETTINGS_TEMPORARY_FREEZE: "Temporary Freeze"
    SUB_CATEGORY_CARD_SETTINGS_UNABLE_TO_CHANGE_USAGE_SETTINGS: "Unable To Change Usage Settings"

    # Card Delivery L3 categories
    SUB_CATEGORY_CARD_DELIVERY_RTO_REDISPATCH: "RTO (Redispatch)"
    SUB_CATEGORY_CARD_DELIVERY_RTO_REFUND: "RTO (Refund)"

    # Card Charges L3 categories
    SUB_CATEGORY_CARD_CHARGES_AMC: "AMC"
    SUB_CATEGORY_CARD_CHARGES_ECOM_POS_DECLINE_FEES: "ECOM/POS Decline Fees"
    SUB_CATEGORY_CARD_CHARGES_OTHER_CHARGES: "Other Charges"

    # Card Info L3 categories
    SUB_CATEGORY_CARD_INFO_CARD_DETAILS: "Card Details"
    SUB_CATEGORY_CARD_INFO_DELIVERY_RELATED: "Delivery Related"
    SUB_CATEGORY_CARD_INFO_MIN_KYC_USER: "Min Kyc User"

    # ATM Transactions L3 categories
    SUB_CATEGORY_ATM_TRANSACTIONS_CDM_CASH_DEPOSIT_NOT_CREDITED: "CDM Cash Deposit Not Credited"
    SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_NOT_DISPENSED: "Debited But Not Dispensed"
    SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_PARTIALLY_DISPENSED: "Debited But Partially Dispensed"
    SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_DOMESTIC: "Unable To Withdraw (Domestic)"
    SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_INTERNATIONAL: "Unable To Withdraw (International)"

    # Transaction Issues L3 categories
    SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_DEBITED_BUT_NOT_CREDITED: "Amount Debited But Not Credited"
    SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_DOMESTIC: "Txn Failed (Domestic)"
    SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_INTERNATIONAL: "Txn Failed (International)"
    SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_FAILED_REFUND_NOT_RECEIVED: "Order Failed Refund Not Received"
    SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_PENDING: "Order Pending"

    # Fixed Deposit and Smart Deposit L3 categories
    SUB_CATEGORY_FD_SD_UNABLE_TO_CREATE: "Unable To Create"
    SUB_CATEGORY_FD_SD_UNABLE_TO_MODIFY: "Unable To Modify"
    SUB_CATEGORY_FD_SD_UNABLE_TO_PAUSE: "Unable To Pause"
    SUB_CATEGORY_FD_SD_CANCEL_AUTO_RENEWAL: "Cancel Auto Renewal"
    SUB_CATEGORY_FD_SD_FD_CLOSED_BUT_AMOUNT_NOT_RECEIVED: "FD Closed But Amount Not Received"
    SUB_CATEGORY_FD_SD_INCORRECT_MATURITY_AMOUNT: "Incorrect Maturity Amount"
    SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_FD: "Unable To Close FD"
    SUB_CATEGORY_FD_SD_SD_CLOSED_BUT_AMOUNT_NOT_RECEIVED: "SD Closed But Amount Not Received"
    SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_SD: "Unable To Close SD"

    # FIT Rules L3 categories
    SUB_CATEGORY_FIT_RULES_FIT_RULE_NOT_EXECUTED: "FIT Rule Not Executed"
    SUB_CATEGORY_FIT_RULES_INCORRECT_AMOUNT_DEPOSITED: "Incorrect Amount Deposited"
    SUB_CATEGORY_FIT_RULES_FIT_RULE_INFORMATION: "FIT Rule Information"

    # Jump L3 categories
    SUB_CATEGORY_JUMP_PORTFOLIO_MISMATCH: "Portfolio Mismatch"
    SUB_CATEGORY_JUMP_WITHDRAWAL_ISSUES: "Withdrawal Issues"

    # Mutual Funds L3 categories
    SUB_CATEGORY_MUTUAL_FUNDS_SIP_NOT_DEDUCTED: "SIP Not Deducted"
    SUB_CATEGORY_MUTUAL_FUNDS_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED: "Transaction Successful Units Not Allotted"
    SUB_CATEGORY_MUTUAL_FUNDS_STUCK_IN_SCREENING: "Stuck In Screening"
    SUB_CATEGORY_MUTUAL_FUNDS_WITHDRAWAL_FROM_OTHER_PLATFORM: "Withdrawal From Other Platform"
    SUB_CATEGORY_MUTUAL_FUNDS_INCORRECT_AMOUNT_CREDITED: "Incorrect Amount Credited"
    SUB_CATEGORY_MUTUAL_FUNDS_PAUSE_AUTO_INVESTMENT: "Pause Auto Investment"

    # US Stocks L3 categories
    SUB_CATEGORY_US_STOCKS_AMOUNT_DEBITED_NOT_CREDITED_TO_WALLET: "Amount Debited Not Credited To Wallet"

    # Fi Store L3 categories
    SUB_CATEGORY_FI_STORE_DIRECT_TO_HOME: "Direct To Home"
    SUB_CATEGORY_FI_STORE_PHYSICAL_MERCHANDISE: "Fi Store Physical Merchandise"

    # Salary Programs L3 categories
    SUB_CATEGORY_SALARY_PROGRAMS_INSTANT_SALARY: "Instant Salary"
    SUB_CATEGORY_SALARY_PROGRAMS_SALARY_LITE: "Salary Lite"
    SUB_CATEGORY_SALARY_PROGRAMS_INFORMATION_REGARDING_CHARGES: "Information Regarding Charges"

    # Communication L3 categories
    SUB_CATEGORY_COMMUNICATION_SPAM: "SPAM"
    SUB_CATEGORY_COMMUNICATION_CALLBACK: "Callback"
    SUB_CATEGORY_COMMUNICATION_REQUEST_FOR_MORE_INFO: "Request For More Info"

    # Loans L3 categories
    SUB_CATEGORY_LOANS_APPLICATION_FAILED: "Application Failed"
    SUB_CATEGORY_LOANS_DISBURSAL_PENDING: "Disbursal Pending"
    SUB_CATEGORY_LOANS_ISSUE_WITH_LOAN_APPLICATION: "Issue With Loan Application"
    SUB_CATEGORY_LOANS_LOAN_DISBURSED_BUT_ACCOUNT_NOT_CREATED: "Loan Disbursed But A/C Not Created"
    SUB_CATEGORY_LOANS_CONSENT_WITHDRAWAL_FOR_CIBIL_ENQUIRY: "Consent Withdrawal For Cibil Enquiry"
    SUB_CATEGORY_LOANS_REQUEST_FOR_BUREAU_CORRECTION: "Request For Bureau Correction"
    SUB_CATEGORY_LOANS_BORROWERS_DEMISE: "Borrower's Demise"
    SUB_CATEGORY_LOANS_HARASSMENT_COMPLAINT: "Harrasment Complaint"
    SUB_CATEGORY_LOANS_PAYMENT_LINK_TO_BE_SENT: "Payment Link To Be Sent"
    SUB_CATEGORY_LOANS_REQUEST_FOR_SETTLEMENT: "Request For Settlement"
    SUB_CATEGORY_LOANS_REQUESTING_EMI_EXTENSION: "Requesting for EMI Extension"
    SUB_CATEGORY_LOANS_REPAYMENT_SCHEDULE: "Repayment Schedule"
    SUB_CATEGORY_LOANS_EMI_NOT_DEDUCTED: "EMI Not Deducted"
    SUB_CATEGORY_LOANS_NACH_RE_REGISTRATION: "Nach Re Registration"
    SUB_CATEGORY_LOANS_PAYMENT_STATUS_NOT_UPDATED: "Payment Status Not Updated"
    SUB_CATEGORY_LOANS_LOAN_DETAILS_AND_STATUS: "Loan Details & Status"
    SUB_CATEGORY_LOANS_LOAN_PRE_CLOSURE: "Loan Pre Closure"
    SUB_CATEGORY_LOANS_LOAN_REPAYMENT: "Loan Repayment"
    SUB_CATEGORY_LOANS_PAY_MARGIN_AMOUNT: "Pay Margin Amount"
    SUB_CATEGORY_LOANS_PLEDGE_MORE_FUNDS: "Pledge More Funds"
    SUB_CATEGORY_LOANS_PLEDGED_MUTUAL_FUNDS_SOLD: "Pledged Mutual Funds Sold"
    SUB_CATEGORY_LOANS_DELAY_IN_CLOSURE: "Delay in Closure"
    SUB_CATEGORY_LOANS_PAID_BUT_MF_NOT_UNPLEDGED: "Paid But MF Not Unpledged"
    SUB_CATEGORY_LOANS_PRE_CLOSURE: "Pre Closure"
    SUB_CATEGORY_LOANS_PRE_DISBURSEMENT: "Pre Disbursement"
    SUB_CATEGORY_LOANS_SALES: "Sales"
    SUB_CATEGORY_LOANS_SERVICE: "Service"
    SUB_CATEGORY_LOANS_EMI_PAID_BUT_ECS_NACH_RETURN_CHARGED: "EMI Paid But ECS/NACH Return Charged"
    SUB_CATEGORY_LOANS_LATE_PAYMENT_FEES: "Late Payment Fees"
    SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_APP: "Unable To Pay Via App"
    SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_COLLECTIONS_LINK: "Unable To Pay Via Collections Link"

    # Assets L3 categories
    SUB_CATEGORY_ASSETS_ASSETS_VIA_MANUAL_FORMS: "Assets Via Manual Forms"
    SUB_CATEGORY_ASSETS_EPFO: "EPFO"
    SUB_CATEGORY_ASSETS_INDIAN_STOCKS: "Indian Stocks"
    SUB_CATEGORY_ASSETS_LOANS: "Loans"
    SUB_CATEGORY_ASSETS_NPS: "NPS"
    SUB_CATEGORY_ASSETS_OTHER_BANK_ACCOUNTS: "Other Bank Accounts"
    SUB_CATEGORY_ASSETS_ABOUT_NEGATIVE_BALANCE: "About Negative Balance"
    SUB_CATEGORY_ASSETS_ABOUT_NETWORTH: "About Networth"
    SUB_CATEGORY_ASSETS_CONNECTED_ACCOUNTS: "Connected Accounts"
    SUB_CATEGORY_ASSETS_UNABLE_TO_ADD_ASSETS_LIABILITIES: "Unable To Add Assets/Liabilities"

    # Rewards L3 categories
    SUB_CATEGORY_REWARDS_CONVERT_TO_CASH: "Convert To Cash"
    SUB_CATEGORY_REWARDS_PLAY_AND_WIN: "Play And Win"
    SUB_CATEGORY_REWARDS_POWER_UP: "Power Up"
    SUB_CATEGORY_REWARDS_TRAVEL_MILES: "Travel Miles"
    SUB_CATEGORY_REWARDS_HOW_TO_GET_REWARDS: "How To Get Rewards"
    SUB_CATEGORY_REWARDS_HOW_TO_REFER: "How To Refer"
    SUB_CATEGORY_REWARDS_REWARDS_STATEMENT: "Rewards Statement"
    SUB_CATEGORY_REWARDS_AMOUNT_NOT_REFUNDED: "Amount Not Refunded"
    SUB_CATEGORY_REWARDS_EXPIRED_VOUCHER_RECEIVED: "Expired Voucher Received"
    SUB_CATEGORY_REWARDS_FI_POINTS_NOT_REFUNDED: "Fi Points Not Refunded"
    SUB_CATEGORY_REWARDS_CAMPAIGN_SPECIFIC: "Campaign Specific"
    SUB_CATEGORY_REWARDS_DEBIT_CARD_OFFERS: "Debit Card Offers"
    SUB_CATEGORY_REWARDS_REFERRAL: "Referral"
    SUB_CATEGORY_REWARDS_TIERING_REWARDS: "Tiering Rewards"

    # KYC L3 categories
    SUB_CATEGORY_KYC_NON_KYC_RELATED: "Non KYC Related"
    SUB_CATEGORY_KYC_VKYC_RELATED: "VKYC Related"

    # Account Security L3 categories
    SUB_CATEGORY_ACCOUNT_SECURITY_REQUEST_TO_UNFREEZE: "Request To Unfreeze"
    SUB_CATEGORY_ACCOUNT_SECURITY_FREEZE_RELATED: "Freeze Related"
    SUB_CATEGORY_ACCOUNT_SECURITY_ADDITIONAL_INFORMATION: "Additional Information"
    SUB_CATEGORY_ACCOUNT_SECURITY_NOC_RELATED: "NOC Related"

    # Language Support L3 categories
    SUB_CATEGORY_LANGUAGE_ASSAMESE: "Assamese"
    SUB_CATEGORY_LANGUAGE_BENGALI: "Bengali"
    SUB_CATEGORY_LANGUAGE_HINDI: "Hindi"
    SUB_CATEGORY_LANGUAGE_KANNADA: "Kannada"
    SUB_CATEGORY_LANGUAGE_MALAYALAM: "Malayalam"
    SUB_CATEGORY_LANGUAGE_ORIYA: "Oriya"
    SUB_CATEGORY_LANGUAGE_TAMIL: "Tamil"
    SUB_CATEGORY_LANGUAGE_TELUGU: "Telugu"

    # Data and Statements L3 categories
    SUB_CATEGORY_DATA_STATEMENTS_REQUEST_DATA_DELETION: "Request Data Deletion"
    SUB_CATEGORY_DATA_STATEMENTS_BANK_STATEMENT: "Bank Statement"
    SUB_CATEGORY_DATA_STATEMENTS_MUTUAL_FUNDS_STATEMENT: "Mutual Funds Statement"
    SUB_CATEGORY_DATA_STATEMENTS_SIGNED_BANK_STATEMENT: "Signed Bank Statement"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_JUMP: "Tax Statement (Jump)"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_MUTUAL_FUNDS: "Tax Statement (Mutual Funds)"
    SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_US_STOCKS: "Tax Statement (US Stocks)"
    SUB_CATEGORY_DATA_STATEMENTS_US_STOCKS_STATEMENT: "US Stocks Statement"

    # Mandates L3 categories
    SUB_CATEGORY_MANDATES_ACTIVE_MANDATES_DETAILS: "Active Mandates Details"
    SUB_CATEGORY_MANDATES_CANCEL_SI_NACH_MANDATES: "Cancel SI/NACH Mandates"

    # Profile Updates L3 categories
    SUB_CATEGORY_PROFILE_UPDATES_CHANGE_EMPLOYMENT_DETAILS: "Change Employment Details"
    SUB_CATEGORY_PROFILE_UPDATES_CONTACT_DETAILS_UPDATE: "Contact Details Update"
    SUB_CATEGORY_PROFILE_UPDATES_DOB_CHANGE: "DOB Change"
    SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_FATHER_MOTHER: "Name Change (Father/Mother)"
    SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_USER: "Name Change (User)"

    # Device Issues L3 categories
    SUB_CATEGORY_DEVICE_ISSUES_CARDS: "Cards"
    SUB_CATEGORY_DEVICE_ISSUES_DEVICE_LOST: "Device Lost"
    SUB_CATEGORY_DEVICE_ISSUES_PROMOTIONAL_COMMS: "Promotional Comms"

    # Transaction Types L3 categories
    SUB_CATEGORY_TRANSACTION_TYPES_GOODS_SERVICES_NOT_DELIVERED: "Goods/Services Not Delivered"
    SUB_CATEGORY_TRANSACTION_TYPES_INCORRECT_AMOUNT: "Incorrect Amount"
    SUB_CATEGORY_TRANSACTION_TYPES_NOT_VISIBLE_ON_APP: "Not Visible On App"
    SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2M: "In App UPI (P2M)"
    SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2P: "In App UPI (P2P)"
    SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2M: "Off App UPI (P2M)"
    SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2P: "Off App UPI (P2P)"
    SUB_CATEGORY_TRANSACTION_TYPES_INTRA_BANK: "INTRA BANK"
    SUB_CATEGORY_TRANSACTION_TYPES_NACH_ECS_CHARGES: "NACH/ECS Charges"
    SUB_CATEGORY_TRANSACTION_TYPES_RECURRING_PAYMENT_CANCELLED_BUT_AMOUNT_DEBITED: "Recurring Payment cancelled But Amount Debited"
    SUB_CATEGORY_TRANSACTION_TYPES_CHEQUE_DEPOSIT: "Cheque Deposit"
    SUB_CATEGORY_TRANSACTION_TYPES_INTERNATIONAL_REMITTANCE: "International Remittance"
    SUB_CATEGORY_TRANSACTION_TYPES_MERCHANT_REFUND: "Merchant Refund"
    SUB_CATEGORY_TRANSACTION_TYPES_OTHER_DOMESTIC_TRANSACTIONS: "Other Domestic Transactions"
    SUB_CATEGORY_TRANSACTION_TYPES_DEPOSITING_CASH: "Depositing Cash"
    SUB_CATEGORY_TRANSACTION_TYPES_IPO: "IPO"
    SUB_CATEGORY_TRANSACTION_TYPES_TRANSACTION_RELATED_ENQUIRY: "Transaction Related Enquiry"

    # UPI Issues L3 categories
    SUB_CATEGORY_UPI_ISSUES_UNABLE_TO_LINK_FEDERAL_ACCOUNT_TO_OTHER_APPS: "Unable To Link Federal Account To Other Apps"
    SUB_CATEGORY_UPI_ISSUES_BANK_TRANSFER: "Bank Transfer"
    SUB_CATEGORY_UPI_ISSUES_INTERNATIONAL_TRANSACTIONS: "International Transactions"
    SUB_CATEGORY_UPI_ISSUES_LIMIT_EXCEEDED: "Limit Exceeded"
    SUB_CATEGORY_UPI_ISSUES_PIN_TRIES_EXCEEDED: "Pin Tries Exceeded"
    SUB_CATEGORY_UPI_ISSUES_UPI_ISSUE: "UPI Issue"

    # International Transactions L3 categories
    SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_INTERNATIONAL: "International"
    SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_STOP_CHEQUE_PAYMENT: "Stop Cheque Payment"
    SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_FEES_AND_CHARGES: "Fees & Charges"

    # Certificates L3 categories
    SUB_CATEGORY_CERTIFICATES_BALANCE_CERTIFICATE: "Balance Certificate"
    SUB_CATEGORY_CERTIFICATES_INTEREST_CERTIFICATE: "Interest Certificate"

    # Eligibility Issues L3 categories
    SUB_CATEGORY_ELIGIBILITY_NOT_ELIGIBLE: "Not Eligible"
    SUB_CATEGORY_ELIGIBILITY_OTP_NOT_RECEIVED: "OTP Not Received"

    # Profile Changes L3 categories
    SUB_CATEGORY_PROFILE_CHANGES_ADDRESS_CHANGE: "Address Change"
    SUB_CATEGORY_PROFILE_CHANGES_UNAUTHORISED_TRANSACTION: "Unauthorised Transaction"

    # Card Usage Issues L3 categories
    SUB_CATEGORY_CARD_USAGE_CARD_NOT_ACCEPTED: "Card Not Accepted"
    SUB_CATEGORY_CARD_USAGE_CONTACTLESS_NOT_WORKING: "Contactless Not Working"
    SUB_CATEGORY_CARD_USAGE_ATM_DECLINE_FEES: "ATM Decline Fees"
    SUB_CATEGORY_CARD_USAGE_FUEL_CHARGES: "Fuel Charges"
    SUB_CATEGORY_CARD_USAGE_TCS_DEDUCTIONS: "TCS Deductions"

    # Balance Issues L3 categories
    SUB_CATEGORY_BALANCE_ISSUES_BALANCE_NOT_UPDATED: "Balance Not Updated"
    SUB_CATEGORY_BALANCE_ISSUES_DOUBLE_DEBIT: "Double Debit"
    SUB_CATEGORY_BALANCE_ISSUES_INCORRECT_AMOUNT_DEBITED: "Incorrect Amount Debited"
    SUB_CATEGORY_BALANCE_ISSUES_EXCESS_AMOUNT_PAID: "Excess Amount Paid"

    # App Access Issues L3 categories
    SUB_CATEGORY_APP_ACCESS_NO_APP_ACCESS: "No App Access"
    SUB_CATEGORY_APP_ACCESS_UNABLE_TO_INVEST: "Unable to Invest"
    SUB_CATEGORY_APP_ACCESS_UNABLE_TO_WITHDRAW: "Unable To Withdraw"
    SUB_CATEGORY_APP_ACCESS_UNABLE_TO_ADD_FUNDS: "Unable To Add Funds"

    # Payment Issues L3 categories
    SUB_CATEGORY_PAYMENT_ISSUES_SENT_TO_WRONG_USER: "Sent To Wrong User"
    SUB_CATEGORY_PAYMENT_ISSUES_CASH_DEPOSIT_AT_BRANCH: "Cash Deposit At Branch"

    # Fixed/Smart Deposit Issues L3 categories
    SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_FD: "Unable To Create FD"
    SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_SD: "Unable To Create SD"
    SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_REDEEM: "Unable To Redeem"

    # Account Status Issues L3 categories
    SUB_CATEGORY_ACCOUNT_STATUS_ACCOUNT_FROZEN_CLOSED: "Account Frozen/Closed"
    SUB_CATEGORY_ACCOUNT_STATUS_EMAIL_ADDRESS: "Email Address"
    SUB_CATEGORY_ACCOUNT_STATUS_PHONE_NUMBER: "Phone Number"
    SUB_CATEGORY_ACCOUNT_STATUS_BOUNCE_CHARGE: "Bounce Charge"

    # Reward Issues L3 categories
    SUB_CATEGORY_REWARD_ISSUES_VOUCHER_NOT_RECEIVED: "Voucher Not Received"
    SUB_CATEGORY_REWARD_ISSUES_FOREX_RATE_ISSUE: "Forex Rate Issue"

    # Stock Trading Issues L3 categories
    SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_BUY: "Unable To Buy"
    SUB_CATEGORY_STOCK_TRADING_MONEY_NOT_CREDITED: "Money Not Credited"
    SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_SELL: "Unable To Sell"
  OsTypeEnumToValueMapping:
    ANDROID: "Android"
    IOS: "iOS"
  ResolutionModeEnumToValueMapping:
    RESOLUTION_MODE_AUTO_RESOLUTION: "Auto Resolution"
    RESOLUTION_MODE_BULK_RESOLUTION: "Bulk Resolution"
    RESOLUTION_MODE_MANUAL_RESOLUTION: "Manual Resolution"
    RESOLUTION_MODE_WATSON_RESOLUTION: "Watson Resolution"
  TicketVisibilityEnumToValueMapping:
    TICKET_VISIBILITY_ONLY_AGENT: "Agent"
    TICKET_VISIBILITY_ONLY_CUSTOMER: "Customer"
    TICKET_VISIBILITY_ALL: "All"
  SavingsAccountBalanceEnumToValueMapping:
    SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1: "Less than 1"
    SAVINGS_ACCOUNT_BALANCE_OTHER: "Other"
  MonorailRaisedEnumToValueMapping:
    MONORAIL_RAISED_YES: "Yes"
    MONORAIL_RAISED_NO: "No"
  BooleanEnumToYesNoMapping:
    TRUE: "Yes"
    FALSE: "No"
  DefaultPageSize: 100

CRM:
  Freshdesk:
    Risk:
      DefaultPageSize: 100
      VerdictEnumToValueMapping:
        VERDICT_UNSPECIFIED: ""
        VERDICT_PASS: "Pass"
        VERDICT_FAIL: "Fail"
      ReviewTypeEnumToValueMapping:
        REVIEW_TYPE_UNSPECIFIED: ""
        REVIEW_TYPE_USER_REVIEW: "User Review"
        REVIEW_TYPE_TRANSACTION_REVIEW: "Transaction Review"
        REVIEW_TYPE_AFU_REVIEW: "Afu Review"
        REVIEW_TYPE_LEA_COMPLAINT_REVIEW: "LEA Complaint Review"
        REVIEW_TYPE_ESCALATION_REVIEW: "Escalation Review"
      PriorityEnumToValueMapping:
        PRIORITY_UNSPECIFIED: 0
        PRIORITY_CRITICAL: 4
        PRIORITY_HIGH: 3
        PRIORITY_MEDIUM: 2
        PRIORITY_LOW: 1
      StatusEnumToValueMapping:
        STATUS_UNSPECIFIED: 0
        STATUS_CREATED: 6
        STATUS_ASSIGNED: 7
        STATUS_IN_REVIEW: 8
        STATUS_IN_QA_REVIEW: 9
        STATUS_DONE: 10
        STATUS_WONT_REVIEW: 11
        STATUS_MANUAL_INTERVENTION: 12
        STATUS_PENDING_USER_INFO: 13
        STATUS_REVIEW_ACTION_IN_PROGRESS: 14
        STATUS_MARKED_FOR_AUTO_ACTION: 15
        STATUS_PENDING_ON_USER: 16
      AgentGroupEnumToGroupIdMapping:
        AGENT_GROUP_UNSPECIFIED: 0
        AGENT_GROUP_USER_OUTBOUND_CALL: 88000111045
        AGENT_GROUP_TRANSACTION_REVIEW: 88000105522
        AGENT_GROUP_USER_REVIEW: 88000112250
        AGENT_GROUP_L1: 88000098850
        AGENT_GROUP_L2: 88000150196
        AGENT_GROUP_QA: 88000150196
        AGENT_GROUP_ESCALATION: 88000094206
        AGENT_GROUP_MULTI_REVIEW: 88000150740
      TicketScopeEnumToValueMapping:
        TICKET_SCOPE_UNSPECIFIED: 0
        GLOBAL_ACCESS: 1
        GROUP_ACCESS: 2
        RESTRICTED_ACCESS: 3

FederalLien:
  ReasonEnumToValueMap:
    REASON_UNSPECIFIED: ""
    REASON_UAT: "OTH"
  LienRequestTypeEnumToValueMap:
    LIEN_REQUEST_TYPE_ADD: "ADD"
    LIEN_REQUEST_TYPE_ENQUIRY: "ENQUIRY"
  LienTypeEnumToValueMap:
    LIEN_TYPE_ADD: "ADD"
  DateLayout: "2006-01-02"
  DateLayoutPostfix: "T00:00:00.000"
  Channel: "Fi"
  StatusCodeToEnumMap:
    S0000: "OK"
    S000: "OK"
    0000: "INTERNAL"
    F000: "INTERNAL"
    F001: "ALREADY_EXISTS"
  CBSStatusCodeToEnumMap:
    SUCCESS: "OK"
    FAILURE: "INTERNAL"

AwsSes:
  ContactListName: "fi"
  UnsubscribeOptionPlaceholder: "to unsubscribe <a href=\"{{amazonSESUnsubscribeUrl}}\">click here</a>"

MaxAllowedTimeIntervalForMiniStatement: 2160h

VendorAddresses:
  RegisteredAddresses:
    FEDERAL_BANK:
      RegionCode: "IN"
      PostalCode: "682031"
      AdministrativeArea: "KERALA"
      Locality: "KOCHI"
      AddressLines:
        - "EPIFI FEDERAL NEO BANKING"
        - "FEDERAL TOWERS,MARINE DRIVE"

DisputeConfig:
  FiRequesterId: "F768484C-9C9A-4023-B298-3BA45DA1F352"


PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: |
        {
         "key": "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",
         "cert": "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",
         "pass_phrase": "qwerty1234"
        }

RedactedRawRequestLogExceptionList: [ ]
RedactedRawResponseLogExceptionList: [ ]

VideoSdk:
  ApiHost: "https://api.videosdk.live"
  ApiVersion: "v2"
  JwtExpiryDuration: "45m"

DCIssuance:
  ChargeType: "DC"
  PartnerId: "EPIFI"
  ApiName: "ENQUIRY"

SkipServerCertVerification: false

