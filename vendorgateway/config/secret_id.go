// nolint
package config

const (
	// Federal
	EpifiFederalPgpPrivateKey = "EpifiFederalPgpPrivateKey"
	FederalPgpPublicKey       = "FederalPgpPublicKey"
	// Adding #nosec to suppress G101: Potential hardcoded credentials

	EpifiFederalPgpPassphrase              = "EpifiFederalPgpPassphrase" // #nosec
	EpifiFederalUpiPrivateKey              = "EpifiFederalUPIPrivateKey"
	EpifiFederalUPIFallbackPrivateKey      = "EpifiFederalUPIFallbackPrivateKey"
	SenderCode                             = "SenderCode"
	SenderCodeLSO                          = "sendercodelso"
	ServiceAccessId                        = "ServiceAccessId"
	ServiceAccessCode                      = "ServiceAccessCode"
	ClientId                               = "ClientId"
	ClientSecretKey                        = "ClientSecretKey"
	EpifiFederalCardDataPrivateKeyFallBack = "EpifiFederalCardDataPrivateKeyFallBack"
	EpifiFederalCardDataPrivateKey         = "EpifiFederalCardDataPrivateKey"

	ClosingBalanceMiniStatementCredentials = "ClosingBalanceCredentials"
	GetBalanceCredentialsV1                = "GetBalanceCredentialsV1"

	GetRemitterDetailsCredentials   = "GetRemitterDetailsCredentials"
	GetRemitterDetailsV1Credentials = "GetRemitterDetailsV1Credentials"

	GetBeneficiaryNameDetailsCredentials = "GetBeneficiaryNameDetailsCredentials"

	GetCsisStatusCredentials = "GetCsisStatusCredentials"

	// FCM
	FCMServiceAccountCredJson = "FCMServiceAccountCredJson"

	// Sendgrid
	SendGridAPIKey = "SendGridAPIKey"

	// Certs
	SimulatorCert               = "SimulatorCert"
	EpiFiFederalClientSslCert   = "EpiFiFederalClientSslCert"
	EpiFiFederalClientSslKey    = "EpiFiFederalClientSslKey"
	EpiFiFederalClientSslSecret = "EpiFiFederalClientSslSecret"

	RemittanceFederalRootCa           = "RemittanceFederalRootCA"
	RemittanceFederalRootCaV1         = "RemittanceFederalRootCAV1"
	RemittanceFederalRootCaV2         = "RemittanceFederalRootCAV2"
	RemittanceFederalIntermediateCa   = "RemittanceFederalIntermediateCA"
	RemittanceFederalIntermediateCaV1 = "RemittanceFederalIntermediateCAV1"
	RemittanceFederalIntermediateCaV2 = "RemittanceFederalIntermediateCAV2"

	// CX vendors
	FreshdeskApiKey = "FreshdeskApiKey"
	FreshchatApiKey = "FreshchatApiKey"
	OzonetelApiKey  = "OzonetelApiKey"

	// Pan validation API
	PanValidationAccessId   = "PanValidationAccessId"
	PanValidationAccessCode = "PanValidationAccessCode"

	// Loylty
	LoyltyClientId            = "LoyltyClientId"
	LoyltyClientKey           = "LoyltyClientKey"
	LoyltyClientSecret        = "LoyltyClientSecret"
	LoyltyClientEncryptionKey = "LoyltyClientEncryptionKey"
	LoyltyEGVModuleId         = "LoyltyEGVModuleId"
	LoyltyCharityModuleId     = "LoyltyCharityModuleId"
	LoyltyApplicationId       = "LoyltyApplicationId"
	LoyltyProgramId           = "LoyltyProgramId"

	// Qwikcilver
	QwikcilverSecrets = "QwikcilverSecrets"

	// Riskcovry
	RiskcovrySecrets = "RiskcovrySecrets"

	// Thriwe
	ThriweSecrets = "ThriweSecrets"

	// Dreamfolks
	DreamfolksSecrets = "DreamfolksSecrets"

	// Poshvine
	PoshvineSecrets = "PoshvineSecrets"

	// UPI APIs access codes
	UPISenderUserId   = "UPISenderUserId"
	UPISenderPassword = "UPISenderPassword"
	UPISenderCode     = "UPISenderCode"

	UPISenderUserIdIOSAddFunds   = "UPISenderUserIdIosAddFunds"
	UPISenderPasswordIOSAddFunds = "UPISenderPasswordIosAddFunds"
	UPISenderCodeIOSAddFunds     = "UPISenderCodeIosAddFunds"

	// SMS keys
	TwilioAccountSid = "TwilioAccountSid"
	TwilioApiKey     = "TwilioApiKey"
	ExotelApiKey     = "ExotelApiKey"

	// GPlace Keys
	GPlaceApiKey = "GPlaceApiKey"
	// Adding #nosec to suppress G101: Potential hardcoded credentials
	ExotelApiToken                 = "ExotelApiToken"
	KaleyraFederalApiKey           = "KaleyraFederalApiKey"
	KaleyraFederalCreditCardApiKey = "KaleyraFederalCreditCardApiKey"
	KaleyraEpifiApiKey             = "KaleyraEpifiApiKey"
	KaleyraEpifiNRApiKey           = "KaleyraEpifiNRApiKey"
	AclEpifiUserId                 = "AclEpifiUserId"
	AclEpifiPassword               = "AclEpifiPassword"
	AclFederalUserId               = "AclFederalUserId"
	AclFederalPassword             = "AclFederalPassword"
	AclEpifiOtpUserId              = "AclEpifiOtpUserId"
	NetCoreEpifiSecrets            = "NetCoreEpifiSecrets"
	AirtelFedSMSSecrets            = "AirtelFedSMSSecrets"
	AirtelEpifiSMSSecrets          = "AirtelEpifiSMSSecrets"
	// Adding #nosec to suppress G101: Potential hardcoded credentials
	AclEpifiOtpPassword     = "AclEpifiOtpPassword"
	AclWhatsappUserId       = "AclWhatsappUserId"
	AclWhatsappPassword     = "AclWhatsappPassword"
	WhatsappEnterpriseId    = "WhatsappEnterpriseId"
	WhatsappEnterpriseToken = "WhatsappEnterpriseToken"

	// Video KYC karza api key
	KarzaVkycApiKey           = "KarzaVkycApiKey"
	KarzaVkycPriorityApiKey   = "KarzaVkycPriorityApiKey"
	KarzaReVkycPriorityApiKey = "KarzaReVkycPriorityApiKey"
	// KarzaSecretsKey Common json based secrets for karza
	KarzaSecretsKey = "KarzaSecrets"

	// FITTT Cricket api and project key
	RounazCricketProjectKey = "RounazCricketProjectKey"
	RounazCricketApiKey     = "RounazCricketApiKey"

	RounazFootballAccessKey = "RounazFootballAccessKey"
	RounazFootballSecretKey = "RounazFootballSecretKey"

	// IPStack Access Key
	IPStackAccessKey = "IpstackAccessKey"

	// aa client api key
	AAClientApiKey  = "AaClientApiKey"
	AAVgSecretsV1   = "AaVgSecretsV1"
	AAVgVnSecretsV1 = "AaVgVnSecretsV1"
	AAVgSecretsV2   = "AaVgSecretsV2"
	AAVgVnSecretsV2 = "AaVgVnSecretsV2"

	// Sahamati Client Id and Secret
	SahamatiClientId     = "SahamatiClientId"
	SahamatiClientSecret = "sahamaticlientsecret"

	// shipway user name and password
	ShipwayUsername = "ShipwayUsername"
	ShipwayPassword = "ShipwayPassword"

	// experian credit report secrets
	ExperianCreditReportPresenceClientName        = "ExperianCreditReportPresenceClientName"
	ExperianCreditReportFetchClientName           = "ExperianCreditReportFetchClientName"
	ExperianCreditReportForExistingUserClientName = "ExperianCreditReportForExistingUserClientName"
	ExperianExtendSubscriptionClientName          = "ExperianExtendSubscriptionClientName"
	ExperianVoucherCode                           = "ExperianVoucherCode"

	// Manch secrets
	ManchSecureKey   = "ManchSecureKey"
	ManchTemplateKey = "ManchTemplateKey"

	// Wealth Karza secrets
	WealthKarzaApiKey = "WealthKarzaApiKey"

	// cvl secrets
	CvlSftpDownloadUser = "CvlSftpUser"
	CvlSftpDownloadPass = "CvlSftpPass"
	CvlSftpUploadUser   = "CvlSftpUploadUser"
	CvlSftpUploadPass   = "CvlSftpUploadPass"
	CvlKraPassKey       = "CvlKraPassKey"
	CvlKraPosCode       = "CvlKraPosCode"
	CvlKraUserName      = "CvlKraUserName"
	CvlKraPassword      = "CvlKraPassword"
	CvlSftpSshKey       = "CvlSftpSshKey"
	cvlSecretsKey       = "CvlSecrets"

	// nsdl secrets
	NsdlUserId = "NsdlUserId"

	// digio secrets
	DigioClientId  = "DigioClientId"
	DigioSecretKey = "DigioSecretKey"

	// ckyc
	_CkycFiCode = "CkycFiCode"

	SeonApiKeyId = "SeonClientApiKey"

	// digilocker secrets
	DigilockerClientSecret = "DigilockerClientSecret"

	// liquiloans
	_liquiloansSecretsKey = "LiquiloansSecrets"

	// lending
	_preApprovedLoanFederalSecrets = "PreApprovedLoanFederalSecrets"
	FederalSftpSshKey              = "FederalSftpSshKey"
	PreApprovedLoanSecrets         = "PreApprovedLoanSecrets"
	// Leegality
	LeegalitySecret = "LeegalitySecret"

	// karza secrets
	KarzaKey = "KarzaKey"

	// tartan secrets
	TartanKey = "TartanKey"

	// dronaPay secrets
	DronaPayKey = "DronaPayKey"

	// Google secrets
	GeolocationKey = "GeolocationKey"

	// Maxmind vendor secrets
	maxmindSecrets = "MaxmindSecrets"

	PAYUToken  = "PayuToken"
	PAYUApiKey = "PayuApiKey"

	bureauSecrets = "BureauSecrets"

	// signzy secrets
	signzySecrets = "SignzySecrets"

	// M2P Register Customer authorization header secrets
	M2pSecrets = "M2PSecrets"

	// M2P Secured card secrets
	M2PSecuredCardSecrets = "M2PSecuredCardSecrets"

	// M2P mass unsecured secrets
	M2PMassUnsecuredCardSecrets = "M2PMassUnsecuredCardSecrets"

	// M2P Rsa public key for CC
	EpifiM2pRsaPublicKey = "EpifiM2pRsaPublicKey"

	// Epifi Rsa private key for sending cc requests to m2p.
	// At the time of rotation, this will hold the older key
	EpifiM2pRsaPrivateKey = "EpifiM2pRsaPrivateKey"

	// More recent private key for secure exchange of m2p requests.
	// At the time of rotation, this will hold the newly created private key
	EpifiM2pRsaPrivateKeyV2 = "EpifiM2pRsaPrivateKeyV2"

	// InHouse BRE bearer
	InHouseBreBearer = "InHouseBreBearer"

	// Alpaca secrets
	AlpacaSecrets = "AlpacaSecrets"

	// profile validation secrets
	federalProfileValidationSecrets = "FederalProfileValidationSecrets"

	FederalInternationalFundTransferSecrets = "FederalInternationalFundTransferSecrets"

	// MorningStar Secrets
	MorningStarSecrets        = "MorningStarSecrets"
	MorningStarAccountSecrets = "MorningStarAccountSecrets"

	P2PInvestmentLiquiloansSftpUser     = "p2pInvestmentLiquiloansSftpUser"
	P2PInvestmentLiquiloansSftpPassword = "p2pInvestmentLiquiloansSftpPassword"
	P2PInvestmentLiquiloansSftpSshKey   = "p2pInvestmentLiquiloansSftpSshKey"

	// deposits secrets
	DepositInterestRateInfoSecrets = "DepositInterestRateInfoSecrets"

	// API token for TSS vendor
	TssApiToken      = "TssApiToken"
	TSSAPITokenForSG = "TSSAPITokenForSG"
	// TODO: Check if SG and EpifiTech credentials can be stored in one AWS SM secret
	TSSCloudCredentials = "TSSCloudCredentials"

	// Vistara Secrets
	VistaraSecrets = "VistaraSecrets"

	// IDFC preapproved loans secrets
	FiIdfcPreApprovedLoanPrivateKey = "FiIdfcPreApprovedLoanPrivateKey"

	// ABFL preapproved loans secrets
	FiAbflPreApprovedLoanUsername = "fiAbflpreapprovedloanusername"
	FiAbflPreApprovedLoanPassword = "fiAbflpreapprovedloanpassword"

	// Slack Bot secrests
	SlackSecrets = "SlackSecrets"

	// Fennel Feature Store Secrets
	FennelFeatureStoreSecrets = "FennelFeatureStoreSecrets"

	// VKYC agent dashboard secrets
	VKYCAgentDashboardSecrets = "VKYCAgentDashboardSecrets"

	// Credgenics authentication key
	CredgenicsAuthenticationKey = "CredgenicsAuthToken"

	LeadSquaredSecrets = "LeadSquaredSecrets"

	FederalDepositSecrets = "FederalDepositSecrets"

	CommonSftpUser = "CommonSftpUser"
	CommonSftpPass = "CommonSftpPass"

	// Lentra secrets
	LentraSecretsKey = "LentraSecrets"

	EpifiFederalEnachSftpSecrets = "EpifiFederalEnachSftpSecrets"

	// MF central secrets for LAMF
	LendingMFCentralSecrets = "LendingMFCentralSecrets"

	LendingFiftyFinLamfSecrets = "LendingFiftyFinLamfSecrets"

	KarzaPanProfileKey = "KarzaPanProfileKey"

	CibilSecretsKey = "CibilSecrets"

	ExperianSecretsKey = "ExperianSecrets"

	EpifiFederalEpanSftpSecrets = "EpifiFederalEpanSftpSecrets"

	RazorpaySecretsKey = "RazorpaySecrets"

	FinfluxSecretsKey = "finfluxsecrets"

	AclSftpSecretKey = "AclSftpSecretKey"

	PanValidationSecretskey = "PanValidationSecretskey"

	OnsuritySecrets = "OnsuritySecrets"

	// bureau.id secrets
	bureauIdSecrets = "BureauIdSecrets"

	digitapSecrets = "DigitapSecrets"

	visaSecretsKey = "VisaSecrets"

	moEngageSecrets = "MoEngageSecrets"

	scienapticSecrets = "ScienapticSecrets"

	perfiosDigilockerSecrets = "PerfiosDigilockerSecrets"

	// CredgenicsAuthenticationKeyV2 secrets v2 version to contain the secrets for both the accounts we have(Epifi, SG)
	CredgenicsAuthenticationKeyV2 = "CredgenicsAuthenticationKeyV2"

	BridgewiseSecrets = "BridgewiseSecrets"

	SavenSecretsKey = "SavenSecrets"

	FederalEscalationSecrets       = "FederalEscalationSecrets"
	FederalEscalationClientSslCert = "FederalEscalationClientSslCert"
	FederalEscalationClientSslKey  = "FederalEscalationClientSslKey"

	SetuBillPaySecrets        = "SetuBillPaySecrets"
	SetuMobileRechargeSecrets = "SetuMobileRechargeSecrets"

	NuggetSecretsKey = "NuggetSecrets"
)
