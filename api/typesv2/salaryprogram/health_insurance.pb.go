//go:generate gen_sql -typesv2=HealthInsurancePolicyType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/salaryprogram/health_insurance.proto

package salaryprogram

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// policy typesv2 of the health insurance issued to salary users
type HealthInsurancePolicyType int32

const (
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED                HealthInsurancePolicyType = 0
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_BASE_HEALTH_INSURANCE      HealthInsurancePolicyType = 1
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_SUPER_TOP_UP               HealthInsurancePolicyType = 2
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_2A2C HealthInsurancePolicyType = 3
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_2A   HealthInsurancePolicyType = 4
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_1A   HealthInsurancePolicyType = 5
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_DIAMOND_PLUS_1A   HealthInsurancePolicyType = 6
	// GPA - Group Personal Accident insurance
	// GHI - Group Health Insurance
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_GHI_GPA_RUBY_1A HealthInsurancePolicyType = 7
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_GHI_GPA_OPAL_1A HealthInsurancePolicyType = 8
)

// Enum value maps for HealthInsurancePolicyType.
var (
	HealthInsurancePolicyType_name = map[int32]string{
		0: "HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED",
		1: "HEALTH_INSURANCE_POLICY_TYPE_BASE_HEALTH_INSURANCE",
		2: "HEALTH_INSURANCE_POLICY_TYPE_SUPER_TOP_UP",
		3: "HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_2A2C",
		4: "HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_2A",
		5: "HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_1A",
		6: "HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_DIAMOND_PLUS_1A",
		7: "HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_GHI_GPA_RUBY_1A",
		8: "HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_GHI_GPA_OPAL_1A",
	}
	HealthInsurancePolicyType_value = map[string]int32{
		"HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED":                0,
		"HEALTH_INSURANCE_POLICY_TYPE_BASE_HEALTH_INSURANCE":      1,
		"HEALTH_INSURANCE_POLICY_TYPE_SUPER_TOP_UP":               2,
		"HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_2A2C": 3,
		"HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_2A":   4,
		"HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_1A":   5,
		"HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_DIAMOND_PLUS_1A":   6,
		"HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_GHI_GPA_RUBY_1A":   7,
		"HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_GHI_GPA_OPAL_1A":   8,
	}
)

func (x HealthInsurancePolicyType) Enum() *HealthInsurancePolicyType {
	p := new(HealthInsurancePolicyType)
	*p = x
	return p
}

func (x HealthInsurancePolicyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HealthInsurancePolicyType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_salaryprogram_health_insurance_proto_enumTypes[0].Descriptor()
}

func (HealthInsurancePolicyType) Type() protoreflect.EnumType {
	return &file_api_typesv2_salaryprogram_health_insurance_proto_enumTypes[0]
}

func (x HealthInsurancePolicyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HealthInsurancePolicyType.Descriptor instead.
func (HealthInsurancePolicyType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_salaryprogram_health_insurance_proto_rawDescGZIP(), []int{0}
}

var File_api_typesv2_salaryprogram_health_insurance_proto protoreflect.FileDescriptor

var file_api_typesv2_salaryprogram_health_insurance_proto_rawDesc = []byte{
	0x0a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x5f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x16, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2a, 0x94, 0x04, 0x0a, 0x19, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x28, 0x48, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4c,
	0x49, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x36, 0x0a, 0x32, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48,
	0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x48, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x2d,
	0x0a, 0x29, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x55, 0x50, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x55, 0x50, 0x10, 0x02, 0x12, 0x3b, 0x0a,
	0x37, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43,
	0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4e,
	0x53, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x50, 0x44, 0x5f, 0x57, 0x45, 0x4c, 0x4c, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x32, 0x41, 0x32, 0x43, 0x10, 0x03, 0x12, 0x39, 0x0a, 0x35, 0x48, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50,
	0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4e, 0x53, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x4f, 0x50, 0x44, 0x5f, 0x57, 0x45, 0x4c, 0x4c, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x32, 0x41, 0x10, 0x04, 0x12, 0x39, 0x0a, 0x35, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4e, 0x53, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4f,
	0x50, 0x44, 0x5f, 0x57, 0x45, 0x4c, 0x4c, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x31, 0x41, 0x10, 0x05,
	0x12, 0x39, 0x0a, 0x35, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4f, 0x4e, 0x53, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x49, 0x41, 0x4d, 0x4f, 0x4e,
	0x44, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x31, 0x41, 0x10, 0x06, 0x12, 0x39, 0x0a, 0x35, 0x48,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4e, 0x53, 0x55,
	0x52, 0x49, 0x54, 0x59, 0x5f, 0x47, 0x48, 0x49, 0x5f, 0x47, 0x50, 0x41, 0x5f, 0x52, 0x55, 0x42,
	0x59, 0x5f, 0x31, 0x41, 0x10, 0x07, 0x12, 0x39, 0x0a, 0x35, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48,
	0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x4e, 0x53, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x47, 0x48, 0x49, 0x5f, 0x47, 0x50, 0x41, 0x5f, 0x4f, 0x50, 0x41, 0x4c, 0x5f, 0x31, 0x41, 0x10,
	0x08, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_salaryprogram_health_insurance_proto_rawDescOnce sync.Once
	file_api_typesv2_salaryprogram_health_insurance_proto_rawDescData = file_api_typesv2_salaryprogram_health_insurance_proto_rawDesc
)

func file_api_typesv2_salaryprogram_health_insurance_proto_rawDescGZIP() []byte {
	file_api_typesv2_salaryprogram_health_insurance_proto_rawDescOnce.Do(func() {
		file_api_typesv2_salaryprogram_health_insurance_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_salaryprogram_health_insurance_proto_rawDescData)
	})
	return file_api_typesv2_salaryprogram_health_insurance_proto_rawDescData
}

var file_api_typesv2_salaryprogram_health_insurance_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_salaryprogram_health_insurance_proto_goTypes = []interface{}{
	(HealthInsurancePolicyType)(0), // 0: api.typesv2.employment.HealthInsurancePolicyType
}
var file_api_typesv2_salaryprogram_health_insurance_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_typesv2_salaryprogram_health_insurance_proto_init() }
func file_api_typesv2_salaryprogram_health_insurance_proto_init() {
	if File_api_typesv2_salaryprogram_health_insurance_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_salaryprogram_health_insurance_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_salaryprogram_health_insurance_proto_goTypes,
		DependencyIndexes: file_api_typesv2_salaryprogram_health_insurance_proto_depIdxs,
		EnumInfos:         file_api_typesv2_salaryprogram_health_insurance_proto_enumTypes,
	}.Build()
	File_api_typesv2_salaryprogram_health_insurance_proto = out.File
	file_api_typesv2_salaryprogram_health_insurance_proto_rawDesc = nil
	file_api_typesv2_salaryprogram_health_insurance_proto_goTypes = nil
	file_api_typesv2_salaryprogram_health_insurance_proto_depIdxs = nil
}
