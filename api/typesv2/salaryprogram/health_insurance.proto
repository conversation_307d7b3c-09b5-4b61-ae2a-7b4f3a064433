//go:generate gen_sql -typesv2=HealthInsurancePolicyType
syntax = "proto3";

package api.typesv2.employment;


option go_package = "github.com/epifi/gamma/api/typesv2/salaryprogram";
option java_package = "com.github.epifi.gamma.api.typesv2.salaryprogram";

// policy typesv2 of the health insurance issued to salary users
// update enums in api/salaryprogram/healthinsurance/policy_issuance_request.proto
enum HealthInsurancePolicyType {
  HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED = 0;
  HEALTH_INSURANCE_POLICY_TYPE_BASE_HEALTH_INSURANCE = 1;
  HEALTH_INSURANCE_POLICY_TYPE_SUPER_TOP_UP = 2;
  HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_2A2C = 3;
  HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_2A = 4;
  HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_OPD_WELLNESS_1A = 5;
  HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_DIAMOND_PLUS_1A = 6;
  // GPA - Group Personal Accident insurance
  // GHI - Group Health Insurance
  HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_GHI_GPA_RUBY_1A = 7;
  HEALTH_INSURANCE_POLICY_TYPE_ONSURITY_GHI_GPA_OPAL_1A = 8;
}
