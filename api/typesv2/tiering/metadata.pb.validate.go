// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/tiering/metadata.proto

package tiering

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TieringScreenMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TieringScreenMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TieringScreenMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TieringScreenMetaDataMultiError, or nil if none found.
func (m *TieringScreenMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringScreenMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Metadata.(type) {
	case *TieringScreenMetaData_NotificationLandingScreenMetadata:
		if v == nil {
			err := TieringScreenMetaDataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNotificationLandingScreenMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "NotificationLandingScreenMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "NotificationLandingScreenMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNotificationLandingScreenMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringScreenMetaDataValidationError{
					field:  "NotificationLandingScreenMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TieringScreenMetaData_DetailedBenefitsScreenMetadata:
		if v == nil {
			err := TieringScreenMetaDataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDetailedBenefitsScreenMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "DetailedBenefitsScreenMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "DetailedBenefitsScreenMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDetailedBenefitsScreenMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringScreenMetaDataValidationError{
					field:  "DetailedBenefitsScreenMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TieringScreenMetaData_CashbackDetailsBottomSheet:
		if v == nil {
			err := TieringScreenMetaDataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCashbackDetailsBottomSheet()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "CashbackDetailsBottomSheet",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "CashbackDetailsBottomSheet",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCashbackDetailsBottomSheet()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringScreenMetaDataValidationError{
					field:  "CashbackDetailsBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TieringScreenMetaData_WaysToUseFiPointsBottomSheet:
		if v == nil {
			err := TieringScreenMetaDataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWaysToUseFiPointsBottomSheet()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "WaysToUseFiPointsBottomSheet",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "WaysToUseFiPointsBottomSheet",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWaysToUseFiPointsBottomSheet()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringScreenMetaDataValidationError{
					field:  "WaysToUseFiPointsBottomSheet",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TieringScreenMetaData_PlansV2Metadata:
		if v == nil {
			err := TieringScreenMetaDataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPlansV2Metadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "PlansV2Metadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "PlansV2Metadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPlansV2Metadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringScreenMetaDataValidationError{
					field:  "PlansV2Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TieringScreenMetaData_SuccessV2Metadata:
		if v == nil {
			err := TieringScreenMetaDataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSuccessV2Metadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "SuccessV2Metadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "SuccessV2Metadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSuccessV2Metadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringScreenMetaDataValidationError{
					field:  "SuccessV2Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TieringScreenMetaData_TierManualUpgradeScreenMetadata:
		if v == nil {
			err := TieringScreenMetaDataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTierManualUpgradeScreenMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "TierManualUpgradeScreenMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "TierManualUpgradeScreenMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTierManualUpgradeScreenMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringScreenMetaDataValidationError{
					field:  "TierManualUpgradeScreenMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TieringScreenMetaData_TrialDeeplinkMetadata:
		if v == nil {
			err := TieringScreenMetaDataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTrialDeeplinkMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "TrialDeeplinkMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TieringScreenMetaDataValidationError{
						field:  "TrialDeeplinkMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTrialDeeplinkMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TieringScreenMetaDataValidationError{
					field:  "TrialDeeplinkMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return TieringScreenMetaDataMultiError(errors)
	}

	return nil
}

// TieringScreenMetaDataMultiError is an error wrapping multiple validation
// errors returned by TieringScreenMetaData.ValidateAll() if the designated
// constraints aren't met.
type TieringScreenMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringScreenMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringScreenMetaDataMultiError) AllErrors() []error { return m }

// TieringScreenMetaDataValidationError is the validation error returned by
// TieringScreenMetaData.Validate if the designated constraints aren't met.
type TieringScreenMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringScreenMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringScreenMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringScreenMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringScreenMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringScreenMetaDataValidationError) ErrorName() string {
	return "TieringScreenMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e TieringScreenMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringScreenMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringScreenMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringScreenMetaDataValidationError{}

// Validate checks the field values on TrialDeeplinkMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TrialDeeplinkMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TrialDeeplinkMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TrialDeeplinkMetadataMultiError, or nil if none found.
func (m *TrialDeeplinkMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *TrialDeeplinkMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TrialDeeplinkMetadataMultiError(errors)
	}

	return nil
}

// TrialDeeplinkMetadataMultiError is an error wrapping multiple validation
// errors returned by TrialDeeplinkMetadata.ValidateAll() if the designated
// constraints aren't met.
type TrialDeeplinkMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TrialDeeplinkMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TrialDeeplinkMetadataMultiError) AllErrors() []error { return m }

// TrialDeeplinkMetadataValidationError is the validation error returned by
// TrialDeeplinkMetadata.Validate if the designated constraints aren't met.
type TrialDeeplinkMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TrialDeeplinkMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TrialDeeplinkMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TrialDeeplinkMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TrialDeeplinkMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TrialDeeplinkMetadataValidationError) ErrorName() string {
	return "TrialDeeplinkMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e TrialDeeplinkMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTrialDeeplinkMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TrialDeeplinkMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TrialDeeplinkMetadataValidationError{}

// Validate checks the field values on TierManualUpgradeScreenMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TierManualUpgradeScreenMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TierManualUpgradeScreenMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TierManualUpgradeScreenMetadataMultiError, or nil if none found.
func (m *TierManualUpgradeScreenMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *TierManualUpgradeScreenMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Provenance

	if len(errors) > 0 {
		return TierManualUpgradeScreenMetadataMultiError(errors)
	}

	return nil
}

// TierManualUpgradeScreenMetadataMultiError is an error wrapping multiple
// validation errors returned by TierManualUpgradeScreenMetadata.ValidateAll()
// if the designated constraints aren't met.
type TierManualUpgradeScreenMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TierManualUpgradeScreenMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TierManualUpgradeScreenMetadataMultiError) AllErrors() []error { return m }

// TierManualUpgradeScreenMetadataValidationError is the validation error
// returned by TierManualUpgradeScreenMetadata.Validate if the designated
// constraints aren't met.
type TierManualUpgradeScreenMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TierManualUpgradeScreenMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TierManualUpgradeScreenMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TierManualUpgradeScreenMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TierManualUpgradeScreenMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TierManualUpgradeScreenMetadataValidationError) ErrorName() string {
	return "TierManualUpgradeScreenMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e TierManualUpgradeScreenMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTierManualUpgradeScreenMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TierManualUpgradeScreenMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TierManualUpgradeScreenMetadataValidationError{}

// Validate checks the field values on SuccessV2MetaData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SuccessV2MetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SuccessV2MetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SuccessV2MetaDataMultiError, or nil if none found.
func (m *SuccessV2MetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *SuccessV2MetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tier

	if len(errors) > 0 {
		return SuccessV2MetaDataMultiError(errors)
	}

	return nil
}

// SuccessV2MetaDataMultiError is an error wrapping multiple validation errors
// returned by SuccessV2MetaData.ValidateAll() if the designated constraints
// aren't met.
type SuccessV2MetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SuccessV2MetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SuccessV2MetaDataMultiError) AllErrors() []error { return m }

// SuccessV2MetaDataValidationError is the validation error returned by
// SuccessV2MetaData.Validate if the designated constraints aren't met.
type SuccessV2MetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SuccessV2MetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SuccessV2MetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SuccessV2MetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SuccessV2MetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SuccessV2MetaDataValidationError) ErrorName() string {
	return "SuccessV2MetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e SuccessV2MetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSuccessV2MetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SuccessV2MetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SuccessV2MetaDataValidationError{}

// Validate checks the field values on PlansV2Metadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PlansV2Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlansV2Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlansV2MetadataMultiError, or nil if none found.
func (m *PlansV2Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *PlansV2Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TierToFocus

	if len(errors) > 0 {
		return PlansV2MetadataMultiError(errors)
	}

	return nil
}

// PlansV2MetadataMultiError is an error wrapping multiple validation errors
// returned by PlansV2Metadata.ValidateAll() if the designated constraints
// aren't met.
type PlansV2MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlansV2MetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlansV2MetadataMultiError) AllErrors() []error { return m }

// PlansV2MetadataValidationError is the validation error returned by
// PlansV2Metadata.Validate if the designated constraints aren't met.
type PlansV2MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlansV2MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlansV2MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlansV2MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlansV2MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlansV2MetadataValidationError) ErrorName() string { return "PlansV2MetadataValidationError" }

// Error satisfies the builtin error interface
func (e PlansV2MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlansV2Metadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlansV2MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlansV2MetadataValidationError{}

// Validate checks the field values on NotificationLandingScreenMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *NotificationLandingScreenMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NotificationLandingScreenMetadata
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// NotificationLandingScreenMetadataMultiError, or nil if none found.
func (m *NotificationLandingScreenMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *NotificationLandingScreenMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tier

	// no validation rules for CriteriaOption

	if len(errors) > 0 {
		return NotificationLandingScreenMetadataMultiError(errors)
	}

	return nil
}

// NotificationLandingScreenMetadataMultiError is an error wrapping multiple
// validation errors returned by
// NotificationLandingScreenMetadata.ValidateAll() if the designated
// constraints aren't met.
type NotificationLandingScreenMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NotificationLandingScreenMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NotificationLandingScreenMetadataMultiError) AllErrors() []error { return m }

// NotificationLandingScreenMetadataValidationError is the validation error
// returned by NotificationLandingScreenMetadata.Validate if the designated
// constraints aren't met.
type NotificationLandingScreenMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NotificationLandingScreenMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NotificationLandingScreenMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NotificationLandingScreenMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NotificationLandingScreenMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NotificationLandingScreenMetadataValidationError) ErrorName() string {
	return "NotificationLandingScreenMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e NotificationLandingScreenMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNotificationLandingScreenMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NotificationLandingScreenMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NotificationLandingScreenMetadataValidationError{}

// Validate checks the field values on DetailedBenefitsScreenMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetailedBenefitsScreenMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailedBenefitsScreenMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DetailedBenefitsScreenMetadataMultiError, or nil if none found.
func (m *DetailedBenefitsScreenMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailedBenefitsScreenMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tier

	if len(errors) > 0 {
		return DetailedBenefitsScreenMetadataMultiError(errors)
	}

	return nil
}

// DetailedBenefitsScreenMetadataMultiError is an error wrapping multiple
// validation errors returned by DetailedBenefitsScreenMetadata.ValidateAll()
// if the designated constraints aren't met.
type DetailedBenefitsScreenMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailedBenefitsScreenMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailedBenefitsScreenMetadataMultiError) AllErrors() []error { return m }

// DetailedBenefitsScreenMetadataValidationError is the validation error
// returned by DetailedBenefitsScreenMetadata.Validate if the designated
// constraints aren't met.
type DetailedBenefitsScreenMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailedBenefitsScreenMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailedBenefitsScreenMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailedBenefitsScreenMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailedBenefitsScreenMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailedBenefitsScreenMetadataValidationError) ErrorName() string {
	return "DetailedBenefitsScreenMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e DetailedBenefitsScreenMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailedBenefitsScreenMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailedBenefitsScreenMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailedBenefitsScreenMetadataValidationError{}

// Validate checks the field values on CashbackDetailsBottomSheet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CashbackDetailsBottomSheet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CashbackDetailsBottomSheet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CashbackDetailsBottomSheetMultiError, or nil if none found.
func (m *CashbackDetailsBottomSheet) ValidateAll() error {
	return m.validate(true)
}

func (m *CashbackDetailsBottomSheet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tier

	if len(errors) > 0 {
		return CashbackDetailsBottomSheetMultiError(errors)
	}

	return nil
}

// CashbackDetailsBottomSheetMultiError is an error wrapping multiple
// validation errors returned by CashbackDetailsBottomSheet.ValidateAll() if
// the designated constraints aren't met.
type CashbackDetailsBottomSheetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CashbackDetailsBottomSheetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CashbackDetailsBottomSheetMultiError) AllErrors() []error { return m }

// CashbackDetailsBottomSheetValidationError is the validation error returned
// by CashbackDetailsBottomSheet.Validate if the designated constraints aren't met.
type CashbackDetailsBottomSheetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CashbackDetailsBottomSheetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CashbackDetailsBottomSheetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CashbackDetailsBottomSheetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CashbackDetailsBottomSheetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CashbackDetailsBottomSheetValidationError) ErrorName() string {
	return "CashbackDetailsBottomSheetValidationError"
}

// Error satisfies the builtin error interface
func (e CashbackDetailsBottomSheetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCashbackDetailsBottomSheet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CashbackDetailsBottomSheetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CashbackDetailsBottomSheetValidationError{}

// Validate checks the field values on WaysToUseFiPointsBottomSheet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WaysToUseFiPointsBottomSheet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WaysToUseFiPointsBottomSheet with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WaysToUseFiPointsBottomSheetMultiError, or nil if none found.
func (m *WaysToUseFiPointsBottomSheet) ValidateAll() error {
	return m.validate(true)
}

func (m *WaysToUseFiPointsBottomSheet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tier

	if len(errors) > 0 {
		return WaysToUseFiPointsBottomSheetMultiError(errors)
	}

	return nil
}

// WaysToUseFiPointsBottomSheetMultiError is an error wrapping multiple
// validation errors returned by WaysToUseFiPointsBottomSheet.ValidateAll() if
// the designated constraints aren't met.
type WaysToUseFiPointsBottomSheetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WaysToUseFiPointsBottomSheetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WaysToUseFiPointsBottomSheetMultiError) AllErrors() []error { return m }

// WaysToUseFiPointsBottomSheetValidationError is the validation error returned
// by WaysToUseFiPointsBottomSheet.Validate if the designated constraints
// aren't met.
type WaysToUseFiPointsBottomSheetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WaysToUseFiPointsBottomSheetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WaysToUseFiPointsBottomSheetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WaysToUseFiPointsBottomSheetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WaysToUseFiPointsBottomSheetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WaysToUseFiPointsBottomSheetValidationError) ErrorName() string {
	return "WaysToUseFiPointsBottomSheetValidationError"
}

// Error satisfies the builtin error interface
func (e WaysToUseFiPointsBottomSheetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWaysToUseFiPointsBottomSheet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WaysToUseFiPointsBottomSheetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WaysToUseFiPointsBottomSheetValidationError{}
