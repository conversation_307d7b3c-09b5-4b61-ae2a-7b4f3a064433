// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/tiering/metadata.proto

package tiering

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TieringScreenMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Metadata:
	//
	//	*TieringScreenMetaData_NotificationLandingScreenMetadata
	//	*TieringScreenMetaData_DetailedBenefitsScreenMetadata
	//	*TieringScreenMetaData_CashbackDetailsBottomSheet
	//	*TieringScreenMetaData_WaysToUseFiPointsBottomSheet
	//	*TieringScreenMetaData_PlansV2Metadata
	//	*TieringScreenMetaData_SuccessV2Metadata
	//	*TieringScreenMetaData_TierManualUpgradeScreenMetadata
	//	*TieringScreenMetaData_TrialDeeplinkMetadata
	Metadata isTieringScreenMetaData_Metadata `protobuf_oneof:"metadata"`
}

func (x *TieringScreenMetaData) Reset() {
	*x = TieringScreenMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TieringScreenMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TieringScreenMetaData) ProtoMessage() {}

func (x *TieringScreenMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TieringScreenMetaData.ProtoReflect.Descriptor instead.
func (*TieringScreenMetaData) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{0}
}

func (m *TieringScreenMetaData) GetMetadata() isTieringScreenMetaData_Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (x *TieringScreenMetaData) GetNotificationLandingScreenMetadata() *NotificationLandingScreenMetadata {
	if x, ok := x.GetMetadata().(*TieringScreenMetaData_NotificationLandingScreenMetadata); ok {
		return x.NotificationLandingScreenMetadata
	}
	return nil
}

func (x *TieringScreenMetaData) GetDetailedBenefitsScreenMetadata() *DetailedBenefitsScreenMetadata {
	if x, ok := x.GetMetadata().(*TieringScreenMetaData_DetailedBenefitsScreenMetadata); ok {
		return x.DetailedBenefitsScreenMetadata
	}
	return nil
}

func (x *TieringScreenMetaData) GetCashbackDetailsBottomSheet() *CashbackDetailsBottomSheet {
	if x, ok := x.GetMetadata().(*TieringScreenMetaData_CashbackDetailsBottomSheet); ok {
		return x.CashbackDetailsBottomSheet
	}
	return nil
}

func (x *TieringScreenMetaData) GetWaysToUseFiPointsBottomSheet() *WaysToUseFiPointsBottomSheet {
	if x, ok := x.GetMetadata().(*TieringScreenMetaData_WaysToUseFiPointsBottomSheet); ok {
		return x.WaysToUseFiPointsBottomSheet
	}
	return nil
}

func (x *TieringScreenMetaData) GetPlansV2Metadata() *PlansV2Metadata {
	if x, ok := x.GetMetadata().(*TieringScreenMetaData_PlansV2Metadata); ok {
		return x.PlansV2Metadata
	}
	return nil
}

func (x *TieringScreenMetaData) GetSuccessV2Metadata() *SuccessV2MetaData {
	if x, ok := x.GetMetadata().(*TieringScreenMetaData_SuccessV2Metadata); ok {
		return x.SuccessV2Metadata
	}
	return nil
}

func (x *TieringScreenMetaData) GetTierManualUpgradeScreenMetadata() *TierManualUpgradeScreenMetadata {
	if x, ok := x.GetMetadata().(*TieringScreenMetaData_TierManualUpgradeScreenMetadata); ok {
		return x.TierManualUpgradeScreenMetadata
	}
	return nil
}

func (x *TieringScreenMetaData) GetTrialDeeplinkMetadata() *TrialDeeplinkMetadata {
	if x, ok := x.GetMetadata().(*TieringScreenMetaData_TrialDeeplinkMetadata); ok {
		return x.TrialDeeplinkMetadata
	}
	return nil
}

type isTieringScreenMetaData_Metadata interface {
	isTieringScreenMetaData_Metadata()
}

type TieringScreenMetaData_NotificationLandingScreenMetadata struct {
	NotificationLandingScreenMetadata *NotificationLandingScreenMetadata `protobuf:"bytes,1,opt,name=notification_landing_screen_metadata,json=notificationLandingScreenMetadata,proto3,oneof"`
}

type TieringScreenMetaData_DetailedBenefitsScreenMetadata struct {
	DetailedBenefitsScreenMetadata *DetailedBenefitsScreenMetadata `protobuf:"bytes,2,opt,name=detailed_benefits_screen_metadata,json=detailedBenefitsScreenMetadata,proto3,oneof"`
}

type TieringScreenMetaData_CashbackDetailsBottomSheet struct {
	CashbackDetailsBottomSheet *CashbackDetailsBottomSheet `protobuf:"bytes,3,opt,name=cashback_details_bottom_sheet,json=cashbackDetailsBottomSheet,proto3,oneof"`
}

type TieringScreenMetaData_WaysToUseFiPointsBottomSheet struct {
	WaysToUseFiPointsBottomSheet *WaysToUseFiPointsBottomSheet `protobuf:"bytes,4,opt,name=ways_to_use_fi_points_bottom_sheet,json=waysToUseFiPointsBottomSheet,proto3,oneof"`
}

type TieringScreenMetaData_PlansV2Metadata struct {
	PlansV2Metadata *PlansV2Metadata `protobuf:"bytes,5,opt,name=plans_v2_metadata,json=plansV2Metadata,proto3,oneof"`
}

type TieringScreenMetaData_SuccessV2Metadata struct {
	SuccessV2Metadata *SuccessV2MetaData `protobuf:"bytes,6,opt,name=success_v2_metadata,json=successV2Metadata,proto3,oneof"`
}

type TieringScreenMetaData_TierManualUpgradeScreenMetadata struct {
	TierManualUpgradeScreenMetadata *TierManualUpgradeScreenMetadata `protobuf:"bytes,7,opt,name=tier_manual_upgrade_screen_metadata,json=tierManualUpgradeScreenMetadata,proto3,oneof"`
}

type TieringScreenMetaData_TrialDeeplinkMetadata struct {
	TrialDeeplinkMetadata *TrialDeeplinkMetadata `protobuf:"bytes,8,opt,name=trial_deeplink_metadata,json=trialDeeplinkMetadata,proto3,oneof"`
}

func (*TieringScreenMetaData_NotificationLandingScreenMetadata) isTieringScreenMetaData_Metadata() {}

func (*TieringScreenMetaData_DetailedBenefitsScreenMetadata) isTieringScreenMetaData_Metadata() {}

func (*TieringScreenMetaData_CashbackDetailsBottomSheet) isTieringScreenMetaData_Metadata() {}

func (*TieringScreenMetaData_WaysToUseFiPointsBottomSheet) isTieringScreenMetaData_Metadata() {}

func (*TieringScreenMetaData_PlansV2Metadata) isTieringScreenMetaData_Metadata() {}

func (*TieringScreenMetaData_SuccessV2Metadata) isTieringScreenMetaData_Metadata() {}

func (*TieringScreenMetaData_TierManualUpgradeScreenMetadata) isTieringScreenMetaData_Metadata() {}

func (*TieringScreenMetaData_TrialDeeplinkMetadata) isTieringScreenMetaData_Metadata() {}

// Metadata to invoke GetTrialDetails RPC and get trials deeplink
type TrialDeeplinkMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TrialDeeplinkMetadata) Reset() {
	*x = TrialDeeplinkMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrialDeeplinkMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrialDeeplinkMetadata) ProtoMessage() {}

func (x *TrialDeeplinkMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrialDeeplinkMetadata.ProtoReflect.Descriptor instead.
func (*TrialDeeplinkMetadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{1}
}

// Metadata to invoke frontend.tiering.Upgrade RPC
type TierManualUpgradeScreenMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tiering.enums.Provenance.String()
	// optional parameter
	// if empty, we will use PROVENANCE_FREE_UPGRADE as default provenance
	Provenance string `protobuf:"bytes,1,opt,name=provenance,proto3" json:"provenance,omitempty"`
}

func (x *TierManualUpgradeScreenMetadata) Reset() {
	*x = TierManualUpgradeScreenMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TierManualUpgradeScreenMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TierManualUpgradeScreenMetadata) ProtoMessage() {}

func (x *TierManualUpgradeScreenMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TierManualUpgradeScreenMetadata.ProtoReflect.Descriptor instead.
func (*TierManualUpgradeScreenMetadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{2}
}

func (x *TierManualUpgradeScreenMetadata) GetProvenance() string {
	if x != nil {
		return x.Provenance
	}
	return ""
}

// Metadata to mark, which plan success screen got displayed
type SuccessV2MetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tiering.external.Tier.String()
	Tier string `protobuf:"bytes,1,opt,name=tier,proto3" json:"tier,omitempty"`
}

func (x *SuccessV2MetaData) Reset() {
	*x = SuccessV2MetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuccessV2MetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuccessV2MetaData) ProtoMessage() {}

func (x *SuccessV2MetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuccessV2MetaData.ProtoReflect.Descriptor instead.
func (*SuccessV2MetaData) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{3}
}

func (x *SuccessV2MetaData) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

type PlansV2Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TierToFocus string `protobuf:"bytes,1,opt,name=tier_to_focus,json=tierToFocus,proto3" json:"tier_to_focus,omitempty"`
}

func (x *PlansV2Metadata) Reset() {
	*x = PlansV2Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlansV2Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlansV2Metadata) ProtoMessage() {}

func (x *PlansV2Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlansV2Metadata.ProtoReflect.Descriptor instead.
func (*PlansV2Metadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{4}
}

func (x *PlansV2Metadata) GetTierToFocus() string {
	if x != nil {
		return x.TierToFocus
	}
	return ""
}

// metadata will have the tier and the criteria option to pitch to user for upgrading
// eg: https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10175-83805&t=f2oABQDtEgTdOtwz-4
type NotificationLandingScreenMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tiering.external.Tier.String()
	Tier string `protobuf:"bytes,1,opt,name=tier,proto3" json:"tier,omitempty"`
	// tiering.enums.CriteriaOptionType.String()
	CriteriaOption string `protobuf:"bytes,2,opt,name=criteria_option,json=criteriaOption,proto3" json:"criteria_option,omitempty"`
}

func (x *NotificationLandingScreenMetadata) Reset() {
	*x = NotificationLandingScreenMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationLandingScreenMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationLandingScreenMetadata) ProtoMessage() {}

func (x *NotificationLandingScreenMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationLandingScreenMetadata.ProtoReflect.Descriptor instead.
func (*NotificationLandingScreenMetadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{5}
}

func (x *NotificationLandingScreenMetadata) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *NotificationLandingScreenMetadata) GetCriteriaOption() string {
	if x != nil {
		return x.CriteriaOption
	}
	return ""
}

// bottom sheet for detailed benefits which will be displayed on the tier all plans v2 screen
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10043-11580&t=CutvBGxUGPg2XuAk-4
type DetailedBenefitsScreenMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tiering.external.Tier.String()
	Tier string `protobuf:"bytes,1,opt,name=tier,proto3" json:"tier,omitempty"`
}

func (x *DetailedBenefitsScreenMetadata) Reset() {
	*x = DetailedBenefitsScreenMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailedBenefitsScreenMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedBenefitsScreenMetadata) ProtoMessage() {}

func (x *DetailedBenefitsScreenMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedBenefitsScreenMetadata.ProtoReflect.Descriptor instead.
func (*DetailedBenefitsScreenMetadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{6}
}

func (x *DetailedBenefitsScreenMetadata) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

// bottom sheet for cashback details which will be displayed on the tier all plans v2 screen
// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=10088-2968&t=CutvBGxUGPg2XuAk-4
type CashbackDetailsBottomSheet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tiering.external.Tier.String()
	Tier string `protobuf:"bytes,1,opt,name=tier,proto3" json:"tier,omitempty"`
}

func (x *CashbackDetailsBottomSheet) Reset() {
	*x = CashbackDetailsBottomSheet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CashbackDetailsBottomSheet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CashbackDetailsBottomSheet) ProtoMessage() {}

func (x *CashbackDetailsBottomSheet) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CashbackDetailsBottomSheet.ProtoReflect.Descriptor instead.
func (*CashbackDetailsBottomSheet) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{7}
}

func (x *CashbackDetailsBottomSheet) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

// bottom sheet for ways to use fi points which will be displayed on the tier all plans v2 screen
type WaysToUseFiPointsBottomSheet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tiering.external.Tier.String()
	Tier string `protobuf:"bytes,1,opt,name=tier,proto3" json:"tier,omitempty"`
}

func (x *WaysToUseFiPointsBottomSheet) Reset() {
	*x = WaysToUseFiPointsBottomSheet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaysToUseFiPointsBottomSheet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaysToUseFiPointsBottomSheet) ProtoMessage() {}

func (x *WaysToUseFiPointsBottomSheet) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_tiering_metadata_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaysToUseFiPointsBottomSheet.ProtoReflect.Descriptor instead.
func (*WaysToUseFiPointsBottomSheet) Descriptor() ([]byte, []int) {
	return file_api_typesv2_tiering_metadata_proto_rawDescGZIP(), []int{8}
}

func (x *WaysToUseFiPointsBottomSheet) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

var File_api_typesv2_tiering_metadata_proto protoreflect.FileDescriptor

var file_api_typesv2_tiering_metadata_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x22, 0xc2, 0x07, 0x0a, 0x15, 0x54, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x89, 0x01, 0x0a, 0x24, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x21, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x80, 0x01, 0x0a, 0x21, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x1e, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x74, 0x0a, 0x1d, 0x63, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42,
	0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x48, 0x00, 0x52, 0x1a, 0x63, 0x61,
	0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x6f, 0x74,
	0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x12, 0x7d, 0x0a, 0x22, 0x77, 0x61, 0x79, 0x73,
	0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x5f, 0x66, 0x69, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x68, 0x65, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x57, 0x61, 0x79, 0x73, 0x54,
	0x6f, 0x55, 0x73, 0x65, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x48, 0x00, 0x52, 0x1c, 0x77, 0x61, 0x79, 0x73, 0x54,
	0x6f, 0x55, 0x73, 0x65, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x12, 0x52, 0x0a, 0x11, 0x70, 0x6c, 0x61, 0x6e, 0x73,
	0x5f, 0x76, 0x32, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x56, 0x32,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0f, 0x70, 0x6c, 0x61, 0x6e,
	0x73, 0x56, 0x32, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x58, 0x0a, 0x13, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x76, 0x32, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x56, 0x32, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x56, 0x32, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x84, 0x01, 0x0a, 0x23, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1f, 0x74, 0x69, 0x65,
	0x72, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x64, 0x0a, 0x17,
	0x74, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x15, 0x74, 0x72, 0x69,
	0x61, 0x6c, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x42, 0x0a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x17,
	0x0a, 0x15, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x41, 0x0a, 0x1f, 0x54, 0x69, 0x65, 0x72, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x27, 0x0a, 0x11, 0x53, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x56, 0x32, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x69, 0x65, 0x72, 0x22, 0x35, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x56, 0x32, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x74,
	0x6f, 0x5f, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74,
	0x69, 0x65, 0x72, 0x54, 0x6f, 0x46, 0x6f, 0x63, 0x75, 0x73, 0x22, 0x60, 0x0a, 0x21, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x69, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x1e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69,
	0x65, 0x72, 0x22, 0x30, 0x0a, 0x1a, 0x43, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x69, 0x65, 0x72, 0x22, 0x32, 0x0a, 0x1c, 0x57, 0x61, 0x79, 0x73, 0x54, 0x6f, 0x55, 0x73,
	0x65, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53,
	0x68, 0x65, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x65, 0x72, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_tiering_metadata_proto_rawDescOnce sync.Once
	file_api_typesv2_tiering_metadata_proto_rawDescData = file_api_typesv2_tiering_metadata_proto_rawDesc
)

func file_api_typesv2_tiering_metadata_proto_rawDescGZIP() []byte {
	file_api_typesv2_tiering_metadata_proto_rawDescOnce.Do(func() {
		file_api_typesv2_tiering_metadata_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_tiering_metadata_proto_rawDescData)
	})
	return file_api_typesv2_tiering_metadata_proto_rawDescData
}

var file_api_typesv2_tiering_metadata_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_typesv2_tiering_metadata_proto_goTypes = []interface{}{
	(*TieringScreenMetaData)(nil),             // 0: api.typesv2.tiering.TieringScreenMetaData
	(*TrialDeeplinkMetadata)(nil),             // 1: api.typesv2.tiering.TrialDeeplinkMetadata
	(*TierManualUpgradeScreenMetadata)(nil),   // 2: api.typesv2.tiering.TierManualUpgradeScreenMetadata
	(*SuccessV2MetaData)(nil),                 // 3: api.typesv2.tiering.SuccessV2MetaData
	(*PlansV2Metadata)(nil),                   // 4: api.typesv2.tiering.PlansV2Metadata
	(*NotificationLandingScreenMetadata)(nil), // 5: api.typesv2.tiering.NotificationLandingScreenMetadata
	(*DetailedBenefitsScreenMetadata)(nil),    // 6: api.typesv2.tiering.DetailedBenefitsScreenMetadata
	(*CashbackDetailsBottomSheet)(nil),        // 7: api.typesv2.tiering.CashbackDetailsBottomSheet
	(*WaysToUseFiPointsBottomSheet)(nil),      // 8: api.typesv2.tiering.WaysToUseFiPointsBottomSheet
}
var file_api_typesv2_tiering_metadata_proto_depIdxs = []int32{
	5, // 0: api.typesv2.tiering.TieringScreenMetaData.notification_landing_screen_metadata:type_name -> api.typesv2.tiering.NotificationLandingScreenMetadata
	6, // 1: api.typesv2.tiering.TieringScreenMetaData.detailed_benefits_screen_metadata:type_name -> api.typesv2.tiering.DetailedBenefitsScreenMetadata
	7, // 2: api.typesv2.tiering.TieringScreenMetaData.cashback_details_bottom_sheet:type_name -> api.typesv2.tiering.CashbackDetailsBottomSheet
	8, // 3: api.typesv2.tiering.TieringScreenMetaData.ways_to_use_fi_points_bottom_sheet:type_name -> api.typesv2.tiering.WaysToUseFiPointsBottomSheet
	4, // 4: api.typesv2.tiering.TieringScreenMetaData.plans_v2_metadata:type_name -> api.typesv2.tiering.PlansV2Metadata
	3, // 5: api.typesv2.tiering.TieringScreenMetaData.success_v2_metadata:type_name -> api.typesv2.tiering.SuccessV2MetaData
	2, // 6: api.typesv2.tiering.TieringScreenMetaData.tier_manual_upgrade_screen_metadata:type_name -> api.typesv2.tiering.TierManualUpgradeScreenMetadata
	1, // 7: api.typesv2.tiering.TieringScreenMetaData.trial_deeplink_metadata:type_name -> api.typesv2.tiering.TrialDeeplinkMetadata
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_typesv2_tiering_metadata_proto_init() }
func file_api_typesv2_tiering_metadata_proto_init() {
	if File_api_typesv2_tiering_metadata_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_tiering_metadata_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TieringScreenMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_tiering_metadata_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrialDeeplinkMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_tiering_metadata_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TierManualUpgradeScreenMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_tiering_metadata_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuccessV2MetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_tiering_metadata_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlansV2Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_tiering_metadata_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationLandingScreenMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_tiering_metadata_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailedBenefitsScreenMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_tiering_metadata_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CashbackDetailsBottomSheet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_tiering_metadata_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaysToUseFiPointsBottomSheet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_tiering_metadata_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*TieringScreenMetaData_NotificationLandingScreenMetadata)(nil),
		(*TieringScreenMetaData_DetailedBenefitsScreenMetadata)(nil),
		(*TieringScreenMetaData_CashbackDetailsBottomSheet)(nil),
		(*TieringScreenMetaData_WaysToUseFiPointsBottomSheet)(nil),
		(*TieringScreenMetaData_PlansV2Metadata)(nil),
		(*TieringScreenMetaData_SuccessV2Metadata)(nil),
		(*TieringScreenMetaData_TierManualUpgradeScreenMetadata)(nil),
		(*TieringScreenMetaData_TrialDeeplinkMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_tiering_metadata_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_tiering_metadata_proto_goTypes,
		DependencyIndexes: file_api_typesv2_tiering_metadata_proto_depIdxs,
		MessageInfos:      file_api_typesv2_tiering_metadata_proto_msgTypes,
	}.Build()
	File_api_typesv2_tiering_metadata_proto = out.File
	file_api_typesv2_tiering_metadata_proto_rawDesc = nil
	file_api_typesv2_tiering_metadata_proto_goTypes = nil
	file_api_typesv2_tiering_metadata_proto_depIdxs = nil
}
