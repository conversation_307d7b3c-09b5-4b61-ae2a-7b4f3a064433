syntax = "proto3";

package api.typesv2.deeplink_screen_option.insights.networth;

import "api/typesv2/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.insights.networth";

option java_multiple_files = true;

enum Entrypoint {
  ENTRYPOINT_UNSPECIFIED = 0;
  // wealth builder dashboard entrypoint
  ENTRYPOINT_WB_DASHBOARD = 1;
  // entrypoint from daily and weekly portfolio tracker
  ENTRYPOINT_PORTFOLIO_TRACKER = 2;
  // wealth analyser mutual funds report page
  ENTRYPOINT_MF_REPORT_PAGE = 3;
  // wealth analyser epf report page
  ENTRYPOINT_EPF_REPORT_PAGE = 4;
  // indian stocks dashboard page
  ENTRYPOINT_INDIAN_STOCKS_DASHBOARD = 5;
  // connected account bank account page
  ENTRYPOINT_BANK_ACCOUNT_PAGE = 6;
  // asset landing pages (manual assets, nps)
  ENTRYPOINT_ASSET_LANDING = 7;
}

message InteractiveTalkToAiScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Entrypoint.String()
  string entrypoint = 2;
}
