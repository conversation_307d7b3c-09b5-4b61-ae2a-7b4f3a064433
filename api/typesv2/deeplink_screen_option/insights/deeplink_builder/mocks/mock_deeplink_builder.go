// Code generated by MockGen. DO NOT EDIT.
// Source: deeplink_builder.go

// Package mock_deeplink_builder is a generated GoMock package.
package mock_deeplink_builder

import (
	context "context"
	reflect "reflect"

	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	networth "github.com/epifi/gamma/api/insights/networth"
	enums "github.com/epifi/gamma/api/insights/networth/enums"
	assetandanalysis "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/assetandanalysis"
	networth0 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	networth_refresh "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth_refresh"
	manual_assets "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth_refresh/manual_assets"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	gomock "github.com/golang/mock/gomock"
)

// MockIDeeplinkBuilder is a mock of IDeeplinkBuilder interface.
type MockIDeeplinkBuilder struct {
	ctrl     *gomock.Controller
	recorder *MockIDeeplinkBuilderMockRecorder
}

// MockIDeeplinkBuilderMockRecorder is the mock recorder for MockIDeeplinkBuilder.
type MockIDeeplinkBuilderMockRecorder struct {
	mock *MockIDeeplinkBuilder
}

// NewMockIDeeplinkBuilder creates a new mock instance.
func NewMockIDeeplinkBuilder(ctrl *gomock.Controller) *MockIDeeplinkBuilder {
	mock := &MockIDeeplinkBuilder{ctrl: ctrl}
	mock.recorder = &MockIDeeplinkBuilderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDeeplinkBuilder) EXPECT() *MockIDeeplinkBuilderMockRecorder {
	return m.recorder
}

// EPFConfirmPhoneNumberScreen mocks base method.
func (m *MockIDeeplinkBuilder) EPFConfirmPhoneNumberScreen(ctx context.Context, phoneNumbers []*common.PhoneNumber, actorId, epfSessionId string) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EPFConfirmPhoneNumberScreen", ctx, phoneNumbers, actorId, epfSessionId)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EPFConfirmPhoneNumberScreen indicates an expected call of EPFConfirmPhoneNumberScreen.
func (mr *MockIDeeplinkBuilderMockRecorder) EPFConfirmPhoneNumberScreen(ctx, phoneNumbers, actorId, epfSessionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EPFConfirmPhoneNumberScreen", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).EPFConfirmPhoneNumberScreen), ctx, phoneNumbers, actorId, epfSessionId)
}

// EPFConfirmUanScreen mocks base method.
func (m *MockIDeeplinkBuilder) EPFConfirmUanScreen(epfSessionId string) *deeplink.Deeplink {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EPFConfirmUanScreen", epfSessionId)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	return ret0
}

// EPFConfirmUanScreen indicates an expected call of EPFConfirmUanScreen.
func (mr *MockIDeeplinkBuilderMockRecorder) EPFConfirmUanScreen(epfSessionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EPFConfirmUanScreen", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).EPFConfirmUanScreen), epfSessionId)
}

// ExportToAiBottomSheetScreen mocks base method.
func (m *MockIDeeplinkBuilder) ExportToAiBottomSheetScreen(ctx context.Context) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExportToAiBottomSheetScreen", ctx)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExportToAiBottomSheetScreen indicates an expected call of ExportToAiBottomSheetScreen.
func (mr *MockIDeeplinkBuilderMockRecorder) ExportToAiBottomSheetScreen(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExportToAiBottomSheetScreen", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).ExportToAiBottomSheetScreen), ctx)
}

// ExportToAiBottomSheetScreenOptions mocks base method.
func (m *MockIDeeplinkBuilder) ExportToAiBottomSheetScreenOptions(ctx context.Context) (*assetandanalysis.WealthSduiBottomSheetScreenOptions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExportToAiBottomSheetScreenOptions", ctx)
	ret0, _ := ret[0].(*assetandanalysis.WealthSduiBottomSheetScreenOptions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExportToAiBottomSheetScreenOptions indicates an expected call of ExportToAiBottomSheetScreenOptions.
func (mr *MockIDeeplinkBuilderMockRecorder) ExportToAiBottomSheetScreenOptions(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExportToAiBottomSheetScreenOptions", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).ExportToAiBottomSheetScreenOptions), ctx)
}

// GetTalkToAIFloatingIconWithDeeplink mocks base method.
func (m *MockIDeeplinkBuilder) GetTalkToAIFloatingIconWithDeeplink(ctx context.Context, actorId string, entrypoint networth0.Entrypoint) *ui.FloatingActionButton {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTalkToAIFloatingIconWithDeeplink", ctx, actorId, entrypoint)
	ret0, _ := ret[0].(*ui.FloatingActionButton)
	return ret0
}

// GetTalkToAIFloatingIconWithDeeplink indicates an expected call of GetTalkToAIFloatingIconWithDeeplink.
func (mr *MockIDeeplinkBuilderMockRecorder) GetTalkToAIFloatingIconWithDeeplink(ctx, actorId, entrypoint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTalkToAIFloatingIconWithDeeplink", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).GetTalkToAIFloatingIconWithDeeplink), ctx, actorId, entrypoint)
}

// ManualAssetRefreshDeeplink mocks base method.
func (m *MockIDeeplinkBuilder) ManualAssetRefreshDeeplink(manualAssetsDetails []*manual_assets.ManualAssetsCurrentValueRefreshDetails) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ManualAssetRefreshDeeplink", manualAssetsDetails)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManualAssetRefreshDeeplink indicates an expected call of ManualAssetRefreshDeeplink.
func (mr *MockIDeeplinkBuilderMockRecorder) ManualAssetRefreshDeeplink(manualAssetsDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualAssetRefreshDeeplink", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).ManualAssetRefreshDeeplink), manualAssetsDetails)
}

// NetWorthAssetHubDeeplink mocks base method.
func (m *MockIDeeplinkBuilder) NetWorthAssetHubDeeplink(ctx context.Context, actorId string) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetWorthAssetHubDeeplink", ctx, actorId)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NetWorthAssetHubDeeplink indicates an expected call of NetWorthAssetHubDeeplink.
func (mr *MockIDeeplinkBuilderMockRecorder) NetWorthAssetHubDeeplink(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetWorthAssetHubDeeplink", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).NetWorthAssetHubDeeplink), ctx, actorId)
}

// NetWorthRefreshBottomSheetScreen mocks base method.
func (m *MockIDeeplinkBuilder) NetWorthRefreshBottomSheetScreen(ctx context.Context, instrumentRefreshDetails []*networth.InstrumentRefreshDetails, actorId string, dashboardType enums.NetWorthDashBoardType) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetWorthRefreshBottomSheetScreen", ctx, instrumentRefreshDetails, actorId, dashboardType)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NetWorthRefreshBottomSheetScreen indicates an expected call of NetWorthRefreshBottomSheetScreen.
func (mr *MockIDeeplinkBuilderMockRecorder) NetWorthRefreshBottomSheetScreen(ctx, instrumentRefreshDetails, actorId, dashboardType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetWorthRefreshBottomSheetScreen", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).NetWorthRefreshBottomSheetScreen), ctx, instrumentRefreshDetails, actorId, dashboardType)
}

// NetWorthRefreshSuccessScreen mocks base method.
func (m *MockIDeeplinkBuilder) NetWorthRefreshSuccessScreen() (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetWorthRefreshSuccessScreen")
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NetWorthRefreshSuccessScreen indicates an expected call of NetWorthRefreshSuccessScreen.
func (mr *MockIDeeplinkBuilderMockRecorder) NetWorthRefreshSuccessScreen() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetWorthRefreshSuccessScreen", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).NetWorthRefreshSuccessScreen))
}

// NextNetWorthRefreshScreenDeeplink mocks base method.
func (m *MockIDeeplinkBuilder) NextNetWorthRefreshScreenDeeplink(refreshId string, assetRefreshInfo []*networth_refresh.AssetRefreshInfo) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NextNetWorthRefreshScreenDeeplink", refreshId, assetRefreshInfo)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NextNetWorthRefreshScreenDeeplink indicates an expected call of NextNetWorthRefreshScreenDeeplink.
func (mr *MockIDeeplinkBuilderMockRecorder) NextNetWorthRefreshScreenDeeplink(refreshId, assetRefreshInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NextNetWorthRefreshScreenDeeplink", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).NextNetWorthRefreshScreenDeeplink), refreshId, assetRefreshInfo)
}

// RoastBottomSheetScreen mocks base method.
func (m *MockIDeeplinkBuilder) RoastBottomSheetScreen(ctx context.Context, aiResponse string) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RoastBottomSheetScreen", ctx, aiResponse)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RoastBottomSheetScreen indicates an expected call of RoastBottomSheetScreen.
func (mr *MockIDeeplinkBuilderMockRecorder) RoastBottomSheetScreen(ctx, aiResponse interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RoastBottomSheetScreen", reflect.TypeOf((*MockIDeeplinkBuilder)(nil).RoastBottomSheetScreen), ctx, aiResponse)
}
