//go:generate mockgen -source=deeplink_builder.go -destination=./mocks/mock_deeplink_builder.go package=mocks

package deeplink_builder

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/assetandanalysis"

	"github.com/epifi/gamma/api/insights/networth/enums"
	"github.com/epifi/gamma/frontend/config/genconf"

	"github.com/google/wire"
	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/pkg/colors"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	netWorthPb "github.com/epifi/gamma/api/insights/networth"
	typesPb "github.com/epifi/gamma/api/typesv2"
	epfScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/epf"
	netWorthScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth_refresh"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth_refresh/manual_assets"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	successScreenTitle           = "Well done! All assets are successfully refreshed."
	manualAssetRefreshTitle      = "Update the latest value of these assets"
	bottomSheetTitle             = "Stay updated on your savings and investments"
	bottonSheetTitleForAssets    = "Get all my Assets"
	bottomSheetSubtitle          = "Sync the below accounts to avail latest information"
	bottonSheetSubTitleForAssets = "Connect these to get a full picture of your wealth"
	dataSafeText                 = "Your data is safe with us."
	startSyncText                = "Start Sync"
	startSyncForAssets           = "Start connecting"
	netWorthSuccessIcon          = "https://epifi-icons.pointz.in/networth/networth-refresh-success-check.png"
	netWorthRefreshAutomateIcon  = "https://epifi-icons.pointz.in/networth/automate.png"
	netWorthRefreshShieldOkIcon  = "https://epifi-icons.pointz.in/networth/shield_ok.png"
	aiBottomSheetClose           = "https://epifi-icons.pointz.in/networth/ai_botttom_sheet_close.png"
)

var (
	DeeplinkBuilderWireset = wire.NewSet(NewDeeplinkBuilder, wire.Bind(new(IDeeplinkBuilder), new(*DeeplinkBuilder)))
)

type DeeplinkBuilder struct {
	config           *genconf.Config
	releaseEvaluator release.IEvaluator
}

type IDeeplinkBuilder interface {
	EPFConfirmPhoneNumberScreen(ctx context.Context, phoneNumbers []*commontypes.PhoneNumber, actorId, epfSessionId string) (*deeplinkPb.Deeplink, error)
	EPFConfirmUanScreen(epfSessionId string) *deeplinkPb.Deeplink
	NetWorthRefreshSuccessScreen() (*deeplinkPb.Deeplink, error)
	ManualAssetRefreshDeeplink(manualAssetsDetails []*manual_assets.ManualAssetsCurrentValueRefreshDetails) (*deeplinkPb.Deeplink, error)
	NetWorthRefreshBottomSheetScreen(ctx context.Context, instrumentRefreshDetails []*netWorthPb.InstrumentRefreshDetails, actorId string, dashboardType enums.NetWorthDashBoardType) (*deeplinkPb.Deeplink, error)
	NextNetWorthRefreshScreenDeeplink(refreshId string, assetRefreshInfo []*networth_refresh.AssetRefreshInfo) (*deeplinkPb.Deeplink, error)
	NetWorthAssetHubDeeplink(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error)
	ExportToAiBottomSheetScreen(ctx context.Context) (*deeplinkPb.Deeplink, error)
	ExportToAiBottomSheetScreenOptions(ctx context.Context) (*assetandanalysis.WealthSduiBottomSheetScreenOptions, error)
	RoastBottomSheetScreen(ctx context.Context, aiResponse string) (*deeplinkPb.Deeplink, error)
	GetTalkToAIFloatingIconWithDeeplink(ctx context.Context, actorId string, entrypoint netWorthScreenOptions.Entrypoint) *typesUi.FloatingActionButton
}

func NewDeeplinkBuilder(config *genconf.Config, releaseEvaluator release.IEvaluator) *DeeplinkBuilder {
	return &DeeplinkBuilder{
		config:           config,
		releaseEvaluator: releaseEvaluator,
	}
}

// EPFConfirmPhoneNumberScreen provides the deeplink to confirm phone number to connect uan
// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=1-5456&mode=design&t=b0trnPNocY7YgBX0-4
func (d *DeeplinkBuilder) EPFConfirmPhoneNumberScreen(ctx context.Context, phoneNumbers []*commontypes.PhoneNumber, actorId, epfSessionId string) (*deeplinkPb.Deeplink, error) {
	uanEnabled, uanErr := d.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_MANUAL_UAN_EPF_FLOW).WithActorId(actorId))
	if uanErr != nil {
		return nil, fmt.Errorf("error in checking if feature manual_uan_epf flow is enabled: %w", uanErr)
	}
	secondaryCta := &typesUi.IconTextComponent{}
	if uanEnabled {
		epfConfirmUanScreen := d.EPFConfirmUanScreen(epfSessionId)
		secondaryCta = &typesUi.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("Connect using my UAN number", colors.ColorLightPrimaryAction, commontypes.FontStyle_HEADLINE_M),
			},
			Deeplink: epfConfirmUanScreen,
		}
	}

	epfLoadingScreenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&epfScreenOptions.DmfGenericLoadingScreenOptions{
		Message:       commontypes.GetPlainStringText("Fetching your UANs").WithFontColor(colors.ColorOnDarkHighEmphasis).WithFontStyle(commontypes.FontStyle_HEADLINE_L),
		LoadingAction: epfScreenOptions.DmfGenericLoadingScreenOptions_LOADING_SCREEN_ACTION_DISCOVER_UAN,
		MinScreenTimeInMs: &durationpb.Duration{
			Seconds: 2,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get epf loading screen options v2: %w", err)
	}
	epfLoadingScreenDeeplink := &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_DMF_GENERIC_LOADING_SCREEN,
		ScreenOptionsV2: epfLoadingScreenOptionsV2,
	}

	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&epfScreenOptions.EpfPassbookImportConfirmNumberScreenOptions{
		Header: nil,
		Title: commontypes.GetTextFromStringFontColourFontStyle("Confirm your mobile number linked with your EPFO account",
			colors.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_M),
		Cta: &deeplinkPb.Cta{
			Text:     "Swipe to confirm",
			Deeplink: epfLoadingScreenDeeplink,
		},
		PhoneNumberBoxTitle: commontypes.GetTextFromStringFontColourFontStyle("MOBILE NUMBER", "", commontypes.FontStyle_HEADLINE_M),
		PhoneNumbers:        phoneNumbers,
		SlidingPages:        getSlidingPagesForEpfUanImportScreen(),
		ConsentText: commontypes.GetTextFromStringFontColourFontStyle("By proceeding you consent to Epifi Tech receiving your investment information from EPFO for calculating your Net Worth.",
			colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_XS),
		SecondaryCta:       secondaryCta,
		EpfImportSessionId: epfSessionId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to convert confirm phone number screen options to screen option v2: %w", err)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_EPF_PASSBOOK_IMPORT_CONFIRM_PHONE_NUMBER_SCREEN,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}

func getSlidingPagesForEpfUanImportScreen() []*epfScreenOptions.SlidingPage {
	return []*epfScreenOptions.SlidingPage{
		{
			Visualisation: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/epf/graph.png").
				WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
				Width:  88,
				Height: 88,
			}),
			Message: commontypes.GetTextFromStringFontColourFontStyle("Track all your Employee Provident Fund contributions", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		},
		{
			Visualisation: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/epf/stopwatch.png").
				WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
				Width:  88,
				Height: 88,
			}),
			Message: commontypes.GetTextFromStringFontColourFontStyle("Access important details quickly & effortlessly", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		},
	}
}

// EPFConfirmUanScreen provides the deeplink for the uan manual flow screen
// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=1-5700&mode=design&t=b0trnPNocY7YgBX0-4
func (d *DeeplinkBuilder) EPFConfirmUanScreen(epfSessionId string) *deeplinkPb.Deeplink {
	optScreenOptions := GetOtpScreenOptionsForSendingOtp("", "", epfSessionId, true)

	screenOptionsV2 := deeplinkv3.GetScreenOptionV2WithoutError(&epfScreenOptions.EpfPassbookImportConfirmUANScreenOptions{
		Header: nil,
		Title: commontypes.GetTextFromStringFontColourFontStyle("Enter your UAN to connect your EPF account to Fi",
			colors.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		UanInputBox: &epfScreenOptions.UanInputBox{
			Title:           commontypes.GetTextFromStringFontColourFontStyle("12 DIGIT UAN", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS),
			UanNumber:       nil,
			PlaceholderText: commontypes.GetTextFromStringFontColourFontStyle("12 DIGIT UAN", colors.ColorMonochromeAsh, commontypes.FontStyle_HEADLINE_XL),
		},
		ConsentText: commontypes.GetTextFromStringFontColourFontStyle("By proceeding you consent to Epifi Tech receiving your investment information from EPFO for calculating your Net Worth.",
			colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_XS),
		Cta: &deeplinkPb.Cta{
			Text: "Get OTP",
			Deeplink: &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_EPF_PASSBOOK_IMPORT_OTP_SCREEN,
				ScreenOptionsV2: optScreenOptions,
			},
		},
	})
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_EPF_PASSBOOK_IMPORT_CONFIRM_UAN_SCREEN,
		ScreenOptionsV2: screenOptionsV2,
	}
}

// NetWorthRefreshSuccessScreen provides the deeplink for the showing success screen for networth refresh
// Screen : NET_WORTH_REFRESH_SUCCESS_SCREEN
// Figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5507-88804&mode=design&t=hSaIgwcI9T2sYwqz-4
func (d *DeeplinkBuilder) NetWorthRefreshSuccessScreen() (*deeplinkPb.Deeplink, error) {
	networthHubScreenDeeplink, err := GetNetworthHubScreenDeeplink()
	if err != nil {
		return nil, fmt.Errorf("failed to get networth screen deeplink: %w", err)
	}
	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&netWorthScreenOptions.NetWorthRefreshSuccessScreenOptions{
		Header:     nil,
		StatusIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(netWorthSuccessIcon, 80, 80),
		Title:      commontypes.GetTextFromStringFontColourFontStyle(successScreenTitle, colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
		DoneCta: &deeplinkPb.Cta{
			Text:     "Close",
			Deeplink: networthHubScreenDeeplink,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to convert networth refresh success screen options to screen option v2: %w", err)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_NET_WORTH_REFRESH_SUCCESS_SCREEN,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}

// ManualAssetRefreshDeeplink provides the deeplink for showing form to update all manual assets
// Screen : NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH
// Figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5507-88732&mode=design&t=hSaIgwcI9T2sYwqz-4
func (d *DeeplinkBuilder) ManualAssetRefreshDeeplink(manualAssetsDetails []*manual_assets.ManualAssetsCurrentValueRefreshDetails) (*deeplinkPb.Deeplink, error) {
	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&netWorthScreenOptions.NetWorthManualAssetsRefreshScreenOptions{
		Header:               nil,
		Title:                commontypes.GetTextFromStringFontColourFontStyle(manualAssetRefreshTitle, colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
		ManualAssetDetailsV2: manualAssetsDetails,
		UpdateCta: &deeplinkPb.Cta{
			Text: "Update",
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_UPDATE_MANUAL_ASSETS,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to convert manual asset refresh screen options to screen option v2: %w", err)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}

// NetWorthRefreshBottomSheetScreen provides deeplink for the NetWorth refresh bottom sheet
// Screen : NET_WORTH_REFRESH_INIT_BOTTOM_SHEET
// Ref: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5507-88987&mode=design&t=pCQYH80V62Je2fua-4
func (d *DeeplinkBuilder) NetWorthRefreshBottomSheetScreen(ctx context.Context, instrumentRefreshDetails []*netWorthPb.InstrumentRefreshDetails, actorId string, dashboardType enums.NetWorthDashBoardType) (*deeplinkPb.Deeplink, error) {
	// list of asset refresh info used in next net worth refresh screen
	assetRefreshInfo := d.getAssetRefreshInfoList(ctx, instrumentRefreshDetails, dashboardType)
	// list of asset refresh items used to show list of assets refreshed in bottom sheet
	assetRefreshItems := d.getAssetRefreshItems(ctx, instrumentRefreshDetails, actorId, dashboardType)
	// next net worth refresh deeplink with empty refresh id for first time
	nextNetWorthRefreshDeeplink, err := d.NextNetWorthRefreshScreenDeeplink("", assetRefreshInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to get next net worth refresh screen options v2: %w", err)
	}
	refreshTitleText, bottonSheetSubTitleforRefresh, refreshStartCta := bottomSheetTitle, bottomSheetSubtitle, startSyncText
	if dashboardType == enums.NetWorthDashBoardType_ASSETS {
		refreshTitleText = bottonSheetTitleForAssets
		bottonSheetSubTitleforRefresh = bottonSheetSubTitleForAssets
		refreshStartCta = startSyncForAssets
	}
	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&netWorthScreenOptions.NetWorthRefreshBottomSheetOptions{
		Header:   nil,
		Icon:     commontypes.GetVisualElementFromUrlHeightAndWidth(netWorthRefreshAutomateIcon, 56, 54),
		Title:    commontypes.GetTextFromStringFontColourFontStyle(refreshTitleText, colors.ColorNight, commontypes.FontStyle_HEADLINE_L),
		SubTitle: commontypes.GetTextFromStringFontColourFontStyle(bottonSheetSubTitleforRefresh, colors.ColorLead, commontypes.FontStyle_BODY_S),
		HeaderFooter: typesUi.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(dataSafeText, colors.ColorLead, commontypes.FontStyle_BODY_4)).
			WithLeftImageUrlHeightAndWidth(netWorthRefreshShieldOkIcon, 16, 16).WithLeftImagePadding(4).
			WithContainerPadding(4, 4, 4, 4).WithContainerCornerRadius(8).WithContainerBackgroundColor(colors.ColorIvory),
		InitiateCta: &deeplinkPb.Cta{
			Text:     refreshStartCta,
			Deeplink: nextNetWorthRefreshDeeplink,
		},
		AssetRefreshItems: assetRefreshItems,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to convert bottom sheet screen options to screen option v2: %w", err)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_NET_WORTH_REFRESH_INIT_BOTTOM_SHEET,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}

// NextNetWorthRefreshScreenDeeplink provides deeplink for the Next NetWorth refresh action
// Calls 'GetNextNetWorthRefreshAction' rpc using the id and asset refresh info provided
// Screen : NET_WORTH_REFRESH_GET_NEXT_ACTION
func (d *DeeplinkBuilder) NextNetWorthRefreshScreenDeeplink(refreshId string, assetRefreshInfo []*networth_refresh.AssetRefreshInfo) (*deeplinkPb.Deeplink, error) {
	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&netWorthScreenOptions.NextNetWorthRefreshScreenOptions{
		Header: nil,
		RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
			NetWorthRefreshId:  refreshId,
			AssetRefreshInfoV2: assetRefreshInfo,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to convert next networth refresh screen options to screen option v2: %w", err)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}

func (d *DeeplinkBuilder) NetWorthAssetHubDeeplink(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error) {
	wealthDashboardFlow, err := d.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION).WithActorId(actorId))
	if err != nil {
		return nil, fmt.Errorf("error in checking if feature wealth dashboard flow is enabled: %w", err)
	}
	if wealthDashboardFlow {
		return GetWealthBuilderDashboardDeeplink(), nil
	}
	return GetNetworthHubScreenDeeplinkWithoutError(), nil
}

func (d *DeeplinkBuilder) ExportToAiBottomSheetScreen(ctx context.Context) (*deeplinkPb.Deeplink, error) {
	screenOptions, screenOptionsError := d.ExportToAiBottomSheetScreenOptions(ctx)
	if screenOptionsError != nil {
		return nil, screenOptionsError
	}
	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(screenOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to convert bottom sheet screen options to screen option v2: %w", err)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_WEALTH_SDUI_BOTTOM_SHEET,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}

// ExportToAiBottomSheetScreenOptions returns screen options for export to AI bottom sheet
func (d *DeeplinkBuilder) ExportToAiBottomSheetScreenOptions(ctx context.Context) (*assetandanalysis.WealthSduiBottomSheetScreenOptions, error) {
	section, secErr := d.exportToAiBottomSheetSduiContent(ctx)
	if secErr != nil {
		logger.Error(ctx, "Export to AI SDUI error", zap.Error(secErr))
		return nil, secErr
	}
	metaData, err := protojson.Marshal(
		&assetandanalysis.WealthSduiBottomSheetMetaData{
			Purpose: assetandanalysis.WealthSduiBottomSheetMetaData_PURPOSE_TALK_TO_AI,
		},
	)
	if err != nil {
		logger.Error(ctx, "failed to marshal metadata")
	}
	return &assetandanalysis.WealthSduiBottomSheetScreenOptions{
		BackgroundColour: widget.GetBlockBackgroundColour(colors.ColorSnow),
		CloseIcon:        commontypes.GetVisualElementFromUrlHeightAndWidth(aiBottomSheetClose, 32, 32),
		Content:          section,
		MetaData:         string(metaData),
	}, nil
}

// RoastBottomSheetScreen provides the deeplink for the AI Roast bottom sheet
// Screen : WEALTH_SDUI_BOTTOM_SHEET
// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16590-14776&t=K96t9VP9wUR6cM1g-0
func (d *DeeplinkBuilder) RoastBottomSheetScreen(ctx context.Context, aiResponse string) (*deeplinkPb.Deeplink, error) {
	section, secErr := d.roastBottomSheetSduiContent(ctx, aiResponse)
	if secErr != nil {
		return nil, secErr
	}
	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&pkg.SduiBottomSheetOptions{
		Section:                section,
		PopParentScreenOnClose: false,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to convert roast bottom sheet screen options to screen option v2: %w", err)
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_SDUI_BOTTOM_SHEET,
		ScreenOptionsV2: screenOptionsV2,
	}, nil
}

func (d *DeeplinkBuilder) GetTalkToAIFloatingIconWithDeeplink(ctx context.Context, actorId string, entrypoint netWorthScreenOptions.Entrypoint) *typesUi.FloatingActionButton {
	var floatingActionButton *typesUi.FloatingActionButton
	interactiveTalkToAI, err := d.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_INTERACTIVE_TALK_TO_AI_SCREEN).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate interactive ai bottom sheet feature flag")
		return nil
	}
	if interactiveTalkToAI {
		floatingActionButton = &typesUi.FloatingActionButton{
			Icon:     commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/insights/talktoai.png", 24, 24),
			Title:    commontypes.GetTextFromStringFontColourFontStyle("Talk to AI", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M),
			BgColour: widget.GetLinearGradientBackgroundColour(45, []*widget.ColorStop{{Color: "#006D5B", StopPercentage: 0}, {Color: "#00B899", StopPercentage: 100}}), CornerRadius: 24,
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_INTERACTIVE_TALK_TO_AI_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&netWorthScreenOptions.InteractiveTalkToAiScreenOptions{
					Header:     &deeplink_screen_option.ScreenOptionHeader{},
					Entrypoint: entrypoint.String(),
				}),
			},
		}
	}
	return floatingActionButton
}
