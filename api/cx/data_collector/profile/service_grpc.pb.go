// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/cx/data_collector/profile/service.proto

package profile

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CustomerProfile_GetCustomerProfile_FullMethodName         = "/cx.data_collector.profile.CustomerProfile/GetCustomerProfile"
	CustomerProfile_GetUserByBankInfo_FullMethodName          = "/cx.data_collector.profile.CustomerProfile/GetUserByBankInfo"
	CustomerProfile_GetBulkUserInfoByTicketIds_FullMethodName = "/cx.data_collector.profile.CustomerProfile/GetBulkUserInfoByTicketIds"
	CustomerProfile_GetBulkUserInfo_FullMethodName            = "/cx.data_collector.profile.CustomerProfile/GetBulkUserInfo"
	CustomerProfile_GetSegmentsWithUserCounts_FullMethodName  = "/cx.data_collector.profile.CustomerProfile/GetSegmentsWithUserCounts"
)

// CustomerProfileClient is the client API for CustomerProfile service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CustomerProfileClient interface {
	// RPC to fetch customer profile
	// The service will return error if user is not found in the system
	GetCustomerProfile(ctx context.Context, in *GetCustomerProfileRequest, opts ...grpc.CallOption) (*GetCustomerProfileResponse, error)
	// rpc method to get fi users detail using account number of the user
	// will be used by biz devs to identify a user internally given the account number
	// will return code
	// OK for success
	// INTERNAL for server errors
	// NOT_FOUND if user is not found
	// INVALID_ARGUMENT if the mandatory parameters are missing
	GetUserByBankInfo(ctx context.Context, in *GetUserByBankInfoRequest, opts ...grpc.CallOption) (*GetUserByBankInfoResponse, error)
	// rpc method to get fi users detail in bulk using list of ticket ids
	// will be used by admins to get user details in bulk for a list of ticket ids
	// will return code
	// OK for success
	// INTERNAL for server errors
	// INVALID_ARGUMENT if the ticket list is empty
	// will return an s3 signed url for the csv file containing the user details
	GetBulkUserInfoByTicketIds(ctx context.Context, in *GetBulkUserInfoByTicketIdsRequest, opts ...grpc.CallOption) (*GetBulkUserInfoByTicketIdsResponse, error)
	// rpc method to get fi users details(email IDs, FCM IDs) in bulk using list of Customer ID/ Acc ID/ Actor ID
	// will be used by admins to get user details in bulk for a list of Customer ID/ Acc ID/ Actor ID
	// will return code:
	// OK for success - even if details for some ids not found(returned in failed_id_list)
	// INTERNAL for server errors
	// INVALID_ARGUMENT if the list is empty or greater than max threshold limit
	// will return an s3 signed url for the csv file containing the user details
	// Also send email to the one who requested
	GetBulkUserInfo(ctx context.Context, in *GetBulkUserInfoRequest, opts ...grpc.CallOption) (*GetBulkUserInfoResponse, error)
	// Get all segments with their user counts
	GetSegmentsWithUserCounts(ctx context.Context, in *GetSegmentsWithUserCountsRequest, opts ...grpc.CallOption) (*GetSegmentsWithUserCountsResponse, error)
}

type customerProfileClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomerProfileClient(cc grpc.ClientConnInterface) CustomerProfileClient {
	return &customerProfileClient{cc}
}

func (c *customerProfileClient) GetCustomerProfile(ctx context.Context, in *GetCustomerProfileRequest, opts ...grpc.CallOption) (*GetCustomerProfileResponse, error) {
	out := new(GetCustomerProfileResponse)
	err := c.cc.Invoke(ctx, CustomerProfile_GetCustomerProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerProfileClient) GetUserByBankInfo(ctx context.Context, in *GetUserByBankInfoRequest, opts ...grpc.CallOption) (*GetUserByBankInfoResponse, error) {
	out := new(GetUserByBankInfoResponse)
	err := c.cc.Invoke(ctx, CustomerProfile_GetUserByBankInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerProfileClient) GetBulkUserInfoByTicketIds(ctx context.Context, in *GetBulkUserInfoByTicketIdsRequest, opts ...grpc.CallOption) (*GetBulkUserInfoByTicketIdsResponse, error) {
	out := new(GetBulkUserInfoByTicketIdsResponse)
	err := c.cc.Invoke(ctx, CustomerProfile_GetBulkUserInfoByTicketIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerProfileClient) GetBulkUserInfo(ctx context.Context, in *GetBulkUserInfoRequest, opts ...grpc.CallOption) (*GetBulkUserInfoResponse, error) {
	out := new(GetBulkUserInfoResponse)
	err := c.cc.Invoke(ctx, CustomerProfile_GetBulkUserInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerProfileClient) GetSegmentsWithUserCounts(ctx context.Context, in *GetSegmentsWithUserCountsRequest, opts ...grpc.CallOption) (*GetSegmentsWithUserCountsResponse, error) {
	out := new(GetSegmentsWithUserCountsResponse)
	err := c.cc.Invoke(ctx, CustomerProfile_GetSegmentsWithUserCounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerProfileServer is the server API for CustomerProfile service.
// All implementations should embed UnimplementedCustomerProfileServer
// for forward compatibility
type CustomerProfileServer interface {
	// RPC to fetch customer profile
	// The service will return error if user is not found in the system
	GetCustomerProfile(context.Context, *GetCustomerProfileRequest) (*GetCustomerProfileResponse, error)
	// rpc method to get fi users detail using account number of the user
	// will be used by biz devs to identify a user internally given the account number
	// will return code
	// OK for success
	// INTERNAL for server errors
	// NOT_FOUND if user is not found
	// INVALID_ARGUMENT if the mandatory parameters are missing
	GetUserByBankInfo(context.Context, *GetUserByBankInfoRequest) (*GetUserByBankInfoResponse, error)
	// rpc method to get fi users detail in bulk using list of ticket ids
	// will be used by admins to get user details in bulk for a list of ticket ids
	// will return code
	// OK for success
	// INTERNAL for server errors
	// INVALID_ARGUMENT if the ticket list is empty
	// will return an s3 signed url for the csv file containing the user details
	GetBulkUserInfoByTicketIds(context.Context, *GetBulkUserInfoByTicketIdsRequest) (*GetBulkUserInfoByTicketIdsResponse, error)
	// rpc method to get fi users details(email IDs, FCM IDs) in bulk using list of Customer ID/ Acc ID/ Actor ID
	// will be used by admins to get user details in bulk for a list of Customer ID/ Acc ID/ Actor ID
	// will return code:
	// OK for success - even if details for some ids not found(returned in failed_id_list)
	// INTERNAL for server errors
	// INVALID_ARGUMENT if the list is empty or greater than max threshold limit
	// will return an s3 signed url for the csv file containing the user details
	// Also send email to the one who requested
	GetBulkUserInfo(context.Context, *GetBulkUserInfoRequest) (*GetBulkUserInfoResponse, error)
	// Get all segments with their user counts
	GetSegmentsWithUserCounts(context.Context, *GetSegmentsWithUserCountsRequest) (*GetSegmentsWithUserCountsResponse, error)
}

// UnimplementedCustomerProfileServer should be embedded to have forward compatible implementations.
type UnimplementedCustomerProfileServer struct {
}

func (UnimplementedCustomerProfileServer) GetCustomerProfile(context.Context, *GetCustomerProfileRequest) (*GetCustomerProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerProfile not implemented")
}
func (UnimplementedCustomerProfileServer) GetUserByBankInfo(context.Context, *GetUserByBankInfoRequest) (*GetUserByBankInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserByBankInfo not implemented")
}
func (UnimplementedCustomerProfileServer) GetBulkUserInfoByTicketIds(context.Context, *GetBulkUserInfoByTicketIdsRequest) (*GetBulkUserInfoByTicketIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBulkUserInfoByTicketIds not implemented")
}
func (UnimplementedCustomerProfileServer) GetBulkUserInfo(context.Context, *GetBulkUserInfoRequest) (*GetBulkUserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBulkUserInfo not implemented")
}
func (UnimplementedCustomerProfileServer) GetSegmentsWithUserCounts(context.Context, *GetSegmentsWithUserCountsRequest) (*GetSegmentsWithUserCountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSegmentsWithUserCounts not implemented")
}

// UnsafeCustomerProfileServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomerProfileServer will
// result in compilation errors.
type UnsafeCustomerProfileServer interface {
	mustEmbedUnimplementedCustomerProfileServer()
}

func RegisterCustomerProfileServer(s grpc.ServiceRegistrar, srv CustomerProfileServer) {
	s.RegisterService(&CustomerProfile_ServiceDesc, srv)
}

func _CustomerProfile_GetCustomerProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerProfileServer).GetCustomerProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerProfile_GetCustomerProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerProfileServer).GetCustomerProfile(ctx, req.(*GetCustomerProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerProfile_GetUserByBankInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserByBankInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerProfileServer).GetUserByBankInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerProfile_GetUserByBankInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerProfileServer).GetUserByBankInfo(ctx, req.(*GetUserByBankInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerProfile_GetBulkUserInfoByTicketIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBulkUserInfoByTicketIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerProfileServer).GetBulkUserInfoByTicketIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerProfile_GetBulkUserInfoByTicketIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerProfileServer).GetBulkUserInfoByTicketIds(ctx, req.(*GetBulkUserInfoByTicketIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerProfile_GetBulkUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBulkUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerProfileServer).GetBulkUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerProfile_GetBulkUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerProfileServer).GetBulkUserInfo(ctx, req.(*GetBulkUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerProfile_GetSegmentsWithUserCounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSegmentsWithUserCountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerProfileServer).GetSegmentsWithUserCounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerProfile_GetSegmentsWithUserCounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerProfileServer).GetSegmentsWithUserCounts(ctx, req.(*GetSegmentsWithUserCountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomerProfile_ServiceDesc is the grpc.ServiceDesc for CustomerProfile service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomerProfile_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cx.data_collector.profile.CustomerProfile",
	HandlerType: (*CustomerProfileServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCustomerProfile",
			Handler:    _CustomerProfile_GetCustomerProfile_Handler,
		},
		{
			MethodName: "GetUserByBankInfo",
			Handler:    _CustomerProfile_GetUserByBankInfo_Handler,
		},
		{
			MethodName: "GetBulkUserInfoByTicketIds",
			Handler:    _CustomerProfile_GetBulkUserInfoByTicketIds_Handler,
		},
		{
			MethodName: "GetBulkUserInfo",
			Handler:    _CustomerProfile_GetBulkUserInfo_Handler,
		},
		{
			MethodName: "GetSegmentsWithUserCounts",
			Handler:    _CustomerProfile_GetSegmentsWithUserCounts_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/cx/data_collector/profile/service.proto",
}
