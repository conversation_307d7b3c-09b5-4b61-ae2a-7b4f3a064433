// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/data_collector/profile/service.proto

package profile

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	cx "github.com/epifi/gamma/api/cx"
	customer_auth "github.com/epifi/gamma/api/cx/customer_auth"
	segment "github.com/epifi/gamma/api/segment"
	webui "github.com/epifi/gamma/api/typesv2/webui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Ticket id -> Mandatory
// Agent email -> Mandatory
// Access token -> Mandatory
// Identifier -> Optional (case by case basis)
type GetCustomerProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// enum to let the backend know if the balance must be updated or not
	// force_balance_update must be passed empty if no balance update is needed
	ForceBalanceUpdate ForceBalanceUpdate `protobuf:"varint,2,opt,name=force_balance_update,json=forceBalanceUpdate,proto3,enum=cx.data_collector.profile.ForceBalanceUpdate" json:"force_balance_update,omitempty"`
}

func (x *GetCustomerProfileRequest) Reset() {
	*x = GetCustomerProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProfileRequest) ProtoMessage() {}

func (x *GetCustomerProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProfileRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerProfileRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCustomerProfileRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetCustomerProfileRequest) GetForceBalanceUpdate() ForceBalanceUpdate {
	if x != nil {
		return x.ForceBalanceUpdate
	}
	return ForceBalanceUpdate_FORCE_BALANCE_UPDATE_UNSPECIFIED
}

type GetCustomerProfileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	AppVersion string `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	DeviceOs string `protobuf:"bytes,3,opt,name=device_os,json=deviceOs,proto3" json:"device_os,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	DeviceOsVersion string `protobuf:"bytes,4,opt,name=device_os_version,json=deviceOsVersion,proto3" json:"device_os_version,omitempty"`
	// deprecated
	//
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	IsDiscoverabilityOn bool `protobuf:"varint,5,opt,name=is_discoverability_on,json=isDiscoverabilityOn,proto3" json:"is_discoverability_on,omitempty"`
	// deprecated
	//
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	IsFindMyDeviceOn bool `protobuf:"varint,6,opt,name=is_find_my_device_on,json=isFindMyDeviceOn,proto3" json:"is_find_my_device_on,omitempty"`
	// deprecated
	//
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	IsDualSimDevice        bool                            `protobuf:"varint,7,opt,name=is_dual_sim_device,json=isDualSimDevice,proto3" json:"is_dual_sim_device,omitempty"`
	Account                *CustomerAccount                `protobuf:"bytes,8,opt,name=account,proto3" json:"account,omitempty"`
	SherlockDeepLink       *customer_auth.SherlockDeepLink `protobuf:"bytes,9,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
	CustomerId             string                          `protobuf:"bytes,10,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	LegalName              *common.Name                    `protobuf:"bytes,11,opt,name=legal_name,json=legalName,proto3" json:"legal_name,omitempty"`
	Discoverability        common.BooleanEnum              `protobuf:"varint,12,opt,name=discoverability,proto3,enum=api.typesv2.common.BooleanEnum" json:"discoverability,omitempty"`
	FindMyDeviceOn         common.BooleanEnum              `protobuf:"varint,13,opt,name=find_my_device_on,json=findMyDeviceOn,proto3,enum=api.typesv2.common.BooleanEnum" json:"find_my_device_on,omitempty"`
	DualSimDevice          common.BooleanEnum              `protobuf:"varint,14,opt,name=dual_sim_device,json=dualSimDevice,proto3,enum=api.typesv2.common.BooleanEnum" json:"dual_sim_device,omitempty"`
	DeviceDetails          *DeviceDetails                  `protobuf:"bytes,15,opt,name=device_details,json=deviceDetails,proto3" json:"device_details,omitempty"`
	AccessRevokeState      string                          `protobuf:"bytes,16,opt,name=access_revoke_state,json=accessRevokeState,proto3" json:"access_revoke_state,omitempty"`
	AccessRevokeDetails    *AccessRevokeDetails            `protobuf:"bytes,17,opt,name=access_revoke_details,json=accessRevokeDetails,proto3" json:"access_revoke_details,omitempty"`
	ExternalAccountDetails *ExternalAccountDetails         `protobuf:"bytes,18,opt,name=external_account_details,json=externalAccountDetails,proto3" json:"external_account_details,omitempty"`
	FatherName             *common.Name                    `protobuf:"bytes,19,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	// Display string for current tier
	CurrentTierPlanName string `protobuf:"bytes,20,opt,name=current_tier_plan_name,json=currentTierPlanName,proto3" json:"current_tier_plan_name,omitempty"`
	// balance that was present in the account during closure as reported by federal.
	// main use case is for min-kyc expired accounts
	ClosedAccountBalance string `protobuf:"bytes,21,opt,name=closed_account_balance,json=closedAccountBalance,proto3" json:"closed_account_balance,omitempty"`
	// weather fi account (SA or fi-lite) has been created or not
	FiAccountStatus string `protobuf:"bytes,22,opt,name=fi_account_status,json=fiAccountStatus,proto3" json:"fi_account_status,omitempty"`
	// sign_status represent whether signature present at bank or not
	SignStatus string `protobuf:"bytes,23,opt,name=sign_status,json=signStatus,proto3" json:"sign_status,omitempty"`
	// balance refresh data
	BalanceRefreshData *GetCustomerProfileResponse_BalanceRefreshData `protobuf:"bytes,24,opt,name=balance_refresh_data,json=balanceRefreshData,proto3" json:"balance_refresh_data,omitempty"`
	LevelValues        []*webui.LabelValue                            `protobuf:"bytes,25,rep,name=level_values,json=levelValues,proto3" json:"level_values,omitempty"`
	// section to list savings account closure requests created by user
	AccountClosureRequestSection *Section `protobuf:"bytes,26,opt,name=account_closure_request_section,json=accountClosureRequestSection,proto3" json:"account_closure_request_section,omitempty"`
}

func (x *GetCustomerProfileResponse) Reset() {
	*x = GetCustomerProfileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProfileResponse) ProtoMessage() {}

func (x *GetCustomerProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProfileResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerProfileResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCustomerProfileResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetCustomerProfileResponse) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetCustomerProfileResponse) GetDeviceOs() string {
	if x != nil {
		return x.DeviceOs
	}
	return ""
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetCustomerProfileResponse) GetDeviceOsVersion() string {
	if x != nil {
		return x.DeviceOsVersion
	}
	return ""
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetCustomerProfileResponse) GetIsDiscoverabilityOn() bool {
	if x != nil {
		return x.IsDiscoverabilityOn
	}
	return false
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetCustomerProfileResponse) GetIsFindMyDeviceOn() bool {
	if x != nil {
		return x.IsFindMyDeviceOn
	}
	return false
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetCustomerProfileResponse) GetIsDualSimDevice() bool {
	if x != nil {
		return x.IsDualSimDevice
	}
	return false
}

func (x *GetCustomerProfileResponse) GetAccount() *CustomerAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GetCustomerProfileResponse) GetLegalName() *common.Name {
	if x != nil {
		return x.LegalName
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetDiscoverability() common.BooleanEnum {
	if x != nil {
		return x.Discoverability
	}
	return common.BooleanEnum(0)
}

func (x *GetCustomerProfileResponse) GetFindMyDeviceOn() common.BooleanEnum {
	if x != nil {
		return x.FindMyDeviceOn
	}
	return common.BooleanEnum(0)
}

func (x *GetCustomerProfileResponse) GetDualSimDevice() common.BooleanEnum {
	if x != nil {
		return x.DualSimDevice
	}
	return common.BooleanEnum(0)
}

func (x *GetCustomerProfileResponse) GetDeviceDetails() *DeviceDetails {
	if x != nil {
		return x.DeviceDetails
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetAccessRevokeState() string {
	if x != nil {
		return x.AccessRevokeState
	}
	return ""
}

func (x *GetCustomerProfileResponse) GetAccessRevokeDetails() *AccessRevokeDetails {
	if x != nil {
		return x.AccessRevokeDetails
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetExternalAccountDetails() *ExternalAccountDetails {
	if x != nil {
		return x.ExternalAccountDetails
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetCurrentTierPlanName() string {
	if x != nil {
		return x.CurrentTierPlanName
	}
	return ""
}

func (x *GetCustomerProfileResponse) GetClosedAccountBalance() string {
	if x != nil {
		return x.ClosedAccountBalance
	}
	return ""
}

func (x *GetCustomerProfileResponse) GetFiAccountStatus() string {
	if x != nil {
		return x.FiAccountStatus
	}
	return ""
}

func (x *GetCustomerProfileResponse) GetSignStatus() string {
	if x != nil {
		return x.SignStatus
	}
	return ""
}

func (x *GetCustomerProfileResponse) GetBalanceRefreshData() *GetCustomerProfileResponse_BalanceRefreshData {
	if x != nil {
		return x.BalanceRefreshData
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetLevelValues() []*webui.LabelValue {
	if x != nil {
		return x.LevelValues
	}
	return nil
}

func (x *GetCustomerProfileResponse) GetAccountClosureRequestSection() *Section {
	if x != nil {
		return x.AccountClosureRequestSection
	}
	return nil
}

type Section struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title       string                `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	LabelValues []*webui.LabelValueV2 `protobuf:"bytes,2,rep,name=label_values,json=labelValues,proto3" json:"label_values,omitempty"`
}

func (x *Section) Reset() {
	*x = Section{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Section) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Section) ProtoMessage() {}

func (x *Section) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Section.ProtoReflect.Descriptor instead.
func (*Section) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{2}
}

func (x *Section) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Section) GetLabelValues() []*webui.LabelValueV2 {
	if x != nil {
		return x.LabelValues
	}
	return nil
}

type GetUserByBankInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Reason string     `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// one of the identifier is mandatory
	//
	// Types that are assignable to Identifier:
	//
	//	*GetUserByBankInfoRequest_AccountNumber
	Identifier isGetUserByBankInfoRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetUserByBankInfoRequest) Reset() {
	*x = GetUserByBankInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserByBankInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByBankInfoRequest) ProtoMessage() {}

func (x *GetUserByBankInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByBankInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserByBankInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserByBankInfoRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetUserByBankInfoRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (m *GetUserByBankInfoRequest) GetIdentifier() isGetUserByBankInfoRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetUserByBankInfoRequest) GetAccountNumber() string {
	if x, ok := x.GetIdentifier().(*GetUserByBankInfoRequest_AccountNumber); ok {
		return x.AccountNumber
	}
	return ""
}

type isGetUserByBankInfoRequest_Identifier interface {
	isGetUserByBankInfoRequest_Identifier()
}

type GetUserByBankInfoRequest_AccountNumber struct {
	// account number of the user
	AccountNumber string `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3,oneof"`
}

func (*GetUserByBankInfoRequest_AccountNumber) isGetUserByBankInfoRequest_Identifier() {}

type GetUserByBankInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return code
	// INTERNAL for server errors
	// NOT_FOUND if user is not found
	// INVALID_ARGUMENT if the mandatory parameters are missing
	Status      *rpc.Status                            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UserDetails *GetUserByBankInfoResponse_UserDetails `protobuf:"bytes,2,opt,name=user_details,json=userDetails,proto3" json:"user_details,omitempty"`
}

func (x *GetUserByBankInfoResponse) Reset() {
	*x = GetUserByBankInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserByBankInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByBankInfoResponse) ProtoMessage() {}

func (x *GetUserByBankInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByBankInfoResponse.ProtoReflect.Descriptor instead.
func (*GetUserByBankInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserByBankInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUserByBankInfoResponse) GetUserDetails() *GetUserByBankInfoResponse_UserDetails {
	if x != nil {
		return x.UserDetails
	}
	return nil
}

type GetBulkUserInfoByTicketIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// list of ticket ids for which user information is needed
	TicketIdList []int64 `protobuf:"varint,2,rep,packed,name=ticket_id_list,json=ticketIdList,proto3" json:"ticket_id_list,omitempty"`
}

func (x *GetBulkUserInfoByTicketIdsRequest) Reset() {
	*x = GetBulkUserInfoByTicketIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBulkUserInfoByTicketIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBulkUserInfoByTicketIdsRequest) ProtoMessage() {}

func (x *GetBulkUserInfoByTicketIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBulkUserInfoByTicketIdsRequest.ProtoReflect.Descriptor instead.
func (*GetBulkUserInfoByTicketIdsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetBulkUserInfoByTicketIdsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBulkUserInfoByTicketIdsRequest) GetTicketIdList() []int64 {
	if x != nil {
		return x.TicketIdList
	}
	return nil
}

type GetBulkUserInfoByTicketIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return code
	// OK for success
	// INTERNAL for server errors
	// INVALID_ARGUMENT if the mandatory parameters are missing
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of tickets for which we were not able to fetch user information along with failure reason
	// this field is deprecated in favour of async processing and sending details via email
	//
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	FailedTicketList []*TicketFailure `protobuf:"bytes,2,rep,name=failed_ticket_list,json=failedTicketList,proto3" json:"failed_ticket_list,omitempty"`
	// s3 signed url for csv file with the user details
	// this field is deprecated in favour of async processing and sending details via email
	//
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	FileUrl string `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
}

func (x *GetBulkUserInfoByTicketIdsResponse) Reset() {
	*x = GetBulkUserInfoByTicketIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBulkUserInfoByTicketIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBulkUserInfoByTicketIdsResponse) ProtoMessage() {}

func (x *GetBulkUserInfoByTicketIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBulkUserInfoByTicketIdsResponse.ProtoReflect.Descriptor instead.
func (*GetBulkUserInfoByTicketIdsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetBulkUserInfoByTicketIdsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetBulkUserInfoByTicketIdsResponse) GetFailedTicketList() []*TicketFailure {
	if x != nil {
		return x.FailedTicketList
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetBulkUserInfoByTicketIdsResponse) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

type TicketFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TicketId      int64  `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	FailureReason string `protobuf:"bytes,2,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *TicketFailure) Reset() {
	*x = TicketFailure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketFailure) ProtoMessage() {}

func (x *TicketFailure) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketFailure.ProtoReflect.Descriptor instead.
func (*TicketFailure) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{7}
}

func (x *TicketFailure) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *TicketFailure) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type GetBulkUserInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// list of ids for which user information is needed
	// a combination of different IDs(eg: {0-AccountId, 1-ActorId} is also supported
	IdList []*BulkUserIdentifier `protobuf:"bytes,2,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	Reason string                `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *GetBulkUserInfoRequest) Reset() {
	*x = GetBulkUserInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBulkUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBulkUserInfoRequest) ProtoMessage() {}

func (x *GetBulkUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBulkUserInfoRequest.ProtoReflect.Descriptor instead.
func (*GetBulkUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetBulkUserInfoRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBulkUserInfoRequest) GetIdList() []*BulkUserIdentifier {
	if x != nil {
		return x.IdList
	}
	return nil
}

func (x *GetBulkUserInfoRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type GetBulkUserInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return code
	// OK for success - even if details for some ids not found(returned in failed_id_list)
	// INTERNAL for server errors
	// INVALID_ARGUMENT if the list is empty or greater than max threshold limit
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of ids for which we were not able to fetch user information along with failure reason
	// this field is deprecated in favour of async processing and sending details via email
	//
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	FailedIdList []*FailureID `protobuf:"bytes,2,rep,name=failed_id_list,json=failedIdList,proto3" json:"failed_id_list,omitempty"`
	// s3 signed url for csv file with the user details
	// this field is deprecated in favour of async processing and sending details via email
	//
	// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
	FileUrl string `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
}

func (x *GetBulkUserInfoResponse) Reset() {
	*x = GetBulkUserInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBulkUserInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBulkUserInfoResponse) ProtoMessage() {}

func (x *GetBulkUserInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBulkUserInfoResponse.ProtoReflect.Descriptor instead.
func (*GetBulkUserInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetBulkUserInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetBulkUserInfoResponse) GetFailedIdList() []*FailureID {
	if x != nil {
		return x.FailedIdList
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/data_collector/profile/service.proto.
func (x *GetBulkUserInfoResponse) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

type FailureID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the ID for which we were not able to fetch the required user information
	Id *BulkUserIdentifier `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// reason for the failure for this ID (eg: unable to fetch account details)
	FailureReason string `protobuf:"bytes,2,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *FailureID) Reset() {
	*x = FailureID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FailureID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FailureID) ProtoMessage() {}

func (x *FailureID) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FailureID.ProtoReflect.Descriptor instead.
func (*FailureID) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{10}
}

func (x *FailureID) GetId() *BulkUserIdentifier {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *FailureID) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

// Segment related messages
type GetSegmentsWithUserCountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Duration from which to fetch segments (ISO timestamp)
	Duration string `protobuf:"bytes,2,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *GetSegmentsWithUserCountsRequest) Reset() {
	*x = GetSegmentsWithUserCountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentsWithUserCountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentsWithUserCountsRequest) ProtoMessage() {}

func (x *GetSegmentsWithUserCountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentsWithUserCountsRequest.ProtoReflect.Descriptor instead.
func (*GetSegmentsWithUserCountsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetSegmentsWithUserCountsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetSegmentsWithUserCountsRequest) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

type GetSegmentsWithUserCountsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of segment metadata with user counts
	Segments []*segment.SegmentMetadata `protobuf:"bytes,2,rep,name=segments,proto3" json:"segments,omitempty"`
	// Map of segment ID to user count
	UserCounts map[string]int64 `protobuf:"bytes,3,rep,name=user_counts,json=userCounts,proto3" json:"user_counts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetSegmentsWithUserCountsResponse) Reset() {
	*x = GetSegmentsWithUserCountsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSegmentsWithUserCountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSegmentsWithUserCountsResponse) ProtoMessage() {}

func (x *GetSegmentsWithUserCountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSegmentsWithUserCountsResponse.ProtoReflect.Descriptor instead.
func (*GetSegmentsWithUserCountsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetSegmentsWithUserCountsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSegmentsWithUserCountsResponse) GetSegments() []*segment.SegmentMetadata {
	if x != nil {
		return x.Segments
	}
	return nil
}

func (x *GetSegmentsWithUserCountsResponse) GetUserCounts() map[string]int64 {
	if x != nil {
		return x.UserCounts
	}
	return nil
}

type GetCustomerProfileResponse_BalanceRefreshData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// timestamp when last refresh of data has happened
	Timestamp string `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// last incoming transaction
	LastIncomingTransaction *GetCustomerProfileResponse_BalanceRefreshData_Transaction `protobuf:"bytes,2,opt,name=last_incoming_transaction,json=lastIncomingTransaction,proto3" json:"last_incoming_transaction,omitempty"`
	// last outgoing transaction
	LastOutgoingTransaction *GetCustomerProfileResponse_BalanceRefreshData_Transaction `protobuf:"bytes,3,opt,name=last_outgoing_transaction,json=lastOutgoingTransaction,proto3" json:"last_outgoing_transaction,omitempty"`
}

func (x *GetCustomerProfileResponse_BalanceRefreshData) Reset() {
	*x = GetCustomerProfileResponse_BalanceRefreshData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerProfileResponse_BalanceRefreshData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProfileResponse_BalanceRefreshData) ProtoMessage() {}

func (x *GetCustomerProfileResponse_BalanceRefreshData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProfileResponse_BalanceRefreshData.ProtoReflect.Descriptor instead.
func (*GetCustomerProfileResponse_BalanceRefreshData) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetCustomerProfileResponse_BalanceRefreshData) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *GetCustomerProfileResponse_BalanceRefreshData) GetLastIncomingTransaction() *GetCustomerProfileResponse_BalanceRefreshData_Transaction {
	if x != nil {
		return x.LastIncomingTransaction
	}
	return nil
}

func (x *GetCustomerProfileResponse_BalanceRefreshData) GetLastOutgoingTransaction() *GetCustomerProfileResponse_BalanceRefreshData_Transaction {
	if x != nil {
		return x.LastOutgoingTransaction
	}
	return nil
}

type GetCustomerProfileResponse_BalanceRefreshData_Transaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// amount of transaction
	Amount string `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// timestamp of transaction
	Timestamp string `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *GetCustomerProfileResponse_BalanceRefreshData_Transaction) Reset() {
	*x = GetCustomerProfileResponse_BalanceRefreshData_Transaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerProfileResponse_BalanceRefreshData_Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProfileResponse_BalanceRefreshData_Transaction) ProtoMessage() {}

func (x *GetCustomerProfileResponse_BalanceRefreshData_Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProfileResponse_BalanceRefreshData_Transaction.ProtoReflect.Descriptor instead.
func (*GetCustomerProfileResponse_BalanceRefreshData_Transaction) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *GetCustomerProfileResponse_BalanceRefreshData_Transaction) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GetCustomerProfileResponse_BalanceRefreshData_Transaction) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

type GetUserByBankInfoResponse_UserDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        *common.Name        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email       string              `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	ActorId     string              `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetUserByBankInfoResponse_UserDetails) Reset() {
	*x = GetUserByBankInfoResponse_UserDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserByBankInfoResponse_UserDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByBankInfoResponse_UserDetails) ProtoMessage() {}

func (x *GetUserByBankInfoResponse_UserDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_data_collector_profile_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByBankInfoResponse_UserDetails.ProtoReflect.Descriptor instead.
func (*GetUserByBankInfoResponse_UserDetails) Descriptor() ([]byte, []int) {
	return file_api_cx_data_collector_profile_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *GetUserByBankInfoResponse_UserDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *GetUserByBankInfoResponse_UserDetails) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *GetUserByBankInfoResponse_UserDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetUserByBankInfoResponse_UserDetails) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

var File_api_cx_data_collector_profile_service_proto protoreflect.FileDescriptor

var file_api_cx_data_collector_profile_service_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaa, 0x01, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x14, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x12, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0xb4, 0x10, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4f, 0x73, 0x12, 0x2e, 0x0a, 0x11, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x73,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x69, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x6e, 0x12, 0x32, 0x0a, 0x14, 0x69,
	0x73, 0x5f, 0x66, 0x69, 0x6e, 0x64, 0x5f, 0x6d, 0x79, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x69,
	0x73, 0x46, 0x69, 0x6e, 0x64, 0x4d, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x6e, 0x12,
	0x2f, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x64, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0f, 0x69, 0x73, 0x44, 0x75, 0x61, 0x6c, 0x53, 0x69, 0x6d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x44, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x12, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65,
	0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b,
	0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0a, 0x6c, 0x65, 0x67,
	0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x09, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x49, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x4a, 0x0a,
	0x11, 0x66, 0x69, 0x6e, 0x64, 0x5f, 0x6d, 0x79, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x66, 0x69, 0x6e, 0x64, 0x4d,
	0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x6e, 0x12, 0x47, 0x0a, 0x0f, 0x64, 0x75, 0x61,
	0x6c, 0x5f, 0x73, 0x69, 0x6d, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x64, 0x75, 0x61, 0x6c, 0x53, 0x69, 0x6d, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x4f, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65,
	0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x62, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65,
	0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x13, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6b, 0x0a, 0x18, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x16, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x5f,
	0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x69,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x67,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7a, 0x0a, 0x14, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x12, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x0c, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x69, 0x0a, 0x1f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x1c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x1a, 0x9d, 0x03, 0x0a, 0x12, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x90, 0x01, 0x0a, 0x19, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x69,
	0x6e, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x17, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x90, 0x01, 0x0a, 0x19, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6f, 0x75, 0x74, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x67, 0x6f, 0x69, 0x6e, 0x67,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x43, 0x0a, 0x0b, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x22, 0x63, 0x0a, 0x07, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x42, 0x0a, 0x0c, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x56, 0x32, 0x52, 0x0b, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x42, 0x79, 0x42, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0xd8, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x42, 0x61, 0x6e,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x63, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x42, 0x61,
	0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xb0, 0x01, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x77, 0x0a, 0x21, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x24, 0x0a,
	0x0e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0xc4, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x5a, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x78,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x53, 0x0a, 0x0d, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22,
	0xa6, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x06, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xad, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x6c, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4e, 0x0a, 0x0e, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x49, 0x44, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x07, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x71, 0x0a, 0x09, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x49, 0x44, 0x12, 0x3d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x42, 0x75,
	0x6c, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x62, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xac, 0x02, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x57,
	0x69, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x6d, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x57, 0x69, 0x74,
	0x68, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x1a,
	0x3d, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x84,
	0x07, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x12, 0xa7, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x35, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7,
	0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xa4, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x42, 0x61, 0x6e, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x33, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x42, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x42, 0x61, 0x6e,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8,
	0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49,
	0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92,
	0xe8, 0x6a, 0x00, 0x12, 0xbf, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x3c, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3d, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x6c, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e,
	0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01,
	0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x9e, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c,
	0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x63,
	0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45,
	0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a,
	0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0xbc, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x3b, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x57, 0x69, 0x74, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3c, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x57, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e,
	0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01,
	0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5a, 0x34, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_data_collector_profile_service_proto_rawDescOnce sync.Once
	file_api_cx_data_collector_profile_service_proto_rawDescData = file_api_cx_data_collector_profile_service_proto_rawDesc
)

func file_api_cx_data_collector_profile_service_proto_rawDescGZIP() []byte {
	file_api_cx_data_collector_profile_service_proto_rawDescOnce.Do(func() {
		file_api_cx_data_collector_profile_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_data_collector_profile_service_proto_rawDescData)
	})
	return file_api_cx_data_collector_profile_service_proto_rawDescData
}

var file_api_cx_data_collector_profile_service_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_api_cx_data_collector_profile_service_proto_goTypes = []interface{}{
	(*GetCustomerProfileRequest)(nil),                                 // 0: cx.data_collector.profile.GetCustomerProfileRequest
	(*GetCustomerProfileResponse)(nil),                                // 1: cx.data_collector.profile.GetCustomerProfileResponse
	(*Section)(nil),                                                   // 2: cx.data_collector.profile.Section
	(*GetUserByBankInfoRequest)(nil),                                  // 3: cx.data_collector.profile.GetUserByBankInfoRequest
	(*GetUserByBankInfoResponse)(nil),                                 // 4: cx.data_collector.profile.GetUserByBankInfoResponse
	(*GetBulkUserInfoByTicketIdsRequest)(nil),                         // 5: cx.data_collector.profile.GetBulkUserInfoByTicketIdsRequest
	(*GetBulkUserInfoByTicketIdsResponse)(nil),                        // 6: cx.data_collector.profile.GetBulkUserInfoByTicketIdsResponse
	(*TicketFailure)(nil),                                             // 7: cx.data_collector.profile.TicketFailure
	(*GetBulkUserInfoRequest)(nil),                                    // 8: cx.data_collector.profile.GetBulkUserInfoRequest
	(*GetBulkUserInfoResponse)(nil),                                   // 9: cx.data_collector.profile.GetBulkUserInfoResponse
	(*FailureID)(nil),                                                 // 10: cx.data_collector.profile.FailureID
	(*GetSegmentsWithUserCountsRequest)(nil),                          // 11: cx.data_collector.profile.GetSegmentsWithUserCountsRequest
	(*GetSegmentsWithUserCountsResponse)(nil),                         // 12: cx.data_collector.profile.GetSegmentsWithUserCountsResponse
	(*GetCustomerProfileResponse_BalanceRefreshData)(nil),             // 13: cx.data_collector.profile.GetCustomerProfileResponse.BalanceRefreshData
	(*GetCustomerProfileResponse_BalanceRefreshData_Transaction)(nil), // 14: cx.data_collector.profile.GetCustomerProfileResponse.BalanceRefreshData.Transaction
	(*GetUserByBankInfoResponse_UserDetails)(nil),                     // 15: cx.data_collector.profile.GetUserByBankInfoResponse.UserDetails
	nil,                                    // 16: cx.data_collector.profile.GetSegmentsWithUserCountsResponse.UserCountsEntry
	(*cx.Header)(nil),                      // 17: cx.Header
	(ForceBalanceUpdate)(0),                // 18: cx.data_collector.profile.ForceBalanceUpdate
	(*rpc.Status)(nil),                     // 19: rpc.Status
	(*CustomerAccount)(nil),                // 20: cx.data_collector.profile.CustomerAccount
	(*customer_auth.SherlockDeepLink)(nil), // 21: cx.customer_auth.SherlockDeepLink
	(*common.Name)(nil),                    // 22: api.typesv2.common.Name
	(common.BooleanEnum)(0),                // 23: api.typesv2.common.BooleanEnum
	(*DeviceDetails)(nil),                  // 24: cx.data_collector.profile.DeviceDetails
	(*AccessRevokeDetails)(nil),            // 25: cx.data_collector.profile.AccessRevokeDetails
	(*ExternalAccountDetails)(nil),         // 26: cx.data_collector.profile.ExternalAccountDetails
	(*webui.LabelValue)(nil),               // 27: api.typesv2.webui.LabelValue
	(*webui.LabelValueV2)(nil),             // 28: api.typesv2.webui.LabelValueV2
	(*BulkUserIdentifier)(nil),             // 29: cx.data_collector.profile.BulkUserIdentifier
	(*segment.SegmentMetadata)(nil),        // 30: segment.SegmentMetadata
	(*common.PhoneNumber)(nil),             // 31: api.typesv2.common.PhoneNumber
}
var file_api_cx_data_collector_profile_service_proto_depIdxs = []int32{
	17, // 0: cx.data_collector.profile.GetCustomerProfileRequest.header:type_name -> cx.Header
	18, // 1: cx.data_collector.profile.GetCustomerProfileRequest.force_balance_update:type_name -> cx.data_collector.profile.ForceBalanceUpdate
	19, // 2: cx.data_collector.profile.GetCustomerProfileResponse.status:type_name -> rpc.Status
	20, // 3: cx.data_collector.profile.GetCustomerProfileResponse.account:type_name -> cx.data_collector.profile.CustomerAccount
	21, // 4: cx.data_collector.profile.GetCustomerProfileResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	22, // 5: cx.data_collector.profile.GetCustomerProfileResponse.legal_name:type_name -> api.typesv2.common.Name
	23, // 6: cx.data_collector.profile.GetCustomerProfileResponse.discoverability:type_name -> api.typesv2.common.BooleanEnum
	23, // 7: cx.data_collector.profile.GetCustomerProfileResponse.find_my_device_on:type_name -> api.typesv2.common.BooleanEnum
	23, // 8: cx.data_collector.profile.GetCustomerProfileResponse.dual_sim_device:type_name -> api.typesv2.common.BooleanEnum
	24, // 9: cx.data_collector.profile.GetCustomerProfileResponse.device_details:type_name -> cx.data_collector.profile.DeviceDetails
	25, // 10: cx.data_collector.profile.GetCustomerProfileResponse.access_revoke_details:type_name -> cx.data_collector.profile.AccessRevokeDetails
	26, // 11: cx.data_collector.profile.GetCustomerProfileResponse.external_account_details:type_name -> cx.data_collector.profile.ExternalAccountDetails
	22, // 12: cx.data_collector.profile.GetCustomerProfileResponse.father_name:type_name -> api.typesv2.common.Name
	13, // 13: cx.data_collector.profile.GetCustomerProfileResponse.balance_refresh_data:type_name -> cx.data_collector.profile.GetCustomerProfileResponse.BalanceRefreshData
	27, // 14: cx.data_collector.profile.GetCustomerProfileResponse.level_values:type_name -> api.typesv2.webui.LabelValue
	2,  // 15: cx.data_collector.profile.GetCustomerProfileResponse.account_closure_request_section:type_name -> cx.data_collector.profile.Section
	28, // 16: cx.data_collector.profile.Section.label_values:type_name -> api.typesv2.webui.LabelValueV2
	17, // 17: cx.data_collector.profile.GetUserByBankInfoRequest.header:type_name -> cx.Header
	19, // 18: cx.data_collector.profile.GetUserByBankInfoResponse.status:type_name -> rpc.Status
	15, // 19: cx.data_collector.profile.GetUserByBankInfoResponse.user_details:type_name -> cx.data_collector.profile.GetUserByBankInfoResponse.UserDetails
	17, // 20: cx.data_collector.profile.GetBulkUserInfoByTicketIdsRequest.header:type_name -> cx.Header
	19, // 21: cx.data_collector.profile.GetBulkUserInfoByTicketIdsResponse.status:type_name -> rpc.Status
	7,  // 22: cx.data_collector.profile.GetBulkUserInfoByTicketIdsResponse.failed_ticket_list:type_name -> cx.data_collector.profile.TicketFailure
	17, // 23: cx.data_collector.profile.GetBulkUserInfoRequest.header:type_name -> cx.Header
	29, // 24: cx.data_collector.profile.GetBulkUserInfoRequest.id_list:type_name -> cx.data_collector.profile.BulkUserIdentifier
	19, // 25: cx.data_collector.profile.GetBulkUserInfoResponse.status:type_name -> rpc.Status
	10, // 26: cx.data_collector.profile.GetBulkUserInfoResponse.failed_id_list:type_name -> cx.data_collector.profile.FailureID
	29, // 27: cx.data_collector.profile.FailureID.id:type_name -> cx.data_collector.profile.BulkUserIdentifier
	17, // 28: cx.data_collector.profile.GetSegmentsWithUserCountsRequest.header:type_name -> cx.Header
	19, // 29: cx.data_collector.profile.GetSegmentsWithUserCountsResponse.status:type_name -> rpc.Status
	30, // 30: cx.data_collector.profile.GetSegmentsWithUserCountsResponse.segments:type_name -> segment.SegmentMetadata
	16, // 31: cx.data_collector.profile.GetSegmentsWithUserCountsResponse.user_counts:type_name -> cx.data_collector.profile.GetSegmentsWithUserCountsResponse.UserCountsEntry
	14, // 32: cx.data_collector.profile.GetCustomerProfileResponse.BalanceRefreshData.last_incoming_transaction:type_name -> cx.data_collector.profile.GetCustomerProfileResponse.BalanceRefreshData.Transaction
	14, // 33: cx.data_collector.profile.GetCustomerProfileResponse.BalanceRefreshData.last_outgoing_transaction:type_name -> cx.data_collector.profile.GetCustomerProfileResponse.BalanceRefreshData.Transaction
	22, // 34: cx.data_collector.profile.GetUserByBankInfoResponse.UserDetails.name:type_name -> api.typesv2.common.Name
	31, // 35: cx.data_collector.profile.GetUserByBankInfoResponse.UserDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	0,  // 36: cx.data_collector.profile.CustomerProfile.GetCustomerProfile:input_type -> cx.data_collector.profile.GetCustomerProfileRequest
	3,  // 37: cx.data_collector.profile.CustomerProfile.GetUserByBankInfo:input_type -> cx.data_collector.profile.GetUserByBankInfoRequest
	5,  // 38: cx.data_collector.profile.CustomerProfile.GetBulkUserInfoByTicketIds:input_type -> cx.data_collector.profile.GetBulkUserInfoByTicketIdsRequest
	8,  // 39: cx.data_collector.profile.CustomerProfile.GetBulkUserInfo:input_type -> cx.data_collector.profile.GetBulkUserInfoRequest
	11, // 40: cx.data_collector.profile.CustomerProfile.GetSegmentsWithUserCounts:input_type -> cx.data_collector.profile.GetSegmentsWithUserCountsRequest
	1,  // 41: cx.data_collector.profile.CustomerProfile.GetCustomerProfile:output_type -> cx.data_collector.profile.GetCustomerProfileResponse
	4,  // 42: cx.data_collector.profile.CustomerProfile.GetUserByBankInfo:output_type -> cx.data_collector.profile.GetUserByBankInfoResponse
	6,  // 43: cx.data_collector.profile.CustomerProfile.GetBulkUserInfoByTicketIds:output_type -> cx.data_collector.profile.GetBulkUserInfoByTicketIdsResponse
	9,  // 44: cx.data_collector.profile.CustomerProfile.GetBulkUserInfo:output_type -> cx.data_collector.profile.GetBulkUserInfoResponse
	12, // 45: cx.data_collector.profile.CustomerProfile.GetSegmentsWithUserCounts:output_type -> cx.data_collector.profile.GetSegmentsWithUserCountsResponse
	41, // [41:46] is the sub-list for method output_type
	36, // [36:41] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_api_cx_data_collector_profile_service_proto_init() }
func file_api_cx_data_collector_profile_service_proto_init() {
	if File_api_cx_data_collector_profile_service_proto != nil {
		return
	}
	file_api_cx_data_collector_profile_enums_proto_init()
	file_api_cx_data_collector_profile_profile_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_data_collector_profile_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerProfileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Section); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserByBankInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserByBankInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBulkUserInfoByTicketIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBulkUserInfoByTicketIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketFailure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBulkUserInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBulkUserInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FailureID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentsWithUserCountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSegmentsWithUserCountsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerProfileResponse_BalanceRefreshData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerProfileResponse_BalanceRefreshData_Transaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_data_collector_profile_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserByBankInfoResponse_UserDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cx_data_collector_profile_service_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*GetUserByBankInfoRequest_AccountNumber)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_data_collector_profile_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_data_collector_profile_service_proto_goTypes,
		DependencyIndexes: file_api_cx_data_collector_profile_service_proto_depIdxs,
		MessageInfos:      file_api_cx_data_collector_profile_service_proto_msgTypes,
	}.Build()
	File_api_cx_data_collector_profile_service_proto = out.File
	file_api_cx_data_collector_profile_service_proto_rawDesc = nil
	file_api_cx_data_collector_profile_service_proto_goTypes = nil
	file_api_cx_data_collector_profile_service_proto_depIdxs = nil
}
