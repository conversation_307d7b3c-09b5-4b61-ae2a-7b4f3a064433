// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/data_collector/profile/service.proto

package profile

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on GetCustomerProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerProfileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerProfileRequestMultiError, or nil if none found.
func (m *GetCustomerProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetCustomerProfileRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ForceBalanceUpdate

	if len(errors) > 0 {
		return GetCustomerProfileRequestMultiError(errors)
	}

	return nil
}

// GetCustomerProfileRequestMultiError is an error wrapping multiple validation
// errors returned by GetCustomerProfileRequest.ValidateAll() if the
// designated constraints aren't met.
type GetCustomerProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerProfileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerProfileRequestMultiError) AllErrors() []error { return m }

// GetCustomerProfileRequestValidationError is the validation error returned by
// GetCustomerProfileRequest.Validate if the designated constraints aren't met.
type GetCustomerProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerProfileRequestValidationError) ErrorName() string {
	return "GetCustomerProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerProfileRequestValidationError{}

// Validate checks the field values on GetCustomerProfileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerProfileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomerProfileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerProfileResponseMultiError, or nil if none found.
func (m *GetCustomerProfileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerProfileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AppVersion

	// no validation rules for DeviceOs

	// no validation rules for DeviceOsVersion

	// no validation rules for IsDiscoverabilityOn

	// no validation rules for IsFindMyDeviceOn

	// no validation rules for IsDualSimDevice

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSherlockDeepLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "SherlockDeepLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSherlockDeepLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "SherlockDeepLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerId

	if all {
		switch v := interface{}(m.GetLegalName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "LegalName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "LegalName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLegalName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "LegalName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Discoverability

	// no validation rules for FindMyDeviceOn

	// no validation rules for DualSimDevice

	if all {
		switch v := interface{}(m.GetDeviceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "DeviceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "DeviceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessRevokeState

	if all {
		switch v := interface{}(m.GetAccessRevokeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "AccessRevokeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "AccessRevokeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccessRevokeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "AccessRevokeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExternalAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "ExternalAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "ExternalAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExternalAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "ExternalAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentTierPlanName

	// no validation rules for ClosedAccountBalance

	// no validation rules for FiAccountStatus

	// no validation rules for SignStatus

	if all {
		switch v := interface{}(m.GetBalanceRefreshData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "BalanceRefreshData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "BalanceRefreshData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceRefreshData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "BalanceRefreshData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLevelValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCustomerProfileResponseValidationError{
						field:  fmt.Sprintf("LevelValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCustomerProfileResponseValidationError{
						field:  fmt.Sprintf("LevelValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCustomerProfileResponseValidationError{
					field:  fmt.Sprintf("LevelValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAccountClosureRequestSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "AccountClosureRequestSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponseValidationError{
					field:  "AccountClosureRequestSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountClosureRequestSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponseValidationError{
				field:  "AccountClosureRequestSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCustomerProfileResponseMultiError(errors)
	}

	return nil
}

// GetCustomerProfileResponseMultiError is an error wrapping multiple
// validation errors returned by GetCustomerProfileResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCustomerProfileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerProfileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerProfileResponseMultiError) AllErrors() []error { return m }

// GetCustomerProfileResponseValidationError is the validation error returned
// by GetCustomerProfileResponse.Validate if the designated constraints aren't met.
type GetCustomerProfileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerProfileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerProfileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomerProfileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerProfileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerProfileResponseValidationError) ErrorName() string {
	return "GetCustomerProfileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerProfileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerProfileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerProfileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerProfileResponseValidationError{}

// Validate checks the field values on Section with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Section) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Section with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SectionMultiError, or nil if none found.
func (m *Section) ValidateAll() error {
	return m.validate(true)
}

func (m *Section) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetLabelValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  fmt.Sprintf("LabelValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  fmt.Sprintf("LabelValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionValidationError{
					field:  fmt.Sprintf("LabelValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SectionMultiError(errors)
	}

	return nil
}

// SectionMultiError is an error wrapping multiple validation errors returned
// by Section.ValidateAll() if the designated constraints aren't met.
type SectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionMultiError) AllErrors() []error { return m }

// SectionValidationError is the validation error returned by Section.Validate
// if the designated constraints aren't met.
type SectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionValidationError) ErrorName() string { return "SectionValidationError" }

// Error satisfies the builtin error interface
func (e SectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionValidationError{}

// Validate checks the field values on GetUserByBankInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserByBankInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserByBankInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserByBankInfoRequestMultiError, or nil if none found.
func (m *GetUserByBankInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserByBankInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetUserByBankInfoRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserByBankInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserByBankInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserByBankInfoRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reason

	switch v := m.Identifier.(type) {
	case *GetUserByBankInfoRequest_AccountNumber:
		if v == nil {
			err := GetUserByBankInfoRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AccountNumber
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetUserByBankInfoRequestMultiError(errors)
	}

	return nil
}

// GetUserByBankInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserByBankInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserByBankInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserByBankInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserByBankInfoRequestMultiError) AllErrors() []error { return m }

// GetUserByBankInfoRequestValidationError is the validation error returned by
// GetUserByBankInfoRequest.Validate if the designated constraints aren't met.
type GetUserByBankInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserByBankInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserByBankInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserByBankInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserByBankInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserByBankInfoRequestValidationError) ErrorName() string {
	return "GetUserByBankInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserByBankInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserByBankInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserByBankInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserByBankInfoRequestValidationError{}

// Validate checks the field values on GetUserByBankInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserByBankInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserByBankInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserByBankInfoResponseMultiError, or nil if none found.
func (m *GetUserByBankInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserByBankInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserByBankInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserByBankInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserByBankInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserByBankInfoResponseValidationError{
					field:  "UserDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserByBankInfoResponseValidationError{
					field:  "UserDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserByBankInfoResponseValidationError{
				field:  "UserDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserByBankInfoResponseMultiError(errors)
	}

	return nil
}

// GetUserByBankInfoResponseMultiError is an error wrapping multiple validation
// errors returned by GetUserByBankInfoResponse.ValidateAll() if the
// designated constraints aren't met.
type GetUserByBankInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserByBankInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserByBankInfoResponseMultiError) AllErrors() []error { return m }

// GetUserByBankInfoResponseValidationError is the validation error returned by
// GetUserByBankInfoResponse.Validate if the designated constraints aren't met.
type GetUserByBankInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserByBankInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserByBankInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserByBankInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserByBankInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserByBankInfoResponseValidationError) ErrorName() string {
	return "GetUserByBankInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserByBankInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserByBankInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserByBankInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserByBankInfoResponseValidationError{}

// Validate checks the field values on GetBulkUserInfoByTicketIdsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetBulkUserInfoByTicketIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBulkUserInfoByTicketIdsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetBulkUserInfoByTicketIdsRequestMultiError, or nil if none found.
func (m *GetBulkUserInfoByTicketIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBulkUserInfoByTicketIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetBulkUserInfoByTicketIdsRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBulkUserInfoByTicketIdsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBulkUserInfoByTicketIdsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBulkUserInfoByTicketIdsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBulkUserInfoByTicketIdsRequestMultiError(errors)
	}

	return nil
}

// GetBulkUserInfoByTicketIdsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetBulkUserInfoByTicketIdsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBulkUserInfoByTicketIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBulkUserInfoByTicketIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBulkUserInfoByTicketIdsRequestMultiError) AllErrors() []error { return m }

// GetBulkUserInfoByTicketIdsRequestValidationError is the validation error
// returned by GetBulkUserInfoByTicketIdsRequest.Validate if the designated
// constraints aren't met.
type GetBulkUserInfoByTicketIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBulkUserInfoByTicketIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBulkUserInfoByTicketIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBulkUserInfoByTicketIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBulkUserInfoByTicketIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBulkUserInfoByTicketIdsRequestValidationError) ErrorName() string {
	return "GetBulkUserInfoByTicketIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBulkUserInfoByTicketIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBulkUserInfoByTicketIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBulkUserInfoByTicketIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBulkUserInfoByTicketIdsRequestValidationError{}

// Validate checks the field values on GetBulkUserInfoByTicketIdsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetBulkUserInfoByTicketIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBulkUserInfoByTicketIdsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetBulkUserInfoByTicketIdsResponseMultiError, or nil if none found.
func (m *GetBulkUserInfoByTicketIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBulkUserInfoByTicketIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBulkUserInfoByTicketIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBulkUserInfoByTicketIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBulkUserInfoByTicketIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFailedTicketList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBulkUserInfoByTicketIdsResponseValidationError{
						field:  fmt.Sprintf("FailedTicketList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBulkUserInfoByTicketIdsResponseValidationError{
						field:  fmt.Sprintf("FailedTicketList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBulkUserInfoByTicketIdsResponseValidationError{
					field:  fmt.Sprintf("FailedTicketList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FileUrl

	if len(errors) > 0 {
		return GetBulkUserInfoByTicketIdsResponseMultiError(errors)
	}

	return nil
}

// GetBulkUserInfoByTicketIdsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetBulkUserInfoByTicketIdsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetBulkUserInfoByTicketIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBulkUserInfoByTicketIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBulkUserInfoByTicketIdsResponseMultiError) AllErrors() []error { return m }

// GetBulkUserInfoByTicketIdsResponseValidationError is the validation error
// returned by GetBulkUserInfoByTicketIdsResponse.Validate if the designated
// constraints aren't met.
type GetBulkUserInfoByTicketIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBulkUserInfoByTicketIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBulkUserInfoByTicketIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBulkUserInfoByTicketIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBulkUserInfoByTicketIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBulkUserInfoByTicketIdsResponseValidationError) ErrorName() string {
	return "GetBulkUserInfoByTicketIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBulkUserInfoByTicketIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBulkUserInfoByTicketIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBulkUserInfoByTicketIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBulkUserInfoByTicketIdsResponseValidationError{}

// Validate checks the field values on TicketFailure with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TicketFailure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TicketFailure with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TicketFailureMultiError, or
// nil if none found.
func (m *TicketFailure) ValidateAll() error {
	return m.validate(true)
}

func (m *TicketFailure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return TicketFailureMultiError(errors)
	}

	return nil
}

// TicketFailureMultiError is an error wrapping multiple validation errors
// returned by TicketFailure.ValidateAll() if the designated constraints
// aren't met.
type TicketFailureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TicketFailureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TicketFailureMultiError) AllErrors() []error { return m }

// TicketFailureValidationError is the validation error returned by
// TicketFailure.Validate if the designated constraints aren't met.
type TicketFailureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TicketFailureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TicketFailureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TicketFailureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TicketFailureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TicketFailureValidationError) ErrorName() string { return "TicketFailureValidationError" }

// Error satisfies the builtin error interface
func (e TicketFailureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTicketFailure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TicketFailureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TicketFailureValidationError{}

// Validate checks the field values on GetBulkUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBulkUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBulkUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBulkUserInfoRequestMultiError, or nil if none found.
func (m *GetBulkUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBulkUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetBulkUserInfoRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBulkUserInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBulkUserInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBulkUserInfoRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIdList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBulkUserInfoRequestValidationError{
						field:  fmt.Sprintf("IdList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBulkUserInfoRequestValidationError{
						field:  fmt.Sprintf("IdList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBulkUserInfoRequestValidationError{
					field:  fmt.Sprintf("IdList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Reason

	if len(errors) > 0 {
		return GetBulkUserInfoRequestMultiError(errors)
	}

	return nil
}

// GetBulkUserInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetBulkUserInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBulkUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBulkUserInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBulkUserInfoRequestMultiError) AllErrors() []error { return m }

// GetBulkUserInfoRequestValidationError is the validation error returned by
// GetBulkUserInfoRequest.Validate if the designated constraints aren't met.
type GetBulkUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBulkUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBulkUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBulkUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBulkUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBulkUserInfoRequestValidationError) ErrorName() string {
	return "GetBulkUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBulkUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBulkUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBulkUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBulkUserInfoRequestValidationError{}

// Validate checks the field values on GetBulkUserInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBulkUserInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBulkUserInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBulkUserInfoResponseMultiError, or nil if none found.
func (m *GetBulkUserInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBulkUserInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBulkUserInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBulkUserInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBulkUserInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFailedIdList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBulkUserInfoResponseValidationError{
						field:  fmt.Sprintf("FailedIdList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBulkUserInfoResponseValidationError{
						field:  fmt.Sprintf("FailedIdList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBulkUserInfoResponseValidationError{
					field:  fmt.Sprintf("FailedIdList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FileUrl

	if len(errors) > 0 {
		return GetBulkUserInfoResponseMultiError(errors)
	}

	return nil
}

// GetBulkUserInfoResponseMultiError is an error wrapping multiple validation
// errors returned by GetBulkUserInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetBulkUserInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBulkUserInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBulkUserInfoResponseMultiError) AllErrors() []error { return m }

// GetBulkUserInfoResponseValidationError is the validation error returned by
// GetBulkUserInfoResponse.Validate if the designated constraints aren't met.
type GetBulkUserInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBulkUserInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBulkUserInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBulkUserInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBulkUserInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBulkUserInfoResponseValidationError) ErrorName() string {
	return "GetBulkUserInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBulkUserInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBulkUserInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBulkUserInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBulkUserInfoResponseValidationError{}

// Validate checks the field values on FailureID with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FailureID) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FailureID with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FailureIDMultiError, or nil
// if none found.
func (m *FailureID) ValidateAll() error {
	return m.validate(true)
}

func (m *FailureID) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetId()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FailureIDValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FailureIDValidationError{
					field:  "Id",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetId()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FailureIDValidationError{
				field:  "Id",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return FailureIDMultiError(errors)
	}

	return nil
}

// FailureIDMultiError is an error wrapping multiple validation errors returned
// by FailureID.ValidateAll() if the designated constraints aren't met.
type FailureIDMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FailureIDMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FailureIDMultiError) AllErrors() []error { return m }

// FailureIDValidationError is the validation error returned by
// FailureID.Validate if the designated constraints aren't met.
type FailureIDValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FailureIDValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FailureIDValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FailureIDValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FailureIDValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FailureIDValidationError) ErrorName() string { return "FailureIDValidationError" }

// Error satisfies the builtin error interface
func (e FailureIDValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFailureID.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FailureIDValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FailureIDValidationError{}

// Validate checks the field values on GetSegmentsWithUserCountsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSegmentsWithUserCountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSegmentsWithUserCountsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSegmentsWithUserCountsRequestMultiError, or nil if none found.
func (m *GetSegmentsWithUserCountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSegmentsWithUserCountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSegmentsWithUserCountsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSegmentsWithUserCountsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSegmentsWithUserCountsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Duration

	if len(errors) > 0 {
		return GetSegmentsWithUserCountsRequestMultiError(errors)
	}

	return nil
}

// GetSegmentsWithUserCountsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSegmentsWithUserCountsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSegmentsWithUserCountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSegmentsWithUserCountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSegmentsWithUserCountsRequestMultiError) AllErrors() []error { return m }

// GetSegmentsWithUserCountsRequestValidationError is the validation error
// returned by GetSegmentsWithUserCountsRequest.Validate if the designated
// constraints aren't met.
type GetSegmentsWithUserCountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSegmentsWithUserCountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSegmentsWithUserCountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSegmentsWithUserCountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSegmentsWithUserCountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSegmentsWithUserCountsRequestValidationError) ErrorName() string {
	return "GetSegmentsWithUserCountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSegmentsWithUserCountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSegmentsWithUserCountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSegmentsWithUserCountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSegmentsWithUserCountsRequestValidationError{}

// Validate checks the field values on GetSegmentsWithUserCountsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSegmentsWithUserCountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSegmentsWithUserCountsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSegmentsWithUserCountsResponseMultiError, or nil if none found.
func (m *GetSegmentsWithUserCountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSegmentsWithUserCountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSegmentsWithUserCountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSegmentsWithUserCountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSegmentsWithUserCountsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSegments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSegmentsWithUserCountsResponseValidationError{
						field:  fmt.Sprintf("Segments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSegmentsWithUserCountsResponseValidationError{
						field:  fmt.Sprintf("Segments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSegmentsWithUserCountsResponseValidationError{
					field:  fmt.Sprintf("Segments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UserCounts

	if len(errors) > 0 {
		return GetSegmentsWithUserCountsResponseMultiError(errors)
	}

	return nil
}

// GetSegmentsWithUserCountsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSegmentsWithUserCountsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSegmentsWithUserCountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSegmentsWithUserCountsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSegmentsWithUserCountsResponseMultiError) AllErrors() []error { return m }

// GetSegmentsWithUserCountsResponseValidationError is the validation error
// returned by GetSegmentsWithUserCountsResponse.Validate if the designated
// constraints aren't met.
type GetSegmentsWithUserCountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSegmentsWithUserCountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSegmentsWithUserCountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSegmentsWithUserCountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSegmentsWithUserCountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSegmentsWithUserCountsResponseValidationError) ErrorName() string {
	return "GetSegmentsWithUserCountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSegmentsWithUserCountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSegmentsWithUserCountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSegmentsWithUserCountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSegmentsWithUserCountsResponseValidationError{}

// Validate checks the field values on
// GetCustomerProfileResponse_BalanceRefreshData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetCustomerProfileResponse_BalanceRefreshData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCustomerProfileResponse_BalanceRefreshData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetCustomerProfileResponse_BalanceRefreshDataMultiError, or nil if none found.
func (m *GetCustomerProfileResponse_BalanceRefreshData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerProfileResponse_BalanceRefreshData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Timestamp

	if all {
		switch v := interface{}(m.GetLastIncomingTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponse_BalanceRefreshDataValidationError{
					field:  "LastIncomingTransaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponse_BalanceRefreshDataValidationError{
					field:  "LastIncomingTransaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastIncomingTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponse_BalanceRefreshDataValidationError{
				field:  "LastIncomingTransaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastOutgoingTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomerProfileResponse_BalanceRefreshDataValidationError{
					field:  "LastOutgoingTransaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomerProfileResponse_BalanceRefreshDataValidationError{
					field:  "LastOutgoingTransaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastOutgoingTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomerProfileResponse_BalanceRefreshDataValidationError{
				field:  "LastOutgoingTransaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCustomerProfileResponse_BalanceRefreshDataMultiError(errors)
	}

	return nil
}

// GetCustomerProfileResponse_BalanceRefreshDataMultiError is an error wrapping
// multiple validation errors returned by
// GetCustomerProfileResponse_BalanceRefreshData.ValidateAll() if the
// designated constraints aren't met.
type GetCustomerProfileResponse_BalanceRefreshDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerProfileResponse_BalanceRefreshDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerProfileResponse_BalanceRefreshDataMultiError) AllErrors() []error { return m }

// GetCustomerProfileResponse_BalanceRefreshDataValidationError is the
// validation error returned by
// GetCustomerProfileResponse_BalanceRefreshData.Validate if the designated
// constraints aren't met.
type GetCustomerProfileResponse_BalanceRefreshDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerProfileResponse_BalanceRefreshDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomerProfileResponse_BalanceRefreshDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCustomerProfileResponse_BalanceRefreshDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomerProfileResponse_BalanceRefreshDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomerProfileResponse_BalanceRefreshDataValidationError) ErrorName() string {
	return "GetCustomerProfileResponse_BalanceRefreshDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerProfileResponse_BalanceRefreshDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerProfileResponse_BalanceRefreshData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerProfileResponse_BalanceRefreshDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerProfileResponse_BalanceRefreshDataValidationError{}

// Validate checks the field values on
// GetCustomerProfileResponse_BalanceRefreshData_Transaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomerProfileResponse_BalanceRefreshData_Transaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCustomerProfileResponse_BalanceRefreshData_Transaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomerProfileResponse_BalanceRefreshData_TransactionMultiError, or nil
// if none found.
func (m *GetCustomerProfileResponse_BalanceRefreshData_Transaction) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomerProfileResponse_BalanceRefreshData_Transaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amount

	// no validation rules for Timestamp

	if len(errors) > 0 {
		return GetCustomerProfileResponse_BalanceRefreshData_TransactionMultiError(errors)
	}

	return nil
}

// GetCustomerProfileResponse_BalanceRefreshData_TransactionMultiError is an
// error wrapping multiple validation errors returned by
// GetCustomerProfileResponse_BalanceRefreshData_Transaction.ValidateAll() if
// the designated constraints aren't met.
type GetCustomerProfileResponse_BalanceRefreshData_TransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomerProfileResponse_BalanceRefreshData_TransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomerProfileResponse_BalanceRefreshData_TransactionMultiError) AllErrors() []error {
	return m
}

// GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError is
// the validation error returned by
// GetCustomerProfileResponse_BalanceRefreshData_Transaction.Validate if the
// designated constraints aren't met.
type GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError) ErrorName() string {
	return "GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomerProfileResponse_BalanceRefreshData_Transaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomerProfileResponse_BalanceRefreshData_TransactionValidationError{}

// Validate checks the field values on GetUserByBankInfoResponse_UserDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetUserByBankInfoResponse_UserDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserByBankInfoResponse_UserDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetUserByBankInfoResponse_UserDetailsMultiError, or nil if none found.
func (m *GetUserByBankInfoResponse_UserDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserByBankInfoResponse_UserDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserByBankInfoResponse_UserDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserByBankInfoResponse_UserDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserByBankInfoResponse_UserDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserByBankInfoResponse_UserDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserByBankInfoResponse_UserDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserByBankInfoResponse_UserDetailsValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetUserByBankInfoResponse_UserDetailsMultiError(errors)
	}

	return nil
}

// GetUserByBankInfoResponse_UserDetailsMultiError is an error wrapping
// multiple validation errors returned by
// GetUserByBankInfoResponse_UserDetails.ValidateAll() if the designated
// constraints aren't met.
type GetUserByBankInfoResponse_UserDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserByBankInfoResponse_UserDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserByBankInfoResponse_UserDetailsMultiError) AllErrors() []error { return m }

// GetUserByBankInfoResponse_UserDetailsValidationError is the validation error
// returned by GetUserByBankInfoResponse_UserDetails.Validate if the
// designated constraints aren't met.
type GetUserByBankInfoResponse_UserDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserByBankInfoResponse_UserDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserByBankInfoResponse_UserDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserByBankInfoResponse_UserDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserByBankInfoResponse_UserDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserByBankInfoResponse_UserDetailsValidationError) ErrorName() string {
	return "GetUserByBankInfoResponse_UserDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserByBankInfoResponse_UserDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserByBankInfoResponse_UserDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserByBankInfoResponse_UserDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserByBankInfoResponse_UserDetailsValidationError{}
