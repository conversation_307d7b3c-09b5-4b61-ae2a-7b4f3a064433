// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package cx.data_collector.profile;

import "api/cx/customer_auth/customer_auth.proto";
import "api/cx/data_collector/profile/enums.proto";
import "api/cx/data_collector/profile/profile.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/segment/segment.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/webui/text.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/profile";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.profile";

service CustomerProfile {
  // RPC to fetch customer profile
  // The service will return error if user is not found in the system
  rpc GetCustomerProfile (GetCustomerProfileRequest) returns (GetCustomerProfileResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc method to get fi users detail using account number of the user
  // will be used by biz devs to identify a user internally given the account number
  // will return code
  // OK for success
  // INTERNAL for server errors
  // NOT_FOUND if user is not found
  // INVALID_ARGUMENT if the mandatory parameters are missing
  rpc GetUserByBankInfo (GetUserByBankInfoRequest) returns (GetUserByBankInfoResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc method to get fi users detail in bulk using list of ticket ids
  // will be used by admins to get user details in bulk for a list of ticket ids
  // will return code
  // OK for success
  // INTERNAL for server errors
  // INVALID_ARGUMENT if the ticket list is empty
  // will return an s3 signed url for the csv file containing the user details
  rpc GetBulkUserInfoByTicketIds (GetBulkUserInfoByTicketIdsRequest) returns (GetBulkUserInfoByTicketIdsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc method to get fi users details(email IDs, FCM IDs) in bulk using list of Customer ID/ Acc ID/ Actor ID
  // will be used by admins to get user details in bulk for a list of Customer ID/ Acc ID/ Actor ID
  // will return code:
  // OK for success - even if details for some ids not found(returned in failed_id_list)
  // INTERNAL for server errors
  // INVALID_ARGUMENT if the list is empty or greater than max threshold limit
  // will return an s3 signed url for the csv file containing the user details
  // Also send email to the one who requested
  rpc GetBulkUserInfo (GetBulkUserInfoRequest) returns (GetBulkUserInfoResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

    // Get all segments with their user counts
  rpc GetSegmentsWithUserCounts (GetSegmentsWithUserCountsRequest) returns (GetSegmentsWithUserCountsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
}
}

// Ticket id -> Mandatory
// Agent email -> Mandatory
// Access token -> Mandatory
// Identifier -> Optional (case by case basis)
message GetCustomerProfileRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  // enum to let the backend know if the balance must be updated or not
  // force_balance_update must be passed empty if no balance update is needed
  cx.data_collector.profile.ForceBalanceUpdate force_balance_update = 2;
}

message GetCustomerProfileResponse {
  rpc.Status status = 1;

  string app_version = 2 [deprecated = true];

  string device_os = 3 [deprecated = true];

  string device_os_version = 4 [deprecated = true];

  // deprecated
  bool is_discoverability_on = 5 [deprecated = true];

  // deprecated
  bool is_find_my_device_on = 6 [deprecated = true];

  // deprecated
  bool is_dual_sim_device = 7 [deprecated = true];

  profile.CustomerAccount account = 8;

  customer_auth.SherlockDeepLink sherlock_deep_link = 9;

  string customer_id = 10;

  api.typesv2.common.Name legal_name = 11;

  api.typesv2.common.BooleanEnum discoverability = 12;

  api.typesv2.common.BooleanEnum find_my_device_on = 13;

  api.typesv2.common.BooleanEnum dual_sim_device = 14;

  DeviceDetails device_details = 15;

  string access_revoke_state = 16;

  AccessRevokeDetails access_revoke_details = 17;

  ExternalAccountDetails external_account_details = 18;

  api.typesv2.common.Name father_name = 19;
  // Display string for current tier
  string current_tier_plan_name = 20;

  // balance that was present in the account during closure as reported by federal.
  // main use case is for min-kyc expired accounts
  string closed_account_balance = 21;

  // weather fi account (SA or fi-lite) has been created or not
  string fi_account_status = 22;

  // sign_status represent whether signature present at bank or not
  string sign_status = 23;

  message BalanceRefreshData {
    // timestamp when last refresh of data has happened
    string timestamp = 1;
    message Transaction {
      // amount of transaction
      string amount = 1;
      // timestamp of transaction
      string timestamp = 2;
    }
    // last incoming transaction
    Transaction last_incoming_transaction = 2;
    // last outgoing transaction
    Transaction last_outgoing_transaction = 3;
  }

  // balance refresh data
  BalanceRefreshData balance_refresh_data = 24;
  repeated api.typesv2.webui.LabelValue level_values = 25;

  // section to list savings account closure requests created by user
  Section account_closure_request_section = 26;
}

message Section {
  string title = 1;
  repeated api.typesv2.webui.LabelValueV2 label_values = 2;
}

message GetUserByBankInfoRequest {
  // agent email and access token is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  string reason = 2;

  // one of the identifier is mandatory
  oneof identifier {
    // account number of the user
    string account_number = 3;
  }

}

message GetUserByBankInfoResponse {
  // will return code
  // INTERNAL for server errors
  // NOT_FOUND if user is not found
  // INVALID_ARGUMENT if the mandatory parameters are missing
  rpc.Status status = 1;

  message UserDetails {
    api.typesv2.common.Name name = 1;

    api.typesv2.common.PhoneNumber phone_number = 2;

    string email = 3;

    string actor_id = 4;
  }

  UserDetails user_details = 2;
}

message GetBulkUserInfoByTicketIdsRequest {
  // agent email and access token is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
  // list of ticket ids for which user information is needed
  repeated int64 ticket_id_list = 2;
}

message GetBulkUserInfoByTicketIdsResponse {
  // will return code
  // OK for success
  // INTERNAL for server errors
  // INVALID_ARGUMENT if the mandatory parameters are missing
  rpc.Status status = 1;

  // list of tickets for which we were not able to fetch user information along with failure reason
  // this field is deprecated in favour of async processing and sending details via email
  repeated TicketFailure failed_ticket_list = 2 [deprecated = true];

  // s3 signed url for csv file with the user details
  // this field is deprecated in favour of async processing and sending details via email
  string file_url = 3 [deprecated = true];
}

message TicketFailure {
  int64 ticket_id = 1;

  string failure_reason = 2;
}

message GetBulkUserInfoRequest {
  // agent email and access token is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // list of ids for which user information is needed
  // a combination of different IDs(eg: {0-AccountId, 1-ActorId} is also supported
  repeated BulkUserIdentifier id_list = 2;

  string reason = 3;
}

message GetBulkUserInfoResponse {
  // will return code
  // OK for success - even if details for some ids not found(returned in failed_id_list)
  // INTERNAL for server errors
  // INVALID_ARGUMENT if the list is empty or greater than max threshold limit
  rpc.Status status = 1;

  // list of ids for which we were not able to fetch user information along with failure reason
  // this field is deprecated in favour of async processing and sending details via email
  repeated FailureID failed_id_list = 2 [deprecated = true];

  // s3 signed url for csv file with the user details
  // this field is deprecated in favour of async processing and sending details via email
  string file_url = 3 [deprecated = true];
}

message FailureID {
  // the ID for which we were not able to fetch the required user information
  BulkUserIdentifier id = 1;

  // reason for the failure for this ID (eg: unable to fetch account details)
  string failure_reason = 2;
}

// Segment related messages
message GetSegmentsWithUserCountsRequest {
  cx.Header header = 1;
  // Duration from which to fetch segments (ISO timestamp)
  string duration = 2;
}

message GetSegmentsWithUserCountsResponse {
  rpc.Status status = 1;
  // List of segment metadata with user counts
  repeated segment.SegmentMetadata segments = 2;
  // Map of segment ID to user count
  map<string, int64> user_counts = 3;
}
