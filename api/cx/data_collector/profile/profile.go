package profile

import (
	"github.com/golang/protobuf/jsonpb"
	"go.uber.org/zap"

	alPb "github.com/epifi/gamma/api/cx/audit_log"
	"github.com/epifi/be-common/pkg/logger"
)

func (m *GetCustomerProfileRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetCustomerProfileRequest) GetObject() alPb.Object {
	return alPb.Object_PROFILE
}

func (m *GetUserByBankInfoRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetUserByBankInfoRequest) GetObject() alPb.Object {
	return alPb.Object_USER
}

func (m *GetUserByBankInfoRequest) GetParametersJson() string {
	marshaller := jsonpb.Marshaler{EmitDefaults: true, EnumsAsInts: false}
	params := &UserDetailsParamsForLogging{
		Reason: m.GetReason(),
	}
	s, err := marshaller.MarshalToString(params)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal get user details for biz admins request params", zap.Error(err))
	}
	return s
}

func (m *GetBulkUserInfoByTicketIdsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH_BULK
}

func (m *GetBulkUserInfoByTicketIdsRequest) GetObject() alPb.Object {
	return alPb.Object_USER
}

func (m *GetBulkUserInfoByTicketIdsRequest) GetParametersJson() string {
	marshaller := jsonpb.Marshaler{EmitDefaults: true, EnumsAsInts: false}
	params := &BulkUserDetailsParamsForLogging{
		TicketIdList: m.GetTicketIdList(),
	}
	s, err := marshaller.MarshalToString(params)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal bulk user details params", zap.Error(err))
	}
	return s
}

func (m *GetBulkUserInfoRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH_BULK
}

func (m *GetBulkUserInfoRequest) GetObject() alPb.Object {
	return alPb.Object_USER
}

func (m *GetBulkUserInfoRequest) GetParametersJson() string {
	marshaller := jsonpb.Marshaler{EmitDefaults: true, EnumsAsInts: false}
	params := &BulkUserDetailsIdParamsForLogging{
		IdList: m.GetIdList(),
		Reason: m.GetReason(),
	}
	s, err := marshaller.MarshalToString(params)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal bulk user details params", zap.Error(err))
	}
	return s
}

// Implement RequestWithLogging interface for GetSegmentsWithUserCountsRequest
func (m *GetSegmentsWithUserCountsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetSegmentsWithUserCountsRequest) GetObject() alPb.Object {
	return alPb.Object_PROFILE
}
