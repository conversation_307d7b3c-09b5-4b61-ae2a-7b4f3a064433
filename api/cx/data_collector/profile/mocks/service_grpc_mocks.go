// Code generated by MockGen. DO NOT EDIT.
// Source: api/cx/data_collector/profile/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	profile "github.com/epifi/gamma/api/cx/data_collector/profile"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCustomerProfileClient is a mock of CustomerProfileClient interface.
type MockCustomerProfileClient struct {
	ctrl     *gomock.Controller
	recorder *MockCustomerProfileClientMockRecorder
}

// MockCustomerProfileClientMockRecorder is the mock recorder for MockCustomerProfileClient.
type MockCustomerProfileClientMockRecorder struct {
	mock *MockCustomerProfileClient
}

// NewMockCustomerProfileClient creates a new mock instance.
func NewMockCustomerProfileClient(ctrl *gomock.Controller) *MockCustomerProfileClient {
	mock := &MockCustomerProfileClient{ctrl: ctrl}
	mock.recorder = &MockCustomerProfileClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCustomerProfileClient) EXPECT() *MockCustomerProfileClientMockRecorder {
	return m.recorder
}

// GetBulkUserInfo mocks base method.
func (m *MockCustomerProfileClient) GetBulkUserInfo(ctx context.Context, in *profile.GetBulkUserInfoRequest, opts ...grpc.CallOption) (*profile.GetBulkUserInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBulkUserInfo", varargs...)
	ret0, _ := ret[0].(*profile.GetBulkUserInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkUserInfo indicates an expected call of GetBulkUserInfo.
func (mr *MockCustomerProfileClientMockRecorder) GetBulkUserInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkUserInfo", reflect.TypeOf((*MockCustomerProfileClient)(nil).GetBulkUserInfo), varargs...)
}

// GetBulkUserInfoByTicketIds mocks base method.
func (m *MockCustomerProfileClient) GetBulkUserInfoByTicketIds(ctx context.Context, in *profile.GetBulkUserInfoByTicketIdsRequest, opts ...grpc.CallOption) (*profile.GetBulkUserInfoByTicketIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBulkUserInfoByTicketIds", varargs...)
	ret0, _ := ret[0].(*profile.GetBulkUserInfoByTicketIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkUserInfoByTicketIds indicates an expected call of GetBulkUserInfoByTicketIds.
func (mr *MockCustomerProfileClientMockRecorder) GetBulkUserInfoByTicketIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkUserInfoByTicketIds", reflect.TypeOf((*MockCustomerProfileClient)(nil).GetBulkUserInfoByTicketIds), varargs...)
}

// GetCustomerProfile mocks base method.
func (m *MockCustomerProfileClient) GetCustomerProfile(ctx context.Context, in *profile.GetCustomerProfileRequest, opts ...grpc.CallOption) (*profile.GetCustomerProfileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCustomerProfile", varargs...)
	ret0, _ := ret[0].(*profile.GetCustomerProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerProfile indicates an expected call of GetCustomerProfile.
func (mr *MockCustomerProfileClientMockRecorder) GetCustomerProfile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerProfile", reflect.TypeOf((*MockCustomerProfileClient)(nil).GetCustomerProfile), varargs...)
}

// GetSegmentsWithUserCounts mocks base method.
func (m *MockCustomerProfileClient) GetSegmentsWithUserCounts(ctx context.Context, in *profile.GetSegmentsWithUserCountsRequest, opts ...grpc.CallOption) (*profile.GetSegmentsWithUserCountsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSegmentsWithUserCounts", varargs...)
	ret0, _ := ret[0].(*profile.GetSegmentsWithUserCountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSegmentsWithUserCounts indicates an expected call of GetSegmentsWithUserCounts.
func (mr *MockCustomerProfileClientMockRecorder) GetSegmentsWithUserCounts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSegmentsWithUserCounts", reflect.TypeOf((*MockCustomerProfileClient)(nil).GetSegmentsWithUserCounts), varargs...)
}

// GetUserByBankInfo mocks base method.
func (m *MockCustomerProfileClient) GetUserByBankInfo(ctx context.Context, in *profile.GetUserByBankInfoRequest, opts ...grpc.CallOption) (*profile.GetUserByBankInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserByBankInfo", varargs...)
	ret0, _ := ret[0].(*profile.GetUserByBankInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserByBankInfo indicates an expected call of GetUserByBankInfo.
func (mr *MockCustomerProfileClientMockRecorder) GetUserByBankInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByBankInfo", reflect.TypeOf((*MockCustomerProfileClient)(nil).GetUserByBankInfo), varargs...)
}

// MockCustomerProfileServer is a mock of CustomerProfileServer interface.
type MockCustomerProfileServer struct {
	ctrl     *gomock.Controller
	recorder *MockCustomerProfileServerMockRecorder
}

// MockCustomerProfileServerMockRecorder is the mock recorder for MockCustomerProfileServer.
type MockCustomerProfileServerMockRecorder struct {
	mock *MockCustomerProfileServer
}

// NewMockCustomerProfileServer creates a new mock instance.
func NewMockCustomerProfileServer(ctrl *gomock.Controller) *MockCustomerProfileServer {
	mock := &MockCustomerProfileServer{ctrl: ctrl}
	mock.recorder = &MockCustomerProfileServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCustomerProfileServer) EXPECT() *MockCustomerProfileServerMockRecorder {
	return m.recorder
}

// GetBulkUserInfo mocks base method.
func (m *MockCustomerProfileServer) GetBulkUserInfo(arg0 context.Context, arg1 *profile.GetBulkUserInfoRequest) (*profile.GetBulkUserInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBulkUserInfo", arg0, arg1)
	ret0, _ := ret[0].(*profile.GetBulkUserInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkUserInfo indicates an expected call of GetBulkUserInfo.
func (mr *MockCustomerProfileServerMockRecorder) GetBulkUserInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkUserInfo", reflect.TypeOf((*MockCustomerProfileServer)(nil).GetBulkUserInfo), arg0, arg1)
}

// GetBulkUserInfoByTicketIds mocks base method.
func (m *MockCustomerProfileServer) GetBulkUserInfoByTicketIds(arg0 context.Context, arg1 *profile.GetBulkUserInfoByTicketIdsRequest) (*profile.GetBulkUserInfoByTicketIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBulkUserInfoByTicketIds", arg0, arg1)
	ret0, _ := ret[0].(*profile.GetBulkUserInfoByTicketIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkUserInfoByTicketIds indicates an expected call of GetBulkUserInfoByTicketIds.
func (mr *MockCustomerProfileServerMockRecorder) GetBulkUserInfoByTicketIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkUserInfoByTicketIds", reflect.TypeOf((*MockCustomerProfileServer)(nil).GetBulkUserInfoByTicketIds), arg0, arg1)
}

// GetCustomerProfile mocks base method.
func (m *MockCustomerProfileServer) GetCustomerProfile(arg0 context.Context, arg1 *profile.GetCustomerProfileRequest) (*profile.GetCustomerProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerProfile", arg0, arg1)
	ret0, _ := ret[0].(*profile.GetCustomerProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerProfile indicates an expected call of GetCustomerProfile.
func (mr *MockCustomerProfileServerMockRecorder) GetCustomerProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerProfile", reflect.TypeOf((*MockCustomerProfileServer)(nil).GetCustomerProfile), arg0, arg1)
}

// GetSegmentsWithUserCounts mocks base method.
func (m *MockCustomerProfileServer) GetSegmentsWithUserCounts(arg0 context.Context, arg1 *profile.GetSegmentsWithUserCountsRequest) (*profile.GetSegmentsWithUserCountsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSegmentsWithUserCounts", arg0, arg1)
	ret0, _ := ret[0].(*profile.GetSegmentsWithUserCountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSegmentsWithUserCounts indicates an expected call of GetSegmentsWithUserCounts.
func (mr *MockCustomerProfileServerMockRecorder) GetSegmentsWithUserCounts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSegmentsWithUserCounts", reflect.TypeOf((*MockCustomerProfileServer)(nil).GetSegmentsWithUserCounts), arg0, arg1)
}

// GetUserByBankInfo mocks base method.
func (m *MockCustomerProfileServer) GetUserByBankInfo(arg0 context.Context, arg1 *profile.GetUserByBankInfoRequest) (*profile.GetUserByBankInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByBankInfo", arg0, arg1)
	ret0, _ := ret[0].(*profile.GetUserByBankInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserByBankInfo indicates an expected call of GetUserByBankInfo.
func (mr *MockCustomerProfileServerMockRecorder) GetUserByBankInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByBankInfo", reflect.TypeOf((*MockCustomerProfileServer)(nil).GetUserByBankInfo), arg0, arg1)
}

// MockUnsafeCustomerProfileServer is a mock of UnsafeCustomerProfileServer interface.
type MockUnsafeCustomerProfileServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCustomerProfileServerMockRecorder
}

// MockUnsafeCustomerProfileServerMockRecorder is the mock recorder for MockUnsafeCustomerProfileServer.
type MockUnsafeCustomerProfileServerMockRecorder struct {
	mock *MockUnsafeCustomerProfileServer
}

// NewMockUnsafeCustomerProfileServer creates a new mock instance.
func NewMockUnsafeCustomerProfileServer(ctrl *gomock.Controller) *MockUnsafeCustomerProfileServer {
	mock := &MockUnsafeCustomerProfileServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCustomerProfileServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCustomerProfileServer) EXPECT() *MockUnsafeCustomerProfileServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCustomerProfileServer mocks base method.
func (m *MockUnsafeCustomerProfileServer) mustEmbedUnimplementedCustomerProfileServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCustomerProfileServer")
}

// mustEmbedUnimplementedCustomerProfileServer indicates an expected call of mustEmbedUnimplementedCustomerProfileServer.
func (mr *MockUnsafeCustomerProfileServerMockRecorder) mustEmbedUnimplementedCustomerProfileServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCustomerProfileServer", reflect.TypeOf((*MockUnsafeCustomerProfileServer)(nil).mustEmbedUnimplementedCustomerProfileServer))
}
