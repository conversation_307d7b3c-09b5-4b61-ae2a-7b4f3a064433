// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/nudge_parser/service.proto

package nudge_parser

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ParseNudgesFromExcelRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ParseNudgesFromExcelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParseNudgesFromExcelRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ParseNudgesFromExcelRequestMultiError, or nil if none found.
func (m *ParseNudgesFromExcelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ParseNudgesFromExcelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParseNudgesFromExcelRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParseNudgesFromExcelRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParseNudgesFromExcelRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExcelFileContent

	// no validation rules for SheetName

	// no validation rules for CreatedBy

	if len(errors) > 0 {
		return ParseNudgesFromExcelRequestMultiError(errors)
	}

	return nil
}

// ParseNudgesFromExcelRequestMultiError is an error wrapping multiple
// validation errors returned by ParseNudgesFromExcelRequest.ValidateAll() if
// the designated constraints aren't met.
type ParseNudgesFromExcelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParseNudgesFromExcelRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParseNudgesFromExcelRequestMultiError) AllErrors() []error { return m }

// ParseNudgesFromExcelRequestValidationError is the validation error returned
// by ParseNudgesFromExcelRequest.Validate if the designated constraints
// aren't met.
type ParseNudgesFromExcelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParseNudgesFromExcelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParseNudgesFromExcelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParseNudgesFromExcelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParseNudgesFromExcelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParseNudgesFromExcelRequestValidationError) ErrorName() string {
	return "ParseNudgesFromExcelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ParseNudgesFromExcelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParseNudgesFromExcelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParseNudgesFromExcelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParseNudgesFromExcelRequestValidationError{}

// Validate checks the field values on ParseNudgesFromExcelResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ParseNudgesFromExcelResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParseNudgesFromExcelResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ParseNudgesFromExcelResponseMultiError, or nil if none found.
func (m *ParseNudgesFromExcelResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ParseNudgesFromExcelResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParseNudgesFromExcelResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParseNudgesFromExcelResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParseNudgesFromExcelResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetParsedNudges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ParseNudgesFromExcelResponseValidationError{
						field:  fmt.Sprintf("ParsedNudges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ParseNudgesFromExcelResponseValidationError{
						field:  fmt.Sprintf("ParsedNudges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ParseNudgesFromExcelResponseValidationError{
					field:  fmt.Sprintf("ParsedNudges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ParseNudgesFromExcelResponseMultiError(errors)
	}

	return nil
}

// ParseNudgesFromExcelResponseMultiError is an error wrapping multiple
// validation errors returned by ParseNudgesFromExcelResponse.ValidateAll() if
// the designated constraints aren't met.
type ParseNudgesFromExcelResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParseNudgesFromExcelResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParseNudgesFromExcelResponseMultiError) AllErrors() []error { return m }

// ParseNudgesFromExcelResponseValidationError is the validation error returned
// by ParseNudgesFromExcelResponse.Validate if the designated constraints
// aren't met.
type ParseNudgesFromExcelResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParseNudgesFromExcelResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParseNudgesFromExcelResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParseNudgesFromExcelResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParseNudgesFromExcelResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParseNudgesFromExcelResponseValidationError) ErrorName() string {
	return "ParseNudgesFromExcelResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ParseNudgesFromExcelResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParseNudgesFromExcelResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParseNudgesFromExcelResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParseNudgesFromExcelResponseValidationError{}

// Validate checks the field values on ParsedNudgeResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ParsedNudgeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParsedNudgeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ParsedNudgeResultMultiError, or nil if none found.
func (m *ParsedNudgeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ParsedNudgeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SerialNo

	// no validation rules for Description

	// no validation rules for EntrySegmentExpression

	// no validation rules for EntryEvent

	// no validation rules for EntryEventExpression

	// no validation rules for ExitEvent

	// no validation rules for ExitExpression

	// no validation rules for AutoDismissDurationInSeconds

	// no validation rules for SnoozeDurationInSeconds

	// no validation rules for ActiveSince

	// no validation rules for ActiveTill

	// no validation rules for SetupBy

	// no validation rules for Area

	// no validation rules for Category

	// no validation rules for SubCategory

	// no validation rules for Urgency

	// no validation rules for UserInactionConsequence

	// no validation rules for DisplayConfigs

	// no validation rules for LoadScreen

	// no validation rules for IsEntryActivationEventDriven

	// no validation rules for NudgeType

	// no validation rules for NudgeUserType

	// no validation rules for NudgeAdditionalDetails

	// no validation rules for CreatedBy

	// no validation rules for ParseStatus

	if len(errors) > 0 {
		return ParsedNudgeResultMultiError(errors)
	}

	return nil
}

// ParsedNudgeResultMultiError is an error wrapping multiple validation errors
// returned by ParsedNudgeResult.ValidateAll() if the designated constraints
// aren't met.
type ParsedNudgeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParsedNudgeResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParsedNudgeResultMultiError) AllErrors() []error { return m }

// ParsedNudgeResultValidationError is the validation error returned by
// ParsedNudgeResult.Validate if the designated constraints aren't met.
type ParsedNudgeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParsedNudgeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParsedNudgeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParsedNudgeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParsedNudgeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParsedNudgeResultValidationError) ErrorName() string {
	return "ParsedNudgeResultValidationError"
}

// Error satisfies the builtin error interface
func (e ParsedNudgeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParsedNudgeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParsedNudgeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParsedNudgeResultValidationError{}
