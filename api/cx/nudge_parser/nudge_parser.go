package nudge_parser

import (
	"encoding/json"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	alPb "github.com/epifi/gamma/api/cx/audit_log"
)

// NudgeParserParamsForLogging defines the structure for logging custom parameters.
type NudgeParserParamsForLogging struct {
	CreatedBy string `json:"created_by,omitempty"`
	SheetName string `json:"sheet_name,omitempty"`
}

// Implement RequestWithLogging interface for ParseNudgesFromExcelRequest
func (m *ParseNudgesFromExcelRequest) GetAction() alPb.Action {
	return alPb.Action_CREATE
}

func (m *ParseNudgesFromExcelRequest) GetObject() alPb.Object {
	return alPb.Object_OFFERS
}

// GetParametersJson implements the RequestWithCustomLoggingParams interface.
func (m *ParseNudgesFromExcelRequest) GetParametersJson() string {
	params := &NudgeParserParamsForLogging{
		CreatedBy: m.GetCreated<PERSON>y(),
		SheetName: m.GetSheetName(),
	}
	b, err := json.Marshal(params)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal nudge parser params", zap.Error(err))
		return ""
	}
	return string(b)
}
