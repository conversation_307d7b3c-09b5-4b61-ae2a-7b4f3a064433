// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/cx/nudge_parser/service.proto

package nudge_parser

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NudgeParser_ParseNudgesFromExcel_FullMethodName = "/cx.nudge_parser.NudgeParser/ParseNudgesFromExcel"
)

// NudgeParserClient is the client API for NudgeParser service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NudgeParserClient interface {
	// ParseNudgesFromExcel parses nudge configurations from an Excel file
	// Returns validation results for each row in the Excel file
	ParseNudgesFromExcel(ctx context.Context, in *ParseNudgesFromExcelRequest, opts ...grpc.CallOption) (*ParseNudgesFromExcelResponse, error)
}

type nudgeParserClient struct {
	cc grpc.ClientConnInterface
}

func NewNudgeParserClient(cc grpc.ClientConnInterface) NudgeParserClient {
	return &nudgeParserClient{cc}
}

func (c *nudgeParserClient) ParseNudgesFromExcel(ctx context.Context, in *ParseNudgesFromExcelRequest, opts ...grpc.CallOption) (*ParseNudgesFromExcelResponse, error) {
	out := new(ParseNudgesFromExcelResponse)
	err := c.cc.Invoke(ctx, NudgeParser_ParseNudgesFromExcel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NudgeParserServer is the server API for NudgeParser service.
// All implementations should embed UnimplementedNudgeParserServer
// for forward compatibility
type NudgeParserServer interface {
	// ParseNudgesFromExcel parses nudge configurations from an Excel file
	// Returns validation results for each row in the Excel file
	ParseNudgesFromExcel(context.Context, *ParseNudgesFromExcelRequest) (*ParseNudgesFromExcelResponse, error)
}

// UnimplementedNudgeParserServer should be embedded to have forward compatible implementations.
type UnimplementedNudgeParserServer struct {
}

func (UnimplementedNudgeParserServer) ParseNudgesFromExcel(context.Context, *ParseNudgesFromExcelRequest) (*ParseNudgesFromExcelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParseNudgesFromExcel not implemented")
}

// UnsafeNudgeParserServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NudgeParserServer will
// result in compilation errors.
type UnsafeNudgeParserServer interface {
	mustEmbedUnimplementedNudgeParserServer()
}

func RegisterNudgeParserServer(s grpc.ServiceRegistrar, srv NudgeParserServer) {
	s.RegisterService(&NudgeParser_ServiceDesc, srv)
}

func _NudgeParser_ParseNudgesFromExcel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParseNudgesFromExcelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NudgeParserServer).ParseNudgesFromExcel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NudgeParser_ParseNudgesFromExcel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NudgeParserServer).ParseNudgesFromExcel(ctx, req.(*ParseNudgesFromExcelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NudgeParser_ServiceDesc is the grpc.ServiceDesc for NudgeParser service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NudgeParser_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cx.nudge_parser.NudgeParser",
	HandlerType: (*NudgeParserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ParseNudgesFromExcel",
			Handler:    _NudgeParser_ParseNudgesFromExcel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/cx/nudge_parser/service.proto",
}
