// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/nudge_parser/service.proto

package nudge_parser

import (
	rpc "github.com/epifi/be-common/api/rpc"
	cx "github.com/epifi/gamma/api/cx"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ParseNudgesFromExcelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Excel file content as bytes
	ExcelFileContent []byte `protobuf:"bytes,2,opt,name=excel_file_content,json=excelFileContent,proto3" json:"excel_file_content,omitempty"`
	// Sheet name to parse (defaults to "Sheet1" if not provided)
	SheetName string `protobuf:"bytes,3,opt,name=sheet_name,json=sheetName,proto3" json:"sheet_name,omitempty"`
	// User who is creating the nudges
	CreatedBy string `protobuf:"bytes,4,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
}

func (x *ParseNudgesFromExcelRequest) Reset() {
	*x = ParseNudgesFromExcelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_nudge_parser_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParseNudgesFromExcelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseNudgesFromExcelRequest) ProtoMessage() {}

func (x *ParseNudgesFromExcelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_nudge_parser_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseNudgesFromExcelRequest.ProtoReflect.Descriptor instead.
func (*ParseNudgesFromExcelRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_nudge_parser_service_proto_rawDescGZIP(), []int{0}
}

func (x *ParseNudgesFromExcelRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ParseNudgesFromExcelRequest) GetExcelFileContent() []byte {
	if x != nil {
		return x.ExcelFileContent
	}
	return nil
}

func (x *ParseNudgesFromExcelRequest) GetSheetName() string {
	if x != nil {
		return x.SheetName
	}
	return ""
}

func (x *ParseNudgesFromExcelRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type ParseNudgesFromExcelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of parsed nudge results with validation status
	ParsedNudges []*ParsedNudgeResult `protobuf:"bytes,2,rep,name=parsed_nudges,json=parsedNudges,proto3" json:"parsed_nudges,omitempty"`
}

func (x *ParseNudgesFromExcelResponse) Reset() {
	*x = ParseNudgesFromExcelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_nudge_parser_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParseNudgesFromExcelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseNudgesFromExcelResponse) ProtoMessage() {}

func (x *ParseNudgesFromExcelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_nudge_parser_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseNudgesFromExcelResponse.ProtoReflect.Descriptor instead.
func (*ParseNudgesFromExcelResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_nudge_parser_service_proto_rawDescGZIP(), []int{1}
}

func (x *ParseNudgesFromExcelResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ParseNudgesFromExcelResponse) GetParsedNudges() []*ParsedNudgeResult {
	if x != nil {
		return x.ParsedNudges
	}
	return nil
}

type ParsedNudgeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Serial number from Excel row
	SerialNo string `protobuf:"bytes,1,opt,name=serial_no,json=serialNo,proto3" json:"serial_no,omitempty"`
	// Description of the nudge
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Entry segment expression
	EntrySegmentExpression string `protobuf:"bytes,3,opt,name=entry_segment_expression,json=entrySegmentExpression,proto3" json:"entry_segment_expression,omitempty"`
	// Entry event type
	EntryEvent string `protobuf:"bytes,4,opt,name=entry_event,json=entryEvent,proto3" json:"entry_event,omitempty"`
	// Entry event expression
	EntryEventExpression string `protobuf:"bytes,5,opt,name=entry_event_expression,json=entryEventExpression,proto3" json:"entry_event_expression,omitempty"`
	// Exit event type
	ExitEvent string `protobuf:"bytes,6,opt,name=exit_event,json=exitEvent,proto3" json:"exit_event,omitempty"`
	// Exit expression
	ExitExpression string `protobuf:"bytes,7,opt,name=exit_expression,json=exitExpression,proto3" json:"exit_expression,omitempty"`
	// Auto dismiss duration in seconds
	AutoDismissDurationInSeconds uint64 `protobuf:"varint,8,opt,name=auto_dismiss_duration_in_seconds,json=autoDismissDurationInSeconds,proto3" json:"auto_dismiss_duration_in_seconds,omitempty"`
	// Snooze duration in seconds
	SnoozeDurationInSeconds uint64 `protobuf:"varint,9,opt,name=snooze_duration_in_seconds,json=snoozeDurationInSeconds,proto3" json:"snooze_duration_in_seconds,omitempty"`
	// Active since timestamp
	ActiveSince string `protobuf:"bytes,10,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// Active till timestamp
	ActiveTill string `protobuf:"bytes,11,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// Setup by user
	SetupBy string `protobuf:"bytes,12,opt,name=setup_by,json=setupBy,proto3" json:"setup_by,omitempty"`
	// Nudge area
	Area string `protobuf:"bytes,13,opt,name=area,proto3" json:"area,omitempty"`
	// Nudge category
	Category string `protobuf:"bytes,14,opt,name=category,proto3" json:"category,omitempty"`
	// Nudge sub-category
	SubCategory string `protobuf:"bytes,15,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	// Nudge urgency
	Urgency string `protobuf:"bytes,16,opt,name=urgency,proto3" json:"urgency,omitempty"`
	// User inaction consequence
	UserInactionConsequence string `protobuf:"bytes,17,opt,name=user_inaction_consequence,json=userInactionConsequence,proto3" json:"user_inaction_consequence,omitempty"`
	// Display screens list
	DisplayInScreens []string `protobuf:"bytes,18,rep,name=display_in_screens,json=displayInScreens,proto3" json:"display_in_screens,omitempty"`
	// Display configurations (JSON string)
	DisplayConfigs string `protobuf:"bytes,19,opt,name=display_configs,json=displayConfigs,proto3" json:"display_configs,omitempty"`
	// Load screen configuration (JSON string)
	LoadScreen string `protobuf:"bytes,20,opt,name=load_screen,json=loadScreen,proto3" json:"load_screen,omitempty"`
	// Is entry activation event driven
	IsEntryActivationEventDriven bool `protobuf:"varint,21,opt,name=is_entry_activation_event_driven,json=isEntryActivationEventDriven,proto3" json:"is_entry_activation_event_driven,omitempty"`
	// Nudge type
	NudgeType string `protobuf:"bytes,22,opt,name=nudge_type,json=nudgeType,proto3" json:"nudge_type,omitempty"`
	// Nudge user type
	NudgeUserType string `protobuf:"bytes,23,opt,name=nudge_user_type,json=nudgeUserType,proto3" json:"nudge_user_type,omitempty"`
	// Additional details (JSON string)
	NudgeAdditionalDetails string `protobuf:"bytes,24,opt,name=nudge_additional_details,json=nudgeAdditionalDetails,proto3" json:"nudge_additional_details,omitempty"`
	// Created by user
	CreatedBy string `protobuf:"bytes,25,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// Parse status: SUCCESS, INVALID_DATA
	ParseStatus string `protobuf:"bytes,26,opt,name=parse_status,json=parseStatus,proto3" json:"parse_status,omitempty"`
	// List of error messages if validation failed
	ErrorMessages []string `protobuf:"bytes,27,rep,name=error_messages,json=errorMessages,proto3" json:"error_messages,omitempty"`
}

func (x *ParsedNudgeResult) Reset() {
	*x = ParsedNudgeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_nudge_parser_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParsedNudgeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParsedNudgeResult) ProtoMessage() {}

func (x *ParsedNudgeResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_nudge_parser_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParsedNudgeResult.ProtoReflect.Descriptor instead.
func (*ParsedNudgeResult) Descriptor() ([]byte, []int) {
	return file_api_cx_nudge_parser_service_proto_rawDescGZIP(), []int{2}
}

func (x *ParsedNudgeResult) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *ParsedNudgeResult) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ParsedNudgeResult) GetEntrySegmentExpression() string {
	if x != nil {
		return x.EntrySegmentExpression
	}
	return ""
}

func (x *ParsedNudgeResult) GetEntryEvent() string {
	if x != nil {
		return x.EntryEvent
	}
	return ""
}

func (x *ParsedNudgeResult) GetEntryEventExpression() string {
	if x != nil {
		return x.EntryEventExpression
	}
	return ""
}

func (x *ParsedNudgeResult) GetExitEvent() string {
	if x != nil {
		return x.ExitEvent
	}
	return ""
}

func (x *ParsedNudgeResult) GetExitExpression() string {
	if x != nil {
		return x.ExitExpression
	}
	return ""
}

func (x *ParsedNudgeResult) GetAutoDismissDurationInSeconds() uint64 {
	if x != nil {
		return x.AutoDismissDurationInSeconds
	}
	return 0
}

func (x *ParsedNudgeResult) GetSnoozeDurationInSeconds() uint64 {
	if x != nil {
		return x.SnoozeDurationInSeconds
	}
	return 0
}

func (x *ParsedNudgeResult) GetActiveSince() string {
	if x != nil {
		return x.ActiveSince
	}
	return ""
}

func (x *ParsedNudgeResult) GetActiveTill() string {
	if x != nil {
		return x.ActiveTill
	}
	return ""
}

func (x *ParsedNudgeResult) GetSetupBy() string {
	if x != nil {
		return x.SetupBy
	}
	return ""
}

func (x *ParsedNudgeResult) GetArea() string {
	if x != nil {
		return x.Area
	}
	return ""
}

func (x *ParsedNudgeResult) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ParsedNudgeResult) GetSubCategory() string {
	if x != nil {
		return x.SubCategory
	}
	return ""
}

func (x *ParsedNudgeResult) GetUrgency() string {
	if x != nil {
		return x.Urgency
	}
	return ""
}

func (x *ParsedNudgeResult) GetUserInactionConsequence() string {
	if x != nil {
		return x.UserInactionConsequence
	}
	return ""
}

func (x *ParsedNudgeResult) GetDisplayInScreens() []string {
	if x != nil {
		return x.DisplayInScreens
	}
	return nil
}

func (x *ParsedNudgeResult) GetDisplayConfigs() string {
	if x != nil {
		return x.DisplayConfigs
	}
	return ""
}

func (x *ParsedNudgeResult) GetLoadScreen() string {
	if x != nil {
		return x.LoadScreen
	}
	return ""
}

func (x *ParsedNudgeResult) GetIsEntryActivationEventDriven() bool {
	if x != nil {
		return x.IsEntryActivationEventDriven
	}
	return false
}

func (x *ParsedNudgeResult) GetNudgeType() string {
	if x != nil {
		return x.NudgeType
	}
	return ""
}

func (x *ParsedNudgeResult) GetNudgeUserType() string {
	if x != nil {
		return x.NudgeUserType
	}
	return ""
}

func (x *ParsedNudgeResult) GetNudgeAdditionalDetails() string {
	if x != nil {
		return x.NudgeAdditionalDetails
	}
	return ""
}

func (x *ParsedNudgeResult) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ParsedNudgeResult) GetParseStatus() string {
	if x != nil {
		return x.ParseStatus
	}
	return ""
}

func (x *ParsedNudgeResult) GetErrorMessages() []string {
	if x != nil {
		return x.ErrorMessages
	}
	return nil
}

var File_api_cx_nudge_parser_service_proto protoreflect.FileDescriptor

var file_api_cx_nudge_parser_service_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f, 0x70,
	0x61, 0x72, 0x73, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x63, 0x78, 0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x72, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x78, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xad, 0x01, 0x0a,
	0x1b, 0x50, 0x61, 0x72, 0x73, 0x65, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x46, 0x72, 0x6f, 0x6d,
	0x45, 0x78, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63,
	0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x2c, 0x0a, 0x12, 0x65, 0x78, 0x63, 0x65, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x65, 0x78,
	0x63, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x65, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0x8c, 0x01, 0x0a,
	0x1c, 0x50, 0x61, 0x72, 0x73, 0x65, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x46, 0x72, 0x6f, 0x6d,
	0x45, 0x78, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x47, 0x0a, 0x0d, 0x70, 0x61, 0x72, 0x73, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x64,
	0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x6e,
	0x75, 0x64, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x73,
	0x65, 0x64, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0c, 0x70,
	0x61, 0x72, 0x73, 0x65, 0x64, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x22, 0xe2, 0x08, 0x0a, 0x11,
	0x50, 0x61, 0x72, 0x73, 0x65, 0x64, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x38, 0x0a, 0x18, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x16, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x69, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x69, 0x74, 0x45,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x20, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x64, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x1c, 0x61, 0x75, 0x74, 0x6f, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x17, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x69, 0x6e, 0x63,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69,
	0x6c, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x74, 0x75, 0x70, 0x42, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x65,
	0x61, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x75, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x75, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x3a, 0x0a, 0x19, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x73, 0x18, 0x12, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x46,
	0x0a, 0x20, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x72, 0x69, 0x76,
	0x65, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x69, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x75, 0x64, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6e, 0x75, 0x64, 0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a,
	0x18, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x32, 0xa9, 0x01, 0x0a, 0x0b, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x50, 0x61, 0x72, 0x73, 0x65, 0x72,
	0x12, 0x99, 0x01, 0x0a, 0x14, 0x50, 0x61, 0x72, 0x73, 0x65, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x73,
	0x46, 0x72, 0x6f, 0x6d, 0x45, 0x78, 0x63, 0x65, 0x6c, 0x12, 0x2c, 0x2e, 0x63, 0x78, 0x2e, 0x6e,
	0x75, 0x64, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x73,
	0x65, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x45, 0x78, 0x63, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x78, 0x2e, 0x6e, 0x75, 0x64,
	0x67, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x4e,
	0x75, 0x64, 0x67, 0x65, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x45, 0x78, 0x63, 0x65, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7,
	0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x42, 0x58, 0x0a, 0x2a,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x6e, 0x75,
	0x64, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x73, 0x65, 0x72, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x5f,
	0x70, 0x61, 0x72, 0x73, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_nudge_parser_service_proto_rawDescOnce sync.Once
	file_api_cx_nudge_parser_service_proto_rawDescData = file_api_cx_nudge_parser_service_proto_rawDesc
)

func file_api_cx_nudge_parser_service_proto_rawDescGZIP() []byte {
	file_api_cx_nudge_parser_service_proto_rawDescOnce.Do(func() {
		file_api_cx_nudge_parser_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_nudge_parser_service_proto_rawDescData)
	})
	return file_api_cx_nudge_parser_service_proto_rawDescData
}

var file_api_cx_nudge_parser_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_cx_nudge_parser_service_proto_goTypes = []interface{}{
	(*ParseNudgesFromExcelRequest)(nil),  // 0: cx.nudge_parser.ParseNudgesFromExcelRequest
	(*ParseNudgesFromExcelResponse)(nil), // 1: cx.nudge_parser.ParseNudgesFromExcelResponse
	(*ParsedNudgeResult)(nil),            // 2: cx.nudge_parser.ParsedNudgeResult
	(*cx.Header)(nil),                    // 3: cx.Header
	(*rpc.Status)(nil),                   // 4: rpc.Status
}
var file_api_cx_nudge_parser_service_proto_depIdxs = []int32{
	3, // 0: cx.nudge_parser.ParseNudgesFromExcelRequest.header:type_name -> cx.Header
	4, // 1: cx.nudge_parser.ParseNudgesFromExcelResponse.status:type_name -> rpc.Status
	2, // 2: cx.nudge_parser.ParseNudgesFromExcelResponse.parsed_nudges:type_name -> cx.nudge_parser.ParsedNudgeResult
	0, // 3: cx.nudge_parser.NudgeParser.ParseNudgesFromExcel:input_type -> cx.nudge_parser.ParseNudgesFromExcelRequest
	1, // 4: cx.nudge_parser.NudgeParser.ParseNudgesFromExcel:output_type -> cx.nudge_parser.ParseNudgesFromExcelResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_cx_nudge_parser_service_proto_init() }
func file_api_cx_nudge_parser_service_proto_init() {
	if File_api_cx_nudge_parser_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cx_nudge_parser_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParseNudgesFromExcelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_nudge_parser_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParseNudgesFromExcelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_nudge_parser_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParsedNudgeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_nudge_parser_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_nudge_parser_service_proto_goTypes,
		DependencyIndexes: file_api_cx_nudge_parser_service_proto_depIdxs,
		MessageInfos:      file_api_cx_nudge_parser_service_proto_msgTypes,
	}.Build()
	File_api_cx_nudge_parser_service_proto = out.File
	file_api_cx_nudge_parser_service_proto_rawDesc = nil
	file_api_cx_nudge_parser_service_proto_goTypes = nil
	file_api_cx_nudge_parser_service_proto_depIdxs = nil
}
