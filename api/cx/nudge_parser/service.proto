syntax = "proto3";
package cx.nudge_parser;

import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/cx/nudge_parser";
option java_package = "com.github.epifi.gamma.api.cx.nudge_parser";

service NudgeParser {
  // ParseNudgesFromExcel parses nudge configurations from an Excel file
  // Returns validation results for each row in the Excel file
  rpc ParseNudgesFromExcel(ParseNudgesFromExcelRequest) returns (ParseNudgesFromExcelResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message ParseNudgesFromExcelRequest {
  cx.Header header = 1;
  
  // Excel file content as bytes
  bytes excel_file_content = 2;
  
  // Sheet name to parse (defaults to "Sheet1" if not provided)
  string sheet_name = 3;
  
  // User who is creating the nudges
  string created_by = 4;
}

message ParseNudgesFromExcelResponse {
  rpc.Status status = 1;
  
  // List of parsed nudge results with validation status
  repeated ParsedNudgeResult parsed_nudges = 2;
}

message ParsedNudgeResult {
  // Serial number from Excel row
  string serial_no = 1;
  
  // Description of the nudge
  string description = 2;
  
  // Entry segment expression
  string entry_segment_expression = 3;
  
  // Entry event type
  string entry_event = 4;
  
  // Entry event expression
  string entry_event_expression = 5;
  
  // Exit event type
  string exit_event = 6;
  
  // Exit expression
  string exit_expression = 7;
  
  // Auto dismiss duration in seconds
  uint64 auto_dismiss_duration_in_seconds = 8;
  
  // Snooze duration in seconds
  uint64 snooze_duration_in_seconds = 9;
  
  // Active since timestamp
  string active_since = 10;
  
  // Active till timestamp
  string active_till = 11;
  
  // Setup by user
  string setup_by = 12;
  
  // Nudge area
  string area = 13;
  
  // Nudge category
  string category = 14;
  
  // Nudge sub-category
  string sub_category = 15;
  
  // Nudge urgency
  string urgency = 16;
  
  // User inaction consequence
  string user_inaction_consequence = 17;
  
  // Display screens list
  repeated string display_in_screens = 18;
  
  // Display configurations (JSON string)
  string display_configs = 19;
  
  // Load screen configuration (JSON string)
  string load_screen = 20;
  
  // Is entry activation event driven
  bool is_entry_activation_event_driven = 21;
  
  // Nudge type
  string nudge_type = 22;
  
  // Nudge user type
  string nudge_user_type = 23;
  
  // Additional details (JSON string)
  string nudge_additional_details = 24;
  
  // Created by user
  string created_by = 25;
  
  // Parse status: SUCCESS, INVALID_DATA
  string parse_status = 26;
  
  // List of error messages if validation failed
  repeated string error_messages = 27;
} 