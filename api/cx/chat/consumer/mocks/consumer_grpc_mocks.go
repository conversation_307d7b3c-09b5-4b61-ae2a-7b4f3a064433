// Code generated by MockGen. DO NOT EDIT.
// Source: api/cx/chat/consumer/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consumer "github.com/epifi/gamma/api/cx/chat/consumer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCxChatEventConsumerClient is a mock of CxChatEventConsumerClient interface.
type MockCxChatEventConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockCxChatEventConsumerClientMockRecorder
}

// MockCxChatEventConsumerClientMockRecorder is the mock recorder for MockCxChatEventConsumerClient.
type MockCxChatEventConsumerClientMockRecorder struct {
	mock *MockCxChatEventConsumerClient
}

// NewMockCxChatEventConsumerClient creates a new mock instance.
func NewMockCxChatEventConsumerClient(ctrl *gomock.Controller) *MockCxChatEventConsumerClient {
	mock := &MockCxChatEventConsumerClient{ctrl: ctrl}
	mock.recorder = &MockCxChatEventConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCxChatEventConsumerClient) EXPECT() *MockCxChatEventConsumerClientMockRecorder {
	return m.recorder
}

// ProcessFreshchatEvent mocks base method.
func (m *MockCxChatEventConsumerClient) ProcessFreshchatEvent(ctx context.Context, in *consumer.ProcessFreshchatEventRequest, opts ...grpc.CallOption) (*consumer.ProcessFreshchatEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessFreshchatEvent", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessFreshchatEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFreshchatEvent indicates an expected call of ProcessFreshchatEvent.
func (mr *MockCxChatEventConsumerClientMockRecorder) ProcessFreshchatEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFreshchatEvent", reflect.TypeOf((*MockCxChatEventConsumerClient)(nil).ProcessFreshchatEvent), varargs...)
}

// ProcessNuggetEvent mocks base method.
func (m *MockCxChatEventConsumerClient) ProcessNuggetEvent(ctx context.Context, in *consumer.ProcessNuggetEventRequest, opts ...grpc.CallOption) (*consumer.ProcessNuggetEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessNuggetEvent", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessNuggetEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNuggetEvent indicates an expected call of ProcessNuggetEvent.
func (mr *MockCxChatEventConsumerClientMockRecorder) ProcessNuggetEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNuggetEvent", reflect.TypeOf((*MockCxChatEventConsumerClient)(nil).ProcessNuggetEvent), varargs...)
}

// MockCxChatEventConsumerServer is a mock of CxChatEventConsumerServer interface.
type MockCxChatEventConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockCxChatEventConsumerServerMockRecorder
}

// MockCxChatEventConsumerServerMockRecorder is the mock recorder for MockCxChatEventConsumerServer.
type MockCxChatEventConsumerServerMockRecorder struct {
	mock *MockCxChatEventConsumerServer
}

// NewMockCxChatEventConsumerServer creates a new mock instance.
func NewMockCxChatEventConsumerServer(ctrl *gomock.Controller) *MockCxChatEventConsumerServer {
	mock := &MockCxChatEventConsumerServer{ctrl: ctrl}
	mock.recorder = &MockCxChatEventConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCxChatEventConsumerServer) EXPECT() *MockCxChatEventConsumerServerMockRecorder {
	return m.recorder
}

// ProcessFreshchatEvent mocks base method.
func (m *MockCxChatEventConsumerServer) ProcessFreshchatEvent(arg0 context.Context, arg1 *consumer.ProcessFreshchatEventRequest) (*consumer.ProcessFreshchatEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessFreshchatEvent", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessFreshchatEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFreshchatEvent indicates an expected call of ProcessFreshchatEvent.
func (mr *MockCxChatEventConsumerServerMockRecorder) ProcessFreshchatEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFreshchatEvent", reflect.TypeOf((*MockCxChatEventConsumerServer)(nil).ProcessFreshchatEvent), arg0, arg1)
}

// ProcessNuggetEvent mocks base method.
func (m *MockCxChatEventConsumerServer) ProcessNuggetEvent(arg0 context.Context, arg1 *consumer.ProcessNuggetEventRequest) (*consumer.ProcessNuggetEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessNuggetEvent", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessNuggetEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessNuggetEvent indicates an expected call of ProcessNuggetEvent.
func (mr *MockCxChatEventConsumerServerMockRecorder) ProcessNuggetEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessNuggetEvent", reflect.TypeOf((*MockCxChatEventConsumerServer)(nil).ProcessNuggetEvent), arg0, arg1)
}

// MockUnsafeCxChatEventConsumerServer is a mock of UnsafeCxChatEventConsumerServer interface.
type MockUnsafeCxChatEventConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCxChatEventConsumerServerMockRecorder
}

// MockUnsafeCxChatEventConsumerServerMockRecorder is the mock recorder for MockUnsafeCxChatEventConsumerServer.
type MockUnsafeCxChatEventConsumerServerMockRecorder struct {
	mock *MockUnsafeCxChatEventConsumerServer
}

// NewMockUnsafeCxChatEventConsumerServer creates a new mock instance.
func NewMockUnsafeCxChatEventConsumerServer(ctrl *gomock.Controller) *MockUnsafeCxChatEventConsumerServer {
	mock := &MockUnsafeCxChatEventConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCxChatEventConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCxChatEventConsumerServer) EXPECT() *MockUnsafeCxChatEventConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCxChatEventConsumerServer mocks base method.
func (m *MockUnsafeCxChatEventConsumerServer) mustEmbedUnimplementedCxChatEventConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCxChatEventConsumerServer")
}

// mustEmbedUnimplementedCxChatEventConsumerServer indicates an expected call of mustEmbedUnimplementedCxChatEventConsumerServer.
func (mr *MockUnsafeCxChatEventConsumerServerMockRecorder) mustEmbedUnimplementedCxChatEventConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCxChatEventConsumerServer", reflect.TypeOf((*MockUnsafeCxChatEventConsumerServer)(nil).mustEmbedUnimplementedCxChatEventConsumerServer))
}
