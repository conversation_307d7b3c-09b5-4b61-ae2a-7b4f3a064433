syntax = "proto3";

package cx.chat.consumer;

import "api/queue/consumer_headers.proto";
import "api/cx/chat/freshchat_conversation.proto";
import "api/cx/chat/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/cx/chat/consumer";
option java_package = "com.github.epifi.gamma.api.cx.chat.consumer";

service CxChatEventConsumer {
  // consumer rpc which consumes from freshchat event queue
  rpc ProcessFreshchatEvent (ProcessFreshchatEventRequest) returns (ProcessFreshchatEventResponse) {}
  // consumer rpc which consumes from nugget event queue
  rpc ProcessNuggetEvent (ProcessNuggetEventRequest) returns (ProcessNuggetEventResponse) {}
}

// Freshchat events
// Ref: https://support.freshchat.com/en/support/solutions/articles/239404-freshchat-webhooks-payload-structure-and-authentication
message ProcessFreshchatEventRequest {
  // queue request header
  queue.ConsumerRequestHeader request_header = 1;
  // Type of actor - can be user, agent, system
  FreshchatActorType freshchat_actor_type = 2;
  // unique Id of the freshchat actor (this is not EpiFi's actor ID)
  string freshchat_actor_id = 3;
  // action can be -- message_create, conversation_assignment, conversation_resolution, conversation_reopen
  FreshchatCallbackAction action = 4;
  // time at which this action was executed
  google.protobuf.Timestamp action_time = 5;
  // data depending upon the action type
  oneof data {
    // data for message_create action
    Message message = 6;
    // data for conversation_assignment
    ConversationAssignmentCallbackData assignment = 7;
    // data for conversation_resolution
    ConversationResolutionCallbackData resolve = 8;
    // data for conversation_reopen
    ConversationReopenCallbackData reopen = 9;
  }
}

message ProcessFreshchatEventResponse {
  // queue response header
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessNuggetEventRequest {
  // queue request header
  queue.ConsumerRequestHeader request_header = 1;
  string entity = 2;
  string event_name = 3;
  string sub_event_name = 4;
  google.protobuf.Value event_data = 5;
}

message ProcessNuggetEventResponse {
  // queue response header
  queue.ConsumerResponseHeader response_header = 1;
}
