// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/cx/chat/consumer/consumer.proto

package consumer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CxChatEventConsumer_ProcessFreshchatEvent_FullMethodName = "/cx.chat.consumer.CxChatEventConsumer/ProcessFreshchatEvent"
	CxChatEventConsumer_ProcessNuggetEvent_FullMethodName    = "/cx.chat.consumer.CxChatEventConsumer/ProcessNuggetEvent"
)

// CxChatEventConsumerClient is the client API for CxChatEventConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CxChatEventConsumerClient interface {
	// consumer rpc which consumes from freshchat event queue
	ProcessFreshchatEvent(ctx context.Context, in *ProcessFreshchatEventRequest, opts ...grpc.CallOption) (*ProcessFreshchatEventResponse, error)
	// consumer rpc which consumes from nugget event queue
	ProcessNuggetEvent(ctx context.Context, in *ProcessNuggetEventRequest, opts ...grpc.CallOption) (*ProcessNuggetEventResponse, error)
}

type cxChatEventConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewCxChatEventConsumerClient(cc grpc.ClientConnInterface) CxChatEventConsumerClient {
	return &cxChatEventConsumerClient{cc}
}

func (c *cxChatEventConsumerClient) ProcessFreshchatEvent(ctx context.Context, in *ProcessFreshchatEventRequest, opts ...grpc.CallOption) (*ProcessFreshchatEventResponse, error) {
	out := new(ProcessFreshchatEventResponse)
	err := c.cc.Invoke(ctx, CxChatEventConsumer_ProcessFreshchatEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxChatEventConsumerClient) ProcessNuggetEvent(ctx context.Context, in *ProcessNuggetEventRequest, opts ...grpc.CallOption) (*ProcessNuggetEventResponse, error) {
	out := new(ProcessNuggetEventResponse)
	err := c.cc.Invoke(ctx, CxChatEventConsumer_ProcessNuggetEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CxChatEventConsumerServer is the server API for CxChatEventConsumer service.
// All implementations should embed UnimplementedCxChatEventConsumerServer
// for forward compatibility
type CxChatEventConsumerServer interface {
	// consumer rpc which consumes from freshchat event queue
	ProcessFreshchatEvent(context.Context, *ProcessFreshchatEventRequest) (*ProcessFreshchatEventResponse, error)
	// consumer rpc which consumes from nugget event queue
	ProcessNuggetEvent(context.Context, *ProcessNuggetEventRequest) (*ProcessNuggetEventResponse, error)
}

// UnimplementedCxChatEventConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedCxChatEventConsumerServer struct {
}

func (UnimplementedCxChatEventConsumerServer) ProcessFreshchatEvent(context.Context, *ProcessFreshchatEventRequest) (*ProcessFreshchatEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessFreshchatEvent not implemented")
}
func (UnimplementedCxChatEventConsumerServer) ProcessNuggetEvent(context.Context, *ProcessNuggetEventRequest) (*ProcessNuggetEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessNuggetEvent not implemented")
}

// UnsafeCxChatEventConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CxChatEventConsumerServer will
// result in compilation errors.
type UnsafeCxChatEventConsumerServer interface {
	mustEmbedUnimplementedCxChatEventConsumerServer()
}

func RegisterCxChatEventConsumerServer(s grpc.ServiceRegistrar, srv CxChatEventConsumerServer) {
	s.RegisterService(&CxChatEventConsumer_ServiceDesc, srv)
}

func _CxChatEventConsumer_ProcessFreshchatEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessFreshchatEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxChatEventConsumerServer).ProcessFreshchatEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CxChatEventConsumer_ProcessFreshchatEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxChatEventConsumerServer).ProcessFreshchatEvent(ctx, req.(*ProcessFreshchatEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CxChatEventConsumer_ProcessNuggetEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessNuggetEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxChatEventConsumerServer).ProcessNuggetEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CxChatEventConsumer_ProcessNuggetEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxChatEventConsumerServer).ProcessNuggetEvent(ctx, req.(*ProcessNuggetEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CxChatEventConsumer_ServiceDesc is the grpc.ServiceDesc for CxChatEventConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CxChatEventConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cx.chat.consumer.CxChatEventConsumer",
	HandlerType: (*CxChatEventConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessFreshchatEvent",
			Handler:    _CxChatEventConsumer_ProcessFreshchatEvent_Handler,
		},
		{
			MethodName: "ProcessNuggetEvent",
			Handler:    _CxChatEventConsumer_ProcessNuggetEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/cx/chat/consumer/consumer.proto",
}
