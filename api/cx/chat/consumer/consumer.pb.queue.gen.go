// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/cx/chat/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessFreshchatEventMethod = "ProcessFreshchatEvent"
	ProcessNuggetEventMethod    = "ProcessNuggetEvent"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessFreshchatEventRequest{}
var _ queue.ConsumerRequest = &ProcessNuggetEventRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessFreshchatEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessNuggetEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessFreshchatEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessFreshchatEventMethodToSubscriber(subscriber queue.Subscriber, srv CxChatEventConsumerServer) {
	subscriber.RegisterService(&CxChatEventConsumer_ServiceDesc, srv, ProcessFreshchatEventMethod)
}

// RegisterProcessNuggetEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessNuggetEventMethodToSubscriber(subscriber queue.Subscriber, srv CxChatEventConsumerServer) {
	subscriber.RegisterService(&CxChatEventConsumer_ServiceDesc, srv, ProcessNuggetEventMethod)
}
