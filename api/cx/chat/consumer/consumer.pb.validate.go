// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/chat/consumer/consumer.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	chat "github.com/epifi/gamma/api/cx/chat"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = chat.FreshchatActorType(0)
)

// Validate checks the field values on ProcessFreshchatEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessFreshchatEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessFreshchatEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessFreshchatEventRequestMultiError, or nil if none found.
func (m *ProcessFreshchatEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessFreshchatEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessFreshchatEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessFreshchatEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessFreshchatEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FreshchatActorType

	// no validation rules for FreshchatActorId

	// no validation rules for Action

	if all {
		switch v := interface{}(m.GetActionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessFreshchatEventRequestValidationError{
					field:  "ActionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessFreshchatEventRequestValidationError{
					field:  "ActionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessFreshchatEventRequestValidationError{
				field:  "ActionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Data.(type) {
	case *ProcessFreshchatEventRequest_Message:
		if v == nil {
			err := ProcessFreshchatEventRequestValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMessage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessFreshchatEventRequestValidationError{
						field:  "Message",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessFreshchatEventRequestValidationError{
						field:  "Message",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessFreshchatEventRequestValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessFreshchatEventRequest_Assignment:
		if v == nil {
			err := ProcessFreshchatEventRequestValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAssignment()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessFreshchatEventRequestValidationError{
						field:  "Assignment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessFreshchatEventRequestValidationError{
						field:  "Assignment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAssignment()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessFreshchatEventRequestValidationError{
					field:  "Assignment",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessFreshchatEventRequest_Resolve:
		if v == nil {
			err := ProcessFreshchatEventRequestValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetResolve()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessFreshchatEventRequestValidationError{
						field:  "Resolve",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessFreshchatEventRequestValidationError{
						field:  "Resolve",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResolve()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessFreshchatEventRequestValidationError{
					field:  "Resolve",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessFreshchatEventRequest_Reopen:
		if v == nil {
			err := ProcessFreshchatEventRequestValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetReopen()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessFreshchatEventRequestValidationError{
						field:  "Reopen",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessFreshchatEventRequestValidationError{
						field:  "Reopen",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReopen()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessFreshchatEventRequestValidationError{
					field:  "Reopen",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ProcessFreshchatEventRequestMultiError(errors)
	}

	return nil
}

// ProcessFreshchatEventRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessFreshchatEventRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessFreshchatEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessFreshchatEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessFreshchatEventRequestMultiError) AllErrors() []error { return m }

// ProcessFreshchatEventRequestValidationError is the validation error returned
// by ProcessFreshchatEventRequest.Validate if the designated constraints
// aren't met.
type ProcessFreshchatEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessFreshchatEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessFreshchatEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessFreshchatEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessFreshchatEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessFreshchatEventRequestValidationError) ErrorName() string {
	return "ProcessFreshchatEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessFreshchatEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessFreshchatEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessFreshchatEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessFreshchatEventRequestValidationError{}

// Validate checks the field values on ProcessFreshchatEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessFreshchatEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessFreshchatEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessFreshchatEventResponseMultiError, or nil if none found.
func (m *ProcessFreshchatEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessFreshchatEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessFreshchatEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessFreshchatEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessFreshchatEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessFreshchatEventResponseMultiError(errors)
	}

	return nil
}

// ProcessFreshchatEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessFreshchatEventResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessFreshchatEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessFreshchatEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessFreshchatEventResponseMultiError) AllErrors() []error { return m }

// ProcessFreshchatEventResponseValidationError is the validation error
// returned by ProcessFreshchatEventResponse.Validate if the designated
// constraints aren't met.
type ProcessFreshchatEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessFreshchatEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessFreshchatEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessFreshchatEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessFreshchatEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessFreshchatEventResponseValidationError) ErrorName() string {
	return "ProcessFreshchatEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessFreshchatEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessFreshchatEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessFreshchatEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessFreshchatEventResponseValidationError{}

// Validate checks the field values on ProcessNuggetEventRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessNuggetEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessNuggetEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessNuggetEventRequestMultiError, or nil if none found.
func (m *ProcessNuggetEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessNuggetEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessNuggetEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessNuggetEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessNuggetEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Entity

	// no validation rules for EventName

	// no validation rules for SubEventName

	if all {
		switch v := interface{}(m.GetEventData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessNuggetEventRequestValidationError{
					field:  "EventData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessNuggetEventRequestValidationError{
					field:  "EventData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessNuggetEventRequestValidationError{
				field:  "EventData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessNuggetEventRequestMultiError(errors)
	}

	return nil
}

// ProcessNuggetEventRequestMultiError is an error wrapping multiple validation
// errors returned by ProcessNuggetEventRequest.ValidateAll() if the
// designated constraints aren't met.
type ProcessNuggetEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessNuggetEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessNuggetEventRequestMultiError) AllErrors() []error { return m }

// ProcessNuggetEventRequestValidationError is the validation error returned by
// ProcessNuggetEventRequest.Validate if the designated constraints aren't met.
type ProcessNuggetEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessNuggetEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessNuggetEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessNuggetEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessNuggetEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessNuggetEventRequestValidationError) ErrorName() string {
	return "ProcessNuggetEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessNuggetEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessNuggetEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessNuggetEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessNuggetEventRequestValidationError{}

// Validate checks the field values on ProcessNuggetEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessNuggetEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessNuggetEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessNuggetEventResponseMultiError, or nil if none found.
func (m *ProcessNuggetEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessNuggetEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessNuggetEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessNuggetEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessNuggetEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessNuggetEventResponseMultiError(errors)
	}

	return nil
}

// ProcessNuggetEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessNuggetEventResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessNuggetEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessNuggetEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessNuggetEventResponseMultiError) AllErrors() []error { return m }

// ProcessNuggetEventResponseValidationError is the validation error returned
// by ProcessNuggetEventResponse.Validate if the designated constraints aren't met.
type ProcessNuggetEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessNuggetEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessNuggetEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessNuggetEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessNuggetEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessNuggetEventResponseValidationError) ErrorName() string {
	return "ProcessNuggetEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessNuggetEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessNuggetEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessNuggetEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessNuggetEventResponseValidationError{}
