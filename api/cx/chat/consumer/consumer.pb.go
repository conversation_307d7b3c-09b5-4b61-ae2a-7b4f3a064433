// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/chat/consumer/consumer.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	chat "github.com/epifi/gamma/api/cx/chat"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Freshchat events
// Ref: https://support.freshchat.com/en/support/solutions/articles/239404-freshchat-webhooks-payload-structure-and-authentication
type ProcessFreshchatEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// queue request header
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Type of actor - can be user, agent, system
	FreshchatActorType chat.FreshchatActorType `protobuf:"varint,2,opt,name=freshchat_actor_type,json=freshchatActorType,proto3,enum=cx.chat.FreshchatActorType" json:"freshchat_actor_type,omitempty"`
	// unique Id of the freshchat actor (this is not EpiFi's actor ID)
	FreshchatActorId string `protobuf:"bytes,3,opt,name=freshchat_actor_id,json=freshchatActorId,proto3" json:"freshchat_actor_id,omitempty"`
	// action can be -- message_create, conversation_assignment, conversation_resolution, conversation_reopen
	Action chat.FreshchatCallbackAction `protobuf:"varint,4,opt,name=action,proto3,enum=cx.chat.FreshchatCallbackAction" json:"action,omitempty"`
	// time at which this action was executed
	ActionTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=action_time,json=actionTime,proto3" json:"action_time,omitempty"`
	// data depending upon the action type
	//
	// Types that are assignable to Data:
	//
	//	*ProcessFreshchatEventRequest_Message
	//	*ProcessFreshchatEventRequest_Assignment
	//	*ProcessFreshchatEventRequest_Resolve
	//	*ProcessFreshchatEventRequest_Reopen
	Data isProcessFreshchatEventRequest_Data `protobuf_oneof:"data"`
}

func (x *ProcessFreshchatEventRequest) Reset() {
	*x = ProcessFreshchatEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_consumer_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessFreshchatEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessFreshchatEventRequest) ProtoMessage() {}

func (x *ProcessFreshchatEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_consumer_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessFreshchatEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessFreshchatEventRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_consumer_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessFreshchatEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessFreshchatEventRequest) GetFreshchatActorType() chat.FreshchatActorType {
	if x != nil {
		return x.FreshchatActorType
	}
	return chat.FreshchatActorType(0)
}

func (x *ProcessFreshchatEventRequest) GetFreshchatActorId() string {
	if x != nil {
		return x.FreshchatActorId
	}
	return ""
}

func (x *ProcessFreshchatEventRequest) GetAction() chat.FreshchatCallbackAction {
	if x != nil {
		return x.Action
	}
	return chat.FreshchatCallbackAction(0)
}

func (x *ProcessFreshchatEventRequest) GetActionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ActionTime
	}
	return nil
}

func (m *ProcessFreshchatEventRequest) GetData() isProcessFreshchatEventRequest_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *ProcessFreshchatEventRequest) GetMessage() *chat.Message {
	if x, ok := x.GetData().(*ProcessFreshchatEventRequest_Message); ok {
		return x.Message
	}
	return nil
}

func (x *ProcessFreshchatEventRequest) GetAssignment() *chat.ConversationAssignmentCallbackData {
	if x, ok := x.GetData().(*ProcessFreshchatEventRequest_Assignment); ok {
		return x.Assignment
	}
	return nil
}

func (x *ProcessFreshchatEventRequest) GetResolve() *chat.ConversationResolutionCallbackData {
	if x, ok := x.GetData().(*ProcessFreshchatEventRequest_Resolve); ok {
		return x.Resolve
	}
	return nil
}

func (x *ProcessFreshchatEventRequest) GetReopen() *chat.ConversationReopenCallbackData {
	if x, ok := x.GetData().(*ProcessFreshchatEventRequest_Reopen); ok {
		return x.Reopen
	}
	return nil
}

type isProcessFreshchatEventRequest_Data interface {
	isProcessFreshchatEventRequest_Data()
}

type ProcessFreshchatEventRequest_Message struct {
	// data for message_create action
	Message *chat.Message `protobuf:"bytes,6,opt,name=message,proto3,oneof"`
}

type ProcessFreshchatEventRequest_Assignment struct {
	// data for conversation_assignment
	Assignment *chat.ConversationAssignmentCallbackData `protobuf:"bytes,7,opt,name=assignment,proto3,oneof"`
}

type ProcessFreshchatEventRequest_Resolve struct {
	// data for conversation_resolution
	Resolve *chat.ConversationResolutionCallbackData `protobuf:"bytes,8,opt,name=resolve,proto3,oneof"`
}

type ProcessFreshchatEventRequest_Reopen struct {
	// data for conversation_reopen
	Reopen *chat.ConversationReopenCallbackData `protobuf:"bytes,9,opt,name=reopen,proto3,oneof"`
}

func (*ProcessFreshchatEventRequest_Message) isProcessFreshchatEventRequest_Data() {}

func (*ProcessFreshchatEventRequest_Assignment) isProcessFreshchatEventRequest_Data() {}

func (*ProcessFreshchatEventRequest_Resolve) isProcessFreshchatEventRequest_Data() {}

func (*ProcessFreshchatEventRequest_Reopen) isProcessFreshchatEventRequest_Data() {}

type ProcessFreshchatEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// queue response header
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessFreshchatEventResponse) Reset() {
	*x = ProcessFreshchatEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_consumer_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessFreshchatEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessFreshchatEventResponse) ProtoMessage() {}

func (x *ProcessFreshchatEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_consumer_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessFreshchatEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessFreshchatEventResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_consumer_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessFreshchatEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessNuggetEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// queue request header
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Entity        string                       `protobuf:"bytes,2,opt,name=entity,proto3" json:"entity,omitempty"`
	EventName     string                       `protobuf:"bytes,3,opt,name=event_name,json=eventName,proto3" json:"event_name,omitempty"`
	SubEventName  string                       `protobuf:"bytes,4,opt,name=sub_event_name,json=subEventName,proto3" json:"sub_event_name,omitempty"`
	EventData     *structpb.Value              `protobuf:"bytes,5,opt,name=event_data,json=eventData,proto3" json:"event_data,omitempty"`
}

func (x *ProcessNuggetEventRequest) Reset() {
	*x = ProcessNuggetEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_consumer_consumer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessNuggetEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessNuggetEventRequest) ProtoMessage() {}

func (x *ProcessNuggetEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_consumer_consumer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessNuggetEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessNuggetEventRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_consumer_consumer_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessNuggetEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessNuggetEventRequest) GetEntity() string {
	if x != nil {
		return x.Entity
	}
	return ""
}

func (x *ProcessNuggetEventRequest) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *ProcessNuggetEventRequest) GetSubEventName() string {
	if x != nil {
		return x.SubEventName
	}
	return ""
}

func (x *ProcessNuggetEventRequest) GetEventData() *structpb.Value {
	if x != nil {
		return x.EventData
	}
	return nil
}

type ProcessNuggetEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// queue response header
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessNuggetEventResponse) Reset() {
	*x = ProcessNuggetEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_consumer_consumer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessNuggetEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessNuggetEventResponse) ProtoMessage() {}

func (x *ProcessNuggetEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_consumer_consumer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessNuggetEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessNuggetEventResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_consumer_consumer_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessNuggetEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_cx_chat_consumer_consumer_proto protoreflect.FileDescriptor

var file_api_cx_chat_consumer_consumer_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x66, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe8, 0x04, 0x0a, 0x1c,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x4d, 0x0a, 0x14, 0x66, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x46, 0x72, 0x65, 0x73, 0x68, 0x63,
	0x68, 0x61, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2c, 0x0a, 0x12, 0x66, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x38,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x46, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68,
	0x61, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x47, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x07, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x72,
	0x65, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x78,
	0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x06, 0x72, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x42, 0x06,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x67, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x46, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0xf4, 0x01, 0x0a, 0x19, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x67, 0x67, 0x65,
	0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x75, 0x62,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x35, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x09, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x64, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0x84, 0x02, 0x0a,
	0x13, 0x43, 0x78, 0x43, 0x68, 0x61, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x12, 0x7a, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46,
	0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61,
	0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61,
	0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x71, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x67, 0x67, 0x65,
	0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x75,
	0x67, 0x67, 0x65, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_chat_consumer_consumer_proto_rawDescOnce sync.Once
	file_api_cx_chat_consumer_consumer_proto_rawDescData = file_api_cx_chat_consumer_consumer_proto_rawDesc
)

func file_api_cx_chat_consumer_consumer_proto_rawDescGZIP() []byte {
	file_api_cx_chat_consumer_consumer_proto_rawDescOnce.Do(func() {
		file_api_cx_chat_consumer_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_chat_consumer_consumer_proto_rawDescData)
	})
	return file_api_cx_chat_consumer_consumer_proto_rawDescData
}

var file_api_cx_chat_consumer_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_cx_chat_consumer_consumer_proto_goTypes = []interface{}{
	(*ProcessFreshchatEventRequest)(nil),            // 0: cx.chat.consumer.ProcessFreshchatEventRequest
	(*ProcessFreshchatEventResponse)(nil),           // 1: cx.chat.consumer.ProcessFreshchatEventResponse
	(*ProcessNuggetEventRequest)(nil),               // 2: cx.chat.consumer.ProcessNuggetEventRequest
	(*ProcessNuggetEventResponse)(nil),              // 3: cx.chat.consumer.ProcessNuggetEventResponse
	(*queue.ConsumerRequestHeader)(nil),             // 4: queue.ConsumerRequestHeader
	(chat.FreshchatActorType)(0),                    // 5: cx.chat.FreshchatActorType
	(chat.FreshchatCallbackAction)(0),               // 6: cx.chat.FreshchatCallbackAction
	(*timestamppb.Timestamp)(nil),                   // 7: google.protobuf.Timestamp
	(*chat.Message)(nil),                            // 8: cx.chat.Message
	(*chat.ConversationAssignmentCallbackData)(nil), // 9: cx.chat.ConversationAssignmentCallbackData
	(*chat.ConversationResolutionCallbackData)(nil), // 10: cx.chat.ConversationResolutionCallbackData
	(*chat.ConversationReopenCallbackData)(nil),     // 11: cx.chat.ConversationReopenCallbackData
	(*queue.ConsumerResponseHeader)(nil),            // 12: queue.ConsumerResponseHeader
	(*structpb.Value)(nil),                          // 13: google.protobuf.Value
}
var file_api_cx_chat_consumer_consumer_proto_depIdxs = []int32{
	4,  // 0: cx.chat.consumer.ProcessFreshchatEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	5,  // 1: cx.chat.consumer.ProcessFreshchatEventRequest.freshchat_actor_type:type_name -> cx.chat.FreshchatActorType
	6,  // 2: cx.chat.consumer.ProcessFreshchatEventRequest.action:type_name -> cx.chat.FreshchatCallbackAction
	7,  // 3: cx.chat.consumer.ProcessFreshchatEventRequest.action_time:type_name -> google.protobuf.Timestamp
	8,  // 4: cx.chat.consumer.ProcessFreshchatEventRequest.message:type_name -> cx.chat.Message
	9,  // 5: cx.chat.consumer.ProcessFreshchatEventRequest.assignment:type_name -> cx.chat.ConversationAssignmentCallbackData
	10, // 6: cx.chat.consumer.ProcessFreshchatEventRequest.resolve:type_name -> cx.chat.ConversationResolutionCallbackData
	11, // 7: cx.chat.consumer.ProcessFreshchatEventRequest.reopen:type_name -> cx.chat.ConversationReopenCallbackData
	12, // 8: cx.chat.consumer.ProcessFreshchatEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	4,  // 9: cx.chat.consumer.ProcessNuggetEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	13, // 10: cx.chat.consumer.ProcessNuggetEventRequest.event_data:type_name -> google.protobuf.Value
	12, // 11: cx.chat.consumer.ProcessNuggetEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0,  // 12: cx.chat.consumer.CxChatEventConsumer.ProcessFreshchatEvent:input_type -> cx.chat.consumer.ProcessFreshchatEventRequest
	2,  // 13: cx.chat.consumer.CxChatEventConsumer.ProcessNuggetEvent:input_type -> cx.chat.consumer.ProcessNuggetEventRequest
	1,  // 14: cx.chat.consumer.CxChatEventConsumer.ProcessFreshchatEvent:output_type -> cx.chat.consumer.ProcessFreshchatEventResponse
	3,  // 15: cx.chat.consumer.CxChatEventConsumer.ProcessNuggetEvent:output_type -> cx.chat.consumer.ProcessNuggetEventResponse
	14, // [14:16] is the sub-list for method output_type
	12, // [12:14] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_cx_chat_consumer_consumer_proto_init() }
func file_api_cx_chat_consumer_consumer_proto_init() {
	if File_api_cx_chat_consumer_consumer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cx_chat_consumer_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessFreshchatEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_consumer_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessFreshchatEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_consumer_consumer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessNuggetEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_consumer_consumer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessNuggetEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cx_chat_consumer_consumer_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ProcessFreshchatEventRequest_Message)(nil),
		(*ProcessFreshchatEventRequest_Assignment)(nil),
		(*ProcessFreshchatEventRequest_Resolve)(nil),
		(*ProcessFreshchatEventRequest_Reopen)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_chat_consumer_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_chat_consumer_consumer_proto_goTypes,
		DependencyIndexes: file_api_cx_chat_consumer_consumer_proto_depIdxs,
		MessageInfos:      file_api_cx_chat_consumer_consumer_proto_msgTypes,
	}.Build()
	File_api_cx_chat_consumer_consumer_proto = out.File
	file_api_cx_chat_consumer_consumer_proto_rawDesc = nil
	file_api_cx_chat_consumer_consumer_proto_goTypes = nil
	file_api_cx_chat_consumer_consumer_proto_depIdxs = nil
}
