// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendornotification/cx/chatbot/nugget/service.proto

package nugget

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FetchDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FetchDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchDataRequestMultiError, or nil if none found.
func (m *FetchDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccessToken

	for idx, item := range m.GetRequestedFields() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchDataRequestValidationError{
						field:  fmt.Sprintf("RequestedFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchDataRequestValidationError{
						field:  fmt.Sprintf("RequestedFields[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchDataRequestValidationError{
					field:  fmt.Sprintf("RequestedFields[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UserId

	if len(errors) > 0 {
		return FetchDataRequestMultiError(errors)
	}

	return nil
}

// FetchDataRequestMultiError is an error wrapping multiple validation errors
// returned by FetchDataRequest.ValidateAll() if the designated constraints
// aren't met.
type FetchDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchDataRequestMultiError) AllErrors() []error { return m }

// FetchDataRequestValidationError is the validation error returned by
// FetchDataRequest.Validate if the designated constraints aren't met.
type FetchDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchDataRequestValidationError) ErrorName() string { return "FetchDataRequestValidationError" }

// Error satisfies the builtin error interface
func (e FetchDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchDataRequestValidationError{}

// Validate checks the field values on RequestedFieldInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RequestedFieldInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestedFieldInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RequestedFieldInfoMultiError, or nil if none found.
func (m *RequestedFieldInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestedFieldInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FieldName

	for idx, item := range m.GetInputParameters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RequestedFieldInfoValidationError{
						field:  fmt.Sprintf("InputParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RequestedFieldInfoValidationError{
						field:  fmt.Sprintf("InputParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RequestedFieldInfoValidationError{
					field:  fmt.Sprintf("InputParameters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RequestedFieldInfoMultiError(errors)
	}

	return nil
}

// RequestedFieldInfoMultiError is an error wrapping multiple validation errors
// returned by RequestedFieldInfo.ValidateAll() if the designated constraints
// aren't met.
type RequestedFieldInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestedFieldInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestedFieldInfoMultiError) AllErrors() []error { return m }

// RequestedFieldInfoValidationError is the validation error returned by
// RequestedFieldInfo.Validate if the designated constraints aren't met.
type RequestedFieldInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestedFieldInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestedFieldInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestedFieldInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestedFieldInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestedFieldInfoValidationError) ErrorName() string {
	return "RequestedFieldInfoValidationError"
}

// Error satisfies the builtin error interface
func (e RequestedFieldInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestedFieldInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestedFieldInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestedFieldInfoValidationError{}

// Validate checks the field values on KeyValuePair with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KeyValuePair) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KeyValuePair with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KeyValuePairMultiError, or
// nil if none found.
func (m *KeyValuePair) ValidateAll() error {
	return m.validate(true)
}

func (m *KeyValuePair) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KeyValuePairValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KeyValuePairValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KeyValuePairValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KeyValuePairMultiError(errors)
	}

	return nil
}

// KeyValuePairMultiError is an error wrapping multiple validation errors
// returned by KeyValuePair.ValidateAll() if the designated constraints aren't met.
type KeyValuePairMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KeyValuePairMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KeyValuePairMultiError) AllErrors() []error { return m }

// KeyValuePairValidationError is the validation error returned by
// KeyValuePair.Validate if the designated constraints aren't met.
type KeyValuePairValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KeyValuePairValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KeyValuePairValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KeyValuePairValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KeyValuePairValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KeyValuePairValidationError) ErrorName() string { return "KeyValuePairValidationError" }

// Error satisfies the builtin error interface
func (e KeyValuePairValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKeyValuePair.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KeyValuePairValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KeyValuePairValidationError{}

// Validate checks the field values on FetchDataResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FetchDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchDataResponseMultiError, or nil if none found.
func (m *FetchDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchDataResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchDataResponseValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchDataResponseValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Error

	if len(errors) > 0 {
		return FetchDataResponseMultiError(errors)
	}

	return nil
}

// FetchDataResponseMultiError is an error wrapping multiple validation errors
// returned by FetchDataResponse.ValidateAll() if the designated constraints
// aren't met.
type FetchDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchDataResponseMultiError) AllErrors() []error { return m }

// FetchDataResponseValidationError is the validation error returned by
// FetchDataResponse.Validate if the designated constraints aren't met.
type FetchDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchDataResponseValidationError) ErrorName() string {
	return "FetchDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchDataResponseValidationError{}

// Validate checks the field values on HandleCallbackEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleCallbackEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleCallbackEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleCallbackEventRequestMultiError, or nil if none found.
func (m *HandleCallbackEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleCallbackEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Entity

	// no validation rules for EventName

	// no validation rules for SubEventName

	if all {
		switch v := interface{}(m.GetEventData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HandleCallbackEventRequestValidationError{
					field:  "EventData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HandleCallbackEventRequestValidationError{
					field:  "EventData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HandleCallbackEventRequestValidationError{
				field:  "EventData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Timestamp

	if len(errors) > 0 {
		return HandleCallbackEventRequestMultiError(errors)
	}

	return nil
}

// HandleCallbackEventRequestMultiError is an error wrapping multiple
// validation errors returned by HandleCallbackEventRequest.ValidateAll() if
// the designated constraints aren't met.
type HandleCallbackEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleCallbackEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleCallbackEventRequestMultiError) AllErrors() []error { return m }

// HandleCallbackEventRequestValidationError is the validation error returned
// by HandleCallbackEventRequest.Validate if the designated constraints aren't met.
type HandleCallbackEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleCallbackEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleCallbackEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleCallbackEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleCallbackEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleCallbackEventRequestValidationError) ErrorName() string {
	return "HandleCallbackEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HandleCallbackEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleCallbackEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleCallbackEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleCallbackEventRequestValidationError{}
