// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendornotification/cx/chatbot/nugget/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	nugget "github.com/epifi/gamma/api/vendornotification/cx/chatbot/nugget"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockChatbotNuggetServiceClient is a mock of ChatbotNuggetServiceClient interface.
type MockChatbotNuggetServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockChatbotNuggetServiceClientMockRecorder
}

// MockChatbotNuggetServiceClientMockRecorder is the mock recorder for MockChatbotNuggetServiceClient.
type MockChatbotNuggetServiceClientMockRecorder struct {
	mock *MockChatbotNuggetServiceClient
}

// NewMockChatbotNuggetServiceClient creates a new mock instance.
func NewMockChatbotNuggetServiceClient(ctrl *gomock.Controller) *MockChatbotNuggetServiceClient {
	mock := &MockChatbotNuggetServiceClient{ctrl: ctrl}
	mock.recorder = &MockChatbotNuggetServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatbotNuggetServiceClient) EXPECT() *MockChatbotNuggetServiceClientMockRecorder {
	return m.recorder
}

// FetchData mocks base method.
func (m *MockChatbotNuggetServiceClient) FetchData(ctx context.Context, in *nugget.FetchDataRequest, opts ...grpc.CallOption) (*nugget.FetchDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchData", varargs...)
	ret0, _ := ret[0].(*nugget.FetchDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchData indicates an expected call of FetchData.
func (mr *MockChatbotNuggetServiceClientMockRecorder) FetchData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchData", reflect.TypeOf((*MockChatbotNuggetServiceClient)(nil).FetchData), varargs...)
}

// HandleCallbackEvent mocks base method.
func (m *MockChatbotNuggetServiceClient) HandleCallbackEvent(ctx context.Context, in *nugget.HandleCallbackEventRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleCallbackEvent", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleCallbackEvent indicates an expected call of HandleCallbackEvent.
func (mr *MockChatbotNuggetServiceClientMockRecorder) HandleCallbackEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleCallbackEvent", reflect.TypeOf((*MockChatbotNuggetServiceClient)(nil).HandleCallbackEvent), varargs...)
}

// MockChatbotNuggetServiceServer is a mock of ChatbotNuggetServiceServer interface.
type MockChatbotNuggetServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockChatbotNuggetServiceServerMockRecorder
}

// MockChatbotNuggetServiceServerMockRecorder is the mock recorder for MockChatbotNuggetServiceServer.
type MockChatbotNuggetServiceServerMockRecorder struct {
	mock *MockChatbotNuggetServiceServer
}

// NewMockChatbotNuggetServiceServer creates a new mock instance.
func NewMockChatbotNuggetServiceServer(ctrl *gomock.Controller) *MockChatbotNuggetServiceServer {
	mock := &MockChatbotNuggetServiceServer{ctrl: ctrl}
	mock.recorder = &MockChatbotNuggetServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChatbotNuggetServiceServer) EXPECT() *MockChatbotNuggetServiceServerMockRecorder {
	return m.recorder
}

// FetchData mocks base method.
func (m *MockChatbotNuggetServiceServer) FetchData(arg0 context.Context, arg1 *nugget.FetchDataRequest) (*nugget.FetchDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchData", arg0, arg1)
	ret0, _ := ret[0].(*nugget.FetchDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchData indicates an expected call of FetchData.
func (mr *MockChatbotNuggetServiceServerMockRecorder) FetchData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchData", reflect.TypeOf((*MockChatbotNuggetServiceServer)(nil).FetchData), arg0, arg1)
}

// HandleCallbackEvent mocks base method.
func (m *MockChatbotNuggetServiceServer) HandleCallbackEvent(arg0 context.Context, arg1 *nugget.HandleCallbackEventRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleCallbackEvent", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleCallbackEvent indicates an expected call of HandleCallbackEvent.
func (mr *MockChatbotNuggetServiceServerMockRecorder) HandleCallbackEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleCallbackEvent", reflect.TypeOf((*MockChatbotNuggetServiceServer)(nil).HandleCallbackEvent), arg0, arg1)
}

// MockUnsafeChatbotNuggetServiceServer is a mock of UnsafeChatbotNuggetServiceServer interface.
type MockUnsafeChatbotNuggetServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeChatbotNuggetServiceServerMockRecorder
}

// MockUnsafeChatbotNuggetServiceServerMockRecorder is the mock recorder for MockUnsafeChatbotNuggetServiceServer.
type MockUnsafeChatbotNuggetServiceServerMockRecorder struct {
	mock *MockUnsafeChatbotNuggetServiceServer
}

// NewMockUnsafeChatbotNuggetServiceServer creates a new mock instance.
func NewMockUnsafeChatbotNuggetServiceServer(ctrl *gomock.Controller) *MockUnsafeChatbotNuggetServiceServer {
	mock := &MockUnsafeChatbotNuggetServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeChatbotNuggetServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeChatbotNuggetServiceServer) EXPECT() *MockUnsafeChatbotNuggetServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedChatbotNuggetServiceServer mocks base method.
func (m *MockUnsafeChatbotNuggetServiceServer) mustEmbedUnimplementedChatbotNuggetServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedChatbotNuggetServiceServer")
}

// mustEmbedUnimplementedChatbotNuggetServiceServer indicates an expected call of mustEmbedUnimplementedChatbotNuggetServiceServer.
func (mr *MockUnsafeChatbotNuggetServiceServerMockRecorder) mustEmbedUnimplementedChatbotNuggetServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedChatbotNuggetServiceServer", reflect.TypeOf((*MockUnsafeChatbotNuggetServiceServer)(nil).mustEmbedUnimplementedChatbotNuggetServiceServer))
}
