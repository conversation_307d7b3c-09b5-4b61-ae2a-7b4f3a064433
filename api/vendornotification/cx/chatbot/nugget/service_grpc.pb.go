// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendornotification/cx/chatbot/nugget/service.proto

package nugget

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ChatbotNuggetService_FetchData_FullMethodName           = "/vendornotification.cx.chatbot.nugget.ChatbotNuggetService/FetchData"
	ChatbotNuggetService_HandleCallbackEvent_FullMethodName = "/vendornotification.cx.chatbot.nugget.ChatbotNuggetService/HandleCallbackEvent"
)

// ChatbotNuggetServiceClient is the client API for ChatbotNuggetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChatbotNuggetServiceClient interface {
	// Fetches requested data fields for a chatbot session.
	// The chatbot provides an access token and a list of fields it needs.
	// The backend responds with the requested key-value pairs.
	FetchData(ctx context.Context, in *FetchDataRequest, opts ...grpc.CallOption) (*FetchDataResponse, error)
	HandleCallbackEvent(ctx context.Context, in *HandleCallbackEventRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type chatbotNuggetServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewChatbotNuggetServiceClient(cc grpc.ClientConnInterface) ChatbotNuggetServiceClient {
	return &chatbotNuggetServiceClient{cc}
}

func (c *chatbotNuggetServiceClient) FetchData(ctx context.Context, in *FetchDataRequest, opts ...grpc.CallOption) (*FetchDataResponse, error) {
	out := new(FetchDataResponse)
	err := c.cc.Invoke(ctx, ChatbotNuggetService_FetchData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatbotNuggetServiceClient) HandleCallbackEvent(ctx context.Context, in *HandleCallbackEventRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ChatbotNuggetService_HandleCallbackEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatbotNuggetServiceServer is the server API for ChatbotNuggetService service.
// All implementations should embed UnimplementedChatbotNuggetServiceServer
// for forward compatibility
type ChatbotNuggetServiceServer interface {
	// Fetches requested data fields for a chatbot session.
	// The chatbot provides an access token and a list of fields it needs.
	// The backend responds with the requested key-value pairs.
	FetchData(context.Context, *FetchDataRequest) (*FetchDataResponse, error)
	HandleCallbackEvent(context.Context, *HandleCallbackEventRequest) (*emptypb.Empty, error)
}

// UnimplementedChatbotNuggetServiceServer should be embedded to have forward compatible implementations.
type UnimplementedChatbotNuggetServiceServer struct {
}

func (UnimplementedChatbotNuggetServiceServer) FetchData(context.Context, *FetchDataRequest) (*FetchDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchData not implemented")
}
func (UnimplementedChatbotNuggetServiceServer) HandleCallbackEvent(context.Context, *HandleCallbackEventRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleCallbackEvent not implemented")
}

// UnsafeChatbotNuggetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChatbotNuggetServiceServer will
// result in compilation errors.
type UnsafeChatbotNuggetServiceServer interface {
	mustEmbedUnimplementedChatbotNuggetServiceServer()
}

func RegisterChatbotNuggetServiceServer(s grpc.ServiceRegistrar, srv ChatbotNuggetServiceServer) {
	s.RegisterService(&ChatbotNuggetService_ServiceDesc, srv)
}

func _ChatbotNuggetService_FetchData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatbotNuggetServiceServer).FetchData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChatbotNuggetService_FetchData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatbotNuggetServiceServer).FetchData(ctx, req.(*FetchDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatbotNuggetService_HandleCallbackEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleCallbackEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatbotNuggetServiceServer).HandleCallbackEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChatbotNuggetService_HandleCallbackEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatbotNuggetServiceServer).HandleCallbackEvent(ctx, req.(*HandleCallbackEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ChatbotNuggetService_ServiceDesc is the grpc.ServiceDesc for ChatbotNuggetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChatbotNuggetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendornotification.cx.chatbot.nugget.ChatbotNuggetService",
	HandlerType: (*ChatbotNuggetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FetchData",
			Handler:    _ChatbotNuggetService_FetchData_Handler,
		},
		{
			MethodName: "HandleCallbackEvent",
			Handler:    _ChatbotNuggetService_HandleCallbackEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendornotification/cx/chatbot/nugget/service.proto",
}
