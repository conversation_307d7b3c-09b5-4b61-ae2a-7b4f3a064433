// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendornotification/cx/chatbot/nugget/service.proto

package nugget

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request message for FetchData.
// Contains the access token for authentication and a list of fields to fetch.
type FetchDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Access token representing the chatbot session or user.
	AccessToken string `protobuf:"bytes,1,opt,name=access_token,proto3" json:"access_token,omitempty"`
	// List of fields the chatbot wants to retrieve along with any input parameters.
	RequestedFields []*RequestedFieldInfo `protobuf:"bytes,2,rep,name=requested_fields,proto3" json:"requested_fields,omitempty"`
	// User ID for which data fields are requested
	UserId string `protobuf:"bytes,3,opt,name=user_id,proto3" json:"user_id,omitempty"`
}

func (x *FetchDataRequest) Reset() {
	*x = FetchDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDataRequest) ProtoMessage() {}

func (x *FetchDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDataRequest.ProtoReflect.Descriptor instead.
func (*FetchDataRequest) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescGZIP(), []int{0}
}

func (x *FetchDataRequest) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *FetchDataRequest) GetRequestedFields() []*RequestedFieldInfo {
	if x != nil {
		return x.RequestedFields
	}
	return nil
}

func (x *FetchDataRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type RequestedFieldInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// a field is a string form of the types mentioned in - api/typesv2/chat.proto:ChatbotRequestedDataField
	FieldName string `protobuf:"bytes,1,opt,name=field_name,proto3" json:"field_name,omitempty"`
	// input parameters - example : Transaction ID for fetching transaction details
	// note - it is not mandatory that an input parameter has to be passed, some requested fields may not require it
	InputParameters []*KeyValuePair `protobuf:"bytes,2,rep,name=input_parameters,proto3" json:"input_parameters,omitempty"`
}

func (x *RequestedFieldInfo) Reset() {
	*x = RequestedFieldInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestedFieldInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestedFieldInfo) ProtoMessage() {}

func (x *RequestedFieldInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestedFieldInfo.ProtoReflect.Descriptor instead.
func (*RequestedFieldInfo) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescGZIP(), []int{1}
}

func (x *RequestedFieldInfo) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *RequestedFieldInfo) GetInputParameters() []*KeyValuePair {
	if x != nil {
		return x.InputParameters
	}
	return nil
}

// Key-value pair representing a single data field and its value.
type KeyValuePair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string          `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value *structpb.Value `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *KeyValuePair) Reset() {
	*x = KeyValuePair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeyValuePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyValuePair) ProtoMessage() {}

func (x *KeyValuePair) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyValuePair.ProtoReflect.Descriptor instead.
func (*KeyValuePair) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescGZIP(), []int{2}
}

func (x *KeyValuePair) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *KeyValuePair) GetValue() *structpb.Value {
	if x != nil {
		return x.Value
	}
	return nil
}

type FetchDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of key-value pairs for the requested data fields.
	Data  []*KeyValuePair `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
	Error string          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *FetchDataResponse) Reset() {
	*x = FetchDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDataResponse) ProtoMessage() {}

func (x *FetchDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDataResponse.ProtoReflect.Descriptor instead.
func (*FetchDataResponse) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescGZIP(), []int{3}
}

func (x *FetchDataResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *FetchDataResponse) GetData() []*KeyValuePair {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *FetchDataResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type HandleCallbackEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entity       string          `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	EventName    string          `protobuf:"bytes,2,opt,name=event_name,json=event,proto3" json:"event_name,omitempty"`
	SubEventName string          `protobuf:"bytes,3,opt,name=sub_event_name,json=sub_event,proto3" json:"sub_event_name,omitempty"`
	EventData    *structpb.Value `protobuf:"bytes,4,opt,name=event_data,json=data,proto3" json:"event_data,omitempty"`
	Timestamp    int64           `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *HandleCallbackEventRequest) Reset() {
	*x = HandleCallbackEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleCallbackEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleCallbackEventRequest) ProtoMessage() {}

func (x *HandleCallbackEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleCallbackEventRequest.ProtoReflect.Descriptor instead.
func (*HandleCallbackEventRequest) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescGZIP(), []int{4}
}

func (x *HandleCallbackEventRequest) GetEntity() string {
	if x != nil {
		return x.Entity
	}
	return ""
}

func (x *HandleCallbackEventRequest) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *HandleCallbackEventRequest) GetSubEventName() string {
	if x != nil {
		return x.SubEventName
	}
	return ""
}

func (x *HandleCallbackEventRequest) GetEventData() *structpb.Value {
	if x != nil {
		return x.EventData
	}
	return nil
}

func (x *HandleCallbackEventRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_api_vendornotification_cx_chatbot_nugget_service_proto protoreflect.FileDescriptor

var file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDesc = []byte{
	0x0a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74,
	0x62, 0x6f, 0x74, 0x2f, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x78, 0x2e,
	0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x01, 0x0a, 0x10, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x64, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65,
	0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x22, 0x94, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5e, 0x0a, 0x10, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f,
	0x74, 0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x10, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0x4e, 0x0a, 0x0c, 0x4b, 0x65, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x11, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74,
	0x62, 0x6f, 0x74, 0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x22, 0xc2, 0x01, 0x0a, 0x1a, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x19, 0x0a, 0x0a, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x75, 0x62, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x32, 0xe7, 0x02, 0x0a, 0x14, 0x43, 0x68, 0x61,
	0x74, 0x62, 0x6f, 0x74, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0xa6, 0x01, 0x0a, 0x09, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x36, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2e,
	0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x78, 0x2e,
	0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2e, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x63, 0x78,
	0x2f, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2f, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2f,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x2d, 0x64, 0x61, 0x74, 0x61, 0x12, 0xa5, 0x01, 0x0a, 0x13, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x40, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x62,
	0x6f, 0x74, 0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x34, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22, 0x29, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61,
	0x74, 0x62, 0x6f, 0x74, 0x2f, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2f, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x2d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x73, 0x42, 0x82, 0x01, 0x0a, 0x3f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2e,
	0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74,
	0x2f, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescOnce sync.Once
	file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescData = file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDesc
)

func file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescGZIP() []byte {
	file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescOnce.Do(func() {
		file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescData)
	})
	return file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDescData
}

var file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_vendornotification_cx_chatbot_nugget_service_proto_goTypes = []interface{}{
	(*FetchDataRequest)(nil),           // 0: vendornotification.cx.chatbot.nugget.FetchDataRequest
	(*RequestedFieldInfo)(nil),         // 1: vendornotification.cx.chatbot.nugget.RequestedFieldInfo
	(*KeyValuePair)(nil),               // 2: vendornotification.cx.chatbot.nugget.KeyValuePair
	(*FetchDataResponse)(nil),          // 3: vendornotification.cx.chatbot.nugget.FetchDataResponse
	(*HandleCallbackEventRequest)(nil), // 4: vendornotification.cx.chatbot.nugget.HandleCallbackEventRequest
	(*structpb.Value)(nil),             // 5: google.protobuf.Value
	(*emptypb.Empty)(nil),              // 6: google.protobuf.Empty
}
var file_api_vendornotification_cx_chatbot_nugget_service_proto_depIdxs = []int32{
	1, // 0: vendornotification.cx.chatbot.nugget.FetchDataRequest.requested_fields:type_name -> vendornotification.cx.chatbot.nugget.RequestedFieldInfo
	2, // 1: vendornotification.cx.chatbot.nugget.RequestedFieldInfo.input_parameters:type_name -> vendornotification.cx.chatbot.nugget.KeyValuePair
	5, // 2: vendornotification.cx.chatbot.nugget.KeyValuePair.value:type_name -> google.protobuf.Value
	2, // 3: vendornotification.cx.chatbot.nugget.FetchDataResponse.data:type_name -> vendornotification.cx.chatbot.nugget.KeyValuePair
	5, // 4: vendornotification.cx.chatbot.nugget.HandleCallbackEventRequest.event_data:type_name -> google.protobuf.Value
	0, // 5: vendornotification.cx.chatbot.nugget.ChatbotNuggetService.FetchData:input_type -> vendornotification.cx.chatbot.nugget.FetchDataRequest
	4, // 6: vendornotification.cx.chatbot.nugget.ChatbotNuggetService.HandleCallbackEvent:input_type -> vendornotification.cx.chatbot.nugget.HandleCallbackEventRequest
	3, // 7: vendornotification.cx.chatbot.nugget.ChatbotNuggetService.FetchData:output_type -> vendornotification.cx.chatbot.nugget.FetchDataResponse
	6, // 8: vendornotification.cx.chatbot.nugget.ChatbotNuggetService.HandleCallbackEvent:output_type -> google.protobuf.Empty
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_vendornotification_cx_chatbot_nugget_service_proto_init() }
func file_api_vendornotification_cx_chatbot_nugget_service_proto_init() {
	if File_api_vendornotification_cx_chatbot_nugget_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestedFieldInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeyValuePair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleCallbackEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendornotification_cx_chatbot_nugget_service_proto_goTypes,
		DependencyIndexes: file_api_vendornotification_cx_chatbot_nugget_service_proto_depIdxs,
		MessageInfos:      file_api_vendornotification_cx_chatbot_nugget_service_proto_msgTypes,
	}.Build()
	File_api_vendornotification_cx_chatbot_nugget_service_proto = out.File
	file_api_vendornotification_cx_chatbot_nugget_service_proto_rawDesc = nil
	file_api_vendornotification_cx_chatbot_nugget_service_proto_goTypes = nil
	file_api_vendornotification_cx_chatbot_nugget_service_proto_depIdxs = nil
}
