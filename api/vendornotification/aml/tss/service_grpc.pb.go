// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendornotification/aml/tss/service.proto

package tss

import (
	context "context"
	tss "github.com/epifi/gamma/api/vendors/tss"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TSS_ProcessWebhookCallBack_FullMethodName = "/vendornotification.aml.tss.TSS/ProcessWebhookCallBack"
	TSS_ProcessWHS501Callback_FullMethodName  = "/vendornotification.aml.tss.TSS/ProcessWHS501Callback"
)

// TSSClient is the client API for TSS service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TSSClient interface {
	ProcessWebhookCallBack(ctx context.Context, in *tss.ProcessWebhookCallBackRequest, opts ...grpc.CallOption) (*tss.ProcessWebhookCallBackResponse, error)
	// Webhook to capture and process initial screening case details.
	// Triggered when the case reviewer clicks on "Close With No Action"
	// or "Close With Action" buttons on the TSS's Screenza web portal.
	ProcessWHS501Callback(ctx context.Context, in *tss.ProcessWHS501CallbackRequest, opts ...grpc.CallOption) (*tss.ProcessWHS501CallbackResponse, error)
}

type tSSClient struct {
	cc grpc.ClientConnInterface
}

func NewTSSClient(cc grpc.ClientConnInterface) TSSClient {
	return &tSSClient{cc}
}

func (c *tSSClient) ProcessWebhookCallBack(ctx context.Context, in *tss.ProcessWebhookCallBackRequest, opts ...grpc.CallOption) (*tss.ProcessWebhookCallBackResponse, error) {
	out := new(tss.ProcessWebhookCallBackResponse)
	err := c.cc.Invoke(ctx, TSS_ProcessWebhookCallBack_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tSSClient) ProcessWHS501Callback(ctx context.Context, in *tss.ProcessWHS501CallbackRequest, opts ...grpc.CallOption) (*tss.ProcessWHS501CallbackResponse, error) {
	out := new(tss.ProcessWHS501CallbackResponse)
	err := c.cc.Invoke(ctx, TSS_ProcessWHS501Callback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TSSServer is the server API for TSS service.
// All implementations should embed UnimplementedTSSServer
// for forward compatibility
type TSSServer interface {
	ProcessWebhookCallBack(context.Context, *tss.ProcessWebhookCallBackRequest) (*tss.ProcessWebhookCallBackResponse, error)
	// Webhook to capture and process initial screening case details.
	// Triggered when the case reviewer clicks on "Close With No Action"
	// or "Close With Action" buttons on the TSS's Screenza web portal.
	ProcessWHS501Callback(context.Context, *tss.ProcessWHS501CallbackRequest) (*tss.ProcessWHS501CallbackResponse, error)
}

// UnimplementedTSSServer should be embedded to have forward compatible implementations.
type UnimplementedTSSServer struct {
}

func (UnimplementedTSSServer) ProcessWebhookCallBack(context.Context, *tss.ProcessWebhookCallBackRequest) (*tss.ProcessWebhookCallBackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessWebhookCallBack not implemented")
}
func (UnimplementedTSSServer) ProcessWHS501Callback(context.Context, *tss.ProcessWHS501CallbackRequest) (*tss.ProcessWHS501CallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessWHS501Callback not implemented")
}

// UnsafeTSSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TSSServer will
// result in compilation errors.
type UnsafeTSSServer interface {
	mustEmbedUnimplementedTSSServer()
}

func RegisterTSSServer(s grpc.ServiceRegistrar, srv TSSServer) {
	s.RegisterService(&TSS_ServiceDesc, srv)
}

func _TSS_ProcessWebhookCallBack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tss.ProcessWebhookCallBackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TSSServer).ProcessWebhookCallBack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TSS_ProcessWebhookCallBack_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TSSServer).ProcessWebhookCallBack(ctx, req.(*tss.ProcessWebhookCallBackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TSS_ProcessWHS501Callback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tss.ProcessWHS501CallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TSSServer).ProcessWHS501Callback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TSS_ProcessWHS501Callback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TSSServer).ProcessWHS501Callback(ctx, req.(*tss.ProcessWHS501CallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TSS_ServiceDesc is the grpc.ServiceDesc for TSS service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TSS_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendornotification.aml.tss.TSS",
	HandlerType: (*TSSServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessWebhookCallBack",
			Handler:    _TSS_ProcessWebhookCallBack_Handler,
		},
		{
			MethodName: "ProcessWHS501Callback",
			Handler:    _TSS_ProcessWHS501Callback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendornotification/aml/tss/service.proto",
}
