package savings

import (
	"github.com/epifi/be-common/pkg/constants"
)

// HasClosedAccountBalanceData checks if we have any closed account balance data in CBT
//
// 1. LastKnownBalance is captured and stored when <PERSON> revokes app access when the federal savings account is still
// active. We capture the balance of account using balance api and store it here. This balance is currently captured
// only for min kyc access revoked accounts
// 2. ReportedClosureBalance is stored when Federal shares the Dr. Operative file for the txns from user's savings
// account to federal's pool account. This file is currently shared only for Min kyc closed accounts.
// 3. BalanceCapturedFromStatement is the balance captured by fetching the account statement of user's account during
// the time of account closure. We identify the balance by checking the transaction which has particulars as 'Dr. Operative'
func (m *ClosedAccountBalanceTransfer) HasClosedAccountBalanceData() bool {
	return m.GetLastKnownBalance() != nil || m.GetReportedClosureBalance() != nil || m.GetBalanceCapturedFromStatement() != nil
}

// IsBalanceTransferCompleted checks if the balance transfer from federal pool account to user's alternate account is
// done.
func (m *ClosedAccountBalanceTransfer) IsBalanceTransferCompleted() bool {
	return m.GetUtr() != ""
}

// HasClosedAccountZeroBalance checks if there is no balance/less than threshold in the closed account
func (m *ClosedAccountBalanceTransfer) HasClosedAccountZeroBalance() bool {

	switch {
	case m.GetTransactionStatus() == CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS || m.GetReportedClosureBalance() != nil && m.GetReportedClosureBalance().GetUnits() <= constants.ClosedAccountBalanceTransferMinThreshold:
		return true
	case m.GetReportedClosureBalance() == nil && m.GetLastKnownBalance().GetAvailableBalance() != nil && m.GetLastKnownBalance().GetAvailableBalance().GetUnits() <= constants.ClosedAccountBalanceTransferMinThreshold:
		return true
	case m.GetBalanceCapturedFromStatement() != nil && m.GetBalanceCapturedFromStatement().GetUnits() <= constants.ClosedAccountBalanceTransferMinThreshold:
		return true
	}

	return false
}
