// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/aml/service.proto

package aml

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	aml "github.com/epifi/gamma/api/aml"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = aml.AmlEntity(0)

	_ = common.Owner(0)
)

// Validate checks the field values on ScreenCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenCustomerRequestMultiError, or nil if none found.
func (m *ScreenCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenCustomerRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecordIdentifier

	// no validation rules for VendorRequestId

	// no validation rules for Entity

	// no validation rules for Product

	if m.GetCustomerDetails() == nil {
		err := ScreenCustomerRequestValidationError{
			field:  "CustomerDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCustomerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenCustomerRequestValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenCustomerRequestValidationError{
				field:  "CustomerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Owner

	if len(errors) > 0 {
		return ScreenCustomerRequestMultiError(errors)
	}

	return nil
}

// ScreenCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by ScreenCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type ScreenCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenCustomerRequestMultiError) AllErrors() []error { return m }

// ScreenCustomerRequestValidationError is the validation error returned by
// ScreenCustomerRequest.Validate if the designated constraints aren't met.
type ScreenCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenCustomerRequestValidationError) ErrorName() string {
	return "ScreenCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenCustomerRequestValidationError{}

// Validate checks the field values on ScreenCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScreenCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScreenCustomerResponseMultiError, or nil if none found.
func (m *ScreenCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScreenCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScreenCustomerResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScreenCustomerResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RejectionMessage

	// no validation rules for RejectionCode

	// no validation rules for RecordIdentifier

	// no validation rules for MatchStatus

	// no validation rules for CaseId

	// no validation rules for CaseLink

	// no validation rules for AlertCount

	for idx, item := range m.GetMatchDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScreenCustomerResponseValidationError{
						field:  fmt.Sprintf("MatchDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScreenCustomerResponseValidationError{
						field:  fmt.Sprintf("MatchDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScreenCustomerResponseValidationError{
					field:  fmt.Sprintf("MatchDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ScreenCustomerResponseMultiError(errors)
	}

	return nil
}

// ScreenCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by ScreenCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type ScreenCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenCustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenCustomerResponseMultiError) AllErrors() []error { return m }

// ScreenCustomerResponseValidationError is the validation error returned by
// ScreenCustomerResponse.Validate if the designated constraints aren't met.
type ScreenCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenCustomerResponseValidationError) ErrorName() string {
	return "ScreenCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ScreenCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenCustomerResponseValidationError{}

// Validate checks the field values on CustomerDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomerDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerDetailsMultiError, or nil if none found.
func (m *CustomerDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetName() == nil {
		err := CustomerDetailsValidationError{
			field:  "Name",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	// no validation rules for MaritalStatus

	// no validation rules for IncomeSlab

	// no validation rules for PanNumber

	if _, ok := _CustomerDetails_Nationality_NotInLookup[m.GetNationality()]; ok {
		err := CustomerDetailsValidationError{
			field:  "Nationality",
			reason: "value must not be in list [NATIONALITY_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PassportNumber

	if all {
		switch v := interface{}(m.GetPassportExpiryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PassportExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PassportExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassportExpiryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PassportExpiryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DrivingLicenseNumber

	if all {
		switch v := interface{}(m.GetDrivingLicenseExpiryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "DrivingLicenseExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "DrivingLicenseExpiryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDrivingLicenseExpiryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "DrivingLicenseExpiryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VoterId

	// no validation rules for PoaType

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCorrespondenceAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCorrespondenceAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "CorrespondenceAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PoliticallyExposedStatus

	// no validation rules for EmploymentType

	if len(errors) > 0 {
		return CustomerDetailsMultiError(errors)
	}

	return nil
}

// CustomerDetailsMultiError is an error wrapping multiple validation errors
// returned by CustomerDetails.ValidateAll() if the designated constraints
// aren't met.
type CustomerDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerDetailsMultiError) AllErrors() []error { return m }

// CustomerDetailsValidationError is the validation error returned by
// CustomerDetails.Validate if the designated constraints aren't met.
type CustomerDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerDetailsValidationError) ErrorName() string { return "CustomerDetailsValidationError" }

// Error satisfies the builtin error interface
func (e CustomerDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerDetailsValidationError{}

var _CustomerDetails_Nationality_NotInLookup = map[common.Nationality]struct{}{
	0: {},
}

// Validate checks the field values on InitiateScreeningRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateScreeningRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateScreeningRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateScreeningRequestMultiError, or nil if none found.
func (m *InitiateScreeningRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateScreeningRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateScreeningRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateScreeningRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateScreeningRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Owner

	// no validation rules for UserRecordId

	// no validation rules for VendorRequestId

	// no validation rules for Product

	if m.GetCustomerDetails() == nil {
		err := InitiateScreeningRequestValidationError{
			field:  "CustomerDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCustomerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateScreeningRequestValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateScreeningRequestValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateScreeningRequestValidationError{
				field:  "CustomerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateScreeningRequestMultiError(errors)
	}

	return nil
}

// InitiateScreeningRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateScreeningRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateScreeningRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateScreeningRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateScreeningRequestMultiError) AllErrors() []error { return m }

// InitiateScreeningRequestValidationError is the validation error returned by
// InitiateScreeningRequest.Validate if the designated constraints aren't met.
type InitiateScreeningRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateScreeningRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateScreeningRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateScreeningRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateScreeningRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateScreeningRequestValidationError) ErrorName() string {
	return "InitiateScreeningRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateScreeningRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateScreeningRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateScreeningRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateScreeningRequestValidationError{}

// Validate checks the field values on HitResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HitResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HitResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HitResponseMultiError, or
// nil if none found.
func (m *HitResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *HitResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Source

	// no validation rules for WatchlistSourceId

	// no validation rules for MatchType

	// no validation rules for Score

	// no validation rules for ConfirmedMatchingAttributes

	if len(errors) > 0 {
		return HitResponseMultiError(errors)
	}

	return nil
}

// HitResponseMultiError is an error wrapping multiple validation errors
// returned by HitResponse.ValidateAll() if the designated constraints aren't met.
type HitResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HitResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HitResponseMultiError) AllErrors() []error { return m }

// HitResponseValidationError is the validation error returned by
// HitResponse.Validate if the designated constraints aren't met.
type HitResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HitResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HitResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HitResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HitResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HitResponseValidationError) ErrorName() string { return "HitResponseValidationError" }

// Error satisfies the builtin error interface
func (e HitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHitResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HitResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HitResponseValidationError{}

// Validate checks the field values on ScreeningData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScreeningData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreeningData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScreeningDataMultiError, or
// nil if none found.
func (m *ScreeningData) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreeningData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HitsDetected

	// no validation rules for HitsCount

	// no validation rules for ConfirmedHit

	// no validation rules for CaseId

	// no validation rules for CaseUrl

	// no validation rules for ReportData

	// no validation rules for ProfileCode

	for idx, item := range m.GetHitResponse() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScreeningDataValidationError{
						field:  fmt.Sprintf("HitResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScreeningDataValidationError{
						field:  fmt.Sprintf("HitResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScreeningDataValidationError{
					field:  fmt.Sprintf("HitResponse[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ScreeningDataMultiError(errors)
	}

	return nil
}

// ScreeningDataMultiError is an error wrapping multiple validation errors
// returned by ScreeningData.ValidateAll() if the designated constraints
// aren't met.
type ScreeningDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreeningDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreeningDataMultiError) AllErrors() []error { return m }

// ScreeningDataValidationError is the validation error returned by
// ScreeningData.Validate if the designated constraints aren't met.
type ScreeningDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreeningDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreeningDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreeningDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreeningDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreeningDataValidationError) ErrorName() string { return "ScreeningDataValidationError" }

// Error satisfies the builtin error interface
func (e ScreeningDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreeningData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreeningDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreeningDataValidationError{}

// Validate checks the field values on PurposeResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PurposeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurposeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PurposeResponseMultiError, or nil if none found.
func (m *PurposeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PurposeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Purpose

	// no validation rules for PurposeCode

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	// no validation rules for ValidationFailureCount

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PurposeResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PurposeResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PurposeResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PurposeResponseMultiError(errors)
	}

	return nil
}

// PurposeResponseMultiError is an error wrapping multiple validation errors
// returned by PurposeResponse.ValidateAll() if the designated constraints
// aren't met.
type PurposeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurposeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurposeResponseMultiError) AllErrors() []error { return m }

// PurposeResponseValidationError is the validation error returned by
// PurposeResponse.Validate if the designated constraints aren't met.
type PurposeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurposeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurposeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurposeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurposeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurposeResponseValidationError) ErrorName() string { return "PurposeResponseValidationError" }

// Error satisfies the builtin error interface
func (e PurposeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurposeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurposeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurposeResponseValidationError{}

// Validate checks the field values on CustomerResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerResponseMultiError, or nil if none found.
func (m *CustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceSystemCustomerCode

	// no validation rules for ApplicationRefNumber

	// no validation rules for ValidationOutcome

	// no validation rules for SuggestedAction

	for idx, item := range m.GetPurposeResponse() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerResponseValidationError{
						field:  fmt.Sprintf("PurposeResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerResponseValidationError{
						field:  fmt.Sprintf("PurposeResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerResponseValidationError{
					field:  fmt.Sprintf("PurposeResponse[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	// no validation rules for ValidationFailureCount

	if len(errors) > 0 {
		return CustomerResponseMultiError(errors)
	}

	return nil
}

// CustomerResponseMultiError is an error wrapping multiple validation errors
// returned by CustomerResponse.ValidateAll() if the designated constraints
// aren't met.
type CustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerResponseMultiError) AllErrors() []error { return m }

// CustomerResponseValidationError is the validation error returned by
// CustomerResponse.Validate if the designated constraints aren't met.
type CustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerResponseValidationError) ErrorName() string { return "CustomerResponseValidationError" }

// Error satisfies the builtin error interface
func (e CustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerResponseValidationError{}

// Validate checks the field values on InitiateScreeningResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateScreeningResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateScreeningResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateScreeningResponseMultiError, or nil if none found.
func (m *InitiateScreeningResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateScreeningResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateScreeningResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateScreeningResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateScreeningResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for OverallStatus

	// no validation rules for ValidationCode

	// no validation rules for ValidationDescription

	for idx, item := range m.GetCustomerResponse() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiateScreeningResponseValidationError{
						field:  fmt.Sprintf("CustomerResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiateScreeningResponseValidationError{
						field:  fmt.Sprintf("CustomerResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiateScreeningResponseValidationError{
					field:  fmt.Sprintf("CustomerResponse[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRelatedPersonResponse() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiateScreeningResponseValidationError{
						field:  fmt.Sprintf("RelatedPersonResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiateScreeningResponseValidationError{
						field:  fmt.Sprintf("RelatedPersonResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiateScreeningResponseValidationError{
					field:  fmt.Sprintf("RelatedPersonResponse[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRelatedPersonRelationResponse() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InitiateScreeningResponseValidationError{
						field:  fmt.Sprintf("RelatedPersonRelationResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InitiateScreeningResponseValidationError{
						field:  fmt.Sprintf("RelatedPersonRelationResponse[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InitiateScreeningResponseValidationError{
					field:  fmt.Sprintf("RelatedPersonRelationResponse[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InitiateScreeningResponseMultiError(errors)
	}

	return nil
}

// InitiateScreeningResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateScreeningResponse.ValidateAll() if the
// designated constraints aren't met.
type InitiateScreeningResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateScreeningResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateScreeningResponseMultiError) AllErrors() []error { return m }

// InitiateScreeningResponseValidationError is the validation error returned by
// InitiateScreeningResponse.Validate if the designated constraints aren't met.
type InitiateScreeningResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateScreeningResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateScreeningResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateScreeningResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateScreeningResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateScreeningResponseValidationError) ErrorName() string {
	return "InitiateScreeningResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateScreeningResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateScreeningResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateScreeningResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateScreeningResponseValidationError{}
