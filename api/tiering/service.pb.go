// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/tiering/service.proto

package tiering

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	sherlock_banners "github.com/epifi/gamma/api/cx/sherlock_banners"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	enums "github.com/epifi/gamma/api/tiering/enums"
	external "github.com/epifi/gamma/api/tiering/external"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetTieringPitchV2Response_Status int32

const (
	GetTieringPitchV2Response_OK       GetTieringPitchV2Response_Status = 0
	GetTieringPitchV2Response_INTERNAL GetTieringPitchV2Response_Status = 13
	GetTieringPitchV2Response_DISABLED GetTieringPitchV2Response_Status = 101
)

// Enum value maps for GetTieringPitchV2Response_Status.
var (
	GetTieringPitchV2Response_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "DISABLED",
	}
	GetTieringPitchV2Response_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
		"DISABLED": 101,
	}
)

func (x GetTieringPitchV2Response_Status) Enum() *GetTieringPitchV2Response_Status {
	p := new(GetTieringPitchV2Response_Status)
	*p = x
	return p
}

func (x GetTieringPitchV2Response_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTieringPitchV2Response_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tiering_service_proto_enumTypes[0].Descriptor()
}

func (GetTieringPitchV2Response_Status) Type() protoreflect.EnumType {
	return &file_api_tiering_service_proto_enumTypes[0]
}

func (x GetTieringPitchV2Response_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTieringPitchV2Response_Status.Descriptor instead.
func (GetTieringPitchV2Response_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{8, 0}
}

type GetTierAtTimeResponse_Status int32

const (
	GetTierAtTimeResponse_OK       GetTierAtTimeResponse_Status = 0
	GetTierAtTimeResponse_INTERNAL GetTierAtTimeResponse_Status = 13
	GetTierAtTimeResponse_DISABLED GetTierAtTimeResponse_Status = 101
)

// Enum value maps for GetTierAtTimeResponse_Status.
var (
	GetTierAtTimeResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "DISABLED",
	}
	GetTierAtTimeResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
		"DISABLED": 101,
	}
)

func (x GetTierAtTimeResponse_Status) Enum() *GetTierAtTimeResponse_Status {
	p := new(GetTierAtTimeResponse_Status)
	*p = x
	return p
}

func (x GetTierAtTimeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTierAtTimeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tiering_service_proto_enumTypes[1].Descriptor()
}

func (GetTierAtTimeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_tiering_service_proto_enumTypes[1]
}

func (x GetTierAtTimeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTierAtTimeResponse_Status.Descriptor instead.
func (GetTierAtTimeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{17, 0}
}

type UpgradeResponse_Status int32

const (
	UpgradeResponse_OK         UpgradeResponse_Status = 0
	UpgradeResponse_DOWNGRADED UpgradeResponse_Status = 101
)

// Enum value maps for UpgradeResponse_Status.
var (
	UpgradeResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "DOWNGRADED",
	}
	UpgradeResponse_Status_value = map[string]int32{
		"OK":         0,
		"DOWNGRADED": 101,
	}
)

func (x UpgradeResponse_Status) Enum() *UpgradeResponse_Status {
	p := new(UpgradeResponse_Status)
	*p = x
	return p
}

func (x UpgradeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpgradeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tiering_service_proto_enumTypes[2].Descriptor()
}

func (UpgradeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_tiering_service_proto_enumTypes[2]
}

func (x UpgradeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpgradeResponse_Status.Descriptor instead.
func (UpgradeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{21, 0}
}

type GetDetailsForCxResponse_Status int32

const (
	GetDetailsForCxResponse_OK       GetDetailsForCxResponse_Status = 0
	GetDetailsForCxResponse_INTERNAL GetDetailsForCxResponse_Status = 13
	GetDetailsForCxResponse_DISABLED GetDetailsForCxResponse_Status = 101
)

// Enum value maps for GetDetailsForCxResponse_Status.
var (
	GetDetailsForCxResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "DISABLED",
	}
	GetDetailsForCxResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
		"DISABLED": 101,
	}
)

func (x GetDetailsForCxResponse_Status) Enum() *GetDetailsForCxResponse_Status {
	p := new(GetDetailsForCxResponse_Status)
	*p = x
	return p
}

func (x GetDetailsForCxResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetDetailsForCxResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tiering_service_proto_enumTypes[3].Descriptor()
}

func (GetDetailsForCxResponse_Status) Type() protoreflect.EnumType {
	return &file_api_tiering_service_proto_enumTypes[3]
}

func (x GetDetailsForCxResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetDetailsForCxResponse_Status.Descriptor instead.
func (GetDetailsForCxResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{31, 0}
}

type GetActorScreenInteractionDetailsRequest_RequestType int32

const (
	GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_UNSPECIFIED GetActorScreenInteractionDetailsRequest_RequestType = 0
	GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_ADD         GetActorScreenInteractionDetailsRequest_RequestType = 1
	GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_GET         GetActorScreenInteractionDetailsRequest_RequestType = 2
)

// Enum value maps for GetActorScreenInteractionDetailsRequest_RequestType.
var (
	GetActorScreenInteractionDetailsRequest_RequestType_name = map[int32]string{
		0: "REQUEST_TYPE_UNSPECIFIED",
		1: "REQUEST_TYPE_ADD",
		2: "REQUEST_TYPE_GET",
	}
	GetActorScreenInteractionDetailsRequest_RequestType_value = map[string]int32{
		"REQUEST_TYPE_UNSPECIFIED": 0,
		"REQUEST_TYPE_ADD":         1,
		"REQUEST_TYPE_GET":         2,
	}
)

func (x GetActorScreenInteractionDetailsRequest_RequestType) Enum() *GetActorScreenInteractionDetailsRequest_RequestType {
	p := new(GetActorScreenInteractionDetailsRequest_RequestType)
	*p = x
	return p
}

func (x GetActorScreenInteractionDetailsRequest_RequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActorScreenInteractionDetailsRequest_RequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tiering_service_proto_enumTypes[4].Descriptor()
}

func (GetActorScreenInteractionDetailsRequest_RequestType) Type() protoreflect.EnumType {
	return &file_api_tiering_service_proto_enumTypes[4]
}

func (x GetActorScreenInteractionDetailsRequest_RequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActorScreenInteractionDetailsRequest_RequestType.Descriptor instead.
func (GetActorScreenInteractionDetailsRequest_RequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{49, 0}
}

type GetDropOffBottomSheetRequest_TieringPitchMethod int32

const (
	GetDropOffBottomSheetRequest_TIERING_PITCH_METHOD_UNSPECIFIED GetDropOffBottomSheetRequest_TieringPitchMethod = 0
	// Through Add Funds
	GetDropOffBottomSheetRequest_ADD_FUNDS GetDropOffBottomSheetRequest_TieringPitchMethod = 1
	// Through Fixed Deposit
	GetDropOffBottomSheetRequest_FIXED_DEPOSIT GetDropOffBottomSheetRequest_TieringPitchMethod = 2
	// Through US Stocks
	GetDropOffBottomSheetRequest_US_STOCKS GetDropOffBottomSheetRequest_TieringPitchMethod = 3
)

// Enum value maps for GetDropOffBottomSheetRequest_TieringPitchMethod.
var (
	GetDropOffBottomSheetRequest_TieringPitchMethod_name = map[int32]string{
		0: "TIERING_PITCH_METHOD_UNSPECIFIED",
		1: "ADD_FUNDS",
		2: "FIXED_DEPOSIT",
		3: "US_STOCKS",
	}
	GetDropOffBottomSheetRequest_TieringPitchMethod_value = map[string]int32{
		"TIERING_PITCH_METHOD_UNSPECIFIED": 0,
		"ADD_FUNDS":                        1,
		"FIXED_DEPOSIT":                    2,
		"US_STOCKS":                        3,
	}
)

func (x GetDropOffBottomSheetRequest_TieringPitchMethod) Enum() *GetDropOffBottomSheetRequest_TieringPitchMethod {
	p := new(GetDropOffBottomSheetRequest_TieringPitchMethod)
	*p = x
	return p
}

func (x GetDropOffBottomSheetRequest_TieringPitchMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetDropOffBottomSheetRequest_TieringPitchMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_api_tiering_service_proto_enumTypes[5].Descriptor()
}

func (GetDropOffBottomSheetRequest_TieringPitchMethod) Type() protoreflect.EnumType {
	return &file_api_tiering_service_proto_enumTypes[5]
}

func (x GetDropOffBottomSheetRequest_TieringPitchMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetDropOffBottomSheetRequest_TieringPitchMethod.Descriptor instead.
func (GetDropOffBottomSheetRequest_TieringPitchMethod) EnumDescriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{51, 0}
}

type GetTrialDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetTrialDetailsRequest) Reset() {
	*x = GetTrialDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrialDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrialDetailsRequest) ProtoMessage() {}

func (x *GetTrialDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrialDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetTrialDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetTrialDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetTrialDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Trial details opted by the actor, if any
	TrialDetails *external.TrialDetails `protobuf:"bytes,2,opt,name=trial_details,json=trialDetails,proto3" json:"trial_details,omitempty"`
	// Flag to represent if the user is eligible for trial
	// is false if user has an active trial
	// is false if user did not pass the eligibility checks for trial
	// is false if user's current tier is greater than or equal to eligible tier
	IsEligibleForTrial bool `protobuf:"varint,3,opt,name=is_eligible_for_trial,json=isEligibleForTrial,proto3" json:"is_eligible_for_trial,omitempty"`
	// trial tier eligible for the actor
	EligibleTrialTier external.Tier `protobuf:"varint,4,opt,name=eligible_trial_tier,json=eligibleTrialTier,proto3,enum=tiering.external.Tier" json:"eligible_trial_tier,omitempty"`
	// trial intro deeplink (if actor is eligible for trial)
	Deeplink *deeplink.Deeplink `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *GetTrialDetailsResponse) Reset() {
	*x = GetTrialDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrialDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrialDetailsResponse) ProtoMessage() {}

func (x *GetTrialDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrialDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetTrialDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetTrialDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTrialDetailsResponse) GetTrialDetails() *external.TrialDetails {
	if x != nil {
		return x.TrialDetails
	}
	return nil
}

func (x *GetTrialDetailsResponse) GetIsEligibleForTrial() bool {
	if x != nil {
		return x.IsEligibleForTrial
	}
	return false
}

func (x *GetTrialDetailsResponse) GetEligibleTrialTier() external.Tier {
	if x != nil {
		return x.EligibleTrialTier
	}
	return external.Tier(0)
}

func (x *GetTrialDetailsResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type GetCurrentTierForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The actor ID for which to fetch the current tier
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetCurrentTierForActorRequest) Reset() {
	*x = GetCurrentTierForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrentTierForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentTierForActorRequest) ProtoMessage() {}

func (x *GetCurrentTierForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentTierForActorRequest.ProtoReflect.Descriptor instead.
func (*GetCurrentTierForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCurrentTierForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetCurrentTierForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Current tier of the actor
	// Will be actor's base tier if actor is not tiered yet
	Tier external.Tier `protobuf:"varint,2,opt,name=tier,proto3,enum=tiering.external.Tier" json:"tier,omitempty"`
}

func (x *GetCurrentTierForActorResponse) Reset() {
	*x = GetCurrentTierForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrentTierForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentTierForActorResponse) ProtoMessage() {}

func (x *GetCurrentTierForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentTierForActorResponse.ProtoReflect.Descriptor instead.
func (*GetCurrentTierForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetCurrentTierForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCurrentTierForActorResponse) GetTier() external.Tier {
	if x != nil {
		return x.Tier
	}
	return external.Tier(0)
}

type IsUserEligibleForRewardsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string     `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Date    *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *IsUserEligibleForRewardsRequest) Reset() {
	*x = IsUserEligibleForRewardsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsUserEligibleForRewardsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsUserEligibleForRewardsRequest) ProtoMessage() {}

func (x *IsUserEligibleForRewardsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsUserEligibleForRewardsRequest.ProtoReflect.Descriptor instead.
func (*IsUserEligibleForRewardsRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{4}
}

func (x *IsUserEligibleForRewardsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *IsUserEligibleForRewardsRequest) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

type IsUserEligibleForRewardsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of tier to eligibility status mapping
	TierToEligibilityStatus []*IsUserEligibleForRewardsResponse_TierToEligibilityStatus `protobuf:"bytes,2,rep,name=tier_to_eligibility_status,json=tierToEligibilityStatus,proto3" json:"tier_to_eligibility_status,omitempty"`
}

func (x *IsUserEligibleForRewardsResponse) Reset() {
	*x = IsUserEligibleForRewardsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsUserEligibleForRewardsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsUserEligibleForRewardsResponse) ProtoMessage() {}

func (x *IsUserEligibleForRewardsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsUserEligibleForRewardsResponse.ProtoReflect.Descriptor instead.
func (*IsUserEligibleForRewardsResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{5}
}

func (x *IsUserEligibleForRewardsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsUserEligibleForRewardsResponse) GetTierToEligibilityStatus() []*IsUserEligibleForRewardsResponse_TierToEligibilityStatus {
	if x != nil {
		return x.TierToEligibilityStatus
	}
	return nil
}

type GetActorDistinctTiersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to get the distinct tiers
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Time range for getting the distinct tiers. The range will be specified time here till now.
	// MANDATORY field
	TimeSince *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=time_since,json=timeSince,proto3" json:"time_since,omitempty"`
}

func (x *GetActorDistinctTiersRequest) Reset() {
	*x = GetActorDistinctTiersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorDistinctTiersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorDistinctTiersRequest) ProtoMessage() {}

func (x *GetActorDistinctTiersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorDistinctTiersRequest.ProtoReflect.Descriptor instead.
func (*GetActorDistinctTiersRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetActorDistinctTiersRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetActorDistinctTiersRequest) GetTimeSince() *timestamppb.Timestamp {
	if x != nil {
		return x.TimeSince
	}
	return nil
}

type GetActorDistinctTiersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of distinct tiers user has been in
	DistinctTiers []external.Tier `protobuf:"varint,2,rep,packed,name=distinct_tiers,json=distinctTiers,proto3,enum=tiering.external.Tier" json:"distinct_tiers,omitempty"`
}

func (x *GetActorDistinctTiersResponse) Reset() {
	*x = GetActorDistinctTiersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorDistinctTiersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorDistinctTiersResponse) ProtoMessage() {}

func (x *GetActorDistinctTiersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorDistinctTiersResponse.ProtoReflect.Descriptor instead.
func (*GetActorDistinctTiersResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetActorDistinctTiersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActorDistinctTiersResponse) GetDistinctTiers() []external.Tier {
	if x != nil {
		return x.DistinctTiers
	}
	return nil
}

type GetTieringPitchV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of RPC response
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// current tier of the actor
	CurrentTier external.Tier `protobuf:"varint,2,opt,name=current_tier,json=currentTier,proto3,enum=tiering.external.Tier" json:"current_tier,omitempty"`
	// details associated to move from current tier to the next tier
	// list will always be sorted in increasing order of tiers
	MovementDetailsList []*external.MovementExternalDetails `protobuf:"bytes,3,rep,name=movement_details_list,json=movementDetailsList,proto3" json:"movement_details_list,omitempty"`
	// details of the latest upgrade that happened for the user
	// can be nil if no upgrades happened
	LastUpgradeDetails *external.LatestMovementDetails `protobuf:"bytes,4,opt,name=last_upgrade_details,json=lastUpgradeDetails,proto3" json:"last_upgrade_details,omitempty"`
	// details of the latest downgrade that happened for the user
	// can be nil if no downgrades happened
	LastDowngradeDetails *external.LatestMovementDetails `protobuf:"bytes,5,opt,name=last_downgrade_details,json=lastDowngradeDetails,proto3" json:"last_downgrade_details,omitempty"`
	// base tier for the actor - will be either Standard or Regular
	ActorBaseTier external.Tier `protobuf:"varint,6,opt,name=actor_base_tier,json=actorBaseTier,proto3,enum=tiering.external.Tier" json:"actor_base_tier,omitempty"`
	// The criteria option type used when the user initially qualified for their current tier.
	EntryCriteriaOptionType enums.CriteriaOptionType `protobuf:"varint,7,opt,name=entry_criteria_option_type,json=entryCriteriaOptionType,proto3,enum=tiering.enums.CriteriaOptionType" json:"entry_criteria_option_type,omitempty"`
	// The criteria option type currently being used to evaluate the user's tier eligibility.
	CurrentCriteriaOptionType  enums.CriteriaOptionType   `protobuf:"varint,8,opt,name=current_criteria_option_type,json=currentCriteriaOptionType,proto3,enum=tiering.enums.CriteriaOptionType" json:"current_criteria_option_type,omitempty"`
	CurrentCriteriaOptionTypes []enums.CriteriaOptionType `protobuf:"varint,9,rep,packed,name=current_criteria_option_types,json=currentCriteriaOptionTypes,proto3,enum=tiering.enums.CriteriaOptionType" json:"current_criteria_option_types,omitempty"`
}

func (x *GetTieringPitchV2Response) Reset() {
	*x = GetTieringPitchV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTieringPitchV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTieringPitchV2Response) ProtoMessage() {}

func (x *GetTieringPitchV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTieringPitchV2Response.ProtoReflect.Descriptor instead.
func (*GetTieringPitchV2Response) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetTieringPitchV2Response) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTieringPitchV2Response) GetCurrentTier() external.Tier {
	if x != nil {
		return x.CurrentTier
	}
	return external.Tier(0)
}

func (x *GetTieringPitchV2Response) GetMovementDetailsList() []*external.MovementExternalDetails {
	if x != nil {
		return x.MovementDetailsList
	}
	return nil
}

func (x *GetTieringPitchV2Response) GetLastUpgradeDetails() *external.LatestMovementDetails {
	if x != nil {
		return x.LastUpgradeDetails
	}
	return nil
}

func (x *GetTieringPitchV2Response) GetLastDowngradeDetails() *external.LatestMovementDetails {
	if x != nil {
		return x.LastDowngradeDetails
	}
	return nil
}

func (x *GetTieringPitchV2Response) GetActorBaseTier() external.Tier {
	if x != nil {
		return x.ActorBaseTier
	}
	return external.Tier(0)
}

func (x *GetTieringPitchV2Response) GetEntryCriteriaOptionType() enums.CriteriaOptionType {
	if x != nil {
		return x.EntryCriteriaOptionType
	}
	return enums.CriteriaOptionType(0)
}

func (x *GetTieringPitchV2Response) GetCurrentCriteriaOptionType() enums.CriteriaOptionType {
	if x != nil {
		return x.CurrentCriteriaOptionType
	}
	return enums.CriteriaOptionType(0)
}

func (x *GetTieringPitchV2Response) GetCurrentCriteriaOptionTypes() []enums.CriteriaOptionType {
	if x != nil {
		return x.CurrentCriteriaOptionTypes
	}
	return nil
}

type GetTieringPitchV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor Id (mandatory)
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetTieringPitchV2Request) Reset() {
	*x = GetTieringPitchV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTieringPitchV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTieringPitchV2Request) ProtoMessage() {}

func (x *GetTieringPitchV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTieringPitchV2Request.ProtoReflect.Descriptor instead.
func (*GetTieringPitchV2Request) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetTieringPitchV2Request) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type RecordComponentShownToActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor Id (mandatory)
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Display Component enum (mandatory)
	Component enums.DisplayComponent `protobuf:"varint,2,opt,name=component,proto3,enum=tiering.enums.DisplayComponent" json:"component,omitempty"`
}

func (x *RecordComponentShownToActorRequest) Reset() {
	*x = RecordComponentShownToActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordComponentShownToActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordComponentShownToActorRequest) ProtoMessage() {}

func (x *RecordComponentShownToActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordComponentShownToActorRequest.ProtoReflect.Descriptor instead.
func (*RecordComponentShownToActorRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{10}
}

func (x *RecordComponentShownToActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RecordComponentShownToActorRequest) GetComponent() enums.DisplayComponent {
	if x != nil {
		return x.Component
	}
	return enums.DisplayComponent(0)
}

type RecordComponentShownToActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of RPC response
	// Success : record request was successful
	// Internal : record request was unsuccessful
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RecordComponentShownToActorResponse) Reset() {
	*x = RecordComponentShownToActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordComponentShownToActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordComponentShownToActorResponse) ProtoMessage() {}

func (x *RecordComponentShownToActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordComponentShownToActorResponse.ProtoReflect.Descriptor instead.
func (*RecordComponentShownToActorResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{11}
}

func (x *RecordComponentShownToActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetTieringPitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor id for whom add funds details are required
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Flow name for which pitch details are being requested - ex Add funds flow
	TieringPitchFlow enums.TieringPitchFlowFilter `protobuf:"varint,2,opt,name=tiering_pitch_flow,json=tieringPitchFlow,proto3,enum=tiering.enums.TieringPitchFlowFilter" json:"tiering_pitch_flow,omitempty"`
}

func (x *GetTieringPitchRequest) Reset() {
	*x = GetTieringPitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTieringPitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTieringPitchRequest) ProtoMessage() {}

func (x *GetTieringPitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTieringPitchRequest.ProtoReflect.Descriptor instead.
func (*GetTieringPitchRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetTieringPitchRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetTieringPitchRequest) GetTieringPitchFlow() enums.TieringPitchFlowFilter {
	if x != nil {
		return x.TieringPitchFlow
	}
	return enums.TieringPitchFlowFilter(0)
}

type GetTieringPitchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of RPC response
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Whether the pitch is enabled for the actor or not
	IsPitchEnabled bool `protobuf:"varint,2,opt,name=is_pitch_enabled,json=isPitchEnabled,proto3" json:"is_pitch_enabled,omitempty"`
	// Pitch Details
	//
	// Types that are assignable to TieringPitchDetails:
	//
	//	*GetTieringPitchResponse_AddFundsDetails
	//	*GetTieringPitchResponse_ProfileSectionDetails
	TieringPitchDetails isGetTieringPitchResponse_TieringPitchDetails `protobuf_oneof:"tiering_pitch_details"`
}

func (x *GetTieringPitchResponse) Reset() {
	*x = GetTieringPitchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTieringPitchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTieringPitchResponse) ProtoMessage() {}

func (x *GetTieringPitchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTieringPitchResponse.ProtoReflect.Descriptor instead.
func (*GetTieringPitchResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetTieringPitchResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTieringPitchResponse) GetIsPitchEnabled() bool {
	if x != nil {
		return x.IsPitchEnabled
	}
	return false
}

func (m *GetTieringPitchResponse) GetTieringPitchDetails() isGetTieringPitchResponse_TieringPitchDetails {
	if m != nil {
		return m.TieringPitchDetails
	}
	return nil
}

func (x *GetTieringPitchResponse) GetAddFundsDetails() *TieringPitchAddFundsDetails {
	if x, ok := x.GetTieringPitchDetails().(*GetTieringPitchResponse_AddFundsDetails); ok {
		return x.AddFundsDetails
	}
	return nil
}

func (x *GetTieringPitchResponse) GetProfileSectionDetails() *TieringPitchProfileSectionDetails {
	if x, ok := x.GetTieringPitchDetails().(*GetTieringPitchResponse_ProfileSectionDetails); ok {
		return x.ProfileSectionDetails
	}
	return nil
}

type isGetTieringPitchResponse_TieringPitchDetails interface {
	isGetTieringPitchResponse_TieringPitchDetails()
}

type GetTieringPitchResponse_AddFundsDetails struct {
	// Pitch details related to add funds flow
	AddFundsDetails *TieringPitchAddFundsDetails `protobuf:"bytes,3,opt,name=add_funds_details,json=addFundsDetails,proto3,oneof"`
}

type GetTieringPitchResponse_ProfileSectionDetails struct {
	// Pitch details related to profile header section flow
	ProfileSectionDetails *TieringPitchProfileSectionDetails `protobuf:"bytes,4,opt,name=profile_section_details,json=profileSectionDetails,proto3,oneof"`
}

func (*GetTieringPitchResponse_AddFundsDetails) isGetTieringPitchResponse_TieringPitchDetails() {}

func (*GetTieringPitchResponse_ProfileSectionDetails) isGetTieringPitchResponse_TieringPitchDetails() {
}

type TieringPitchAddFundsDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tier which the user is currently in
	CurrentTier external.Tier `protobuf:"varint,1,opt,name=current_tier,json=currentTier,proto3,enum=tiering.external.Tier" json:"current_tier,omitempty"`
	// Min amount required to be in the current tier
	CurrentTierMinAmount *money.Money `protobuf:"bytes,2,opt,name=current_tier_min_amount,json=currentTierMinAmount,proto3" json:"current_tier_min_amount,omitempty"`
	// Immediate next tier to which user can upgrade by adding funds
	NextTier external.Tier `protobuf:"varint,3,opt,name=next_tier,json=nextTier,proto3,enum=tiering.external.Tier" json:"next_tier,omitempty"`
	// Min amount required to be in the next higher tier
	NextTierMinAmount *money.Money `protobuf:"bytes,4,opt,name=next_tier_min_amount,json=nextTierMinAmount,proto3" json:"next_tier_min_amount,omitempty"`
	// Current balance of the user
	CurrentBalanceAmount *money.Money `protobuf:"bytes,5,opt,name=current_balance_amount,json=currentBalanceAmount,proto3" json:"current_balance_amount,omitempty"`
	// Min amount for add funds page below which secondary CTA is shown
	// Min amount can be less than suggested amount
	MinAmount *money.Money `protobuf:"bytes,6,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	// Amount suggested in the add funds page
	SuggestedAmount *money.Money `protobuf:"bytes,7,opt,name=suggested_amount,json=suggestedAmount,proto3" json:"suggested_amount,omitempty"`
	// Flag to determine whether pitch is to retain the user in current tier
	IsRetentionPitch bool `protobuf:"varint,8,opt,name=is_retention_pitch,json=isRetentionPitch,proto3" json:"is_retention_pitch,omitempty"`
}

func (x *TieringPitchAddFundsDetails) Reset() {
	*x = TieringPitchAddFundsDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TieringPitchAddFundsDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TieringPitchAddFundsDetails) ProtoMessage() {}

func (x *TieringPitchAddFundsDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TieringPitchAddFundsDetails.ProtoReflect.Descriptor instead.
func (*TieringPitchAddFundsDetails) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{14}
}

func (x *TieringPitchAddFundsDetails) GetCurrentTier() external.Tier {
	if x != nil {
		return x.CurrentTier
	}
	return external.Tier(0)
}

func (x *TieringPitchAddFundsDetails) GetCurrentTierMinAmount() *money.Money {
	if x != nil {
		return x.CurrentTierMinAmount
	}
	return nil
}

func (x *TieringPitchAddFundsDetails) GetNextTier() external.Tier {
	if x != nil {
		return x.NextTier
	}
	return external.Tier(0)
}

func (x *TieringPitchAddFundsDetails) GetNextTierMinAmount() *money.Money {
	if x != nil {
		return x.NextTierMinAmount
	}
	return nil
}

func (x *TieringPitchAddFundsDetails) GetCurrentBalanceAmount() *money.Money {
	if x != nil {
		return x.CurrentBalanceAmount
	}
	return nil
}

func (x *TieringPitchAddFundsDetails) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *TieringPitchAddFundsDetails) GetSuggestedAmount() *money.Money {
	if x != nil {
		return x.SuggestedAmount
	}
	return nil
}

func (x *TieringPitchAddFundsDetails) GetIsRetentionPitch() bool {
	if x != nil {
		return x.IsRetentionPitch
	}
	return false
}

type TieringPitchProfileSectionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tier which the user is currently in
	CurrentTier external.Tier `protobuf:"varint,1,opt,name=current_tier,json=currentTier,proto3,enum=tiering.external.Tier" json:"current_tier,omitempty"`
	// Min amount required to be in the current tier
	CurrentTierMinAmount *money.Money `protobuf:"bytes,2,opt,name=current_tier_min_amount,json=currentTierMinAmount,proto3" json:"current_tier_min_amount,omitempty"`
	// Immediate next tier to which user can upgrade by adding funds
	NextTier external.Tier `protobuf:"varint,3,opt,name=next_tier,json=nextTier,proto3,enum=tiering.external.Tier" json:"next_tier,omitempty"`
	// Min amount required to be in the next higher tier
	NextTierMinAmount *money.Money `protobuf:"bytes,4,opt,name=next_tier_min_amount,json=nextTierMinAmount,proto3" json:"next_tier_min_amount,omitempty"`
	// Current balance of the user
	CurrentBalanceAmount *money.Money `protobuf:"bytes,5,opt,name=current_balance_amount,json=currentBalanceAmount,proto3" json:"current_balance_amount,omitempty"`
	// Whether user in grace period or not
	IsUserInGracePeriod bool `protobuf:"varint,6,opt,name=is_user_in_grace_period,json=isUserInGracePeriod,proto3" json:"is_user_in_grace_period,omitempty"`
	// Min amount for add funds page below which secondary CTA is shown
	MinAmount *money.Money `protobuf:"bytes,7,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	// Time at which grace period expires for the user if the user is in grace period
	GracePeriodExpiryTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=grace_period_expiry_time,json=gracePeriodExpiryTime,proto3" json:"grace_period_expiry_time,omitempty"`
}

func (x *TieringPitchProfileSectionDetails) Reset() {
	*x = TieringPitchProfileSectionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TieringPitchProfileSectionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TieringPitchProfileSectionDetails) ProtoMessage() {}

func (x *TieringPitchProfileSectionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TieringPitchProfileSectionDetails.ProtoReflect.Descriptor instead.
func (*TieringPitchProfileSectionDetails) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{15}
}

func (x *TieringPitchProfileSectionDetails) GetCurrentTier() external.Tier {
	if x != nil {
		return x.CurrentTier
	}
	return external.Tier(0)
}

func (x *TieringPitchProfileSectionDetails) GetCurrentTierMinAmount() *money.Money {
	if x != nil {
		return x.CurrentTierMinAmount
	}
	return nil
}

func (x *TieringPitchProfileSectionDetails) GetNextTier() external.Tier {
	if x != nil {
		return x.NextTier
	}
	return external.Tier(0)
}

func (x *TieringPitchProfileSectionDetails) GetNextTierMinAmount() *money.Money {
	if x != nil {
		return x.NextTierMinAmount
	}
	return nil
}

func (x *TieringPitchProfileSectionDetails) GetCurrentBalanceAmount() *money.Money {
	if x != nil {
		return x.CurrentBalanceAmount
	}
	return nil
}

func (x *TieringPitchProfileSectionDetails) GetIsUserInGracePeriod() bool {
	if x != nil {
		return x.IsUserInGracePeriod
	}
	return false
}

func (x *TieringPitchProfileSectionDetails) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *TieringPitchProfileSectionDetails) GetGracePeriodExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.GracePeriodExpiryTime
	}
	return nil
}

type GetTierAtTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom tier is needed
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Timestamp at which tier is required. In case service requires latest tier it should pass current time
	// MANDATORY field
	TierTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=tier_timestamp,json=tierTimestamp,proto3" json:"tier_timestamp,omitempty"`
}

func (x *GetTierAtTimeRequest) Reset() {
	*x = GetTierAtTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierAtTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierAtTimeRequest) ProtoMessage() {}

func (x *GetTierAtTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierAtTimeRequest.ProtoReflect.Descriptor instead.
func (*GetTierAtTimeRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetTierAtTimeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetTierAtTimeRequest) GetTierTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.TierTimestamp
	}
	return nil
}

type GetTierAtTimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Tier info for the actor such as tier enum, name, timestamp
	TierInfo *external.TierInfo `protobuf:"bytes,2,opt,name=tier_info,json=tierInfo,proto3" json:"tier_info,omitempty"`
}

func (x *GetTierAtTimeResponse) Reset() {
	*x = GetTierAtTimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierAtTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierAtTimeResponse) ProtoMessage() {}

func (x *GetTierAtTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierAtTimeResponse.ProtoReflect.Descriptor instead.
func (*GetTierAtTimeResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetTierAtTimeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTierAtTimeResponse) GetTierInfo() *external.TierInfo {
	if x != nil {
		return x.TierInfo
	}
	return nil
}

type IsTieringEnabledForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to check
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *IsTieringEnabledForActorRequest) Reset() {
	*x = IsTieringEnabledForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsTieringEnabledForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsTieringEnabledForActorRequest) ProtoMessage() {}

func (x *IsTieringEnabledForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsTieringEnabledForActorRequest.ProtoReflect.Descriptor instead.
func (*IsTieringEnabledForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{18}
}

func (x *IsTieringEnabledForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type IsTieringEnabledForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsEnabled bool        `protobuf:"varint,2,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
}

func (x *IsTieringEnabledForActorResponse) Reset() {
	*x = IsTieringEnabledForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsTieringEnabledForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsTieringEnabledForActorResponse) ProtoMessage() {}

func (x *IsTieringEnabledForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsTieringEnabledForActorResponse.ProtoReflect.Descriptor instead.
func (*IsTieringEnabledForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{19}
}

func (x *IsTieringEnabledForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsTieringEnabledForActorResponse) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

type UpgradeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to upgrade
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Provide provenance based on the origin of the request
	// eg: OPT_IN for add_funds flow
	// MANDATORY field
	Provenance enums.Provenance `protobuf:"varint,2,opt,name=provenance,proto3,enum=tiering.enums.Provenance" json:"provenance,omitempty"`
}

func (x *UpgradeRequest) Reset() {
	*x = UpgradeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeRequest) ProtoMessage() {}

func (x *UpgradeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeRequest.ProtoReflect.Descriptor instead.
func (*UpgradeRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{20}
}

func (x *UpgradeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpgradeRequest) GetProvenance() enums.Provenance {
	if x != nil {
		return x.Provenance
	}
	return enums.Provenance(0)
}

type UpgradeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	FromTier external.Tier `protobuf:"varint,2,opt,name=from_tier,json=fromTier,proto3,enum=tiering.external.Tier" json:"from_tier,omitempty"`
	ToTier   external.Tier `protobuf:"varint,3,opt,name=to_tier,json=toTier,proto3,enum=tiering.external.Tier" json:"to_tier,omitempty"`
}

func (x *UpgradeResponse) Reset() {
	*x = UpgradeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeResponse) ProtoMessage() {}

func (x *UpgradeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeResponse.ProtoReflect.Descriptor instead.
func (*UpgradeResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{21}
}

func (x *UpgradeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpgradeResponse) GetFromTier() external.Tier {
	if x != nil {
		return x.FromTier
	}
	return external.Tier(0)
}

func (x *UpgradeResponse) GetToTier() external.Tier {
	if x != nil {
		return x.ToTier
	}
	return external.Tier(0)
}

type ShowComponentToActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor Id (mandatory)
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Component name (deprecated)
	//
	// Deprecated: Marked as deprecated in api/tiering/service.proto.
	ComponentName string `protobuf:"bytes,2,opt,name=component_name,json=componentName,proto3" json:"component_name,omitempty"`
	// Display Component enum (mandatory)
	DisplayComponent enums.DisplayComponent `protobuf:"varint,3,opt,name=display_component,json=displayComponent,proto3,enum=tiering.enums.DisplayComponent" json:"display_component,omitempty"`
}

func (x *ShowComponentToActorRequest) Reset() {
	*x = ShowComponentToActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShowComponentToActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShowComponentToActorRequest) ProtoMessage() {}

func (x *ShowComponentToActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShowComponentToActorRequest.ProtoReflect.Descriptor instead.
func (*ShowComponentToActorRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{22}
}

func (x *ShowComponentToActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/tiering/service.proto.
func (x *ShowComponentToActorRequest) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (x *ShowComponentToActorRequest) GetDisplayComponent() enums.DisplayComponent {
	if x != nil {
		return x.DisplayComponent
	}
	return enums.DisplayComponent(0)
}

type ShowComponentToActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Flag to decide whether to show screen or not
	ShowComponent bool `protobuf:"varint,2,opt,name=show_component,json=showComponent,proto3" json:"show_component,omitempty"`
}

func (x *ShowComponentToActorResponse) Reset() {
	*x = ShowComponentToActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShowComponentToActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShowComponentToActorResponse) ProtoMessage() {}

func (x *ShowComponentToActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShowComponentToActorResponse.ProtoReflect.Descriptor instead.
func (*ShowComponentToActorResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{23}
}

func (x *ShowComponentToActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ShowComponentToActorResponse) GetShowComponent() bool {
	if x != nil {
		return x.ShowComponent
	}
	return false
}

type GetCriteriaForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to fetch the criteria for
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetCriteriaForActorRequest) Reset() {
	*x = GetCriteriaForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCriteriaForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCriteriaForActorRequest) ProtoMessage() {}

func (x *GetCriteriaForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCriteriaForActorRequest.ProtoReflect.Descriptor instead.
func (*GetCriteriaForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetCriteriaForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetCriteriaForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// External tier representation
	// gives response as a list of tier and QC
	ExternalCriteria []*external.TierExternalCriteria `protobuf:"bytes,2,rep,name=external_criteria,json=externalCriteria,proto3" json:"external_criteria,omitempty"`
}

func (x *GetCriteriaForActorResponse) Reset() {
	*x = GetCriteriaForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCriteriaForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCriteriaForActorResponse) ProtoMessage() {}

func (x *GetCriteriaForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCriteriaForActorResponse.ProtoReflect.Descriptor instead.
func (*GetCriteriaForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetCriteriaForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCriteriaForActorResponse) GetExternalCriteria() []*external.TierExternalCriteria {
	if x != nil {
		return x.ExternalCriteria
	}
	return nil
}

type IsActorEligibleForMovementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to check eligibility for
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// movement type to check eligibility for
	MovementType enums.TierMovementType `protobuf:"varint,2,opt,name=movement_type,json=movementType,proto3,enum=tiering.enums.TierMovementType" json:"movement_type,omitempty"`
	// from_tier and to_tier to define the movement direction
	FromTier external.Tier `protobuf:"varint,3,opt,name=from_tier,json=fromTier,proto3,enum=tiering.external.Tier" json:"from_tier,omitempty"`
	ToTier   external.Tier `protobuf:"varint,4,opt,name=to_tier,json=toTier,proto3,enum=tiering.external.Tier" json:"to_tier,omitempty"`
}

func (x *IsActorEligibleForMovementRequest) Reset() {
	*x = IsActorEligibleForMovementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsActorEligibleForMovementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsActorEligibleForMovementRequest) ProtoMessage() {}

func (x *IsActorEligibleForMovementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsActorEligibleForMovementRequest.ProtoReflect.Descriptor instead.
func (*IsActorEligibleForMovementRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{26}
}

func (x *IsActorEligibleForMovementRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *IsActorEligibleForMovementRequest) GetMovementType() enums.TierMovementType {
	if x != nil {
		return x.MovementType
	}
	return enums.TierMovementType(0)
}

func (x *IsActorEligibleForMovementRequest) GetFromTier() external.Tier {
	if x != nil {
		return x.FromTier
	}
	return external.Tier(0)
}

func (x *IsActorEligibleForMovementRequest) GetToTier() external.Tier {
	if x != nil {
		return x.ToTier
	}
	return external.Tier(0)
}

type IsActorEligibleForMovementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsEligible bool        `protobuf:"varint,2,opt,name=is_eligible,json=isEligible,proto3" json:"is_eligible,omitempty"`
}

func (x *IsActorEligibleForMovementResponse) Reset() {
	*x = IsActorEligibleForMovementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsActorEligibleForMovementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsActorEligibleForMovementResponse) ProtoMessage() {}

func (x *IsActorEligibleForMovementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsActorEligibleForMovementResponse.ProtoReflect.Descriptor instead.
func (*IsActorEligibleForMovementResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{27}
}

func (x *IsActorEligibleForMovementResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsActorEligibleForMovementResponse) GetIsEligible() bool {
	if x != nil {
		return x.IsEligible
	}
	return false
}

type IsUserInGracePeriodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to check for
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *IsUserInGracePeriodRequest) Reset() {
	*x = IsUserInGracePeriodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsUserInGracePeriodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsUserInGracePeriodRequest) ProtoMessage() {}

func (x *IsUserInGracePeriodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsUserInGracePeriodRequest.ProtoReflect.Descriptor instead.
func (*IsUserInGracePeriodRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{28}
}

func (x *IsUserInGracePeriodRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type IsUserInGracePeriodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsInGracePeriod bool        `protobuf:"varint,2,opt,name=is_in_grace_period,json=isInGracePeriod,proto3" json:"is_in_grace_period,omitempty"`
}

func (x *IsUserInGracePeriodResponse) Reset() {
	*x = IsUserInGracePeriodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsUserInGracePeriodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsUserInGracePeriodResponse) ProtoMessage() {}

func (x *IsUserInGracePeriodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsUserInGracePeriodResponse.ProtoReflect.Descriptor instead.
func (*IsUserInGracePeriodResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{29}
}

func (x *IsUserInGracePeriodResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsUserInGracePeriodResponse) GetIsInGracePeriod() bool {
	if x != nil {
		return x.IsInGracePeriod
	}
	return false
}

type GetDetailsForCxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to fetch details for
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetDetailsForCxRequest) Reset() {
	*x = GetDetailsForCxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailsForCxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailsForCxRequest) ProtoMessage() {}

func (x *GetDetailsForCxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailsForCxRequest.ProtoReflect.Descriptor instead.
func (*GetDetailsForCxRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetDetailsForCxRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetDetailsForCxResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Current tier of the user
	CurrentTier external.Tier `protobuf:"varint,2,opt,name=current_tier,json=currentTier,proto3,enum=tiering.external.Tier" json:"current_tier,omitempty"`
	// Flag to represent if the user is in grace
	IsUserInGrace bool `protobuf:"varint,3,opt,name=is_user_in_grace,json=isUserInGrace,proto3" json:"is_user_in_grace,omitempty"`
	// Grace timestamp if the user is in grace
	GracePeriodTill *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=grace_period_till,json=gracePeriodTill,proto3" json:"grace_period_till,omitempty"`
	// Flag to represent if the user is in cool off
	IsUserInCoolOff bool `protobuf:"varint,5,opt,name=is_user_in_cool_off,json=isUserInCoolOff,proto3" json:"is_user_in_cool_off,omitempty"`
	// Cool off timestamp if the user is in grace
	CoolOffPeriodTill *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=cool_off_period_till,json=coolOffPeriodTill,proto3" json:"cool_off_period_till,omitempty"`
	// Last N movement histories of the user
	MovementHistories []*external.TierMovementHistory `protobuf:"bytes,7,rep,name=movement_histories,json=movementHistories,proto3" json:"movement_histories,omitempty"`
	// Flag to represent if a user is a Rewards Abuser.
	IsARewardsAbuserUser bool `protobuf:"varint,8,opt,name=is_a_rewards_abuser_user,json=isARewardsAbuserUser,proto3" json:"is_a_rewards_abuser_user,omitempty"`
	// latest criterias satisfied by user to be on current tier
	TierMovementCriterias *external.TierMovementCriterias `protobuf:"bytes,9,opt,name=tier_movement_criterias,json=tierMovementCriterias,proto3" json:"tier_movement_criterias,omitempty"`
	// Display string for current AMB
	CurrentAmb string `protobuf:"bytes,10,opt,name=current_amb,json=currentAmb,proto3" json:"current_amb,omitempty"`
	// Display string for required AMB
	RequiredAmb string `protobuf:"bytes,11,opt,name=required_amb,json=requiredAmb,proto3" json:"required_amb,omitempty"`
	// Display string for shortfall
	Shortfall string `protobuf:"bytes,12,opt,name=shortfall,proto3" json:"shortfall,omitempty"`
	// AMB history details
	AmbHistory []*AmbDetails `protobuf:"bytes,13,rep,name=amb_history,json=ambHistory,proto3" json:"amb_history,omitempty"`
}

func (x *GetDetailsForCxResponse) Reset() {
	*x = GetDetailsForCxResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailsForCxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailsForCxResponse) ProtoMessage() {}

func (x *GetDetailsForCxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailsForCxResponse.ProtoReflect.Descriptor instead.
func (*GetDetailsForCxResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetDetailsForCxResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDetailsForCxResponse) GetCurrentTier() external.Tier {
	if x != nil {
		return x.CurrentTier
	}
	return external.Tier(0)
}

func (x *GetDetailsForCxResponse) GetIsUserInGrace() bool {
	if x != nil {
		return x.IsUserInGrace
	}
	return false
}

func (x *GetDetailsForCxResponse) GetGracePeriodTill() *timestamppb.Timestamp {
	if x != nil {
		return x.GracePeriodTill
	}
	return nil
}

func (x *GetDetailsForCxResponse) GetIsUserInCoolOff() bool {
	if x != nil {
		return x.IsUserInCoolOff
	}
	return false
}

func (x *GetDetailsForCxResponse) GetCoolOffPeriodTill() *timestamppb.Timestamp {
	if x != nil {
		return x.CoolOffPeriodTill
	}
	return nil
}

func (x *GetDetailsForCxResponse) GetMovementHistories() []*external.TierMovementHistory {
	if x != nil {
		return x.MovementHistories
	}
	return nil
}

func (x *GetDetailsForCxResponse) GetIsARewardsAbuserUser() bool {
	if x != nil {
		return x.IsARewardsAbuserUser
	}
	return false
}

func (x *GetDetailsForCxResponse) GetTierMovementCriterias() *external.TierMovementCriterias {
	if x != nil {
		return x.TierMovementCriterias
	}
	return nil
}

func (x *GetDetailsForCxResponse) GetCurrentAmb() string {
	if x != nil {
		return x.CurrentAmb
	}
	return ""
}

func (x *GetDetailsForCxResponse) GetRequiredAmb() string {
	if x != nil {
		return x.RequiredAmb
	}
	return ""
}

func (x *GetDetailsForCxResponse) GetShortfall() string {
	if x != nil {
		return x.Shortfall
	}
	return ""
}

func (x *GetDetailsForCxResponse) GetAmbHistory() []*AmbDetails {
	if x != nil {
		return x.AmbHistory
	}
	return nil
}

type AmbDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dates         string `protobuf:"bytes,1,opt,name=dates,proto3" json:"dates,omitempty"`
	Plan          string `protobuf:"bytes,2,opt,name=plan,proto3" json:"plan,omitempty"`
	AmbMaintained string `protobuf:"bytes,3,opt,name=amb_maintained,json=ambMaintained,proto3" json:"amb_maintained,omitempty"`
}

func (x *AmbDetails) Reset() {
	*x = AmbDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmbDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmbDetails) ProtoMessage() {}

func (x *AmbDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmbDetails.ProtoReflect.Descriptor instead.
func (*AmbDetails) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{32}
}

func (x *AmbDetails) GetDates() string {
	if x != nil {
		return x.Dates
	}
	return ""
}

func (x *AmbDetails) GetPlan() string {
	if x != nil {
		return x.Plan
	}
	return ""
}

func (x *AmbDetails) GetAmbMaintained() string {
	if x != nil {
		return x.AmbMaintained
	}
	return ""
}

type OverrideGracePeriodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to override grace period for
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Timestamp to override grace period to
	OverrideTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=override_timestamp,json=overrideTimestamp,proto3" json:"override_timestamp,omitempty"`
}

func (x *OverrideGracePeriodRequest) Reset() {
	*x = OverrideGracePeriodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverrideGracePeriodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverrideGracePeriodRequest) ProtoMessage() {}

func (x *OverrideGracePeriodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverrideGracePeriodRequest.ProtoReflect.Descriptor instead.
func (*OverrideGracePeriodRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{33}
}

func (x *OverrideGracePeriodRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *OverrideGracePeriodRequest) GetOverrideTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OverrideTimestamp
	}
	return nil
}

type OverrideGracePeriodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *OverrideGracePeriodResponse) Reset() {
	*x = OverrideGracePeriodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverrideGracePeriodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverrideGracePeriodResponse) ProtoMessage() {}

func (x *OverrideGracePeriodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverrideGracePeriodResponse.ProtoReflect.Descriptor instead.
func (*OverrideGracePeriodResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{34}
}

func (x *OverrideGracePeriodResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type OverrideCoolOffPeriodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to override cool off for
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Timestamp to override cool off period to
	OverrideTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=override_timestamp,json=overrideTimestamp,proto3" json:"override_timestamp,omitempty"`
}

func (x *OverrideCoolOffPeriodRequest) Reset() {
	*x = OverrideCoolOffPeriodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverrideCoolOffPeriodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverrideCoolOffPeriodRequest) ProtoMessage() {}

func (x *OverrideCoolOffPeriodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverrideCoolOffPeriodRequest.ProtoReflect.Descriptor instead.
func (*OverrideCoolOffPeriodRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{35}
}

func (x *OverrideCoolOffPeriodRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *OverrideCoolOffPeriodRequest) GetOverrideTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OverrideTimestamp
	}
	return nil
}

type OverrideCoolOffPeriodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *OverrideCoolOffPeriodResponse) Reset() {
	*x = OverrideCoolOffPeriodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverrideCoolOffPeriodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverrideCoolOffPeriodResponse) ProtoMessage() {}

func (x *OverrideCoolOffPeriodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverrideCoolOffPeriodResponse.ProtoReflect.Descriptor instead.
func (*OverrideCoolOffPeriodResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{36}
}

func (x *OverrideCoolOffPeriodResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetConfigParamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetConfigParamsRequest) Reset() {
	*x = GetConfigParamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigParamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigParamsRequest) ProtoMessage() {}

func (x *GetConfigParamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigParamsRequest.ProtoReflect.Descriptor instead.
func (*GetConfigParamsRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetConfigParamsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetConfigParamsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// tiering backend config values for DowngradeWindowDuration
	DowngradeWindowDuration *durationpb.Duration `protobuf:"bytes,2,opt,name=downgrade_window_duration,json=downgradeWindowDuration,proto3" json:"downgrade_window_duration,omitempty"`
	// tiering backend config values for GraceWindowDuration
	GraceWindowDuration *durationpb.Duration `protobuf:"bytes,3,opt,name=grace_window_duration,json=graceWindowDuration,proto3" json:"grace_window_duration,omitempty"`
	// tiering backend config values for GraceInitialWindowDuration
	GraceInitialWindowDuration *durationpb.Duration `protobuf:"bytes,4,opt,name=grace_initial_window_duration,json=graceInitialWindowDuration,proto3" json:"grace_initial_window_duration,omitempty"`
	// tiering backend config values for IsRegularTierEnabledForActor
	// this is evaluated only if actor id is provided in the request
	IsRegularTierEnabledForActor bool `protobuf:"varint,5,opt,name=is_regular_tier_enabled_for_actor,json=isRegularTierEnabledForActor,proto3" json:"is_regular_tier_enabled_for_actor,omitempty"`
	// config params for Regular Tier
	RegularTierConfigParams *RegularTierConfigParams `protobuf:"bytes,6,opt,name=regular_tier_config_params,json=regularTierConfigParams,proto3" json:"regular_tier_config_params,omitempty"`
	// feature flag value for actor to enable multiple ways to enter tiering
	IsMultipleWaysToEnterTieringEnabledForActor bool `protobuf:"varint,7,opt,name=is_multiple_ways_to_enter_tiering_enabled_for_actor,json=isMultipleWaysToEnterTieringEnabledForActor,proto3" json:"is_multiple_ways_to_enter_tiering_enabled_for_actor,omitempty"`
	// segment of users for whom certain criteria based evaluations are excluded
	CriteriaSegmentExclusionConfigs *CriteriaSegmentExclusionConfigs `protobuf:"bytes,8,opt,name=criteria_segment_exclusion_configs,json=criteriaSegmentExclusionConfigs,proto3" json:"criteria_segment_exclusion_configs,omitempty"`
}

func (x *GetConfigParamsResponse) Reset() {
	*x = GetConfigParamsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigParamsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigParamsResponse) ProtoMessage() {}

func (x *GetConfigParamsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigParamsResponse.ProtoReflect.Descriptor instead.
func (*GetConfigParamsResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetConfigParamsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetConfigParamsResponse) GetDowngradeWindowDuration() *durationpb.Duration {
	if x != nil {
		return x.DowngradeWindowDuration
	}
	return nil
}

func (x *GetConfigParamsResponse) GetGraceWindowDuration() *durationpb.Duration {
	if x != nil {
		return x.GraceWindowDuration
	}
	return nil
}

func (x *GetConfigParamsResponse) GetGraceInitialWindowDuration() *durationpb.Duration {
	if x != nil {
		return x.GraceInitialWindowDuration
	}
	return nil
}

func (x *GetConfigParamsResponse) GetIsRegularTierEnabledForActor() bool {
	if x != nil {
		return x.IsRegularTierEnabledForActor
	}
	return false
}

func (x *GetConfigParamsResponse) GetRegularTierConfigParams() *RegularTierConfigParams {
	if x != nil {
		return x.RegularTierConfigParams
	}
	return nil
}

func (x *GetConfigParamsResponse) GetIsMultipleWaysToEnterTieringEnabledForActor() bool {
	if x != nil {
		return x.IsMultipleWaysToEnterTieringEnabledForActor
	}
	return false
}

func (x *GetConfigParamsResponse) GetCriteriaSegmentExclusionConfigs() *CriteriaSegmentExclusionConfigs {
	if x != nil {
		return x.CriteriaSegmentExclusionConfigs
	}
	return nil
}

type CriteriaSegmentExclusionConfigs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SalaryB2CExcludedSegments []string `protobuf:"bytes,1,rep,name=salary_b2c_excluded_segments,json=salaryB2cExcludedSegments,proto3" json:"salary_b2c_excluded_segments,omitempty"`
	AaSalaryExcludedSegments  []string `protobuf:"bytes,2,rep,name=aa_salary_excluded_segments,json=aaSalaryExcludedSegments,proto3" json:"aa_salary_excluded_segments,omitempty"`
}

func (x *CriteriaSegmentExclusionConfigs) Reset() {
	*x = CriteriaSegmentExclusionConfigs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CriteriaSegmentExclusionConfigs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CriteriaSegmentExclusionConfigs) ProtoMessage() {}

func (x *CriteriaSegmentExclusionConfigs) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CriteriaSegmentExclusionConfigs.ProtoReflect.Descriptor instead.
func (*CriteriaSegmentExclusionConfigs) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{39}
}

func (x *CriteriaSegmentExclusionConfigs) GetSalaryB2CExcludedSegments() []string {
	if x != nil {
		return x.SalaryB2CExcludedSegments
	}
	return nil
}

func (x *CriteriaSegmentExclusionConfigs) GetAaSalaryExcludedSegments() []string {
	if x != nil {
		return x.AaSalaryExcludedSegments
	}
	return nil
}

type RegularTierConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Min Avg Monthly balance required for Regular Tier
	MinBalanceForRegularTier *money.Money `protobuf:"bytes,1,opt,name=min_balance_for_regular_tier,json=minBalanceForRegularTier,proto3" json:"min_balance_for_regular_tier,omitempty"`
	// Monthly Charges for regular tier
	MinBalancePenaltyForRegularTier *money.Money `protobuf:"bytes,2,opt,name=min_balance_penalty_for_regular_tier,json=minBalancePenaltyForRegularTier,proto3" json:"min_balance_penalty_for_regular_tier,omitempty"`
}

func (x *RegularTierConfigParams) Reset() {
	*x = RegularTierConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegularTierConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegularTierConfigParams) ProtoMessage() {}

func (x *RegularTierConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegularTierConfigParams.ProtoReflect.Descriptor instead.
func (*RegularTierConfigParams) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{40}
}

func (x *RegularTierConfigParams) GetMinBalanceForRegularTier() *money.Money {
	if x != nil {
		return x.MinBalanceForRegularTier
	}
	return nil
}

func (x *RegularTierConfigParams) GetMinBalancePenaltyForRegularTier() *money.Money {
	if x != nil {
		return x.MinBalancePenaltyForRegularTier
	}
	return nil
}

type EvaluateTierForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to override cool off for
	// MANDATORY field
	ActorId string                               `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Options *EvaluateTierForActorRequest_Options `protobuf:"bytes,2,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *EvaluateTierForActorRequest) Reset() {
	*x = EvaluateTierForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluateTierForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateTierForActorRequest) ProtoMessage() {}

func (x *EvaluateTierForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateTierForActorRequest.ProtoReflect.Descriptor instead.
func (*EvaluateTierForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{41}
}

func (x *EvaluateTierForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *EvaluateTierForActorRequest) GetOptions() *EvaluateTierForActorRequest_Options {
	if x != nil {
		return x.Options
	}
	return nil
}

type EvaluateTierForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Tier evaluated basis on current active criteria for actor
	EvaluatedTier external.Tier `protobuf:"varint,2,opt,name=evaluated_tier,json=evaluatedTier,proto3,enum=tiering.external.Tier" json:"evaluated_tier,omitempty"`
	// Criteria reference id that the tier evaluated against
	CriteriaReferenceId string `protobuf:"bytes,3,opt,name=criteria_reference_id,json=criteriaReferenceId,proto3" json:"criteria_reference_id,omitempty"`
	// list of all criteria satisfied by the actor
	EvaluatedTierSatisfiedCriteriaOptionTypes []enums.CriteriaOptionType `protobuf:"varint,4,rep,packed,name=EvaluatedTierSatisfiedCriteriaOptionTypes,proto3,enum=tiering.enums.CriteriaOptionType" json:"EvaluatedTierSatisfiedCriteriaOptionTypes,omitempty"`
}

func (x *EvaluateTierForActorResponse) Reset() {
	*x = EvaluateTierForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluateTierForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateTierForActorResponse) ProtoMessage() {}

func (x *EvaluateTierForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateTierForActorResponse.ProtoReflect.Descriptor instead.
func (*EvaluateTierForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{42}
}

func (x *EvaluateTierForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *EvaluateTierForActorResponse) GetEvaluatedTier() external.Tier {
	if x != nil {
		return x.EvaluatedTier
	}
	return external.Tier(0)
}

func (x *EvaluateTierForActorResponse) GetCriteriaReferenceId() string {
	if x != nil {
		return x.CriteriaReferenceId
	}
	return ""
}

func (x *EvaluateTierForActorResponse) GetEvaluatedTierSatisfiedCriteriaOptionTypes() []enums.CriteriaOptionType {
	if x != nil {
		return x.EvaluatedTierSatisfiedCriteriaOptionTypes
	}
	return nil
}

type CheckIfActorIsEligibleForCashbackRewardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor for whom to check eligibility for cashback reward
	// MANDATORY field
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Tier to be evaluated for cashback reward eligibility
	Tier external.Tier `protobuf:"varint,2,opt,name=tier,proto3,enum=tiering.external.Tier" json:"tier,omitempty"`
	// Timestamp (focusing just on the month) for which the rewards eligibility will be checked.
	// i.e. Irrespective of the timestamp granularity here, only the Month-Year will be considered.
	RewardMonth *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=reward_month,json=rewardMonth,proto3" json:"reward_month,omitempty"`
}

func (x *CheckIfActorIsEligibleForCashbackRewardRequest) Reset() {
	*x = CheckIfActorIsEligibleForCashbackRewardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIfActorIsEligibleForCashbackRewardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIfActorIsEligibleForCashbackRewardRequest) ProtoMessage() {}

func (x *CheckIfActorIsEligibleForCashbackRewardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIfActorIsEligibleForCashbackRewardRequest.ProtoReflect.Descriptor instead.
func (*CheckIfActorIsEligibleForCashbackRewardRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{43}
}

func (x *CheckIfActorIsEligibleForCashbackRewardRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CheckIfActorIsEligibleForCashbackRewardRequest) GetTier() external.Tier {
	if x != nil {
		return x.Tier
	}
	return external.Tier(0)
}

func (x *CheckIfActorIsEligibleForCashbackRewardRequest) GetRewardMonth() *timestamppb.Timestamp {
	if x != nil {
		return x.RewardMonth
	}
	return nil
}

type CheckIfActorIsEligibleForCashbackRewardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsEligible bool        `protobuf:"varint,2,opt,name=is_eligible,json=isEligible,proto3" json:"is_eligible,omitempty"`
}

func (x *CheckIfActorIsEligibleForCashbackRewardResponse) Reset() {
	*x = CheckIfActorIsEligibleForCashbackRewardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIfActorIsEligibleForCashbackRewardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIfActorIsEligibleForCashbackRewardResponse) ProtoMessage() {}

func (x *CheckIfActorIsEligibleForCashbackRewardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIfActorIsEligibleForCashbackRewardResponse.ProtoReflect.Descriptor instead.
func (*CheckIfActorIsEligibleForCashbackRewardResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{44}
}

func (x *CheckIfActorIsEligibleForCashbackRewardResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckIfActorIsEligibleForCashbackRewardResponse) GetIsEligible() bool {
	if x != nil {
		return x.IsEligible
	}
	return false
}

type GetTierTimeRangesForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// list of tiers for which timeline is requested
	Tiers []external.Tier `protobuf:"varint,2,rep,packed,name=tiers,proto3,enum=tiering.external.Tier" json:"tiers,omitempty"`
	// only time_ranges that have to_time greater than filter_from are fetched and returned
	// this is to do a controlled fetch of the tier movement history (TMH) as TMH for an actor grows to a big value over time
	FilterFrom *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=filter_from,json=filterFrom,proto3" json:"filter_from,omitempty"`
}

func (x *GetTierTimeRangesForActorRequest) Reset() {
	*x = GetTierTimeRangesForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierTimeRangesForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierTimeRangesForActorRequest) ProtoMessage() {}

func (x *GetTierTimeRangesForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierTimeRangesForActorRequest.ProtoReflect.Descriptor instead.
func (*GetTierTimeRangesForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetTierTimeRangesForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetTierTimeRangesForActorRequest) GetTiers() []external.Tier {
	if x != nil {
		return x.Tiers
	}
	return nil
}

func (x *GetTierTimeRangesForActorRequest) GetFilterFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.FilterFrom
	}
	return nil
}

type GetTierTimeRangesForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// map of tiering.external.Tier.String() to TimeRange list
	// TimeRanges is the list of time ranges when user was in the tier
	TierTimeRangesMap map[string]*TimeRanges `protobuf:"bytes,2,rep,name=tier_time_ranges_map,json=tierTimeRangesMap,proto3" json:"tier_time_ranges_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetTierTimeRangesForActorResponse) Reset() {
	*x = GetTierTimeRangesForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTierTimeRangesForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTierTimeRangesForActorResponse) ProtoMessage() {}

func (x *GetTierTimeRangesForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTierTimeRangesForActorResponse.ProtoReflect.Descriptor instead.
func (*GetTierTimeRangesForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{46}
}

func (x *GetTierTimeRangesForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTierTimeRangesForActorResponse) GetTierTimeRangesMap() map[string]*TimeRanges {
	if x != nil {
		return x.TierTimeRangesMap
	}
	return nil
}

type TimeRanges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRanges []*TimeRange `protobuf:"bytes,1,rep,name=time_ranges,json=timeRanges,proto3" json:"time_ranges,omitempty"`
}

func (x *TimeRanges) Reset() {
	*x = TimeRanges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRanges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRanges) ProtoMessage() {}

func (x *TimeRanges) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRanges.ProtoReflect.Descriptor instead.
func (*TimeRanges) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{47}
}

func (x *TimeRanges) GetTimeRanges() []*TimeRange {
	if x != nil {
		return x.TimeRanges
	}
	return nil
}

type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	ToTime   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=to_time,json=toTime,proto3" json:"to_time,omitempty"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{48}
}

func (x *TimeRange) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *TimeRange) GetToTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTime
	}
	return nil
}

type GetActorScreenInteractionDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string                                              `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Screen      enums.TieringScreen                                 `protobuf:"varint,2,opt,name=screen,proto3,enum=tiering.enums.TieringScreen" json:"screen,omitempty"`
	RequestType GetActorScreenInteractionDetailsRequest_RequestType `protobuf:"varint,3,opt,name=request_type,json=requestType,proto3,enum=tiering.GetActorScreenInteractionDetailsRequest_RequestType" json:"request_type,omitempty"`
}

func (x *GetActorScreenInteractionDetailsRequest) Reset() {
	*x = GetActorScreenInteractionDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorScreenInteractionDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorScreenInteractionDetailsRequest) ProtoMessage() {}

func (x *GetActorScreenInteractionDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorScreenInteractionDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetActorScreenInteractionDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{49}
}

func (x *GetActorScreenInteractionDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetActorScreenInteractionDetailsRequest) GetScreen() enums.TieringScreen {
	if x != nil {
		return x.Screen
	}
	return enums.TieringScreen(0)
}

func (x *GetActorScreenInteractionDetailsRequest) GetRequestType() GetActorScreenInteractionDetailsRequest_RequestType {
	if x != nil {
		return x.RequestType
	}
	return GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_UNSPECIFIED
}

type GetActorScreenInteractionDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status                        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Response enums.ActorScreenInteractionStatus `protobuf:"varint,2,opt,name=response,proto3,enum=tiering.enums.ActorScreenInteractionStatus" json:"response,omitempty"`
}

func (x *GetActorScreenInteractionDetailsResponse) Reset() {
	*x = GetActorScreenInteractionDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorScreenInteractionDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorScreenInteractionDetailsResponse) ProtoMessage() {}

func (x *GetActorScreenInteractionDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorScreenInteractionDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetActorScreenInteractionDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{50}
}

func (x *GetActorScreenInteractionDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActorScreenInteractionDetailsResponse) GetResponse() enums.ActorScreenInteractionStatus {
	if x != nil {
		return x.Response
	}
	return enums.ActorScreenInteractionStatus(0)
}

type GetDropOffBottomSheetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId            string                                          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	TieringPitchMethod GetDropOffBottomSheetRequest_TieringPitchMethod `protobuf:"varint,2,opt,name=tiering_pitch_method,json=tieringPitchMethod,proto3,enum=tiering.GetDropOffBottomSheetRequest_TieringPitchMethod" json:"tiering_pitch_method,omitempty"`
	TierToPitch        external.Tier                                   `protobuf:"varint,3,opt,name=tier_to_pitch,json=tierToPitch,proto3,enum=tiering.external.Tier" json:"tier_to_pitch,omitempty"`
}

func (x *GetDropOffBottomSheetRequest) Reset() {
	*x = GetDropOffBottomSheetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDropOffBottomSheetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDropOffBottomSheetRequest) ProtoMessage() {}

func (x *GetDropOffBottomSheetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDropOffBottomSheetRequest.ProtoReflect.Descriptor instead.
func (*GetDropOffBottomSheetRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetDropOffBottomSheetRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetDropOffBottomSheetRequest) GetTieringPitchMethod() GetDropOffBottomSheetRequest_TieringPitchMethod {
	if x != nil {
		return x.TieringPitchMethod
	}
	return GetDropOffBottomSheetRequest_TIERING_PITCH_METHOD_UNSPECIFIED
}

func (x *GetDropOffBottomSheetRequest) GetTierToPitch() external.Tier {
	if x != nil {
		return x.TierToPitch
	}
	return external.Tier(0)
}

type GetDropOffBottomSheetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *GetDropOffBottomSheetResponse) Reset() {
	*x = GetDropOffBottomSheetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDropOffBottomSheetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDropOffBottomSheetResponse) ProtoMessage() {}

func (x *GetDropOffBottomSheetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDropOffBottomSheetResponse.ProtoReflect.Descriptor instead.
func (*GetDropOffBottomSheetResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{52}
}

func (x *GetDropOffBottomSheetResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDropOffBottomSheetResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type GetAMBInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor ID (mandatory)
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetAMBInfoRequest) Reset() {
	*x = GetAMBInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAMBInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAMBInfoRequest) ProtoMessage() {}

func (x *GetAMBInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAMBInfoRequest.ProtoReflect.Descriptor instead.
func (*GetAMBInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{53}
}

func (x *GetAMBInfoRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetAMBInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of RPC response
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Current (projected) AMB for the user
	CurrentAmb *money.Money `protobuf:"bytes,2,opt,name=current_amb,json=currentAmb,proto3" json:"current_amb,omitempty"`
	// Target AMB required for the current tier
	TargetAmb *money.Money `protobuf:"bytes,3,opt,name=target_amb,json=targetAmb,proto3" json:"target_amb,omitempty"`
	// Current tier of the user
	CurrentTier external.Tier `protobuf:"varint,4,opt,name=current_tier,json=currentTier,proto3,enum=tiering.external.Tier" json:"current_tier,omitempty"`
	// Amount to be added to reach target AMB
	ShortfallAmount *money.Money `protobuf:"bytes,5,opt,name=shortfall_amount,json=shortfallAmount,proto3" json:"shortfall_amount,omitempty"`
}

func (x *GetAMBInfoResponse) Reset() {
	*x = GetAMBInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAMBInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAMBInfoResponse) ProtoMessage() {}

func (x *GetAMBInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAMBInfoResponse.ProtoReflect.Descriptor instead.
func (*GetAMBInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{54}
}

func (x *GetAMBInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAMBInfoResponse) GetCurrentAmb() *money.Money {
	if x != nil {
		return x.CurrentAmb
	}
	return nil
}

func (x *GetAMBInfoResponse) GetTargetAmb() *money.Money {
	if x != nil {
		return x.TargetAmb
	}
	return nil
}

func (x *GetAMBInfoResponse) GetCurrentTier() external.Tier {
	if x != nil {
		return x.CurrentTier
	}
	return external.Tier(0)
}

func (x *GetAMBInfoResponse) GetShortfallAmount() *money.Money {
	if x != nil {
		return x.ShortfallAmount
	}
	return nil
}

type IsUserEligibleForRewardsResponse_TierToEligibilityStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Display string for tier plan
	TierName external.Tier `protobuf:"varint,1,opt,name=tier_name,json=tierName,proto3,enum=tiering.external.Tier" json:"tier_name,omitempty"`
	// Boolean to represent if a user is eligible for rewards.
	IsEligible bool `protobuf:"varint,2,opt,name=is_eligible,json=isEligible,proto3" json:"is_eligible,omitempty"`
}

func (x *IsUserEligibleForRewardsResponse_TierToEligibilityStatus) Reset() {
	*x = IsUserEligibleForRewardsResponse_TierToEligibilityStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsUserEligibleForRewardsResponse_TierToEligibilityStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsUserEligibleForRewardsResponse_TierToEligibilityStatus) ProtoMessage() {}

func (x *IsUserEligibleForRewardsResponse_TierToEligibilityStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsUserEligibleForRewardsResponse_TierToEligibilityStatus.ProtoReflect.Descriptor instead.
func (*IsUserEligibleForRewardsResponse_TierToEligibilityStatus) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{5, 0}
}

func (x *IsUserEligibleForRewardsResponse_TierToEligibilityStatus) GetTierName() external.Tier {
	if x != nil {
		return x.TierName
	}
	return external.Tier(0)
}

func (x *IsUserEligibleForRewardsResponse_TierToEligibilityStatus) GetIsEligible() bool {
	if x != nil {
		return x.IsEligible
	}
	return false
}

type EvaluateTierForActorRequest_Options struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToEvalOnRealTimeData  bool `protobuf:"varint,1,opt,name=to_eval_on_real_time_data,json=toEvalOnRealTimeData,proto3" json:"to_eval_on_real_time_data,omitempty"`
	ToEvalForMultipleWays bool `protobuf:"varint,2,opt,name=to_eval_for_multiple_ways,json=toEvalForMultipleWays,proto3" json:"to_eval_for_multiple_ways,omitempty"`
	ToSkipAppAccessCheck  bool `protobuf:"varint,3,opt,name=to_skip_app_access_check,json=toSkipAppAccessCheck,proto3" json:"to_skip_app_access_check,omitempty"`
	// evaluator will use trial thresholds to evaluate the tier
	ToEvalWithTrialThresholdsForPlus     bool `protobuf:"varint,4,opt,name=to_eval_with_trial_thresholds_for_plus,json=toEvalWithTrialThresholdsForPlus,proto3" json:"to_eval_with_trial_thresholds_for_plus,omitempty"`
	ToEvalWithTrialThresholdsForInfinite bool `protobuf:"varint,5,opt,name=to_eval_with_trial_thresholds_for_infinite,json=toEvalWithTrialThresholdsForInfinite,proto3" json:"to_eval_with_trial_thresholds_for_infinite,omitempty"`
	ToEvalWithTrialThresholdsForPrime    bool `protobuf:"varint,6,opt,name=to_eval_with_trial_thresholds_for_prime,json=toEvalWithTrialThresholdsForPrime,proto3" json:"to_eval_with_trial_thresholds_for_prime,omitempty"`
}

func (x *EvaluateTierForActorRequest_Options) Reset() {
	*x = EvaluateTierForActorRequest_Options{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluateTierForActorRequest_Options) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateTierForActorRequest_Options) ProtoMessage() {}

func (x *EvaluateTierForActorRequest_Options) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateTierForActorRequest_Options.ProtoReflect.Descriptor instead.
func (*EvaluateTierForActorRequest_Options) Descriptor() ([]byte, []int) {
	return file_api_tiering_service_proto_rawDescGZIP(), []int{41, 0}
}

func (x *EvaluateTierForActorRequest_Options) GetToEvalOnRealTimeData() bool {
	if x != nil {
		return x.ToEvalOnRealTimeData
	}
	return false
}

func (x *EvaluateTierForActorRequest_Options) GetToEvalForMultipleWays() bool {
	if x != nil {
		return x.ToEvalForMultipleWays
	}
	return false
}

func (x *EvaluateTierForActorRequest_Options) GetToSkipAppAccessCheck() bool {
	if x != nil {
		return x.ToSkipAppAccessCheck
	}
	return false
}

func (x *EvaluateTierForActorRequest_Options) GetToEvalWithTrialThresholdsForPlus() bool {
	if x != nil {
		return x.ToEvalWithTrialThresholdsForPlus
	}
	return false
}

func (x *EvaluateTierForActorRequest_Options) GetToEvalWithTrialThresholdsForInfinite() bool {
	if x != nil {
		return x.ToEvalWithTrialThresholdsForInfinite
	}
	return false
}

func (x *EvaluateTierForActorRequest_Options) GetToEvalWithTrialThresholdsForPrime() bool {
	if x != nil {
		return x.ToEvalWithTrialThresholdsForPrime
	}
	return false
}

var File_api_tiering_service_proto protoreflect.FileDescriptor

var file_api_tiering_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x73, 0x68, 0x65,
	0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x2f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61,
	0x70, 0x69, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x33, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x72, 0x69, 0x61,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xb7, 0x02, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x0d, 0x74,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x0c, 0x74, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x31, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x5f,
	0x66, 0x6f, 0x72, 0x5f, 0x74, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x12, 0x69, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x54, 0x72,
	0x69, 0x61, 0x6c, 0x12, 0x46, 0x0a, 0x13, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x5f,
	0x74, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x11, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x6c, 0x65, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x69, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x3a, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x22, 0x71, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69,
	0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x04, 0x74,
	0x69, 0x65, 0x72, 0x22, 0x63, 0x0a, 0x1f, 0x49, 0x73, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22, 0xb8, 0x02, 0x0a, 0x20, 0x49, 0x73, 0x55,
	0x73, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x7e, 0x0a, 0x1a, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x6c,
	0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x49, 0x73, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46,
	0x6f, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x54, 0x6f, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x17, 0x74, 0x69, 0x65, 0x72, 0x54,
	0x6f, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x1a, 0x6f, 0x0a, 0x17, 0x54, 0x69, 0x65, 0x72, 0x54, 0x6f, 0x45, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a,
	0x09, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x08, 0x74, 0x69, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x22, 0x7d, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x44,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x54, 0x69, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x69, 0x6e,
	0x63, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x44,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x54, 0x69, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0e, 0x64, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x63, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x63, 0x74, 0x54, 0x69, 0x65, 0x72, 0x73, 0x22, 0xac, 0x06, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x56, 0x32, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x12, 0x5d, 0x0a, 0x15, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x13, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x59, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x76,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x6c, 0x61,
	0x73, 0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x5d, 0x0a, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2e, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x44,
	0x6f, 0x77, 0x6e, 0x67, 0x72, 0x61, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x3e, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x69,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72,
	0x52, 0x0d, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x61, 0x73, 0x65, 0x54, 0x69, 0x65, 0x72, 0x12,
	0x5e, 0x0a, 0x1a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x17, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x62, 0x0a, 0x1c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x19, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x64, 0x0a, 0x1d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x2c, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53,
	0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x65, 0x22, 0x3e, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a, 0x22, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x6e,
	0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x3d, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x22, 0x4a, 0x0a, 0x23, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x91, 0x01,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x12,
	0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x69, 0x74, 0x63, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x10, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x46, 0x6c, 0x6f,
	0x77, 0x22, 0xbb, 0x02, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73,
	0x50, 0x69, 0x74, 0x63, 0x68, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x52, 0x0a, 0x11,
	0x61, 0x64, 0x64, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x41, 0x64,
	0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52,
	0x0f, 0x61, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x64, 0x0a, 0x17, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52,
	0x15, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x70, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22,
	0x87, 0x04, 0x0a, 0x1b, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68,
	0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x39, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x17, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x74, 0x69,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72,
	0x52, 0x08, 0x6e, 0x65, 0x78, 0x74, 0x54, 0x69, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x14, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x6e, 0x65,
	0x78, 0x74, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x48, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x69, 0x6e,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x10,
	0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x73, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x69,
	0x73, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x69, 0x74, 0x63,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x52, 0x65, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x69, 0x74, 0x63, 0x68, 0x22, 0xab, 0x04, 0x0a, 0x21, 0x54, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x39, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x17, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x74, 0x69,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72,
	0x52, 0x08, 0x6e, 0x65, 0x78, 0x74, 0x54, 0x69, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x14, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x6e, 0x65,
	0x78, 0x74, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x48, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x17, 0x69, 0x73, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12,
	0x31, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x53, 0x0a, 0x18, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x15, 0x67, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x65, 0x72, 0x41, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0e, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x0d, 0x74, 0x69, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0xa3, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x37, 0x0a, 0x09, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x74, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2c, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53,
	0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x65, 0x22, 0x45, 0x0a, 0x1f, 0x49, 0x73, 0x54, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x66,
	0x0a, 0x20, 0x49, 0x73, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x79, 0x0a, 0x0e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0xbe, 0x01, 0x0a, 0x0f, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x65, 0x72, 0x12,
	0x2f, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x06, 0x74, 0x6f, 0x54, 0x69, 0x65, 0x72,
	0x22, 0x20, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x45, 0x44,
	0x10, 0x65, 0x22, 0xba, 0x01, 0x0a, 0x1b, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x4c, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22,
	0x6a, 0x0a, 0x1c, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x68,
	0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0x40, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x97, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x53, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2e, 0x54, 0x69, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x22, 0xf3, 0x01, 0x0a, 0x21, 0x49, 0x73, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x6f,
	0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x6f, 0x76,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6d, 0x6f, 0x76, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x74, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69,
	0x65, 0x72, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x07,
	0x74, 0x6f, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x06, 0x74, 0x6f, 0x54, 0x69, 0x65, 0x72, 0x22, 0x6a, 0x0a,
	0x22, 0x49, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x46, 0x6f, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x22, 0x40, 0x0a, 0x1a, 0x49, 0x73, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x6f, 0x0a, 0x1b, 0x49,
	0x73, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x49,
	0x6e, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0x3c, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x78, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x9a, 0x06, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x78, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0c, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x47, 0x72, 0x61, 0x63, 0x65, 0x12,
	0x46, 0x0a, 0x11, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f,
	0x74, 0x69, 0x6c, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x2c, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6f, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x43, 0x6f,
	0x6f, 0x6c, 0x4f, 0x66, 0x66, 0x12, 0x4b, 0x0a, 0x14, 0x63, 0x6f, 0x6f, 0x6c, 0x5f, 0x6f, 0x66,
	0x66, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x11, 0x63, 0x6f, 0x6f, 0x6c, 0x4f, 0x66, 0x66, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54, 0x69,
	0x6c, 0x6c, 0x12, 0x54, 0x0a, 0x12, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x68,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x61,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x61, 0x62, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x41, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x62, 0x75, 0x73, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x5f, 0x0a, 0x17, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x73, 0x52, 0x15, 0x74, 0x69, 0x65, 0x72,
	0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x62,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41,
	0x6d, 0x62, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x61,
	0x6d, 0x62, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x41, 0x6d, 0x62, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x66, 0x61,
	0x6c, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x66,
	0x61, 0x6c, 0x6c, 0x12, 0x34, 0x0a, 0x0b, 0x61, 0x6d, 0x62, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x41, 0x6d, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0a, 0x61,
	0x6d, 0x62, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x22, 0x2c, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53,
	0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x65, 0x22, 0x5d, 0x0a, 0x0a, 0x41, 0x6d, 0x62, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x6c, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x6d, 0x62, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x6d, 0x62, 0x4d, 0x61, 0x69, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x1a, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x12, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x11, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x22, 0x42, 0x0a, 0x1b, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x1c, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x6f, 0x6c, 0x4f, 0x66, 0x66, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x49, 0x0a,
	0x12, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x44, 0x0a, 0x1d, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x6f, 0x6c, 0x4f, 0x66, 0x66, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x33,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x22, 0xcb, 0x05, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x55, 0x0a, 0x19, 0x64, 0x6f, 0x77, 0x6e, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x17, 0x64, 0x6f, 0x77, 0x6e, 0x67, 0x72, 0x61, 0x64, 0x65, 0x57, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x15, 0x67,
	0x72, 0x61, 0x63, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x67, 0x72, 0x61, 0x63, 0x65, 0x57, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x1d, 0x67, 0x72,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1a, 0x67, 0x72,
	0x61, 0x63, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x21, 0x69, 0x73, 0x5f, 0x72,
	0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1c, 0x69, 0x73, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x54, 0x69,
	0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x5d, 0x0a, 0x1a, 0x72, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x74, 0x69, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x54, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x17, 0x72, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x54, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x68, 0x0a, 0x33, 0x69, 0x73, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x5f,
	0x77, 0x61, 0x79, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x66, 0x6f,
	0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x2b, 0x69,
	0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x57, 0x61, 0x79, 0x73, 0x54, 0x6f, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x75, 0x0a, 0x22, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x52, 0x1f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x22, 0xa1, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f,
	0x62, 0x32, 0x63, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x19, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x42, 0x32, 0x63, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x61, 0x61, 0x5f, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x18, 0x61, 0x61, 0x53,
	0x61, 0x6c, 0x61, 0x72, 0x79, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xd0, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x72, 0x54, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x52, 0x0a, 0x1c, 0x6d, 0x69, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x74, 0x69, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x6d, 0x69, 0x6e,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x72, 0x54, 0x69, 0x65, 0x72, 0x12, 0x61, 0x0a, 0x24, 0x6d, 0x69, 0x6e, 0x5f, 0x62, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x66, 0x6f, 0x72,
	0x5f, 0x72, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1f, 0x6d, 0x69, 0x6e, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x50, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x54, 0x69, 0x65, 0x72, 0x22, 0xc0, 0x04, 0x0a, 0x1b, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x65, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xb4, 0x03, 0x0a, 0x07, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x37, 0x0a, 0x19, 0x74, 0x6f, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x6f, 0x6e, 0x5f, 0x72,
	0x65, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x14, 0x74, 0x6f, 0x45, 0x76, 0x61, 0x6c, 0x4f, 0x6e, 0x52, 0x65, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x19, 0x74, 0x6f, 0x5f,
	0x65, 0x76, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c,
	0x65, 0x5f, 0x77, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x74, 0x6f,
	0x45, 0x76, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x57,
	0x61, 0x79, 0x73, 0x12, 0x36, 0x0a, 0x18, 0x74, 0x6f, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x61,
	0x70, 0x70, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x74, 0x6f, 0x53, 0x6b, 0x69, 0x70, 0x41, 0x70, 0x70,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x50, 0x0a, 0x26, 0x74,
	0x6f, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x5f, 0x66, 0x6f, 0x72,
	0x5f, 0x70, 0x6c, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x20, 0x74, 0x6f, 0x45,
	0x76, 0x61, 0x6c, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x46, 0x6f, 0x72, 0x50, 0x6c, 0x75, 0x73, 0x12, 0x58, 0x0a,
	0x2a, 0x74, 0x6f, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x72,
	0x69, 0x61, 0x6c, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x5f, 0x66,
	0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x24, 0x74, 0x6f, 0x45, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x69,
	0x61, 0x6c, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x46, 0x6f, 0x72, 0x49,
	0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x12, 0x52, 0x0a, 0x27, 0x74, 0x6f, 0x5f, 0x65, 0x76,
	0x61, 0x6c, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x74, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x70, 0x72, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x21, 0x74, 0x6f, 0x45, 0x76, 0x61, 0x6c,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x73, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x69, 0x6d, 0x65, 0x22, 0xb7, 0x02, 0x0a, 0x1c,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3d, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65,
	0x72, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x65, 0x72,
	0x12, 0x32, 0x0a, 0x15, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x7f, 0x0a, 0x29, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x65, 0x72, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x69, 0x65, 0x64, 0x43, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x29, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x65, 0x72, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x69,
	0x65, 0x64, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xbf, 0x01, 0x0a, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x66, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x04,
	0x74, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69,
	0x65, 0x72, 0x52, 0x04, 0x74, 0x69, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x22, 0x77, 0x0a, 0x2f, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x49, 0x66, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c,
	0x65, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x22, 0xc5, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x05, 0x74, 0x69, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x05, 0x74, 0x69, 0x65, 0x72,
	0x73, 0x12, 0x45, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x22, 0x97, 0x02, 0x0a, 0x21, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x46, 0x6f,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x72, 0x0a, 0x14, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x41, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54, 0x69,
	0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x74, 0x69, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x1a, 0x59, 0x0a, 0x16, 0x54, 0x69, 0x65, 0x72, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x41, 0x0a, 0x0a, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0x79, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x74,
	0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x74, 0x6f, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xd1, 0x02, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x3e, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x12, 0x69, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x57, 0x0a, 0x0b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x14,
	0x0a, 0x10, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47,
	0x45, 0x54, 0x10, 0x02, 0x22, 0x98, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xce, 0x02, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x44, 0x72, 0x6f, 0x70, 0x4f, 0x66, 0x66, 0x42, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x14, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x72, 0x6f, 0x70, 0x4f, 0x66, 0x66, 0x42, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x52, 0x12, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63,
	0x68, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x3a, 0x0a, 0x0d, 0x74, 0x69, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x5f, 0x70, 0x69, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x74, 0x69, 0x65, 0x72, 0x54, 0x6f, 0x50, 0x69,
	0x74, 0x63, 0x68, 0x22, 0x6b, 0x0a, 0x12, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69,
	0x74, 0x63, 0x68, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x24, 0x0a, 0x20, 0x54, 0x49, 0x45,
	0x52, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f,
	0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x01, 0x12, 0x11,
	0x0a, 0x0d, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10,
	0x02, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x03,
	0x22, 0x7d, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x44, 0x72, 0x6f, 0x70, 0x4f, 0x66, 0x66, 0x42, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22,
	0x37, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x4d, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x9b, 0x02, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x41, 0x4d, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x6d, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x62, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x6d, 0x62, 0x12, 0x39, 0x0a, 0x0c,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x10, 0x73, 0x68, 0x6f, 0x72, 0x74,
	0x66, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x66, 0x61, 0x6c, 0x6c,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0xbd, 0x15, 0x0a, 0x07, 0x54, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x50, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1d, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x65, 0x72, 0x41, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x3e, 0x0a, 0x07, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12,
	0x17, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x14, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x24, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x25, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x68, 0x6f,
	0x77, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x1b, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77,
	0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2b, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53,
	0x68, 0x6f, 0x77, 0x6e, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x56, 0x32, 0x12, 0x21, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74,
	0x63, 0x68, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x50, 0x69, 0x74, 0x63, 0x68, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x54, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72,
	0x43, 0x78, 0x12, 0x1f, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x78, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x78, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x13, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x23, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x47,
	0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x15, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x43, 0x6f, 0x6f, 0x6c, 0x4f, 0x66, 0x66, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x12, 0x25, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x43, 0x6f, 0x6f, 0x6c, 0x4f, 0x66, 0x66, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x43, 0x6f, 0x6f, 0x6c, 0x4f, 0x66,
	0x66, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x74, 0x0a, 0x18, 0x49, 0x73, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x28, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x73, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x49, 0x73, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x59, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1f, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01,
	0x12, 0x65, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x46,
	0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x23, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x7a, 0x0a, 0x1a, 0x49, 0x73, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x6f, 0x76,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x49, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46,
	0x6f, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2b, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x73, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x6f,
	0x76, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03,
	0x88, 0x02, 0x01, 0x12, 0x65, 0x0a, 0x13, 0x49, 0x73, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x47,
	0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x23, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x73, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x47, 0x72, 0x61,
	0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x73, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x77, 0x0a, 0x14, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x2d, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2e, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x16, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x2f, 0x2e,
	0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x54, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x1f, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x14, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x24,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x27,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x66, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x73, 0x45, 0x6c,
	0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63,
	0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x37, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x66, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x73,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x68, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x38, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x49, 0x66, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c,
	0x65, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x54, 0x69, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x46,
	0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x29, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x46, 0x6f,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87,
	0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x30, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x54, 0x69, 0x65, 0x72,
	0x73, 0x12, 0x25, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x63, 0x74, 0x54, 0x69, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x63, 0x74, 0x54, 0x69, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6f, 0x0a, 0x18, 0x49, 0x73, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x28, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x73, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x49, 0x73, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46,
	0x6f, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x45, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x4d, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x4d, 0x42,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x4d, 0x42, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x14, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73,
	0x12, 0x30, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x62,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x68, 0x65, 0x72,
	0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x78, 0x2e, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x68,
	0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12,
	0x26, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72,
	0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x54, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x1f, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5a, 0x22, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_tiering_service_proto_rawDescOnce sync.Once
	file_api_tiering_service_proto_rawDescData = file_api_tiering_service_proto_rawDesc
)

func file_api_tiering_service_proto_rawDescGZIP() []byte {
	file_api_tiering_service_proto_rawDescOnce.Do(func() {
		file_api_tiering_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_tiering_service_proto_rawDescData)
	})
	return file_api_tiering_service_proto_rawDescData
}

var file_api_tiering_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_tiering_service_proto_msgTypes = make([]protoimpl.MessageInfo, 58)
var file_api_tiering_service_proto_goTypes = []interface{}{
	(GetTieringPitchV2Response_Status)(0),                            // 0: tiering.GetTieringPitchV2Response.Status
	(GetTierAtTimeResponse_Status)(0),                                // 1: tiering.GetTierAtTimeResponse.Status
	(UpgradeResponse_Status)(0),                                      // 2: tiering.UpgradeResponse.Status
	(GetDetailsForCxResponse_Status)(0),                              // 3: tiering.GetDetailsForCxResponse.Status
	(GetActorScreenInteractionDetailsRequest_RequestType)(0),         // 4: tiering.GetActorScreenInteractionDetailsRequest.RequestType
	(GetDropOffBottomSheetRequest_TieringPitchMethod)(0),             // 5: tiering.GetDropOffBottomSheetRequest.TieringPitchMethod
	(*GetTrialDetailsRequest)(nil),                                   // 6: tiering.GetTrialDetailsRequest
	(*GetTrialDetailsResponse)(nil),                                  // 7: tiering.GetTrialDetailsResponse
	(*GetCurrentTierForActorRequest)(nil),                            // 8: tiering.GetCurrentTierForActorRequest
	(*GetCurrentTierForActorResponse)(nil),                           // 9: tiering.GetCurrentTierForActorResponse
	(*IsUserEligibleForRewardsRequest)(nil),                          // 10: tiering.IsUserEligibleForRewardsRequest
	(*IsUserEligibleForRewardsResponse)(nil),                         // 11: tiering.IsUserEligibleForRewardsResponse
	(*GetActorDistinctTiersRequest)(nil),                             // 12: tiering.GetActorDistinctTiersRequest
	(*GetActorDistinctTiersResponse)(nil),                            // 13: tiering.GetActorDistinctTiersResponse
	(*GetTieringPitchV2Response)(nil),                                // 14: tiering.GetTieringPitchV2Response
	(*GetTieringPitchV2Request)(nil),                                 // 15: tiering.GetTieringPitchV2Request
	(*RecordComponentShownToActorRequest)(nil),                       // 16: tiering.RecordComponentShownToActorRequest
	(*RecordComponentShownToActorResponse)(nil),                      // 17: tiering.RecordComponentShownToActorResponse
	(*GetTieringPitchRequest)(nil),                                   // 18: tiering.GetTieringPitchRequest
	(*GetTieringPitchResponse)(nil),                                  // 19: tiering.GetTieringPitchResponse
	(*TieringPitchAddFundsDetails)(nil),                              // 20: tiering.TieringPitchAddFundsDetails
	(*TieringPitchProfileSectionDetails)(nil),                        // 21: tiering.TieringPitchProfileSectionDetails
	(*GetTierAtTimeRequest)(nil),                                     // 22: tiering.GetTierAtTimeRequest
	(*GetTierAtTimeResponse)(nil),                                    // 23: tiering.GetTierAtTimeResponse
	(*IsTieringEnabledForActorRequest)(nil),                          // 24: tiering.IsTieringEnabledForActorRequest
	(*IsTieringEnabledForActorResponse)(nil),                         // 25: tiering.IsTieringEnabledForActorResponse
	(*UpgradeRequest)(nil),                                           // 26: tiering.UpgradeRequest
	(*UpgradeResponse)(nil),                                          // 27: tiering.UpgradeResponse
	(*ShowComponentToActorRequest)(nil),                              // 28: tiering.ShowComponentToActorRequest
	(*ShowComponentToActorResponse)(nil),                             // 29: tiering.ShowComponentToActorResponse
	(*GetCriteriaForActorRequest)(nil),                               // 30: tiering.GetCriteriaForActorRequest
	(*GetCriteriaForActorResponse)(nil),                              // 31: tiering.GetCriteriaForActorResponse
	(*IsActorEligibleForMovementRequest)(nil),                        // 32: tiering.IsActorEligibleForMovementRequest
	(*IsActorEligibleForMovementResponse)(nil),                       // 33: tiering.IsActorEligibleForMovementResponse
	(*IsUserInGracePeriodRequest)(nil),                               // 34: tiering.IsUserInGracePeriodRequest
	(*IsUserInGracePeriodResponse)(nil),                              // 35: tiering.IsUserInGracePeriodResponse
	(*GetDetailsForCxRequest)(nil),                                   // 36: tiering.GetDetailsForCxRequest
	(*GetDetailsForCxResponse)(nil),                                  // 37: tiering.GetDetailsForCxResponse
	(*AmbDetails)(nil),                                               // 38: tiering.AmbDetails
	(*OverrideGracePeriodRequest)(nil),                               // 39: tiering.OverrideGracePeriodRequest
	(*OverrideGracePeriodResponse)(nil),                              // 40: tiering.OverrideGracePeriodResponse
	(*OverrideCoolOffPeriodRequest)(nil),                             // 41: tiering.OverrideCoolOffPeriodRequest
	(*OverrideCoolOffPeriodResponse)(nil),                            // 42: tiering.OverrideCoolOffPeriodResponse
	(*GetConfigParamsRequest)(nil),                                   // 43: tiering.GetConfigParamsRequest
	(*GetConfigParamsResponse)(nil),                                  // 44: tiering.GetConfigParamsResponse
	(*CriteriaSegmentExclusionConfigs)(nil),                          // 45: tiering.CriteriaSegmentExclusionConfigs
	(*RegularTierConfigParams)(nil),                                  // 46: tiering.RegularTierConfigParams
	(*EvaluateTierForActorRequest)(nil),                              // 47: tiering.EvaluateTierForActorRequest
	(*EvaluateTierForActorResponse)(nil),                             // 48: tiering.EvaluateTierForActorResponse
	(*CheckIfActorIsEligibleForCashbackRewardRequest)(nil),           // 49: tiering.CheckIfActorIsEligibleForCashbackRewardRequest
	(*CheckIfActorIsEligibleForCashbackRewardResponse)(nil),          // 50: tiering.CheckIfActorIsEligibleForCashbackRewardResponse
	(*GetTierTimeRangesForActorRequest)(nil),                         // 51: tiering.GetTierTimeRangesForActorRequest
	(*GetTierTimeRangesForActorResponse)(nil),                        // 52: tiering.GetTierTimeRangesForActorResponse
	(*TimeRanges)(nil),                                               // 53: tiering.TimeRanges
	(*TimeRange)(nil),                                                // 54: tiering.TimeRange
	(*GetActorScreenInteractionDetailsRequest)(nil),                  // 55: tiering.GetActorScreenInteractionDetailsRequest
	(*GetActorScreenInteractionDetailsResponse)(nil),                 // 56: tiering.GetActorScreenInteractionDetailsResponse
	(*GetDropOffBottomSheetRequest)(nil),                             // 57: tiering.GetDropOffBottomSheetRequest
	(*GetDropOffBottomSheetResponse)(nil),                            // 58: tiering.GetDropOffBottomSheetResponse
	(*GetAMBInfoRequest)(nil),                                        // 59: tiering.GetAMBInfoRequest
	(*GetAMBInfoResponse)(nil),                                       // 60: tiering.GetAMBInfoResponse
	(*IsUserEligibleForRewardsResponse_TierToEligibilityStatus)(nil), // 61: tiering.IsUserEligibleForRewardsResponse.TierToEligibilityStatus
	(*EvaluateTierForActorRequest_Options)(nil),                      // 62: tiering.EvaluateTierForActorRequest.Options
	nil,                                                     // 63: tiering.GetTierTimeRangesForActorResponse.TierTimeRangesMapEntry
	(*rpc.Status)(nil),                                      // 64: rpc.Status
	(*external.TrialDetails)(nil),                           // 65: tiering.external.TrialDetails
	(external.Tier)(0),                                      // 66: tiering.external.Tier
	(*deeplink.Deeplink)(nil),                               // 67: frontend.deeplink.Deeplink
	(*date.Date)(nil),                                       // 68: google.type.Date
	(*timestamppb.Timestamp)(nil),                           // 69: google.protobuf.Timestamp
	(*external.MovementExternalDetails)(nil),                // 70: tiering.external.MovementExternalDetails
	(*external.LatestMovementDetails)(nil),                  // 71: tiering.external.LatestMovementDetails
	(enums.CriteriaOptionType)(0),                           // 72: tiering.enums.CriteriaOptionType
	(enums.DisplayComponent)(0),                             // 73: tiering.enums.DisplayComponent
	(enums.TieringPitchFlowFilter)(0),                       // 74: tiering.enums.TieringPitchFlowFilter
	(*money.Money)(nil),                                     // 75: google.type.Money
	(*external.TierInfo)(nil),                               // 76: tiering.external.TierInfo
	(enums.Provenance)(0),                                   // 77: tiering.enums.Provenance
	(*external.TierExternalCriteria)(nil),                   // 78: tiering.external.TierExternalCriteria
	(enums.TierMovementType)(0),                             // 79: tiering.enums.TierMovementType
	(*external.TierMovementHistory)(nil),                    // 80: tiering.external.TierMovementHistory
	(*external.TierMovementCriterias)(nil),                  // 81: tiering.external.TierMovementCriterias
	(*durationpb.Duration)(nil),                             // 82: google.protobuf.Duration
	(enums.TieringScreen)(0),                                // 83: tiering.enums.TieringScreen
	(enums.ActorScreenInteractionStatus)(0),                 // 84: tiering.enums.ActorScreenInteractionStatus
	(*dynamic_elements.FetchDynamicElementsRequest)(nil),    // 85: dynamic_elements.FetchDynamicElementsRequest
	(*dynamic_elements.DynamicElementCallbackRequest)(nil),  // 86: dynamic_elements.DynamicElementCallbackRequest
	(*sherlock_banners.FetchSherlockBannersRequest)(nil),    // 87: cx.sherlock_banners.FetchSherlockBannersRequest
	(*dynamic_elements.FetchDynamicElementsResponse)(nil),   // 88: dynamic_elements.FetchDynamicElementsResponse
	(*dynamic_elements.DynamicElementCallbackResponse)(nil), // 89: dynamic_elements.DynamicElementCallbackResponse
	(*sherlock_banners.FetchSherlockBannersResponse)(nil),   // 90: cx.sherlock_banners.FetchSherlockBannersResponse
}
var file_api_tiering_service_proto_depIdxs = []int32{
	64,  // 0: tiering.GetTrialDetailsResponse.status:type_name -> rpc.Status
	65,  // 1: tiering.GetTrialDetailsResponse.trial_details:type_name -> tiering.external.TrialDetails
	66,  // 2: tiering.GetTrialDetailsResponse.eligible_trial_tier:type_name -> tiering.external.Tier
	67,  // 3: tiering.GetTrialDetailsResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	64,  // 4: tiering.GetCurrentTierForActorResponse.status:type_name -> rpc.Status
	66,  // 5: tiering.GetCurrentTierForActorResponse.tier:type_name -> tiering.external.Tier
	68,  // 6: tiering.IsUserEligibleForRewardsRequest.date:type_name -> google.type.Date
	64,  // 7: tiering.IsUserEligibleForRewardsResponse.status:type_name -> rpc.Status
	61,  // 8: tiering.IsUserEligibleForRewardsResponse.tier_to_eligibility_status:type_name -> tiering.IsUserEligibleForRewardsResponse.TierToEligibilityStatus
	69,  // 9: tiering.GetActorDistinctTiersRequest.time_since:type_name -> google.protobuf.Timestamp
	64,  // 10: tiering.GetActorDistinctTiersResponse.status:type_name -> rpc.Status
	66,  // 11: tiering.GetActorDistinctTiersResponse.distinct_tiers:type_name -> tiering.external.Tier
	64,  // 12: tiering.GetTieringPitchV2Response.status:type_name -> rpc.Status
	66,  // 13: tiering.GetTieringPitchV2Response.current_tier:type_name -> tiering.external.Tier
	70,  // 14: tiering.GetTieringPitchV2Response.movement_details_list:type_name -> tiering.external.MovementExternalDetails
	71,  // 15: tiering.GetTieringPitchV2Response.last_upgrade_details:type_name -> tiering.external.LatestMovementDetails
	71,  // 16: tiering.GetTieringPitchV2Response.last_downgrade_details:type_name -> tiering.external.LatestMovementDetails
	66,  // 17: tiering.GetTieringPitchV2Response.actor_base_tier:type_name -> tiering.external.Tier
	72,  // 18: tiering.GetTieringPitchV2Response.entry_criteria_option_type:type_name -> tiering.enums.CriteriaOptionType
	72,  // 19: tiering.GetTieringPitchV2Response.current_criteria_option_type:type_name -> tiering.enums.CriteriaOptionType
	72,  // 20: tiering.GetTieringPitchV2Response.current_criteria_option_types:type_name -> tiering.enums.CriteriaOptionType
	73,  // 21: tiering.RecordComponentShownToActorRequest.component:type_name -> tiering.enums.DisplayComponent
	64,  // 22: tiering.RecordComponentShownToActorResponse.status:type_name -> rpc.Status
	74,  // 23: tiering.GetTieringPitchRequest.tiering_pitch_flow:type_name -> tiering.enums.TieringPitchFlowFilter
	64,  // 24: tiering.GetTieringPitchResponse.status:type_name -> rpc.Status
	20,  // 25: tiering.GetTieringPitchResponse.add_funds_details:type_name -> tiering.TieringPitchAddFundsDetails
	21,  // 26: tiering.GetTieringPitchResponse.profile_section_details:type_name -> tiering.TieringPitchProfileSectionDetails
	66,  // 27: tiering.TieringPitchAddFundsDetails.current_tier:type_name -> tiering.external.Tier
	75,  // 28: tiering.TieringPitchAddFundsDetails.current_tier_min_amount:type_name -> google.type.Money
	66,  // 29: tiering.TieringPitchAddFundsDetails.next_tier:type_name -> tiering.external.Tier
	75,  // 30: tiering.TieringPitchAddFundsDetails.next_tier_min_amount:type_name -> google.type.Money
	75,  // 31: tiering.TieringPitchAddFundsDetails.current_balance_amount:type_name -> google.type.Money
	75,  // 32: tiering.TieringPitchAddFundsDetails.min_amount:type_name -> google.type.Money
	75,  // 33: tiering.TieringPitchAddFundsDetails.suggested_amount:type_name -> google.type.Money
	66,  // 34: tiering.TieringPitchProfileSectionDetails.current_tier:type_name -> tiering.external.Tier
	75,  // 35: tiering.TieringPitchProfileSectionDetails.current_tier_min_amount:type_name -> google.type.Money
	66,  // 36: tiering.TieringPitchProfileSectionDetails.next_tier:type_name -> tiering.external.Tier
	75,  // 37: tiering.TieringPitchProfileSectionDetails.next_tier_min_amount:type_name -> google.type.Money
	75,  // 38: tiering.TieringPitchProfileSectionDetails.current_balance_amount:type_name -> google.type.Money
	75,  // 39: tiering.TieringPitchProfileSectionDetails.min_amount:type_name -> google.type.Money
	69,  // 40: tiering.TieringPitchProfileSectionDetails.grace_period_expiry_time:type_name -> google.protobuf.Timestamp
	69,  // 41: tiering.GetTierAtTimeRequest.tier_timestamp:type_name -> google.protobuf.Timestamp
	64,  // 42: tiering.GetTierAtTimeResponse.status:type_name -> rpc.Status
	76,  // 43: tiering.GetTierAtTimeResponse.tier_info:type_name -> tiering.external.TierInfo
	64,  // 44: tiering.IsTieringEnabledForActorResponse.status:type_name -> rpc.Status
	77,  // 45: tiering.UpgradeRequest.provenance:type_name -> tiering.enums.Provenance
	64,  // 46: tiering.UpgradeResponse.status:type_name -> rpc.Status
	66,  // 47: tiering.UpgradeResponse.from_tier:type_name -> tiering.external.Tier
	66,  // 48: tiering.UpgradeResponse.to_tier:type_name -> tiering.external.Tier
	73,  // 49: tiering.ShowComponentToActorRequest.display_component:type_name -> tiering.enums.DisplayComponent
	64,  // 50: tiering.ShowComponentToActorResponse.status:type_name -> rpc.Status
	64,  // 51: tiering.GetCriteriaForActorResponse.status:type_name -> rpc.Status
	78,  // 52: tiering.GetCriteriaForActorResponse.external_criteria:type_name -> tiering.external.TierExternalCriteria
	79,  // 53: tiering.IsActorEligibleForMovementRequest.movement_type:type_name -> tiering.enums.TierMovementType
	66,  // 54: tiering.IsActorEligibleForMovementRequest.from_tier:type_name -> tiering.external.Tier
	66,  // 55: tiering.IsActorEligibleForMovementRequest.to_tier:type_name -> tiering.external.Tier
	64,  // 56: tiering.IsActorEligibleForMovementResponse.status:type_name -> rpc.Status
	64,  // 57: tiering.IsUserInGracePeriodResponse.status:type_name -> rpc.Status
	64,  // 58: tiering.GetDetailsForCxResponse.status:type_name -> rpc.Status
	66,  // 59: tiering.GetDetailsForCxResponse.current_tier:type_name -> tiering.external.Tier
	69,  // 60: tiering.GetDetailsForCxResponse.grace_period_till:type_name -> google.protobuf.Timestamp
	69,  // 61: tiering.GetDetailsForCxResponse.cool_off_period_till:type_name -> google.protobuf.Timestamp
	80,  // 62: tiering.GetDetailsForCxResponse.movement_histories:type_name -> tiering.external.TierMovementHistory
	81,  // 63: tiering.GetDetailsForCxResponse.tier_movement_criterias:type_name -> tiering.external.TierMovementCriterias
	38,  // 64: tiering.GetDetailsForCxResponse.amb_history:type_name -> tiering.AmbDetails
	69,  // 65: tiering.OverrideGracePeriodRequest.override_timestamp:type_name -> google.protobuf.Timestamp
	64,  // 66: tiering.OverrideGracePeriodResponse.status:type_name -> rpc.Status
	69,  // 67: tiering.OverrideCoolOffPeriodRequest.override_timestamp:type_name -> google.protobuf.Timestamp
	64,  // 68: tiering.OverrideCoolOffPeriodResponse.status:type_name -> rpc.Status
	64,  // 69: tiering.GetConfigParamsResponse.status:type_name -> rpc.Status
	82,  // 70: tiering.GetConfigParamsResponse.downgrade_window_duration:type_name -> google.protobuf.Duration
	82,  // 71: tiering.GetConfigParamsResponse.grace_window_duration:type_name -> google.protobuf.Duration
	82,  // 72: tiering.GetConfigParamsResponse.grace_initial_window_duration:type_name -> google.protobuf.Duration
	46,  // 73: tiering.GetConfigParamsResponse.regular_tier_config_params:type_name -> tiering.RegularTierConfigParams
	45,  // 74: tiering.GetConfigParamsResponse.criteria_segment_exclusion_configs:type_name -> tiering.CriteriaSegmentExclusionConfigs
	75,  // 75: tiering.RegularTierConfigParams.min_balance_for_regular_tier:type_name -> google.type.Money
	75,  // 76: tiering.RegularTierConfigParams.min_balance_penalty_for_regular_tier:type_name -> google.type.Money
	62,  // 77: tiering.EvaluateTierForActorRequest.options:type_name -> tiering.EvaluateTierForActorRequest.Options
	64,  // 78: tiering.EvaluateTierForActorResponse.status:type_name -> rpc.Status
	66,  // 79: tiering.EvaluateTierForActorResponse.evaluated_tier:type_name -> tiering.external.Tier
	72,  // 80: tiering.EvaluateTierForActorResponse.EvaluatedTierSatisfiedCriteriaOptionTypes:type_name -> tiering.enums.CriteriaOptionType
	66,  // 81: tiering.CheckIfActorIsEligibleForCashbackRewardRequest.tier:type_name -> tiering.external.Tier
	69,  // 82: tiering.CheckIfActorIsEligibleForCashbackRewardRequest.reward_month:type_name -> google.protobuf.Timestamp
	64,  // 83: tiering.CheckIfActorIsEligibleForCashbackRewardResponse.status:type_name -> rpc.Status
	66,  // 84: tiering.GetTierTimeRangesForActorRequest.tiers:type_name -> tiering.external.Tier
	69,  // 85: tiering.GetTierTimeRangesForActorRequest.filter_from:type_name -> google.protobuf.Timestamp
	64,  // 86: tiering.GetTierTimeRangesForActorResponse.status:type_name -> rpc.Status
	63,  // 87: tiering.GetTierTimeRangesForActorResponse.tier_time_ranges_map:type_name -> tiering.GetTierTimeRangesForActorResponse.TierTimeRangesMapEntry
	54,  // 88: tiering.TimeRanges.time_ranges:type_name -> tiering.TimeRange
	69,  // 89: tiering.TimeRange.from_time:type_name -> google.protobuf.Timestamp
	69,  // 90: tiering.TimeRange.to_time:type_name -> google.protobuf.Timestamp
	83,  // 91: tiering.GetActorScreenInteractionDetailsRequest.screen:type_name -> tiering.enums.TieringScreen
	4,   // 92: tiering.GetActorScreenInteractionDetailsRequest.request_type:type_name -> tiering.GetActorScreenInteractionDetailsRequest.RequestType
	64,  // 93: tiering.GetActorScreenInteractionDetailsResponse.status:type_name -> rpc.Status
	84,  // 94: tiering.GetActorScreenInteractionDetailsResponse.response:type_name -> tiering.enums.ActorScreenInteractionStatus
	5,   // 95: tiering.GetDropOffBottomSheetRequest.tiering_pitch_method:type_name -> tiering.GetDropOffBottomSheetRequest.TieringPitchMethod
	66,  // 96: tiering.GetDropOffBottomSheetRequest.tier_to_pitch:type_name -> tiering.external.Tier
	64,  // 97: tiering.GetDropOffBottomSheetResponse.status:type_name -> rpc.Status
	67,  // 98: tiering.GetDropOffBottomSheetResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	64,  // 99: tiering.GetAMBInfoResponse.status:type_name -> rpc.Status
	75,  // 100: tiering.GetAMBInfoResponse.current_amb:type_name -> google.type.Money
	75,  // 101: tiering.GetAMBInfoResponse.target_amb:type_name -> google.type.Money
	66,  // 102: tiering.GetAMBInfoResponse.current_tier:type_name -> tiering.external.Tier
	75,  // 103: tiering.GetAMBInfoResponse.shortfall_amount:type_name -> google.type.Money
	66,  // 104: tiering.IsUserEligibleForRewardsResponse.TierToEligibilityStatus.tier_name:type_name -> tiering.external.Tier
	53,  // 105: tiering.GetTierTimeRangesForActorResponse.TierTimeRangesMapEntry.value:type_name -> tiering.TimeRanges
	22,  // 106: tiering.Tiering.GetTierAtTime:input_type -> tiering.GetTierAtTimeRequest
	26,  // 107: tiering.Tiering.Upgrade:input_type -> tiering.UpgradeRequest
	28,  // 108: tiering.Tiering.ShowComponentToActor:input_type -> tiering.ShowComponentToActorRequest
	16,  // 109: tiering.Tiering.RecordComponentShownToActor:input_type -> tiering.RecordComponentShownToActorRequest
	15,  // 110: tiering.Tiering.GetTieringPitchV2:input_type -> tiering.GetTieringPitchV2Request
	36,  // 111: tiering.Tiering.GetDetailsForCx:input_type -> tiering.GetDetailsForCxRequest
	39,  // 112: tiering.Tiering.OverrideGracePeriod:input_type -> tiering.OverrideGracePeriodRequest
	41,  // 113: tiering.Tiering.OverrideCoolOffPeriod:input_type -> tiering.OverrideCoolOffPeriodRequest
	24,  // 114: tiering.Tiering.IsTieringEnabledForActor:input_type -> tiering.IsTieringEnabledForActorRequest
	18,  // 115: tiering.Tiering.GetTieringPitch:input_type -> tiering.GetTieringPitchRequest
	30,  // 116: tiering.Tiering.GetCriteriaForActor:input_type -> tiering.GetCriteriaForActorRequest
	32,  // 117: tiering.Tiering.IsActorEligibleForMovement:input_type -> tiering.IsActorEligibleForMovementRequest
	34,  // 118: tiering.Tiering.IsUserInGracePeriod:input_type -> tiering.IsUserInGracePeriodRequest
	85,  // 119: tiering.Tiering.FetchDynamicElements:input_type -> dynamic_elements.FetchDynamicElementsRequest
	86,  // 120: tiering.Tiering.DynamicElementCallback:input_type -> dynamic_elements.DynamicElementCallbackRequest
	43,  // 121: tiering.Tiering.GetConfigParams:input_type -> tiering.GetConfigParamsRequest
	47,  // 122: tiering.Tiering.EvaluateTierForActor:input_type -> tiering.EvaluateTierForActorRequest
	49,  // 123: tiering.Tiering.CheckIfActorIsEligibleForCashbackReward:input_type -> tiering.CheckIfActorIsEligibleForCashbackRewardRequest
	51,  // 124: tiering.Tiering.GetTierTimeRangesForActor:input_type -> tiering.GetTierTimeRangesForActorRequest
	55,  // 125: tiering.Tiering.GetActorScreenInteractionDetails:input_type -> tiering.GetActorScreenInteractionDetailsRequest
	12,  // 126: tiering.Tiering.GetActorDistinctTiers:input_type -> tiering.GetActorDistinctTiersRequest
	10,  // 127: tiering.Tiering.IsUserEligibleForRewards:input_type -> tiering.IsUserEligibleForRewardsRequest
	59,  // 128: tiering.Tiering.GetAMBInfo:input_type -> tiering.GetAMBInfoRequest
	87,  // 129: tiering.Tiering.FetchSherlockBanners:input_type -> cx.sherlock_banners.FetchSherlockBannersRequest
	8,   // 130: tiering.Tiering.GetCurrentTierForActor:input_type -> tiering.GetCurrentTierForActorRequest
	6,   // 131: tiering.Tiering.GetTrialDetails:input_type -> tiering.GetTrialDetailsRequest
	23,  // 132: tiering.Tiering.GetTierAtTime:output_type -> tiering.GetTierAtTimeResponse
	27,  // 133: tiering.Tiering.Upgrade:output_type -> tiering.UpgradeResponse
	29,  // 134: tiering.Tiering.ShowComponentToActor:output_type -> tiering.ShowComponentToActorResponse
	17,  // 135: tiering.Tiering.RecordComponentShownToActor:output_type -> tiering.RecordComponentShownToActorResponse
	14,  // 136: tiering.Tiering.GetTieringPitchV2:output_type -> tiering.GetTieringPitchV2Response
	37,  // 137: tiering.Tiering.GetDetailsForCx:output_type -> tiering.GetDetailsForCxResponse
	40,  // 138: tiering.Tiering.OverrideGracePeriod:output_type -> tiering.OverrideGracePeriodResponse
	42,  // 139: tiering.Tiering.OverrideCoolOffPeriod:output_type -> tiering.OverrideCoolOffPeriodResponse
	25,  // 140: tiering.Tiering.IsTieringEnabledForActor:output_type -> tiering.IsTieringEnabledForActorResponse
	19,  // 141: tiering.Tiering.GetTieringPitch:output_type -> tiering.GetTieringPitchResponse
	31,  // 142: tiering.Tiering.GetCriteriaForActor:output_type -> tiering.GetCriteriaForActorResponse
	33,  // 143: tiering.Tiering.IsActorEligibleForMovement:output_type -> tiering.IsActorEligibleForMovementResponse
	35,  // 144: tiering.Tiering.IsUserInGracePeriod:output_type -> tiering.IsUserInGracePeriodResponse
	88,  // 145: tiering.Tiering.FetchDynamicElements:output_type -> dynamic_elements.FetchDynamicElementsResponse
	89,  // 146: tiering.Tiering.DynamicElementCallback:output_type -> dynamic_elements.DynamicElementCallbackResponse
	44,  // 147: tiering.Tiering.GetConfigParams:output_type -> tiering.GetConfigParamsResponse
	48,  // 148: tiering.Tiering.EvaluateTierForActor:output_type -> tiering.EvaluateTierForActorResponse
	50,  // 149: tiering.Tiering.CheckIfActorIsEligibleForCashbackReward:output_type -> tiering.CheckIfActorIsEligibleForCashbackRewardResponse
	52,  // 150: tiering.Tiering.GetTierTimeRangesForActor:output_type -> tiering.GetTierTimeRangesForActorResponse
	56,  // 151: tiering.Tiering.GetActorScreenInteractionDetails:output_type -> tiering.GetActorScreenInteractionDetailsResponse
	13,  // 152: tiering.Tiering.GetActorDistinctTiers:output_type -> tiering.GetActorDistinctTiersResponse
	11,  // 153: tiering.Tiering.IsUserEligibleForRewards:output_type -> tiering.IsUserEligibleForRewardsResponse
	60,  // 154: tiering.Tiering.GetAMBInfo:output_type -> tiering.GetAMBInfoResponse
	90,  // 155: tiering.Tiering.FetchSherlockBanners:output_type -> cx.sherlock_banners.FetchSherlockBannersResponse
	9,   // 156: tiering.Tiering.GetCurrentTierForActor:output_type -> tiering.GetCurrentTierForActorResponse
	7,   // 157: tiering.Tiering.GetTrialDetails:output_type -> tiering.GetTrialDetailsResponse
	132, // [132:158] is the sub-list for method output_type
	106, // [106:132] is the sub-list for method input_type
	106, // [106:106] is the sub-list for extension type_name
	106, // [106:106] is the sub-list for extension extendee
	0,   // [0:106] is the sub-list for field type_name
}

func init() { file_api_tiering_service_proto_init() }
func file_api_tiering_service_proto_init() {
	if File_api_tiering_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_tiering_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrialDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrialDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrentTierForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrentTierForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsUserEligibleForRewardsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsUserEligibleForRewardsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorDistinctTiersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorDistinctTiersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTieringPitchV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTieringPitchV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordComponentShownToActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordComponentShownToActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTieringPitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTieringPitchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TieringPitchAddFundsDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TieringPitchProfileSectionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierAtTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierAtTimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsTieringEnabledForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsTieringEnabledForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShowComponentToActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShowComponentToActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCriteriaForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCriteriaForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsActorEligibleForMovementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsActorEligibleForMovementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsUserInGracePeriodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsUserInGracePeriodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailsForCxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailsForCxResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmbDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverrideGracePeriodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverrideGracePeriodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverrideCoolOffPeriodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverrideCoolOffPeriodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigParamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigParamsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CriteriaSegmentExclusionConfigs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegularTierConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluateTierForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluateTierForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIfActorIsEligibleForCashbackRewardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIfActorIsEligibleForCashbackRewardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierTimeRangesForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTierTimeRangesForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRanges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorScreenInteractionDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorScreenInteractionDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDropOffBottomSheetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDropOffBottomSheetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAMBInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAMBInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsUserEligibleForRewardsResponse_TierToEligibilityStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluateTierForActorRequest_Options); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_tiering_service_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*GetTieringPitchResponse_AddFundsDetails)(nil),
		(*GetTieringPitchResponse_ProfileSectionDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_tiering_service_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   58,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_tiering_service_proto_goTypes,
		DependencyIndexes: file_api_tiering_service_proto_depIdxs,
		EnumInfos:         file_api_tiering_service_proto_enumTypes,
		MessageInfos:      file_api_tiering_service_proto_msgTypes,
	}.Build()
	File_api_tiering_service_proto = out.File
	file_api_tiering_service_proto_rawDesc = nil
	file_api_tiering_service_proto_goTypes = nil
	file_api_tiering_service_proto_depIdxs = nil
}
