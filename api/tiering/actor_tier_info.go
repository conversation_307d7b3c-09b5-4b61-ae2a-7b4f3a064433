package tiering

import "time"

// IsTrialPeriodActive checks if the trial is currently active based on the start and end times.
// update api/tiering/external/external.go if this function is modified.
func (x *TrialDetails) IsTrialPeriodActive() bool {
	if x == nil {
		return false
	}

	if x.GetTrialStartTime() == nil || x.GetTrialEndTime() == nil {
		return false
	}

	return x.GetTrialStartTime().AsTime().Before(time.Now()) && x.GetTrialEndTime().AsTime().After(time.Now())
}
