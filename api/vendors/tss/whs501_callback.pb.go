// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/tss/whs501_callback.proto

package tss

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// WHS501 Webhook Messages
// Documentation Link: https://drive.google.com/file/d/1Zq3wmcJseneGk2FZfHTeU59vQlw7elqX/
type ProcessWHS501CallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Event type identifier, should be "WHS501" for this webhook
	EventType string `protobuf:"bytes,1,opt,name=event_type,json=EventType,proto3" json:"event_type,omitempty"`
	// Case details for initial screening
	RequestData *WHS501RequestData `protobuf:"bytes,2,opt,name=request_data,json=RequestData,proto3" json:"request_data,omitempty"`
}

func (x *ProcessWHS501CallbackRequest) Reset() {
	*x = ProcessWHS501CallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_whs501_callback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessWHS501CallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessWHS501CallbackRequest) ProtoMessage() {}

func (x *ProcessWHS501CallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_whs501_callback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessWHS501CallbackRequest.ProtoReflect.Descriptor instead.
func (*ProcessWHS501CallbackRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_whs501_callback_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessWHS501CallbackRequest) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *ProcessWHS501CallbackRequest) GetRequestData() *WHS501RequestData {
	if x != nil {
		return x.RequestData
	}
	return nil
}

type WHS501RequestData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId                string `protobuf:"bytes,1,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	CaseId                   string `protobuf:"bytes,2,opt,name=case_id,json=CaseId,proto3" json:"case_id,omitempty"`
	SourceSystemCustomerCode string `protobuf:"bytes,3,opt,name=source_system_customer_code,json=SourceSystemCustomerCode,proto3" json:"source_system_customer_code,omitempty"`
	ApplicationRefNumber     string `protobuf:"bytes,4,opt,name=application_ref_number,json=ApplicationRefNumber,proto3" json:"application_ref_number,omitempty"`
	// Onboarding decision for initial screening cases. Possible values: Proceed/Decline
	OnboardingDecision string `protobuf:"bytes,5,opt,name=onboarding_decision,json=OnboardingDecision,proto3" json:"onboarding_decision,omitempty"`
	// Alert count where no match is marked
	NoMatchCount string `protobuf:"bytes,6,opt,name=no_match_count,json=NoMatchCount,proto3" json:"no_match_count,omitempty"`
	// Alert count where true match is marked
	TrueMatchCount string `protobuf:"bytes,7,opt,name=true_match_count,json=TrueMatchCount,proto3" json:"true_match_count,omitempty"`
	CaseStage      string `protobuf:"bytes,8,opt,name=case_stage,json=CaseStage,proto3" json:"case_stage,omitempty"`
	// Person who closed the case
	CaseClosedBy    string              `protobuf:"bytes,9,opt,name=case_closed_by,json=CaseClosedBy,proto3" json:"case_closed_by,omitempty"`
	CaseClosureDate string              `protobuf:"bytes,10,opt,name=case_closure_date,json=CaseClosureDate,proto3" json:"case_closure_date,omitempty"`
	FinalRemarks    string              `protobuf:"bytes,11,opt,name=final_remarks,json=FinalRemarks,proto3" json:"final_remarks,omitempty"`
	CaseActions     []*WHS501CaseAction `protobuf:"bytes,12,rep,name=case_actions,json=CaseActions,proto3" json:"case_actions,omitempty"`
}

func (x *WHS501RequestData) Reset() {
	*x = WHS501RequestData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_whs501_callback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WHS501RequestData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WHS501RequestData) ProtoMessage() {}

func (x *WHS501RequestData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_whs501_callback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WHS501RequestData.ProtoReflect.Descriptor instead.
func (*WHS501RequestData) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_whs501_callback_proto_rawDescGZIP(), []int{1}
}

func (x *WHS501RequestData) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *WHS501RequestData) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *WHS501RequestData) GetSourceSystemCustomerCode() string {
	if x != nil {
		return x.SourceSystemCustomerCode
	}
	return ""
}

func (x *WHS501RequestData) GetApplicationRefNumber() string {
	if x != nil {
		return x.ApplicationRefNumber
	}
	return ""
}

func (x *WHS501RequestData) GetOnboardingDecision() string {
	if x != nil {
		return x.OnboardingDecision
	}
	return ""
}

func (x *WHS501RequestData) GetNoMatchCount() string {
	if x != nil {
		return x.NoMatchCount
	}
	return ""
}

func (x *WHS501RequestData) GetTrueMatchCount() string {
	if x != nil {
		return x.TrueMatchCount
	}
	return ""
}

func (x *WHS501RequestData) GetCaseStage() string {
	if x != nil {
		return x.CaseStage
	}
	return ""
}

func (x *WHS501RequestData) GetCaseClosedBy() string {
	if x != nil {
		return x.CaseClosedBy
	}
	return ""
}

func (x *WHS501RequestData) GetCaseClosureDate() string {
	if x != nil {
		return x.CaseClosureDate
	}
	return ""
}

func (x *WHS501RequestData) GetFinalRemarks() string {
	if x != nil {
		return x.FinalRemarks
	}
	return ""
}

func (x *WHS501RequestData) GetCaseActions() []*WHS501CaseAction {
	if x != nil {
		return x.CaseActions
	}
	return nil
}

type WHS501CaseAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Assignee name
	Name string `protobuf:"bytes,1,opt,name=name,json=Name,proto3" json:"name,omitempty"`
	// Date when the action was taken
	Date   string `protobuf:"bytes,2,opt,name=date,json=Date,proto3" json:"date,omitempty"`
	Action string `protobuf:"bytes,3,opt,name=action,json=Action,proto3" json:"action,omitempty"`
}

func (x *WHS501CaseAction) Reset() {
	*x = WHS501CaseAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_whs501_callback_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WHS501CaseAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WHS501CaseAction) ProtoMessage() {}

func (x *WHS501CaseAction) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_whs501_callback_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WHS501CaseAction.ProtoReflect.Descriptor instead.
func (*WHS501CaseAction) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_whs501_callback_proto_rawDescGZIP(), []int{2}
}

func (x *WHS501CaseAction) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WHS501CaseAction) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *WHS501CaseAction) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type ProcessWHS501CallbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ProcessWHS501CallbackResponse) Reset() {
	*x = ProcessWHS501CallbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_whs501_callback_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessWHS501CallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessWHS501CallbackResponse) ProtoMessage() {}

func (x *ProcessWHS501CallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_whs501_callback_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessWHS501CallbackResponse.ProtoReflect.Descriptor instead.
func (*ProcessWHS501CallbackResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_whs501_callback_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessWHS501CallbackResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_api_vendors_tss_whs501_callback_proto protoreflect.FileDescriptor

var file_api_vendors_tss_whs501_callback_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73,
	0x73, 0x2f, 0x77, 0x68, 0x73, 0x35, 0x30, 0x31, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x74, 0x73, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x99, 0x04, 0x0a, 0x11, 0x57, 0x48, 0x53, 0x35,
	0x30, 0x31, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x43,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x6e,
	0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x4e, 0x6f, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x72, 0x75, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x54, 0x72, 0x75,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x43, 0x61, 0x73, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x2a, 0x0a, 0x11, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x43, 0x61, 0x73,
	0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x73, 0x12, 0x40, 0x0a, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x43, 0x61, 0x73, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x43, 0x61, 0x73, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x52, 0x0a, 0x10, 0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x43, 0x61, 0x73,
	0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x37, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74,
	0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_tss_whs501_callback_proto_rawDescOnce sync.Once
	file_api_vendors_tss_whs501_callback_proto_rawDescData = file_api_vendors_tss_whs501_callback_proto_rawDesc
)

func file_api_vendors_tss_whs501_callback_proto_rawDescGZIP() []byte {
	file_api_vendors_tss_whs501_callback_proto_rawDescOnce.Do(func() {
		file_api_vendors_tss_whs501_callback_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_tss_whs501_callback_proto_rawDescData)
	})
	return file_api_vendors_tss_whs501_callback_proto_rawDescData
}

var file_api_vendors_tss_whs501_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_vendors_tss_whs501_callback_proto_goTypes = []interface{}{
	(*ProcessWHS501CallbackRequest)(nil),  // 0: vendors.tss.ProcessWHS501CallbackRequest
	(*WHS501RequestData)(nil),             // 1: vendors.tss.WHS501RequestData
	(*WHS501CaseAction)(nil),              // 2: vendors.tss.WHS501CaseAction
	(*ProcessWHS501CallbackResponse)(nil), // 3: vendors.tss.ProcessWHS501CallbackResponse
}
var file_api_vendors_tss_whs501_callback_proto_depIdxs = []int32{
	1, // 0: vendors.tss.ProcessWHS501CallbackRequest.request_data:type_name -> vendors.tss.WHS501RequestData
	2, // 1: vendors.tss.WHS501RequestData.case_actions:type_name -> vendors.tss.WHS501CaseAction
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_vendors_tss_whs501_callback_proto_init() }
func file_api_vendors_tss_whs501_callback_proto_init() {
	if File_api_vendors_tss_whs501_callback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_tss_whs501_callback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessWHS501CallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_whs501_callback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WHS501RequestData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_whs501_callback_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WHS501CaseAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_whs501_callback_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessWHS501CallbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_tss_whs501_callback_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_tss_whs501_callback_proto_goTypes,
		DependencyIndexes: file_api_vendors_tss_whs501_callback_proto_depIdxs,
		MessageInfos:      file_api_vendors_tss_whs501_callback_proto_msgTypes,
	}.Build()
	File_api_vendors_tss_whs501_callback_proto = out.File
	file_api_vendors_tss_whs501_callback_proto_rawDesc = nil
	file_api_vendors_tss_whs501_callback_proto_goTypes = nil
	file_api_vendors_tss_whs501_callback_proto_depIdxs = nil
}
