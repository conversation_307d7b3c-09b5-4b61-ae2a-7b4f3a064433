// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/tss/whs501_callback.proto

package tss

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessWHS501CallbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessWHS501CallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessWHS501CallbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessWHS501CallbackRequestMultiError, or nil if none found.
func (m *ProcessWHS501CallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessWHS501CallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EventType

	if all {
		switch v := interface{}(m.GetRequestData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessWHS501CallbackRequestValidationError{
					field:  "RequestData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessWHS501CallbackRequestValidationError{
					field:  "RequestData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessWHS501CallbackRequestValidationError{
				field:  "RequestData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessWHS501CallbackRequestMultiError(errors)
	}

	return nil
}

// ProcessWHS501CallbackRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessWHS501CallbackRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessWHS501CallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessWHS501CallbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessWHS501CallbackRequestMultiError) AllErrors() []error { return m }

// ProcessWHS501CallbackRequestValidationError is the validation error returned
// by ProcessWHS501CallbackRequest.Validate if the designated constraints
// aren't met.
type ProcessWHS501CallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessWHS501CallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessWHS501CallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessWHS501CallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessWHS501CallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessWHS501CallbackRequestValidationError) ErrorName() string {
	return "ProcessWHS501CallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessWHS501CallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessWHS501CallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessWHS501CallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessWHS501CallbackRequestValidationError{}

// Validate checks the field values on WHS501RequestData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WHS501RequestData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WHS501RequestData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WHS501RequestDataMultiError, or nil if none found.
func (m *WHS501RequestData) ValidateAll() error {
	return m.validate(true)
}

func (m *WHS501RequestData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for CaseId

	// no validation rules for SourceSystemCustomerCode

	// no validation rules for ApplicationRefNumber

	// no validation rules for OnboardingDecision

	// no validation rules for NoMatchCount

	// no validation rules for TrueMatchCount

	// no validation rules for CaseStage

	// no validation rules for CaseClosedBy

	// no validation rules for CaseClosureDate

	// no validation rules for FinalRemarks

	for idx, item := range m.GetCaseActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WHS501RequestDataValidationError{
						field:  fmt.Sprintf("CaseActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WHS501RequestDataValidationError{
						field:  fmt.Sprintf("CaseActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WHS501RequestDataValidationError{
					field:  fmt.Sprintf("CaseActions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WHS501RequestDataMultiError(errors)
	}

	return nil
}

// WHS501RequestDataMultiError is an error wrapping multiple validation errors
// returned by WHS501RequestData.ValidateAll() if the designated constraints
// aren't met.
type WHS501RequestDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WHS501RequestDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WHS501RequestDataMultiError) AllErrors() []error { return m }

// WHS501RequestDataValidationError is the validation error returned by
// WHS501RequestData.Validate if the designated constraints aren't met.
type WHS501RequestDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WHS501RequestDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WHS501RequestDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WHS501RequestDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WHS501RequestDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WHS501RequestDataValidationError) ErrorName() string {
	return "WHS501RequestDataValidationError"
}

// Error satisfies the builtin error interface
func (e WHS501RequestDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWHS501RequestData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WHS501RequestDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WHS501RequestDataValidationError{}

// Validate checks the field values on WHS501CaseAction with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WHS501CaseAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WHS501CaseAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WHS501CaseActionMultiError, or nil if none found.
func (m *WHS501CaseAction) ValidateAll() error {
	return m.validate(true)
}

func (m *WHS501CaseAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Date

	// no validation rules for Action

	if len(errors) > 0 {
		return WHS501CaseActionMultiError(errors)
	}

	return nil
}

// WHS501CaseActionMultiError is an error wrapping multiple validation errors
// returned by WHS501CaseAction.ValidateAll() if the designated constraints
// aren't met.
type WHS501CaseActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WHS501CaseActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WHS501CaseActionMultiError) AllErrors() []error { return m }

// WHS501CaseActionValidationError is the validation error returned by
// WHS501CaseAction.Validate if the designated constraints aren't met.
type WHS501CaseActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WHS501CaseActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WHS501CaseActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WHS501CaseActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WHS501CaseActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WHS501CaseActionValidationError) ErrorName() string { return "WHS501CaseActionValidationError" }

// Error satisfies the builtin error interface
func (e WHS501CaseActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWHS501CaseAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WHS501CaseActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WHS501CaseActionValidationError{}

// Validate checks the field values on ProcessWHS501CallbackResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessWHS501CallbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessWHS501CallbackResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessWHS501CallbackResponseMultiError, or nil if none found.
func (m *ProcessWHS501CallbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessWHS501CallbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if len(errors) > 0 {
		return ProcessWHS501CallbackResponseMultiError(errors)
	}

	return nil
}

// ProcessWHS501CallbackResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessWHS501CallbackResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessWHS501CallbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessWHS501CallbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessWHS501CallbackResponseMultiError) AllErrors() []error { return m }

// ProcessWHS501CallbackResponseValidationError is the validation error
// returned by ProcessWHS501CallbackResponse.Validate if the designated
// constraints aren't met.
type ProcessWHS501CallbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessWHS501CallbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessWHS501CallbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessWHS501CallbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessWHS501CallbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessWHS501CallbackResponseValidationError) ErrorName() string {
	return "ProcessWHS501CallbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessWHS501CallbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessWHS501CallbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessWHS501CallbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessWHS501CallbackResponseValidationError{}
