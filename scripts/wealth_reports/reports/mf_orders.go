package reports

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/investment/mutualfund/dao"
)

// MFOrdersAuditor audits mutual fund orders in non-terminal state
type MFOrdersAuditor struct {
	orderDao dao.OrderDao
}

// NewMFOrdersAuditor creates a new MFOrdersAuditor instance
func NewMFOrdersAuditor(orderDao dao.OrderDao) *MFOrdersAuditor {
	return &MFOrdersAuditor{
		orderDao: orderDao,
	}
}

func withCreatedBefore(t time.Time) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("created_at < ?", t)
	})
}

func withCreatedAfter(t time.Time) storagev2.FilterOption {
	return storagev2.NewFuncFilterOption(func(db *gorm.DB) *gorm.DB {
		return db.Where("created_at >= ?", t)
	})
}

// calculateBusinessHoursCutoff returns a time that is the given number of business hours before the reference time
// excluding weekends (Saturday and Sunday)
// Holidays are not considered
func calculateBusinessHoursCutoff(referenceTime time.Time, hoursToSubtract int) time.Time {
	currentTime := referenceTime
	hoursRemaining := hoursToSubtract

	for hoursRemaining > 0 {
		currentTime = currentTime.Add(-1 * time.Hour)
		// Skip counting hours during weekends
		if currentTime.Weekday() != time.Saturday && currentTime.Weekday() != time.Sunday {
			hoursRemaining--
		}
	}
	return time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, datetime.UTC)
}

// Audit checks for orders in non-terminal state for too long
func (a *MFOrdersAuditor) Audit(ctx context.Context, now time.Time) (bool, *ReportData, error) {
	cutoffTime := calculateBusinessHoursCutoff(now, 48)
	startDate := time.Date(2025, 7, 1, 0, 0, 0, 0, datetime.UTC)

	logger.InfoNoCtx("Finding orders in non-terminal state for more than 2 business days",
		zap.Time("cutoff_time", cutoffTime),
		zap.Time("start_date", startDate))

	orders, err := a.orderDao.GetNonTerminalOrdersByFilterOptions(ctx,
		withCreatedBefore(cutoffTime),
		withCreatedAfter(startDate))
	if err != nil {
		logger.Error(ctx, "Failed to fetch orders", zap.Error(err))
		return false, nil, fmt.Errorf("failed to fetch orders: %w", err)
	}

	if len(orders) == 0 {
		return true, &ReportData{
			Title: "Mutual Fund Orders Status Check",
			Text:  fmt.Sprintf("✅ No mutual fund orders in non-terminal state since %s", cutoffTime.Format(time.RFC3339)),
		}, nil
	}

	// Collect stats
	orderTypeStats := make(map[string]int)
	orderStatusStats := make(map[string]int)
	csvRows := [][]string{
		{"Order ID", "Vendor Order ID", "Actor ID", "Order Type", "Order Status", "Last Updated", "Failure Debug Reason"},
	}

	for _, o := range orders {
		csvRows = append(csvRows, []string{
			o.Id,
			o.VendorOrderId,
			o.ActorId,
			o.OrderType.String(),
			o.OrderStatus.String(),
			o.UpdatedAt.AsTime().Format(time.RFC3339),
			o.FailureDebugReason,
		})
		orderTypeStats[o.OrderType.String()]++
		orderStatusStats[o.OrderStatus.String()]++
	}

	// Create summary message
	summary := fmt.Sprintf("⚠️ *MF Orders in Non-Terminal State (%s)*\nTotal Orders: %d\n\n*By Order Type:*\n", cutoffTime.Format(time.RFC3339), len(orders))
	for orderType, count := range orderTypeStats {
		summary += fmt.Sprintf("• %s: %d\n", orderType, count)
	}
	summary += "\n*By Status:*\n"
	for status, count := range orderStatusStats {
		summary += fmt.Sprintf("• %s: %d\n", status, count)
	}

	return false, &ReportData{
		Title:    "Mutual Fund Orders in Non-Terminal State",
		Text:     summary,
		CsvRows:  csvRows,
		filename: "mfNonTerminalOrdersReport",
	}, nil
}
