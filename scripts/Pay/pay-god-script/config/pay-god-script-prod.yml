Environment: "prod"

Logging:
  Level: "INFO"
  Development: false

TemporalSecrets:
  TemporalCodecAesKeySecret: "prod/temporal/codec-encryption-key"

PrestoConfigWrapper:
  PrestoSecretsPath: "prod/banking/presto"
  PrestoConfig:
    Host: "presto-master-dp-ext.data-prod.epifi.in"
    Port: 8889
    Catalog: "hive"
    Schema: "federal_epifi"

DataExport:
  PrestoSecrets:
    PrestoSecretKey: "prod/banking/presto"


CurrentAccountStatementGenParams:
  SenderEmail: "<EMAIL>"
  SenderName: "Current Account Statement Job"
  UsecaseToReceiverDetailsMap:
    "STOCK_GUARDIAN_TSP":
      # The account must be a federal bank account
      AccountNumbers:
        - "**************"
      ReceiverDetails:
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Mohit Patni"
    "ADD_FUNDS_POOL_ACCOUNT":
      # The account must be a federal bank account
      AccountNumbers:
        - "**************"
      ReceiverDetails:
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Nahid <PERSON>"
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Rohan"
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Saurabh K"
    "USER_SAVINGS_ACCOUNT":
      AccountNumbers:
        - "**************"
      ReceiverDetails:
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Nahid Kalam"
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Rohan"



RiskyVPABucket: "epifi-prod-pay-scripts-data"

PgProgramToAuthSecretMap:
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "prod/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-stockguardian_MbZRPgHhafW":
    AuthParam: "prod/vendorgateway/razorpay-stock-guardian-loans-api-key"

EpifiDb:
  DbType: "CRDB"
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "SILENT"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

UsecaseDbConfigMap:
  EPIFI_TECH:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    DbType: "CRDB"
    AppName: "order"
    StatementTimeout: 15s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      UseInsecureLogInProd: false
      SlowQueryLogThreshold: 200ms

  LIQUILOANS_PL:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    DbType: "CRDB"
    AppName: "order"
    StatementTimeout: 10s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 4
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false
  STOCK_GUARDIAN_TSP:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    DBType: "CRDB"
    StatementTimeout: 10s
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: false
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 4
    MaxConnTtl: "30m"
    AppName: "order"
    GormV2:
      LogLevelGormV2: "SILENT"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

FetchAndCreateFailedEnachTransactionPublisher:
  QueueName: "prod-recurringpayment-failed-enach-transaction-queue"

TieringDb:
  DbType: "PGDB"
  AppName: "tiering"
  StatementTimeout: 10s
  Name: "tiering"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/tiering_dev_user"
  GormV2:
    LogLevelGormV2: "SILENT"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false
