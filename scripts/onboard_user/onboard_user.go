package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"context"
	"database/sql"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"

	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	authPb "github.com/epifi/gamma/api/auth"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/scripts/crud/userdata"
	"github.com/epifi/gamma/scripts/crud/waitlist"
	"github.com/epifi/gamma/scripts/onboard_user/config"
	"github.com/epifi/gamma/testing/integration/app"
)

var (
	phoneNo                                                                       = flag.String("phoneno", "", "Custom phone number to onboard user")
	emailId                                                                       = flag.String("emailid", "", "Custom email ID to onboard user")
	pan                                                                           = flag.String("pan", "", "Custom PAN to onboard user")
	addMoney                                                                      = flag.String("addmoney", "", "Custom amount to be added in add money stages")
	kycLevel                                                                      = flag.String("kyclevel", "", "Custom KYC level")
	finiteCode                                                                    = flag.String("finitecode", "", "Claim finite code")
	deviceId                                                                      = flag.String("deviceid", "", "Custom Device ID to avoid Re-Onboarding")
	apoUnspecified                                                                = flag.String("apounspecified", "true", "Flag to mark APO unspecified")
	authClient                                                                    authPb.AuthClient
	epifiDbV2, simulatorDb, timelineDb, paymentInstrumentDb, actorDb              *gormV2.DB
	epifiSqlDb, simulatorSqlDb, timelineSqlDb, actorSqlDb, paymentInstrumentSqlDb *sql.DB
	conf                                                                          *config.Config
	userRedisCacheStorage, actorRedisCacheStorage, piRedisCacheStorage            *cache.RedisCacheStorage
	savingsLedgerReconCacheStorage, timelineRedisCacheStorage                     *cache.RedisCacheStorage
	savingsRedisCacheStorage, devRegRedisCacheStorage                             *cache.RedisCacheStorage
	err                                                                           error
	moolah                                                                        int64
	livVid                                                                        []byte
	phNum                                                                         *commontypes.PhoneNumber
)

func main() {
	flag.Parse()
	env, errEnv := cfg.GetEnvironment()
	if errEnv != nil {
		panic(errEnv)
	}
	logger.Init(env)
	conf, err = config.Load()
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to load config: %v", err))
	}

	if simulatorDb, err = storageV2.NewGormDB(conf.SimulatorDb); err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to simulator db: %v", err))
	}
	if epifiDbV2, err = storageV2.NewGormDB(conf.EpifiDb); err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to epifi db: %v", err))
	}
	if timelineDb, err = storageV2.NewGormDB(conf.TimelineDb); err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to timeline db: %v", err))
	}
	if actorDb, err = storageV2.NewGormDB(conf.ActorDb); err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to timeline db: %v", err))
	}

	if paymentInstrumentDb, err = storageV2.NewGormDB(conf.PaymentInstrumentDb); err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to payment instrument db: %v", err))
	}

	paymentInstrumentSqlDb, err = paymentInstrumentDb.DB()
	if err != nil {
		logger.Panic("failed to get payment instrument sql DB", zap.Error(err))
	}

	actorSqlDb, err = actorDb.DB()
	if err != nil {
		logger.Panic("failed to get actor sql DB", zap.Error(err))
	}

	timelineSqlDb, err = timelineDb.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	simulatorSqlDb, err = simulatorDb.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	epifiSqlDb, err = epifiDbV2.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}

	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	authClient = authPb.NewAuthClient(authConn)
	defer func() {
		_ = simulatorSqlDb.Close()
		_ = epifiSqlDb.Close()
		_ = timelineSqlDb.Close()
		_ = actorSqlDb.Close()
		_ = paymentInstrumentSqlDb.Close()
	}()
	initResources()

	onboardUser()
	logger.InfoNoCtx("Ended Onboarding User")

}

func initResources() {
	userRedisClient := storage.NewRedisClient(conf.UserRedis.Options, conf.UserRedis.IsSecure, false)
	userRedisCacheStorage = cache.NewRedisCacheStorage(userRedisClient)

	actorRedisClient := storage.NewRedisClient(conf.ActorRedis.Options, conf.ActorRedis.IsSecure, false)
	actorRedisCacheStorage = cache.NewRedisCacheStorage(actorRedisClient)

	piRedisClient := storage.NewRedisClient(conf.PiRedis.Options, conf.PiRedis.IsSecure, false)
	piRedisCacheStorage = cache.NewRedisCacheStorage(piRedisClient)

	savingsLedgerReconRedisClient := storage.NewRedisClient(conf.SavingsLedgerReconRedis.Options, conf.SavingsLedgerReconRedis.IsSecure, false)
	savingsLedgerReconCacheStorage = cache.NewRedisCacheStorage(savingsLedgerReconRedisClient)

	timelineRedisClient := storage.NewRedisClient(conf.TimelineRedis.Options, conf.TimelineRedis.IsSecure, false)
	timelineRedisCacheStorage = cache.NewRedisCacheStorage(timelineRedisClient)

	savingsRedisClient := storage.NewRedisClient(conf.SavingsRedis.Options, conf.SavingsRedis.IsSecure, false)
	savingsRedisCacheStorage = cache.NewRedisCacheStorage(savingsRedisClient)

	devRegRedisClient := storage.NewRedisClientFromConfig(conf.DevRegRedis, false)
	devRegRedisCacheStorage = cache.NewRedisCacheStorage(devRegRedisClient)
}

type Assert struct {
	*sync.Mutex
	errs []error
}

func newAssert() *Assert {
	return &Assert{Mutex: &sync.Mutex{}}
}

func (a *Assert) Errorf(format string, args ...interface{}) {
	a.Lock()
	defer a.Unlock()
	a.errs = append(a.errs, fmt.Errorf(format, args...))
}

func (a *Assert) FailNow() {
	for _, er := range a.errs {
		fmt.Println("error in onboarding user", er)
	}
	logger.Fatal("Failed to onboard user")
}

// nolint:funlen
func onboardUser() {
	a := require.New(newAssert())
	ctx := context.Background()
	if *phoneNo != "" {
		logger.Info(ctx, "phone number given", zap.String(logger.STATUS_CODE, *phoneNo))
		if phNum, err = commontypes.ParsePhoneNumber(*phoneNo); err != nil {
			logger.Error(ctx, "error in parsing phone number from string", zap.Error(err))
			a.Error(err)
		}
		phNum.CountryCode = 91
	} else {
		logger.Error(ctx, "phone number is mandatory")
		os.Exit(1)
	}
	if *emailId == "" {
		logger.Error(ctx, "email id is mandatory")
		os.Exit(1)
	} else {
		// Delete if a different user exists by email id also.
		var phoneNum string
		if err = epifiDbV2.Table("users").Select("computed_phone_number").Where("profile->>'email' = ? and deleted_at_unix = 0", *emailId).Take(&phoneNum).Error; err != nil {
			logger.Error(ctx, "error in fetching user by email id", zap.Error(err))
		}
		logger.Debug(ctx, "phone number by email id", zap.String(logger.STATUS_CODE, phoneNum))
		if phoneNum != "" {
			var (
				phNum2 *commontypes.PhoneNumber
			)
			if phNum2, err = commontypes.ParsePhoneNumber(phoneNum); err != nil {
				logger.Error(ctx, "error in parsing phone number from string", zap.Error(err))
				a.Error(err)
			}
			phNum2.CountryCode = 91
			if !strings.EqualFold(phNum.ToStringInMobileFormat(), phNum2.ToStringInMobileFormat()) {
				userdata.DeleteUserData(epifiDbV2, actorDb, timelineDb, paymentInstrumentDb, simulatorDb, phNum2, userRedisCacheStorage, actorRedisCacheStorage, piRedisCacheStorage, savingsLedgerReconCacheStorage, timelineRedisCacheStorage, savingsRedisCacheStorage, devRegRedisCacheStorage, authClient)
				// delete data for the waitlist user with this phone number
				// if the user doesn't exist it'll log an error which can be ignored.
				if err = waitlist.DeleteUser(ctx, epifiDbV2, phNum2); err != nil {
					logger.Error(ctx, "Error to delete waitlist user data", zap.Error(err))
				}
			}
		}
	}
	// delete data for the user with this phone number
	// if the user doesn't exist it'll log an error which can be ignored
	userdata.DeleteUserData(epifiDbV2, actorDb, timelineDb, paymentInstrumentDb, simulatorDb, phNum, userRedisCacheStorage, actorRedisCacheStorage, piRedisCacheStorage, savingsLedgerReconCacheStorage, timelineRedisCacheStorage, savingsRedisCacheStorage, devRegRedisCacheStorage, authClient)
	// delete data for the waitlist user with this phone number
	// if the user doesn't exist it'll log an error which can be ignored.
	if err = waitlist.DeleteUser(ctx, epifiDbV2, phNum); err != nil {
		logger.Error(ctx, "Error to delete waitlist user data", zap.Error(err))
	}
	dbConn := map[string]*gormV2.DB{
		config.EpifiDb:     epifiDbV2,
		config.SimulatorDb: simulatorDb,
	}
	assert := newAssert()
	dep, def := app.NewOnbDeps(ctx, require.New(assert), dbConn)
	defer func() {
		_ = logger.Log.Sync()
		def()
	}()
	currPath, err := os.Executable()
	dep.Assert.NoError(err, "error while fetching path of executable")
	livenessVideoFilePath := filepath.Clean(filepath.Join(filepath.Dir(currPath), conf.RelativeLivenessVideoFilePath))
	logger.Info(ctx, fmt.Sprintf("current liveness file path: %v", livenessVideoFilePath))
	livVid, err = os.ReadFile(livenessVideoFilePath)
	dep.Assert.NoError(err, "Failed to read video file")
	devId := ""
	if *deviceId != "RANDOMDEVICEID" && *deviceId != "" {
		logger.Info(ctx, "device id given", zap.String(logger.STATUS_CODE, *deviceId))
		devId = *deviceId
	}

	var opts []app.OnboardUserPropertyOptions
	opts = append(opts, app.OverrideUserParamsOption(app.OverrideUserParams{
		PhoneNoPref:   int(phNum.GetNationalNumber()),
		MailIdPref:    *emailId,
		Pan:           *pan,
		DeviceId:      devId,
		LivenessVideo: livVid,
		LatLong:       app.RandIndiaLatLng(),
	}))
	if *finiteCode != "NOFINITECODE" && *finiteCode != "" {
		logger.Info(ctx, "finite code given", zap.String(logger.STATUS_CODE, *finiteCode))
		opts = append(opts, app.FiniteCodeOption(*finiteCode))
	}
	switch *kycLevel {
	case "MIN_KYC":
		opts = append(opts, app.KycLevelOption(kycPb.KYCLevel_MIN_KYC))
	case "FULL_KYC":
		opts = append(opts, app.KycLevelOption(kycPb.KYCLevel_FULL_KYC))
	default:
		logger.InfoNoCtx("user with default KYC level based on PAN")
	}
	moolah = 0
	if *addMoney != "" {
		logger.Info(ctx, "add money amount given", zap.String(logger.STATUS_CODE, *addMoney))
		if moolah, err = strconv.ParseInt(*addMoney, 10, 32); err != nil {
			logger.ErrorNoCtx("error while parsing money as int", zap.Error(err))
			a.FailNow("error while parsing money as int")
		}
		if moolah > 0 {
			opts = append(opts, app.AddFundsOption(moolah))
		}
	}

	opts = append(opts, app.APOUnspecifiedOption(*apoUnspecified == "true"))

	// Onboard user with given properties
	app.NewUserForOnboardingWithProperties(opts...).OnboardUserForSavingsAccount(ctx, dep)
	if len(assert.errs) != 0 {
		logger.Fatal("error while onboarding user", zap.Errors("errors", assert.errs))
	}
}
