package userdata

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/lib/pq"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/nulltypes"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/actor/dao/model"
	authPb "github.com/epifi/gamma/api/auth"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	model3 "github.com/epifi/gamma/auth/dao/model"
	model9 "github.com/epifi/gamma/bankcust/dao/model"
	cardModel "github.com/epifi/gamma/card/dao/model"
	depositModel "github.com/epifi/gamma/deposit/dao/model"
	employmentModel "github.com/epifi/gamma/employment/dao/model"
	model8 "github.com/epifi/gamma/order/dao/model"
	model4 "github.com/epifi/gamma/paymentinstrument/dao/model"
	model2 "github.com/epifi/gamma/savings/dao/model"
	model6 "github.com/epifi/gamma/simulator/dao/model"
	model7 "github.com/epifi/gamma/timeline/dao/model"
	model10 "github.com/epifi/gamma/upi/dao/model"
	model5 "github.com/epifi/gamma/user/dao/model"
)

const (
	userPrefix               = "user_id_"
	actorPrefix              = "actor_id_"
	piPrefix                 = "payment_instrument_id_"
	savingsLedgerReconPrefix = "savings_ledger_recon_account_Id"
	timelinePrefix           = "timeline_id_"
	savingsPrefix            = "savings_id_"
	// NOTE: pgdb_conn_alias should be same as defined in the db config.
	userPropertiesConnAlias = "user_properties_pgdb"
)

type TxnFunc func(db *gorm.DB) error

// Wrapper for a transaction.  This automatically re-calls `fn` with
// the open transaction as an argument as long as the database server
// asks for the transaction to be retried.

func RunTransaction(db *gorm.DB, fn TxnFunc) error {
	var maxRetries = 6
	for retries := 0; retries <= maxRetries; retries++ {
		if retries == maxRetries {
			return fmt.Errorf("hit max of %d retries, aborting", retries)
		}
		txn := db.Begin()
		if err := fn(txn); err != nil {
			// We need to cast GORM's db.Error to *pq.Error so we can
			// detect the Postgres transaction retry error code and
			// handle retries appropriately.
			pqErr, ok := errors.Unwrap(err).(*pq.Error)
			if !ok {
				// If it's not a retry error, it's some other sort of
				// DB interaction error that needs to be handled by
				// the caller.
				txn.Rollback()
				return err
			}

			if pqErr.Code == "40001" {
				// Since this is a transaction retry error, we
				// ROLLBACK the transaction and sleep a little before
				// trying again.  Each time through the loop we sleep
				// for a little longer than the last time
				// (A.K.A. exponential backoff).
				txn.Rollback()
				var sleepMs = math.Pow(2, float64(retries)) * 100 * (rand.Float64() + 0.5)
				logger.InfoNoCtx("Hit 40001 transaction retry error", zap.Any("sleeping for ms", sleepMs))
				time.Sleep(time.Millisecond * time.Duration(sleepMs))
			} else {
				// If it's not a retry error, it's some other sort of
				// DB interaction error that needs to be handled by
				// the caller.
				txn.Rollback()
				return err
			}
		} else {
			// All went well, so we try to commit and break out of the
			// retry loop if possible.
			if err := txn.Commit().Error; err != nil {
				if pqErr, ok := err.(*pq.Error); ok {
					// pqErr := err.(*pq.Error)
					if pqErr.Code == "40001" {
						// However, our attempt to COMMIT could also
						// result in a retry error, in which case we
						// continue back through the loop and try again.
						continue
					} else {
						// If it's not a retry error, it's some other sort
						// of DB interaction error that needs to be
						// handled by the caller.
						return err
					}
				}
			}
			break
		}
	}
	return nil
}

func GetUserDetails(epifiDb, actorDb *gorm.DB, ph *commontypes.PhoneNumber) (string, string, error) {
	var (
		userId = ""
		actor  = &model.Actor{}
	)

	if err := epifiDb.Debug().Raw("SELECT id FROM users WHERE computed_phone_number = ? AND deleted_at_unix = 0", ph.ToString()).Row().Scan(&userId); err != nil {
		return "", "", fmt.Errorf("failed to fetch user with given phone number: %w", err)
	}

	logger.InfoNoCtx("user", zap.Any("id", userId))

	if err := actorDb.Debug().Model(&model.Actor{}).Select("id").Where(&model.Actor{EntityId: nulltypes.NewNullString(userId)}).Find(actor).Error; err != nil {
		return "", "", fmt.Errorf("failed to fetch actor: %w", err)
	}

	logger.InfoNoCtx("actor", zap.Any("id", actor.Id))

	return actor.Id, userId, nil
}

// nolint: funlen
func DeleteUserDebitCardDetails(cardDB *gorm.DB, actorID string) error {
	var (
		cardIds    []string
		cardModels []*cardModel.Card
	)
	// get all cards
	if err := cardDB.Where("actor_id = ?", actorID).Find(&cardModels).Error; err != nil {
		return fmt.Errorf("failed to fetch cards, actorid %s: %w", actorID, err)
	}
	for _, val := range cardModels {
		cardIds = append(cardIds, val.ID)
	}
	logger.InfoNoCtx("", zap.Any("card ids", cardIds))

	// delete card activation requests
	if err := cardDB.Exec("DELETE FROM card_sku_overrides WHERE actor_id = ?", actorID).Error; err != nil {
		return fmt.Errorf("error deleting card_sku_overrides relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_sku_overrides for ", zap.String("actorId", actorID))

	// delete card activation requests
	if err := cardDB.Exec("DELETE FROM card_activation_requests WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_activation_requests relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_activation_requests for ", zap.Any("ids", cardIds))

	// delete card creation requests
	if err := cardDB.Exec("DELETE FROM card_creation_requests WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_creation_requests relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_creation_requests for ", zap.Any("ids", cardIds))

	// delete physical card dispatch requests
	if err := cardDB.Exec("DELETE FROM physical_card_dispatch_requests WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting physical_card_dispatch_requests relation: %w", err)
	}
	logger.InfoNoCtx("deleted physical_card_dispatch_requests for ", zap.Any("ids", cardIds))

	// delete card pins
	if err := cardDB.Exec("DELETE FROM card_pins WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_pins relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_pins for ", zap.Any("ids", cardIds))

	// delete card limits
	if err := cardDB.Exec("DELETE FROM card_limits WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_limits relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_limits for ", zap.Any("ids", cardIds))

	// delete card delivery trackings
	if err := cardDB.Exec("DELETE FROM card_delivery_trackings WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_delivery_trackings relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_delivery_trackings for ", zap.Any("ids", cardIds))

	// delete card block details
	if err := cardDB.Exec("DELETE FROM card_block_details WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_block_details relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_block_details for ", zap.Any("ids", cardIds))

	// delete card auth attempts
	if err := cardDB.Exec("DELETE FROM card_auth_attempts WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_auth_attempts relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_auth_attempts for ", zap.Any("ids", cardIds))

	// delete card action attempts
	if err := cardDB.Exec("DELETE FROM card_action_attempts WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_action_attempts relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_action_attempts for ", zap.Any("ids", cardIds))

	// delete card tracking requests
	if err := cardDB.Exec("DELETE FROM card_tracking_requests WHERE card_id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting card_tracking_requests relation: %w", err)
	}
	logger.InfoNoCtx("deleted card_tracking_requests for ", zap.Any("ids", cardIds))

	// delete all cards
	if err := cardDB.Exec("DELETE FROM cards WHERE id IN (?)", cardIds).Error; err != nil {
		return fmt.Errorf("error deleting cards relation: %w", err)
	}
	logger.InfoNoCtx("deleted cards for ", zap.Any("ids", cardIds))

	return nil
}

func DeletePaymentInstrumentDetails(paymentInstrumentDb *gorm.DB, actorID string, piCache cache.CacheStorage) error {
	var (
		piIds           []string
		accountPIModels []*model4.AccountPI
	)

	if err := paymentInstrumentDb.Where("actor_id = ?", actorID).Find(&accountPIModels).Error; err != nil {
		return fmt.Errorf("failed to fetch accountPIs, actorId: %w", err)
	}

	for _, val := range accountPIModels {
		piIds = append(piIds, val.PiId)
	}

	logger.InfoNoCtx("list of pis attached to account", zap.Any("pi ids", piIds))

	if piCache != nil && len(piIds) != 0 {
		cachePiIds := make([]string, len(piIds))
		for ind, piId := range piIds {
			cachePiIds[ind] = piPrefix + piId
		}
		cacheErr := piCache.Delete(context.Background(), cachePiIds...)
		if cacheErr != nil {
			logger.Debug(context.Background(), "error while deleting pi in cache", zap.Error(cacheErr))
		}
	}

	if len(piIds) != 0 {
		if err := paymentInstrumentDb.Exec("UPDATE payment_instruments SET identifier = NULL WHERE id IN (?)", piIds).Error; err != nil {
			return fmt.Errorf("failed to update pis: %w", err)
		}

		logger.InfoNoCtx("updated pis")
	}

	return nil
}

func DeleteTimelineDetails(timelineDb *gorm.DB, actorID string, timelineCache cache.CacheStorage) error {
	var (
		timelineModels []*model7.Timeline
		timelineIds    []string
	)

	if err := timelineDb.Where("primary_actor_id = ? OR secondary_actor_id = ?", actorID, actorID).Find(&timelineModels).Error; err != nil {
		return fmt.Errorf("failed to fetch timelines, actorId: %w", err)
	}

	for _, val := range timelineModels {
		timelineIds = append(timelineIds, val.ID)
	}

	logger.InfoNoCtx("list of timelines attached to account", zap.Any("timeline ids", timelineIds))

	if timelineCache != nil && len(timelineIds) != 0 {
		cacheTimelineIds := make([]string, len(timelineIds))
		for ind, timelineId := range timelineIds {
			cacheTimelineIds[ind] = timelinePrefix + timelineId
		}
		cacheErr := timelineCache.Delete(context.Background(), cacheTimelineIds...)
		if cacheErr != nil {
			logger.Debug(context.Background(), "error while deleting timeline in cache", zap.Error(cacheErr))
		}
	}

	if err := timelineDb.Unscoped().Where("primary_actor_id = ? OR secondary_actor_id = ?", actorID, actorID).
		Delete(&model7.Timeline{}).Error; err != nil {
		return fmt.Errorf("failed to delete actor's timeline: %w", err)
	}

	logger.InfoNoCtx("deleted user's timelines")

	return nil
}

func DeleteUserProperties(userPropertiesDb *gorm.DB, actorID string) error {

	if err := userPropertiesDb.Exec("DELETE FROM contact_properties WHERE actor_id = ?", actorID).Error; err != nil {
		return fmt.Errorf("error deleting contact properties entry: %w", err)
	}
	logger.InfoNoCtx("deleted contact_properties for actor", zap.String(logger.ACTOR_ID_V2, actorID))

	if err := userPropertiesDb.Exec("DELETE FROM contacts WHERE actor_id = ?", actorID).Error; err != nil {
		return fmt.Errorf("error deleting contacts entry: %w", err)
	}
	logger.InfoNoCtx("deleted contacts for actor", zap.String(logger.ACTOR_ID_V2, actorID))

	return nil
}

// nolint:funlen
func DeleteUserDetailsFromEpifiDB(db *gorm.DB, ph *commontypes.PhoneNumber, actorID string, userID string, userCache cache.CacheStorage, actorCache cache.CacheStorage, savingsLedgerReconCache cache.CacheStorage, savingsCache cache.CacheStorage, devRegCache cache.CacheStorage) error {
	var (
		deviceId    string
		orderModels []model8.Order
		savingsAcct = &model2.SavingsAccount{}
		upiAccounts []*model10.UpiAccount
		accountIds  []string
	)

	// delete auth_factor_updates by phone number
	if err := db.Exec("delete from auth_factor_updates where overall_status = 'OVERALL_STATUS_IN_PROGRESS' and computed_phone  = ?", ph.ToString()).Error; err != nil {
		return fmt.Errorf("failed to delete AFU attempts by phone number: %w", err)
	}

	if err := db.Model(&model2.SavingsAccount{}).Unscoped().Where("primary_account_holder = ?", userID).Take(&savingsAcct).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to fetch savings account: %w", err)
	}

	if savingsLedgerReconCache != nil {
		cacheErr := savingsLedgerReconCache.Delete(context.Background(), savingsLedgerReconPrefix+savingsAcct.Id)
		if cacheErr != nil {
			logger.Debug(context.Background(), "error while deleting savings ledger recon in cache", zap.Error(cacheErr), zap.String(logger.ACCOUNT_ID, savingsAcct.Id))
		}
	}

	if err := db.Exec("DELETE FROM savings_ledger_recons WHERE savings_account_id = ?", savingsAcct.Id).Error; err != nil {
		return fmt.Errorf("failed to delete entry from savings ledger recon: %w", err)
	}

	logger.InfoNoCtx("deleted savings ledger recon")

	if err := db.Unscoped().Where("savings_account_id = ?", savingsAcct.Id).Delete(&model2.SavingsAccountAggregation{}).Error; err != nil {
		return fmt.Errorf("failed to delete entry from savings account aggregations: %w", err)
	}

	logger.InfoNoCtx("deleted savings account aggregation")

	if err := db.Exec("DELETE FROM closed_accounts_balance_transfer WHERE savings_account_id = ?", savingsAcct.Id).Error; err != nil {
		return fmt.Errorf("failed to delete entry from closed_accounts_balance_transfer: %w", err)
	}

	logger.InfoNoCtx("deleted data from closed_accounts_balance_transfer table")

	if savingsCache != nil {
		cacheErr := savingsCache.Delete(context.Background(), savingsPrefix+savingsAcct.PrimaryAccountHolder)
		if cacheErr != nil {
			logger.Debug(context.Background(), "error while deleting savings account in cache", zap.Error(cacheErr), zap.String(logger.USER_ID, savingsAcct.PrimaryAccountHolder))
		}
	}

	if err := db.Unscoped().Where("primary_account_holder = ?", userID).Delete(&model2.SavingsAccount{}).Error; err != nil {
		return fmt.Errorf("failed to delete savings account: %w", err)
	}

	logger.InfoNoCtx("deleted savings account")
	if userCache != nil {
		cacheErr := userCache.Delete(context.Background(), userPrefix+userID)
		if cacheErr != nil {
			logger.Debug(context.Background(), "error while deleting user in cache", zap.Error(cacheErr), zap.String(logger.USER_ID, userID))
		}
		// deleting minUser cache with user cache client as it shares same endpoint as user cache.
		if cacheErr = userCache.Delete(context.Background(), cache.MinUserKeyPrefix+userID); cacheErr != nil {
			logger.Debug(context.Background(), "error while deleting min user in cache", zap.Error(cacheErr), zap.String(logger.USER_ID, userID))
		}
	}
	if err := db.Unscoped().Where("id = ?", userID).Delete(&model5.User{}).Error; err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	logger.InfoNoCtx("deleted user")

	if err := db.Raw("SELECT device_id from device_registrations WHERE actor_id = ? and deleted_at_unix = 0", actorID).Row().Scan(&deviceId); err != nil && !storageV2.IsRecordNotFoundError(err) {
		return err
	}
	logger.InfoNoCtx("device id", zap.String(logger.DEVICE_ID, deviceId))
	if err := db.Unscoped().Where("actor_id = ?", actorID).Delete(&model3.DeviceRegistration{}).Error; err != nil {
		return fmt.Errorf("failed to delete actor's registration details: %w", err)
	}
	if err := devRegCache.Delete(context.Background(), "dvRg:actorId:"+actorID, "dvRg:devId:"+deviceId); err != nil {
		logger.DebugNoCtx("error while removing device reg from cache", zap.Error(err))
	}
	logger.InfoNoCtx("deleted device registration details")

	if err := db.Exec("DELETE FROM otps WHERE computed_phone_number = ?", ph.ToString()).Error; err != nil {
		return fmt.Errorf("failed to delete otp entries for phone number: %w", err)
	}
	logger.InfoNoCtx("deleted otp entries")

	if err := db.Exec("DELETE FROM auth_factor_updates WHERE overall_status = 'OVERALL_STATUS_IN_PROGRESS' and actor_id = ?", actorID).Error; err != nil {
		return fmt.Errorf("failed to delete in progress AFU attempts by actor id: %w", err)
	}

	logger.InfoNoCtx("deleted in progress AFU attempts")

	// Delete deposit entities
	if err := db.Unscoped().Where("actor_id = ?", actorID).Delete(&depositModel.DepositRequest{}).Error; err != nil {
		return fmt.Errorf("error deleting deposit requests for actor: %s: %w", actorID, err)
	}
	var depositAccountModels []*depositModel.DepositAccount
	if err := db.Where("actor_id = ?", actorID).Find(&depositAccountModels).Error; err != nil {
		return fmt.Errorf("failed to fetch deposit accounts for actorId: %w", err)
	}
	var depositAccIds []string
	for _, depositAcc := range depositAccountModels {
		depositAccIds = append(depositAccIds, depositAcc.Id)
	}
	if err := db.Unscoped().Where("deposit_account_id IN (?)", depositAccIds).Delete(&depositModel.DepositTransaction{}).Error; err != nil {
		return fmt.Errorf("error deleting deposit transactions for actor: %s: %w", actorID, err)
	}
	if err := db.Unscoped().Where("actor_id = ?", actorID).Delete(&depositModel.DepositAccount{}).Error; err != nil {
		return fmt.Errorf("error deleting deposit accounts for actor: %s: %w", actorID, err)
	}
	logger.InfoNoCtx("deleted deposit requests, deposit transactions and accounts for actor")

	// Delete payment entities
	if err := db.Where("from_actor_id = ? OR to_actor_id = ?", actorID, actorID).
		Find(&orderModels).Error; err != nil {
		return fmt.Errorf("failed to fetch actor's order: %w", err)
	}

	var (
		orderIDs []string
		txnIDs   []string
	)

	for index := range orderModels {
		orderIDs = append(orderIDs, orderModels[index].ID)
	}

	var (
		rows *sql.Rows
		err  error
	)
	// Explicitly checking for length of orders because when orderIDs are empty then in the SQL it is substituted as
	// transactions.order_ref_id IN (NULL), which results in the query returning all the rows.
	if len(orderIDs) > 0 {
		rows, err = db.Raw("SELECT id from transactions WHERE transactions.order_ref_id IN (?)", orderIDs).Rows()
	} else {
		rows, err = db.Raw("SELECT id from transactions WHERE transactions.order_ref_id IN ('')").Rows()
	}
	if err != nil {
		return fmt.Errorf("failed to get transactions from transactions: %w", err)
	}

	defer func() {
		_ = rows.Close()
	}()

	for rows.Next() {
		var txnID string
		if err = rows.Scan(&txnID); err != nil {
			return fmt.Errorf("failed to get transactions from transactions: %w", err)
		}

		txnIDs = append(txnIDs, txnID)
	}

	if err = db.Unscoped().Where("order_id IN (?)", orderIDs).Delete(&model8.OrderAttempt{}).Error; err != nil {
		return fmt.Errorf("error deleting order_attempts: %w", err)
	}

	logger.InfoNoCtx("deleted order attempts")

	if err = db.Unscoped().Where("transaction_id IN (?)", txnIDs).Delete(&model8.TransactionNotificationMap{}).Error; err != nil {
		return fmt.Errorf("error deleting txn notification map for actor: %w", err)
	}

	logger.InfoNoCtx("deleted transaction notification map for actor")

	if err = db.Unscoped().Where("transaction_id IN (?)", txnIDs).Delete(&model8.DisputedTransaction{}).Error; err != nil {
		return fmt.Errorf("error deleting disputed transaction for actor: %w", err)
	}

	logger.InfoNoCtx("deleted disputed transaction for actor")

	if err = db.Unscoped().Where("id IN (?)", txnIDs).Delete(&model8.Transaction{}).Error; err != nil {
		return fmt.Errorf("error deleting txns for actor: %w", err)
	}

	logger.InfoNoCtx("deleted transactions for actor")

	if err = db.Unscoped().Where("id IN (?)", orderIDs).Delete(&model8.Order{}).Error; err != nil {
		return fmt.Errorf("error deleting orders for actor: %w", err)
	}

	logger.InfoNoCtx("deleted bank customer for the user")

	if err = db.Unscoped().Where("user_id = ?", userID).Delete(&model9.BankCustomer{}).Error; err != nil {
		return fmt.Errorf("error deleting bank customer for user, %w", err)
	}

	logger.InfoNoCtx("deleted orders for actor")
	// get all upi accounts
	if err = db.Where("actor_id = ?", actorID).Find(&upiAccounts).Error; err != nil {
		return fmt.Errorf("failed to fetch upi accounts, actorid %s: %w", actorID, err)
	}
	for _, val := range upiAccounts {
		accountIds = append(accountIds, val.Id)
	}
	logger.InfoNoCtx("", zap.Any("account ids", accountIds))

	if err = db.Unscoped().Where("account_id IN (?)", accountIds).Delete(&model10.UpiOnboardingDetail{}).Error; err != nil {
		return fmt.Errorf("error deleting upi onboarding details for actor, %w", err)
	}
	logger.InfoNoCtx("deleted upi onboarding details for actor")

	if err = db.Unscoped().Where("actor_id = ?", actorID).Delete(&model10.ActorVpaNameMap{}).Error; err != nil {
		return fmt.Errorf("error deleting vpa name for actor, %w", err)
	}
	logger.InfoNoCtx("deleted vpa name for actor")

	if err = db.Unscoped().Where("id IN (?)", accountIds).Delete(&model10.UpiAccount{}).Error; err != nil {
		return fmt.Errorf("error deleting upi account for user, %w", err)
	}
	logger.InfoNoCtx("deleted upi accounts for actor")

	if err = db.Unscoped().Where("actor_id IN (?)", actorID).Delete(&employmentModel.EmploymentVerificationCheck{}).Error; err != nil {
		return fmt.Errorf("error deleting employment verification check for actor, %w", err)
	}

	logger.InfoNoCtx("deleted employment verification checks for actor")

	if err = db.Unscoped().Where("actor_id IN (?)", actorID).Delete(&employmentModel.EmploymentVerificationProcess{}).Error; err != nil {
		return fmt.Errorf("error deleting employment verification process for actor, %w", err)
	}

	logger.InfoNoCtx("deleted employment verification process for actor")

	if err = db.Unscoped().Where("actor_id IN (?)", actorID).Delete(&employmentModel.EmploymentData{}).Error; err != nil {
		return fmt.Errorf("error deleting employment data for actor, %w", err)
	}

	logger.InfoNoCtx("deleted employment data for actor")

	logger.InfoNoCtx("deleted user from epiFi DB successfully!")
	return nil
}

func DeleteCustomer(db *gorm.DB, ph *commontypes.PhoneNumber) error {
	var (
		customer = &model6.Customer{}
		err      error
	)

	if err = db.Model(&model6.Customer{}).Where("phone_no = ?", ph.ToString()).Find(&customer).Error; err != nil {
		return fmt.Errorf("failed fetch customer details from simulator: %w", err)
	}

	logger.InfoNoCtx("customer", zap.String("id", customer.ID))

	if err = db.Unscoped().Where("customer_id = ?", customer.ID).Delete(&model6.Account{}).Error; err != nil {
		return fmt.Errorf("failed to delete account from simulator: %w", err)
	}

	logger.InfoNoCtx("account deleted from simulator")

	if err = db.Unscoped().Where("customer_id = ?", customer.ID).Delete(&model6.DepositAccount{}).Error; err != nil {
		return fmt.Errorf("failed to delete deposit accounts from simulator: %w", err)
	}

	logger.InfoNoCtx("deposit account deleted from simulator")

	if err = db.Unscoped().Where("phone_number = ?", ph.ToString()).Delete(&model6.TPAPAccount{}).Error; err != nil {
		return fmt.Errorf("failed to delete deposit accounts from simulator: %w", err)
	}

	logger.InfoNoCtx("tpap account deleted from simulator")

	if err = db.Unscoped().Where("id = ?", customer.ID).Delete(&model6.Customer{}).Error; err != nil {
		return fmt.Errorf("failed to delete customer from simulator: %w", err)
	}

	logger.InfoNoCtx("deleted customer from simulator successfully")

	return nil

}

// nolint: funlen
func DeleteUserData(epifiDb, actorDb, timelineDb, paymentInstrumentDb, simulatorDb *gorm.DB, ph *commontypes.PhoneNumber, userCacheStorage, actorCacheStorage, piCacheStorage, savingsLedgerReconCacheStorage, timelineCacheStorage, savingsCacheStorage, devRegCacheStorage cache.CacheStorage, authClient authPb.AuthClient) {
	// delete all the entries from epifi DB

	actorID, userID, err := GetUserDetails(epifiDb, actorDb, ph)
	if err != nil {
		if !storageV2.IsRecordNotFoundError(err) {
			logger.ErrorNoCtx("failed to fetch user details for the given phone number", zap.Error(err))
		} else {
			logger.InfoNoCtx(fmt.Sprintf("no user found for given phone number : %v", ph))
		}
		return
	}
	res, err := authClient.UpdateToken(context.Background(), &authPb.UpdateTokenRequest{
		Status: authPb.UpdateTokenRequest_DELETE,
		Identifier: &authPb.UpdateTokenRequest_PhoneNumber{
			PhoneNumber: ph,
		},
		TokenTypes: []authPb.TokenType{
			authPb.TokenType_REFRESH_TOKEN,
		},
		TokenUpdationReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_EXPIRY,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.ErrorNoCtx("failed to delete user tokens", zap.Error(rpcErr))
	}

	// delete all the entries from epifi DB
	err = RunTransaction(epifiDb, func(db *gorm.DB) error {
		return DeleteUserDebitCardDetails(db, actorID)
	})
	if err != nil {
		logger.ErrorNoCtx("failed to delete user card details", zap.Error(err))
		return
	}

	err = RunTransaction(paymentInstrumentDb, func(db *gorm.DB) error {
		return DeletePaymentInstrumentDetails(db, actorID, piCacheStorage)
	})
	if err != nil {
		logger.ErrorNoCtx("failed to delete user payment instrument details", zap.Error(err))
		return
	}

	err = RunTransaction(timelineDb, func(db *gorm.DB) error {
		return DeleteTimelineDetails(db, actorID, timelineCacheStorage)
	})
	if err != nil {
		logger.ErrorNoCtx("failed to delete user payment instrument details", zap.Error(err))
		return
	}

	if err = RunTransaction(epifiDb.Clauses(dbresolver.Use(userPropertiesConnAlias)), func(db *gorm.DB) error {
		return DeleteUserProperties(db, actorID)
	}); err != nil {
		logger.ErrorNoCtx("failed to delete user properties from pgdb", zap.Error(err))
	}

	// delete all the entries from epifi DB
	err = RunTransaction(epifiDb, func(db *gorm.DB) error {
		return DeleteUserDetailsFromEpifiDB(db, ph, actorID, userID, userCacheStorage, actorCacheStorage, savingsLedgerReconCacheStorage, savingsCacheStorage, devRegCacheStorage)
	})
	if err != nil {
		logger.ErrorNoCtx("failed to delete user from epifi DB", zap.Error(err))
		return
	}

	if actorCacheStorage != nil {
		cacheErr := actorCacheStorage.Delete(context.Background(), actorPrefix+actorID)
		if cacheErr != nil {
			logger.Debug(context.Background(), "error while deleting actor in cache", zap.Error(cacheErr), zap.String(logger.ACTOR_ID, actorID))
		}
	}

	// delete entries from simulator DB
	err = RunTransaction(simulatorDb, func(db *gorm.DB) error {
		return DeleteCustomer(db, ph)
	})
	if err != nil {
		logger.ErrorNoCtx("failed to delete customer from simulator DB", zap.Error(err))
		return
	}
}

func DeleteWealthData(ph *commontypes.PhoneNumber, epifiDb, simulatorDb, epifiWealthDb, pgDb, actorDb *gorm.DB) error {
	logger.InfoNoCtx("deleting connected account user data from DB")
	actorId, err := GetActorIdForUser(epifiDb, actorDb, ph)
	if err != nil {
		return err
	}
	if err == nil {
		if err := RunTransaction(epifiWealthDb, func(db *gorm.DB) error {
			return DeleteEpifiWealthData(db, pgDb, actorId)
		}); err != nil {
			logger.ErrorNoCtx("error deleting epifi wealth data for connected account user", zap.Error(err))
			return err
		}
		if err := RunTransaction(simulatorDb, func(db *gorm.DB) error {
			return DeleteSimulatorData(db, ph)
		}); err != nil {
			logger.ErrorNoCtx("error deleting simulator data for connected account user", zap.Error(err))
			return err
		}
	}
	return nil
}

func DeleteTieringEntries(ph *commontypes.PhoneNumber, epifiDb, tieringDb, actorDb *gorm.DB) error {
	logger.InfoNoCtx("deleting tiering user data from tiering DB")
	actorId, err := GetActorIdForUser(epifiDb, actorDb, ph)
	if err != nil {
		return err
	}
	// actor tier infos
	if err := tieringDb.Exec("DELETE FROM actor_tier_infos WHERE actor_id = ?", actorId).Error; err != nil {
		return fmt.Errorf("error deleting actor_tier_infos relation: %w", err)
	}
	// tier movement histories
	if err := tieringDb.Exec("DELETE FROM tier_movement_histories WHERE actor_id = ?", actorId).Error; err != nil {
		return fmt.Errorf("error deleting tier_movement_histories relation: %w", err)
	}
	// eligible tier movements
	if err := tieringDb.Exec("DELETE FROM eligible_tier_movements WHERE actor_id = ?", actorId).Error; err != nil {
		return fmt.Errorf("error deleting eligible_tier_movements relation: %w", err)
	}

	logger.InfoNoCtx("deleted tiering entries for ", zap.Any(logger.ACTOR_ID_V2, actorId))
	return nil
}

func DeleteConnectedAccountData(ph *commontypes.PhoneNumber, epifiDb, connectedAccountDb, actorDb *gorm.DB) error {
	logger.InfoNoCtx("deleting connected account user data from connected_account DB")
	actorId, err := GetActorIdForUser(epifiDb, actorDb, ph)
	if err != nil {
		return err
	}
	if err == nil {
		if err := RunTransaction(connectedAccountDb, func(db *gorm.DB) error {
			return DeleteConnectedAccountEntitiesData(db, actorId)
		}); err != nil {
			logger.ErrorNoCtx("error deleting connected account user data", zap.Error(err))
			return err
		}
	}
	return nil
}
