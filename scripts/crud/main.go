package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"context"
	"errors"
	"flag"
	"fmt"
	"os"

	"github.com/epifi/be-common/pkg/cache"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	authPb "github.com/epifi/gamma/api/auth"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/scripts/crud/config"
	"github.com/epifi/gamma/scripts/crud/federal"
	"github.com/epifi/gamma/scripts/crud/userdata"
	"github.com/epifi/gamma/scripts/crud/waitlist"
	"github.com/epifi/gamma/user/dao"
)

var (
	countryCode = flag.Uint64("countryCode", 91,
		"country code spericfying for India or NR onboarding")
	phoneNumber = flag.Uint64("phone", 9090909090,
		"10 digit phone number i.e. without country code, for the user that needs to be deleted from the system")

	deleteFederalUser = flag.Bool("federal", false,
		"bool value representing whether data from federal uat env be deleted")

	deleteWaitlistUser = flag.Bool("waitlist", false,
		"bool value representing to delete waitlist user. If marked, It won't delete main app data.")
)

func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}
	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	authClient := authPb.NewAuthClient(authConn)
	userRedisClient := storage.NewRedisClient(conf.UserRedis.Options, conf.UserRedis.IsSecure, false)
	userRedisCacheStorage := cache.NewRedisCacheStorage(userRedisClient)

	actorRedisClient := storage.NewRedisClient(conf.ActorRedis.Options, conf.ActorRedis.IsSecure, false)
	actorRedisCacheStorage := cache.NewRedisCacheStorage(actorRedisClient)

	piRedisClient := storage.NewRedisClient(conf.PiRedis.Options, conf.PiRedis.IsSecure, false)
	piRedisCacheStorage := cache.NewRedisCacheStorage(piRedisClient)

	savingsLedgerReconRedisClient := storage.NewRedisClient(conf.SavingsLedgerReconRedis.Options, conf.SavingsLedgerReconRedis.IsSecure, false)
	savingsLedgerReconCacheStorage := cache.NewRedisCacheStorage(savingsLedgerReconRedisClient)

	timelineRedisClient := storage.NewRedisClient(conf.TimelineRedis.Options, conf.TimelineRedis.IsSecure, false)
	timelineRedisCacheStorage := cache.NewRedisCacheStorage(timelineRedisClient)

	savingsRedisClient := storage.NewRedisClient(conf.SavingsRedis.Options, conf.SavingsRedis.IsSecure, false)
	savingsRedisCacheStorage := cache.NewRedisCacheStorage(savingsRedisClient)

	devRegRedisClient := storage.NewRedisClientFromConfig(conf.DevRegRedis, false)
	devRegCacheStorage := cache.NewRedisCacheStorage(devRegRedisClient)

	dbv2, err := storageV2.NewCRDBWithConfig(conf.EpifiDb, false)
	logger.InfoNoCtx("crdb config", zap.Any("crdb config", dbv2.Config))
	if err != nil {
		logger.ErrorNoCtx("Failed to load DB", zap.Error(err))
		return
	}
	cf := changefeed.NewChangefeed(dbv2)
	userDao := dao.NewUserGormDao(dbv2, cf)

	simulatorDB, err := storageV2.NewCRDBWithConfig(conf.SimulatorDb, false)
	if err != nil {
		logger.ErrorNoCtx("Failed to load simulator DB", zap.Error(err))
		return
	}

	epifiWealthDb, err := storageV2.NewCRDBWithConfig(conf.EpifiWealthDb, false)
	if err != nil {
		logger.ErrorNoCtx("Failed to epifi wealth DB", zap.Error(err))
		return
	}

	connectedAccountPgdb, err := storageV2.NewPostgresDBWithConfig(conf.ConnectedAccountDb, false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to connected account DB: %v", err))
	}

	tieringPgdb, err := storageV2.NewPostgresDBWithConfig(conf.TieringDb, false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to tiering DB: %v", err))
	}

	rmsDb, err := storageV2.NewPostgresDBWithConfig(conf.RMSDb, false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to rms DB: %v", err))
	}

	timelinePgdb, timelinePgdbErr := storageV2.NewGormDB(conf.TimelineDb)
	if timelinePgdbErr != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to timeline DB: %v", timelinePgdbErr))
	}

	actorPgdb, actorPgdbErr := storageV2.NewGormDB(conf.ActorDb)
	if actorPgdbErr != nil {
		logger.Fatal("failed to connect to actor db", zap.Error(actorPgdbErr))
	}

	paymentInstrumentPgdb, paymentInstrumentPgdbErr := storageV2.NewGormDB(conf.PaymentInstrumentDb)
	if paymentInstrumentPgdbErr != nil {
		logger.Fatal("failed to connecto to payment instrument db", zap.Error(paymentInstrumentPgdbErr))
	}

	ph := &commontypes.PhoneNumber{
		CountryCode:    uint32(*countryCode),
		NationalNumber: *phoneNumber,
	}

	if *deleteWaitlistUser {
		err = waitlist.DeleteUser(context.Background(), dbv2, ph)
		if err != nil {
			logger.ErrorNoCtx("Failed to waitlist.DeleteUser", zap.Error(err))
		}
		return
	}

	if conf.Application.Environment == cfg.UatEnv && *deleteFederalUser {
		err = federal.DeactivateDevice(dbv2, userDao, ph)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.ErrorNoCtx("error in deactivate device", zap.Error(err))
			fmt.Printf("\n\n USER DELETION AT FEDERAL FAILED: %v\n\n", err)
			os.Exit(1) // we want the jenkins job to fail
		} else {
			fmt.Printf("\n\n USER NOT FOUND IN DB, SKIPPING FEDERAL DATA DELETION: %v\n\n", err)
		}
	} else {
		logger.InfoNoCtx("Skipping Federal Data Deletion",
			zap.String("env", conf.Application.Environment),
			zap.Bool("federal flag", *deleteFederalUser),
		)
	}

	// delete tiering entries
	if deleteTieringEntriesErr := userdata.DeleteTieringEntries(ph, dbv2, tieringPgdb, actorPgdb); deleteTieringEntriesErr != nil {
		return
	}

	if err := userdata.DeleteConnectedAccountData(ph, dbv2, connectedAccountPgdb, actorPgdb); err != nil {
		return
	}

	if err := userdata.DeleteWealthData(ph, dbv2, simulatorDB, epifiWealthDb, rmsDb, actorPgdb); err != nil {
		return
	}

	userdata.DeleteUserData(dbv2, actorPgdb, timelinePgdb, paymentInstrumentPgdb, simulatorDB, ph, userRedisCacheStorage, actorRedisCacheStorage, piRedisCacheStorage, savingsLedgerReconCacheStorage, timelineRedisCacheStorage, savingsRedisCacheStorage, devRegCacheStorage, authClient)
}
