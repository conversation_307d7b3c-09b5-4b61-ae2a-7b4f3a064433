package tiering

import (
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
)

type DynamicElementsCollectedData struct {
	GraceTimestamp       *timestampPb.Timestamp
	GracePeriod          float64
	DowngradedFrom       tieringEnumPb.Tier
	CurrentTier          tieringEnumPb.Tier
	IsUserInGrace        bool
	IsUserDowngraded     bool
	IsARewardsAbuserUser bool
	IsSalaryPromoEnabled bool
	IsFiLiteUser         bool
	TrialDetailsResp     *tiering.GetTrialDetailsResponse
}

func (d *DynamicElementsCollectedData) GetIsUserInGrace() bool {
	if d != nil {
		return d.IsUserInGrace
	}
	return false
}

func (d *DynamicElementsCollectedData) GetIsUserDowngraded() bool {
	if d != nil {
		return d.IsUserDowngraded
	}
	return false
}

func (d *DynamicElementsCollectedData) GetIsARewardsAbuserUser() bool {
	if d != nil {
		return d.IsARewardsAbuserUser
	}
	return false
}

func (d *DynamicElementsCollectedData) GetDowngradedFrom() tieringEnumPb.Tier {
	if d != nil {
		return d.DowngradedFrom
	}
	return tieringEnumPb.Tier_TIER_UNSPECIFIED
}

func (d *DynamicElementsCollectedData) GetCurrentTier() tieringEnumPb.Tier {
	if d != nil {
		return d.CurrentTier
	}
	return tieringEnumPb.Tier_TIER_UNSPECIFIED
}

func (d *DynamicElementsCollectedData) GetGraceTimestamp() *timestampPb.Timestamp {
	if d != nil {
		return d.GraceTimestamp
	}
	return nil
}

func (d *DynamicElementsCollectedData) GetGracePeriod() float64 {
	if d != nil {
		return d.GracePeriod
	}
	return 0
}

func (d *DynamicElementsCollectedData) GetIsSalaryPromoEnabled() (defaultVal bool) {
	if d != nil {
		return d.IsSalaryPromoEnabled
	}
	return defaultVal
}

func (d *DynamicElementsCollectedData) GetIsFiLiteUser() (defaultVal bool) {
	if d != nil {
		return d.IsFiLiteUser
	}
	return defaultVal
}

func (d *DynamicElementsCollectedData) GetTrialDetailsResp() *tiering.GetTrialDetailsResponse {
	if d != nil {
		return d.TrialDetailsResp
	}
	return nil
}

type BannerInfo struct {
	Title        string
	ImageUrl     string
	BgColor      string
	FontColor    string
	ToShowBanner bool
}

func (b *BannerInfo) GetToShowBanner() bool {
	if b != nil {
		return b.ToShowBanner
	}
	return false
}

func (b *BannerInfo) GetTitle() string {
	if b != nil {
		return b.Title
	}
	return ""
}

func (b *BannerInfo) GetImageUrl() string {
	if b != nil {
		return b.ImageUrl
	}
	return ""
}

func (b *BannerInfo) GetBgColor() string {
	if b != nil {
		return b.BgColor
	}
	return ""
}

func (b *BannerInfo) GetFontColor() string {
	if b != nil {
		return b.FontColor
	}
	return ""
}

type PitchV2CollectedData struct {
	currentTier                                tieringEnumPb.Tier
	tierOptionsMap                             map[tieringEnumPb.Tier][]*criteriaPb.Option
	lastUpgradeMovement                        *tieringExtPb.LatestMovementDetails
	lastDowngradeMovement                      *tieringExtPb.LatestMovementDetails
	actorBaseTier                              tieringEnumPb.Tier
	currentTierlatestDoneOptionType            tieringEnumPb.CriteriaOptionType
	currentTierLatestCriteriaChangeOptionType  tieringEnumPb.CriteriaOptionType
	currentTierLatestCriteriaChangeOptionTypes []tieringEnumPb.CriteriaOptionType
}

func (p *PitchV2CollectedData) CurrentTierLatestCriteriaChangeOptionTypes() []tieringEnumPb.CriteriaOptionType {
	if p != nil {
		return p.currentTierLatestCriteriaChangeOptionTypes
	}
	return nil
}

func (p *PitchV2CollectedData) CurrentTierLatestCriteriaChangeOptionType() tieringEnumPb.CriteriaOptionType {
	if p != nil {
		return p.currentTierLatestCriteriaChangeOptionType
	}
	return tieringEnumPb.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED
}

func (p *PitchV2CollectedData) CurrentTierLatestDoneOptionType() tieringEnumPb.CriteriaOptionType {
	if p != nil {
		return p.currentTierlatestDoneOptionType
	}
	return tieringEnumPb.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED
}
func (p *PitchV2CollectedData) ActorBaseTier() tieringEnumPb.Tier {
	if p != nil {
		return p.actorBaseTier
	}
	return tieringEnumPb.Tier_TIER_UNSPECIFIED
}

func (p *PitchV2CollectedData) LastDowngradeMovement() *tieringExtPb.LatestMovementDetails {
	if p != nil {
		return p.lastDowngradeMovement
	}
	return nil
}

func (p *PitchV2CollectedData) LastUpgradeMovement() *tieringExtPb.LatestMovementDetails {
	if p != nil {
		return p.lastUpgradeMovement
	}
	return nil
}

func (p *PitchV2CollectedData) TierOptionsMap() map[tieringEnumPb.Tier][]*criteriaPb.Option {
	if p != nil {
		return p.tierOptionsMap
	}
	return nil
}

func (p *PitchV2CollectedData) CurrentTier() tieringEnumPb.Tier {
	if p != nil {
		return p.currentTier
	}
	return tieringEnumPb.Tier_TIER_UNSPECIFIED
}
