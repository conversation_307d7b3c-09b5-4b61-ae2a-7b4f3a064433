package tiering

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/durationpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	pkgDateTime "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	events2 "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/savings"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	beUserGrpPb "github.com/epifi/gamma/api/user/group"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
	helper2 "github.com/epifi/gamma/frontend/tiering/helper"
	"github.com/epifi/gamma/tiering/evaluator/options"

	"github.com/epifi/gamma/api/pay"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	pkgRelease "github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/criteria"
	"github.com/epifi/gamma/tiering/dao"
	"github.com/epifi/gamma/tiering/data"
	"github.com/epifi/gamma/tiering/data_collector"
	"github.com/epifi/gamma/tiering/display_properties"
	"github.com/epifi/gamma/tiering/evaluator"
	tevents "github.com/epifi/gamma/tiering/events"
	"github.com/epifi/gamma/tiering/helper"
	"github.com/epifi/gamma/tiering/movement"
	"github.com/epifi/gamma/tiering/orchestrator"
	"github.com/epifi/gamma/tiering/release"
	"github.com/epifi/gamma/tiering/tier_options"
	"github.com/epifi/gamma/tiering/tiermappings"
	"github.com/epifi/gamma/tiering/timeline"
)

type Service struct {
	tieringPb.UnimplementedTieringServer
	gconf                     *genconf.Config
	tieringOrchestrator       orchestrator.Orchestrator
	releaseManager            release.Manager
	tierMovementHistoryDao    dao.TierMovementHistoryDao
	eligibleTierMovementDao   dao.EligibleTierMovementDao
	actorTierInfoDao          dao.ActorTierInfoDao
	actorScreenInteractionDao dao.ActorScreenInteractionDao
	tieringAbuserDao          dao.TieringAbuserDao
	criteriaManager           criteria.TierCriteria
	tierTimelineManager       timeline.TierTimeline
	dataCollectorFactory      data_collector.DataCollectorFactory
	displayProperties         display_properties.DisplayProperties
	movementManager           movement.TierMovementManager
	tierOptionsManager        tier_options.Manager
	tierEvaluator             evaluator.TierEvaluator
	segmentationClient        segmentPb.SegmentationServiceClient
	salaryProgramClient       salaryprogramPb.SalaryProgramClient
	dataProcessor             data.TieringDataProcessor
	onboardingClient          onboardingPb.OnboardingClient
	userClient                userPb.UsersClient
	payClient                 pay.PayClient
	releaseEvaluator          pkgRelease.IEvaluator
	abEvaluatorGeneric        *pkgRelease.ABEvaluator[string]
	redisClient               *redis.Client
	tieringPinotClient        tieringPinotPb.EODBalanceClient
	savingsClient             savings.SavingsClient
	accountBalanceClient      accountBalancePb.BalanceClient
	eventBroker               events2.Broker
}

func NewService(
	gconf *genconf.Config,
	tieringOrchestrator orchestrator.Orchestrator,
	releaseManager release.Manager,
	tierMovementHistoryDao dao.TierMovementHistoryDao,
	eligibleTierMovementDao dao.EligibleTierMovementDao,
	actorTierInfoDao dao.ActorTierInfoDao,
	actorScreenInteractionDao dao.ActorScreenInteractionDao,
	tieringAbuserDao dao.TieringAbuserDao,
	tierTimelineManager timeline.TierTimeline,
	criteriaManager criteria.TierCriteria,
	dataCollector data_collector.DataCollectorFactory,
	displayProperties display_properties.DisplayProperties,
	movementManager movement.TierMovementManager,
	tierOptionsManager tier_options.Manager,
	tierEvaluator evaluator.TierEvaluator,
	segmentationClient segmentPb.SegmentationServiceClient,
	salaryProgramClient salaryprogramPb.SalaryProgramClient,
	dataProcessor data.TieringDataProcessor,
	onboardingClient onboardingPb.OnboardingClient,
	userClient userPb.UsersClient,
	payClient pay.PayClient,
	releaseEvaluator pkgRelease.IEvaluator,
	redisClient pkgTypes.TieringConnectedAccountRedisStore,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	savingsClient savings.SavingsClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	eventBroker events2.Broker,
	actorClient actor.ActorClient,
	userGrpClient beUserGrpPb.GroupClient,
) *Service {
	return &Service{
		gconf:                     gconf,
		tieringOrchestrator:       tieringOrchestrator,
		releaseManager:            releaseManager,
		tierMovementHistoryDao:    tierMovementHistoryDao,
		actorTierInfoDao:          actorTierInfoDao,
		eligibleTierMovementDao:   eligibleTierMovementDao,
		actorScreenInteractionDao: actorScreenInteractionDao,
		tieringAbuserDao:          tieringAbuserDao,
		tierTimelineManager:       tierTimelineManager,
		criteriaManager:           criteriaManager,
		dataCollectorFactory:      dataCollector,
		displayProperties:         displayProperties,
		movementManager:           movementManager,
		tierOptionsManager:        tierOptionsManager,
		tierEvaluator:             tierEvaluator,
		segmentationClient:        segmentationClient,
		salaryProgramClient:       salaryProgramClient,
		dataProcessor:             dataProcessor,
		onboardingClient:          onboardingClient,
		userClient:                userClient,
		payClient:                 payClient,
		releaseEvaluator:          releaseEvaluator,
		abEvaluatorGeneric:        helper2.GetABEvaluatorOfFeature[string](actorClient, userClient, userGrpClient, gconf.ABFeatureReleaseConfig(), func(str string) string { return str }),
		redisClient:               redisClient,
		tieringPinotClient:        tieringPinotClient,
		savingsClient:             savingsClient,
		accountBalanceClient:      accountBalanceClient,
		eventBroker:               eventBroker,
	}
}

const (
	StatusMessageTieringDisabled = "tiering is disabled for the actor"
)

// GetTierAtTime gets tier of a user or before a particular time
// In case tiering is disabled for a user the rpc returns status code as DISABLED
func (s *Service) GetTierAtTime(ctx context.Context, getTierRequest *tieringPb.GetTierAtTimeRequest) (*tieringPb.GetTierAtTimeResponse, error) {
	actorId := getTierRequest.GetActorId()
	// Get the latest tier movement for an actor after given timestamp
	// with condition that to_tier should be in current active internal tiers
	tierAt := getTierRequest.GetTierTimestamp()
	activeInternalTiers := tiermappings.GetAllActiveInternalTiers()
	tierMovement, tierMovementErr := s.tierMovementHistoryDao.GetLatestByActorIdAndTierBeforeTimestamp(ctx, actorId, activeInternalTiers, tierAt.AsTime(),
		tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
		tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT)
	if tierMovementErr != nil {
		// if actor is not already tiered, return base tier for actor
		if errors.Is(epifierrors.ErrRecordNotFound, tierMovementErr) {
			logger.Debug(ctx, "actor is not tiered ", zap.Error(tierMovementErr))
			baseTier, baseTierErr := s.getExternalBaseTierForActor(ctx, actorId)
			if baseTierErr != nil {
				logger.Error(ctx, "error getting base tier for actor", zap.Error(baseTierErr))
				return &tieringPb.GetTierAtTimeResponse{
					Status: rpcPb.StatusInternalWithDebugMsg("error getting base tier for actor"),
				}, nil
			}
			return &tieringPb.GetTierAtTimeResponse{
				Status: rpcPb.StatusOk(),
				TierInfo: &tieringExtPb.TierInfo{
					Tier:          baseTier,
					LastUpdatedAt: tierAt,
				},
			}, nil
		}
		logger.Error(ctx, "error getting latest tier movement history from dao", zap.Error(tierMovementErr))
		return &tieringPb.GetTierAtTimeResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting latest tier movement history from dao"),
		}, nil
	}
	// convert internal tier to external tier
	extTier, extTierErr := tiermappings.GetExternalTierFromInternalTier(tierMovement.GetToTier())
	if extTierErr != nil {
		// ideally this should be unreachable code since we are only querying first from active internal tiers that has mappings
		logger.Error(ctx, "error converting internal tier to external tier", zap.Error(extTierErr))
		return &tieringPb.GetTierAtTimeResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error converting internal tier to external tier"),
		}, nil
	}
	return &tieringPb.GetTierAtTimeResponse{
		Status: rpcPb.StatusOk(),
		TierInfo: &tieringExtPb.TierInfo{
			Tier:          extTier,
			LastUpdatedAt: tierMovement.GetCreatedAt(),
		},
	}, nil
}

func (s *Service) Upgrade(ctx context.Context, upgradeRequest *tieringPb.UpgradeRequest) (*tieringPb.UpgradeResponse, error) {
	actorId := upgradeRequest.GetActorId()

	fromTier, toTier, orchestrateErr := s.tieringOrchestrator.OrchestrateTierMovement(ctx, actorId, upgradeRequest.GetProvenance(), &tieringPb.TierMovementReason{})
	if orchestrateErr != nil {
		if helper.IsIgnorableOrchestratorError(orchestrateErr) {
			logger.Error(ctx, "got ignorable error in Upgrade flow", zap.Error(orchestrateErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringPb.UpgradeResponse{
				Status: rpcPb.StatusAbortedWithDebugMsg(orchestrateErr.Error()),
			}, nil
		}
		logger.Error(ctx, "error orchestrating tier movement in Upgrade flow", zap.Error(orchestrateErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.UpgradeResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error orchestrating tier movement"),
		}, nil
	}
	movementType, _ := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	logger.Info(ctx, "successfully orchestrated tier movement in Upgrade flow", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any(logger.FROM_TIER, fromTier), zap.Any(logger.TO_TIER, toTier), zap.Any(logger.PROVENANCE, upgradeRequest.GetProvenance()), zap.Any(logger.MOVEMENT_TYPE, movementType))

	fromTierExt, fromTierExtErr := tiermappings.GetExternalTierFromInternalTier(fromTier)
	if fromTierExtErr != nil {
		logger.Error(ctx, "error getting external tier", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(fromTierExtErr))
		return &tieringPb.UpgradeResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting external from tier")}, nil
	}
	toTierExt, toTierExtErr := tiermappings.GetExternalTierFromInternalTier(toTier)
	if toTierExtErr != nil {
		logger.Error(ctx, "error getting external tier", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(fromTierExtErr))
		return &tieringPb.UpgradeResponse{Status: rpcPb.StatusInternalWithDebugMsg("error getting external to tier")}, nil
	}
	if fromTier.Number() < toTier.Number() {
		return &tieringPb.UpgradeResponse{
			Status:   rpcPb.StatusOk(),
			FromTier: fromTierExt,
			ToTier:   toTierExt,
		}, nil
	}

	return &tieringPb.UpgradeResponse{
		Status:   rpcPb.NewStatusWithoutDebug(uint32(tieringPb.UpgradeResponse_DOWNGRADED), "downgrade orchestrated for actor"),
		FromTier: fromTierExt,
		ToTier:   toTierExt,
	}, nil
}

func (s *Service) ShowComponentToActor(ctx context.Context, req *tieringPb.ShowComponentToActorRequest) (*tieringPb.ShowComponentToActorResponse, error) {
	actorId := req.GetActorId()

	component := req.GetDisplayComponent()
	if component == tieringEnumPb.DisplayComponent_DISPLAY_COMPONENT_UNSPECIFIED {
		logger.Error(ctx, "received unspecified display component", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.ShowComponentToActorResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}

	// Check if component is shown to user or not
	// If not, record the component as shown for that particular actor
	shownComponentToActor, showComponentErr := s.displayProperties.IsComponentShownToActor(ctx, actorId, component.String())
	if showComponentErr != nil {
		logger.Error(ctx, "error determining whether screen has been shown to actor", zap.Error(showComponentErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.ShowComponentToActorResponse{Status: rpcPb.StatusInternal()}, nil
	}

	// If already shown, do not show again
	return &tieringPb.ShowComponentToActorResponse{Status: rpcPb.StatusOk(), ShowComponent: !shownComponentToActor}, nil
}

func (s *Service) RecordComponentShownToActor(ctx context.Context, req *tieringPb.RecordComponentShownToActorRequest) (*tieringPb.RecordComponentShownToActorResponse, error) {
	actorId, component := req.GetActorId(), req.GetComponent()
	if component == tieringEnumPb.DisplayComponent_DISPLAY_COMPONENT_UNSPECIFIED {
		logger.Error(ctx, "received unspecified display component", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.RecordComponentShownToActorResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}

	// Get expiry for the component
	expiry, expiryErr := s.gconf.GetTTLFromComponent(component.String())
	if expiryErr != nil {
		logger.Error(ctx, "error getting expiry for component", zap.Error(expiryErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.RecordComponentShownToActorResponse{Status: rpcPb.StatusInternal()}, nil
	}
	logger.Debug(ctx, "setting expiry time", zap.Any("component", component.String()), zap.Any("expiry", expiry))

	// Record display component
	// Return internal error, if unsuccessful
	recordScreenErr := s.displayProperties.RecordDisplayComponentShownToActor(ctx, actorId, component.String(), expiry)
	if recordScreenErr != nil {
		logger.Error(ctx, "error recording display component for actor", zap.Error(recordScreenErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.RecordComponentShownToActorResponse{Status: rpcPb.StatusInternal()}, nil
	}

	logger.Debug(ctx, "screen not shown to actor, successfully set in cache", zap.Any(logger.ACTOR_ID_V2, actorId),
		zap.Any("screenName", component), zap.Any("keyExpiry", expiry))
	return &tieringPb.RecordComponentShownToActorResponse{Status: rpcPb.StatusOk()}, nil
}

func (s *Service) GetTieringPitchV2(ctx context.Context, req *tieringPb.GetTieringPitchV2Request) (*tieringPb.GetTieringPitchV2Response, error) {
	actorId := req.GetActorId()
	// gather data needed for pitch v2
	collectedData, gatherDataErr := s.gatherDataForPitchV2(ctx, actorId)
	if gatherDataErr != nil {
		logger.Error(ctx, "error gathering data for pitch v2", zap.Any(logger.ACTOR_ID_V2, actorId),
			zap.Error(gatherDataErr))
		return &tieringPb.GetTieringPitchV2Response{
			Status: rpcPb.StatusInternalWithDebugMsg("error gathering data for pitch v2"),
		}, nil
	}
	// get all possible tier movement details
	movementDetails, getMovementDetailsErr := s.getTierMovementDetails(ctx, actorId, collectedData.CurrentTier(), collectedData.TierOptionsMap())
	if getMovementDetailsErr != nil {
		logger.Error(ctx, "error getting tier movement details", zap.Any(logger.ACTOR_ID_V2, actorId),
			zap.Error(getMovementDetailsErr))
		return &tieringPb.GetTieringPitchV2Response{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting tier movement details"),
		}, nil
	}
	// convert current tier to external tier
	currentExtTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(collectedData.CurrentTier())
	if conversionErr != nil {
		logger.Error(ctx, "error converting current tier to external", zap.Any(logger.ACTOR_ID_V2, actorId),
			zap.Error(conversionErr))
		return &tieringPb.GetTieringPitchV2Response{
			Status: rpcPb.StatusInternalWithDebugMsg("error converting current tier to external"),
		}, nil
	}
	// convert actor base tier to external tier
	actorBaseTierExt, baseTierConvErr := tiermappings.GetExternalTierFromInternalTier(collectedData.ActorBaseTier())
	if baseTierConvErr != nil {
		logger.Error(ctx, "error converting actor base tier to external", zap.Any(logger.ACTOR_ID_V2, actorId),
			zap.Error(baseTierConvErr))
		return &tieringPb.GetTieringPitchV2Response{
			Status: rpcPb.StatusInternalWithDebugMsg("error converting actor base tier to external"),
		}, nil
	}
	return &tieringPb.GetTieringPitchV2Response{
		Status:                     rpcPb.StatusOk(),
		CurrentTier:                currentExtTier,
		MovementDetailsList:        movementDetails,
		LastUpgradeDetails:         collectedData.LastUpgradeMovement(),
		LastDowngradeDetails:       collectedData.LastDowngradeMovement(),
		ActorBaseTier:              actorBaseTierExt,
		EntryCriteriaOptionType:    collectedData.CurrentTierLatestDoneOptionType(),
		CurrentCriteriaOptionType:  collectedData.CurrentTierLatestCriteriaChangeOptionType(),
		CurrentCriteriaOptionTypes: collectedData.CurrentTierLatestCriteriaChangeOptionTypes(),
	}, nil
}

func (s *Service) GetDetailsForCx(ctx context.Context, req *tieringPb.GetDetailsForCxRequest) (*tieringPb.GetDetailsForCxResponse, error) {
	actorId := req.GetActorId()
	// make parallel calls to fetch details related to cx
	currentTier, mntHistories, graceTimestamp, coolOffTimestamp, isRewardsAbuserUser, tieringPitchResp, gatherDataErr := s.gatherDataForCx(ctx, actorId)
	if gatherDataErr != nil {
		logger.Error(ctx, "error in gathering data for cx", zap.Error(gatherDataErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.GetDetailsForCxResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in gathering data for cx"),
		}, nil
	}
	curExtTier, mmtExtHistories, tierMovementCriterias, conversionErr := s.convertCxDetailsToExternal(currentTier, mntHistories, tieringPitchResp)
	if conversionErr != nil {
		logger.Error(ctx, "error in gathering data for cx", zap.Error(conversionErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.GetDetailsForCxResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in gathering data for cx"),
		}, nil
	}

	// Get AMB-related data
	currentAmb, requiredAmb, shortfall, ambHistory, ambErr := s.getAMBDetailsForCx(ctx, actorId)
	if ambErr != nil {
		logger.Error(ctx, "error getting AMB details", zap.Error(ambErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		// Continue without AMB data, still return other information
	}

	return &tieringPb.GetDetailsForCxResponse{
		Status:                rpcPb.StatusOk(),
		CurrentTier:           curExtTier,
		IsUserInGrace:         graceTimestamp != nil,
		GracePeriodTill:       graceTimestamp,
		IsUserInCoolOff:       coolOffTimestamp != nil,
		CoolOffPeriodTill:     coolOffTimestamp,
		MovementHistories:     mmtExtHistories,
		IsARewardsAbuserUser:  isRewardsAbuserUser,
		TierMovementCriterias: tierMovementCriterias,
		CurrentAmb:            currentAmb,
		RequiredAmb:           requiredAmb,
		Shortfall:             shortfall,
		AmbHistory:            ambHistory,
	}, nil
}

func (s *Service) convertCxDetailsToExternal(currentTier tieringEnumPb.Tier, mntHistories []*tieringPb.TierMovementHistory, tieringPitchResp *tieringPb.GetTieringPitchV2Response) (
	tieringExtPb.Tier, []*tieringExtPb.TierMovementHistory, *tieringExtPb.TierMovementCriterias, error,
) {
	curExtTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(currentTier)
	if conversionErr != nil {
		return 0, nil, nil, errors.Wrap(conversionErr, "error converting internal current tier to external tier")
	}
	var extMntHistories []*tieringExtPb.TierMovementHistory
	for _, history := range mntHistories {
		fromExtTier, fromConversionErr := tiermappings.GetExternalTierFromInternalTier(history.GetFromTier())
		if fromConversionErr != nil {
			return 0, nil, nil, errors.Wrap(fromConversionErr, "error converting internal fromTier to external tier")
		}
		toExtTier, toConversionErr := tiermappings.GetExternalTierFromInternalTier(history.GetToTier())
		if toConversionErr != nil {
			return 0, nil, nil, errors.Wrap(toConversionErr, "error converting internal toTier to external tier")
		}
		extMntHistories = append(extMntHistories, &tieringExtPb.TierMovementHistory{
			Id:           history.GetId(),
			FromTier:     fromExtTier,
			ToTier:       toExtTier,
			CreatedAt:    history.GetCreatedAt(),
			MovementType: history.GetMovementType().String(),
		})
	}
	extTierMovementCriteria := &tieringExtPb.TierMovementCriterias{
		CurrentCriteriaOptionTypes: tieringPitchResp.GetCurrentCriteriaOptionTypes(),
		EntryCriteriaOptionType:    tieringPitchResp.GetEntryCriteriaOptionType(),
	}
	return curExtTier, extMntHistories, extTierMovementCriteria, nil
}

func (s *Service) gatherDataForCx(ctx context.Context, actorId string) (tieringEnumPb.Tier, []*tieringPb.TierMovementHistory,
	*timestampPb.Timestamp, *timestampPb.Timestamp, bool, *tieringPb.GetTieringPitchV2Response, error,
) {
	gatherDataErrGrp, gCtx := errgroup.WithContext(ctx)
	// get current tier
	var currentTier tieringEnumPb.Tier
	gatherDataErrGrp.Go(func() error {
		var getCurTierErr error
		currentTier, getCurTierErr = s.dataProcessor.GetCurrentTierDefaultToBaseTier(gCtx, actorId)
		if getCurTierErr != nil {
			return errors.Wrap(getCurTierErr, "error fetching current tier for actor")
		}
		return nil
	})
	// get last N movements for the actor
	var mmtHistories []*tieringPb.TierMovementHistory
	gatherDataErrGrp.Go(func() error {
		var getMmtHistoryErr error
		mmtHistories, getMmtHistoryErr = s.tierMovementHistoryDao.GetByActorId(gCtx, actorId, s.gconf.CxMovementHistoryLimit())
		if getMmtHistoryErr != nil {
			return errors.Wrap(getMmtHistoryErr, "error fetching last N movement histories for actor")
		}
		return nil
	})
	// check if user in grace
	// if in grace fetch grace timestamp for the user
	var graceTimestamp *timestampPb.Timestamp
	gatherDataErrGrp.Go(func() error {
		isUserInGrace, graceDetails, graceCheckErr := s.tierTimelineManager.IsUserInGracePeriod(ctx, actorId)
		if graceCheckErr != nil {
			return errors.Wrap(graceCheckErr, "error checking if user is in grace or not")
		}
		if isUserInGrace {
			graceTimestamp = graceDetails.GetMovementTimestamp()
		}
		return nil
	})
	// check if user in cool off
	// if in grace fetch cool off timestamp for the user
	var coolOffTimestamp *timestampPb.Timestamp
	gatherDataErrGrp.Go(func() error {
		willUserBeInCoolOff, willCoolOffCheckErr := s.tierTimelineManager.WillUserBeInCooloff(ctx, actorId)
		if willCoolOffCheckErr != nil {
			return errors.Wrap(willCoolOffCheckErr, "error checking if user will be in cool off or not")
		}
		if willUserBeInCoolOff {
			var getUpgradeDetailsErr error
			coolOffTimestamp, _, getUpgradeDetailsErr = s.tierTimelineManager.GetUpgradeDetails(ctx, actorId)
			if getUpgradeDetailsErr != nil {
				return errors.Wrap(getUpgradeDetailsErr, "error fetching upgrade details for the actor")
			}
		}
		return nil
	})
	// get tiering pitch
	tieringPitchResp := &tieringPb.GetTieringPitchV2Response{}
	gatherDataErrGrp.Go(func() error {
		var err error
		tieringPitchResp, err = s.GetTieringPitchV2(gCtx, &tieringPb.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if getPitchDetailsErr := epifigrpc.RPCError(tieringPitchResp, err); getPitchDetailsErr != nil {
			if tieringPitchResp.GetStatus().GetCode() == uint32(tieringPb.GetTierAtTimeResponse_DISABLED) {
				return feTieringErrors.ErrTieringDisabled
			}
			return errors.Wrap(getPitchDetailsErr, "error getting tiering pitch details")
		}
		return nil
	})
	gatherDataErr := gatherDataErrGrp.Wait()
	if gatherDataErr != nil {
		return 0, nil, nil, nil, false, nil, gatherDataErr
	}

	isARewardsAbuserUser, _ := s.tierTimelineManager.IsARewardAbuserUser(ctx, actorId)
	return currentTier, mmtHistories, graceTimestamp, coolOffTimestamp, isARewardsAbuserUser, tieringPitchResp, nil
}

// nolint:dupl
func (s *Service) OverrideGracePeriod(ctx context.Context, req *tieringPb.OverrideGracePeriodRequest) (*tieringPb.OverrideGracePeriodResponse, error) {
	actorId := req.GetActorId()
	overrideErr := s.tierTimelineManager.OverrideGracePeriod(ctx, actorId, req.GetOverrideTimestamp())
	if overrideErr != nil {
		logger.Error(ctx, "error in overriding grace period", zap.Error(overrideErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.OverrideGracePeriodResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in overriding grace period"),
		}, nil
	}
	return &tieringPb.OverrideGracePeriodResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// nolint:dupl
func (s *Service) OverrideCoolOffPeriod(ctx context.Context, req *tieringPb.OverrideCoolOffPeriodRequest) (*tieringPb.OverrideCoolOffPeriodResponse, error) {
	actorId := req.GetActorId()
	overrideErr := s.tierTimelineManager.OverrideCoolOffPeriod(ctx, actorId, req.GetOverrideTimestamp())
	if overrideErr != nil {
		logger.Error(ctx, "error in overriding cool off period", zap.Error(overrideErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.OverrideCoolOffPeriodResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in overriding grace period"),
		}, nil
	}
	fromTier, toTier, orchestrateErr := s.tieringOrchestrator.OrchestrateTierMovement(ctx, actorId, tieringEnumPb.Provenance_PROVENANCE_MANUAL_OVERRIDE, &tieringPb.TierMovementReason{})
	if orchestrateErr != nil {
		if helper.IsIgnorableOrchestratorError(orchestrateErr) {
			logger.Error(ctx, "got ignorable error in Manual override flow", zap.Error(orchestrateErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringPb.OverrideCoolOffPeriodResponse{
				Status: rpcPb.StatusAbortedWithDebugMsg(orchestrateErr.Error()),
			}, nil
		}
		logger.Error(ctx, "error orchestrating tier movement in Manual override flow", zap.Error(orchestrateErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.OverrideCoolOffPeriodResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error orchestrating tier movement"),
		}, nil
	}
	movementType, _ := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	logger.Info(ctx, "successfully orchestrated tier movement in Manual override flow", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any(logger.FROM_TIER, fromTier), zap.Any(logger.TO_TIER, toTier), zap.Any(logger.PROVENANCE, tieringEnumPb.Provenance_PROVENANCE_MANUAL_OVERRIDE), zap.Any(logger.MOVEMENT_TYPE, movementType))
	return &tieringPb.OverrideCoolOffPeriodResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) GetConfigParams(ctx context.Context, req *tieringPb.GetConfigParamsRequest) (*tieringPb.GetConfigParamsResponse, error) {
	var regularTierEnabled, isMultipleWaysEnabled bool
	var regularTierEnabledErr, isMultipleWaysErr error

	if req.GetActorId() != "" {
		var err error
		ctx, err = s.dataProcessor.EnrichCtxForFeatureEvaluation(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error enriching context for feature evaluation", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()))
			return &tieringPb.GetConfigParamsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error enriching context for feature evaluation"),
			}, nil
		}

		regularTierEnabled, regularTierEnabledErr = s.releaseEvaluator.Evaluate(ctx, pkgRelease.NewCommonConstraintData(typesv2.Feature_FEATURE_REGULAR_TIER).WithActorId(req.GetActorId()))
		if regularTierEnabledErr != nil {
			logger.Error(ctx, "failed to regular tier feature", zap.Error(regularTierEnabledErr))
			return &tieringPb.GetConfigParamsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to evaluate regular tier feature"),
			}, nil
		}

		isMultipleWaysEnabled, isMultipleWaysErr = s.dataProcessor.IsMultipleWaysToEnterTierEnabled(ctx, req.GetActorId())
		if isMultipleWaysErr != nil {
			logger.Error(ctx, "error getting multiple ways to enter tier flag", zap.Error(isMultipleWaysErr), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()))
			return &tieringPb.GetConfigParamsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting multiple ways to enter tier flag"),
			}, nil
		}
	}

	return &tieringPb.GetConfigParamsResponse{
		Status:                       rpcPb.StatusOk(),
		DowngradeWindowDuration:      durationpb.New(s.gconf.DowngradeWindowDuration(ctx)),
		GraceWindowDuration:          durationpb.New(s.gconf.GraceWindowDuration(ctx)),
		GraceInitialWindowDuration:   durationpb.New(s.gconf.GraceInitialWindowDuration(ctx)),
		IsRegularTierEnabledForActor: regularTierEnabled,
		RegularTierConfigParams: &tieringPb.RegularTierConfigParams{
			MinBalanceForRegularTier:        &gmoney.Money{CurrencyCode: "INR", Units: int64(s.gconf.MinAvgMonthlyBalanceForRegularTier())},
			MinBalancePenaltyForRegularTier: &gmoney.Money{CurrencyCode: "INR", Units: int64(s.gconf.MinBalancePenaltyForRegularTier())},
		},
		IsMultipleWaysToEnterTieringEnabledForActor: isMultipleWaysEnabled,
		CriteriaSegmentExclusionConfigs: &tieringPb.CriteriaSegmentExclusionConfigs{
			SalaryB2CExcludedSegments: s.gconf.CriteriaSegmentExclusions().SalaryB2CExcludedSegments().ToStringArray(),
			AaSalaryExcludedSegments:  s.gconf.CriteriaSegmentExclusions().AaSalaryExcludedSegments().ToStringArray(),
		},
	}, nil
}

func (s *Service) EvaluateTierForActor(ctx context.Context, req *tieringPb.EvaluateTierForActorRequest) (*tieringPb.EvaluateTierForActorResponse, error) {
	actorId := req.GetActorId()
	activeCriteria, getCriteriaErr := s.criteriaManager.GetActiveCriteriaForActor(ctx, actorId)
	if getCriteriaErr != nil {
		logger.Error(ctx, "error getting active criteria for actor", zap.Error(getCriteriaErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.EvaluateTierForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting active criteria for actor"),
		}, nil
	}

	currentTier, getCurrentTierErr := s.getCurrentTierForActor(ctx, actorId)
	if getCurrentTierErr != nil {
		return &tieringPb.EvaluateTierForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(errors.Wrap(getCurrentTierErr, "Error Evaluating Tier for Actor").Error()),
		}, nil
	}

	evaluateTierResp, evaluateErr := s.tierEvaluator.Evaluate(ctx, actorId, currentTier, activeCriteria, options.GetEvaluatorOptions(req.GetOptions())...)
	if evaluateErr != nil {
		logger.Error(ctx, "error evaluating tier for the actor", zap.Error(evaluateErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.EvaluateTierForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error evaluating tier for the actor"),
		}, nil
	}
	evaluatedTierExt, conversionErr := tiermappings.GetExternalTierFromInternalTier(evaluateTierResp.GetEvaluatedTier())
	if conversionErr != nil {
		logger.Error(ctx, "error converting external tier to internal tier", zap.Error(conversionErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.EvaluateTierForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error converting external tier to internal tier"),
		}, nil
	}

	return &tieringPb.EvaluateTierForActorResponse{
		Status:              rpcPb.StatusOk(),
		EvaluatedTier:       evaluatedTierExt,
		CriteriaReferenceId: activeCriteria.GetId(),
		EvaluatedTierSatisfiedCriteriaOptionTypes: evaluateTierResp.GetSatisfiedCriteriaOptionTypes(),
	}, nil
}

// getCurrentTierForActor returns current tier for an actor and returns TIER_UNSPECIFIED if not current tier found for an actor
func (s *Service) getCurrentTierForActor(ctx context.Context, actorId string) (tieringEnumPb.Tier, error) {
	var currentTier tieringEnumPb.Tier
	actorTierInfo, actorTierInfoErr := s.actorTierInfoDao.Get(ctx, actorId, tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TIER)
	if actorTierInfoErr != nil {
		if !(errors.Is(actorTierInfoErr, epifierrors.ErrRecordNotFound)) {
			return tieringEnumPb.Tier_TIER_UNSPECIFIED, errors.Wrap(actorTierInfoErr, "error getting current tier for the actor")
		}
		currentTier = tieringEnumPb.Tier_TIER_UNSPECIFIED
	} else {
		currentTier = actorTierInfo.GetTier()
	}
	return currentTier, nil
}

// GetCurrentTierForActor RPC method calls the private method
// GetCurrentTierForActor gets the current tier stored for an actor
// Returns base tier if actor is not tiered yet
// INTERNAL in case of any server error
func (s *Service) GetCurrentTierForActor(ctx context.Context, req *tieringPb.GetCurrentTierForActorRequest) (*tieringPb.GetCurrentTierForActorResponse, error) {
	tier, currentTierErr := s.getCurrentTierForActor(ctx, req.GetActorId())
	if currentTierErr != nil {
		// if actor is not already tiered, return base tier for actor
		if errors.Is(epifierrors.ErrRecordNotFound, currentTierErr) {
			logger.Debug(ctx, "actor is not tiered ", zap.Error(currentTierErr))
			baseTier, baseTierErr := s.getExternalBaseTierForActor(ctx, req.GetActorId())
			if baseTierErr != nil {
				logger.Error(ctx, "error getting base tier for actor", zap.Error(baseTierErr))
				return &tieringPb.GetCurrentTierForActorResponse{
					Status: rpcPb.StatusInternalWithDebugMsg("error getting base tier for actor"),
				}, nil
			}
			return &tieringPb.GetCurrentTierForActorResponse{
				Status: rpcPb.StatusOk(),
				Tier:   baseTier,
			}, nil
		}
		logger.Error(ctx, "error getting latest tier movement history from dao", zap.Error(currentTierErr))
		return &tieringPb.GetCurrentTierForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting latest tier movement history from dao"),
		}, nil
	}
	// ignoring, as tier will be 'unspecified' in case of error
	currentTier, _ := tiermappings.GetExternalTierFromInternalTier(tier)
	var actorBaseTierErr error
	// if actor tier is 'unspecified', return the base tier of the actor
	if currentTier == tieringExtPb.Tier_TIER_UNSPECIFIED {
		currentTier, actorBaseTierErr = s.getExternalBaseTierForActor(ctx, req.GetActorId())
		if actorBaseTierErr != nil {
			logger.Error(ctx, "error getting actor's base tier for actor", zap.Error(actorBaseTierErr))
			return &tieringPb.GetCurrentTierForActorResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting actor's base tier for actor"),
			}, nil
		}
	}
	return &tieringPb.GetCurrentTierForActorResponse{
		Status: rpcPb.StatusOk(),
		Tier:   currentTier,
	}, nil
}

// CheckIfActorIsEligibleForCashbackReward determines whether an actor is eligible for a cashback reward.
//
// Eligibility Criteria:
// 1. The actor must have been part of the input tier for the month provided in the request
// 2. By Default reward month is the previous month
// 3. The actor must not belong to an ineligible segment for the given tier.
//
// Returns:
// - A response indicating whether the actor is eligible for cashback rewards.
// - An error if any issue occurs during processing.
func (s *Service) CheckIfActorIsEligibleForCashbackReward(ctx context.Context, req *tieringPb.CheckIfActorIsEligibleForCashbackRewardRequest) (*tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse, error) {
	actorId := req.GetActorId()

	// by default takes the reward month as the previous month
	if req.GetRewardMonth() == nil {
		req.RewardMonth = timestampPb.New(time.Now().AddDate(0, -1, 0).In(pkgDateTime.IST))
	}
	// 1. Check if the actor was in the input tier for the full month
	monthStart := pkgDateTime.StartOfMonth(req.GetRewardMonth().AsTime().In(pkgDateTime.IST))
	monthEnd := pkgDateTime.EndOfMonth(req.GetRewardMonth().AsTime().In(pkgDateTime.IST))
	distinctTiers, distinctTierErr := s.dataProcessor.GetActorDistinctTier(ctx, actorId, monthStart, monthEnd)
	if distinctTierErr != nil {
		logger.Error(ctx, "error in getting distinct tier for actor", zap.Error(distinctTierErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in getting distinct tiers for actor"),
		}, nil
	}
	if !lo.Contains(distinctTiers, req.GetTier()) {
		logger.Info(ctx, "actor was not part of the input tier for the month. Thus, not eligible for reward.", zap.String(logger.TIER, req.GetTier().String()), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
			Status:     rpcPb.StatusOkWithDebugMsg("actor was not part of the input tier for the month"),
			IsEligible: false,
		}, nil
	}

	// 2. Check if abuser check is bypassed
	val, err := s.redisClient.Exists(ctx, helper.GetAbuserCheckBypassKey(actorId, req.GetTier())).Result()
	if err != nil {
		logger.WarnWithCtx(ctx, "failed to check if user is part of abuser check bypass. gracefully proceeding", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
	}
	if val == 1 {
		logger.Info(ctx, "abuser check is bypassed for the user", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.TIER, req.GetTier().String()))
		return &tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
			Status:     rpcPb.StatusOk(),
			IsEligible: true,
		}, nil
	}

	// 3. Check if the actor is flagged as abuser
	var isActorInEligible bool

	if s.gconf.EnableTieringAbuserDbCheck() {
		// Use tiering_abuser table for abuser check
		isActorInEligible, err = s.checkAbuserFromDatabase(ctx, actorId, req.GetTier(), req.GetRewardMonth().AsTime().In(pkgDateTime.IST))
		if err != nil {
			logger.Error(ctx, "error checking abuser status using tiering_abuser table", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error checking abuser status"),
			}, nil
		}
	} else {
		// Use segment-based abuser check (existing logic)
		segmentId, ok := s.gconf.TierToIneligibleActorSegmentIdMap().Load(req.GetTier().String())
		if !ok {
			logger.Info(ctx, "segment ID not found for the given tier, returning true", zap.String(logger.TIER, req.GetTier().String()), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
				Status:     rpcPb.StatusOk(),
				IsEligible: true,
			}, nil
		}

		isMemberResp, isMemberErr := s.segmentationClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: []string{segmentId},
		})
		if rpcErr := epifigrpc.RPCError(isMemberResp, isMemberErr); rpcErr != nil {
			logger.Error(ctx, "error in segmentation client", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return &tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in segmentation client"),
			}, nil
		}
		segmentStatus := isMemberResp.GetSegmentMembershipMap()[segmentId].GetSegmentStatus()
		if segmentStatus != segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND {
			logger.Error(ctx, "unexpected segment status", zap.Any(logger.STATUS, segmentStatus))
			return &tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("unexpected segment status"),
			}, nil
		}
		isActorInEligible = isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember()
	}

	logger.Info(ctx, "eligibility of actor for rewards cashback", zap.Any("isEligible", !isActorInEligible), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.TIER, req.GetTier().String()))

	// 4. Emit event if abuser flagged, else not
	if isActorInEligible {
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(
				epificontext.WithEventAttributesV2(ctx),
				tevents.NewFlaggedTierAbuser(
					actorId,
					monthStart.Format("Jan-2006"),
					req.GetTier().String(),
					// null for now , to be populated later when we receive enriched data along with the
					"",
					time.Now(),
				),
			)
		})
	}
	return &tieringPb.CheckIfActorIsEligibleForCashbackRewardResponse{
		Status:     rpcPb.StatusOk(),
		IsEligible: !isActorInEligible,
	}, nil
}

// checkAbuserFromDatabase checks if an actor is an abuser for a specific tier and month using the tiering_abuser table
func (s *Service) checkAbuserFromDatabase(ctx context.Context, actorId string, requestTier tieringExtPb.Tier, rewardMonth time.Time) (bool, error) {
	// Convert external tier to internal tier for comparison with the table
	internalTier, err := tiermappings.GetInternalTierFromExternalTier(requestTier)
	if err != nil {
		logger.Error(ctx, "failed to convert external tier to internal tier", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("tier", requestTier.String()), zap.Error(err))
		return false, errors.Wrap(err, "failed to convert external tier to internal tier")
	}

	// Get abuser records for the actor and month
	abuserRecords, _, err := s.tieringAbuserDao.GetByActorIdAndMonth(ctx, actorId, rewardMonth, nil, 1)
	if err != nil {
		// Error occurred while fetching records
		return false, errors.Wrap(err, "failed to get abuser records from tiering_abuser table")
	}

	// Check if the requested tier exists in any of the abuser entries
	for _, abuserRecord := range abuserRecords {
		if abuserRecord.GetTierInfo() != nil {
			for _, entry := range abuserRecord.GetTierInfo().GetEntries() {
				if entry.GetTier() == internalTier {
					logger.Info(ctx, "actor found in abuser record for the requested tier", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("tier", requestTier.String()), zap.String("internalTier", internalTier.String()), zap.String("month", rewardMonth.Format("Jan-2006")))
					return true, nil
				}
			}
		}
	}

	// Tier not found in abuser records, actor is not abuser for this tier
	logger.Info(ctx, "actor not found in abuser record for the requested tier", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("tier", requestTier.String()), zap.String("internalTier", internalTier.String()), zap.String("month", rewardMonth.Format("Jan-2006")))
	return false, nil
}

func (s *Service) GetTierTimeRangesForActor(ctx context.Context, req *tieringPb.GetTierTimeRangesForActorRequest) (*tieringPb.GetTierTimeRangesForActorResponse, error) {
	actorId := req.GetActorId()
	tierRangeMap, getRangeErr := s.dataProcessor.GetTierTimeRangesForActor(ctx, actorId, req.GetTiers(), req.GetFilterFrom().AsTime())
	if getRangeErr != nil {
		logger.Error(ctx, "failed to get tier time range for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(getRangeErr))
		return &tieringPb.GetTierTimeRangesForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(getRangeErr.Error()),
		}, nil
	}

	tierRangeMapForResp := make(map[string]*tieringPb.TimeRanges)
	for key, val := range tierRangeMap {
		tierRangeMapForResp[key.String()] = &tieringPb.TimeRanges{
			TimeRanges: val,
		}
	}

	return &tieringPb.GetTierTimeRangesForActorResponse{
		Status:            rpcPb.StatusOk(),
		TierTimeRangesMap: tierRangeMapForResp,
	}, nil
}

func (s *Service) isEligibleForAASalRewardsForLastMonth(ctx context.Context, actorId string) (bool, error) {

	regDetails, err := s.salaryProgramClient.GetRegistrationDetails(ctx, &salaryprogramPb.GetRegistrationDetailsRequest{
		ActorId:  actorId,
		FlowType: salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_AA_SALARY,
	})
	if rpcErr := epifigrpc.RPCError(regDetails, err); rpcErr != nil {
		logger.Error(ctx, "error getting registration details", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return false, rpcErr
	}

	if regDetails.GetRegistrationStatus() != salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		logger.Info(ctx, "registration not completed", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}

	if regDetails.GetRegistrationId() == "" {
		logger.Error(ctx, "registrationId for completed registration cannot be empty", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, errors.New("registrationId for completed registration cannot be empty")
	}

	getSalaryProgramActivationHistoriesReq := &salaryprogramPb.GetSalaryProgramActivationHistoriesRequest{
		RegistrationId: regDetails.GetRegistrationId(),
		ActivationType: salaryprogramPb.SalaryActivationType_AA_SALARY_ACTIVATION,
		PageContext: &rpcPb.PageContextRequest{
			PageSize: 5,
		},
	}

	getActivationHistoriesRes, getActivationHistoriesErr := s.salaryProgramClient.GetSalaryProgramActivationHistories(ctx, getSalaryProgramActivationHistoriesReq)
	if rpcErr := epifigrpc.RPCError(getActivationHistoriesRes, getActivationHistoriesErr); rpcErr != nil {
		logger.Error(ctx, "error getting latest salary activation details", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return false, rpcErr
	}
	if len(getActivationHistoriesRes.GetActivationHistories()) == 0 {
		return false, nil
	}

	previousMonth, previousYear := pkgDateTime.AddNMonthsToMonth(pkgDateTime.GetMonthYear(time.Now()).GetMonth(), pkgDateTime.GetMonthYear(time.Now()).GetYear(), -1)
	activationHistoriesList := getActivationHistoriesRes.GetActivationHistories()
	for _, activationHistory := range activationHistoriesList {
		activationMonthYear, activationMonthYearParseErr := pkgDateTime.ParseMonthYear(activationHistory.GetRewardActivatedMonthYear(), pkgDateTime.MM_YYYY)
		if activationMonthYearParseErr != nil {
			continue // activationMonthYear is not backfilled for old activations
		}

		// if activation month match current month
		if previousMonth == activationMonthYear.Month && previousYear == activationMonthYear.Year {
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) GetActorScreenInteractionDetails(ctx context.Context, req *tieringPb.GetActorScreenInteractionDetailsRequest) (*tieringPb.GetActorScreenInteractionDetailsResponse, error) {
	res := &tieringPb.GetActorScreenInteractionDetailsResponse{}

	actorId := req.GetActorId()
	screen := req.GetScreen()
	requestType := req.GetRequestType()

	switch requestType {
	case tieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_GET:
		{
			actorScreenInteraction, actorScreenInteractionErr := s.actorScreenInteractionDao.GetByActorIdAndScreen(ctx, actorId, screen)
			if actorScreenInteractionErr != nil {
				if errors.Is(actorScreenInteractionErr, epifierrors.ErrRecordNotFound) {
					res.Status = rpcPb.StatusRecordNotFound()
					return res, nil
				}
				logger.Error(ctx, "error getting actor page details", zap.Error(actorScreenInteractionErr), zap.String(logger.ACTOR_ID_V2, actorId))
				return nil, errors.Wrap(actorScreenInteractionErr, "error getting actor page details")
			}
			if actorScreenInteraction.GetStatus() == tieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_VISITED {
				res.Status = rpcPb.StatusOk()
				res.Response = tieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_VISITED
			}
			return res, nil
		}
	case tieringPb.GetActorScreenInteractionDetailsRequest_REQUEST_TYPE_ADD:
		{
			_, addActorScreenInteractionErr := s.actorScreenInteractionDao.Create(ctx, &tieringPb.ActorScreenInteraction{
				ActorId: actorId,
				Screen:  screen,
				Status:  tieringEnumPb.ActorScreenInteractionStatus_ACTOR_SCREEN_INTERACTION_STATUS_VISITED,
			})
			if addActorScreenInteractionErr != nil {
				if errors.Is(addActorScreenInteractionErr, epifierrors.ErrDuplicateEntry) {
					res.Status = rpcPb.StatusAlreadyExists()
					return res, nil
				}
				logger.Error(ctx, "error adding actor page details", zap.Error(addActorScreenInteractionErr), zap.String(logger.ACTOR_ID_V2, actorId))
				return nil, errors.Wrap(addActorScreenInteractionErr, "error adding actor page details")
			}
			res.Status = rpcPb.StatusOk()
			return res, nil
		}
	default:
		logger.Error(ctx, fmt.Sprintf("Unsupported request type %s", requestType))
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	}
}

func (s *Service) GetActorDistinctTiers(ctx context.Context, req *tieringPb.GetActorDistinctTiersRequest) (*tieringPb.GetActorDistinctTiersResponse, error) {
	// restrict rpc call with time since to 3 year to avoid overloading the system
	if !req.GetTimeSince().IsValid() || req.GetTimeSince().AsTime().Before(time.Now().AddDate(-3, 0, 0)) {
		logger.Error(ctx, "invalid time since", zap.Any(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any("timeSince", req.GetTimeSince().AsTime()))
		return &tieringPb.GetActorDistinctTiersResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	distinctTiers, getDistinctTierErr := s.dataProcessor.GetActorDistinctTier(ctx, req.GetActorId(), req.GetTimeSince().AsTime(), time.Now())
	if getDistinctTierErr != nil {
		logger.Error(ctx, "error getting distinct tiers for actor", zap.Error(getDistinctTierErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &tieringPb.GetActorDistinctTiersResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &tieringPb.GetActorDistinctTiersResponse{
		Status:        rpcPb.StatusOk(),
		DistinctTiers: distinctTiers,
	}, nil
}

// IsUserEligibleForRewards checks if a user is eligible for rewards across different tiers (Plus, Infinite, Prime)
// for a specific date. It performs concurrent eligibility checks for each tier.
//
// Parameters:
//   - ctx: Context for the request
//   - req: Request containing actorId and date to check eligibility for
//
// Returns:
//   - Response containing eligibility status for each tier
//   - Error if any of the eligibility checks fail
//
// The function runs three concurrent checks using errgroup and aggregates the results.
// If any check fails, the entire operation is considered failed.
//
// This is to be used only for CX agent purposes as it is checking in presto for segment membership.
func (s *Service) IsUserEligibleForRewards(ctx context.Context, req *tieringPb.IsUserEligibleForRewardsRequest) (*tieringPb.IsUserEligibleForRewardsResponse, error) {
	var (
		isPlusEligible, isInfiniteEligible, isPrimeEligible bool
		plusErr, infiniteErr, primeErr                      error
	)
	actorId := req.GetActorId()
	g, gCtx := errgroup.WithContext(ctx)

	// Check Plus tier eligibility
	g.Go(func() error {
		isPlusEligible, plusErr = s.checkTierRewardEligibilityOnDate(gCtx, actorId, tieringExtPb.Tier_TIER_FI_PLUS, req.GetDate())
		return plusErr
	})

	// Check Infinite tier eligibility
	g.Go(func() error {
		isInfiniteEligible, infiniteErr = s.checkTierRewardEligibilityOnDate(gCtx, actorId, tieringExtPb.Tier_TIER_FI_INFINITE, req.GetDate())
		return infiniteErr
	})

	// Check Prime tier eligibility
	g.Go(func() error {
		isPrimeEligible, primeErr = s.checkTierRewardEligibilityOnDate(gCtx, actorId, tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, req.GetDate())
		return primeErr
	})

	if err := g.Wait(); err != nil {
		logger.Error(ctx, "error checking tier reward eligibility on date", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.IsUserEligibleForRewardsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error checking tier reward eligibility on date"),
		}, nil
	}

	return &tieringPb.IsUserEligibleForRewardsResponse{
		Status: rpcPb.StatusOk(),
		TierToEligibilityStatus: []*tieringPb.IsUserEligibleForRewardsResponse_TierToEligibilityStatus{
			{
				TierName:   tieringExtPb.Tier_TIER_FI_PLUS,
				IsEligible: isPlusEligible,
			},
			{
				TierName:   tieringExtPb.Tier_TIER_FI_INFINITE,
				IsEligible: isInfiniteEligible,
			},
			{
				TierName:   tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
				IsEligible: isPrimeEligible,
			},
		},
	}, nil
}

// checkTierRewardEligibilityOnDate determines if a user is eligible for rewards in a specific tier on a given date
// by checking if they belong to an ineligible segment or are flagged as abuser in database depending on whether
// EnableTieringAbuserDbCheck flag is disabled or enabled .
//
// Parameters:
//   - ctx: Context for the request
//   - actorId: ID of the user to check eligibility for
//   - tier: The tier to check eligibility for (Plus, Infinite, Prime)
//   - date: The date for which to check eligibility
//
// Returns:
//   - bool: true if the user is eligible (not in ineligible segment/not flagged as abuser), false otherwise
//   - error: if there's an error during the check (e.g., segment lookup failure, API error, database error)
//
// The function works by:
// 1. Checking if database-based abuser check is enabled via feature flag
// 2. If enabled, checking abuser status from tiering_abuser table
// 3. If disabled, falling back to segment-based check (existing logic)
// 4. Returning true if the user is NOT flagged as abuser/ineligible
func (s *Service) checkTierRewardEligibilityOnDate(ctx context.Context, actorId string, tier tieringExtPb.Tier, date *date.Date) (bool, error) {
	var isActorInEligible bool
	var err error

	if s.gconf.EnableTieringAbuserDbCheck() {
		isActorInEligible, err = s.checkAbuserFromDatabase(ctx, actorId, tier, pkgDateTime.DateToTimeV2(date, pkgDateTime.IST))
		if err != nil {
			logger.Error(ctx, "error checking abuser status using tiering_abuser table", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.TIER, tier.String()))
			return false, err
		}
	} else {
		// Use segment-based abuser check (existing logic)
		segmentId, ok := s.gconf.TierToIneligibleActorSegmentIdMap().Load(tier.String())
		if !ok {
			logger.Error(ctx, "segment id not found for tier", zap.String(logger.TIER, tier.String()))
			return false, errors.New("segment id not found for tier " + tier.String())
		}

		// Check if user is in any ineligible segment
		getSegmentResp, err := s.segmentationClient.GetUsersInSegmentOnDate(ctx, &segmentPb.GetUsersInSegmentOnDateRequest{
			Date:      date,
			SegmentId: segmentId,
			ActorId:   actorId,
		})
		if rpcErr := epifigrpc.RPCError(getSegmentResp, err); rpcErr != nil {
			logger.Error(ctx, "error in GetUsersInSegmentOnDate", zap.String(logger.TIER, tier.String()), zap.Error(rpcErr))
			return false, rpcErr
		}

		// User is ineligible if they are in the ineligible segment
		for _, ineligibleActor := range getSegmentResp.GetActorIds() {
			if ineligibleActor == actorId {
				isActorInEligible = true
				break
			}
		}
	}

	logger.Info(ctx, "eligibility of actor for rewards", zap.Bool("isEligible", !isActorInEligible), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.TIER, tier.String()))

	// User is eligible if they are NOT flagged as abuser/ineligible
	return !isActorInEligible, nil
}
