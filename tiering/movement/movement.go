// nolint:funlen
//
//go:generate mockgen -source=movement.go -destination=../test/mocks/mock_movement.go -package=mocks
package movement

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/tiering/comms"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/criteria"
	"github.com/epifi/gamma/tiering/dao"
	tieringErrors "github.com/epifi/gamma/tiering/errors"
	tieringEvents "github.com/epifi/gamma/tiering/events"
	"github.com/epifi/gamma/tiering/helper"
	"github.com/epifi/gamma/tiering/tiermappings"
	"github.com/epifi/gamma/tiering/timeline"
	"github.com/epifi/gamma/tiering/typedef"
)

// TierMovementManager to upgrade/downgrade tiers for a particular actor
// Upgrade and Downgrade made separate to support addition of custom logic in future
type TierMovementManager interface {
	Upgrade(ctx context.Context, actorId string, eligibleTierMovement *tieringPb.EligibleTierMovement, provenance tieringEnumPb.Provenance, reason *tieringPb.TierMovementReason) error
	Downgrade(ctx context.Context, actorId string, eligibleTierMovement *tieringPb.EligibleTierMovement, provenance tieringEnumPb.Provenance, reason *tieringPb.TierMovementReason) error
	IsEligibleForTierUpgrade(ctx context.Context, actorId string, tierFrom tieringEnumPb.Tier, tierTo tieringEnumPb.Tier) (bool, error)
	IsEligibleForTierDowngrade(ctx context.Context, actorId string, tierFrom tieringEnumPb.Tier, tierTo tieringEnumPb.Tier) (bool, error)
	CreateOrGetEligibility(ctx context.Context, actorId string, fromTier tieringEnumPb.Tier, toTier tieringEnumPb.Tier, evaluatorMeta *tieringPb.EvaluatorMeta) (*tieringPb.EligibleTierMovement, error)
}

type TierMovementManagerService struct {
	gconf                   *genconf.Config
	eligibleTierMovementDao dao.EligibleTierMovementDao
	actorTierInfoDao        dao.ActorTierInfoDao
	tierMmtHistoryDao       dao.TierMovementHistoryDao
	timelineManager         timeline.TierTimeline
	tierCriteriaManager     criteria.TierCriteria
	commsSender             comms.Sender
	eventBroker             events.Broker
	tierUpdatePub           typedef.TierUpdateEventExternalPublisher
	txnExecutor             storagev2.TxnExecutor
	segmentationClient      segmentPb.SegmentationServiceClient
}

func NewTierMovementManager(
	gconf *genconf.Config,
	eligibleTierMovementDao dao.EligibleTierMovementDao,
	actorTierInfoDao dao.ActorTierInfoDao,
	tierMmtHistoryDao dao.TierMovementHistoryDao,
	timelineManager timeline.TierTimeline,
	tierCriteriaManager criteria.TierCriteria,
	commsSender comms.Sender,
	eventBroker events.Broker,
	tierUpdatePub typedef.TierUpdateEventExternalPublisher,
	txnExecutor storagev2.TxnExecutor,
	segmentationClient segmentPb.SegmentationServiceClient,
) *TierMovementManagerService {
	return &TierMovementManagerService{
		gconf:                   gconf,
		eligibleTierMovementDao: eligibleTierMovementDao,
		actorTierInfoDao:        actorTierInfoDao,
		tierMmtHistoryDao:       tierMmtHistoryDao,
		timelineManager:         timelineManager,
		tierCriteriaManager:     tierCriteriaManager,
		commsSender:             commsSender,
		eventBroker:             eventBroker,
		tierUpdatePub:           tierUpdatePub,
		txnExecutor:             txnExecutor,
		segmentationClient:      segmentationClient,
	}
}

var _ TierMovementManager = &TierMovementManagerService{}

// enrollForTrial is the internal method which will be called by Upgrade method in a db txn block to enroll the user for trial period
func (t *TierMovementManagerService) enrollForTrialIfEligible(
	ctx context.Context,
	actorId string,
	provenance tieringEnumPb.Provenance,
	eligibleMmt *tieringPb.EligibleTierMovement,
	reason *tieringPb.TierMovementReason,
) error {
	// early return if not eligible for trial
	if !provenance.IsTrialProvenance() || eligibleMmt.GetMovementType() != tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE {
		return nil
	}

	trialTier, getTierErr := provenance.GetInternalTierFromTrialProvenance()
	if getTierErr != nil {
		return fmt.Errorf("error getting internal tier from trial provenance: %w", getTierErr)
	}

	if eligibleMmt.GetToTier() != trialTier {
		return nil
	}

	actorTierInfo, getAtiErr := t.actorTierInfoDao.Get(ctx, actorId)
	if getAtiErr != nil && !errors.Is(getAtiErr, epifierrors.ErrRecordNotFound) {
		return errors.Wrap(getAtiErr, "error getting actor tier info")
	}

	if actorTierInfo.GetTrialDetails().IsTrialPeriodActive() {
		// user should not reach this state as we do not show trials entry point if the user already has an active trial
		logger.WarnWithCtx(ctx, "actor already has an active trial, skip recording trial opt in flag", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil
	}

	trialConfig := t.gconf.TieringTrialConfig()
	if time.Until(trialConfig.TrialEndDate()) < trialConfig.MinimumTrialDuration() {
		// we do not show entry point to the user if time till trial end date is less than minimum trial duration
		// this should not happen as TrialEntryPointEndDate and TrialEndDate should be at-least MinimumTrialDuration apart
		logger.WarnWithCtx(ctx, "trial end date is before current time, skipping trial enrollment", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil
	}

	reason.SetUpgradeReason(tieringEnumPb.UpgradeReason_UPGRADE_REASON_TRIAL_PERIOD)
	trialDetails := &tieringPb.TrialDetails{
		OptedAt:        timestamppb.Now(),
		Tier:           trialTier,
		TrialStartTime: timestamppb.Now(),
		TrialEndTime:   timestamppb.New(trialConfig.TrialEndDate()),
	}

	switch {
	case errors.Is(getAtiErr, epifierrors.ErrRecordNotFound):
		var createErr error
		_, createErr = t.actorTierInfoDao.Create(ctx, &tieringPb.ActorTierInfo{
			ActorId:             actorId,
			MovementReferenceId: uuid.Nil.String(),
			CriteriaReferenceId: uuid.Nil.String(),
			TrialDetails:        trialDetails,
		})
		if createErr != nil {
			return fmt.Errorf("failed to create actor tier info: %w", createErr)
		}
	default:
		var updateErr error
		_, updateErr = t.actorTierInfoDao.Update(ctx, &tieringPb.ActorTierInfo{
			ActorId:      actorId,
			TrialDetails: trialDetails,
		}, []tieringPb.ActorTierInfoFieldMask{tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TRIAL_DETAILS})
		if updateErr != nil {
			return fmt.Errorf("failed to update actor tier info: %w", updateErr)
		}
	}

	return nil
}

// Upgrade the tier for an actor
// Takes eligibility id and provenance as a parameter
// Returns error if unsuccessful, nil otherwise
func (t *TierMovementManagerService) Upgrade(ctx context.Context, actorId string, eligibleTierMovement *tieringPb.EligibleTierMovement,
	provenance tieringEnumPb.Provenance, reason *tieringPb.TierMovementReason) error {
	// populate reason
	hanldeMovementErr := t.handleMovement(ctx, actorId, eligibleTierMovement, provenance, reason)
	if hanldeMovementErr != nil {
		return hanldeMovementErr
	}

	switch reason.GetUpgradeReason() {
	case tieringEnumPb.UpgradeReason_UPGRADE_REASON_AUTO_UPGRADE:
		logger.Info(ctx, "user auto upgraded to tier", zap.String(logger.TO_TIER, eligibleTierMovement.GetToTier().String()), zap.String(logger.ACTOR_ID_V2, actorId))
	case tieringEnumPb.UpgradeReason_UPGRADE_REASON_TRIAL_PERIOD:
		logger.Info(ctx, "user enrolled for trial period", zap.String(logger.TO_TIER, eligibleTierMovement.GetToTier().String()), zap.String(logger.ACTOR_ID_V2, actorId))
	default:
	}

	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		sendNotifErr := t.commsSender.SendNotification(ctx, actorId, tieringEnumPb.NotificationType_NOTIFICATION_TYPE_UPGRADE)
		if sendNotifErr != nil {
			logger.Error(ctx, "error sending tiering upgrade notification", zap.String(logger.ACTOR_ID_V2, actorId),
				zap.Error(sendNotifErr))
		} else {
			logger.Debug(ctx, "sent tiering upgrade notification", zap.String(logger.ACTOR_ID_V2, actorId))
		}
	})
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		t.eventBroker.AddToBatch(
			epificontext.WithEventAttributes(ctx),
			tieringEvents.NewUserUpgradedTiering(
				actorId,
				time.Now(),
				provenance.String(),
				eligibleTierMovement.GetFromTier().String(),
				eligibleTierMovement.GetToTier().String(),
				reason.GetUpgradeReason().String(),
			),
		)
	})
	return nil
}

// publishTierUpdateEvent creates tier update event and publishes to sns topic
func (t *TierMovementManagerService) publishTierUpdateEvent(ctx context.Context, actorId string, createdMovement *tieringPb.TierMovementHistory) error {
	fromTier, fromTierConversionErr := tiermappings.GetExternalTierFromInternalTier(createdMovement.GetFromTier())
	if fromTierConversionErr != nil {
		return errors.Wrap(fromTierConversionErr, "error converting fromTier to external tier")
	}
	toTier, toTierConversionErr := tiermappings.GetExternalTierFromInternalTier(createdMovement.GetToTier())
	if toTierConversionErr != nil {
		return errors.Wrap(fromTierConversionErr, "error converting toTier to external tier")
	}
	movementType, getMmtTypeErr := helper.GetMovementTypeFromStartAndEndTiers(createdMovement.GetFromTier(), createdMovement.GetToTier())
	if getMmtTypeErr != nil {
		return errors.Wrap(getMmtTypeErr, "error getting movement type from start and end tiers")
	}
	tierUpdateEvent := &tieringExtPb.TierUpdateEvent{
		EventId:           createdMovement.GetId(),
		ActorId:           actorId,
		FromTier:          fromTier,
		ToTier:            toTier,
		MovementType:      helper.ConvertInternalMovementTypeToExternal(movementType),
		EventTimestamp:    timestamppb.Now(),
		MovementTimestamp: createdMovement.GetCreatedAt(),
	}
	msgId, pubErr := t.tierUpdatePub.Publish(ctx, tierUpdateEvent)
	if pubErr != nil {
		return errors.Wrap(pubErr, "error publishing tier update event to topic")
	}
	logger.Debug(ctx, "successfully published event to topic",
		zap.String(logger.QUEUE_MESSAGE_ID, msgId),
		zap.String(logger.ACTOR_ID_V2, actorId),
		zap.String(logger.FROM_TIER, fromTier.String()),
		zap.String(logger.TO_TIER, toTier.String()),
		zap.Any("tier_update_event", tierUpdateEvent),
	)
	return nil
}

// Downgrade the tier for an actor
// Takes eligibility id and provenance as a parameter
// Returns error if unsuccessful, nil otherwise
func (t *TierMovementManagerService) Downgrade(ctx context.Context, actorId string, eligibleTierMovement *tieringPb.EligibleTierMovement,
	provenance tieringEnumPb.Provenance, reason *tieringPb.TierMovementReason) error {

	if !t.gconf.EnableTierDowngrades() {
		return tieringErrors.ErrTierDowngradeDisabled
	}

	// populate reason
	handleMovementErr := t.handleMovement(ctx, actorId, eligibleTierMovement, provenance, reason)
	if handleMovementErr != nil {
		return handleMovementErr
	}
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		sendNotifErr := t.commsSender.SendNotification(ctx, actorId, tieringEnumPb.NotificationType_NOTIFICATION_TYPE_DOWNGRADE)
		if sendNotifErr != nil {
			logger.Error(ctx, "error sending tiering downgrade notification", zap.String(logger.ACTOR_ID_V2, actorId),
				zap.Error(sendNotifErr))
		} else {
			logger.Debug(ctx, "sent tiering downgrade notification", zap.String(logger.ACTOR_ID_V2, actorId))
		}
	})
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		t.eventBroker.AddToBatch(
			epificontext.WithEventAttributes(ctx),
			tieringEvents.NewUserDowngradedTiering(
				actorId,
				time.Now(),
				provenance.String(),
				eligibleTierMovement.GetFromTier(),
				eligibleTierMovement.GetToTier(),
				reason.GetDowngradeReason().String(),
			),
		)
	})
	return nil
}

// HandleMovement : Steps
// 1. Create tier movement history
// 2. Modify current tier info
// 3. Mark eligible tier movement as done
func (t *TierMovementManagerService) handleMovement(ctx context.Context, actorId string, eligibleMmt *tieringPb.EligibleTierMovement,
	provenance tieringEnumPb.Provenance, reason *tieringPb.TierMovementReason) error {
	if actorId == "" {
		return errors.New("received empty actor id")
	}
	if eligibleMmt == nil {
		return errors.New("received nil eligible tier movement")
	}
	if validateErr := validateEligibleTierMovement(eligibleMmt); validateErr != nil {
		return errors.Wrap(validateErr, "error validating eligible tier movement")
	}

	tierMmt := &tieringPb.TierMovementHistory{
		EligibilityMovementReferenceId: eligibleMmt.GetId(),
		CriteriaReferenceId:            eligibleMmt.GetCriteriaReferenceId(),
		FromTier:                       eligibleMmt.GetFromTier(),
		ToTier:                         eligibleMmt.GetToTier(),
		ActorId:                        actorId,
		MovementType:                   eligibleMmt.GetMovementType(),
		Provenance:                     provenance,
		Reason:                         reason, // TODO (sayan) : confirm format for reason
	}
	var tierInfo *tieringPb.ActorTierInfo
	var tierInfoErr error
	// get tier info for the actor
	tierInfo, tierInfoErr = t.actorTierInfoDao.Get(ctx, actorId)
	if tierInfoErr != nil && !errors.Is(tierInfoErr, epifierrors.ErrRecordNotFound) {
		return errors.Wrap(tierInfoErr, "error getting tier info for actor")
	}
	// new tier info will have different to tier and might have different criteria ref. id
	if tierInfo != nil {
		tierInfo.Tier = eligibleMmt.GetToTier()
		tierInfo.CriteriaReferenceId = eligibleMmt.GetCriteriaReferenceId()
	}

	var createdTmh *tieringPb.TierMovementHistory
	txnFunc := func(txnCtx context.Context) error {
		if enrollErr := t.enrollForTrialIfEligible(txnCtx, actorId, provenance, eligibleMmt, reason); enrollErr != nil {
			return errors.Wrap(enrollErr, "error enrolling user for trial")
		}

		var createErr error
		createdTmh, createErr = t.tierMmtHistoryDao.Create(txnCtx, tierMmt)
		if createErr != nil {
			return errors.Wrap(createErr, "error creating tier movement history")
		}

		// If the tier is getting created for the first time
		// Create the tier info rather than updating
		if errors.Is(tierInfoErr, epifierrors.ErrRecordNotFound) {
			var tierCreateErr error
			// create a new tier info if tier info is nil
			tierInfo = &tieringPb.ActorTierInfo{
				ActorId:             actorId,
				Tier:                eligibleMmt.GetToTier(),
				MovementReferenceId: createdTmh.GetId(),
				CriteriaReferenceId: eligibleMmt.GetCriteriaReferenceId(),
			}
			tierInfo, tierCreateErr = t.actorTierInfoDao.Create(txnCtx, tierInfo)
			if tierCreateErr != nil {
				return errors.Wrap(tierCreateErr, "error assigning tier to user for first time")
			}
		} else {
			// populate movement reference id
			tierInfo.MovementReferenceId = createdTmh.GetId()
			var updErr error
			tierInfo, updErr = t.actorTierInfoDao.Update(txnCtx, tierInfo, []tieringPb.ActorTierInfoFieldMask{
				tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TIER,
				tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_MOVEMENT_REFERENCE_ID,
				tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_CRITERIA_REFERENCE_ID,
				tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_UPDATED_AT,
			})
			if updErr != nil {
				return errors.Wrap(updErr, "error updating tier info for actor")
			}
		}

		eligibleMmt.MovementTimestamp = timestamppb.Now()
		eligibleMmt.Status = tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_DONE
		if updErr := t.eligibleTierMovementDao.Update(txnCtx, eligibleMmt, []tieringPb.EligibleTierMovementFieldMask{
			tieringPb.EligibleTierMovementFieldMask_ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_MOVEMENT_TIMESTAMP,
			tieringPb.EligibleTierMovementFieldMask_ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_STATUS}); updErr != nil {
			return errors.Wrap(updErr, "error updating eligible tier movement for actor")
		}
		return nil
	}
	if txnErr := t.txnExecutor.RunTxn(ctx, txnFunc); txnErr != nil {
		return errors.Wrap(txnErr, "txn error in creating and updating entities")
	}
	// In case of no errors in tier update transaction, publish tier update event
	if publishTierUpdateEventErr := t.publishTierUpdateEvent(ctx, actorId, createdTmh); publishTierUpdateEventErr != nil {
		return errors.Wrap(publishTierUpdateEventErr, "error publishing tier update event for the movement")
	}
	return nil
}

func validateEligibleTierMovement(etm *tieringPb.EligibleTierMovement) error {
	// If current time is before the movement timestamp, movement has not matured yet
	if etm.GetToTier() == tieringEnumPb.Tier_TIER_UNSPECIFIED {
		return errors.New("toTier cannot be unspecified")
	}
	if etm.GetMovementType() == tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UNSPECIFIED {
		return errors.New("movement type cannot be unspecified")
	}
	// bypass all checks in case toTier is SALARY related tiers
	if tiermappings.IsSalaryRelatedTier(etm.GetToTier()) {
		return nil
	}
	if time.Now().Before(etm.GetMovementTimestamp().AsTime()) {
		return errors.Wrap(tieringErrors.ErrEligibleMovementNotMatured, fmt.Sprintf("movement id : %s", etm.GetId()))
	}
	return nil
}

// IsEligibleForTierUpgrade checks if user is eligible for tier upgrade
// This method always returns error if ineligible
func (t *TierMovementManagerService) IsEligibleForTierUpgrade(ctx context.Context, actorId string, fromTier tieringEnumPb.Tier, toTier tieringEnumPb.Tier) (bool, error) {
	if actorId == "" {
		return false, errors.New("received empty actor id")
	}
	// don't check for cool off in case of SALARY related tiers
	if tiermappings.IsSalaryRelatedTier(toTier) {
		return true, nil
	}

	userInCoolOff, err := t.timelineManager.IsUserInCooloff(ctx, actorId)
	if err != nil {
		return false, err
	}

	willUserBeInCoolOff, err := t.timelineManager.WillUserBeInCooloff(ctx, actorId)
	if err != nil {
		return false, err
	}

	if userInCoolOff || willUserBeInCoolOff {
		return false, tieringErrors.ErrCooloff
	}
	return true, nil
}

// IsEligibleForTierDowngrade checks if user is eligible for tier downgrade
// This method always returns error if ineligible
func (t *TierMovementManagerService) IsEligibleForTierDowngrade(ctx context.Context, actorId string, fromTier tieringEnumPb.Tier, toTier tieringEnumPb.Tier) (bool, error) {
	if actorId == "" {
		return false, errors.New("received empty actor id")
	}

	userInGracePeriod, _, err := t.timelineManager.IsUserInGracePeriod(ctx, actorId)
	if err != nil {
		return false, err
	}
	if userInGracePeriod {
		return false, tieringErrors.ErrGrace
	}
	return true, nil
}

// CreateOrGetEligibility : Idempotent function to create or record eligibility of an actor trying to travel from one tier to another
// If eligible movement is present for the same from and to tiers, the same is returned
// If not already present it creates one, invalidating the previous one
func (t *TierMovementManagerService) CreateOrGetEligibility(ctx context.Context, actorId string, fromTier tieringEnumPb.Tier, toTier tieringEnumPb.Tier, evaluatorMeta *tieringPb.EvaluatorMeta) (*tieringPb.EligibleTierMovement, error) {
	if actorId == "" {
		return nil, errors.New("received empty actor id")
	}
	if toTier == tieringEnumPb.Tier_TIER_UNSPECIFIED {
		return nil, errors.New(fmt.Sprintf("received unspecified to tier : %s", toTier))
	}

	mmtType, err := helper.GetMovementTypeFromStartAndEndTiers(fromTier, toTier)
	if err != nil {
		return nil, errors.Wrap(err, "error getting movement type from start and end tiers")
	}

	oldEligibleMmt, err := t.eligibleTierMovementDao.GetLatestByActorIdAndStatus(ctx, actorId,
		tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_ELIGIBLE)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "error getting eligible movement by actor id and status")
	}

	// true if user belongs to rewards abuser segment, and it is a downgrade movement
	isDowngradeMovementForRewardsAbuser, _ := t.isDowngradeMovementForRewardsAbuser(ctx, actorId, mmtType)
	if isDowngradeMovementForRewardsAbuser {
		logger.Info(ctx, fmt.Sprintf("isDowngradeMovementForRewardsAbuser is %t", isDowngradeMovementForRewardsAbuser), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	// Use oldEligibleMmt if and only if below conditions are satisfied.
	// 1. Old Eligible Movement is not nil
	// 2. Old From Tier = New From Tier
	// 3. Old To Tier = New To Tier
	// 4. It is not a downgrade movement for a rewards abuser user
	if oldEligibleMmt != nil && oldEligibleMmt.GetFromTier() == fromTier && oldEligibleMmt.GetToTier() == toTier && !isDowngradeMovementForRewardsAbuser {
		return oldEligibleMmt, nil
	}

	newEligibleMmt, newMmtErr := t.getNewEligibleMovement(ctx, actorId, fromTier, toTier, mmtType,
		tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_ELIGIBLE, oldEligibleMmt, isDowngradeMovementForRewardsAbuser, evaluatorMeta)
	if newMmtErr != nil {
		return nil, errors.Wrap(newMmtErr, "error getting new eligible movement")
	}

	txnFunc := func(txnCtx context.Context) error {
		// If old eligible movement is present, invalidate first and then proceed to create
		if oldEligibleMmt != nil {
			err := t.invalidateEligibility(txnCtx, actorId, oldEligibleMmt)
			if err != nil {
				return errors.Wrap(err, "error invalidating previous eligibility")
			}
		}

		// create new eligible movement
		createdMmt, createErr := t.eligibleTierMovementDao.Create(txnCtx, newEligibleMmt)
		if createErr != nil {
			return errors.Wrap(createErr, "error creating new eligible movement")
		}
		newEligibleMmt.Id = createdMmt.GetId()
		return nil
	}
	if txnErr := t.txnExecutor.RunTxn(ctx, txnFunc); txnErr != nil {
		return nil, errors.Wrap(txnErr, "error executing transaction")
	}

	return newEligibleMmt, nil

}

func (t *TierMovementManagerService) getNewEligibleMovement(ctx context.Context, actorId string, fromTier, toTier tieringEnumPb.Tier,
	mmtType tieringEnumPb.TierMovementType, status tieringEnumPb.EligibleTierMovementStatus, oldEligibleMmt *tieringPb.EligibleTierMovement,
	isDowngradeMovementForRewardsAbuser bool, evaluatorMeta *tieringPb.EvaluatorMeta) (*tieringPb.EligibleTierMovement, error) {
	var criteriaRefId string

	// If old eligible movement is present, we assign the same criteria
	// If not, the active criteria is assigned
	// EXPLANATION : Suppose user wants to go from T1 -> T2, and they are in cooloff.
	// Midway through the cooloff period they want to go to a higher tier and adds funds such that now they are going from T1 -> T3
	// If we assign a different criteria, there is a chance user ends up in T2 because the acc. to the new criteria user might not land in T3
	if oldEligibleMmt != nil {
		criteriaRefId = oldEligibleMmt.GetCriteriaReferenceId()
	} else {
		criteriaForActor, criteriaErr := t.tierCriteriaManager.GetActiveCriteria(ctx)
		if criteriaErr != nil {
			return nil, errors.Wrap(criteriaErr, "error getting active criteria")
		}
		criteriaRefId = criteriaForActor.GetId()
	}

	var (
		newMovementTimestamp  *timestamppb.Timestamp
		newEligibilityDetails *tieringPb.EligibilityDetails
	)

	zeroGraceEligibilityDetails := getZeroGraceEligibilityDetails(isDowngradeMovementForRewardsAbuser)

	if isDowngradeMovementForRewardsAbuser {
		newMovementTimestamp = timestamppb.New(time.Now())
		newEligibilityDetails = zeroGraceEligibilityDetails
	} else {
		var (
			getMvtTsErr error
		)
		// get details for upgrade/downgrade
		newMovementTimestamp, newEligibilityDetails, getMvtTsErr = t.getMovementTsAndDetails(ctx, actorId, mmtType, oldEligibleMmt)
		if getMvtTsErr != nil {
			return nil, errors.Wrap(getMvtTsErr, "error getting timestamp and movement details")
		}
		toPopulateZeroGraceDetails, toPopulateZeroGraceDetailsErr := t.toPopulateZeroGraceDetails(ctx, actorId, fromTier, toTier, mmtType, oldEligibleMmt)
		if toPopulateZeroGraceDetailsErr != nil {
			return nil, errors.Wrap(toPopulateZeroGraceDetailsErr, "error checking whether to populate downgrade movment with zero state details or not")
		}
		if toPopulateZeroGraceDetails {
			newMovementTimestamp = timestamppb.Now()
			if t.gconf.ShouldRoundGraceToNextDay() {
				newMovementTimestamp = timestamppb.New(helper.RoundUpToNextDay(time.Now()))
			}
			newEligibilityDetails = zeroGraceEligibilityDetails
		}
	}

	// create new eligible movement
	return &tieringPb.EligibleTierMovement{
		CriteriaReferenceId: criteriaRefId,
		FromTier:            fromTier,
		ToTier:              toTier,
		ActorId:             actorId,
		MovementType:        mmtType,
		Status:              status,
		MovementTimestamp:   newMovementTimestamp,
		Details:             newEligibilityDetails,
		CriteriaOptionType:  evaluatorMeta.GetHighPriorityCriteriaOptionType(),
		EvaluatorMeta:       evaluatorMeta,
	}, nil
}

func getZeroGraceEligibilityDetails(isDowngradeMovementForRewardsAbuser bool) *tieringPb.EligibilityDetails {
	zeroGraceEligibilityDetails := &tieringPb.EligibilityDetails{
		Details: &tieringPb.EligibilityDetails_GraceDetails{
			GraceDetails: &tieringPb.GraceDetails{
				Period:                           0,
				GraceLadderPosition:              0,
				IsARewardsAbuserEligibleMovement: isDowngradeMovementForRewardsAbuser,
			},
		},
	}
	return zeroGraceEligibilityDetails
}

// toPopulateZeroGraceDetails checks whether to populate details with zero grace details or not
// populate iff
// 1. User is downgrading from salary or salary lite tier
// 2. User is downgrading from base tier
// 3. User is downgrading from AA Salary Band 2
// 4. User is downgrading after completing trial period
func (t *TierMovementManagerService) toPopulateZeroGraceDetails(ctx context.Context, actorId string, fromTier, _ tieringEnumPb.Tier, movementType tieringEnumPb.TierMovementType, oldEligibleMmt *tieringPb.EligibleTierMovement) (bool, error) {
	extFromTier, convErr := tiermappings.GetExternalTierFromInternalTier(fromTier)
	if convErr != nil {
		return false, errors.Wrap(convErr, "error converting internal tier to external tier")
	}

	if movementType != tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE {
		return false, nil
	}

	if extFromTier.IsSalaryOrSalaryLiteTier() || extFromTier.IsBaseTier() || extFromTier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2 {
		return true, nil
	}

	return t.isDowngradeMovementForTrialUser(ctx, actorId, oldEligibleMmt)
}

func (t *TierMovementManagerService) isDowngradeMovementForTrialUser(ctx context.Context, actorId string, oldEligibleMmt *tieringPb.EligibleTierMovement) (bool, error) {
	tmh, err := t.tierMmtHistoryDao.GetLatestByActorId(ctx, actorId,
		tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_PROVENANCE,
		tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_REASON,
	)
	if err != nil {
		return false, errors.Wrap(err, "error getting latest tier movement history by actor id")
	}

	// check if user is downgrading from trial period from
	// - the last upgrade reason
	// - if old eligible movement has only one satisfied criteria option type which is BALANCE_TRIAL_AND_KYC
	if tmh.GetReason().GetUpgradeReason() == tieringEnumPb.UpgradeReason_UPGRADE_REASON_TRIAL_PERIOD &&
		len(oldEligibleMmt.GetEvaluatorMeta().GetSatisfiedCriteriaOptionTypes()) == 1 &&
		lo.Contains(oldEligibleMmt.GetEvaluatorMeta().GetSatisfiedCriteriaOptionTypes(), tieringEnumPb.CriteriaOptionType_BALANCE_TRIAL_AND_KYC) {
		return true, nil
	}

	return false, nil
}

func (t *TierMovementManagerService) isDowngradeMovementForRewardsAbuser(ctx context.Context, actorId string, movementType tieringEnumPb.TierMovementType) (bool, error) {

	if movementType != tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE {
		return false, nil
	}
	return t.timelineManager.IsARewardAbuserUser(ctx, actorId)
}

func (t *TierMovementManagerService) getMovementTsAndDetails(ctx context.Context, actorId string, newMmtType tieringEnumPb.TierMovementType, oldMmt *tieringPb.EligibleTierMovement) (*timestamppb.Timestamp, *tieringPb.EligibilityDetails, error) {
	// If upgrade types match for older and newer eligible movement - we copy the movement timestamp
	// This is because we do not want to change the upgrade or downgrade timeline
	// Ex - Lets say user was previously shown 15 days to downgrade, we cannot change it to 5 days suddenly. Same with upgrades.
	if oldMmt != nil && newMmtType == oldMmt.GetMovementType() {
		return oldMmt.GetMovementTimestamp(), oldMmt.GetDetails(), nil
	}
	if newMmtType == tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE {
		return t.timelineManager.GetUpgradeDetails(ctx, actorId)
	}

	return t.timelineManager.GetDowngradeDetails(ctx, actorId)
}

func (t *TierMovementManagerService) invalidateEligibility(ctx context.Context, actorId string, eligibility *tieringPb.EligibleTierMovement) error {
	eligibility.Status = tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_INVALID
	userInSilentGrace, err := helper.IsUserInSilentGracePeriod(eligibility)
	if err != nil {
		return errors.Wrap(err, "error determining whether user is in silent grace period")
	}
	// If user is in silent grace period, their entry will be marked as invalid silent grace
	if userInSilentGrace {
		eligibility.Status = tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_INVALID_SILENT_GRACE
	}
	// Send an event iff user is exiting only grace but not silent grace
	if eligibility.GetMovementType() == tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE &&
		!userInSilentGrace {
		goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
			t.eventBroker.AddToBatch(
				epificontext.WithEventAttributes(ctx),
				tieringEvents.NewExitedGraceTiering(
					actorId,
					time.Now(),
				),
			)
		})
	}
	eligibility.DeletedAt = timestamppb.New(time.Now())
	if updErr := t.eligibleTierMovementDao.Update(ctx, eligibility,
		[]tieringPb.EligibleTierMovementFieldMask{
			tieringPb.EligibleTierMovementFieldMask_ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_STATUS,
			tieringPb.EligibleTierMovementFieldMask_ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_DELETED_AT}); updErr != nil {
		return errors.Wrap(updErr, "error invalidating eligibility")
	}

	return nil
}
