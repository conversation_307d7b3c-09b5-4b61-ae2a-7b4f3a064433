//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/lock"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/investment/aggregator"
	"github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	tieringPb "github.com/epifi/gamma/api/tiering"
	ussAccountPb "github.com/epifi/gamma/api/usstocks/account"
	ussOrderPb "github.com/epifi/gamma/api/usstocks/order"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/auth"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/pay"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	release2 "github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	pinotClient "github.com/epifi/gamma/pkg/pinot"
	"github.com/epifi/gamma/tiering/autoupgrade"
	"github.com/epifi/gamma/tiering/comms"
	"github.com/epifi/gamma/tiering/data"
	"github.com/epifi/gamma/tiering/tier_options"
	"github.com/epifi/gamma/tiering/typedef"

	actorPb "github.com/epifi/gamma/api/actor"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/tiering"
	"github.com/epifi/gamma/tiering/config"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/consumer"
	"github.com/epifi/gamma/tiering/criteria"
	"github.com/epifi/gamma/tiering/dao"
	"github.com/epifi/gamma/tiering/data_collector"
	"github.com/epifi/gamma/tiering/data_collector/collectors"
	tieringDev "github.com/epifi/gamma/tiering/developer"
	"github.com/epifi/gamma/tiering/display_properties"
	"github.com/epifi/gamma/tiering/evaluator"
	"github.com/epifi/gamma/tiering/movement"
	"github.com/epifi/gamma/tiering/orchestrator"
	tieringPinot "github.com/epifi/gamma/tiering/pinot"
	pinotDao "github.com/epifi/gamma/tiering/pinot/dao"
	"github.com/epifi/gamma/tiering/release"
	"github.com/epifi/gamma/tiering/timeline"
)

func InitializeService(
	pgdb pkgTypes.TieringPGDB,
	gconf *genconf.Config,
	savingsClient savingsPb.SavingsClient,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	ugClient usergroupPb.GroupClient,
	bankCustomerClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryPb.SalaryProgramClient,
	daoCacheClient pkgTypes.TieringActorRedisStore,
	tieringRedisStore pkgTypes.TieringConnectedAccountRedisStore,
	commsClient commsPb.CommsClient,
	segmentClient segmentPb.SegmentationServiceClient,
	authClient auth.AuthClient,
	eventBroker events.Broker,
	tierUpdatePub typedef.TierUpdateEventExternalPublisher,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
	eodBalanceClient tieringPinotPb.EODBalanceClient,
	onboardingClient onboardingPb.OnboardingClient,
	payClient pay.PayClient,
	ussOrderClient ussOrderPb.OrderManagerClient,
	ussAccountClient ussAccountPb.AccountManagerClient,
	investmentAggregatorClient aggregator.InvestmentAggregatorClient,
) *tiering.Service {
	wire.Build(
		dao.WireSet,
		tmhMaxPageSizeProvider,
		pkgTypes.TieringPGDBGormDBProvider,
		redisOptionProvider,
		pkgTypes.TieringActorRedisStoreRedisClientProvider,
		cache.RedisStorageWithHystrixWireSet,
		wire.NewSet(criteria.NewTierCriteriaManager, wire.Bind(new(criteria.TierCriteria), new(*criteria.TierCriteriaManager))),
		wire.NewSet(evaluator.NewTierEvaluatorService, wire.Bind(new(evaluator.TierEvaluator), new(*evaluator.TierEvaluatorService))),
		wire.NewSet(movement.NewTierMovementManager, wire.Bind(new(movement.TierMovementManager), new(*movement.TierMovementManagerService))),
		wire.NewSet(orchestrator.NewOrchestratorService, wire.Bind(new(orchestrator.Orchestrator), new(*orchestrator.Service))),
		wire.NewSet(autoupgrade.NewTierAutoUpgradeManager, wire.Bind(new(autoupgrade.TierAutoUpgrade), new(*autoupgrade.TierAutoUpgradeManager))),
		wire.NewSet(timeline.NewTierTimelineManager, wire.Bind(new(timeline.TierTimeline), new(*timeline.TierTimelineManager))),
		wire.NewSet(data_collector.NewDataCollectorFactorySvc, wire.Bind(new(data_collector.DataCollectorFactory), new(*data_collector.DataCollectorFactorySvc))),
		wire.NewSet(release.NewReleaseManagerService, wire.Bind(new(release.Manager), new(*release.ManagerService))),
		wire.NewSet(tier_options.NewTierOptionsManagerService, wire.Bind(new(tier_options.Manager), new(*tier_options.Service))),
		wire.NewSet(data.NewTieringDataProcessorService, wire.Bind(new(data.TieringDataProcessor), new(*data.TieringDataProcessorService))),
		collectors.NewBalanceDataCollector,
		collectors.NewKycDataCollector,
		collectors.NewSalaryDataCollector,
		collectors.NewBaseTierDataCollector,
		collectors.NewUsStocksDataCollector,
		collectors.NewDepositsDataCollector,
		collectors.NewTrialDataCollector,
		display_properties.WireSet,
		tiering.NewService,
		comms.TieringCommsWireset,
		lock.DefaultLockMangerWireSet,
		storageV2.DefaultTxnExecutorWireSet,
		featureReleaseConfigProvider,
		release2.EvaluatorWireSet,
	)
	return &tiering.Service{}
}

func InitializeTieringConsumerService(
	pgdb pkgTypes.TieringPGDB,
	conf *genconf.Config,
	savingsClient savingsPb.SavingsClient,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	ugClient usergroupPb.GroupClient,
	bankCustomerClient bankCustPb.BankCustomerServiceClient,
	salaryProgramClient salaryPb.SalaryProgramClient, commsClient commsPb.CommsClient,
	redisClient pkgTypes.TieringActorRedisStore,
	segmentClient segmentPb.SegmentationServiceClient,
	authClient auth.AuthClient,
	eventBroker events.Broker,
	tierUpdatePub typedef.TierUpdateEventExternalPublisher,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
	eodBalanceClient tieringPinotPb.EODBalanceClient,
	onboardingClient onboardingPb.OnboardingClient,
	ussOrderClient ussOrderPb.OrderManagerClient,
	ussAccountClient ussAccountPb.AccountManagerClient,
	investmentAggregatorClient aggregator.InvestmentAggregatorClient,
	piClient paymentinstrument.PiClient,
	accPiClient accountPiPb.AccountPIRelationClient,
	tieringClient tieringPb.TieringClient,
) *consumer.TieringConsumer {
	wire.Build(
		dao.WireSet,
		tmhMaxPageSizeProvider,
		pkgTypes.TieringPGDBGormDBProvider,
		redisOptionProvider,
		pkgTypes.TieringActorRedisStoreRedisClientProvider,
		cache.RedisStorageWithHystrixWireSet,
		wire.NewSet(criteria.NewTierCriteriaManager, wire.Bind(new(criteria.TierCriteria), new(*criteria.TierCriteriaManager))),
		wire.NewSet(evaluator.NewTierEvaluatorService, wire.Bind(new(evaluator.TierEvaluator), new(*evaluator.TierEvaluatorService))),
		wire.NewSet(movement.NewTierMovementManager, wire.Bind(new(movement.TierMovementManager), new(*movement.TierMovementManagerService))),
		wire.NewSet(orchestrator.NewOrchestratorService, wire.Bind(new(orchestrator.Orchestrator), new(*orchestrator.Service))),
		wire.NewSet(autoupgrade.NewTierAutoUpgradeManager, wire.Bind(new(autoupgrade.TierAutoUpgrade), new(*autoupgrade.TierAutoUpgradeManager))),
		wire.NewSet(timeline.NewTierTimelineManager, wire.Bind(new(timeline.TierTimeline), new(*timeline.TierTimelineManager))),
		wire.NewSet(data_collector.NewDataCollectorFactorySvc, wire.Bind(new(data_collector.DataCollectorFactory), new(*data_collector.DataCollectorFactorySvc))),
		wire.NewSet(release.NewReleaseManagerService, wire.Bind(new(release.Manager), new(*release.ManagerService))),
		wire.NewSet(tier_options.NewTierOptionsManagerService, wire.Bind(new(tier_options.Manager), new(*tier_options.Service))),
		wire.NewSet(data.NewTieringDataProcessorService, wire.Bind(new(data.TieringDataProcessor), new(*data.TieringDataProcessorService))),
		collectors.NewBalanceDataCollector,
		collectors.NewKycDataCollector,
		collectors.NewSalaryDataCollector,
		collectors.NewBaseTierDataCollector,
		collectors.NewUsStocksDataCollector,
		collectors.NewDepositsDataCollector,
		collectors.NewTrialDataCollector,
		consumer.NewTieringConsumer,
		comms.TieringCommsWireset,
		lock.DefaultLockMangerWireSet,
		storageV2.DefaultTxnExecutorWireSet,
		featureReleaseConfigProvider,
		release2.EvaluatorWireSet,
	)
	return &consumer.TieringConsumer{}
}

func InitializeDevService(
	pgdb pkgTypes.TieringPGDB,
	conf *config.Config,
	gconf *genconf.Config,
	daoCacheClinet pkgTypes.TieringActorRedisStore,
	displayPropsCacheClient pkgTypes.TieringConnectedAccountRedisStore,
) *tieringDev.TieringDevService {
	wire.Build(
		dao.WireSet,
		tmhMaxPageSizeProvider,
		redisOptionProvider,
		cache.RedisStorageWithHystrixWireSet,
		pkgTypes.TieringActorRedisStoreRedisClientProvider,
		display_properties.WireSet,
		pinotDao.WireSet,
		pinotClientProvider,
		tieringDev.DbStatesWireSet,
		tieringDev.NewTieringDevService,
	)
	return &tieringDev.TieringDevService{}
}

// TODO(sainath): figure out how to provide different redis options for different redis clients
func redisOptionProvider(conf *genconf.Config) *cfg.RedisOptions {
	return conf.DaoCacheRedisOptions()
}

func InitializeEODBalanceService(
	conf *config.Config,
) *tieringPinot.Service {
	wire.Build(
		pinotDao.WireSet,
		pinotClientProvider,
		tieringPinot.NewService,
	)
	return &tieringPinot.Service{}
}

func pinotClientProvider(conf *config.Config) pinotClient.Client {
	env, _ := cfg.GetEnvironment()
	client, _ := pinotClient.NewPinotClient(conf.PinotConfig, env)
	return client
}

func tmhMaxPageSizeProvider(conf *genconf.Config) uint32 {
	return conf.TierMovementHistoriesDbMaxPageSize()
}

func featureReleaseConfigProvider(conf *genconf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}
