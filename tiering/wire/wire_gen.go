// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/investment/aggregator"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	tiering2 "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/pinot"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	pinot3 "github.com/epifi/gamma/pkg/pinot"
	"github.com/epifi/gamma/tiering"
	"github.com/epifi/gamma/tiering/autoupgrade"
	comms2 "github.com/epifi/gamma/tiering/comms"
	"github.com/epifi/gamma/tiering/comms/processors"
	"github.com/epifi/gamma/tiering/config"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/consumer"
	"github.com/epifi/gamma/tiering/criteria"
	"github.com/epifi/gamma/tiering/dao"
	"github.com/epifi/gamma/tiering/data"
	"github.com/epifi/gamma/tiering/data_collector"
	"github.com/epifi/gamma/tiering/data_collector/collectors"
	"github.com/epifi/gamma/tiering/developer"
	"github.com/epifi/gamma/tiering/developer/processor"
	"github.com/epifi/gamma/tiering/display_properties"
	"github.com/epifi/gamma/tiering/evaluator"
	"github.com/epifi/gamma/tiering/movement"
	"github.com/epifi/gamma/tiering/orchestrator"
	pinot2 "github.com/epifi/gamma/tiering/pinot"
	dao2 "github.com/epifi/gamma/tiering/pinot/dao"
	release2 "github.com/epifi/gamma/tiering/release"
	"github.com/epifi/gamma/tiering/tier_options"
	"github.com/epifi/gamma/tiering/timeline"
	"github.com/epifi/gamma/tiering/typedef"
)

// Injectors from wire.go:

func InitializeService(pgdb types.TieringPGDB, gconf *genconf.Config, savingsClient savings.SavingsClient, actorClient actor.ActorClient, userClient user.UsersClient, ugClient group.GroupClient, bankCustomerClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, daoCacheClient types.TieringActorRedisStore, tieringRedisStore types.TieringConnectedAccountRedisStore, commsClient comms.CommsClient, segmentClient segment.SegmentationServiceClient, authClient auth.AuthClient, eventBroker events.Broker, tierUpdatePub typedef.TierUpdateEventExternalPublisher, paySavingsBalanceClient balance.BalanceClient, eodBalanceClient pinot.EODBalanceClient, onboardingClient onboarding.OnboardingClient, payClient pay.PayClient, ussOrderClient order.OrderManagerClient, ussAccountClient account.AccountManagerClient, investmentAggregatorClient aggregator.InvestmentAggregatorClient) *tiering.Service {
	eligibleTierMovementImpl := dao.NewEligibleTierMovementImpl(pgdb)
	client := types.TieringActorRedisStoreRedisClientProvider(daoCacheClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	redisOptions := redisOptionProvider(gconf)
	hystrixCommand := redisOptions.HystrixCommand
	cacheStorageWithHystrix := cache.NewRedisCacheStorageWithHystrix(redisCacheStorage, hystrixCommand)
	eligibleTierMovementCache := dao.NewEligibleTierMovementCache(eligibleTierMovementImpl, cacheStorageWithHystrix, gconf)
	tierCriteriaImpl := dao.NewTierCriteriaImpl(pgdb)
	tierCriteriaManager := criteria.NewTierCriteriaManager(eligibleTierMovementCache, tierCriteriaImpl)
	balanceDataCollector := collectors.NewBalanceDataCollector(savingsClient, actorClient, paySavingsBalanceClient)
	kycDataCollector := collectors.NewKycDataCollector(bankCustomerClient)
	salaryDataCollector := collectors.NewSalaryDataCollector(salaryProgramClient, userClient)
	actorTierInfoImpl := dao.NewActorTierInfoImpl(pgdb)
	actorTierInfoCache := dao.NewActorTierInfoCache(actorTierInfoImpl, cacheStorageWithHystrix, gconf)
	baseTierDataCollector := collectors.NewBaseTierDataCollector(gconf, actorTierInfoCache, onboardingClient)
	usStocksDataCollector := collectors.NewUsStocksDataCollector(ussOrderClient, ussAccountClient)
	depositsDataCollector := collectors.NewDepositsDataCollector(investmentAggregatorClient)
	trialDataCollector := collectors.NewTrialDataCollector(gconf, actorTierInfoCache)
	dataCollectorFactorySvc := data_collector.NewDataCollectorFactorySvc(balanceDataCollector, kycDataCollector, salaryDataCollector, baseTierDataCollector, usStocksDataCollector, depositsDataCollector, trialDataCollector)
	tierEvaluatorService := evaluator.NewTierEvaluatorService(dataCollectorFactorySvc, gconf, segmentClient)
	uint32_2 := tmhMaxPageSizeProvider(gconf)
	tierMovementHistoryImpl := dao.NewTierMovementHistoryImpl(pgdb, uint32_2)
	tierMovementHistoryCache := dao.NewTierMovementHistoryCache(tierMovementHistoryImpl, cacheStorageWithHystrix, gconf)
	db := types.TieringPGDBGormDBProvider(pgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	tierTimelineManager := timeline.NewTierTimelineManager(gconf, eligibleTierMovementCache, tierMovementHistoryCache, segmentClient, gormTxnExecutor, actorTierInfoCache, eodBalanceClient)
	featureReleaseConfig := featureReleaseConfigProvider(gconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, ugClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	releaseEvaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	tieringDataProcessorService := data.NewTieringDataProcessorService(gconf, actorTierInfoCache, tierMovementHistoryCache, userClient, releaseEvaluator)
	upgradeProcessor := processors.NewUpgradeProcessor(gconf, tierMovementHistoryCache, tieringDataProcessorService)
	service := tier_options.NewTierOptionsManagerService(tierCriteriaManager)
	downgradeProcessor := processors.NewDowngradeProcessor(gconf, tierMovementHistoryCache, actorTierInfoCache, service)
	graceProcessor := processors.NewGraceProcessor(gconf, tierTimelineManager, actorTierInfoCache, tieringDataProcessorService, service)
	factoryService := comms2.NewFactoryService(upgradeProcessor, downgradeProcessor, graceProcessor)
	senderService := comms2.NewSenderService(commsClient, actorClient, factoryService, authClient, gconf, userClient)
	tierMovementManagerService := movement.NewTierMovementManager(gconf, eligibleTierMovementCache, actorTierInfoCache, tierMovementHistoryCache, tierTimelineManager, tierCriteriaManager, senderService, eventBroker, tierUpdatePub, gormTxnExecutor, segmentClient)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	tierAutoUpgradeManager := autoupgrade.NewTierAutoUpgradeManager(gconf, tierMovementHistoryCache, eodBalanceClient)
	orchestratorService := orchestrator.NewOrchestratorService(gconf, tierCriteriaManager, tierEvaluatorService, actorTierInfoCache, tierMovementManagerService, eligibleTierMovementCache, senderService, redisLockManager, eventBroker, tierTimelineManager, tierAutoUpgradeManager, userClient, gormTxnExecutor, releaseEvaluator, tieringDataProcessorService, segmentClient)
	managerService := release2.NewReleaseManagerService(actorClient, userClient, ugClient, gconf)
	actorScreenInteractionImpl := dao.NewActorScreenInteractionImpl(pgdb)
	tieringAbuserImpl := dao.NewTieringAbuserImpl(pgdb)
	tieringDisplayPropertiesSvc := display_properties.NewTieringDisplayPropertiesSvc(tieringRedisStore)
	tieringService := tiering.NewService(gconf, orchestratorService, managerService, tierMovementHistoryCache, eligibleTierMovementCache, actorTierInfoCache, actorScreenInteractionImpl, tieringAbuserImpl, tierTimelineManager, tierCriteriaManager, dataCollectorFactorySvc, tieringDisplayPropertiesSvc, tierMovementManagerService, service, tierEvaluatorService, segmentClient, salaryProgramClient, tieringDataProcessorService, onboardingClient, userClient, payClient, releaseEvaluator, tieringRedisStore, eodBalanceClient, savingsClient, paySavingsBalanceClient, eventBroker, actorClient, ugClient)
	return tieringService
}

func InitializeTieringConsumerService(pgdb types.TieringPGDB, conf *genconf.Config, savingsClient savings.SavingsClient, actorClient actor.ActorClient, userClient user.UsersClient, ugClient group.GroupClient, bankCustomerClient bankcust.BankCustomerServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, commsClient comms.CommsClient, redisClient types.TieringActorRedisStore, segmentClient segment.SegmentationServiceClient, authClient auth.AuthClient, eventBroker events.Broker, tierUpdatePub typedef.TierUpdateEventExternalPublisher, paySavingsBalanceClient balance.BalanceClient, eodBalanceClient pinot.EODBalanceClient, onboardingClient onboarding.OnboardingClient, ussOrderClient order.OrderManagerClient, ussAccountClient account.AccountManagerClient, investmentAggregatorClient aggregator.InvestmentAggregatorClient, piClient paymentinstrument.PiClient, accPiClient account_pi.AccountPIRelationClient, tieringClient tiering2.TieringClient) *consumer.TieringConsumer {
	eligibleTierMovementImpl := dao.NewEligibleTierMovementImpl(pgdb)
	client := types.TieringActorRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	redisOptions := redisOptionProvider(conf)
	hystrixCommand := redisOptions.HystrixCommand
	cacheStorageWithHystrix := cache.NewRedisCacheStorageWithHystrix(redisCacheStorage, hystrixCommand)
	eligibleTierMovementCache := dao.NewEligibleTierMovementCache(eligibleTierMovementImpl, cacheStorageWithHystrix, conf)
	tierCriteriaImpl := dao.NewTierCriteriaImpl(pgdb)
	tierCriteriaManager := criteria.NewTierCriteriaManager(eligibleTierMovementCache, tierCriteriaImpl)
	balanceDataCollector := collectors.NewBalanceDataCollector(savingsClient, actorClient, paySavingsBalanceClient)
	kycDataCollector := collectors.NewKycDataCollector(bankCustomerClient)
	salaryDataCollector := collectors.NewSalaryDataCollector(salaryProgramClient, userClient)
	actorTierInfoImpl := dao.NewActorTierInfoImpl(pgdb)
	actorTierInfoCache := dao.NewActorTierInfoCache(actorTierInfoImpl, cacheStorageWithHystrix, conf)
	baseTierDataCollector := collectors.NewBaseTierDataCollector(conf, actorTierInfoCache, onboardingClient)
	usStocksDataCollector := collectors.NewUsStocksDataCollector(ussOrderClient, ussAccountClient)
	depositsDataCollector := collectors.NewDepositsDataCollector(investmentAggregatorClient)
	trialDataCollector := collectors.NewTrialDataCollector(conf, actorTierInfoCache)
	dataCollectorFactorySvc := data_collector.NewDataCollectorFactorySvc(balanceDataCollector, kycDataCollector, salaryDataCollector, baseTierDataCollector, usStocksDataCollector, depositsDataCollector, trialDataCollector)
	tierEvaluatorService := evaluator.NewTierEvaluatorService(dataCollectorFactorySvc, conf, segmentClient)
	uint32_2 := tmhMaxPageSizeProvider(conf)
	tierMovementHistoryImpl := dao.NewTierMovementHistoryImpl(pgdb, uint32_2)
	tierMovementHistoryCache := dao.NewTierMovementHistoryCache(tierMovementHistoryImpl, cacheStorageWithHystrix, conf)
	db := types.TieringPGDBGormDBProvider(pgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	tierTimelineManager := timeline.NewTierTimelineManager(conf, eligibleTierMovementCache, tierMovementHistoryCache, segmentClient, gormTxnExecutor, actorTierInfoCache, eodBalanceClient)
	featureReleaseConfig := featureReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, ugClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	releaseEvaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	tieringDataProcessorService := data.NewTieringDataProcessorService(conf, actorTierInfoCache, tierMovementHistoryCache, userClient, releaseEvaluator)
	upgradeProcessor := processors.NewUpgradeProcessor(conf, tierMovementHistoryCache, tieringDataProcessorService)
	service := tier_options.NewTierOptionsManagerService(tierCriteriaManager)
	downgradeProcessor := processors.NewDowngradeProcessor(conf, tierMovementHistoryCache, actorTierInfoCache, service)
	graceProcessor := processors.NewGraceProcessor(conf, tierTimelineManager, actorTierInfoCache, tieringDataProcessorService, service)
	factoryService := comms2.NewFactoryService(upgradeProcessor, downgradeProcessor, graceProcessor)
	senderService := comms2.NewSenderService(commsClient, actorClient, factoryService, authClient, conf, userClient)
	tierMovementManagerService := movement.NewTierMovementManager(conf, eligibleTierMovementCache, actorTierInfoCache, tierMovementHistoryCache, tierTimelineManager, tierCriteriaManager, senderService, eventBroker, tierUpdatePub, gormTxnExecutor, segmentClient)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	tierAutoUpgradeManager := autoupgrade.NewTierAutoUpgradeManager(conf, tierMovementHistoryCache, eodBalanceClient)
	orchestratorService := orchestrator.NewOrchestratorService(conf, tierCriteriaManager, tierEvaluatorService, actorTierInfoCache, tierMovementManagerService, eligibleTierMovementCache, senderService, redisLockManager, eventBroker, tierTimelineManager, tierAutoUpgradeManager, userClient, gormTxnExecutor, releaseEvaluator, tieringDataProcessorService, segmentClient)
	managerService := release2.NewReleaseManagerService(actorClient, userClient, ugClient, conf)
	tieringConsumer := consumer.NewTieringConsumer(conf, orchestratorService, managerService, eventBroker, actorTierInfoCache, tierTimelineManager, service, tieringDataProcessorService, piClient, savingsClient, accPiClient, tieringClient)
	return tieringConsumer
}

func InitializeDevService(pgdb types.TieringPGDB, conf *config.Config, gconf *genconf.Config, daoCacheClinet types.TieringActorRedisStore, displayPropsCacheClient types.TieringConnectedAccountRedisStore) *developer.TieringDevService {
	tierCriteriaImpl := dao.NewTierCriteriaImpl(pgdb)
	tieringTierCriteriaEntity := processor.NewTieringTierCriteriaEntity(tierCriteriaImpl)
	eligibleTierMovementImpl := dao.NewEligibleTierMovementImpl(pgdb)
	client := types.TieringActorRedisStoreRedisClientProvider(daoCacheClinet)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	redisOptions := redisOptionProvider(gconf)
	hystrixCommand := redisOptions.HystrixCommand
	cacheStorageWithHystrix := cache.NewRedisCacheStorageWithHystrix(redisCacheStorage, hystrixCommand)
	eligibleTierMovementCache := dao.NewEligibleTierMovementCache(eligibleTierMovementImpl, cacheStorageWithHystrix, gconf)
	tieringETMEntity := processor.NewTieringETMEntity(eligibleTierMovementCache)
	uint32_2 := tmhMaxPageSizeProvider(gconf)
	tierMovementHistoryImpl := dao.NewTierMovementHistoryImpl(pgdb, uint32_2)
	tierMovementHistoryCache := dao.NewTierMovementHistoryCache(tierMovementHistoryImpl, cacheStorageWithHystrix, gconf)
	tieringTMHEntity := processor.NewTieringTMHEntity(tierMovementHistoryCache)
	actorTierInfoImpl := dao.NewActorTierInfoImpl(pgdb)
	actorTierInfoCache := dao.NewActorTierInfoCache(actorTierInfoImpl, cacheStorageWithHystrix, gconf)
	actorTierInfoEntity := processor.NewActorTierInfoEntity(actorTierInfoCache, tierMovementHistoryCache)
	tieringDisplayPropertiesSvc := display_properties.NewTieringDisplayPropertiesSvc(displayPropsCacheClient)
	redisStatesEntity := processor.NewRedisStatesEntity(tieringDisplayPropertiesSvc)
	pinotClient := pinotClientProvider(conf)
	eodBalanceJournalDaoPinot := dao2.NewEODBalanceJournalDaoPinot(pinotClient)
	pinotEODBalanceEntity := processor.NewPinotEODBalanceEntity(eodBalanceJournalDaoPinot)
	tieringAbuserImpl := dao.NewTieringAbuserImpl(pgdb)
	tieringAbuserEntity := processor.NewTieringAbuserEntity(tieringAbuserImpl)
	devFactory := developer.NewDevFactory(tieringTierCriteriaEntity, tieringETMEntity, tieringTMHEntity, actorTierInfoEntity, redisStatesEntity, pinotEODBalanceEntity, tieringAbuserEntity)
	tieringDevService := developer.NewTieringDevService(devFactory)
	return tieringDevService
}

func InitializeEODBalanceService(conf *config.Config) *pinot2.Service {
	client := pinotClientProvider(conf)
	eodBalanceJournalDaoPinot := dao2.NewEODBalanceJournalDaoPinot(client)
	service := pinot2.NewService(eodBalanceJournalDaoPinot)
	return service
}

// wire.go:

// TODO(sainath): figure out how to provide different redis options for different redis clients
func redisOptionProvider(conf *genconf.Config) *cfg.RedisOptions {
	return conf.DaoCacheRedisOptions()
}

func pinotClientProvider(conf *config.Config) pinot3.Client {
	env, _ := cfg.GetEnvironment()
	client, _ := pinot3.NewPinotClient(conf.PinotConfig, env)
	return client
}

func tmhMaxPageSizeProvider(conf *genconf.Config) uint32 {
	return conf.TierMovementHistoriesDbMaxPageSize()
}

func featureReleaseConfigProvider(conf *genconf.Config) *genconf2.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}
