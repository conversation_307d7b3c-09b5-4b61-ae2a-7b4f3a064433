// nolint: goconst
package tiering

import (
	"context"
	"fmt"
	"time"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	homeFePb "github.com/epifi/gamma/api/frontend/home"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/pkg/feature/release"
	tieringHelper "github.com/epifi/gamma/pkg/tiering"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	tieringErrors "github.com/epifi/gamma/tiering/errors"

	paymentPb "github.com/epifi/gamma/api/order/payment"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	dynamicElementsPb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	payPb "github.com/epifi/gamma/api/pay"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/helper"
	"github.com/epifi/gamma/tiering/tiermappings"
	"github.com/epifi/gamma/tiering/timeline"
)

const (
	plusDisplayString                 = "Plus"
	infiniteDisplayString             = "Infinite"
	graceInitialDaysTopBarBannerText  = "Add funds to stay on your %s plan."
	graceTopBarBannerText             = "Add funds to stay on your %s plan. Expires in %s days!"
	graceTopBarBannerTextOneDay       = "Add funds to stay on your %s plan. Expires in %s day!"
	graceTopBarBannerTextZeroDays     = "Add funds to stay on your %s plan. Expires today!"
	graceTopBarBannerImageUrl         = "https://epifi-icons.pointz.in/home/<USER>/Danger_4x.png"
	graceTopBarBannerBgColor          = "#D3B250"
	graceTopBarBannerFontColor        = "#262728"
	downgradeTopBarBannerTextPlus     = "Plus Plan expired! Add funds & enjoy 2X rewards."
	downgradeTopBarBannerTextInfinite = "Infinite Plan expired! Add funds & enjoy 2% back."
	downgradeTopBarBannerImageUrl     = "https://epifi-icons.pointz.in/home/<USER>/Danger_4x.png"
	downgradeTopBarBannerBgColor      = "#A73F4B"
	downgradeTopBarBannerFontColor    = "#FFFFFF"

	promoInfiniteInSufficientBalTopImageUrl    = "https://epifi-icons.pointz.in/tiering/promo/top_section_inf_not_enough_bal.png"
	promoInfiniteSufficientBalTopImageUrl      = "https://epifi-icons.pointz.in/tiering/promo/top_section_inf_enough_bal.png"
	plusTrialPromoWidgetUrl                    = "https://epifi-icons.pointz.in/tiering/promo-widget/trials/plus.png"
	infiniteTrialPromoWidgetUrl                = "https://epifi-icons.pointz.in/tiering/promo-widget/trials/infinite.png"
	primeTrialPromoWidgetUrl                   = "https://epifi-icons.pointz.in/tiering/promo-widget/trials/prime.png"
	promoInfiniteInSufficientCtaText           = "Upgrade for free"
	promoInfiniteSufficientCtaText             = "Claim now"
	tieringTrialCtaTextTemplate                = "Start your %s Plan trial now"
	promoMiddleSectionRewardsImageUrl          = "https://epifi-icons.pointz.in/tiering/promo/middle_section_rewards.png"
	promoMiddleSectionRewardsPreText           = "Rewards"
	promoMiddleSectionRewardsText              = "4X Fi-Coins"
	promoMiddleSectionCashbackImageUrl         = "https://epifi-icons.pointz.in/tiering/promo/middle_section_cashback.png"
	promoMiddleSectionCashbackPreText          = "Cashback"
	promoMiddleSectionCashbackText             = "₹500/month"
	promoMiddleSectionDebitCardImageUrl        = "https://epifi-icons.pointz.in/tiering/promo/middle_section_debit_card.png"
	promoMiddleSectionDebitCardPreText         = "Free Debit Card"
	promoMiddleSectionDebitCardText            = "0 Forex fee"
	promoMiddleSectionCustomerSupportImageUrl  = "https://epifi-icons.pointz.in/tiering/promo/middle_section_customer_support.png"
	promoMiddleSectionCustomerSupportPreText   = "Customer support"
	promoMiddleSectionCustomerSupportText      = "On priority"
	promoChevronImageUrl                       = "https://epifi-icons.pointz.in/tiering/promo/right_chevron.png"
	reactivationTxnType_3txn                   = "3txn"
	reactivationTxnType_5txn                   = "5txn"
	reward_Amount_50                           = 50
	reward_Amount_75                           = 75
	reward_Amount_100                          = 100
	reactivationPromoWidgetImageUrl_3Txn_0     = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_3txn_0.png"
	reactivationPromoWidgetImageUrl_3Txn_1     = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_3txn_1.png"
	reactivationPromoWidgetImageUrl_3Txn_2     = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_3txn_2.png"
	reactivationPromoWidgetImageUrl_3Txn_3     = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_3txn_3.png"
	reactivationPromoWidgetImageUrl_5Txn_50_0  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_50_0.png"
	reactivationPromoWidgetImageUrl_5Txn_50_1  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_50_1.png"
	reactivationPromoWidgetImageUrl_5Txn_50_2  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_50_2.png"
	reactivationPromoWidgetImageUrl_5Txn_50_3  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_50_3.png"
	reactivationPromoWidgetImageUrl_5Txn_50_4  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_50_4.png"
	reactivationPromoWidgetImageUrl_5Txn_50_5  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_50_5.png"
	reactivationPromoWidgetImageUrl_5Txn_75_0  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_75_0.png"
	reactivationPromoWidgetImageUrl_5Txn_75_1  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_75_1.png"
	reactivationPromoWidgetImageUrl_5Txn_75_2  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_75_2.png"
	reactivationPromoWidgetImageUrl_5Txn_75_3  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_75_3.png"
	reactivationPromoWidgetImageUrl_5Txn_75_4  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_75_4.png"
	reactivationPromoWidgetImageUrl_5Txn_75_5  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_75_5.png"
	reactivationPromoWidgetImageUrl_5Txn_100_0 = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_100_0.png"
	reactivationPromoWidgetImageUrl_5Txn_100_1 = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_100_1.png"
	reactivationPromoWidgetImageUrl_5Txn_100_2 = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_100_2.png"
	reactivationPromoWidgetImageUrl_5Txn_100_3 = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_100_3.png"
	reactivationPromoWidgetImageUrl_5Txn_100_4 = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_100_4.png"
	reactivationPromoWidgetImageUrl_5Txn_100_5 = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/reactivation_5txn_100_5.png"

	tieringLaunchPromoWidgetUpto1000Cashback = "https://epifi-icons.pointz.in/multiple-ways-tiering/launch/1000-cashback.png"
	tieringLaunchPromoWidgetUpto100Cashback  = "https://epifi-icons.pointz.in/multiple-ways-tiering/launch/100-cashback.png"
	tieringLaunchPromoWidgetHighestCashback  = "https://epifi-icons.pointz.in/multiple-ways-tiering/launch/highest-cashback-ever.png"
	promoWidgetBasicImagePrime               = "https://epifi-icons.pointz.in/promo_widget_primev3.png"
	promoWidgetBasicImageInfinite            = "https://epifi-icons.pointz.in/promo_widget_infinitev3.png"
	promoWidgetBasicImagePlus                = "https://epifi-icons.pointz.in/promo_widget_plusv3.png"
	ctaTextBasicToPrime                      = "Upgrade to Prime"
	ctaTextBasicToInfinite                   = "Upgrade to Infinite"
	ctaTextBasicToPlus                       = "Upgrade to Plus"
	colorSnow                                = "#F6F9FD"
	colorWhite                               = "#FFFFFF"
	colorIndigoDark                          = "#002D6A"
	colorIndigoLight                         = "#4F71AB"
	colorAmber                               = "#D48647"
	colorFiGreen                             = "#00B899"
)

var addFundsDeeplink = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TRANSFER_IN,
	}
}

type segmentEntryTimeForUserResp struct {
	segmentEntryTimeForUser *segment.GetSegmentEntryTimeForUserResponse
	userSegmentIdTxnDetails *genconf.SegmentTxnDetails
	transactionType         string
}

func (s *Service) FetchDynamicElements(ctx context.Context, req *dynamicElementsPb.FetchDynamicElementsRequest) (*dynamicElementsPb.FetchDynamicElementsResponse, error) {
	actorId := req.GetActorId()
	toCollectData, reasonIfDisabled := s.toCollectData(req)
	if !toCollectData {
		logger.Debug(ctx, "no dynamic elements found for the context", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.REASON, reasonIfDisabled))
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	// for NR account don't show any tiering related promo widgets
	nrResp, nrErr := s.userClient.IsNonResidentUser(ctx, &userPb.IsNonResidentUserRequest{
		Identifier: &userPb.IsNonResidentUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(nrResp, nrErr); rpcErr != nil {
		logger.Error(ctx, "error in non resident user check rpc", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in non resident user check rpc"),
		}, nil
	}
	if nrResp.GetIsNonResidentUser().ToBool() {
		logger.Debug(ctx, "not showing for NR account", zap.String(logger.ACTOR_ID_V2, actorId))
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	collectedData, gatherDataErr := s.gatherDataForDynamicElements(ctx, actorId)
	if gatherDataErr != nil {
		logger.Error(ctx, "error gathering data for dynamic elements", zap.Error(gatherDataErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error gathering data for dynamic elements"),
		}, nil
	}
	dynamicElements := make([]*dynamicElementsPb.DynamicElement, 0)
	// Incase of error fetching dynamic elements of any type, we need to gracefully ignore that error and not return record not found
	// This is needed because for example if fetching dynamic elements of screen1 fails we should still return screen2 elements
	// TODO(sainath): Add slack alerts for these cases since they are getting gracefully ignored
	topBarElements, fetchTopBarElementsErr := s.getDynamicElementsTopBar(ctx, req, collectedData)
	if fetchTopBarElementsErr != nil {
		logger.Error(ctx, "error fetching top bar dynamic elements", zap.Error(fetchTopBarElementsErr), zap.String(logger.ACTOR_ID_V2, actorId))
	} else {
		dynamicElements = append(dynamicElements, topBarElements...)
	}

	if s.gconf.IsReactivationPromoWidgetEnabled() {
		reactivationPromoWidget, reactivationPromoWidgetErr := s.getReactivationPromoWidget(ctx, actorId)
		if reactivationPromoWidgetErr != nil {
			if errors.Is(reactivationPromoWidgetErr, tieringErrors.ErrUserNotFoundInSegment) || errors.Is(reactivationPromoWidgetErr, tieringErrors.ErrUserOutOfDateRangeForReactivationCampaign) {
				logger.Debug(ctx, "error fetching reactivation promo widget", zap.Error(reactivationPromoWidgetErr), zap.String(logger.ACTOR_ID_V2, actorId))
			} else {
				logger.Error(ctx, "error fetching reactivation promo widget", zap.Error(reactivationPromoWidgetErr), zap.String(logger.ACTOR_ID_V2, actorId))
			}
		} else {
			dynamicElements = append(dynamicElements, reactivationPromoWidget)
		}
	}

	// Disabled for promoting AA Salary
	// promoWidgetElements, reasonIfNil, getPromoWidgetElementsErr := s.getPromoWidgetElements(ctx, req, collectedData)
	// if getPromoWidgetElementsErr != nil {
	//	logger.Error(ctx, "error fetching promo widgets", zap.Error(getPromoWidgetElementsErr), zap.String(logger.ACTOR_ID_V2, actorId))
	// } else {
	//	if reasonIfNil != "" {
	//		logger.Info(ctx, fmt.Sprintf("not showing promo widget, reason: %s", reasonIfNil), zap.String(logger.ACTOR_ID_V2, actorId))
	//	}
	//	dynamicElements = append(dynamicElements, promoWidgetElements...)
	// }

	// For pitching base tier users to higher tiers based on their account balance
	featurePitchTierInHome, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_TIERING_PITCH_THROUGH_PROMO_WIDGET).WithActorId(actorId))
	// gracefully handling as failure in tier evaluation should not fail the flow
	if err != nil {
		logger.Error(ctx, "failed to evaluate if pitching through promo widget in home is enabled or not", zap.Error(err))
	}

	// get b2b verification status
	isUserB2BVerified, isUserB2BVerifiedErr := s.userClient.GetB2BSalaryProgramVerificationStatus(ctx, &userPb.GetB2BSalaryProgramVerificationStatusRequest{
		Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{ActorId: actorId},
	})
	// gracefully handling as failure in getting b2b verification status, should not fail the flow
	if rpcErr := epifigrpc.RPCError(isUserB2BVerified, isUserB2BVerifiedErr); rpcErr != nil {
		logger.Error(ctx, "failed to get b2b verification status", zap.Error(rpcErr))
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status:       rpcPb.StatusOk(),
			ElementsList: dynamicElements,
		}, nil
	}

	if !isUserB2BVerified.GetIsVerified() && featurePitchTierInHome {
		promoWidgetElements, reasonIfNil, getPromoWidgetElementsErr := s.getPromoWidgetElementsV2(ctx, req, collectedData, actorId)
		if getPromoWidgetElementsErr != nil || len(promoWidgetElements) == 0 {
			logger.WarnWithCtx(ctx, "gracefully handling if there is error while fetching promo widget or reason present for not showing promo widget", zap.Error(getPromoWidgetElementsErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String("reason if nil", reasonIfNil))
		} else {
			// adding promo widget to dynamic elements if there is no error and no reason for not showing promo widget
			dynamicElements = append(dynamicElements, promoWidgetElements...)
		}
	}

	return &dynamicElementsPb.FetchDynamicElementsResponse{
		Status:       rpcPb.StatusOk(),
		ElementsList: dynamicElements,
	}, nil
}

func (s *Service) getReactivationPromoWidget(ctx context.Context, actorId string) (*dynamicElementsPb.DynamicElement, error) {

	segmentEntryTimeForUserResponse, segmentEntryTimeForUserErr := s.getSegmentEntryTimeAndReactivationTxnType(ctx, actorId)

	if segmentEntryTimeForUserErr != nil {
		return nil, segmentEntryTimeForUserErr
	}

	getTransactionAggregates, getTransactionAggregatesErr := s.payClient.GetTransactionAggregates(ctx, &payPb.GetTransactionAggregatesRequest{
		ActorId:             actorId,
		FromTime:            segmentEntryTimeForUserResponse.segmentEntryTimeForUser.GetEntryTime(),
		AccountingEntryType: paymentPb.AccountingEntryType_DEBIT,
	})
	if rpcErr := epifigrpc.RPCError(getTransactionAggregates, getTransactionAggregatesErr); rpcErr != nil {
		if getTransactionAggregates.GetStatus().IsRecordNotFound() {
			return nil, nil
		}
		return nil, fmt.Errorf("error in get transactin aggregates rpc: %v", rpcErr)
	}

	if segmentEntryTimeForUserResponse.userSegmentIdTxnDetails.StartDate() > time.Now().Day() || time.Now().Day() > segmentEntryTimeForUserResponse.userSegmentIdTxnDetails.EndDate() {
		return nil, tieringErrors.ErrUserOutOfDateRangeForReactivationCampaign
	}

	return s.getFourPointFeatureWidgetFullVisualElementCardForReactivationCampaign(getTransactionAggregates.GetTransactionAggregates().GetCount(), segmentEntryTimeForUserResponse.transactionType, segmentEntryTimeForUserResponse.userSegmentIdTxnDetails.RewardAmount()), nil
}

// nolint
func (s *Service) getSegmentEntryTimeAndReactivationTxnType(ctx context.Context, actorId string) (segmentEntryTimeForUserResp, error) {

	ch := make(chan segmentEntryTimeForUserResp, s.getSizeOfReactivationSegmentIdsMaps())
	var err error
	reactivationErrGrp, grpCtx := errgroup.WithContext(ctx)

	reactivationErrGrp.Go(func() error {
		err = s.getSegmentEntryTimesFor5TxnsSegmentIds(grpCtx, actorId, ch)
		return err
	})

	reactivationErrGrp.Go(func() error {
		err = s.getSegmentEntryTimesFor3TxnsSegmentIds(grpCtx, actorId, ch)
		return err
	})

	err = reactivationErrGrp.Wait()
	if err != nil {
		return segmentEntryTimeForUserResp{}, fmt.Errorf("error in fetching segment entry time for user: %v", err)
	}

	close(ch)
	if len(ch) == 0 {
		return segmentEntryTimeForUserResp{}, tieringErrors.ErrUserNotFoundInSegment
	}
	return <-ch, nil
}

func (s *Service) getSegmentEntryTimesFor5TxnsSegmentIds(ctx context.Context, actorId string, ch chan<- segmentEntryTimeForUserResp) error {
	segmentEntryErrGrp, grpCtx := errgroup.WithContext(ctx)
	s.gconf.SegmentIdsForReactivationPromoWidget_5txn().Range(func(segmentId string, segmentIdTxnDetails *genconf.SegmentTxnDetails) bool {
		segmentEntryErrGrp.Go(func() error {
			return s.fetchSegmentEntryTimeForUser(grpCtx, actorId, segmentId, segmentIdTxnDetails, ch, reactivationTxnType_5txn)
		})
		return true
	})
	return segmentEntryErrGrp.Wait()
}

func (s *Service) getSegmentEntryTimesFor3TxnsSegmentIds(ctx context.Context, actorId string, ch chan<- segmentEntryTimeForUserResp) error {
	segmentEntryErrGrp, grpCtx := errgroup.WithContext(ctx)
	s.gconf.SegmentIdsForReactivationPromoWidget_3txn().Range(func(segmentId string, segmentIdTxnDetails *genconf.SegmentTxnDetails) bool {
		segmentEntryErrGrp.Go(func() error {
			return s.fetchSegmentEntryTimeForUser(grpCtx, actorId, segmentId, segmentIdTxnDetails, ch, reactivationTxnType_3txn)
		})
		return true
	})
	return segmentEntryErrGrp.Wait()
}

func (s *Service) fetchSegmentEntryTimeForUser(ctx context.Context, actorId, segmentId string, segmentIdTxnDetails *genconf.SegmentTxnDetails, ch chan<- segmentEntryTimeForUserResp, reactivationTxnType string) error {
	segmentEntryTimeForUserResponse, segmentEntryTimeForUserErr := s.segmentationClient.GetSegmentEntryTimeForUser(ctx, &segment.GetSegmentEntryTimeForUserRequest{
		ActorId:   actorId,
		SegmentId: segmentId,
	})
	if rpcErr := epifigrpc.RPCError(segmentEntryTimeForUserResponse, segmentEntryTimeForUserErr); rpcErr != nil {
		if segmentEntryTimeForUserResponse.GetStatus().IsRecordNotFound() {
			return nil
		}
		return fmt.Errorf("error in get segment entry time for user rpc: %v", rpcErr)
	}

	if segmentEntryTimeForUserResponse.GetStatus().IsSuccess() {
		ch <- segmentEntryTimeForUserResp{
			segmentEntryTimeForUser: segmentEntryTimeForUserResponse,
			userSegmentIdTxnDetails: segmentIdTxnDetails,
			transactionType:         reactivationTxnType,
		}
	}
	return nil
}

// nolint:funlen
func (s *Service) getFourPointFeatureWidgetFullVisualElementCardForReactivationCampaign(txnCount int64, reactivationTxnType string, rewardAmount int) *dynamicElementsPb.DynamicElement {

	var visualElement *commontypes.VisualElement
	var cta *ui.IconTextComponent
	cta = &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Make a payment >"},
				FontColor:    "#37522A",
			},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN,
		},
	}

	switch reactivationTxnType {
	case reactivationTxnType_3txn:
		switch txnCount {
		case 0:
			visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_3Txn_0)
		case 1:
			visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_3Txn_1)
		case 2:
			visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_3Txn_2)
		default:
			visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_3Txn_3)
			cta = &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim your ₹250 >"},
						FontColor:    "#37522A",
					},
				},
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_MY_REWARDS_SCREEN,
				},
			}
		}
	case reactivationTxnType_5txn:
		switch rewardAmount {
		case reward_Amount_50:
			switch txnCount {
			case 0:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_50_0)
			case 1:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_50_1)
			case 2:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_50_2)
			case 3:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_50_3)
			case 4:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_50_4)
			default:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_50_5)
				cta = &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim your ₹50 >"},
							FontColor:    "#37522A",
						},
					},
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_MY_REWARDS_SCREEN,
					},
				}
			}
		case reward_Amount_75:
			switch txnCount {
			case 0:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_75_0)
			case 1:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_75_1)
			case 2:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_75_2)
			case 3:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_75_3)
			case 4:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_75_4)
			default:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_75_5)
				cta = &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim your ₹75 >"},
							FontColor:    "#37522A",
						},
					},
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_MY_REWARDS_SCREEN,
					},
				}
			}
		case reward_Amount_100:
			switch txnCount {
			case 0:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_100_0)
			case 1:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_100_1)
			case 2:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_100_2)
			case 3:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_100_3)
			case 4:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_100_4)
			default:
				visualElement = commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_100_5)
				cta = &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim your ₹100 >"},
							FontColor:    "#37522A",
						},
					},
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_MY_REWARDS_SCREEN,
					},
				}
			}
		}
	}

	return &dynamicElementsPb.DynamicElement{
		OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
		BizAnalyticsData: map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Tiering",
		},
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
				FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
					BorderColor: homeFePb.GetHomeWidgetBorderColor(),
					Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
						FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
							VisualElement: visualElement,
							Cta:           cta,
							Shadow: &ui.Shadow{
								Colour: ui.GetBlockColor("#AFD2A2"),
							},
						},
					},
				},
			},
		},
	}
}

// to get length of syncmap we need to iterate over it, len() doesn't work
func (s *Service) getSizeOfReactivationSegmentIdsMaps() int {
	size := 0
	s.gconf.SegmentIdsForReactivationPromoWidget_5txn().Range(func(segmentId string, segmentIdDisplayIntervalValue *genconf.SegmentTxnDetails) bool {
		size++
		return true
	})
	s.gconf.SegmentIdsForReactivationPromoWidget_3txn().Range(func(segmentId string, segmentIdDisplayIntervalValue *genconf.SegmentTxnDetails) bool {
		size++
		return true
	})
	return size
}

// toCollectData checks whether to proceed further and collect data
// We need to collect data if we are showing any one of the dynamic elements
func (s *Service) toCollectData(req *dynamicElementsPb.FetchDynamicElementsRequest) (bool, string) {
	isTopBarEnabled, reasonIfTopBarDisabled := s.isTopBarEnabled(req)
	isPromoWidgetEnabled, reasonIfPromoWidgetDisabled := s.isPromoWidgetEnabled(req)
	return isTopBarEnabled || isPromoWidgetEnabled,
		reasonIfTopBarDisabled +
			" " + reasonIfPromoWidgetDisabled
}

func (s *Service) isTopBarEnabled(req *dynamicElementsPb.FetchDynamicElementsRequest) (bool, string) {
	// If flag is turned off then return record not found with no elements
	if !s.gconf.EnableHomeTopBarBanner() {
		return false, "home v2 top bar banner flag is OFF"
	}
	// If type assertion fails then return record not found with no elements
	homeInfo, ok := req.GetClientContext().GetScreenAdditionalInfo().(*dynamicElementsPb.ClientContext_HomeInfo)
	if !ok {
		return false, "call did not come from home context"
	}
	// If context is not from TOP_BAR or not from home v2 then return record not found with no elements
	if homeInfo.HomeInfo.GetSection() != dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TOP_BAR ||
		homeInfo.HomeInfo.GetVersion() != dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2 {
		return false, "context is not from top bar section or home v2"
	}
	return true, ""
}

func (s *Service) isPromoWidgetEnabled(req *dynamicElementsPb.FetchDynamicElementsRequest) (bool, string) {
	// If flag is turned off then return record not found with no elements
	if !s.gconf.EnablePromoWidget() {
		return false, "promo widget flag is OFF"
	}
	// If type assertion fails then return record not found with no elements
	homeInfo, ok := req.GetClientContext().GetScreenAdditionalInfo().(*dynamicElementsPb.ClientContext_HomeInfo)
	if !ok {
		return false, "call did not come from home context"
	}
	// If context is not from PRIMARY or SECONDARY  then return record not found with no elements
	section := homeInfo.HomeInfo.GetSection()
	if section != dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY &&
		section != dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY {
		return false, "context is not from primary or secondary"
	}
	return true, ""
}

func (s *Service) DynamicElementCallback(ctx context.Context, req *dynamicElementsPb.DynamicElementCallbackRequest) (*dynamicElementsPb.DynamicElementCallbackResponse, error) {
	return nil, nil
}

// gatherDataForDynamicElements gathers data via parallel calls needed for dynamic elements
func (s *Service) gatherDataForDynamicElements(ctx context.Context, actorId string) (*DynamicElementsCollectedData, error) {
	gatherDataErrGrp, gCtx := errgroup.WithContext(ctx)
	var isUserInGrace bool
	var graceDetails *timeline.GraceDetails
	gatherDataErrGrp.Go(func() error {
		var graceCheckErr error
		isUserInGrace, graceDetails, graceCheckErr = s.tierTimelineManager.IsUserInGracePeriod(gCtx, actorId)
		if graceCheckErr != nil {
			return errors.Wrap(graceCheckErr, "error checking if user is in grace or not")
		}
		return nil
	})
	var downgradedFrom tieringEnumPb.Tier
	var isUserDowngraded bool
	gatherDataErrGrp.Go(func() error {
		lastMmt, getTmhErr := s.tierMovementHistoryDao.GetLatestByActorId(gCtx, actorId,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_MOVEMENT_TYPE,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
		)
		if getTmhErr != nil {
			if errors.Is(getTmhErr, epifierrors.ErrRecordNotFound) {
				logger.Debug(ctx, "no downgrade movement found for the actor", zap.String(logger.ACTOR_ID_V2, actorId))
				return nil
			}
			return errors.Wrap(getTmhErr, "error fetching latest downgrade tier movement for actor")
		}
		if lastMmt.GetMovementType() == tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE && -1*time.Until(lastMmt.GetCreatedAt().AsTime()) <= s.gconf.DowngradeWindowDuration(ctx) {
			isUserDowngraded = true
			downgradedFrom = lastMmt.GetFromTier()
		}
		return nil
	})
	var currentTier tieringEnumPb.Tier
	gatherDataErrGrp.Go(func() error {
		var getCurTierErr error
		currentTier, getCurTierErr = s.dataProcessor.GetCurrentTierDefaultToBaseTier(gCtx, actorId)
		if getCurTierErr != nil {
			return errors.Wrap(getCurTierErr, "error getting current tier for actor")
		}
		return nil
	})

	var isARewardsAbuserUser bool
	gatherDataErrGrp.Go(func() error {
		var rewardsAbuserCheckErr error
		isARewardsAbuserUser, rewardsAbuserCheckErr = s.tierTimelineManager.IsARewardAbuserUser(gCtx, actorId)
		if rewardsAbuserCheckErr != nil {
			return errors.Wrap(rewardsAbuserCheckErr, "Error determining if user is a rewards abuser")
		}
		return nil
	})

	var isSalaryPromoEnabled bool
	gatherDataErrGrp.Go(func() error {
		isEligibleResp, isEligibleErr := s.salaryProgramClient.IsEligibleForSalaryPromoWidget(ctx, &salaryprogramPb.IsEligibleForSalaryPromoWidgetRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(isEligibleResp, isEligibleErr); rpcErr != nil {
			return errors.Wrap(rpcErr, "error calling salary program promo widget eligibility rpc")
		}
		isSalaryPromoEnabled = isEligibleResp.GetIsEligible()
		return nil
	})

	var isFiLiteUser bool
	gatherDataErrGrp.Go(func() error {
		getFeatureDetailsRes, getFeatureDetailsResErr := s.onboardingClient.GetFeatureDetails(ctx, &onboardingPb.GetFeatureDetailsRequest{
			ActorId: actorId,
			Feature: onboardingPb.Feature_FEATURE_FI_LITE,
		})
		if rpcErr := epifigrpc.RPCError(getFeatureDetailsRes, getFeatureDetailsResErr); rpcErr != nil {
			return fmt.Errorf("error in GetFeatureDetails rpc: %w", rpcErr)
		}
		isFiLiteUser = getFeatureDetailsRes.GetIsFiLiteUser()
		return nil
	})

	var trialDetailsResp *tieringPb.GetTrialDetailsResponse
	gatherDataErrGrp.Go(func() error {
		var getTrialDetailsErr error
		trialDetailsResp, getTrialDetailsErr = s.GetTrialDetails(ctx, &tieringPb.GetTrialDetailsRequest{ActorId: actorId})
		if rpcErr := epifigrpc.RPCError(trialDetailsResp, getTrialDetailsErr); rpcErr != nil {
			return fmt.Errorf("error in GetTrialDetails rpc: %w", rpcErr)
		}

		return nil
	})

	gatherDataErr := gatherDataErrGrp.Wait()
	if gatherDataErr != nil {
		return nil, gatherDataErr
	}

	return &DynamicElementsCollectedData{
		GraceTimestamp:       graceDetails.GetMovementTimestamp(),
		GracePeriod:          graceDetails.GetGracePeriod(),
		DowngradedFrom:       downgradedFrom,
		CurrentTier:          currentTier,
		IsUserInGrace:        isUserInGrace,
		IsUserDowngraded:     isUserDowngraded,
		IsARewardsAbuserUser: isARewardsAbuserUser,
		IsSalaryPromoEnabled: isSalaryPromoEnabled,
		IsFiLiteUser:         isFiLiteUser,
		TrialDetailsResp:     trialDetailsResp,
	}, nil
}

//nolint:ineffassign
func (s *Service) getPromoWidgetElementsV2(ctx context.Context, req *dynamicElementsPb.FetchDynamicElementsRequest, collectedData *DynamicElementsCollectedData, actorId string) ([]*dynamicElementsPb.DynamicElement, string, error) {
	isEnabled, reasonIfNotEnabled := s.isPromoWidgetEnabled(req)
	if !isEnabled {
		return nil, reasonIfNotEnabled, nil
	}

	homeInfo, ok := req.GetClientContext().GetScreenAdditionalInfo().(*dynamicElementsPb.ClientContext_HomeInfo)
	if !ok {
		return nil, "call did not come from home context", nil
	}

	section := homeInfo.HomeInfo.GetSection()

	isTieringAllPlansV2Enabled, err := s.dataProcessor.IsMultipleWaysToEnterTierEnabled(ctx, req.GetActorId())
	if err != nil {
		return nil, "", errors.Wrap(err, "error checking if tiering all plans v2 is enabled")
	}
	if !isTieringAllPlansV2Enabled {
		return nil, "not showing since tiering all plans v2 is not enabled", nil
	}
	if collectedData.GetIsFiLiteUser() {
		return nil, "not showing since tiering promo widget is not enabled for fi-lite user", nil
	}

	currentTier, err := tiermappings.GetExternalTierFromInternalTier(collectedData.GetCurrentTier())
	if err != nil {
		return nil, "error in getting external tier from internal tier", err
	}

	var (
		deeplink      *deeplinkPb.Deeplink
		image         string
		ctaTextColor  string
		ctaText       string
		ctaBgColor    string
		analyticsData = make(map[string]string)
		title         *commontypes.Text
	)

	switch {
	case collectedData.GetTrialDetailsResp().GetIsEligibleForTrial():
		deeplink = collectedData.GetTrialDetailsResp().GetDeeplink()
		ctaTextColor = colorWhite
		ctaBgColor = colorFiGreen
		tierDisplayString, _ := tieringHelper.GetTitleCaseDisplayString(collectedData.GetTrialDetailsResp().GetEligibleTrialTier())
		title = commontypes.GetPlainStringText(fmt.Sprintf("Try %s perks at 50%% off", tierDisplayString)).WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#38393B")
		ctaText = fmt.Sprintf(tieringTrialCtaTextTemplate, tierDisplayString)
		switch collectedData.GetTrialDetailsResp().GetEligibleTrialTier() {
		case tieringExtPb.Tier_TIER_FI_PLUS:
			image = plusTrialPromoWidgetUrl
		case tieringExtPb.Tier_TIER_FI_INFINITE:
			image = infiniteTrialPromoWidgetUrl
		case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
			image = primeTrialPromoWidgetUrl
		}

		analyticsData = map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String():         "Tiering Trials",
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_CURRENT_USER.String(): currentTier.String(),
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_PITCHED_TIER.String(): collectedData.GetTrialDetailsResp().GetEligibleTrialTier().String(),
		}
	case currentTier.IsBaseTier():
		// Get the savings account for the actor
		accountRes, accountErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorId{ActorId: actorId},
		})
		if accountErr != nil || accountRes.GetAccount() == nil {
			logger.Error(ctx, "error getting account in getPromoWidgetElementsV2", zap.Error(accountErr))
			return nil, "error getting account in getPromoWidgetElementsV2", accountErr
		}
		accountId := accountRes.GetAccount().GetId()

		// Get the account balance
		balanceRes, balanceErr := s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
			Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: accountId},
			ActorId:       actorId,
			DataFreshness: enums.DataFreshness_HISTORICAL,
		})
		if err = epifigrpc.RPCError(balanceRes, balanceErr); err != nil {
			logger.Error(ctx, "error getting account balance in getPromoWidgetElementsV2", zap.Error(err))
			return nil, "error getting account balance in getPromoWidgetElementsV2", err
		}
		balance := balanceRes.GetAvailableBalance().GetUnits()
		pitchedTier := ""
		switch {
		// Balance More than 50,000 -> Pitch for prime (directs to all plans page focus at prime)
		// TIER_FI_AA_SALARY_BAND_3 is prime
		case balance > s.gconf.TieringBalanceBasedPitchParams().BalanceForPitchingPrimeForBasic():
			deeplink = tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, isTieringAllPlansV2Enabled)
			ctaText = ctaTextBasicToPrime
			ctaTextColor = colorIndigoDark
			ctaBgColor = colorWhite
			image = promoWidgetBasicImagePrime
			pitchedTier = tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3.String()
		// Balance (25000, 50000] -> Pitch for infinite (directs to all plans page focus at infinite)
		case balance > s.gconf.TieringBalanceBasedPitchParams().BalanceForPitchingInfiniteForBasic():
			deeplink = tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_INFINITE, isTieringAllPlansV2Enabled)
			ctaText = ctaTextBasicToInfinite
			ctaTextColor = colorSnow
			ctaBgColor = colorIndigoLight
			image = promoWidgetBasicImageInfinite
			pitchedTier = tieringExtPb.Tier_TIER_FI_INFINITE.String()
		// Balance [0, 25000] -> Pitch for plus (directs to add money)
		default:
			deeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_TRANSFER_IN,
				ScreenOptions: &deeplinkPb.Deeplink_TransferInScreenOptions{
					TransferInScreenOptions: &deeplinkPb.TransferInScreenOptions{
						UiEntryPoint: feTransactionPb.UIEntryPoint_HOME.String(),
					},
				},
			}
			ctaText = ctaTextBasicToPlus
			ctaTextColor = colorSnow
			ctaBgColor = colorAmber
			image = promoWidgetBasicImagePlus
			pitchedTier = tieringExtPb.Tier_TIER_FI_PLUS.String()
		}

		analyticsData = map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String():           "Pitch base tiers balance based",
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_CURRENT_USER.String():   currentTier.String(),
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_PITCHED_TIER.String():   pitchedTier,
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_BALANCE_BUCKET.String(): tieringHelper.DetermineBalanceBucket(balance),
		}
	default:
		return nil, "", fmt.Errorf("unhandled tier: %s", currentTier.String())
	}

	dynamicElement := &dynamicElementsPb.DynamicElement{}
	switch section {
	case dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY:
		dynamicElement = &dynamicElementsPb.DynamicElement{
			OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
			UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
			Content: &dynamicElementsPb.ElementContent{Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
				FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
					Title: title,
					Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
						FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
							VisualElement: commontypes.GetVisualElementImageFromUrl(image),
							Cta: &ui.IconTextComponent{
								Texts:    []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(ctaText, ctaTextColor, commontypes.FontStyle_BUTTON_S)},
								Deeplink: deeplink,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: ctaBgColor,
									Width: 300,
								},
							},
						},
					},
				},
			},
			},
			BizAnalyticsData: analyticsData,
		}
	default:
		return nil, "", fmt.Errorf("unhandled section: %s", section.String())
	}

	return []*dynamicElementsPb.DynamicElement{dynamicElement}, "", nil
}

func (s *Service) getPromoWidgetElements(ctx context.Context, req *dynamicElementsPb.FetchDynamicElementsRequest, collectedData *DynamicElementsCollectedData) ([]*dynamicElementsPb.DynamicElement, string, error) {
	isEnabled, reasonIfNotEnabled := s.isPromoWidgetEnabled(req)
	if !isEnabled {
		return nil, reasonIfNotEnabled, nil
	}
	homeInfo, ok := req.GetClientContext().GetScreenAdditionalInfo().(*dynamicElementsPb.ClientContext_HomeInfo)
	if !ok {
		return nil, "call did not come from home context", nil
	}
	section := homeInfo.HomeInfo.GetSection()
	// If current tier is not strictly lower than infinite then don't show promo widget
	if collectedData.GetCurrentTier() != tieringEnumPb.Tier_TIER_FIVE &&
		collectedData.GetCurrentTier() != tieringEnumPb.Tier_TIER_TEN &&
		collectedData.GetCurrentTier() != tieringEnumPb.Tier_TIER_ONE_HUNDRED {
		return nil, "current tier is not lower than infinite", nil
	}
	// If salary promo widget is shown then don't show tiering infinite promo widget
	if collectedData.GetIsSalaryPromoEnabled() {
		return nil, "not showing since salary promo widget is enabled for the user", nil
	}

	isTieringAllPlansV2Enabled, err := s.dataProcessor.IsMultipleWaysToEnterTierEnabled(ctx, req.GetActorId())
	if err != nil {
		return nil, "", errors.Wrap(err, "error checking if tiering all plans v2 is enabled")
	}

	dl := tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_INFINITE, isTieringAllPlansV2Enabled)
	if collectedData.GetIsFiLiteUser() {
		return nil, "not showing since tiering promo widget is not enabled for fi-lite user", nil
	}
	var fourPointPromoWidget *dynamicElementsPb.DynamicElement
	var getFourPointPromoWidgetErr error
	switch section {
	case dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY:
		fourPointPromoWidget, getFourPointPromoWidgetErr = s.getFourPointPromoWidget(ctx, false, false, dl)
	case dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY:
		fourPointPromoWidget, getFourPointPromoWidgetErr = s.getFourPointPromoWidget(ctx, true, false, dl)
	default:
		return nil, "", fmt.Errorf("unhandled section: %s", section.String())
	}
	if getFourPointPromoWidgetErr != nil {
		return nil, "", errors.Wrap(getFourPointPromoWidgetErr, "error getting four point promo widget")
	}
	return []*dynamicElementsPb.DynamicElement{fourPointPromoWidget}, "", nil
}

// nolint: funlen,unparam
func (s *Service) getFourPointPromoWidget(_ context.Context, isCarouselVariant bool, isEnoughBalanceVariant bool, dl *deeplinkPb.Deeplink) (*dynamicElementsPb.DynamicElement, error) {
	topSectionImageUrl := promoInfiniteInSufficientBalTopImageUrl
	ctaText := promoInfiniteInSufficientCtaText
	if isEnoughBalanceVariant {
		topSectionImageUrl = promoInfiniteSufficientBalTopImageUrl
		ctaText = promoInfiniteSufficientCtaText
	}
	return &dynamicElementsPb.DynamicElement{
		OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
		BizAnalyticsData: map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Tiering",
		},
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
				FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
					BorderColor: homeFePb.GetHomeWidgetBorderColor(),
					Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_{
						TextVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard{
							TopSection: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection{
								VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(topSectionImageUrl, 116, 380),
							},
							MiddleSection: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection{
								HighlightedPoints: []*dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{
									{
										LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(promoMiddleSectionRewardsImageUrl, 32, 32),
										PreText:  commontypes.GetTextFromStringFontColourFontStyle(promoMiddleSectionRewardsPreText, "#929599", commontypes.FontStyle_HEADLINE_S),
										Text:     commontypes.GetTextFromStringFontColourFontStyle(promoMiddleSectionRewardsText, "#313234", commontypes.FontStyle_NUMBER_M),
									},
									{
										LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(promoMiddleSectionCashbackImageUrl, 32, 32),
										PreText:  commontypes.GetTextFromStringFontColourFontStyle(promoMiddleSectionCashbackPreText, "#929599", commontypes.FontStyle_HEADLINE_S),
										Text:     commontypes.GetTextFromStringFontColourFontStyle(promoMiddleSectionCashbackText, "#313234", commontypes.FontStyle_NUMBER_M),
									},
									{
										LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(promoMiddleSectionDebitCardImageUrl, 32, 32),
										PreText:  commontypes.GetTextFromStringFontColourFontStyle(promoMiddleSectionDebitCardPreText, "#929599", commontypes.FontStyle_HEADLINE_S),
										Text:     commontypes.GetTextFromStringFontColourFontStyle(promoMiddleSectionDebitCardText, "#313234", commontypes.FontStyle_NUMBER_M),
									},
									{
										LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(promoMiddleSectionCustomerSupportImageUrl, 32, 32),
										PreText:  commontypes.GetTextFromStringFontColourFontStyle(promoMiddleSectionCustomerSupportPreText, "#929599", commontypes.FontStyle_HEADLINE_S),
										Text:     commontypes.GetTextFromStringFontColourFontStyle(promoMiddleSectionCustomerSupportText, "#313234", commontypes.FontStyle_NUMBER_M),
									},
								},
							},
							Cta: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle(ctaText, "#2D5E6E", commontypes.FontStyle_BUTTON_S),
								},
								Deeplink: dl,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:      "#EFF2F6",
									CornerRadius: 20,
									Height:       38,
									Width:        332,
								},
								RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(promoChevronImageUrl, 16, 16),
							},
							BgColour: ui.GetBlockColor("#ffffff"),
						},
					},
					IsCarouselVariant: isCarouselVariant,
				},
			},
		},
	}, nil
}

// nolint: funlen
func (s *Service) getDynamicElementsTopBar(ctx context.Context, req *dynamicElementsPb.FetchDynamicElementsRequest, collectedData *DynamicElementsCollectedData) ([]*dynamicElementsPb.DynamicElement, error) {
	actorId := req.GetActorId()
	isEnabled, _ := s.isTopBarEnabled(req)
	if !isEnabled {
		return nil, nil
	}
	graceBannerInfo, getGraceBannerInfoErr := s.getBannerInfoForGrace(ctx,
		actorId, collectedData.GetCurrentTier(), collectedData.GetGraceTimestamp(), collectedData.GetGracePeriod())
	if getGraceBannerInfoErr != nil {
		return nil, errors.Wrap(getGraceBannerInfoErr, "error getting grace related info for top bar")
	}
	downgradeBannerInfo, getDowngradeBannerInfoErr := getBannerInfoForDowngrade(ctx,
		actorId, collectedData.GetDowngradedFrom(), collectedData.GetIsARewardsAbuserUser())
	if getDowngradeBannerInfoErr != nil {
		return nil, errors.Wrap(getDowngradeBannerInfoErr, "error getting downgrade related info for top bar")
	}
	var title, imageUrl, bgColor, fontColor string
	switch {
	// More preference is given to grace banner
	case collectedData.GetIsUserInGrace() && graceBannerInfo.GetToShowBanner():
		title = graceBannerInfo.GetTitle()
		imageUrl = graceBannerInfo.GetImageUrl()
		bgColor = graceBannerInfo.GetBgColor()
		fontColor = graceBannerInfo.GetFontColor()
	case collectedData.GetIsUserDowngraded() && downgradeBannerInfo.GetToShowBanner():
		title = downgradeBannerInfo.GetTitle()
		imageUrl = downgradeBannerInfo.GetImageUrl()
		bgColor = downgradeBannerInfo.GetBgColor()
		fontColor = downgradeBannerInfo.GetFontColor()
	default:
		return nil, nil
	}
	return []*dynamicElementsPb.DynamicElement{
		{
			OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
			UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
			StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
			Content: &dynamicElementsPb.ElementContent{
				Content: &dynamicElementsPb.ElementContent_BannerV2{
					BannerV2: &dynamicElementsPb.BannerElementContentV2{
						Title: &commontypes.Text{
							FontColor: fontColor,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: title,
							},
						},
						Image:           commontypes.GetImageFromUrl(imageUrl),
						BackgroundColor: ui.GetBlockColor(bgColor),
						Deeplink:        addFundsDeeplink(),
					},
				},
			},
			EndTime: collectedData.GetGraceTimestamp(),
		},
	}, nil
}

// getBannerInfoForGrace returns banner info for grace
func (s *Service) getBannerInfoForGrace(ctx context.Context, actorId string, currentTier tieringEnumPb.Tier,
	graceTimestamp *timestampPb.Timestamp, gracePeriod float64,
) (*BannerInfo, error) {
	curTierExt, conversionErr := tiermappings.GetExternalTierFromInternalTier(currentTier)
	if conversionErr != nil {
		return nil, errors.Wrap(conversionErr, "error converting internal tier to BE external tier")
	}
	if curTierExt != tieringExtPb.Tier_TIER_FI_PLUS && curTierExt != tieringExtPb.Tier_TIER_FI_INFINITE {
		logger.Debug(ctx, "user is not eligible for grace banner surfacing because of tier", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.TIER, curTierExt.String()))
		return nil, nil
	}
	graceStartTime := graceTimestamp.AsTime().Add(-time.Duration(gracePeriod) * time.Second)
	if time.Until(graceTimestamp.AsTime()) > s.gconf.GraceWindowDuration(ctx) &&
		time.Since(graceStartTime) > s.gconf.GraceInitialWindowDuration(ctx) {
		logger.Debug(ctx, "user is not eligible for grace banner surfacing because of graceTimestamp", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, nil
	}
	curTierString, getCurTierStringErr := getDisplayStringForTier(curTierExt)
	if getCurTierStringErr != nil {
		return nil, errors.Wrap(getCurTierStringErr, "error getting display string for current tier")
	}
	title := s.getGraceTopBannerTitle(ctx, curTierString, graceStartTime, graceTimestamp)
	return &BannerInfo{
		ToShowBanner: true,
		Title:        title,
		ImageUrl:     graceTopBarBannerImageUrl,
		BgColor:      graceTopBarBannerBgColor,
		FontColor:    graceTopBarBannerFontColor,
	}, nil
}

func (s *Service) getGraceTopBannerTitle(ctx context.Context, curTierString string, graceStartTime time.Time, graceExpiryTimestamp *timestampPb.Timestamp) string {
	graceExpiryInDays := helper.GetGraceTimestampExpiryInDays(graceExpiryTimestamp)
	var title string
	switch {
	case time.Since(graceStartTime) < s.gconf.GraceInitialWindowDuration(ctx):
		title = fmt.Sprintf(graceInitialDaysTopBarBannerText, curTierString)
	case graceExpiryInDays == "0":
		title = fmt.Sprintf(graceTopBarBannerTextZeroDays, curTierString)
	case graceExpiryInDays == "1":
		title = fmt.Sprintf(graceTopBarBannerTextOneDay, curTierString, graceExpiryInDays)
	default:
		title = fmt.Sprintf(graceTopBarBannerText, curTierString, graceExpiryInDays)
	}

	return title
}

// getBannerInfoForDowngrade returns banner info for downgrade
func getBannerInfoForDowngrade(ctx context.Context, actorId string, downgradedFrom tieringEnumPb.Tier, isARewardsAbuserUser bool) (*BannerInfo, error) {
	downgradedFromExt, conversionErr := tiermappings.GetExternalTierFromInternalTier(downgradedFrom)
	if conversionErr != nil {
		return nil, errors.Wrap(conversionErr, "error converting internal tier to BE external tier")
	}
	if isARewardsAbuserUser || (downgradedFromExt != tieringExtPb.Tier_TIER_FI_PLUS && downgradedFromExt != tieringExtPb.Tier_TIER_FI_INFINITE) {
		logger.Debug(ctx, "user is not eligible for downgrade banner surfacing", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.FROM_TIER, downgradedFromExt.String()))
		return nil, nil
	}
	downgradeTopBarTextString, getDowngradeTopBarTextStringErr := getDowngradeTopBarTextString(downgradedFromExt)
	if getDowngradeTopBarTextStringErr != nil {
		return nil, errors.Wrap(getDowngradeTopBarTextStringErr, "error getting downgrade text")
	}
	return &BannerInfo{
		ToShowBanner: true,
		Title:        downgradeTopBarTextString,
		ImageUrl:     downgradeTopBarBannerImageUrl,
		BgColor:      downgradeTopBarBannerBgColor,
		FontColor:    downgradeTopBarBannerFontColor,
	}, nil
}

func getDisplayStringForTier(currentTier tieringExtPb.Tier) (string, error) {
	switch currentTier {
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return plusDisplayString, nil
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return infiniteDisplayString, nil
	default:
		return "", fmt.Errorf("display string not handled for tier: %s", currentTier.String())
	}
}

func getDowngradeTopBarTextString(downgradedFrom tieringExtPb.Tier) (string, error) {
	switch downgradedFrom {
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return downgradeTopBarBannerTextPlus, nil
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return downgradeTopBarBannerTextInfinite, nil
	default:
		return "", fmt.Errorf("downgrade top bar text not available for tier: %s", downgradedFrom.String())
	}
}
