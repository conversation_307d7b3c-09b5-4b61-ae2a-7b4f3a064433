package helper

import (
	"fmt"

	"github.com/epifi/be-common/pkg/money"

	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	tieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
)

func EvaluateBalanceTrialData(
	requiredData *criteriaPb.BalanceTrial,
	haveData *tieringPb.TrialData,
	balanceData *tieringPb.BalanceData,
	evaluationTier tieringEnumPb.Tier,
	evaluatorOptions *tieringPb.EvaluateTierForActorRequest_Options,
) (bool, error) {
	// evaluate to true if a trial is active for the evaluation tier
	if evaluationTier == haveData.GetTrialDetails().GetTier() && haveData.GetTrialDetails().IsTrialPeriodActive() {
		return true, nil
	}

	compareRequiredAndAvailableBalance, compareErr := money.CompareV2(balanceData.GetAvailableBalance(), requiredData.GetMinBalanceForTrial())
	if compareErr != nil {
		return false, fmt.Errorf("error comparing required and available balance: %w", compareErr)
	}

	// evaluate to true if evaluator options to evaluate with trial threshold is set to true
	// and the available balance is greater than or equal to the required balance
	switch {
	case evaluatorOptions.GetToEvalWithTrialThresholdsForPrime() && evaluationTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_NINETY,
		evaluatorOptions.GetToEvalWithTrialThresholdsForInfinite() && evaluationTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND,
		evaluatorOptions.GetToEvalWithTrialThresholdsForPlus() && evaluationTier == tieringEnumPb.Tier_TIER_ONE_HUNDRED:
		return compareRequiredAndAvailableBalance >= 0, nil
	default:
		return false, nil
	}
}

// EvaluateBalanceData evaluates balance data for an actor against balance data for criteria
func EvaluateBalanceData(requiredBalanceData *criteriaPb.Balance, haveBalanceData *tieringPb.BalanceData) bool {
	// right now it is min <= avail <= max
	compareMinAndAvail := money.Compare(requiredBalanceData.GetMinBalance(), haveBalanceData.GetAvailableBalance())
	return compareMinAndAvail <= 0
}

func EvaluateBalanceDataV2(requiredBalanceData *criteriaPb.BalanceV2, haveBalanceData *tieringPb.BalanceData, evaluationTier tieringEnumPb.Tier, currentTier tieringEnumPb.Tier) bool {
	if requiredBalanceData.GetMinBalanceForUpgrade() != nil {
		compareMinBalForUpgrade, compareErr := money.CompareV2(haveBalanceData.GetAvailableBalance(), requiredBalanceData.GetMinBalanceForUpgrade())
		if compareErr != nil {
			return false
		}
		if compareMinBalForUpgrade >= 0 {
			return true
		}
	}
	if requiredBalanceData.GetMinBalanceToPreventDowngrade() != nil && evaluationTier == currentTier {
		compareMinBalToPreventDowngrade, compareErr := money.CompareV2(haveBalanceData.GetAvailableBalance(), requiredBalanceData.GetMinBalanceToPreventDowngrade())
		if compareErr != nil {
			return false
		}
		if compareMinBalToPreventDowngrade >= 0 {
			return true
		}
	}
	return false
}

// EvaluateKycData evaluates kyc data for an actor against kyc data for criteria
func EvaluateKycData(requiredKycData *criteriaPb.Kyc, haveKycData *tieringPb.KycData) bool {
	// check if kyc levels are same
	return requiredKycData.GetKycLevel().Number() <= haveKycData.GetKycLevel().Number()
}

// EvaluateSalaryData evaluates salary data for an actor against salary data for criteria
func EvaluateSalaryData(requiredSalaryData *criteriaPb.Salary, haveSalaryData *tieringPb.SalaryData, toEvaluateForMultipleWays bool, evaluationTier tieringEnumPb.Tier, shouldExcludeB2CSalaryCriteria, shouldExcludeAaSalaryCriteria bool) bool {
	if toEvaluateForMultipleWays {
		switch requiredSalaryData.GetSalaryActivationType() {
		case salaryPb.SalaryActivationType_AA_SALARY_ACTIVATION:
			return EvaluateAaSalaryData(requiredSalaryData, haveSalaryData, shouldExcludeAaSalaryCriteria)
		case salaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION:
			if haveSalaryData.GetSalaryActivationType() == salaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION && haveSalaryData.GetB2BSalaryProgramVerificationStatus() == true {
				if requiredSalaryData.GetB2BSalaryBand() == salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_UNSPECIFIED {
					return true
				}
				return haveSalaryData.GetB2BSalaryBand() == requiredSalaryData.GetB2BSalaryBand()
			}

			// Check segment exclusion for SALARY_B2C criteria using shouldExcludeB2CSalaryCriteria from pre-fetched data
			if haveSalaryData.GetSalaryActivationType() == salaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION &&
				!haveSalaryData.GetB2BSalaryProgramVerificationStatus() &&
				evaluationTier != tieringEnumPb.Tier_TIER_TWO_THOUSAND && evaluationTier != tieringEnumPb.Tier_TIER_ONE_THOUSAND_EIGHT_HUNDRED &&
				shouldExcludeB2CSalaryCriteria {
				return false
			}

			if haveSalaryData.GetSalaryActivationType() == salaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION &&
				!haveSalaryData.GetB2BSalaryProgramVerificationStatus() &&
				evaluationTier != tieringEnumPb.Tier_TIER_TWO_THOUSAND && evaluationTier != tieringEnumPb.Tier_TIER_ONE_THOUSAND_EIGHT_HUNDRED {
				return haveSalaryData.GetSalaryBand() == requiredSalaryData.GetSalaryBand()
			}
		case salaryPb.SalaryActivationType_SALARY_LITE_ACTIVATION:
			return haveSalaryData.GetSalaryActivationType() == salaryPb.SalaryActivationType_SALARY_LITE_ACTIVATION
		}

		return false
	}
	// Evaluate for salary activation type only if we are evaluating for old criteria
	if haveSalaryData.GetSalaryActivationType() == salaryPb.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED {
		return false
	}

	// special handling for tier_1090 only for old version
	if evaluationTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_NINETY &&
		haveSalaryData.GetSalaryActivationType() == salaryPb.SalaryActivationType_AA_SALARY_ACTIVATION &&
		haveSalaryData.GetSalaryBand() == requiredSalaryData.GetSalaryBand() {
		return true
	}

	if haveSalaryData.GetSalaryActivationType() != requiredSalaryData.GetSalaryActivationType() {
		return false
	}
	if requiredSalaryData.GetSalaryBand() != salaryEnumsPb.SalaryBand_SALARY_BAND_UNSPECIFIED {
		return haveSalaryData.GetSalaryBand() == requiredSalaryData.GetSalaryBand()
	}

	return true
}

func EvaluateBaseTierData(requiredBaseTierData *criteriaPb.BaseTier, haveBaseTierData *tieringPb.BaseTierData) bool {
	return haveBaseTierData.GetActorBaseTier() == requiredBaseTierData.GetBaseTier()
}

func EvaluateUsStockSipData(requiredUsStocksData *criteriaPb.UsStocksSip, haveUsStocksData *tieringPb.UsStocksData) bool {
	cmpVal, cmpErr := money.CompareV2(haveUsStocksData.GetWalletAddFundsValueInL30D(), requiredUsStocksData.GetMinWalletAddFunds())
	if cmpErr != nil {
		return false
	}
	return cmpVal >= 0
}

func EvaluateDepositsData(requiredFDData *criteriaPb.Deposits, haveFDData *tieringPb.DepositsData) bool {
	cmpVal, cmpErr := money.CompareV2(haveFDData.GetTotalDeposits(), requiredFDData.GetMinDepositsAmount())
	if cmpErr != nil {
		return false
	}
	return cmpVal >= 0
}

// EvaluateAaSalaryData evaluates aa salary data for an actor against salary data for criteria
func EvaluateAaSalaryData(requiredSalaryData *criteriaPb.Salary, haveSalaryData *tieringPb.SalaryData, shouldExcludeAaSalaryCriteria bool) bool {
	if haveSalaryData.GetSalaryActivationType() != salaryPb.SalaryActivationType_AA_SALARY_ACTIVATION {
		return false
	}
	// Check segment exclusion for AA_SALARY criteria using shouldExcludeAaSalaryCriteria from pre-fetched data
	if shouldExcludeAaSalaryCriteria {
		return false
	}
	return haveSalaryData.GetSalaryBand() == requiredSalaryData.GetSalaryBand()
}

// HandleWrappingErrors returns first not nil error from group of errors
func HandleWrappingErrors(errs ...error) error {
	for _, err := range errs {
		if err != nil {
			return err
		}
	}
	return nil
}

func ToEvaluateWithRealTimeData(provenance tieringEnumPb.Provenance) bool {
	return provenance == tieringEnumPb.Provenance_PROVENANCE_OPT_IN ||
		provenance == tieringEnumPb.Provenance_PROVENANCE_INSTANT_UPGRADE ||
		provenance == tieringEnumPb.Provenance_PROVENANCE_FREE_UPGRADE ||
		provenance == tieringEnumPb.Provenance_PROVENANCE_INTENT_UPGRADE_ALL_PLANS_JOIN_PLUS ||
		provenance == tieringEnumPb.Provenance_PROVENANCE_INTENT_UPGRADE_ALL_PLANS_JOIN_INFINITE ||
		provenance == tieringEnumPb.Provenance_PROVENANCE_INTENT_UPGRADE_ALL_PLANS_JOIN_PRIME ||
		provenance == tieringEnumPb.Provenance_PROVENANCE_ON_APP_AUTO_UPGRADE
}

func ToEvaluateWithoutAppAccessCheck(provenance tieringEnumPb.Provenance) bool {
	return provenance == tieringEnumPb.Provenance_PROVENANCE_MANUAL_OVERRIDE
}
