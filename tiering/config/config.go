package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
	"github.com/epifi/gamma/pkg/pinot"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

// nolint: gosec
const (
	RudderWriteKey         = "RudderWriteKey"
	TieringDbCreds         = "TieringDbCreds"
	StartreePinotAuthToken = "StartreePinotAuthToken"
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.TIERING_SERVICE)
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal config")
	}
	if onlyStaticFiles {
		return conf, nil
	}
	keyToSecret, err := cfg.LoadAllSecretsV2(conf.EpifiDb, conf.TieringDb, conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}
	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)

	pgdbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.TieringDb, pgdbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
	cfg.UpdatePGDBSecretValuesV2(c.TieringDb, keyToSecret)

	// update pinot config
	c.PinotConfig.Endpoint = cfg.GetPinotEndpoint(cfg.PINOT)
	if _, ok := keyToSecret[StartreePinotAuthToken]; !ok {
		return fmt.Errorf("startree pinot authtoken not fetched from secrets manager")
	}
	pinot.UpdateAuthTokenInConfig(c.PinotConfig, keyToSecret[StartreePinotAuthToken])

	// update db username and password in db config
	// if env is development keys are not fetched from SM hence update from yml file itself
	if cfg.IsLocalEnv(c.Application.Environment) {
		cfg.UpdateDbUsernamePasswordInConfig(c.TieringDb, c.Secrets.Ids[TieringDbCreds])
		return nil
	}
	if _, ok := keyToSecret[TieringDbCreds]; !ok {
		return errors.New("db username password not fetched tiering db from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.TieringDb, keyToSecret[TieringDbCreds])

	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Port = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.TieringDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// Constraints with respect to dynamic config generation tool:
// 1. Struct names have to start with uppercase letters
// 2. Struct variables need to be pointers
//
//go:generate conf_gen github.com/epifi/gamma/tiering/config Config
type Config struct {
	Application                               *Application
	Server                                    *Server
	EpifiDb                                   *cfg.DB
	TieringDb                                 *cfg.DB
	AWS                                       *cfg.AWS
	Flags                                     *Flags
	Secrets                                   *cfg.Secrets
	Tracing                                   *cfg.Tracing
	Profiling                                 *cfg.Profiling
	CooloffParams                             *CooloffParams `dynamic:"true"`
	GraceParams                               *GraceParams
	TieringFeatureRelease                     *InternalReleaseConfig `dynamic:"true"`
	TierReEvaluationEventSqsSubscriber        *cfg.SqsSubscriber     `dynamic:"true" iam:"external-consumer"`
	ProcessInvestmentEventSubscriber          *cfg.SqsSubscriber     `dynamic:"true"`
	ProcessUsStocksWalletOrderEventSubscriber *cfg.SqsSubscriber     `dynamic:"true"`
	// TODO(sayan) : remove this field
	DisplayPropertyKeyExpiryDuration    time.Duration `dynamic:"true"`
	DaoCacheRedisOptions                *cfg.RedisOptions
	DisplayPropertiesRedisOptions       *cfg.RedisOptions
	DisplayComponentTTLMap              map[string]time.Duration       `dynamic:"true"`
	ProcessKycUpdateEventSubscriber     *cfg.SqsSubscriber             `dynamic:"true"`
	ProcessSalaryUpdateEventSubscriber  *cfg.SqsSubscriber             `dynamic:"true"`
	ProcessBalanceUpdateEventSubscriber *cfg.SqsSubscriber             `dynamic:"true"`
	ProcessAddFundsOrderEventSubscriber *cfg.SqsSubscriber             `dynamic:"true"`
	AutoUpgradeCutoff                   time.Duration                  `dynamic:"true"`
	ToShowUpgradeNotifForSalary         bool                           `dynamic:"true"`
	ToShowUpgradeNotifForSalaryLite     bool                           `dynamic:"true"`
	NotificationConfigMap               map[string]*NotificationParams `dynamic:"true"`
	ActorTierInfoCacheConfig            *CacheConfig                   `dynamic:"true"`
	EligibleTierMovementCacheConfig     *CacheConfig                   `dynamic:"true"`
	TierMovementHistoryCacheConfig      *CacheConfig                   `dynamic:"true"`
	CxMovementHistoryLimit              int                            `dynamic:"true"`
	SegmentIds                          *SegmentIds                    `dynamic:"true"`
	CriteriaSegmentExclusions           *CriteriaSegmentExclusions     `dynamic:"true"`
	TierToIneligibleActorSegmentIdMap   map[string]string              `dynamic:"true"`
	// map of segments ids mapped to tier enums as string
	// tieringEnumPb.Tier_X.String()
	SilentGraceTierSegmentsMap            map[string]string `dynamic:"true"`
	EnableGoodUserSilentGraceSegmentation bool              `dynamic:"true"`
	EnableRewardsAbuserSegmentation       bool              `dynamic:"true"`
	// Feature flag to control whether to use tiering_abuser table for abuser checks
	// When enabled, uses table-based approach instead of segment-based approach
	EnableTieringAbuserDbCheck bool `dynamic:"true"`
	// These min versions should be synced with frontend all plans min versions
	MinAndroidVersion                uint32 `dynamic:"true"`
	MinIosVersion                    uint32 `dynamic:"true"`
	RudderStack                      *cfg.RudderStackBroker
	TierUpdateEventExternalPublisher *cfg.SnsPublisher
	EnableHomeTopBarBanner           bool `dynamic:"true"`
	EnablePromoWidget                bool `dynamic:"true"`
	// This needs to be in sync with config in frontend for profile notch
	GraceWindowDuration                          time.Duration `dynamic:"true" ,quest:"variable,area:Tiering"`
	DowngradeWindowDuration                      time.Duration `dynamic:"true" ,quest:"variable,area:Tiering"`
	GraceInitialWindowDuration                   time.Duration `dynamic:"true" ,quest:"variable,area:Tiering"`
	QuestRedisOptions                            *cfg.RedisOptions
	QuestSdk                                     *sdkconfig.Config `dynamic:"true"`
	PinotConfig                                  *pinot.Config
	ProcessBalanceUpdateEventMarketingSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	// Map of tieringEnumPb.Tier.String() to EODBalanceServiceConfig
	TierEODBalanceServiceConfigMap map[string]*EODBalanceServiceConfig `dynamic:"true"`
	// config flags to enable and disable auto upgrade conditions
	AutoUpgradeConfig                         *AutoUpgradeConfig `dynamic:"true" ,quest:"component,area:Tiering"`
	TierMovementHistoriesDbMaxPageSize        uint32
	EnableTierDowngrades                      bool                                `dynamic:"true"`
	IsReactivationPromoWidgetEnabled          bool                                `dynamic:"true"`
	SegmentIdsForReactivationPromoWidget_3txn map[string]*SegmentTxnDetails       `dynamic:"true"`
	SegmentIdsForReactivationPromoWidget_5txn map[string]*SegmentTxnDetails       `dynamic:"true"`
	FeatureReleaseConfig                      *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	RegularTierLaunchDate                     time.Time                           `dynamic:"true"`
	MinAvgMonthlyBalanceForRegularTier        int                                 `dynamic:"true"`
	MinBalancePenaltyForRegularTier           int                                 `dynamic:"true"`
	ShouldRoundGraceToNextDay                 bool                                `dynamic:"true"`
	TieringTrialConfig                        *TieringTrialConfig                 `dynamic:"true"`
	TieringSherlockBannersConfig              *TieringSherlockBannersConfig
	// specifies the balance range required by base tier users to get pitched for higher tiers
	TieringBalanceBasedPitchParams *TieringBalanceBasedPitchParams       `dynamic:"true"`
	ABFeatureReleaseConfig         *releaseConfig.ABFeatureReleaseConfig `dynamic:"true"`
}

type TieringBalanceBasedPitchParams struct {
	BalanceForPitchingInfiniteForBasic int64 `dynamic:"true"`
	BalanceForPitchingPrimeForBasic    int64 `dynamic:"true"`
}
type SegmentTxnDetails struct {
	StartDate    int `dynamic:"true"`
	EndDate      int `dynamic:"true"`
	RewardAmount int `dynamic:"true"`
}

type AutoUpgradeConfig struct {
	DisableProvenanceCheck        bool `dynamic:"true"`
	DisableSalaryTierCheck        bool `dynamic:"true"`
	DisableFirstTierMovementCheck bool `dynamic:"true"`
	DisableAutoUpgradeCutoffCheck bool `dynamic:"true"`
	DisableHigherTierCheck        bool `dynamic:"true"`

	DisableUserSpecificChecks bool `dynamic:"true"`
	// below checks are enabled only on unsetting DisableUserSpecificChecks
	// if enabled, auto upgrades user if they were in plus/infinite tier earlier
	DisableBeenInTieringCheck  bool          `dynamic:"true" ,quest:"variable"`
	DisableAverageBalanceCheck bool          `dynamic:"true"`
	AvgBalanceDurationBuffer   time.Duration `dynamic:"true"`
	// Map of tieringEnumPb.Tier.String() to map of BalanceDuration.String() to balance threshold
	AutoUpgradeAvgBalanceConditionMap map[string]*AverageBalanceConditions `dynamic:"true"`
	// auto upgrade is done for given tier on enabling
	// using a struct instead of map[tier] to bool as map is not supported by quest yet
	// todo[obed]: use map when supported by quest
	DisableToTierConditionMap *TierConditionMap `dynamic:"true" ,quest:"component"`
}

type TierConditionMap struct {
	Regular     bool `dynamic:"true" ,quest:"variable"`
	Standard    bool `dynamic:"true" ,quest:"variable"`
	Plus        bool `dynamic:"true" ,quest:"variable"`
	Infinite    bool `dynamic:"true" ,quest:"variable"`
	AaSalary    bool `dynamic:"true" ,quest:"variable"`
	SalaryLite  bool `dynamic:"true" ,quest:"variable"`
	Salary      bool `dynamic:"true" ,quest:"variable"`
	SalaryBasic bool `dynamic:"true" ,quest:"variable"`
}

type AverageBalanceConditions struct {
	// Map of balance duration (denominator for calculating average) to threshold balance
	AverageBalanceConditionMap map[string]int64 `dynamic:"true"`
}

type EODBalanceServiceConfig struct {
	Enable bool `dynamic:"true"`
	// Duration of EOD balance aggregation
	EODBalanceAggregateDuration time.Duration `dynamic:"true"`
	// Minimum days of aggregation to consider that average is valid
	// Eg: If pinot service returns data for 19 days but minimum number of days for accurate data is
	// 20 days then fallback to other methods
	MinAggregationDays uint32 `dynamic:"true"`
	// Average balance threshold criteria
	AverageBalanceThreshold int64 `dynamic:"true"`
}

type CacheConfig struct {
	IsCachingEnabled bool `dynamic:"true"`
	// prefix to be appended to id while creating the cache keys
	Prefix string
	// duration for which actor data should be cached
	CacheTTl time.Duration `dynamic:"true"`
}

type InternalReleaseConfig struct {
	// Global flag for feature whether it is enabled or not
	IsFeatureEnabled bool `dynamic:"true"`
	// Percentage rollout for external users
	// NOTE: This will not be used for other user groups
	RolloutPercentage uint32 `dynamic:"true"`
	// one of
	// 1 - INTERNAL
	// 2 - FNF
	// 7 - TIERING
	AllowedGroups []commontypes.UserGroup
}

type GraceParams struct {
	// ResetDuration is the time after which grace ladder position is reset to 0
	// If the last invalidated or completed downgrade was > x days back
	// New grace period allotted is the first in the ladder
	ResetDuration time.Duration
	// Ladder containing grace periods to be allotted
	Ladder []time.Duration
	// Silent grace period being applied
	SilentGracePeriod time.Duration
	// Silent grace period for good user
	GoodUserSilentGracePeriod time.Duration
}

type CooloffParams struct {
	MaxMovementsAllowed      int64         `dynamic:"true"`
	EvaluationWindowDuration time.Duration `dynamic:"true"`
}

type SegmentIds struct {
	GoodUser                                string `dynamic:"true"`
	RewardsAbuser                           string `dynamic:"true"`
	HigherTiersCashbackRewardInEligibleUser string `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Port            int
	HealthCheckPort int
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

type NotificationParams struct {
	IsEnabled                       bool          `dynamic:"true"`
	ExpireAfter                     time.Duration `dynamic:"true"`
	AutoDismissOnHomeAfterInSeconds int32         `dynamic:"true"`
}

type TieringTrialConfig struct {
	// Trials are planned to be run in phases
	// we will have start date and end dates for displaying the entry points
	// Trial will end for all the users on TrialEndDate irrespective of the date on which user opts in for the trial
	// we will give users a trial period of at-least 15 days + 7 days of grace.
	// hence TrialEndDate should be equal to TrialEntryPointEndDate + 22 days
	// tier enrollment will not be done if Trial duration is less than MinimumTrialDuration for the user
	TrialEntryPointStartDate time.Time     `dynamic:"true"`
	TrialEntryPointEndDate   time.Time     `dynamic:"true"`
	TrialEndDate             time.Time     `dynamic:"true"`
	TrialEndDateWithoutGrace time.Time     `dynamic:"true"`
	MinimumTrialDuration     time.Duration `dynamic:"true"`
	PlusTrialSegments        []string      `dynamic:"true"`
	InfiniteTrialSegments    []string      `dynamic:"true"`
	PrimeTrialSegments       []string      `dynamic:"true"`
	TrialIntroWebUrl         string        `dynamic:"true"`
}

// CriteriaSegmentExclusions maps criteria types to their excluded segment IDs
type CriteriaSegmentExclusions struct {
	SalaryB2CExcludedSegments []string `dynamic:"true"`
	AaSalaryExcludedSegments  []string `dynamic:"true"`
	// Future criteria can be added here easily
}

// SherlockBannerConfig contains configuration for a single sherlock banner
type SherlockBannerConfig struct {
	IsEnabled         bool
	EligibleSegmentId string
	Title             string
	Body              string
	BannerType        string
}

// TieringSherlockBannersConfig contains configuration for all tiering sherlock banners
// Banners are ordered by priority (first banner has highest priority)
type TieringSherlockBannersConfig struct {
	Banners []*SherlockBannerConfig
}
