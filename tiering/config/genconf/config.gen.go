// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	common "github.com/epifi/be-common/api/typesv2/common"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	genconfig2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	pinot "github.com/epifi/gamma/pkg/pinot"
	config "github.com/epifi/gamma/tiering/config"
)

type Config struct {
	callbacks               *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                questsdk.Client
	questFieldPath          string
	_CxMovementHistoryLimit int64
	// These min versions should be synced with frontend all plans min versions
	_MinAndroidVersion                     uint32
	_MinIosVersion                         uint32
	_MinAvgMonthlyBalanceForRegularTier    int64
	_MinBalancePenaltyForRegularTier       int64
	_ToShowUpgradeNotifForSalary           uint32
	_ToShowUpgradeNotifForSalaryLite       uint32
	_EnableGoodUserSilentGraceSegmentation uint32
	_EnableRewardsAbuserSegmentation       uint32
	// Feature flag to control whether to use tiering_abuser table for abuser checks
	// When enabled, uses table-based approach instead of segment-based approach
	_EnableTieringAbuserDbCheck       uint32
	_EnableHomeTopBarBanner           uint32
	_EnablePromoWidget                uint32
	_EnableTierDowngrades             uint32
	_IsReactivationPromoWidgetEnabled uint32
	_ShouldRoundGraceToNextDay        uint32
	// TODO(sayan) : remove this field
	_DisplayPropertyKeyExpiryDuration int64
	_AutoUpgradeCutoff                int64
	// This needs to be in sync with config in frontend for profile notch
	_GraceWindowDuration               int64
	_DowngradeWindowDuration           int64
	_GraceInitialWindowDuration        int64
	_DisplayComponentTTLMap            *syncmap.Map[string, time.Duration]
	_NotificationConfigMap             *syncmap.Map[string, *NotificationParams]
	_TierToIneligibleActorSegmentIdMap *syncmap.Map[string, string]
	// map of segments ids mapped to tier enums as string
	// tieringEnumPb.Tier_X.String()
	_SilentGraceTierSegmentsMap *syncmap.Map[string, string]
	// Map of tieringEnumPb.Tier.String() to EODBalanceServiceConfig
	_TierEODBalanceServiceConfigMap               *syncmap.Map[string, *EODBalanceServiceConfig]
	_SegmentIdsForReactivationPromoWidget_3txn    *syncmap.Map[string, *SegmentTxnDetails]
	_SegmentIdsForReactivationPromoWidget_5txn    *syncmap.Map[string, *SegmentTxnDetails]
	_RegularTierLaunchDate                        time.Time
	_RegularTierLaunchDateMutex                   *sync.RWMutex
	_CooloffParams                                *CooloffParams
	_TieringFeatureRelease                        *InternalReleaseConfig
	_TierReEvaluationEventSqsSubscriber           *gencfg.SqsSubscriber
	_ProcessInvestmentEventSubscriber             *gencfg.SqsSubscriber
	_ProcessUsStocksWalletOrderEventSubscriber    *gencfg.SqsSubscriber
	_ProcessKycUpdateEventSubscriber              *gencfg.SqsSubscriber
	_ProcessSalaryUpdateEventSubscriber           *gencfg.SqsSubscriber
	_ProcessBalanceUpdateEventSubscriber          *gencfg.SqsSubscriber
	_ProcessAddFundsOrderEventSubscriber          *gencfg.SqsSubscriber
	_ActorTierInfoCacheConfig                     *CacheConfig
	_EligibleTierMovementCacheConfig              *CacheConfig
	_TierMovementHistoryCacheConfig               *CacheConfig
	_SegmentIds                                   *SegmentIds
	_CriteriaSegmentExclusions                    *CriteriaSegmentExclusions
	_QuestSdk                                     *genconfig.Config
	_ProcessBalanceUpdateEventMarketingSubscriber *gencfg.SqsSubscriber
	_AutoUpgradeConfig                            *AutoUpgradeConfig
	_FeatureReleaseConfig                         *genconfig2.FeatureReleaseConfig
	_TieringTrialConfig                           *TieringTrialConfig
	_TieringBalanceBasedPitchParams               *TieringBalanceBasedPitchParams
	_ABFeatureReleaseConfig                       *genconfig2.ABFeatureReleaseConfig
	_Application                                  *config.Application
	_Server                                       *config.Server
	_EpifiDb                                      *cfg.DB
	_TieringDb                                    *cfg.DB
	_AWS                                          *cfg.AWS
	_Flags                                        *config.Flags
	_Secrets                                      *cfg.Secrets
	_Tracing                                      *cfg.Tracing
	_Profiling                                    *cfg.Profiling
	_GraceParams                                  *config.GraceParams
	_DaoCacheRedisOptions                         *cfg.RedisOptions
	_DisplayPropertiesRedisOptions                *cfg.RedisOptions
	_RudderStack                                  *cfg.RudderStackBroker
	_TierUpdateEventExternalPublisher             *cfg.SnsPublisher
	_QuestRedisOptions                            *cfg.RedisOptions
	_PinotConfig                                  *pinot.Config
	_TierMovementHistoriesDbMaxPageSize           uint32
	_TieringSherlockBannersConfig                 *config.TieringSherlockBannersConfig
}

func (obj *Config) CxMovementHistoryLimit() int {
	return int(atomic.LoadInt64(&obj._CxMovementHistoryLimit))
}

// These min versions should be synced with frontend all plans min versions
func (obj *Config) MinAndroidVersion() uint32 {
	return uint32(atomic.LoadUint32(&obj._MinAndroidVersion))
}
func (obj *Config) MinIosVersion() uint32 {
	return uint32(atomic.LoadUint32(&obj._MinIosVersion))
}
func (obj *Config) MinAvgMonthlyBalanceForRegularTier() int {
	return int(atomic.LoadInt64(&obj._MinAvgMonthlyBalanceForRegularTier))
}
func (obj *Config) MinBalancePenaltyForRegularTier() int {
	return int(atomic.LoadInt64(&obj._MinBalancePenaltyForRegularTier))
}
func (obj *Config) ToShowUpgradeNotifForSalary() bool {
	if atomic.LoadUint32(&obj._ToShowUpgradeNotifForSalary) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) ToShowUpgradeNotifForSalaryLite() bool {
	if atomic.LoadUint32(&obj._ToShowUpgradeNotifForSalaryLite) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableGoodUserSilentGraceSegmentation() bool {
	if atomic.LoadUint32(&obj._EnableGoodUserSilentGraceSegmentation) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableRewardsAbuserSegmentation() bool {
	if atomic.LoadUint32(&obj._EnableRewardsAbuserSegmentation) == 0 {
		return false
	} else {
		return true
	}
}

// Feature flag to control whether to use tiering_abuser table for abuser checks
// When enabled, uses table-based approach instead of segment-based approach
func (obj *Config) EnableTieringAbuserDbCheck() bool {
	if atomic.LoadUint32(&obj._EnableTieringAbuserDbCheck) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableHomeTopBarBanner() bool {
	if atomic.LoadUint32(&obj._EnableHomeTopBarBanner) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnablePromoWidget() bool {
	if atomic.LoadUint32(&obj._EnablePromoWidget) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableTierDowngrades() bool {
	if atomic.LoadUint32(&obj._EnableTierDowngrades) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) IsReactivationPromoWidgetEnabled() bool {
	if atomic.LoadUint32(&obj._IsReactivationPromoWidgetEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) ShouldRoundGraceToNextDay() bool {
	if atomic.LoadUint32(&obj._ShouldRoundGraceToNextDay) == 0 {
		return false
	} else {
		return true
	}
}

// TODO(sayan) : remove this field
func (obj *Config) DisplayPropertyKeyExpiryDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DisplayPropertyKeyExpiryDuration))
}
func (obj *Config) AutoUpgradeCutoff() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._AutoUpgradeCutoff))
}

// This needs to be in sync with config in frontend for profile notch
func (obj *Config) graceWindowDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._GraceWindowDuration))
}

// This needs to be in sync with config in frontend for profile notch
func (obj *Config) GraceWindowDuration(ctx context.Context) time.Duration {
	defVal := obj.graceWindowDuration()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "GraceWindowDuration"}, defVal)
	val, ok := res.(time.Duration)
	if ok {
		return val
	}
	return defVal
}

func (obj *Config) downgradeWindowDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DowngradeWindowDuration))
}
func (obj *Config) DowngradeWindowDuration(ctx context.Context) time.Duration {
	defVal := obj.downgradeWindowDuration()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DowngradeWindowDuration"}, defVal)
	val, ok := res.(time.Duration)
	if ok {
		return val
	}
	return defVal
}

func (obj *Config) graceInitialWindowDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._GraceInitialWindowDuration))
}
func (obj *Config) GraceInitialWindowDuration(ctx context.Context) time.Duration {
	defVal := obj.graceInitialWindowDuration()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "GraceInitialWindowDuration"}, defVal)
	val, ok := res.(time.Duration)
	if ok {
		return val
	}
	return defVal
}

func (obj *Config) DisplayComponentTTLMap() *syncmap.Map[string, time.Duration] {
	return obj._DisplayComponentTTLMap
}
func (obj *Config) NotificationConfigMap() *syncmap.Map[string, *NotificationParams] {
	return obj._NotificationConfigMap
}
func (obj *Config) TierToIneligibleActorSegmentIdMap() *syncmap.Map[string, string] {
	return obj._TierToIneligibleActorSegmentIdMap
}

// map of segments ids mapped to tier enums as string
// tieringEnumPb.Tier_X.String()
func (obj *Config) SilentGraceTierSegmentsMap() *syncmap.Map[string, string] {
	return obj._SilentGraceTierSegmentsMap
}

// Map of tieringEnumPb.Tier.String() to EODBalanceServiceConfig
func (obj *Config) TierEODBalanceServiceConfigMap() *syncmap.Map[string, *EODBalanceServiceConfig] {
	return obj._TierEODBalanceServiceConfigMap
}
func (obj *Config) SegmentIdsForReactivationPromoWidget_3txn() *syncmap.Map[string, *SegmentTxnDetails] {
	return obj._SegmentIdsForReactivationPromoWidget_3txn
}
func (obj *Config) SegmentIdsForReactivationPromoWidget_5txn() *syncmap.Map[string, *SegmentTxnDetails] {
	return obj._SegmentIdsForReactivationPromoWidget_5txn
}
func (obj *Config) RegularTierLaunchDate() time.Time {
	obj._RegularTierLaunchDateMutex.RLock()
	defer obj._RegularTierLaunchDateMutex.RUnlock()
	return obj._RegularTierLaunchDate
}
func (obj *Config) CooloffParams() *CooloffParams {
	return obj._CooloffParams
}
func (obj *Config) TieringFeatureRelease() *InternalReleaseConfig {
	return obj._TieringFeatureRelease
}
func (obj *Config) TierReEvaluationEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._TierReEvaluationEventSqsSubscriber
}
func (obj *Config) ProcessInvestmentEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessInvestmentEventSubscriber
}
func (obj *Config) ProcessUsStocksWalletOrderEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessUsStocksWalletOrderEventSubscriber
}
func (obj *Config) ProcessKycUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessKycUpdateEventSubscriber
}
func (obj *Config) ProcessSalaryUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessSalaryUpdateEventSubscriber
}
func (obj *Config) ProcessBalanceUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessBalanceUpdateEventSubscriber
}
func (obj *Config) ProcessAddFundsOrderEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessAddFundsOrderEventSubscriber
}
func (obj *Config) ActorTierInfoCacheConfig() *CacheConfig {
	return obj._ActorTierInfoCacheConfig
}
func (obj *Config) EligibleTierMovementCacheConfig() *CacheConfig {
	return obj._EligibleTierMovementCacheConfig
}
func (obj *Config) TierMovementHistoryCacheConfig() *CacheConfig {
	return obj._TierMovementHistoryCacheConfig
}
func (obj *Config) SegmentIds() *SegmentIds {
	return obj._SegmentIds
}
func (obj *Config) CriteriaSegmentExclusions() *CriteriaSegmentExclusions {
	return obj._CriteriaSegmentExclusions
}
func (obj *Config) QuestSdk() *genconfig.Config {
	return obj._QuestSdk
}
func (obj *Config) ProcessBalanceUpdateEventMarketingSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessBalanceUpdateEventMarketingSubscriber
}
func (obj *Config) AutoUpgradeConfig() *AutoUpgradeConfig {
	return obj._AutoUpgradeConfig
}
func (obj *Config) FeatureReleaseConfig() *genconfig2.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) TieringTrialConfig() *TieringTrialConfig {
	return obj._TieringTrialConfig
}
func (obj *Config) TieringBalanceBasedPitchParams() *TieringBalanceBasedPitchParams {
	return obj._TieringBalanceBasedPitchParams
}
func (obj *Config) ABFeatureReleaseConfig() *genconfig2.ABFeatureReleaseConfig {
	return obj._ABFeatureReleaseConfig
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) TieringDb() *cfg.DB {
	return obj._TieringDb
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) Flags() *config.Flags {
	return obj._Flags
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) GraceParams() *config.GraceParams {
	return obj._GraceParams
}
func (obj *Config) DaoCacheRedisOptions() *cfg.RedisOptions {
	return obj._DaoCacheRedisOptions
}
func (obj *Config) DisplayPropertiesRedisOptions() *cfg.RedisOptions {
	return obj._DisplayPropertiesRedisOptions
}
func (obj *Config) RudderStack() *cfg.RudderStackBroker {
	return obj._RudderStack
}
func (obj *Config) TierUpdateEventExternalPublisher() *cfg.SnsPublisher {
	return obj._TierUpdateEventExternalPublisher
}
func (obj *Config) QuestRedisOptions() *cfg.RedisOptions {
	return obj._QuestRedisOptions
}
func (obj *Config) PinotConfig() *pinot.Config {
	return obj._PinotConfig
}
func (obj *Config) TierMovementHistoriesDbMaxPageSize() uint32 {
	return obj._TierMovementHistoriesDbMaxPageSize
}
func (obj *Config) TieringSherlockBannersConfig() *config.TieringSherlockBannersConfig {
	return obj._TieringSherlockBannersConfig
}

type CooloffParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxMovementsAllowed      int32
	_EvaluationWindowDuration int64
}

func (obj *CooloffParams) MaxMovementsAllowed() int64 {
	return int64(atomic.LoadInt32(&obj._MaxMovementsAllowed))
}
func (obj *CooloffParams) EvaluationWindowDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._EvaluationWindowDuration))
}

type InternalReleaseConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Percentage rollout for external users
	// NOTE: This will not be used for other user groups
	_RolloutPercentage uint32
	// Global flag for feature whether it is enabled or not
	_IsFeatureEnabled uint32
	_AllowedGroups    []common.UserGroup
}

// Percentage rollout for external users
// NOTE: This will not be used for other user groups
func (obj *InternalReleaseConfig) RolloutPercentage() uint32 {
	return uint32(atomic.LoadUint32(&obj._RolloutPercentage))
}

// Global flag for feature whether it is enabled or not
func (obj *InternalReleaseConfig) IsFeatureEnabled() bool {
	if atomic.LoadUint32(&obj._IsFeatureEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *InternalReleaseConfig) AllowedGroups() []common.UserGroup {
	return obj._AllowedGroups
}

type NotificationParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_AutoDismissOnHomeAfterInSeconds int32
	_IsEnabled                       uint32
	_ExpireAfter                     int64
}

func (obj *NotificationParams) AutoDismissOnHomeAfterInSeconds() int32 {
	return int32(atomic.LoadInt32(&obj._AutoDismissOnHomeAfterInSeconds))
}
func (obj *NotificationParams) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *NotificationParams) ExpireAfter() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ExpireAfter))
}

type CacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	// duration for which actor data should be cached
	_CacheTTl int64
	_Prefix   string
}

func (obj *CacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// duration for which actor data should be cached
func (obj *CacheConfig) CacheTTl() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTl))
}
func (obj *CacheConfig) Prefix() string {
	return obj._Prefix
}

type SegmentIds struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_GoodUser                                     string
	_GoodUserMutex                                *sync.RWMutex
	_RewardsAbuser                                string
	_RewardsAbuserMutex                           *sync.RWMutex
	_HigherTiersCashbackRewardInEligibleUser      string
	_HigherTiersCashbackRewardInEligibleUserMutex *sync.RWMutex
}

func (obj *SegmentIds) GoodUser() string {
	obj._GoodUserMutex.RLock()
	defer obj._GoodUserMutex.RUnlock()
	return obj._GoodUser
}
func (obj *SegmentIds) RewardsAbuser() string {
	obj._RewardsAbuserMutex.RLock()
	defer obj._RewardsAbuserMutex.RUnlock()
	return obj._RewardsAbuser
}
func (obj *SegmentIds) HigherTiersCashbackRewardInEligibleUser() string {
	obj._HigherTiersCashbackRewardInEligibleUserMutex.RLock()
	defer obj._HigherTiersCashbackRewardInEligibleUserMutex.RUnlock()
	return obj._HigherTiersCashbackRewardInEligibleUser
}

type CriteriaSegmentExclusions struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_SalaryB2CExcludedSegments      roarray.ROArray[string]
	_SalaryB2CExcludedSegmentsMutex *sync.RWMutex
	_AaSalaryExcludedSegments       roarray.ROArray[string]
	_AaSalaryExcludedSegmentsMutex  *sync.RWMutex
}

func (obj *CriteriaSegmentExclusions) SalaryB2CExcludedSegments() roarray.ROArray[string] {
	obj._SalaryB2CExcludedSegmentsMutex.RLock()
	defer obj._SalaryB2CExcludedSegmentsMutex.RUnlock()
	return obj._SalaryB2CExcludedSegments
}
func (obj *CriteriaSegmentExclusions) AaSalaryExcludedSegments() roarray.ROArray[string] {
	obj._AaSalaryExcludedSegmentsMutex.RLock()
	defer obj._AaSalaryExcludedSegmentsMutex.RUnlock()
	return obj._AaSalaryExcludedSegments
}

type EODBalanceServiceConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Minimum days of aggregation to consider that average is valid
	// Eg: If pinot service returns data for 19 days but minimum number of days for accurate data is
	// 20 days then fallback to other methods
	_MinAggregationDays uint32
	// Average balance threshold criteria
	_AverageBalanceThreshold int32
	_Enable                  uint32
	// Duration of EOD balance aggregation
	_EODBalanceAggregateDuration int64
}

// Minimum days of aggregation to consider that average is valid
// Eg: If pinot service returns data for 19 days but minimum number of days for accurate data is
// 20 days then fallback to other methods
func (obj *EODBalanceServiceConfig) MinAggregationDays() uint32 {
	return uint32(atomic.LoadUint32(&obj._MinAggregationDays))
}

// Average balance threshold criteria
func (obj *EODBalanceServiceConfig) AverageBalanceThreshold() int64 {
	return int64(atomic.LoadInt32(&obj._AverageBalanceThreshold))
}
func (obj *EODBalanceServiceConfig) Enable() bool {
	if atomic.LoadUint32(&obj._Enable) == 0 {
		return false
	} else {
		return true
	}
}

// Duration of EOD balance aggregation
func (obj *EODBalanceServiceConfig) EODBalanceAggregateDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._EODBalanceAggregateDuration))
}

type AutoUpgradeConfig struct {
	callbacks                      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                       questsdk.Client
	questFieldPath                 string
	_DisableProvenanceCheck        uint32
	_DisableSalaryTierCheck        uint32
	_DisableFirstTierMovementCheck uint32
	_DisableAutoUpgradeCutoffCheck uint32
	_DisableHigherTierCheck        uint32
	_DisableUserSpecificChecks     uint32
	// below checks are enabled only on unsetting DisableUserSpecificChecks
	// if enabled, auto upgrades user if they were in plus/infinite tier earlier
	_DisableBeenInTieringCheck  uint32
	_DisableAverageBalanceCheck uint32
	_AvgBalanceDurationBuffer   int64
	// Map of tieringEnumPb.Tier.String() to map of BalanceDuration.String() to balance threshold
	_AutoUpgradeAvgBalanceConditionMap *syncmap.Map[string, *AverageBalanceConditions]
	_DisableToTierConditionMap         *TierConditionMap
}

func (obj *AutoUpgradeConfig) DisableProvenanceCheck() bool {
	if atomic.LoadUint32(&obj._DisableProvenanceCheck) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AutoUpgradeConfig) DisableSalaryTierCheck() bool {
	if atomic.LoadUint32(&obj._DisableSalaryTierCheck) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AutoUpgradeConfig) DisableFirstTierMovementCheck() bool {
	if atomic.LoadUint32(&obj._DisableFirstTierMovementCheck) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AutoUpgradeConfig) DisableAutoUpgradeCutoffCheck() bool {
	if atomic.LoadUint32(&obj._DisableAutoUpgradeCutoffCheck) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AutoUpgradeConfig) DisableHigherTierCheck() bool {
	if atomic.LoadUint32(&obj._DisableHigherTierCheck) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AutoUpgradeConfig) DisableUserSpecificChecks() bool {
	if atomic.LoadUint32(&obj._DisableUserSpecificChecks) == 0 {
		return false
	} else {
		return true
	}
}

// below checks are enabled only on unsetting DisableUserSpecificChecks
// if enabled, auto upgrades user if they were in plus/infinite tier earlier
func (obj *AutoUpgradeConfig) disableBeenInTieringCheck() bool {
	if atomic.LoadUint32(&obj._DisableBeenInTieringCheck) == 0 {
		return false
	} else {
		return true
	}
}

// below checks are enabled only on unsetting DisableUserSpecificChecks
// if enabled, auto upgrades user if they were in plus/infinite tier earlier
func (obj *AutoUpgradeConfig) DisableBeenInTieringCheck(ctx context.Context) bool {
	defVal := obj.disableBeenInTieringCheck()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DisableBeenInTieringCheck"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *AutoUpgradeConfig) DisableAverageBalanceCheck() bool {
	if atomic.LoadUint32(&obj._DisableAverageBalanceCheck) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AutoUpgradeConfig) AvgBalanceDurationBuffer() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._AvgBalanceDurationBuffer))
}

// Map of tieringEnumPb.Tier.String() to map of BalanceDuration.String() to balance threshold
func (obj *AutoUpgradeConfig) AutoUpgradeAvgBalanceConditionMap() *syncmap.Map[string, *AverageBalanceConditions] {
	return obj._AutoUpgradeAvgBalanceConditionMap
}
func (obj *AutoUpgradeConfig) DisableToTierConditionMap() *TierConditionMap {
	return obj._DisableToTierConditionMap
}

type AverageBalanceConditions struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Map of balance duration (denominator for calculating average) to threshold balance
	_AverageBalanceConditionMap *syncmap.Map[string, int64]
}

// Map of balance duration (denominator for calculating average) to threshold balance
func (obj *AverageBalanceConditions) AverageBalanceConditionMap() *syncmap.Map[string, int64] {
	return obj._AverageBalanceConditionMap
}

type TierConditionMap struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_Regular       uint32
	_Standard      uint32
	_Plus          uint32
	_Infinite      uint32
	_AaSalary      uint32
	_SalaryLite    uint32
	_Salary        uint32
	_SalaryBasic   uint32
}

func (obj *TierConditionMap) regular() bool {
	if atomic.LoadUint32(&obj._Regular) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TierConditionMap) Regular(ctx context.Context) bool {
	defVal := obj.regular()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Regular"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *TierConditionMap) standard() bool {
	if atomic.LoadUint32(&obj._Standard) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TierConditionMap) Standard(ctx context.Context) bool {
	defVal := obj.standard()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Standard"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *TierConditionMap) plus() bool {
	if atomic.LoadUint32(&obj._Plus) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TierConditionMap) Plus(ctx context.Context) bool {
	defVal := obj.plus()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Plus"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *TierConditionMap) infinite() bool {
	if atomic.LoadUint32(&obj._Infinite) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TierConditionMap) Infinite(ctx context.Context) bool {
	defVal := obj.infinite()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Infinite"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *TierConditionMap) aaSalary() bool {
	if atomic.LoadUint32(&obj._AaSalary) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TierConditionMap) AaSalary(ctx context.Context) bool {
	defVal := obj.aaSalary()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "AaSalary"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *TierConditionMap) salaryLite() bool {
	if atomic.LoadUint32(&obj._SalaryLite) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TierConditionMap) SalaryLite(ctx context.Context) bool {
	defVal := obj.salaryLite()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "SalaryLite"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *TierConditionMap) salary() bool {
	if atomic.LoadUint32(&obj._Salary) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TierConditionMap) Salary(ctx context.Context) bool {
	defVal := obj.salary()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Salary"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *TierConditionMap) salaryBasic() bool {
	if atomic.LoadUint32(&obj._SalaryBasic) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TierConditionMap) SalaryBasic(ctx context.Context) bool {
	defVal := obj.salaryBasic()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "SalaryBasic"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

type SegmentTxnDetails struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_StartDate    int64
	_EndDate      int64
	_RewardAmount int64
}

func (obj *SegmentTxnDetails) StartDate() int {
	return int(atomic.LoadInt64(&obj._StartDate))
}
func (obj *SegmentTxnDetails) EndDate() int {
	return int(atomic.LoadInt64(&obj._EndDate))
}
func (obj *SegmentTxnDetails) RewardAmount() int {
	return int(atomic.LoadInt64(&obj._RewardAmount))
}

type TieringTrialConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinimumTrialDuration       int64
	_PlusTrialSegments          roarray.ROArray[string]
	_PlusTrialSegmentsMutex     *sync.RWMutex
	_InfiniteTrialSegments      roarray.ROArray[string]
	_InfiniteTrialSegmentsMutex *sync.RWMutex
	_PrimeTrialSegments         roarray.ROArray[string]
	_PrimeTrialSegmentsMutex    *sync.RWMutex
	// Trials are planned to be run in phases
	// we will have start date and end dates for displaying the entry points
	// Trial will end for all the users on TrialEndDate irrespective of the date on which user opts in for the trial
	// we will give users a trial period of at-least 15 days + 7 days of grace.
	// hence TrialEndDate should be equal to TrialEntryPointEndDate + 22 days
	// tier enrollment will not be done if Trial duration is less than MinimumTrialDuration for the user
	_TrialEntryPointStartDate      time.Time
	_TrialEntryPointStartDateMutex *sync.RWMutex
	_TrialEntryPointEndDate        time.Time
	_TrialEntryPointEndDateMutex   *sync.RWMutex
	_TrialEndDate                  time.Time
	_TrialEndDateMutex             *sync.RWMutex
	_TrialEndDateWithoutGrace      time.Time
	_TrialEndDateWithoutGraceMutex *sync.RWMutex
	_TrialIntroWebUrl              string
	_TrialIntroWebUrlMutex         *sync.RWMutex
}

func (obj *TieringTrialConfig) MinimumTrialDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MinimumTrialDuration))
}
func (obj *TieringTrialConfig) PlusTrialSegments() roarray.ROArray[string] {
	obj._PlusTrialSegmentsMutex.RLock()
	defer obj._PlusTrialSegmentsMutex.RUnlock()
	return obj._PlusTrialSegments
}
func (obj *TieringTrialConfig) InfiniteTrialSegments() roarray.ROArray[string] {
	obj._InfiniteTrialSegmentsMutex.RLock()
	defer obj._InfiniteTrialSegmentsMutex.RUnlock()
	return obj._InfiniteTrialSegments
}
func (obj *TieringTrialConfig) PrimeTrialSegments() roarray.ROArray[string] {
	obj._PrimeTrialSegmentsMutex.RLock()
	defer obj._PrimeTrialSegmentsMutex.RUnlock()
	return obj._PrimeTrialSegments
}

// Trials are planned to be run in phases
// we will have start date and end dates for displaying the entry points
// Trial will end for all the users on TrialEndDate irrespective of the date on which user opts in for the trial
// we will give users a trial period of at-least 15 days + 7 days of grace.
// hence TrialEndDate should be equal to TrialEntryPointEndDate + 22 days
// tier enrollment will not be done if Trial duration is less than MinimumTrialDuration for the user
func (obj *TieringTrialConfig) TrialEntryPointStartDate() time.Time {
	obj._TrialEntryPointStartDateMutex.RLock()
	defer obj._TrialEntryPointStartDateMutex.RUnlock()
	return obj._TrialEntryPointStartDate
}
func (obj *TieringTrialConfig) TrialEntryPointEndDate() time.Time {
	obj._TrialEntryPointEndDateMutex.RLock()
	defer obj._TrialEntryPointEndDateMutex.RUnlock()
	return obj._TrialEntryPointEndDate
}
func (obj *TieringTrialConfig) TrialEndDate() time.Time {
	obj._TrialEndDateMutex.RLock()
	defer obj._TrialEndDateMutex.RUnlock()
	return obj._TrialEndDate
}
func (obj *TieringTrialConfig) TrialEndDateWithoutGrace() time.Time {
	obj._TrialEndDateWithoutGraceMutex.RLock()
	defer obj._TrialEndDateWithoutGraceMutex.RUnlock()
	return obj._TrialEndDateWithoutGrace
}
func (obj *TieringTrialConfig) TrialIntroWebUrl() string {
	obj._TrialIntroWebUrlMutex.RLock()
	defer obj._TrialIntroWebUrlMutex.RUnlock()
	return obj._TrialIntroWebUrl
}

type TieringBalanceBasedPitchParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_BalanceForPitchingInfiniteForBasic int32
	_BalanceForPitchingPrimeForBasic    int32
}

func (obj *TieringBalanceBasedPitchParams) BalanceForPitchingInfiniteForBasic() int64 {
	return int64(atomic.LoadInt32(&obj._BalanceForPitchingInfiniteForBasic))
}
func (obj *TieringBalanceBasedPitchParams) BalanceForPitchingPrimeForBasic() int64 {
	return int64(atomic.LoadInt32(&obj._BalanceForPitchingPrimeForBasic))
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["cxmovementhistorylimit"] = _obj.SetCxMovementHistoryLimit
	_setters["minandroidversion"] = _obj.SetMinAndroidVersion
	_setters["miniosversion"] = _obj.SetMinIosVersion
	_setters["minavgmonthlybalanceforregulartier"] = _obj.SetMinAvgMonthlyBalanceForRegularTier
	_setters["minbalancepenaltyforregulartier"] = _obj.SetMinBalancePenaltyForRegularTier
	_setters["toshowupgradenotifforsalary"] = _obj.SetToShowUpgradeNotifForSalary
	_setters["toshowupgradenotifforsalarylite"] = _obj.SetToShowUpgradeNotifForSalaryLite
	_setters["enablegoodusersilentgracesegmentation"] = _obj.SetEnableGoodUserSilentGraceSegmentation
	_setters["enablerewardsabusersegmentation"] = _obj.SetEnableRewardsAbuserSegmentation
	_setters["enabletieringabuserdbcheck"] = _obj.SetEnableTieringAbuserDbCheck
	_setters["enablehometopbarbanner"] = _obj.SetEnableHomeTopBarBanner
	_setters["enablepromowidget"] = _obj.SetEnablePromoWidget
	_setters["enabletierdowngrades"] = _obj.SetEnableTierDowngrades
	_setters["isreactivationpromowidgetenabled"] = _obj.SetIsReactivationPromoWidgetEnabled
	_setters["shouldroundgracetonextday"] = _obj.SetShouldRoundGraceToNextDay
	_setters["displaypropertykeyexpiryduration"] = _obj.SetDisplayPropertyKeyExpiryDuration
	_setters["autoupgradecutoff"] = _obj.SetAutoUpgradeCutoff
	_setters["gracewindowduration"] = _obj.SetGraceWindowDuration
	_setters["downgradewindowduration"] = _obj.SetDowngradeWindowDuration
	_setters["graceinitialwindowduration"] = _obj.SetGraceInitialWindowDuration

	_obj._DisplayComponentTTLMap = &syncmap.Map[string, time.Duration]{}
	_setters["displaycomponentttlmap"] = _obj.SetDisplayComponentTTLMap

	_obj._NotificationConfigMap = &syncmap.Map[string, *NotificationParams]{}
	_setters["notificationconfigmap"] = _obj.SetNotificationConfigMap

	_obj._TierToIneligibleActorSegmentIdMap = &syncmap.Map[string, string]{}
	_setters["tiertoineligibleactorsegmentidmap"] = _obj.SetTierToIneligibleActorSegmentIdMap

	_obj._SilentGraceTierSegmentsMap = &syncmap.Map[string, string]{}
	_setters["silentgracetiersegmentsmap"] = _obj.SetSilentGraceTierSegmentsMap

	_obj._TierEODBalanceServiceConfigMap = &syncmap.Map[string, *EODBalanceServiceConfig]{}
	_setters["tiereodbalanceserviceconfigmap"] = _obj.SetTierEODBalanceServiceConfigMap

	_obj._SegmentIdsForReactivationPromoWidget_3txn = &syncmap.Map[string, *SegmentTxnDetails]{}
	_setters["segmentidsforreactivationpromowidget_3txn"] = _obj.SetSegmentIdsForReactivationPromoWidget_3txn

	_obj._SegmentIdsForReactivationPromoWidget_5txn = &syncmap.Map[string, *SegmentTxnDetails]{}
	_setters["segmentidsforreactivationpromowidget_5txn"] = _obj.SetSegmentIdsForReactivationPromoWidget_5txn
	_setters["regulartierlaunchdate"] = _obj.SetRegularTierLaunchDate
	_obj._RegularTierLaunchDateMutex = &sync.RWMutex{}
	_CooloffParams, _fieldSetters := NewCooloffParams()
	_obj._CooloffParams = _CooloffParams
	helper.AddFieldSetters("cooloffparams", _fieldSetters, _setters)
	_TieringFeatureRelease, _fieldSetters := NewInternalReleaseConfig()
	_obj._TieringFeatureRelease = _TieringFeatureRelease
	helper.AddFieldSetters("tieringfeaturerelease", _fieldSetters, _setters)
	_TierReEvaluationEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TierReEvaluationEventSqsSubscriber = _TierReEvaluationEventSqsSubscriber
	helper.AddFieldSetters("tierreevaluationeventsqssubscriber", _fieldSetters, _setters)
	_ProcessInvestmentEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessInvestmentEventSubscriber = _ProcessInvestmentEventSubscriber
	helper.AddFieldSetters("processinvestmenteventsubscriber", _fieldSetters, _setters)
	_ProcessUsStocksWalletOrderEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessUsStocksWalletOrderEventSubscriber = _ProcessUsStocksWalletOrderEventSubscriber
	helper.AddFieldSetters("processusstockswalletordereventsubscriber", _fieldSetters, _setters)
	_ProcessKycUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessKycUpdateEventSubscriber = _ProcessKycUpdateEventSubscriber
	helper.AddFieldSetters("processkycupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessSalaryUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessSalaryUpdateEventSubscriber = _ProcessSalaryUpdateEventSubscriber
	helper.AddFieldSetters("processsalaryupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessBalanceUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessBalanceUpdateEventSubscriber = _ProcessBalanceUpdateEventSubscriber
	helper.AddFieldSetters("processbalanceupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessAddFundsOrderEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessAddFundsOrderEventSubscriber = _ProcessAddFundsOrderEventSubscriber
	helper.AddFieldSetters("processaddfundsordereventsubscriber", _fieldSetters, _setters)
	_ActorTierInfoCacheConfig, _fieldSetters := NewCacheConfig()
	_obj._ActorTierInfoCacheConfig = _ActorTierInfoCacheConfig
	helper.AddFieldSetters("actortierinfocacheconfig", _fieldSetters, _setters)
	_EligibleTierMovementCacheConfig, _fieldSetters := NewCacheConfig()
	_obj._EligibleTierMovementCacheConfig = _EligibleTierMovementCacheConfig
	helper.AddFieldSetters("eligibletiermovementcacheconfig", _fieldSetters, _setters)
	_TierMovementHistoryCacheConfig, _fieldSetters := NewCacheConfig()
	_obj._TierMovementHistoryCacheConfig = _TierMovementHistoryCacheConfig
	helper.AddFieldSetters("tiermovementhistorycacheconfig", _fieldSetters, _setters)
	_SegmentIds, _fieldSetters := NewSegmentIds()
	_obj._SegmentIds = _SegmentIds
	helper.AddFieldSetters("segmentids", _fieldSetters, _setters)
	_CriteriaSegmentExclusions, _fieldSetters := NewCriteriaSegmentExclusions()
	_obj._CriteriaSegmentExclusions = _CriteriaSegmentExclusions
	helper.AddFieldSetters("criteriasegmentexclusions", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_ProcessBalanceUpdateEventMarketingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessBalanceUpdateEventMarketingSubscriber = _ProcessBalanceUpdateEventMarketingSubscriber
	helper.AddFieldSetters("processbalanceupdateeventmarketingsubscriber", _fieldSetters, _setters)
	_AutoUpgradeConfig, _fieldSetters := NewAutoUpgradeConfig()
	_obj._AutoUpgradeConfig = _AutoUpgradeConfig
	helper.AddFieldSetters("autoupgradeconfig", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig2.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_TieringTrialConfig, _fieldSetters := NewTieringTrialConfig()
	_obj._TieringTrialConfig = _TieringTrialConfig
	helper.AddFieldSetters("tieringtrialconfig", _fieldSetters, _setters)
	_TieringBalanceBasedPitchParams, _fieldSetters := NewTieringBalanceBasedPitchParams()
	_obj._TieringBalanceBasedPitchParams = _TieringBalanceBasedPitchParams
	helper.AddFieldSetters("tieringbalancebasedpitchparams", _fieldSetters, _setters)
	_ABFeatureReleaseConfig, _fieldSetters := genconfig2.NewABFeatureReleaseConfig()
	_obj._ABFeatureReleaseConfig = _ABFeatureReleaseConfig
	helper.AddFieldSetters("abfeaturereleaseconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["cxmovementhistorylimit"] = _obj.SetCxMovementHistoryLimit
	_setters["minandroidversion"] = _obj.SetMinAndroidVersion
	_setters["miniosversion"] = _obj.SetMinIosVersion
	_setters["minavgmonthlybalanceforregulartier"] = _obj.SetMinAvgMonthlyBalanceForRegularTier
	_setters["minbalancepenaltyforregulartier"] = _obj.SetMinBalancePenaltyForRegularTier
	_setters["toshowupgradenotifforsalary"] = _obj.SetToShowUpgradeNotifForSalary
	_setters["toshowupgradenotifforsalarylite"] = _obj.SetToShowUpgradeNotifForSalaryLite
	_setters["enablegoodusersilentgracesegmentation"] = _obj.SetEnableGoodUserSilentGraceSegmentation
	_setters["enablerewardsabusersegmentation"] = _obj.SetEnableRewardsAbuserSegmentation
	_setters["enabletieringabuserdbcheck"] = _obj.SetEnableTieringAbuserDbCheck
	_setters["enablehometopbarbanner"] = _obj.SetEnableHomeTopBarBanner
	_setters["enablepromowidget"] = _obj.SetEnablePromoWidget
	_setters["enabletierdowngrades"] = _obj.SetEnableTierDowngrades
	_setters["isreactivationpromowidgetenabled"] = _obj.SetIsReactivationPromoWidgetEnabled
	_setters["shouldroundgracetonextday"] = _obj.SetShouldRoundGraceToNextDay
	_setters["displaypropertykeyexpiryduration"] = _obj.SetDisplayPropertyKeyExpiryDuration
	_setters["autoupgradecutoff"] = _obj.SetAutoUpgradeCutoff
	_setters["gracewindowduration"] = _obj.SetGraceWindowDuration
	_setters["downgradewindowduration"] = _obj.SetDowngradeWindowDuration
	_setters["graceinitialwindowduration"] = _obj.SetGraceInitialWindowDuration

	_obj._DisplayComponentTTLMap = &syncmap.Map[string, time.Duration]{}
	_setters["displaycomponentttlmap"] = _obj.SetDisplayComponentTTLMap

	_obj._NotificationConfigMap = &syncmap.Map[string, *NotificationParams]{}
	_setters["notificationconfigmap"] = _obj.SetNotificationConfigMap

	_obj._TierToIneligibleActorSegmentIdMap = &syncmap.Map[string, string]{}
	_setters["tiertoineligibleactorsegmentidmap"] = _obj.SetTierToIneligibleActorSegmentIdMap

	_obj._SilentGraceTierSegmentsMap = &syncmap.Map[string, string]{}
	_setters["silentgracetiersegmentsmap"] = _obj.SetSilentGraceTierSegmentsMap

	_obj._TierEODBalanceServiceConfigMap = &syncmap.Map[string, *EODBalanceServiceConfig]{}
	_setters["tiereodbalanceserviceconfigmap"] = _obj.SetTierEODBalanceServiceConfigMap

	_obj._SegmentIdsForReactivationPromoWidget_3txn = &syncmap.Map[string, *SegmentTxnDetails]{}
	_setters["segmentidsforreactivationpromowidget_3txn"] = _obj.SetSegmentIdsForReactivationPromoWidget_3txn

	_obj._SegmentIdsForReactivationPromoWidget_5txn = &syncmap.Map[string, *SegmentTxnDetails]{}
	_setters["segmentidsforreactivationpromowidget_5txn"] = _obj.SetSegmentIdsForReactivationPromoWidget_5txn
	_setters["regulartierlaunchdate"] = _obj.SetRegularTierLaunchDate
	_obj._RegularTierLaunchDateMutex = &sync.RWMutex{}
	_CooloffParams, _fieldSetters := NewCooloffParams()
	_obj._CooloffParams = _CooloffParams
	helper.AddFieldSetters("cooloffparams", _fieldSetters, _setters)
	_TieringFeatureRelease, _fieldSetters := NewInternalReleaseConfig()
	_obj._TieringFeatureRelease = _TieringFeatureRelease
	helper.AddFieldSetters("tieringfeaturerelease", _fieldSetters, _setters)
	_TierReEvaluationEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TierReEvaluationEventSqsSubscriber = _TierReEvaluationEventSqsSubscriber
	helper.AddFieldSetters("tierreevaluationeventsqssubscriber", _fieldSetters, _setters)
	_ProcessInvestmentEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessInvestmentEventSubscriber = _ProcessInvestmentEventSubscriber
	helper.AddFieldSetters("processinvestmenteventsubscriber", _fieldSetters, _setters)
	_ProcessUsStocksWalletOrderEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessUsStocksWalletOrderEventSubscriber = _ProcessUsStocksWalletOrderEventSubscriber
	helper.AddFieldSetters("processusstockswalletordereventsubscriber", _fieldSetters, _setters)
	_ProcessKycUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessKycUpdateEventSubscriber = _ProcessKycUpdateEventSubscriber
	helper.AddFieldSetters("processkycupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessSalaryUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessSalaryUpdateEventSubscriber = _ProcessSalaryUpdateEventSubscriber
	helper.AddFieldSetters("processsalaryupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessBalanceUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessBalanceUpdateEventSubscriber = _ProcessBalanceUpdateEventSubscriber
	helper.AddFieldSetters("processbalanceupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessAddFundsOrderEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessAddFundsOrderEventSubscriber = _ProcessAddFundsOrderEventSubscriber
	helper.AddFieldSetters("processaddfundsordereventsubscriber", _fieldSetters, _setters)
	_ActorTierInfoCacheConfig, _fieldSetters := NewCacheConfig()
	_obj._ActorTierInfoCacheConfig = _ActorTierInfoCacheConfig
	helper.AddFieldSetters("actortierinfocacheconfig", _fieldSetters, _setters)
	_EligibleTierMovementCacheConfig, _fieldSetters := NewCacheConfig()
	_obj._EligibleTierMovementCacheConfig = _EligibleTierMovementCacheConfig
	helper.AddFieldSetters("eligibletiermovementcacheconfig", _fieldSetters, _setters)
	_TierMovementHistoryCacheConfig, _fieldSetters := NewCacheConfig()
	_obj._TierMovementHistoryCacheConfig = _TierMovementHistoryCacheConfig
	helper.AddFieldSetters("tiermovementhistorycacheconfig", _fieldSetters, _setters)
	_SegmentIds, _fieldSetters := NewSegmentIds()
	_obj._SegmentIds = _SegmentIds
	helper.AddFieldSetters("segmentids", _fieldSetters, _setters)
	_CriteriaSegmentExclusions, _fieldSetters := NewCriteriaSegmentExclusions()
	_obj._CriteriaSegmentExclusions = _CriteriaSegmentExclusions
	helper.AddFieldSetters("criteriasegmentexclusions", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_ProcessBalanceUpdateEventMarketingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessBalanceUpdateEventMarketingSubscriber = _ProcessBalanceUpdateEventMarketingSubscriber
	helper.AddFieldSetters("processbalanceupdateeventmarketingsubscriber", _fieldSetters, _setters)
	_AutoUpgradeConfig, _fieldSetters := NewAutoUpgradeConfigWithQuest(questFieldPath + "/" + "AutoUpgradeConfig")
	_obj._AutoUpgradeConfig = _AutoUpgradeConfig
	helper.AddFieldSetters("autoupgradeconfig", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig2.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_TieringTrialConfig, _fieldSetters := NewTieringTrialConfig()
	_obj._TieringTrialConfig = _TieringTrialConfig
	helper.AddFieldSetters("tieringtrialconfig", _fieldSetters, _setters)
	_TieringBalanceBasedPitchParams, _fieldSetters := NewTieringBalanceBasedPitchParams()
	_obj._TieringBalanceBasedPitchParams = _TieringBalanceBasedPitchParams
	helper.AddFieldSetters("tieringbalancebasedpitchparams", _fieldSetters, _setters)
	_ABFeatureReleaseConfig, _fieldSetters := genconfig2.NewABFeatureReleaseConfig()
	_obj._ABFeatureReleaseConfig = _ABFeatureReleaseConfig
	helper.AddFieldSetters("abfeaturereleaseconfig", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._AutoUpgradeConfig.SetQuestSDK(questSdk)
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "GraceWindowDuration",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_DURATION},
		Area:     "Tiering", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DowngradeWindowDuration",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_DURATION},
		Area:     "Tiering", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "GraceInitialWindowDuration",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_DURATION},
		Area:     "Tiering", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._AutoUpgradeConfig.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "Tiering" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "cxmovementhistorylimit":
		return obj.SetCxMovementHistoryLimit(v.CxMovementHistoryLimit, true, nil)
	case "minandroidversion":
		return obj.SetMinAndroidVersion(v.MinAndroidVersion, true, nil)
	case "miniosversion":
		return obj.SetMinIosVersion(v.MinIosVersion, true, nil)
	case "minavgmonthlybalanceforregulartier":
		return obj.SetMinAvgMonthlyBalanceForRegularTier(v.MinAvgMonthlyBalanceForRegularTier, true, nil)
	case "minbalancepenaltyforregulartier":
		return obj.SetMinBalancePenaltyForRegularTier(v.MinBalancePenaltyForRegularTier, true, nil)
	case "toshowupgradenotifforsalary":
		return obj.SetToShowUpgradeNotifForSalary(v.ToShowUpgradeNotifForSalary, true, nil)
	case "toshowupgradenotifforsalarylite":
		return obj.SetToShowUpgradeNotifForSalaryLite(v.ToShowUpgradeNotifForSalaryLite, true, nil)
	case "enablegoodusersilentgracesegmentation":
		return obj.SetEnableGoodUserSilentGraceSegmentation(v.EnableGoodUserSilentGraceSegmentation, true, nil)
	case "enablerewardsabusersegmentation":
		return obj.SetEnableRewardsAbuserSegmentation(v.EnableRewardsAbuserSegmentation, true, nil)
	case "enabletieringabuserdbcheck":
		return obj.SetEnableTieringAbuserDbCheck(v.EnableTieringAbuserDbCheck, true, nil)
	case "enablehometopbarbanner":
		return obj.SetEnableHomeTopBarBanner(v.EnableHomeTopBarBanner, true, nil)
	case "enablepromowidget":
		return obj.SetEnablePromoWidget(v.EnablePromoWidget, true, nil)
	case "enabletierdowngrades":
		return obj.SetEnableTierDowngrades(v.EnableTierDowngrades, true, nil)
	case "isreactivationpromowidgetenabled":
		return obj.SetIsReactivationPromoWidgetEnabled(v.IsReactivationPromoWidgetEnabled, true, nil)
	case "shouldroundgracetonextday":
		return obj.SetShouldRoundGraceToNextDay(v.ShouldRoundGraceToNextDay, true, nil)
	case "displaypropertykeyexpiryduration":
		return obj.SetDisplayPropertyKeyExpiryDuration(v.DisplayPropertyKeyExpiryDuration, true, nil)
	case "autoupgradecutoff":
		return obj.SetAutoUpgradeCutoff(v.AutoUpgradeCutoff, true, nil)
	case "gracewindowduration":
		return obj.SetGraceWindowDuration(v.GraceWindowDuration, true, nil)
	case "downgradewindowduration":
		return obj.SetDowngradeWindowDuration(v.DowngradeWindowDuration, true, nil)
	case "graceinitialwindowduration":
		return obj.SetGraceInitialWindowDuration(v.GraceInitialWindowDuration, true, nil)
	case "displaycomponentttlmap":
		return obj.SetDisplayComponentTTLMap(v.DisplayComponentTTLMap, true, path)
	case "notificationconfigmap":
		return obj.SetNotificationConfigMap(v.NotificationConfigMap, true, path)
	case "tiertoineligibleactorsegmentidmap":
		return obj.SetTierToIneligibleActorSegmentIdMap(v.TierToIneligibleActorSegmentIdMap, true, path)
	case "silentgracetiersegmentsmap":
		return obj.SetSilentGraceTierSegmentsMap(v.SilentGraceTierSegmentsMap, true, path)
	case "tiereodbalanceserviceconfigmap":
		return obj.SetTierEODBalanceServiceConfigMap(v.TierEODBalanceServiceConfigMap, true, path)
	case "segmentidsforreactivationpromowidget_3txn":
		return obj.SetSegmentIdsForReactivationPromoWidget_3txn(v.SegmentIdsForReactivationPromoWidget_3txn, true, path)
	case "segmentidsforreactivationpromowidget_5txn":
		return obj.SetSegmentIdsForReactivationPromoWidget_5txn(v.SegmentIdsForReactivationPromoWidget_5txn, true, path)
	case "regulartierlaunchdate":
		return obj.SetRegularTierLaunchDate(v.RegularTierLaunchDate, true, nil)
	case "cooloffparams":
		return obj._CooloffParams.Set(v.CooloffParams, true, path)
	case "tieringfeaturerelease":
		return obj._TieringFeatureRelease.Set(v.TieringFeatureRelease, true, path)
	case "tierreevaluationeventsqssubscriber":
		return obj._TierReEvaluationEventSqsSubscriber.Set(v.TierReEvaluationEventSqsSubscriber, true, path)
	case "processinvestmenteventsubscriber":
		return obj._ProcessInvestmentEventSubscriber.Set(v.ProcessInvestmentEventSubscriber, true, path)
	case "processusstockswalletordereventsubscriber":
		return obj._ProcessUsStocksWalletOrderEventSubscriber.Set(v.ProcessUsStocksWalletOrderEventSubscriber, true, path)
	case "processkycupdateeventsubscriber":
		return obj._ProcessKycUpdateEventSubscriber.Set(v.ProcessKycUpdateEventSubscriber, true, path)
	case "processsalaryupdateeventsubscriber":
		return obj._ProcessSalaryUpdateEventSubscriber.Set(v.ProcessSalaryUpdateEventSubscriber, true, path)
	case "processbalanceupdateeventsubscriber":
		return obj._ProcessBalanceUpdateEventSubscriber.Set(v.ProcessBalanceUpdateEventSubscriber, true, path)
	case "processaddfundsordereventsubscriber":
		return obj._ProcessAddFundsOrderEventSubscriber.Set(v.ProcessAddFundsOrderEventSubscriber, true, path)
	case "actortierinfocacheconfig":
		return obj._ActorTierInfoCacheConfig.Set(v.ActorTierInfoCacheConfig, true, path)
	case "eligibletiermovementcacheconfig":
		return obj._EligibleTierMovementCacheConfig.Set(v.EligibleTierMovementCacheConfig, true, path)
	case "tiermovementhistorycacheconfig":
		return obj._TierMovementHistoryCacheConfig.Set(v.TierMovementHistoryCacheConfig, true, path)
	case "segmentids":
		return obj._SegmentIds.Set(v.SegmentIds, true, path)
	case "criteriasegmentexclusions":
		return obj._CriteriaSegmentExclusions.Set(v.CriteriaSegmentExclusions, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "processbalanceupdateeventmarketingsubscriber":
		return obj._ProcessBalanceUpdateEventMarketingSubscriber.Set(v.ProcessBalanceUpdateEventMarketingSubscriber, true, path)
	case "autoupgradeconfig":
		return obj._AutoUpgradeConfig.Set(v.AutoUpgradeConfig, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	case "tieringtrialconfig":
		return obj._TieringTrialConfig.Set(v.TieringTrialConfig, true, path)
	case "tieringbalancebasedpitchparams":
		return obj._TieringBalanceBasedPitchParams.Set(v.TieringBalanceBasedPitchParams, true, path)
	case "abfeaturereleaseconfig":
		return obj._ABFeatureReleaseConfig.Set(v.ABFeatureReleaseConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetCxMovementHistoryLimit(v.CxMovementHistoryLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinAndroidVersion(v.MinAndroidVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinIosVersion(v.MinIosVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinAvgMonthlyBalanceForRegularTier(v.MinAvgMonthlyBalanceForRegularTier, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinBalancePenaltyForRegularTier(v.MinBalancePenaltyForRegularTier, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetToShowUpgradeNotifForSalary(v.ToShowUpgradeNotifForSalary, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetToShowUpgradeNotifForSalaryLite(v.ToShowUpgradeNotifForSalaryLite, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableGoodUserSilentGraceSegmentation(v.EnableGoodUserSilentGraceSegmentation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRewardsAbuserSegmentation(v.EnableRewardsAbuserSegmentation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableTieringAbuserDbCheck(v.EnableTieringAbuserDbCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableHomeTopBarBanner(v.EnableHomeTopBarBanner, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnablePromoWidget(v.EnablePromoWidget, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableTierDowngrades(v.EnableTierDowngrades, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsReactivationPromoWidgetEnabled(v.IsReactivationPromoWidgetEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShouldRoundGraceToNextDay(v.ShouldRoundGraceToNextDay, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisplayPropertyKeyExpiryDuration(v.DisplayPropertyKeyExpiryDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAutoUpgradeCutoff(v.AutoUpgradeCutoff, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGraceWindowDuration(v.GraceWindowDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDowngradeWindowDuration(v.DowngradeWindowDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGraceInitialWindowDuration(v.GraceInitialWindowDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisplayComponentTTLMap(v.DisplayComponentTTLMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetNotificationConfigMap(v.NotificationConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetTierToIneligibleActorSegmentIdMap(v.TierToIneligibleActorSegmentIdMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetSilentGraceTierSegmentsMap(v.SilentGraceTierSegmentsMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetTierEODBalanceServiceConfigMap(v.TierEODBalanceServiceConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetSegmentIdsForReactivationPromoWidget_3txn(v.SegmentIdsForReactivationPromoWidget_3txn, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetSegmentIdsForReactivationPromoWidget_5txn(v.SegmentIdsForReactivationPromoWidget_5txn, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetRegularTierLaunchDate(v.RegularTierLaunchDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._CooloffParams.Set(v.CooloffParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TieringFeatureRelease.Set(v.TieringFeatureRelease, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TierReEvaluationEventSqsSubscriber.Set(v.TierReEvaluationEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessInvestmentEventSubscriber.Set(v.ProcessInvestmentEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessUsStocksWalletOrderEventSubscriber.Set(v.ProcessUsStocksWalletOrderEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessKycUpdateEventSubscriber.Set(v.ProcessKycUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessSalaryUpdateEventSubscriber.Set(v.ProcessSalaryUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessBalanceUpdateEventSubscriber.Set(v.ProcessBalanceUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessAddFundsOrderEventSubscriber.Set(v.ProcessAddFundsOrderEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ActorTierInfoCacheConfig.Set(v.ActorTierInfoCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EligibleTierMovementCacheConfig.Set(v.EligibleTierMovementCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TierMovementHistoryCacheConfig.Set(v.TierMovementHistoryCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SegmentIds.Set(v.SegmentIds, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CriteriaSegmentExclusions.Set(v.CriteriaSegmentExclusions, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessBalanceUpdateEventMarketingSubscriber.Set(v.ProcessBalanceUpdateEventMarketingSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AutoUpgradeConfig.Set(v.AutoUpgradeConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TieringTrialConfig.Set(v.TieringTrialConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TieringBalanceBasedPitchParams.Set(v.TieringBalanceBasedPitchParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ABFeatureReleaseConfig.Set(v.ABFeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._EpifiDb = v.EpifiDb
	obj._TieringDb = v.TieringDb
	obj._AWS = v.AWS
	obj._Flags = v.Flags
	obj._Secrets = v.Secrets
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._GraceParams = v.GraceParams
	obj._DaoCacheRedisOptions = v.DaoCacheRedisOptions
	obj._DisplayPropertiesRedisOptions = v.DisplayPropertiesRedisOptions
	obj._RudderStack = v.RudderStack
	obj._TierUpdateEventExternalPublisher = v.TierUpdateEventExternalPublisher
	obj._QuestRedisOptions = v.QuestRedisOptions
	obj._PinotConfig = v.PinotConfig
	obj._TierMovementHistoriesDbMaxPageSize = v.TierMovementHistoriesDbMaxPageSize
	obj._TieringSherlockBannersConfig = v.TieringSherlockBannersConfig
	return nil
}

func (obj *Config) SetCxMovementHistoryLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.CxMovementHistoryLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CxMovementHistoryLimit, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CxMovementHistoryLimit")
	}
	return nil
}
func (obj *Config) SetMinAndroidVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MinAndroidVersion", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MinAndroidVersion, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAndroidVersion")
	}
	return nil
}
func (obj *Config) SetMinIosVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MinIosVersion", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MinIosVersion, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinIosVersion")
	}
	return nil
}
func (obj *Config) SetMinAvgMonthlyBalanceForRegularTier(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MinAvgMonthlyBalanceForRegularTier", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinAvgMonthlyBalanceForRegularTier, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAvgMonthlyBalanceForRegularTier")
	}
	return nil
}
func (obj *Config) SetMinBalancePenaltyForRegularTier(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MinBalancePenaltyForRegularTier", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinBalancePenaltyForRegularTier, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinBalancePenaltyForRegularTier")
	}
	return nil
}
func (obj *Config) SetToShowUpgradeNotifForSalary(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.ToShowUpgradeNotifForSalary", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ToShowUpgradeNotifForSalary, 1)
	} else {
		atomic.StoreUint32(&obj._ToShowUpgradeNotifForSalary, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ToShowUpgradeNotifForSalary")
	}
	return nil
}
func (obj *Config) SetToShowUpgradeNotifForSalaryLite(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.ToShowUpgradeNotifForSalaryLite", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ToShowUpgradeNotifForSalaryLite, 1)
	} else {
		atomic.StoreUint32(&obj._ToShowUpgradeNotifForSalaryLite, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ToShowUpgradeNotifForSalaryLite")
	}
	return nil
}
func (obj *Config) SetEnableGoodUserSilentGraceSegmentation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableGoodUserSilentGraceSegmentation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableGoodUserSilentGraceSegmentation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableGoodUserSilentGraceSegmentation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableGoodUserSilentGraceSegmentation")
	}
	return nil
}
func (obj *Config) SetEnableRewardsAbuserSegmentation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableRewardsAbuserSegmentation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRewardsAbuserSegmentation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRewardsAbuserSegmentation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRewardsAbuserSegmentation")
	}
	return nil
}
func (obj *Config) SetEnableTieringAbuserDbCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableTieringAbuserDbCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableTieringAbuserDbCheck, 1)
	} else {
		atomic.StoreUint32(&obj._EnableTieringAbuserDbCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableTieringAbuserDbCheck")
	}
	return nil
}
func (obj *Config) SetEnableHomeTopBarBanner(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableHomeTopBarBanner", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableHomeTopBarBanner, 1)
	} else {
		atomic.StoreUint32(&obj._EnableHomeTopBarBanner, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableHomeTopBarBanner")
	}
	return nil
}
func (obj *Config) SetEnablePromoWidget(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnablePromoWidget", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnablePromoWidget, 1)
	} else {
		atomic.StoreUint32(&obj._EnablePromoWidget, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnablePromoWidget")
	}
	return nil
}
func (obj *Config) SetEnableTierDowngrades(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableTierDowngrades", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableTierDowngrades, 1)
	} else {
		atomic.StoreUint32(&obj._EnableTierDowngrades, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableTierDowngrades")
	}
	return nil
}
func (obj *Config) SetIsReactivationPromoWidgetEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.IsReactivationPromoWidgetEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsReactivationPromoWidgetEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsReactivationPromoWidgetEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsReactivationPromoWidgetEnabled")
	}
	return nil
}
func (obj *Config) SetShouldRoundGraceToNextDay(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.ShouldRoundGraceToNextDay", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShouldRoundGraceToNextDay, 1)
	} else {
		atomic.StoreUint32(&obj._ShouldRoundGraceToNextDay, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShouldRoundGraceToNextDay")
	}
	return nil
}
func (obj *Config) SetDisplayPropertyKeyExpiryDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DisplayPropertyKeyExpiryDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DisplayPropertyKeyExpiryDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisplayPropertyKeyExpiryDuration")
	}
	return nil
}
func (obj *Config) SetAutoUpgradeCutoff(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.AutoUpgradeCutoff", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AutoUpgradeCutoff, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AutoUpgradeCutoff")
	}
	return nil
}
func (obj *Config) SetGraceWindowDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.GraceWindowDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._GraceWindowDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "GraceWindowDuration")
	}
	return nil
}
func (obj *Config) SetDowngradeWindowDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DowngradeWindowDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DowngradeWindowDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DowngradeWindowDuration")
	}
	return nil
}
func (obj *Config) SetGraceInitialWindowDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.GraceInitialWindowDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._GraceInitialWindowDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "GraceInitialWindowDuration")
	}
	return nil
}
func (obj *Config) SetDisplayComponentTTLMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DisplayComponentTTLMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._DisplayComponentTTLMap, v, path)
}
func (obj *Config) SetNotificationConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.NotificationParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.NotificationConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._NotificationConfigMap, v, dynamic, path)

}
func (obj *Config) SetTierToIneligibleActorSegmentIdMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.TierToIneligibleActorSegmentIdMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._TierToIneligibleActorSegmentIdMap, v, path)
}
func (obj *Config) SetSilentGraceTierSegmentsMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.SilentGraceTierSegmentsMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._SilentGraceTierSegmentsMap, v, path)
}
func (obj *Config) SetTierEODBalanceServiceConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.EODBalanceServiceConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.TierEODBalanceServiceConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._TierEODBalanceServiceConfigMap, v, dynamic, path)

}
func (obj *Config) SetSegmentIdsForReactivationPromoWidget_3txn(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.SegmentTxnDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.SegmentIdsForReactivationPromoWidget_3txn", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._SegmentIdsForReactivationPromoWidget_3txn, v, dynamic, path)

}
func (obj *Config) SetSegmentIdsForReactivationPromoWidget_5txn(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.SegmentTxnDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.SegmentIdsForReactivationPromoWidget_5txn", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._SegmentIdsForReactivationPromoWidget_5txn, v, dynamic, path)

}
func (obj *Config) SetRegularTierLaunchDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.RegularTierLaunchDate", reflect.TypeOf(val))
	}
	obj._RegularTierLaunchDateMutex.Lock()
	defer obj._RegularTierLaunchDateMutex.Unlock()
	obj._RegularTierLaunchDate = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "RegularTierLaunchDate")
	}
	return nil
}

func NewCooloffParams() (_obj *CooloffParams, _setters map[string]dynconf.SetFunc) {
	_obj = &CooloffParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxmovementsallowed"] = _obj.SetMaxMovementsAllowed
	_setters["evaluationwindowduration"] = _obj.SetEvaluationWindowDuration
	return _obj, _setters
}

func (obj *CooloffParams) Init() {
	newObj, _ := NewCooloffParams()
	*obj = *newObj
}

func (obj *CooloffParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CooloffParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CooloffParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *CooloffParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CooloffParams) setDynamicField(v *config.CooloffParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxmovementsallowed":
		return obj.SetMaxMovementsAllowed(v.MaxMovementsAllowed, true, nil)
	case "evaluationwindowduration":
		return obj.SetEvaluationWindowDuration(v.EvaluationWindowDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CooloffParams) setDynamicFields(v *config.CooloffParams, dynamic bool, path []string) (err error) {

	err = obj.SetMaxMovementsAllowed(v.MaxMovementsAllowed, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEvaluationWindowDuration(v.EvaluationWindowDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CooloffParams) setStaticFields(v *config.CooloffParams) error {

	return nil
}

func (obj *CooloffParams) SetMaxMovementsAllowed(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *CooloffParams.MaxMovementsAllowed", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxMovementsAllowed, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxMovementsAllowed")
	}
	return nil
}
func (obj *CooloffParams) SetEvaluationWindowDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CooloffParams.EvaluationWindowDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._EvaluationWindowDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "EvaluationWindowDuration")
	}
	return nil
}

func NewInternalReleaseConfig() (_obj *InternalReleaseConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &InternalReleaseConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["rolloutpercentage"] = _obj.SetRolloutPercentage
	_setters["isfeatureenabled"] = _obj.SetIsFeatureEnabled
	return _obj, _setters
}

func (obj *InternalReleaseConfig) Init() {
	newObj, _ := NewInternalReleaseConfig()
	*obj = *newObj
}

func (obj *InternalReleaseConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InternalReleaseConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InternalReleaseConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternalReleaseConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InternalReleaseConfig) setDynamicField(v *config.InternalReleaseConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "rolloutpercentage":
		return obj.SetRolloutPercentage(v.RolloutPercentage, true, nil)
	case "isfeatureenabled":
		return obj.SetIsFeatureEnabled(v.IsFeatureEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InternalReleaseConfig) setDynamicFields(v *config.InternalReleaseConfig, dynamic bool, path []string) (err error) {

	err = obj.SetRolloutPercentage(v.RolloutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsFeatureEnabled(v.IsFeatureEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InternalReleaseConfig) setStaticFields(v *config.InternalReleaseConfig) error {

	obj._AllowedGroups = v.AllowedGroups
	return nil
}

func (obj *InternalReleaseConfig) SetRolloutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternalReleaseConfig.RolloutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._RolloutPercentage, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RolloutPercentage")
	}
	return nil
}
func (obj *InternalReleaseConfig) SetIsFeatureEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternalReleaseConfig.IsFeatureEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsFeatureEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsFeatureEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsFeatureEnabled")
	}
	return nil
}

func NewNotificationParams() (_obj *NotificationParams, _setters map[string]dynconf.SetFunc) {
	_obj = &NotificationParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["autodismissonhomeafterinseconds"] = _obj.SetAutoDismissOnHomeAfterInSeconds
	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["expireafter"] = _obj.SetExpireAfter
	return _obj, _setters
}

func (obj *NotificationParams) Init() {
	newObj, _ := NewNotificationParams()
	*obj = *newObj
}

func (obj *NotificationParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NotificationParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NotificationParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *NotificationParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NotificationParams) setDynamicField(v *config.NotificationParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "autodismissonhomeafterinseconds":
		return obj.SetAutoDismissOnHomeAfterInSeconds(v.AutoDismissOnHomeAfterInSeconds, true, nil)
	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "expireafter":
		return obj.SetExpireAfter(v.ExpireAfter, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NotificationParams) setDynamicFields(v *config.NotificationParams, dynamic bool, path []string) (err error) {

	err = obj.SetAutoDismissOnHomeAfterInSeconds(v.AutoDismissOnHomeAfterInSeconds, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExpireAfter(v.ExpireAfter, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NotificationParams) setStaticFields(v *config.NotificationParams) error {

	return nil
}

func (obj *NotificationParams) SetAutoDismissOnHomeAfterInSeconds(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *NotificationParams.AutoDismissOnHomeAfterInSeconds", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._AutoDismissOnHomeAfterInSeconds, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AutoDismissOnHomeAfterInSeconds")
	}
	return nil
}
func (obj *NotificationParams) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *NotificationParams.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *NotificationParams) SetExpireAfter(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *NotificationParams.ExpireAfter", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ExpireAfter, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExpireAfter")
	}
	return nil
}

func NewCacheConfig() (_obj *CacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["cachettl"] = _obj.SetCacheTTl
	return _obj, _setters
}

func (obj *CacheConfig) Init() {
	newObj, _ := NewCacheConfig()
	*obj = *newObj
}

func (obj *CacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CacheConfig) setDynamicField(v *config.CacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTl(v.CacheTTl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CacheConfig) setDynamicFields(v *config.CacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTl(v.CacheTTl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CacheConfig) setStaticFields(v *config.CacheConfig) error {

	obj._Prefix = v.Prefix
	return nil
}

func (obj *CacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *CacheConfig) SetCacheTTl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig.CacheTTl", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTl, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTl")
	}
	return nil
}

func NewSegmentIds() (_obj *SegmentIds, _setters map[string]dynconf.SetFunc) {
	_obj = &SegmentIds{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["gooduser"] = _obj.SetGoodUser
	_obj._GoodUserMutex = &sync.RWMutex{}
	_setters["rewardsabuser"] = _obj.SetRewardsAbuser
	_obj._RewardsAbuserMutex = &sync.RWMutex{}
	_setters["highertierscashbackrewardineligibleuser"] = _obj.SetHigherTiersCashbackRewardInEligibleUser
	_obj._HigherTiersCashbackRewardInEligibleUserMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *SegmentIds) Init() {
	newObj, _ := NewSegmentIds()
	*obj = *newObj
}

func (obj *SegmentIds) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SegmentIds) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SegmentIds)
	if !ok {
		return fmt.Errorf("invalid data type %v *SegmentIds", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SegmentIds) setDynamicField(v *config.SegmentIds, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "gooduser":
		return obj.SetGoodUser(v.GoodUser, true, nil)
	case "rewardsabuser":
		return obj.SetRewardsAbuser(v.RewardsAbuser, true, nil)
	case "highertierscashbackrewardineligibleuser":
		return obj.SetHigherTiersCashbackRewardInEligibleUser(v.HigherTiersCashbackRewardInEligibleUser, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SegmentIds) setDynamicFields(v *config.SegmentIds, dynamic bool, path []string) (err error) {

	err = obj.SetGoodUser(v.GoodUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRewardsAbuser(v.RewardsAbuser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHigherTiersCashbackRewardInEligibleUser(v.HigherTiersCashbackRewardInEligibleUser, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SegmentIds) setStaticFields(v *config.SegmentIds) error {

	return nil
}

func (obj *SegmentIds) SetGoodUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SegmentIds.GoodUser", reflect.TypeOf(val))
	}
	obj._GoodUserMutex.Lock()
	defer obj._GoodUserMutex.Unlock()
	obj._GoodUser = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GoodUser")
	}
	return nil
}
func (obj *SegmentIds) SetRewardsAbuser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SegmentIds.RewardsAbuser", reflect.TypeOf(val))
	}
	obj._RewardsAbuserMutex.Lock()
	defer obj._RewardsAbuserMutex.Unlock()
	obj._RewardsAbuser = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "RewardsAbuser")
	}
	return nil
}
func (obj *SegmentIds) SetHigherTiersCashbackRewardInEligibleUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SegmentIds.HigherTiersCashbackRewardInEligibleUser", reflect.TypeOf(val))
	}
	obj._HigherTiersCashbackRewardInEligibleUserMutex.Lock()
	defer obj._HigherTiersCashbackRewardInEligibleUserMutex.Unlock()
	obj._HigherTiersCashbackRewardInEligibleUser = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "HigherTiersCashbackRewardInEligibleUser")
	}
	return nil
}

func NewCriteriaSegmentExclusions() (_obj *CriteriaSegmentExclusions, _setters map[string]dynconf.SetFunc) {
	_obj = &CriteriaSegmentExclusions{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["salaryb2cexcludedsegments"] = _obj.SetSalaryB2CExcludedSegments
	_obj._SalaryB2CExcludedSegmentsMutex = &sync.RWMutex{}
	_setters["aasalaryexcludedsegments"] = _obj.SetAaSalaryExcludedSegments
	_obj._AaSalaryExcludedSegmentsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *CriteriaSegmentExclusions) Init() {
	newObj, _ := NewCriteriaSegmentExclusions()
	*obj = *newObj
}

func (obj *CriteriaSegmentExclusions) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CriteriaSegmentExclusions) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CriteriaSegmentExclusions)
	if !ok {
		return fmt.Errorf("invalid data type %v *CriteriaSegmentExclusions", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CriteriaSegmentExclusions) setDynamicField(v *config.CriteriaSegmentExclusions, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "salaryb2cexcludedsegments":
		return obj.SetSalaryB2CExcludedSegments(v.SalaryB2CExcludedSegments, true, path)
	case "aasalaryexcludedsegments":
		return obj.SetAaSalaryExcludedSegments(v.AaSalaryExcludedSegments, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CriteriaSegmentExclusions) setDynamicFields(v *config.CriteriaSegmentExclusions, dynamic bool, path []string) (err error) {

	err = obj.SetSalaryB2CExcludedSegments(v.SalaryB2CExcludedSegments, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetAaSalaryExcludedSegments(v.AaSalaryExcludedSegments, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CriteriaSegmentExclusions) setStaticFields(v *config.CriteriaSegmentExclusions) error {

	return nil
}

func (obj *CriteriaSegmentExclusions) SetSalaryB2CExcludedSegments(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CriteriaSegmentExclusions.SalaryB2CExcludedSegments", reflect.TypeOf(val))
	}
	obj._SalaryB2CExcludedSegmentsMutex.Lock()
	defer obj._SalaryB2CExcludedSegmentsMutex.Unlock()
	obj._SalaryB2CExcludedSegments = roarray.New[string](v)
	return nil
}
func (obj *CriteriaSegmentExclusions) SetAaSalaryExcludedSegments(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CriteriaSegmentExclusions.AaSalaryExcludedSegments", reflect.TypeOf(val))
	}
	obj._AaSalaryExcludedSegmentsMutex.Lock()
	defer obj._AaSalaryExcludedSegmentsMutex.Unlock()
	obj._AaSalaryExcludedSegments = roarray.New[string](v)
	return nil
}

func NewEODBalanceServiceConfig() (_obj *EODBalanceServiceConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EODBalanceServiceConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minaggregationdays"] = _obj.SetMinAggregationDays
	_setters["averagebalancethreshold"] = _obj.SetAverageBalanceThreshold
	_setters["enable"] = _obj.SetEnable
	_setters["eodbalanceaggregateduration"] = _obj.SetEODBalanceAggregateDuration
	return _obj, _setters
}

func (obj *EODBalanceServiceConfig) Init() {
	newObj, _ := NewEODBalanceServiceConfig()
	*obj = *newObj
}

func (obj *EODBalanceServiceConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EODBalanceServiceConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EODBalanceServiceConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EODBalanceServiceConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EODBalanceServiceConfig) setDynamicField(v *config.EODBalanceServiceConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minaggregationdays":
		return obj.SetMinAggregationDays(v.MinAggregationDays, true, nil)
	case "averagebalancethreshold":
		return obj.SetAverageBalanceThreshold(v.AverageBalanceThreshold, true, nil)
	case "enable":
		return obj.SetEnable(v.Enable, true, nil)
	case "eodbalanceaggregateduration":
		return obj.SetEODBalanceAggregateDuration(v.EODBalanceAggregateDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EODBalanceServiceConfig) setDynamicFields(v *config.EODBalanceServiceConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMinAggregationDays(v.MinAggregationDays, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAverageBalanceThreshold(v.AverageBalanceThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnable(v.Enable, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEODBalanceAggregateDuration(v.EODBalanceAggregateDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EODBalanceServiceConfig) setStaticFields(v *config.EODBalanceServiceConfig) error {

	return nil
}

func (obj *EODBalanceServiceConfig) SetMinAggregationDays(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *EODBalanceServiceConfig.MinAggregationDays", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MinAggregationDays, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAggregationDays")
	}
	return nil
}
func (obj *EODBalanceServiceConfig) SetAverageBalanceThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *EODBalanceServiceConfig.AverageBalanceThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._AverageBalanceThreshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AverageBalanceThreshold")
	}
	return nil
}
func (obj *EODBalanceServiceConfig) SetEnable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *EODBalanceServiceConfig.Enable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Enable, 1)
	} else {
		atomic.StoreUint32(&obj._Enable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Enable")
	}
	return nil
}
func (obj *EODBalanceServiceConfig) SetEODBalanceAggregateDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *EODBalanceServiceConfig.EODBalanceAggregateDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._EODBalanceAggregateDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "EODBalanceAggregateDuration")
	}
	return nil
}

func NewAutoUpgradeConfig() (_obj *AutoUpgradeConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AutoUpgradeConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disableprovenancecheck"] = _obj.SetDisableProvenanceCheck
	_setters["disablesalarytiercheck"] = _obj.SetDisableSalaryTierCheck
	_setters["disablefirsttiermovementcheck"] = _obj.SetDisableFirstTierMovementCheck
	_setters["disableautoupgradecutoffcheck"] = _obj.SetDisableAutoUpgradeCutoffCheck
	_setters["disablehighertiercheck"] = _obj.SetDisableHigherTierCheck
	_setters["disableuserspecificchecks"] = _obj.SetDisableUserSpecificChecks
	_setters["disablebeenintieringcheck"] = _obj.SetDisableBeenInTieringCheck
	_setters["disableaveragebalancecheck"] = _obj.SetDisableAverageBalanceCheck
	_setters["avgbalancedurationbuffer"] = _obj.SetAvgBalanceDurationBuffer

	_obj._AutoUpgradeAvgBalanceConditionMap = &syncmap.Map[string, *AverageBalanceConditions]{}
	_setters["autoupgradeavgbalanceconditionmap"] = _obj.SetAutoUpgradeAvgBalanceConditionMap
	_DisableToTierConditionMap, _fieldSetters := NewTierConditionMap()
	_obj._DisableToTierConditionMap = _DisableToTierConditionMap
	helper.AddFieldSetters("disabletotierconditionmap", _fieldSetters, _setters)
	return _obj, _setters
}

func NewAutoUpgradeConfigWithQuest(questFieldPath string) (_obj *AutoUpgradeConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AutoUpgradeConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disableprovenancecheck"] = _obj.SetDisableProvenanceCheck
	_setters["disablesalarytiercheck"] = _obj.SetDisableSalaryTierCheck
	_setters["disablefirsttiermovementcheck"] = _obj.SetDisableFirstTierMovementCheck
	_setters["disableautoupgradecutoffcheck"] = _obj.SetDisableAutoUpgradeCutoffCheck
	_setters["disablehighertiercheck"] = _obj.SetDisableHigherTierCheck
	_setters["disableuserspecificchecks"] = _obj.SetDisableUserSpecificChecks
	_setters["disablebeenintieringcheck"] = _obj.SetDisableBeenInTieringCheck
	_setters["disableaveragebalancecheck"] = _obj.SetDisableAverageBalanceCheck
	_setters["avgbalancedurationbuffer"] = _obj.SetAvgBalanceDurationBuffer

	_obj._AutoUpgradeAvgBalanceConditionMap = &syncmap.Map[string, *AverageBalanceConditions]{}
	_setters["autoupgradeavgbalanceconditionmap"] = _obj.SetAutoUpgradeAvgBalanceConditionMap
	_DisableToTierConditionMap, _fieldSetters := NewTierConditionMapWithQuest(questFieldPath + "/" + "DisableToTierConditionMap")
	_obj._DisableToTierConditionMap = _DisableToTierConditionMap
	helper.AddFieldSetters("disabletotierconditionmap", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *AutoUpgradeConfig) Init(questFieldPath string) {
	newObj, _ := NewAutoUpgradeConfig()
	*obj = *newObj
}
func (obj *AutoUpgradeConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewAutoUpgradeConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *AutoUpgradeConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._DisableToTierConditionMap.SetQuestSDK(questSdk)
}

func (obj *AutoUpgradeConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AutoUpgradeConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DisableBeenInTieringCheck",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._DisableToTierConditionMap.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *AutoUpgradeConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AutoUpgradeConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AutoUpgradeConfig) setDynamicField(v *config.AutoUpgradeConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "disableprovenancecheck":
		return obj.SetDisableProvenanceCheck(v.DisableProvenanceCheck, true, nil)
	case "disablesalarytiercheck":
		return obj.SetDisableSalaryTierCheck(v.DisableSalaryTierCheck, true, nil)
	case "disablefirsttiermovementcheck":
		return obj.SetDisableFirstTierMovementCheck(v.DisableFirstTierMovementCheck, true, nil)
	case "disableautoupgradecutoffcheck":
		return obj.SetDisableAutoUpgradeCutoffCheck(v.DisableAutoUpgradeCutoffCheck, true, nil)
	case "disablehighertiercheck":
		return obj.SetDisableHigherTierCheck(v.DisableHigherTierCheck, true, nil)
	case "disableuserspecificchecks":
		return obj.SetDisableUserSpecificChecks(v.DisableUserSpecificChecks, true, nil)
	case "disablebeenintieringcheck":
		return obj.SetDisableBeenInTieringCheck(v.DisableBeenInTieringCheck, true, nil)
	case "disableaveragebalancecheck":
		return obj.SetDisableAverageBalanceCheck(v.DisableAverageBalanceCheck, true, nil)
	case "avgbalancedurationbuffer":
		return obj.SetAvgBalanceDurationBuffer(v.AvgBalanceDurationBuffer, true, nil)
	case "autoupgradeavgbalanceconditionmap":
		return obj.SetAutoUpgradeAvgBalanceConditionMap(v.AutoUpgradeAvgBalanceConditionMap, true, path)
	case "disabletotierconditionmap":
		return obj._DisableToTierConditionMap.Set(v.DisableToTierConditionMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AutoUpgradeConfig) setDynamicFields(v *config.AutoUpgradeConfig, dynamic bool, path []string) (err error) {

	err = obj.SetDisableProvenanceCheck(v.DisableProvenanceCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableSalaryTierCheck(v.DisableSalaryTierCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableFirstTierMovementCheck(v.DisableFirstTierMovementCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableAutoUpgradeCutoffCheck(v.DisableAutoUpgradeCutoffCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableHigherTierCheck(v.DisableHigherTierCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableUserSpecificChecks(v.DisableUserSpecificChecks, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableBeenInTieringCheck(v.DisableBeenInTieringCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableAverageBalanceCheck(v.DisableAverageBalanceCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAvgBalanceDurationBuffer(v.AvgBalanceDurationBuffer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAutoUpgradeAvgBalanceConditionMap(v.AutoUpgradeAvgBalanceConditionMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DisableToTierConditionMap.Set(v.DisableToTierConditionMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AutoUpgradeConfig) setStaticFields(v *config.AutoUpgradeConfig) error {

	return nil
}

func (obj *AutoUpgradeConfig) SetDisableProvenanceCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.DisableProvenanceCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableProvenanceCheck, 1)
	} else {
		atomic.StoreUint32(&obj._DisableProvenanceCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableProvenanceCheck")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetDisableSalaryTierCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.DisableSalaryTierCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableSalaryTierCheck, 1)
	} else {
		atomic.StoreUint32(&obj._DisableSalaryTierCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableSalaryTierCheck")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetDisableFirstTierMovementCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.DisableFirstTierMovementCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableFirstTierMovementCheck, 1)
	} else {
		atomic.StoreUint32(&obj._DisableFirstTierMovementCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableFirstTierMovementCheck")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetDisableAutoUpgradeCutoffCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.DisableAutoUpgradeCutoffCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableAutoUpgradeCutoffCheck, 1)
	} else {
		atomic.StoreUint32(&obj._DisableAutoUpgradeCutoffCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableAutoUpgradeCutoffCheck")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetDisableHigherTierCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.DisableHigherTierCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableHigherTierCheck, 1)
	} else {
		atomic.StoreUint32(&obj._DisableHigherTierCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableHigherTierCheck")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetDisableUserSpecificChecks(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.DisableUserSpecificChecks", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableUserSpecificChecks, 1)
	} else {
		atomic.StoreUint32(&obj._DisableUserSpecificChecks, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableUserSpecificChecks")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetDisableBeenInTieringCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.DisableBeenInTieringCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableBeenInTieringCheck, 1)
	} else {
		atomic.StoreUint32(&obj._DisableBeenInTieringCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableBeenInTieringCheck")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetDisableAverageBalanceCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.DisableAverageBalanceCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableAverageBalanceCheck, 1)
	} else {
		atomic.StoreUint32(&obj._DisableAverageBalanceCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableAverageBalanceCheck")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetAvgBalanceDurationBuffer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.AvgBalanceDurationBuffer", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AvgBalanceDurationBuffer, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AvgBalanceDurationBuffer")
	}
	return nil
}
func (obj *AutoUpgradeConfig) SetAutoUpgradeAvgBalanceConditionMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.AverageBalanceConditions)
	if !ok {
		return fmt.Errorf("invalid data type %v *AutoUpgradeConfig.AutoUpgradeAvgBalanceConditionMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._AutoUpgradeAvgBalanceConditionMap, v, dynamic, path)

}

func NewAverageBalanceConditions() (_obj *AverageBalanceConditions, _setters map[string]dynconf.SetFunc) {
	_obj = &AverageBalanceConditions{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._AverageBalanceConditionMap = &syncmap.Map[string, int64]{}
	_setters["averagebalanceconditionmap"] = _obj.SetAverageBalanceConditionMap
	return _obj, _setters
}

func (obj *AverageBalanceConditions) Init() {
	newObj, _ := NewAverageBalanceConditions()
	*obj = *newObj
}

func (obj *AverageBalanceConditions) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AverageBalanceConditions) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AverageBalanceConditions)
	if !ok {
		return fmt.Errorf("invalid data type %v *AverageBalanceConditions", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AverageBalanceConditions) setDynamicField(v *config.AverageBalanceConditions, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "averagebalanceconditionmap":
		return obj.SetAverageBalanceConditionMap(v.AverageBalanceConditionMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AverageBalanceConditions) setDynamicFields(v *config.AverageBalanceConditions, dynamic bool, path []string) (err error) {

	err = obj.SetAverageBalanceConditionMap(v.AverageBalanceConditionMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AverageBalanceConditions) setStaticFields(v *config.AverageBalanceConditions) error {

	return nil
}

func (obj *AverageBalanceConditions) SetAverageBalanceConditionMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *AverageBalanceConditions.AverageBalanceConditionMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AverageBalanceConditionMap, v, path)
}

func NewTierConditionMap() (_obj *TierConditionMap, _setters map[string]dynconf.SetFunc) {
	_obj = &TierConditionMap{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["regular"] = _obj.SetRegular
	_setters["standard"] = _obj.SetStandard
	_setters["plus"] = _obj.SetPlus
	_setters["infinite"] = _obj.SetInfinite
	_setters["aasalary"] = _obj.SetAaSalary
	_setters["salarylite"] = _obj.SetSalaryLite
	_setters["salary"] = _obj.SetSalary
	_setters["salarybasic"] = _obj.SetSalaryBasic
	return _obj, _setters
}

func NewTierConditionMapWithQuest(questFieldPath string) (_obj *TierConditionMap, _setters map[string]dynconf.SetFunc) {
	_obj = &TierConditionMap{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["regular"] = _obj.SetRegular
	_setters["standard"] = _obj.SetStandard
	_setters["plus"] = _obj.SetPlus
	_setters["infinite"] = _obj.SetInfinite
	_setters["aasalary"] = _obj.SetAaSalary
	_setters["salarylite"] = _obj.SetSalaryLite
	_setters["salary"] = _obj.SetSalary
	_setters["salarybasic"] = _obj.SetSalaryBasic
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *TierConditionMap) Init(questFieldPath string) {
	newObj, _ := NewTierConditionMap()
	*obj = *newObj
}
func (obj *TierConditionMap) InitWithQuest(questFieldPath string) {
	newObj, _ := NewTierConditionMapWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *TierConditionMap) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *TierConditionMap) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TierConditionMap) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Regular",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Standard",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Plus",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Infinite",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "AaSalary",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "SalaryLite",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Salary",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "SalaryBasic",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *TierConditionMap) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TierConditionMap)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TierConditionMap) setDynamicField(v *config.TierConditionMap, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "regular":
		return obj.SetRegular(v.Regular, true, nil)
	case "standard":
		return obj.SetStandard(v.Standard, true, nil)
	case "plus":
		return obj.SetPlus(v.Plus, true, nil)
	case "infinite":
		return obj.SetInfinite(v.Infinite, true, nil)
	case "aasalary":
		return obj.SetAaSalary(v.AaSalary, true, nil)
	case "salarylite":
		return obj.SetSalaryLite(v.SalaryLite, true, nil)
	case "salary":
		return obj.SetSalary(v.Salary, true, nil)
	case "salarybasic":
		return obj.SetSalaryBasic(v.SalaryBasic, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TierConditionMap) setDynamicFields(v *config.TierConditionMap, dynamic bool, path []string) (err error) {

	err = obj.SetRegular(v.Regular, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetStandard(v.Standard, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPlus(v.Plus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInfinite(v.Infinite, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAaSalary(v.AaSalary, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSalaryLite(v.SalaryLite, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSalary(v.Salary, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSalaryBasic(v.SalaryBasic, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TierConditionMap) setStaticFields(v *config.TierConditionMap) error {

	return nil
}

func (obj *TierConditionMap) SetRegular(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap.Regular", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Regular, 1)
	} else {
		atomic.StoreUint32(&obj._Regular, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Regular")
	}
	return nil
}
func (obj *TierConditionMap) SetStandard(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap.Standard", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Standard, 1)
	} else {
		atomic.StoreUint32(&obj._Standard, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Standard")
	}
	return nil
}
func (obj *TierConditionMap) SetPlus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap.Plus", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Plus, 1)
	} else {
		atomic.StoreUint32(&obj._Plus, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Plus")
	}
	return nil
}
func (obj *TierConditionMap) SetInfinite(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap.Infinite", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Infinite, 1)
	} else {
		atomic.StoreUint32(&obj._Infinite, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Infinite")
	}
	return nil
}
func (obj *TierConditionMap) SetAaSalary(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap.AaSalary", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._AaSalary, 1)
	} else {
		atomic.StoreUint32(&obj._AaSalary, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "AaSalary")
	}
	return nil
}
func (obj *TierConditionMap) SetSalaryLite(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap.SalaryLite", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SalaryLite, 1)
	} else {
		atomic.StoreUint32(&obj._SalaryLite, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SalaryLite")
	}
	return nil
}
func (obj *TierConditionMap) SetSalary(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap.Salary", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Salary, 1)
	} else {
		atomic.StoreUint32(&obj._Salary, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Salary")
	}
	return nil
}
func (obj *TierConditionMap) SetSalaryBasic(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConditionMap.SalaryBasic", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SalaryBasic, 1)
	} else {
		atomic.StoreUint32(&obj._SalaryBasic, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SalaryBasic")
	}
	return nil
}

func NewSegmentTxnDetails() (_obj *SegmentTxnDetails, _setters map[string]dynconf.SetFunc) {
	_obj = &SegmentTxnDetails{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["startdate"] = _obj.SetStartDate
	_setters["enddate"] = _obj.SetEndDate
	_setters["rewardamount"] = _obj.SetRewardAmount
	return _obj, _setters
}

func (obj *SegmentTxnDetails) Init() {
	newObj, _ := NewSegmentTxnDetails()
	*obj = *newObj
}

func (obj *SegmentTxnDetails) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SegmentTxnDetails) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SegmentTxnDetails)
	if !ok {
		return fmt.Errorf("invalid data type %v *SegmentTxnDetails", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SegmentTxnDetails) setDynamicField(v *config.SegmentTxnDetails, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "startdate":
		return obj.SetStartDate(v.StartDate, true, nil)
	case "enddate":
		return obj.SetEndDate(v.EndDate, true, nil)
	case "rewardamount":
		return obj.SetRewardAmount(v.RewardAmount, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SegmentTxnDetails) setDynamicFields(v *config.SegmentTxnDetails, dynamic bool, path []string) (err error) {

	err = obj.SetStartDate(v.StartDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEndDate(v.EndDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRewardAmount(v.RewardAmount, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SegmentTxnDetails) setStaticFields(v *config.SegmentTxnDetails) error {

	return nil
}

func (obj *SegmentTxnDetails) SetStartDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *SegmentTxnDetails.StartDate", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._StartDate, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "StartDate")
	}
	return nil
}
func (obj *SegmentTxnDetails) SetEndDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *SegmentTxnDetails.EndDate", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._EndDate, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "EndDate")
	}
	return nil
}
func (obj *SegmentTxnDetails) SetRewardAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *SegmentTxnDetails.RewardAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RewardAmount, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RewardAmount")
	}
	return nil
}

func NewTieringTrialConfig() (_obj *TieringTrialConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &TieringTrialConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minimumtrialduration"] = _obj.SetMinimumTrialDuration
	_setters["plustrialsegments"] = _obj.SetPlusTrialSegments
	_obj._PlusTrialSegmentsMutex = &sync.RWMutex{}
	_setters["infinitetrialsegments"] = _obj.SetInfiniteTrialSegments
	_obj._InfiniteTrialSegmentsMutex = &sync.RWMutex{}
	_setters["primetrialsegments"] = _obj.SetPrimeTrialSegments
	_obj._PrimeTrialSegmentsMutex = &sync.RWMutex{}
	_setters["trialentrypointstartdate"] = _obj.SetTrialEntryPointStartDate
	_obj._TrialEntryPointStartDateMutex = &sync.RWMutex{}
	_setters["trialentrypointenddate"] = _obj.SetTrialEntryPointEndDate
	_obj._TrialEntryPointEndDateMutex = &sync.RWMutex{}
	_setters["trialenddate"] = _obj.SetTrialEndDate
	_obj._TrialEndDateMutex = &sync.RWMutex{}
	_setters["trialenddatewithoutgrace"] = _obj.SetTrialEndDateWithoutGrace
	_obj._TrialEndDateWithoutGraceMutex = &sync.RWMutex{}
	_setters["trialintroweburl"] = _obj.SetTrialIntroWebUrl
	_obj._TrialIntroWebUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *TieringTrialConfig) Init() {
	newObj, _ := NewTieringTrialConfig()
	*obj = *newObj
}

func (obj *TieringTrialConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TieringTrialConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TieringTrialConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TieringTrialConfig) setDynamicField(v *config.TieringTrialConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minimumtrialduration":
		return obj.SetMinimumTrialDuration(v.MinimumTrialDuration, true, nil)
	case "plustrialsegments":
		return obj.SetPlusTrialSegments(v.PlusTrialSegments, true, path)
	case "infinitetrialsegments":
		return obj.SetInfiniteTrialSegments(v.InfiniteTrialSegments, true, path)
	case "primetrialsegments":
		return obj.SetPrimeTrialSegments(v.PrimeTrialSegments, true, path)
	case "trialentrypointstartdate":
		return obj.SetTrialEntryPointStartDate(v.TrialEntryPointStartDate, true, nil)
	case "trialentrypointenddate":
		return obj.SetTrialEntryPointEndDate(v.TrialEntryPointEndDate, true, nil)
	case "trialenddate":
		return obj.SetTrialEndDate(v.TrialEndDate, true, nil)
	case "trialenddatewithoutgrace":
		return obj.SetTrialEndDateWithoutGrace(v.TrialEndDateWithoutGrace, true, nil)
	case "trialintroweburl":
		return obj.SetTrialIntroWebUrl(v.TrialIntroWebUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TieringTrialConfig) setDynamicFields(v *config.TieringTrialConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMinimumTrialDuration(v.MinimumTrialDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPlusTrialSegments(v.PlusTrialSegments, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetInfiniteTrialSegments(v.InfiniteTrialSegments, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetPrimeTrialSegments(v.PrimeTrialSegments, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetTrialEntryPointStartDate(v.TrialEntryPointStartDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTrialEntryPointEndDate(v.TrialEntryPointEndDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTrialEndDate(v.TrialEndDate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTrialEndDateWithoutGrace(v.TrialEndDateWithoutGrace, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTrialIntroWebUrl(v.TrialIntroWebUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TieringTrialConfig) setStaticFields(v *config.TieringTrialConfig) error {

	return nil
}

func (obj *TieringTrialConfig) SetMinimumTrialDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.MinimumTrialDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinimumTrialDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinimumTrialDuration")
	}
	return nil
}
func (obj *TieringTrialConfig) SetPlusTrialSegments(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.PlusTrialSegments", reflect.TypeOf(val))
	}
	obj._PlusTrialSegmentsMutex.Lock()
	defer obj._PlusTrialSegmentsMutex.Unlock()
	obj._PlusTrialSegments = roarray.New[string](v)
	return nil
}
func (obj *TieringTrialConfig) SetInfiniteTrialSegments(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.InfiniteTrialSegments", reflect.TypeOf(val))
	}
	obj._InfiniteTrialSegmentsMutex.Lock()
	defer obj._InfiniteTrialSegmentsMutex.Unlock()
	obj._InfiniteTrialSegments = roarray.New[string](v)
	return nil
}
func (obj *TieringTrialConfig) SetPrimeTrialSegments(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.PrimeTrialSegments", reflect.TypeOf(val))
	}
	obj._PrimeTrialSegmentsMutex.Lock()
	defer obj._PrimeTrialSegmentsMutex.Unlock()
	obj._PrimeTrialSegments = roarray.New[string](v)
	return nil
}
func (obj *TieringTrialConfig) SetTrialEntryPointStartDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.TrialEntryPointStartDate", reflect.TypeOf(val))
	}
	obj._TrialEntryPointStartDateMutex.Lock()
	defer obj._TrialEntryPointStartDateMutex.Unlock()
	obj._TrialEntryPointStartDate = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrialEntryPointStartDate")
	}
	return nil
}
func (obj *TieringTrialConfig) SetTrialEntryPointEndDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.TrialEntryPointEndDate", reflect.TypeOf(val))
	}
	obj._TrialEntryPointEndDateMutex.Lock()
	defer obj._TrialEntryPointEndDateMutex.Unlock()
	obj._TrialEntryPointEndDate = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrialEntryPointEndDate")
	}
	return nil
}
func (obj *TieringTrialConfig) SetTrialEndDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.TrialEndDate", reflect.TypeOf(val))
	}
	obj._TrialEndDateMutex.Lock()
	defer obj._TrialEndDateMutex.Unlock()
	obj._TrialEndDate = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrialEndDate")
	}
	return nil
}
func (obj *TieringTrialConfig) SetTrialEndDateWithoutGrace(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.TrialEndDateWithoutGrace", reflect.TypeOf(val))
	}
	obj._TrialEndDateWithoutGraceMutex.Lock()
	defer obj._TrialEndDateWithoutGraceMutex.Unlock()
	obj._TrialEndDateWithoutGrace = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrialEndDateWithoutGrace")
	}
	return nil
}
func (obj *TieringTrialConfig) SetTrialIntroWebUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringTrialConfig.TrialIntroWebUrl", reflect.TypeOf(val))
	}
	obj._TrialIntroWebUrlMutex.Lock()
	defer obj._TrialIntroWebUrlMutex.Unlock()
	obj._TrialIntroWebUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrialIntroWebUrl")
	}
	return nil
}

func NewTieringBalanceBasedPitchParams() (_obj *TieringBalanceBasedPitchParams, _setters map[string]dynconf.SetFunc) {
	_obj = &TieringBalanceBasedPitchParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["balanceforpitchinginfiniteforbasic"] = _obj.SetBalanceForPitchingInfiniteForBasic
	_setters["balanceforpitchingprimeforbasic"] = _obj.SetBalanceForPitchingPrimeForBasic
	return _obj, _setters
}

func (obj *TieringBalanceBasedPitchParams) Init() {
	newObj, _ := NewTieringBalanceBasedPitchParams()
	*obj = *newObj
}

func (obj *TieringBalanceBasedPitchParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TieringBalanceBasedPitchParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TieringBalanceBasedPitchParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringBalanceBasedPitchParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TieringBalanceBasedPitchParams) setDynamicField(v *config.TieringBalanceBasedPitchParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "balanceforpitchinginfiniteforbasic":
		return obj.SetBalanceForPitchingInfiniteForBasic(v.BalanceForPitchingInfiniteForBasic, true, nil)
	case "balanceforpitchingprimeforbasic":
		return obj.SetBalanceForPitchingPrimeForBasic(v.BalanceForPitchingPrimeForBasic, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TieringBalanceBasedPitchParams) setDynamicFields(v *config.TieringBalanceBasedPitchParams, dynamic bool, path []string) (err error) {

	err = obj.SetBalanceForPitchingInfiniteForBasic(v.BalanceForPitchingInfiniteForBasic, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBalanceForPitchingPrimeForBasic(v.BalanceForPitchingPrimeForBasic, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TieringBalanceBasedPitchParams) setStaticFields(v *config.TieringBalanceBasedPitchParams) error {

	return nil
}

func (obj *TieringBalanceBasedPitchParams) SetBalanceForPitchingInfiniteForBasic(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringBalanceBasedPitchParams.BalanceForPitchingInfiniteForBasic", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._BalanceForPitchingInfiniteForBasic, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BalanceForPitchingInfiniteForBasic")
	}
	return nil
}
func (obj *TieringBalanceBasedPitchParams) SetBalanceForPitchingPrimeForBasic(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringBalanceBasedPitchParams.BalanceForPitchingPrimeForBasic", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._BalanceForPitchingPrimeForBasic, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BalanceForPitchingPrimeForBasic")
	}
	return nil
}
