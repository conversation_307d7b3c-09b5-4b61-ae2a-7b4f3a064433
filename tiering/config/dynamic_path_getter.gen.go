// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "cxmovementhistorylimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CxMovementHistoryLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CxMovementHistoryLimit, nil
	case "minandroidversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAndroidVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAndroidVersion, nil
	case "miniosversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinIosVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinIosVersion, nil
	case "minavgmonthlybalanceforregulartier":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAvgMonthlyBalanceForRegularTier\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAvgMonthlyBalanceForRegularTier, nil
	case "minbalancepenaltyforregulartier":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinBalancePenaltyForRegularTier\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinBalancePenaltyForRegularTier, nil
	case "toshowupgradenotifforsalary":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ToShowUpgradeNotifForSalary\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ToShowUpgradeNotifForSalary, nil
	case "toshowupgradenotifforsalarylite":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ToShowUpgradeNotifForSalaryLite\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ToShowUpgradeNotifForSalaryLite, nil
	case "enablegoodusersilentgracesegmentation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableGoodUserSilentGraceSegmentation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableGoodUserSilentGraceSegmentation, nil
	case "enablerewardsabusersegmentation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRewardsAbuserSegmentation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRewardsAbuserSegmentation, nil
	case "enabletieringabuserdbcheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableTieringAbuserDbCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableTieringAbuserDbCheck, nil
	case "enablehometopbarbanner":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableHomeTopBarBanner\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableHomeTopBarBanner, nil
	case "enablepromowidget":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnablePromoWidget\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnablePromoWidget, nil
	case "enabletierdowngrades":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableTierDowngrades\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableTierDowngrades, nil
	case "isreactivationpromowidgetenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsReactivationPromoWidgetEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsReactivationPromoWidgetEnabled, nil
	case "shouldroundgracetonextday":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShouldRoundGraceToNextDay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShouldRoundGraceToNextDay, nil
	case "displaypropertykeyexpiryduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisplayPropertyKeyExpiryDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisplayPropertyKeyExpiryDuration, nil
	case "autoupgradecutoff":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AutoUpgradeCutoff\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AutoUpgradeCutoff, nil
	case "gracewindowduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GraceWindowDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GraceWindowDuration, nil
	case "downgradewindowduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DowngradeWindowDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DowngradeWindowDuration, nil
	case "graceinitialwindowduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GraceInitialWindowDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GraceInitialWindowDuration, nil
	case "displaycomponentttlmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DisplayComponentTTLMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"DisplayComponentTTLMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.DisplayComponentTTLMap[dynamicFieldPath[1]], nil

		}
		return obj.DisplayComponentTTLMap, nil
	case "notificationconfigmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.NotificationConfigMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.NotificationConfigMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.NotificationConfigMap, nil
	case "tiertoineligibleactorsegmentidmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.TierToIneligibleActorSegmentIdMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"TierToIneligibleActorSegmentIdMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.TierToIneligibleActorSegmentIdMap[dynamicFieldPath[1]], nil

		}
		return obj.TierToIneligibleActorSegmentIdMap, nil
	case "silentgracetiersegmentsmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SilentGraceTierSegmentsMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"SilentGraceTierSegmentsMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.SilentGraceTierSegmentsMap[dynamicFieldPath[1]], nil

		}
		return obj.SilentGraceTierSegmentsMap, nil
	case "tiereodbalanceserviceconfigmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.TierEODBalanceServiceConfigMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.TierEODBalanceServiceConfigMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.TierEODBalanceServiceConfigMap, nil
	case "segmentidsforreactivationpromowidget_3txn":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SegmentIdsForReactivationPromoWidget_3txn, nil
		case len(dynamicFieldPath) > 1:

			return obj.SegmentIdsForReactivationPromoWidget_3txn[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.SegmentIdsForReactivationPromoWidget_3txn, nil
	case "segmentidsforreactivationpromowidget_5txn":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SegmentIdsForReactivationPromoWidget_5txn, nil
		case len(dynamicFieldPath) > 1:

			return obj.SegmentIdsForReactivationPromoWidget_5txn[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.SegmentIdsForReactivationPromoWidget_5txn, nil
	case "regulartierlaunchdate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RegularTierLaunchDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RegularTierLaunchDate, nil
	case "cooloffparams":
		return obj.CooloffParams.Get(dynamicFieldPath[1:])
	case "tieringfeaturerelease":
		return obj.TieringFeatureRelease.Get(dynamicFieldPath[1:])
	case "tierreevaluationeventsqssubscriber":
		return obj.TierReEvaluationEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "processinvestmenteventsubscriber":
		return obj.ProcessInvestmentEventSubscriber.Get(dynamicFieldPath[1:])
	case "processusstockswalletordereventsubscriber":
		return obj.ProcessUsStocksWalletOrderEventSubscriber.Get(dynamicFieldPath[1:])
	case "processkycupdateeventsubscriber":
		return obj.ProcessKycUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "processsalaryupdateeventsubscriber":
		return obj.ProcessSalaryUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "processbalanceupdateeventsubscriber":
		return obj.ProcessBalanceUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "processaddfundsordereventsubscriber":
		return obj.ProcessAddFundsOrderEventSubscriber.Get(dynamicFieldPath[1:])
	case "actortierinfocacheconfig":
		return obj.ActorTierInfoCacheConfig.Get(dynamicFieldPath[1:])
	case "eligibletiermovementcacheconfig":
		return obj.EligibleTierMovementCacheConfig.Get(dynamicFieldPath[1:])
	case "tiermovementhistorycacheconfig":
		return obj.TierMovementHistoryCacheConfig.Get(dynamicFieldPath[1:])
	case "segmentids":
		return obj.SegmentIds.Get(dynamicFieldPath[1:])
	case "criteriasegmentexclusions":
		return obj.CriteriaSegmentExclusions.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "processbalanceupdateeventmarketingsubscriber":
		return obj.ProcessBalanceUpdateEventMarketingSubscriber.Get(dynamicFieldPath[1:])
	case "autoupgradeconfig":
		return obj.AutoUpgradeConfig.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "tieringtrialconfig":
		return obj.TieringTrialConfig.Get(dynamicFieldPath[1:])
	case "tieringbalancebasedpitchparams":
		return obj.TieringBalanceBasedPitchParams.Get(dynamicFieldPath[1:])
	case "abfeaturereleaseconfig":
		return obj.ABFeatureReleaseConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CooloffParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxmovementsallowed":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxMovementsAllowed\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxMovementsAllowed, nil
	case "evaluationwindowduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EvaluationWindowDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EvaluationWindowDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CooloffParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InternalReleaseConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "rolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RolloutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RolloutPercentage, nil
	case "isfeatureenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsFeatureEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsFeatureEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InternalReleaseConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NotificationParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "autodismissonhomeafterinseconds":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AutoDismissOnHomeAfterInSeconds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AutoDismissOnHomeAfterInSeconds, nil
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "expireafter":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExpireAfter\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExpireAfter, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NotificationParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iscachingenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCachingEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsCachingEnabled, nil
	case "cachettl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CacheTTl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CacheTTl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SegmentIds) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "gooduser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GoodUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GoodUser, nil
	case "rewardsabuser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RewardsAbuser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RewardsAbuser, nil
	case "highertierscashbackrewardineligibleuser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HigherTiersCashbackRewardInEligibleUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HigherTiersCashbackRewardInEligibleUser, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SegmentIds", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CriteriaSegmentExclusions) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "salaryb2cexcludedsegments":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SalaryB2CExcludedSegments\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SalaryB2CExcludedSegments, nil
	case "aasalaryexcludedsegments":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AaSalaryExcludedSegments\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AaSalaryExcludedSegments, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CriteriaSegmentExclusions", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EODBalanceServiceConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minaggregationdays":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAggregationDays\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAggregationDays, nil
	case "averagebalancethreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AverageBalanceThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AverageBalanceThreshold, nil
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	case "eodbalanceaggregateduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EODBalanceAggregateDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EODBalanceAggregateDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EODBalanceServiceConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AutoUpgradeConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "disableprovenancecheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableProvenanceCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableProvenanceCheck, nil
	case "disablesalarytiercheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableSalaryTierCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableSalaryTierCheck, nil
	case "disablefirsttiermovementcheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableFirstTierMovementCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableFirstTierMovementCheck, nil
	case "disableautoupgradecutoffcheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableAutoUpgradeCutoffCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableAutoUpgradeCutoffCheck, nil
	case "disablehighertiercheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableHigherTierCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableHigherTierCheck, nil
	case "disableuserspecificchecks":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableUserSpecificChecks\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableUserSpecificChecks, nil
	case "disablebeenintieringcheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableBeenInTieringCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableBeenInTieringCheck, nil
	case "disableaveragebalancecheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableAverageBalanceCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableAverageBalanceCheck, nil
	case "avgbalancedurationbuffer":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AvgBalanceDurationBuffer\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AvgBalanceDurationBuffer, nil
	case "autoupgradeavgbalanceconditionmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AutoUpgradeAvgBalanceConditionMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.AutoUpgradeAvgBalanceConditionMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.AutoUpgradeAvgBalanceConditionMap, nil
	case "disabletotierconditionmap":
		return obj.DisableToTierConditionMap.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AutoUpgradeConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AverageBalanceConditions) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "averagebalanceconditionmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AverageBalanceConditionMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AverageBalanceConditionMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AverageBalanceConditionMap[dynamicFieldPath[1]], nil

		}
		return obj.AverageBalanceConditionMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AverageBalanceConditions", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TierConditionMap) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "regular":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Regular\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Regular, nil
	case "standard":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Standard\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Standard, nil
	case "plus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Plus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Plus, nil
	case "infinite":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Infinite\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Infinite, nil
	case "aasalary":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AaSalary\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AaSalary, nil
	case "salarylite":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SalaryLite\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SalaryLite, nil
	case "salary":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Salary\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Salary, nil
	case "salarybasic":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SalaryBasic\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SalaryBasic, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TierConditionMap", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SegmentTxnDetails) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "startdate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StartDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.StartDate, nil
	case "enddate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EndDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EndDate, nil
	case "rewardamount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RewardAmount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RewardAmount, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SegmentTxnDetails", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TieringTrialConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minimumtrialduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinimumTrialDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinimumTrialDuration, nil
	case "plustrialsegments":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PlusTrialSegments\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PlusTrialSegments, nil
	case "infinitetrialsegments":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InfiniteTrialSegments\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InfiniteTrialSegments, nil
	case "primetrialsegments":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PrimeTrialSegments\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PrimeTrialSegments, nil
	case "trialentrypointstartdate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TrialEntryPointStartDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrialEntryPointStartDate, nil
	case "trialentrypointenddate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TrialEntryPointEndDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrialEntryPointEndDate, nil
	case "trialenddate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TrialEndDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrialEndDate, nil
	case "trialenddatewithoutgrace":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TrialEndDateWithoutGrace\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrialEndDateWithoutGrace, nil
	case "trialintroweburl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TrialIntroWebUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrialIntroWebUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TieringTrialConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TieringBalanceBasedPitchParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "balanceforpitchinginfiniteforbasic":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BalanceForPitchingInfiniteForBasic\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BalanceForPitchingInfiniteForBasic, nil
	case "balanceforpitchingprimeforbasic":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BalanceForPitchingPrimeForBasic\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BalanceForPitchingPrimeForBasic, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TieringBalanceBasedPitchParams", strings.Join(dynamicFieldPath, "."))
	}
}
