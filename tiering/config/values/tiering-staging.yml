Application:
  Environment: "staging"
  Name: "tiering"

Server:
  Ports:
    GrpcPort: 8092
    GrpcSecurePort: 9501
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "tiering"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

TieringDb:
  AppName: "tiering"
  StatementTimeout: 1m
  Name: "tiering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true

Tracing:
  Enable: true

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"
    TieringDbCreds: "staging/rds/epifimetis/tiering_dev_user"
    StartreePinotAuthToken: "staging/pinot/startree"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

TieringFeatureRelease:
  IsFeatureEnabled: true
  RolloutPercentage: 0
  AllowedGroups:
    - 1 # INTERNAL
    - 2 # FNF
    - 19 # TIERING

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_REGULAR_TIER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_TIERING_MULTIPLE_WAYS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_TIERING_PITCH_THROUGH_PROMO_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100

TierReEvaluationEventSqsSubscriber:
  StartOnServerStart: false
  Disable: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-tiering-re-evaluation-queue"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

CooloffParams:
  MaxMovementsAllowed: 1000
  EvaluationWindowDuration: 10m

GraceParams:
  ResetDuration: 1h
  Ladder: [72h,48h,24h] # 3 days, 2 days, 1 day
  SilentGracePeriod: 0h
  GoodUserSilentGracePeriod: 3h

DisplayPropertyKeyExpiryDuration : 2m

DaoCacheRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  HystrixCommand:
    CommandName: "actor_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

DisplayPropertiesRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 11

DisplayComponentTTLMap:
  - "DISPLAY_COMPONENT_TIER_INTRODUCTION_SCREEN" : 1m

ProcessKycUpdateEventSubscriber:
  StartOnServerStart: false
  Disable: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-tiering-process-kyc-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessSalaryUpdateEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-tiering-process-salary-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessBalanceUpdateEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-tiering-balance-update-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessAddFundsOrderEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-tiering-order-add-funds-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessInvestmentEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-tiering-process-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessUsStocksWalletOrderEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-tiering-process-usstocks-wallet-order-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

AutoUpgradeCutoff : 72h

NotificationConfigMap:
  - NOTIFICATION_TYPE_UPGRADE:
      IsEnabled: true
      ExpireAfter: 168h # 7 days
      AutoDismissOnHomeAfterInSeconds: 60
  - NOTIFICATION_TYPE_DOWNGRADE:
      IsEnabled: true
      ExpireAfter: 720h # 30 days
      AutoDismissOnHomeAfterInSeconds: 60
  - NOTIFICATION_TYPE_GRACE:
      IsEnabled: true
      AutoDismissOnHomeAfterInSeconds: 60

ActorTierInfoCacheConfig:
  IsCachingEnabled: false
  Prefix: "TIERING:ATI:"
  CacheTTl: "48h" # 2 days

EligibleTierMovementCacheConfig:
  IsCachingEnabled: true
  Prefix: "TIERING:ETM:"
  CacheTTl: "1h"

TierMovementHistoryCacheConfig:
  IsCachingEnabled: true
  Prefix: "TIERING:TMH:"
  CacheTTl: "1h"

TierUpdateEventExternalPublisher:
  TopicName: "staging-tiering-tier-update-topic"

EnableHomeTopBarBanner: true
EnablePromoWidget: true

# Feature flag to control whether to use tiering_abuser table for abuser checks
# When enabled, uses table-based approach instead of segment-based approach
EnableTieringAbuserDbCheck: true

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: tiering-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

ProcessBalanceUpdateEventMarketingSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-tiering-balance-update-marketing-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

TierEODBalanceServiceConfigMap:
  - TIER_TEN:
      Enable: false
  - TIER_ONE_HUNDRED:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 2000
  - TIER_ONE_THOUSAND:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 10000
  - TIER_ONE_THOUSAND_FIVE_HUNDRED:
      Enable: false
  - TIER_TWO_THOUSAND:
      Enable: false
  - TIER_ONE_THOUSAND_TWO_NINETY:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 80000

EnableTierDowngrades: true


IsReactivationPromoWidgetEnabled: true

SegmentIdsForReactivationPromoWidget_3txn:
  reactivation_promo_widget_3_txn_segment_1:
    StartDate: 16
    EndDate: 30
    RewardAmount: 50

SegmentIdsForReactivationPromoWidget_5txn:
  reactivation_promo_widget_5_txn_segment_1:
    StartDate: 12
    EndDate: 30
    RewardAmount: 50
  reactivation_promo_widget_5_txn_segment_2:
    StartDate: 16
    EndDate: 30
    RewardAmount: 50
  reactivation_promo_widget_5_txn_segment_3:
    StartDate: 16
    EndDate: 30
    RewardAmount: 50
  reactivation_promo_widget_5_txn_segment_4:
    StartDate: 4
    EndDate: 15
    RewardAmount: 50

MinAvgMonthlyBalanceForRegularTier: 5000
MinBalancePenaltyForRegularTier: 200

TieringTrialConfig:
  TrialIntroWebUrl: "https://web.staging.pointz.in/trials"
