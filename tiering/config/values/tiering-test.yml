Application:
  Environment: "test"
  Name: "tiering"

Server:
  Ports:
    GrpcPort: 8092
    GrpcSecurePort: 9501
    HttpPort: 9892

EpifiDb:
  AppName: "tiering"
  StatementTimeout: 5m
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

TieringDb:
  AppName: "tiering"
  StatementTimeout: 1m
  Name: "tiering_test"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    TieringDbCreds: "{\"username\": \"root\", \"password\": \"\"}"
    StartreePinotAuthToken: "{\"AuthToken\": \"\"}"

Tracing:
  Enable: true

TierReEvaluationEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-re-evaluation-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

CooloffParams:
  MaxMovementsAllowed: 2
  EvaluationWindowDuration: 1h

GraceParams:
  ResetDuration: 144h
  Ladder: [72h,48h,24h] # 3 days, 2 days, 1 day
  SilentGracePeriod: 0h
  GoodUserSilentGracePeriod: 3h

DisplayPropertyKeyExpiryDuration : 1m

EligibleTierMovementCacheConfig:
  IsCachingEnabled: true
  Prefix: "TIERING:ETM:"
  CacheTTl: "1h"

TierMovementHistoryCacheConfig:
  IsCachingEnabled: true
  Prefix: "TIERING:TMH:"
  CacheTTl: "1h"

DaoCacheRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0
  HystrixCommand:
    CommandName: "actor_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

DisplayPropertiesRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0

DisplayComponentTTLMap:
  - "DISPLAY_COMPONENT_TIER_INTRODUCTION_SCREEN" : 1m

ProcessKycUpdateEventSubscriber:
  StartOnServerStart: false
  Disable: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-process-kyc-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessSalaryUpdateEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-process-salary-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessBalanceUpdateEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-balance-update-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessAddFundsOrderEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-order-add-funds-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessInvestmentEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-process-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessUsStocksWalletOrderEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-process-usstocks-wallet-order-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

AutoUpgradeCutoff : 72h

TieringFeatureRelease:
  IsFeatureEnabled: true
  RolloutPercentage: 0
  AllowedGroups:
    - 19 # TIERING

NotificationConfigMap:
  - NOTIFICATION_TYPE_UPGRADE:
      IsEnabled: true
      ExpireAfter: 168h # 7 days
      AutoDismissOnHomeAfterInSeconds: 60
  - NOTIFICATION_TYPE_DOWNGRADE:
      IsEnabled: true
      ExpireAfter: 720h # 30 days
      AutoDismissOnHomeAfterInSeconds: 60
  - NOTIFICATION_TYPE_GRACE:
      IsEnabled: true
      AutoDismissOnHomeAfterInSeconds: 60

TierUpdateEventExternalPublisher:
  TopicName: "tiering-tier-update-topic"

EnableHomeTopBarBanner: true

# Feature flag to control whether to use tiering_abuser table for abuser checks
# When enabled, uses table-based approach instead of segment-based approach
EnableTieringAbuserDbCheck: false

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: tiering-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

GraceInitialWindowDuration: 72h # 3 days

SegmentIds:
  GoodUser: "test-segment"
  RewardsAbuser: "AWS_test-segment"

ProcessBalanceUpdateEventMarketingSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-balance-update-marketing-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

TierEODBalanceServiceConfigMap:
  - TIER_TEN:
      Enable: false
  - TIER_ONE_HUNDRED:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 2000
  - TIER_ONE_THOUSAND:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 10000
  - TIER_ONE_THOUSAND_FIVE_HUNDRED:
      Enable: false
  - TIER_TWO_THOUSAND:
      Enable: false
  - TIER_ONE_THOUSAND_TWO_NINETY:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 80000

AutoUpgradeAvgBalanceConditionMap:
  - TIER_ONE_HUNDRED:
      AverageBalanceConditionMap:
        - "168h": 5000 # 7 days
        - "720h": 2000 # 30 days
  - TIER_ONE_THOUSAND:
      AverageBalanceConditionMap:
        - "168h": 30000 # 7 days
        - "720h": 10000 # 30 days

AutoUpgradeConfig:
  DisableToTierConditionMap:
    Plus: true
    Infinite: false

IsReactivationPromoWidgetEnabled: true

SegmentIdsForReactivationPromoWidget_3txn:
  reactivation_promo_widget_3_txn_segment_1:
    StartDate: 16
    EndDate: 30

SegmentIdsForReactivationPromoWidget_5txn:
  reactivation_promo_widget_5_txn_segment_1:
    StartDate: 12
    EndDate: 30
  reactivation_promo_widget_5_txn_segment_2:
    StartDate: 16
    EndDate: 30
  reactivation_promo_widget_5_txn_segment_3:
    StartDate: 16
    EndDate: 30
  reactivation_promo_widget_5_txn_segment_4:
    StartDate: 4
    EndDate: 15

CriteriaSegmentExclusions:
  SalaryB2CExcludedSegments:
    - "salary-b2c-exclusion-segment"
  AaSalaryExcludedSegments:
    - "aa-salary-exclusion-segment"

TieringSherlockBannersConfig:
  Banners:
    - IsEnabled: true
      EligibleSegmentId: "AMB_CHARGES_ELIGIBLE_USERS_DEV"
      Title: "No AMB Fees applicable for Apr-May. Refund if Charged."
      Body: ""
      BannerType: "AMB_CHARGES"

EnablePromoWidget: true

TieringTrialConfig:
  TrialEntryPointStartDate: "2025-01-01T00:00:00+05:30"
  TrialEntryPointEndDate: "2025-01-28T00:00:00+05:30"
  TrialEndDate: "2025-02-19T00:00:00+05:30"
  MinimumTrialDuration: 528h # 22 days
  PlusTrialSegments:
    - "trial-test-segment-plus"
  InfiniteTrialSegments:
    - "trial-test-segment-infinite"
  PrimeTrialSegments:
    - "trial-test-segment-prime"
