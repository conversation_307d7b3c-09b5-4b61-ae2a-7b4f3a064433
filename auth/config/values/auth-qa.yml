Application:
  Environment: "qa"
  Name: "auth"
  AndroidClientSignature: "FoPk23HZhnO"
  OAuthAndroidClientID: "432746002179-4q86u70ot3opldm79u9qnd82icd3b64h.apps.googleusercontent.com"
  OAuthIOSClientID: "411450025013-t1n09gmk2n6cf2dplh69mmsrktkgmlqj.apps.googleusercontent.com"
  UseFFMPEGToExtractFrame: true
  UserAccessTokenSigningMethod:
    # Keeping it equal to 60 minutes same as the validity of OAuth id token returned by Google
    # 60 * 60
    Duration: 3600
    # 60 minutes * 60
    InactivityTimer: 3600
    Algo: "HS256"
    IsRetiredKeyPresent: true
  UserRefreshTokenSigningMethod:
    # 30 days => 60 * 60 * 24 * 30
    Duration: 2592000
    # No need of inactivity timer on refresh token as that's enforced on access token
    InactivityTimer: 2592000
    Algo: "HS384"
    IsRetiredKeyPresent: true
  WaitlistUserAccessTokenSigningMethod:
    Duration: 3600
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  AppInsightsAccessTokenSigningMethod:
    Duration: 900
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: false
  ChatbotAccessTokenSigningMethod:
    # 24hrs -> 24 * 60 * 60 = 86400
    # the total duration of a chat session can extend beyond 1hr in agent's conversation
    Duration: 86400
    # setting inactivity timer same as token duration to avoid repetition in token generation while testing chatbot
    InactivityTimer: 86400
    Algo: "HS256"
    IsRetiredKeyPresent: false
  WebLiteAccessTokenSigningMethod:
    Duration: 3600
    InactivityTimer: 3600
    Algo: "HS256"
    IsRetiredKeyPresent: false
  GenieRefreshTokenSigningMethod:
    # 1 day => 60 * 60 * 24
    Duration: 86400
    InactivityTimer: 86400
    Algo: "HS384"
    IsRetiredKeyPresent: true
  GenieAccessTokenSigningMethod:
    # Keeping it equal to 15 minutes
    Duration: 900
    # Keeping it 15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  WebCABTRefreshTokenSigningMethod:
    # 1 day => 60 * 60 * 24
    Duration: 86400
    InactivityTimer: 86400
    Algo: "HS384"
    IsRetiredKeyPresent: true
  WebCABTAccessTokenSigningMethod:
    # Keeping it equal to 15 minutes
    Duration: 900
    # Keeping it 15 minutes.
    InactivityTimer: 900
    Algo: "HS256"
    IsRetiredKeyPresent: true
  RiskOutcallWebformAccessTokenSigningMethod:
    # Keeping it equal to 30 minutes
    Duration: 1800
    # Keeping it 30 minutes.
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: true
  NetworthMcpAccessTokenSigningMethod:
    Duration: 1800
    InactivityTimer: 1800
    Algo: "HS256"
    IsRetiredKeyPresent: false
  SMSConfig:
    PhoneNumbers:
      - 9743994779
      - 9743994780
      - 9743994781
    DeviceRegistrationPrefix: "EPIFY "
  EnableHybridOAuthVerifier: true
  NPCIDeviceBindingLimitPerDay: 200
  SkipClientIDCheck: true
  OAuthVerifierType: 2 #Hybrid
  AppleOAuthIOSClientID: "com.epifi.fi.qa"
  AppleClientSecretValidityInMinutes: "10m"
  IsSecureRedis: true

Server:
  Ports:
    GrpcPort: 8086
    GrpcSecurePort: 9503
    HttpPort: 9999
    HttpPProfPort: 9990

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
  EnableReadsFromReplica: true
  ReplicaAddr: "replica.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
  HystrixCommand:
    CommandName: "auth_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

KarzaLivenessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-check-liveness-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

LivenessStatusPublisher:
  QueueName: "qa-liveness-status-queue"

LivenessManualReviewPublisher:
  TopicName: "qa-liveness-manual-review-topic"

LivenessSummaryCompletedEventPublisher:
  TopicName: "qa-liveness-summary-completed-event-topic"

LivenessStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-liveness-status-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 9
      TimeUnit: "Second"

Aws:
  Endpoint: "localhost:4576"
  Region: "ap-south-1"
  S3:
    LivenessBucketName: "epifi-qa-liveness"

OtpConfig:
  OtpResendInterval: 30s
  GenerateOtpLimits:
    - Duration: 5m
      MaxAttempts: 20
  SkipOTPValidationForNumbers:
    - "916666012345"
    - "917777012345"
    - "912222654321"
    - "911111654321"
    - "911212654321"
    - "911212654325"
    - "911212654326"
    - "911212654327"
    - "911212654328"
    - "911212654329"
    - "911212654320"
    - "916363017269" # Automation - Android device 3
    - "918088157315" # Automation - iOS device 1
    - "911111606060" # This number is reserved for Android benchmark tests
    - "918630192634" # For MF flow app automation
    - "917019928352" # For MF flow app automation
    - "918431359353" # For MF flow app automation
    - "916360850941" # For MF flow app automation
    - "918618489912" # For MF flow app automation
    - "916362175344" # For MF flow app automation
    - "917676796586" # For MF flow app automation
    - "917676450328" # For MF flow app automation
    - "916363017269" # For IDFC loans flow app automation on Android
    - "918088157315" # For IDFC loans flow app automation on iOS
    - "919842714115" # Automation - Android device 4
    - "919842714116" # Automation - iOS device 2
    - "917200269987" # For Guardian automation from server
    - "919842798427" # Automation - Android device 1
    - "919842714117" # Automation - Android device 2
    - "919842714118" # Automation - Android device 5
    - "919842714119" # Automation - iOS device 3
    - "919842714114" # Automation - iOS device 5
    - "919842714113" # Automation - iOS device 4
    - "919842714112" # Automation - iOS device 6
    - "919842714111" # Automation - Android- AFU
    - "919842714110" # Automation - iOS - AFU
    - "************" # Automation - iOS device 7
    - "************" # Automation - Android device 6
    - "************" # Automation - Android device 4
    - "************" # Automation - iOS device 8
    - "************" # Reserved for Automation
    - "************" # Reserved for Automation
    - "************" # Reserved for Automation
    - "************" # Reserved for Automation
    - "************" # Reserved for Automation
    - "************" # Reserved for Automation


BankConfig:
  CardPinLength: 4
  SecurePinLength: 4
  BankLogoUrl: "https://epifi-icons.pointz.in/fibank/icon/96.png"
  BankName: "Federal Bank"
  VendorOtpLength: 6

DeviceIntegrityConfig:
  AndroidAppPackageNames:
    - "com.epifi.paisa.qa"
  AndroidApkCertificateDigestSha256:
    - "0kYoXaKxm/apSWnciRJqY3N3YBDSyUvuoUOKKVkgCo4="
  DeviceIntegrityNonceValidityInSecs: 300
  CheckApkPackageName: false
  CheckCertificateHash: false
  EnableHybridOAuthVerifier: false
  AppleAppAttestRootCertificate: |
    -----BEGIN CERTIFICATE-----
    MIICITCCAaegAwIBAgIQC/O+DvHN0uD7jG5yH2IXmDAKBggqhkjOPQQDAzBSMSYw
    JAYDVQQDDB1BcHBsZSBBcHAgQXR0ZXN0YXRpb24gUm9vdCBDQTETMBEGA1UECgwK
    QXBwbGUgSW5jLjETMBEGA1UECAwKQ2FsaWZvcm5pYTAeFw0yMDAzMTgxODMyNTNa
    Fw00NTAzMTUwMDAwMDBaMFIxJjAkBgNVBAMMHUFwcGxlIEFwcCBBdHRlc3RhdGlv
    biBSb290IENBMRMwEQYDVQQKDApBcHBsZSBJbmMuMRMwEQYDVQQIDApDYWxpZm9y
    bmlhMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERTHhmLW07ATaFQIEVwTtT4dyctdh
    NbJhFs/Ii2FdCgAHGbpphY3+d8qjuDngIN3WVhQUBHAoMeQ/cLiP1sOUtgjqK9au
    Yen1mMEvRq9Sk3Jm5X8U62H+xTD3FE9TgS41o0IwQDAPBgNVHRMBAf8EBTADAQH/
    MB0GA1UdDgQWBBSskRBTM72+aEH/pwyp5frq5eWKoTAOBgNVHQ8BAf8EBAMCAQYw
    CgYIKoZIzj0EAwMDaAAwZQIwQgFGnByvsiVbpTKwSga0kP0e8EeDS4+sQmTvb7vn
    53O5+FRXgeLhpJ06ysC5PrOyAjEAp5U4xDgEgllF7En3VcE3iexZZtKeYnpqtijV
    oyFraWVIyd/dganmrduC1bmTBGwD
    -----END CERTIFICATE-----
  AppleCredCertOidExtensionSeq: [ 1, 2, 840, 113635, 100, 8, 2 ]
  IosAppIdentifiers:
    - "com.epifi.fi.qa"
  AllowSafetynetCertChainVerificationWithModifiedCurrTime: true
  CurrTimeOverrideForSafetynetCertVerification: "-48h"
  ExpiryTimeForDeviceIntegrityV2: 4m
  BypassTokenIdValidationForPhoneNumbers:
    - 912222654321
    - 911111654321
    - 911212654321
    - 911212654325
    - 911212654326
    - 911212654327
    - 911212654328
    - 911212654329
    - 911212654320
    - 911111606060 # This number is reserved for Android benchmark tests
  DeviceAttestationV2Cfg:
    DisableFeature: false
    MinAndroidVersion: 183
    MinIOSVersion: 481
    FallbackToEnableFeature: false
  ExpiryTimeForDeviceIntegrity: 1h5m
  MockSafetynetTokenResult:
    SAFETYNET_CTS_PROFILE_MATCH_TEST_FAILED: "97ff6c4e-f4a3-486d-bf42-3e80e56edc12"
    SAFETYNET_BASIC_INTEGRITY_TEST_FAILED: "97ff6c4e-f4a3-486d-bf42-3e80e56edc13"
    ERROR_EXTRACTING_ATTESTATION_PAYLOAD: "a87736ff-c8fb-482c-9705-06d4c7c779dd"
    SAFETYNET_ATTESTATION_CREATION_FAILURE: "a87736ff-c8fb-482c-9705-06d4c7c779dl"

AFU:
  CredentialsOrder:
    UPDATE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
      - [ ATM_PIN_VALIDATION ] # LEVEL 1
    UPDATE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
    UPDATE_PHONE_NUM_EMAIL:
      - [ LIVENESS_FM_VALIDATION ] # LEVEL 0
      - [ ATM_PIN_VALIDATION ] # LEVEL 1
    UPDATE_DEVICE_PHONE_NUM:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_DEVICE_EMAIL:
      - [ LIVENESS_FM_VALIDATION ]
      - [ ATM_PIN_VALIDATION ]
    UPDATE_EMAIL_SIM:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_DEVICE:
      - [ LIVENESS_FM_VALIDATION ]
    UPDATE_SIM: [ ]
  VendorUpdateProducerDelayInSecs: 1
  BypassCredentialVerificationForPhoneNumbers:
    - 916666012345
    - 917777012345
    - 912222654321
    - 911111654321
    - 911212654321
    - 911212654325
    - 911212654326
    - 911212654327
    - 911212654328
    - 911212654329
    - 911212654320
    - 911111606060 # This number is reserved for Android benchmark tests
  ReRegInitIncrementalDelay: 12s
  LivenessManualReviewExpiryTime: 10m
  MaxRecordsDepthForAFUTroubleshooter: 25
  AuthFactorUpdateCacheConfig:
    IsCachingEnabled: true
    AuthFactorUpdatePrefix: "AFU_"
  MinVersionForAFURetry:
    MinAndroidVersion: 1000
    MinIOSVersion: 1000
    FallbackToEnableFeature: true
    DisableFeature: true

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: true

Flags:
  TrimDebugMessageFromStatus: false
  SkipIOSIdTokenExpiryCheck: false
  EnableLivenessManualReviewInAfu: true
  EnableCheckForAccessRevoke: false
  EnableSMSAckListener: true

AFUVendorUpdatePublisher:
  QueueName: "qa-afu-vendor-update-queue"

AFUVendorUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-afu-vendor-update-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~5 min post that regular interval is followed for next 3hr20mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 7
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 5
          MaxAttempts: 40
          TimeUnit: "Minute"
      MaxAttempts: 47
      CutOff: 6

AFUManualReviewNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-afu-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ProcessPinAttemptsExceededEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-auth-pin-attempts-exceeded-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"


DeviceRegSMSAckSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-device-reg-sms-ack-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

BiometricEventPublisher:
  QueueName: "qa-biometrics-details-queue"

AuthFactorUpdatePublisher:
  TopicName: "qa-auth-factor-update"

DeviceReregCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-device-rereg-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

ReturnOtpTokenOnError: true

DeviceLocationCacheConfig:
  IsCachingEnabled: true
  CacheTTl: "2m"
  DeviceLocationPrefix: "device_location_"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

DeviceRegistration:
  SMSAckUrl: "https://vnotificationgw.qa.pointz.in/openbanking/auth/federal/user-device/registration-sms-update"

Keycloak:
  RealmName: "Onboarding"
  BaseURL: "https://keycloak.pointz.in/"

AuthSecrets:
  SecretsKey: "qa/auth/secrets"

TokenStoresCacheConfig:
  UseTokenStoresDaoV2: true

AuthTokenCreationPublisher:
  TopicName: "qa-auth-token-creation-topic"

NetworthMcpConfig:
  LoginUrl: "https://web.qa.pointz.in/wealth-mcp-login?token="
