package deeplink

import (
	"encoding/base64"
	"fmt"
	"net/url"
	"regexp"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg/dynarray"
	typesPkg "github.com/epifi/be-common/pkg/types"

	cfggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"

	deeplinkcfgv2 "github.com/epifi/gamma/pkg/deeplink/cfg/v2/genconf"

	"github.com/golang/protobuf/proto"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/frontend/deeplink"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	rewardsScreenOptionsV2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	salaryProgramScreenOptionsV2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"
	deeplinkCfg "github.com/epifi/gamma/pkg/deeplink/cfg"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

const (
	SCREEN_DOMAIN   = "screen"
	DEEPLINK_SCHEME = "fi"
)

var (
	ScreenOptionsMisMatchError = fmt.Errorf("mismatch in expected and received ScreenOptions")
	InvalidDeeplinkScreenError = fmt.Errorf("invalid deeplink screen")
)

// NewDeeplink should be used to create a new Deeplink object.
// It validates the screen and screenOptions matching.
// `screenOptions` confirms to `isDeeplink_ScreenOptions` interface in frontend/deeplink/deeplink.pb.go.
// The interface is for `oneOf` proto field screenOptions
func NewDeeplink(screen deeplink.Screen, screenOptions interface{}) (*deeplink.Deeplink, error) {
	if screenOptions == nil {
		return &deeplink.Deeplink{Screen: screen}, nil
	}

	// Please add new deeplink screen options here
	switch screen {
	case deeplink.Screen_REGISTER_CKYC:
		if typed, ok := screenOptions.(*deeplink.Deeplink_RegisterCkycScreenOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_TIMELINE:
		if timeLineOptions, ok := screenOptions.(*deeplink.Deeplink_TimelineScreenOptions); ok {
			return &deeplink.Deeplink{
				Screen:        screen,
				ScreenOptions: timeLineOptions,
			}, nil
		}
	case deeplink.Screen_MOBILE_PROMPT_VERIFICATION:
		if typed, ok := screenOptions.(*deeplink.Deeplink_MobilePromptVerificationScreenOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_DEPOSIT_LANDING_SCREEN:
		if typed, ok := screenOptions.(*deeplink.Deeplink_DepositAccountLandingScreenOption); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_DEPOSIT_ACCOUNT_ADD_MONEY:
		if typed, ok := screenOptions.(*deeplink.Deeplink_DepositAddMoneyOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_DEPOSIT_CLOSE_ACCOUNT:
		if typed, ok := screenOptions.(*deeplink.Deeplink_DepositCloseAccountOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_DEPOSIT_OPEN_ACCOUNT:
		if _, ok := screenOptions.(*deeplink.Deeplink_DepositOpenAccountOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: nil}, nil
		}
	case deeplink.Screen_DEPOSIT_ACCOUNT_DETAILS:
		if typed, ok := screenOptions.(*deeplink.Deeplink_DepositDetailsScreenOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_TRANSACTION_RECEIPT:
		if typed, ok := screenOptions.(*deeplink.Deeplink_TransactionReceiptScreenOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_INFORMATION_POPUP:
		if typed, ok := screenOptions.(*deeplink.Deeplink_InformationPopupOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_WEALTH_ONBOARDING_INFO_SCREEN:
		if typed, ok := screenOptions.(*deeplink.Deeplink_WealthOnboardingInfoScreenOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}
	case deeplink.Screen_RAISE_DISPUTE:
		if typed, ok := screenOptions.(*deeplink.Deeplink_RaiseDisputeOptions); ok {
			return &deeplink.Deeplink{Screen: screen, ScreenOptions: typed}, nil
		}

	default:
		return nil, ScreenOptionsMisMatchError
	}

	return nil, ScreenOptionsMisMatchError
}

func NewDeeplinkFromConfig(deeplinkConf *deeplinkCfg.Deeplink) (*deeplink.Deeplink, error) {
	switch {
	case strings.EqualFold(deeplinkConf.Screen, deeplink.Screen_INFORMATION_POPUP.String()):
		if deeplinkConf.InformationPopupDeeplinkScreenOptions != nil {
			screenOption, err := NewInformationPopupScreenOptions(deeplinkConf.InformationPopupDeeplinkScreenOptions)
			if err != nil {
				return nil, err
			}

			return &deeplink.Deeplink{
				Screen:        deeplink.Screen_INFORMATION_POPUP,
				ScreenOptions: screenOption,
			}, nil
		}
	case strings.EqualFold(deeplinkConf.Screen, deeplink.Screen_STORY_SCREEN.String()):
		if deeplinkConf.StoryScreenOptions != nil {
			screenOption, err := NewStoryScreenOptions(deeplinkConf.StoryScreenOptions)
			if err != nil {
				return nil, err
			}

			return &deeplink.Deeplink{
				Screen:        deeplink.Screen_STORY_SCREEN,
				ScreenOptions: screenOption,
			}, nil
		}

	case strings.EqualFold(deeplinkConf.Screen, deeplink.Screen_EXTERNAL_REDIRECTION.String()):
		if deeplinkConf.ExternalRedirectionScreenOptions != nil {
			screenOption, err := NewExternalRedirectionScreenOptions(deeplinkConf.ExternalRedirectionScreenOptions)
			if err != nil {
				return nil, err
			}

			return &deeplink.Deeplink{
				Screen:        deeplink.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: screenOption,
			}, nil
		}
	case strings.EqualFold(deeplinkConf.Screen, deeplink.Screen_OFFERS_LANDING_SCREEN.String()):
		var (
			screenOption *deeplink.Deeplink_OfferCatalogScreenOptions
			err          error
		)
		// screen options are optional for this deeplink
		if deeplinkConf.OfferCatalogScreenOptions != nil {
			screenOption, err = NewOfferCatalogScreenOptions(deeplinkConf.OfferCatalogScreenOptions)
			if err != nil {
				return nil, err
			}
		}
		return &deeplink.Deeplink{
			Screen:        deeplink.Screen_OFFERS_LANDING_SCREEN,
			ScreenOptions: screenOption,
		}, nil
	case strings.EqualFold(deeplinkConf.Screen, deeplink.Screen_DOWNLOAD_DIGITAL_CANCELLED_CHEQUE.String()):
		screenOption, err := NewDownloadDigitalCancelledChequeScreenOptions(deeplinkConf.DownloadDigitalCancelledChequeScreenOptions)
		if err != nil {
			return nil, err
		}
		return &deeplink.Deeplink{
			Screen:        deeplink.Screen_DOWNLOAD_DIGITAL_CANCELLED_CHEQUE,
			ScreenOptions: screenOption,
		}, nil

	case strings.EqualFold(deeplinkConf.Screen, deeplink.Screen_EARLY_SALARY_LANDING_SCREEN.String()):
		screenOption, err := NewPreApprovedLoanLandingScreenOptions(deeplinkConf.PreApprovedLoanLandingScreenOptions)
		if err != nil {
			return nil, err
		}
		return &deeplink.Deeplink{
			Screen:        deeplink.Screen_EARLY_SALARY_LANDING_SCREEN,
			ScreenOptions: screenOption,
		}, nil
	case strings.EqualFold(deeplinkConf.Screen, deeplink.Screen_REWARD_OFFER_DETAILS_SCREEN.String()):
		if deeplinkConf.RewardOfferDetailsScreenOptions != nil {
			return deeplinkv3.GetDeeplinkV3(deeplink.Screen_REWARD_OFFER_DETAILS_SCREEN, &rewardsScreenOptionsV2.RewardOfferDetailsScreenOptions{
				RewardOfferId: deeplinkConf.RewardOfferDetailsScreenOptions.RewardOfferId,
			})
		}
	case strings.EqualFold(deeplinkConf.Screen, deeplink.Screen_SALARY_PROGRAM_BENEFIT_INFO_SCREEN.String()):
		if deeplinkConf.SalaryBenefitInfoScreenOptions != nil {
			return deeplinkv3.GetDeeplinkV3(deeplink.Screen_SALARY_PROGRAM_BENEFIT_INFO_SCREEN, &salaryProgramScreenOptionsV2.SalaryProgramBenefitInfoScreenOptions{
				BenefitId: deeplinkConf.SalaryBenefitInfoScreenOptions.BenefitId,
			})
		}
	default:
		screen := deeplink.Screen(deeplink.Screen_value[deeplinkConf.Screen])
		if screen == deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED {
			return nil, InvalidDeeplinkScreenError
		}
		return &deeplink.Deeplink{Screen: screen}, nil
	}

	return nil, ScreenOptionsMisMatchError
}

func NewDeeplinkFromV2Config(deeplinkConf *deeplinkcfgv2.Deeplink) (*deeplink.Deeplink, error) {
	switch {
	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_INFORMATION_POPUP.String()):
		if deeplinkConf.InformationPopupDeeplinkScreenOptions() != nil {
			screenOption, err := NewInformationPopupScreenOptionsV2(deeplinkConf.InformationPopupDeeplinkScreenOptions())
			if err != nil {
				return nil, err
			}

			return &deeplink.Deeplink{
				Screen:        deeplink.Screen_INFORMATION_POPUP,
				ScreenOptions: screenOption,
			}, nil
		}
	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_STORY_SCREEN.String()):
		if deeplinkConf.StoryScreenOptions() != nil {
			screenOption, err := NewStoryScreenOptionsV2(deeplinkConf.StoryScreenOptions())
			if err != nil {
				return nil, err
			}

			return &deeplink.Deeplink{
				Screen:        deeplink.Screen_STORY_SCREEN,
				ScreenOptions: screenOption,
			}, nil
		}

	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_EXTERNAL_REDIRECTION.String()):
		if deeplinkConf.ExternalRedirectionScreenOptions() != nil {
			screenOption, err := NewExternalRedirectionScreenOptionsV2(deeplinkConf.ExternalRedirectionScreenOptions())
			if err != nil {
				return nil, err
			}

			return &deeplink.Deeplink{
				Screen:        deeplink.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: screenOption,
			}, nil
		}
	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_OFFERS_LANDING_SCREEN.String()):
		var (
			screenOption *deeplink.Deeplink_OfferCatalogScreenOptions
			err          error
		)
		// screen options are optional for this deeplink
		if deeplinkConf.OfferCatalogScreenOptions() != nil {
			screenOption, err = NewOfferCatalogScreenOptionsV2(deeplinkConf.OfferCatalogScreenOptions())
			if err != nil {
				return nil, err
			}
		}
		return &deeplink.Deeplink{
			Screen:        deeplink.Screen_OFFERS_LANDING_SCREEN,
			ScreenOptions: screenOption,
		}, nil
	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_DOWNLOAD_DIGITAL_CANCELLED_CHEQUE.String()):
		screenOption, err := NewDownloadDigitalCancelledChequeScreenOptionsV2(deeplinkConf.DownloadDigitalCancelledChequeScreenOptions())
		if err != nil {
			return nil, err
		}
		return &deeplink.Deeplink{
			Screen:        deeplink.Screen_DOWNLOAD_DIGITAL_CANCELLED_CHEQUE,
			ScreenOptions: screenOption,
		}, nil

	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_EARLY_SALARY_LANDING_SCREEN.String()):
		screenOption, err := NewPreApprovedLoanLandingScreenOptionsV2(deeplinkConf.PreApprovedLoanLandingScreenOptions())
		if err != nil {
			return nil, err
		}
		return &deeplink.Deeplink{
			Screen:        deeplink.Screen_EARLY_SALARY_LANDING_SCREEN,
			ScreenOptions: screenOption,
		}, nil
	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_REWARD_OFFER_DETAILS_SCREEN.String()):
		if deeplinkConf.RewardOfferDetailsScreenOptions() != nil {
			return deeplinkv3.GetDeeplinkV3(deeplink.Screen_REWARD_OFFER_DETAILS_SCREEN, &rewardsScreenOptionsV2.RewardOfferDetailsScreenOptions{
				RewardOfferId: deeplinkConf.RewardOfferDetailsScreenOptions().RewardOfferId(),
			})
		}
	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_SALARY_PROGRAM_BENEFIT_INFO_SCREEN.String()):
		if deeplinkConf.SalaryBenefitInfoScreenOptions() != nil {
			return deeplinkv3.GetDeeplinkV3(deeplink.Screen_SALARY_PROGRAM_BENEFIT_INFO_SCREEN, &salaryProgramScreenOptionsV2.SalaryProgramBenefitInfoScreenOptions{
				BenefitId: deeplinkConf.SalaryBenefitInfoScreenOptions().BenefitId(),
			})
		}
	case strings.EqualFold(deeplinkConf.Screen(), deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN.String()):
		screenOption, err := NewPreApprovedLoanLandingScreenOptionsV2(deeplinkConf.PreApprovedLoanLandingScreenOptions())
		if err != nil {
			return nil, err
		}
		return &deeplink.Deeplink{
			Screen:        deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			ScreenOptions: screenOption,
		}, nil
	default:
		screen := deeplink.Screen(deeplink.Screen_value[deeplinkConf.Screen()])
		if screen == deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED {
			return nil, InvalidDeeplinkScreenError
		}
		return &deeplink.Deeplink{Screen: screen}, nil
	}

	return nil, ScreenOptionsMisMatchError
}

// Generate takes in a deeplink object to return the deeplink string
// that can be used by the client to land user on a specific page.
func Generate(d *deeplink.Deeplink) (*string, error) {
	// validation of the received deeplink object
	if _, err := NewDeeplink(d.Screen, d.ScreenOptions); err != nil {
		return nil, err
	}
	marshalledProto, err := proto.Marshal(d)
	if err != nil {
		return nil, err
	}
	queryParam := fmt.Sprintf("data=%v", url.QueryEscape(string(marshalledProto)))
	return formatURI(queryParam), nil
}

func formatURI(queryParams string) *string {
	u := url.URL{
		Scheme:   DEEPLINK_SCHEME,
		Host:     SCREEN_DOMAIN,
		RawQuery: queryParams,
	}
	retVal := u.String()
	return &retVal
}

// This is temporary function for frontend as it is not able to deserialize
// the string generated from Generate() back to Deeplink object.
// This function returns deeplink in format: fi://screenName e.g. fi://registerCkyc
// It can't be snake case because of an issue in Android. Refer: anuj
func GenerateV2(d *deeplink.Deeplink) (*string, error) {
	matchLetterAfterUnderscore := regexp.MustCompile("(_[a-z])")
	host := strings.ToLower(d.Screen.String())

	//nolint:gocritic
	host = matchLetterAfterUnderscore.ReplaceAllStringFunc(host, func(s string) string {
		return strings.ToUpper(s)
	})
	host = strings.ReplaceAll(host, "_", "")
	dl := fmt.Sprintf("fi://%v/", host)
	return &dl, nil
}

func NewInformationPopupScreenOptions(conf *deeplinkCfg.InformationPopupDeeplinkScreenOptions) (*deeplink.Deeplink_InformationPopupOptions, error) {
	var protoCtas []*deeplink.Cta
	for _, cta := range conf.CTAs {
		protoCta, err := NewCTA(cta)
		if err != nil {
			return nil, fmt.Errorf("failed to generate cta for information popup screen option: %w", err)
		}

		protoCtas = append(protoCtas, protoCta)
	}

	return &deeplink.Deeplink_InformationPopupOptions{
		InformationPopupOptions: &deeplink.InformationPopupOptions{
			Title:            conf.Title,
			SubTitle:         conf.SubTitle,
			Body:             conf.Body,
			IconUrl:          conf.IconURL,
			Ctas:             protoCtas,
			IsNonDismissible: conf.IsNonDismissible,
			TextTitle:        typesPkg.NewText(conf.TextTitle, conf.Title),
			TextSubTitle:     typesPkg.NewText(conf.TextSubTitle, conf.SubTitle),
			BodyTexts:        getTextBodiesFromDeeplinkConf(conf),
			BgColor:          conf.BgColor,
		},
	}, nil
}

func NewStoryScreenOptions(conf *deeplinkCfg.StoryScreenOptions) (*deeplink.Deeplink_StoryScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_StoryScreenOptions{
		StoryScreenOptions: &deeplink.StoryScreenOptions{
			StoryTitle: conf.StoryTitle,
			StoryUrl:   conf.StoryUrl,
			StoryId:    conf.StoryId,
		},
	}, nil
}

func NewExternalRedirectionScreenOptions(conf *deeplinkCfg.ExternalRedirectionScreenOptions) (*deeplink.Deeplink_ExternalRedirectionScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_ExternalRedirectionScreenOptions{
		ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
			ExternalUrl: conf.ExternalUrl,
		},
	}, nil
}

func NewOfferCatalogScreenOptions(conf *deeplinkCfg.OfferCatalogScreenOptions) (*deeplink.Deeplink_OfferCatalogScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_OfferCatalogScreenOptions{
		OfferCatalogScreenOptions: &deeplink.OfferCatalogScreenOptions{
			DisplayFirstOfferIds: conf.DisplayFirstOfferIds,
		},
	}, nil
}

func NewDownloadDigitalCancelledChequeScreenOptions(conf *deeplinkCfg.DownloadDigitalCancelledChequeScreenOptions) (*deeplink.Deeplink_DownloadDigitalCancelledChequeScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_DownloadDigitalCancelledChequeScreenOptions{
		DownloadDigitalCancelledChequeScreenOptions: &deeplink.DownloadDigitalCancelledChequeScreenOptions{
			Title:       typesPkg.NewText(conf.Title, ""),
			Description: typesPkg.NewText(conf.Description, ""),
			CtaText:     typesPkg.NewText(conf.CtaText, ""),
		},
	}, nil
}

func NewPreApprovedLoanLandingScreenOptions(conf *deeplinkCfg.PreApprovedLoanLandingScreenOptions) (*deeplink.Deeplink_PreApprovedLoanLandingScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_PreApprovedLoanLandingScreenOptions{
		PreApprovedLoanLandingScreenOptions: &deeplink.PreApprovedLoanLandingScreenOptions{
			LoanHeader: &palEnumFePb.LoanHeader{
				LoanProgram: palEnumFePb.LoanProgram(conf.LoanProgram),
			},
		},
	}, nil
}

func NewCTA(conf *deeplinkCfg.CTA) (*deeplink.Cta, error) {
	var (
		ctaDeeplink *deeplink.Deeplink
		err         error
	)

	// deeplink is optional for CTAs as some CTA are no-op like cta type `DONE`
	if conf.Deeplink != nil {
		ctaDeeplink, err = NewDeeplinkFromConfig(conf.Deeplink)
		if err != nil {
			return nil, fmt.Errorf("failed to generate deeplink for CTA: %w", err)
		}
	}

	return &deeplink.Cta{
		Type:         deeplink.Cta_Type(deeplink.Cta_Type_value[strings.ToUpper(conf.Type)]),
		Text:         conf.Text,
		Deeplink:     ctaDeeplink,
		DisplayTheme: deeplink.Cta_DisplayTheme(deeplink.Cta_DisplayTheme_value[strings.ToUpper(conf.DisplayTheme)]),
	}, nil
}

func NewInformationPopupScreenOptionsV2(conf *deeplinkcfgv2.InformationPopupDeeplinkScreenOptions) (*deeplink.Deeplink_InformationPopupOptions, error) {
	var protoCtas []*deeplink.Cta
	for _, cta := range dynarray.MapToArray(conf.CTAs()) {
		protoCta, err := NewCTAV2(cta)
		if err != nil {
			return nil, fmt.Errorf("failed to generate cta for information popup screen option: %w", err)
		}
		protoCtas = append(protoCtas, protoCta)
	}
	return &deeplink.Deeplink_InformationPopupOptions{
		InformationPopupOptions: &deeplink.InformationPopupOptions{
			Title:            conf.Title(),
			SubTitle:         conf.SubTitle(),
			Body:             conf.Body(),
			IconUrl:          conf.IconURL(),
			Ctas:             protoCtas,
			IsNonDismissible: conf.IsNonDismissible(),
			TextTitle:        cfggenconf.NewTextPb(conf.TextTitle(), conf.Title()),
			TextSubTitle:     cfggenconf.NewTextPb(conf.TextSubTitle(), conf.SubTitle()),
			BodyTexts:        getTextBodiesFromDeeplinkConfV2(conf),
			BgColor:          conf.BgColor(),
		},
	}, nil
}

func NewStoryScreenOptionsV2(conf *deeplinkcfgv2.StoryScreenOptions) (*deeplink.Deeplink_StoryScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_StoryScreenOptions{
		StoryScreenOptions: &deeplink.StoryScreenOptions{
			StoryTitle: conf.StoryTitle(),
			StoryUrl:   conf.StoryUrl(),
			StoryId:    conf.StoryId(),
		},
	}, nil
}

func NewExternalRedirectionScreenOptionsV2(conf *deeplinkcfgv2.ExternalRedirectionScreenOptions) (*deeplink.Deeplink_ExternalRedirectionScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_ExternalRedirectionScreenOptions{
		ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
			ExternalUrl: conf.ExternalUrl(),
		},
	}, nil
}

func NewOfferCatalogScreenOptionsV2(conf *deeplinkcfgv2.OfferCatalogScreenOptions) (*deeplink.Deeplink_OfferCatalogScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	offerIds := conf.DisplayFirstOfferIds().Slice()
	if conf.DisplayFirstOfferIdsLen() != 0 && conf.DisplayFirstOfferIdsLen() < len(offerIds) {
		offerIds = offerIds[:conf.DisplayFirstOfferIdsLen()]
	}
	return &deeplink.Deeplink_OfferCatalogScreenOptions{
		OfferCatalogScreenOptions: &deeplink.OfferCatalogScreenOptions{
			DisplayFirstOfferIds: offerIds,
		},
	}, nil
}

func NewDownloadDigitalCancelledChequeScreenOptionsV2(conf *deeplinkcfgv2.DownloadDigitalCancelledChequeScreenOptions) (*deeplink.Deeplink_DownloadDigitalCancelledChequeScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_DownloadDigitalCancelledChequeScreenOptions{
		DownloadDigitalCancelledChequeScreenOptions: &deeplink.DownloadDigitalCancelledChequeScreenOptions{
			Title:       cfggenconf.NewTextPb(conf.Title(), ""),
			Description: cfggenconf.NewTextPb(conf.Description(), ""),
			CtaText:     cfggenconf.NewTextPb(conf.CtaText(), ""),
		},
	}, nil
}

func NewPreApprovedLoanLandingScreenOptionsV2(conf *deeplinkcfgv2.PreApprovedLoanLandingScreenOptions) (*deeplink.Deeplink_PreApprovedLoanLandingScreenOptions, error) {
	if conf == nil {
		return nil, epifierrors.ErrInvalidArgument
	}
	return &deeplink.Deeplink_PreApprovedLoanLandingScreenOptions{
		PreApprovedLoanLandingScreenOptions: &deeplink.PreApprovedLoanLandingScreenOptions{
			LoanHeader: &palEnumFePb.LoanHeader{
				LoanProgram: palEnumFePb.LoanProgram(conf.LoanProgram()),
				EventData:   &palEnumFePb.EventData{EntryPoint: palEnumFePb.EntryPoint(conf.EntryPoint())},
			},
		},
	}, nil
}

func NewCTAV2(conf *deeplinkcfgv2.CTA) (*deeplink.Cta, error) {
	var (
		ctaDeeplink *deeplink.Deeplink
		err         error
	)

	// deeplink is optional for CTAs as some CTA are no-op like cta type `DONE`
	// unlike static config, generated config conf.Deeplink() will not be nil even if the config is not populated.
	// We need to check if the zeroth value of screen to identify if the deeplink is populated or not.
	if conf.Deeplink().Screen() != "" {
		ctaDeeplink, err = NewDeeplinkFromV2Config(conf.Deeplink())
		if err != nil {
			return nil, fmt.Errorf("failed to generate deeplink for CTA: %w", err)
		}
	}

	return &deeplink.Cta{
		Type:         deeplink.Cta_Type(deeplink.Cta_Type_value[strings.ToUpper(conf.Type())]),
		Text:         conf.Text(),
		Deeplink:     ctaDeeplink,
		DisplayTheme: deeplink.Cta_DisplayTheme(deeplink.Cta_DisplayTheme_value[strings.ToUpper(conf.DisplayTheme())]),
	}, nil
}

func getTextBodiesFromDeeplinkConf(conf *deeplinkCfg.InformationPopupDeeplinkScreenOptions) []*commontypes.Text {
	var bodyTexts []*commontypes.Text
	if len(conf.BodyTexts) > 0 {
		for _, bodyText := range conf.BodyTexts {
			protoText := typesPkg.NewText(bodyText, "")
			bodyTexts = append(bodyTexts, protoText)
		}
	} else {
		protoText := &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: conf.Body,
			},
		}
		bodyTexts = append(bodyTexts, protoText)
	}
	return bodyTexts
}

func getTextBodiesFromDeeplinkConfV2(conf *deeplinkcfgv2.InformationPopupDeeplinkScreenOptions) []*commontypes.Text {
	var bodyTexts []*commontypes.Text
	for _, bodyText := range dynarray.MapToArray(conf.BodyTexts()) {
		protoText := cfggenconf.NewTextPb(bodyText, "")
		bodyTexts = append(bodyTexts, protoText)
	}

	if len(bodyTexts) == 0 {
		protoText := &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: conf.Body(),
			},
		}
		bodyTexts = append(bodyTexts, protoText)
	}
	return bodyTexts
}

// GetHtmlLinkForDeeplink converts deeplink to url (fi://screen?data=CJ8G) which can be used as html anchor tag in Fi application
func GetHtmlLinkForDeeplink(dl *deeplink.Deeplink) (string, error) {
	encodedStr, err := GetBase64EncodedDeeplink(dl)
	if err != nil {
		return "", errors.Wrapf(err, "failed to get base64 encoded deeplink")
	}

	return fmt.Sprintf("fi://screen?data=%s", encodedStr), nil
}

func GetBase64EncodedDeeplink(dl *deeplink.Deeplink) (string, error) {
	marshalledProto, err := proto.Marshal(dl)
	if err != nil {
		return "", errors.Wrapf(err, "failed to marshal deeplink")
	}

	encodedStr := base64.StdEncoding.EncodeToString(marshalledProto)

	return encodedStr, nil
}

// TODO: change the domain to fi.money in order to push in prod
// Converts deeplink to url of type https://web.uat.pointz.in/app-redirect/screen?data=${}&login_required=false
// These URLs redirect you to the Fi App using the data parameter
// GetHtmlLinkForDeeplink redirects user directly to the Fi App and requires user to be logged in.
// GetHtmlDeeplinkWithoutLoginRequired redirects user to the fi.money/app-redirect and then to the Fi app irrespective of the login state.
// Note: These links don't have properties like defer and analytics. We use onelink for that.
func GetHtmlDeeplinkWithoutLogin(dl *deeplink.Deeplink) (string, error) {
	marshalledProto, err := proto.Marshal(dl)
	if err != nil {
		return "", errors.Wrapf(err, "failed to marshall deeplink")
	}

	encodedStr := base64.StdEncoding.EncodeToString(marshalledProto)

	return fmt.Sprintf("https://fi.money/app-redirect?data=%s&login_required=%s", encodedStr, "false"), nil
}

// GenerateDeeplinkBase64 generates a base64-encoded deeplink from a Deeplink proto message.
func GenerateDeeplinkBase64(d *deeplink.Deeplink) string {
	marshalledProto, err := proto.Marshal(d)
	if err != nil {
		return ""
	}
	base64Str := base64.StdEncoding.EncodeToString(marshalledProto)
	return base64Str
}

// Deprecated: Android app handles PDF deeplink redirection in the client itself.
// We can directly use deeplink.Screen_WEB_PAGE for PDF deeplink redirection on Android & iOS.
func GetDeeplinkForPdf(appPlatform commontypes.Platform, title string, urlString string) *deeplink.Deeplink {
	if appPlatform == commontypes.Platform_ANDROID {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_EXTERNAL_REDIRECTION,
			ScreenOptions: &deeplink.Deeplink_ExternalRedirectionScreenOptions{
				ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
					ExternalUrl: urlString,
				},
			},
		}
	} else {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_WEB_PAGE,
			ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
				WebPageScreenOptions: &deeplink.WebpageScreenOptions{
					WebpageTitle: title,
					WebpageUrl:   urlString,
				},
			},
		}
	}
}
