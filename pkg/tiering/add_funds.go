package tiering

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/tiering"
	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	beTieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	typesPb "github.com/epifi/gamma/api/typesv2"
	payScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	pkgErrors "github.com/epifi/gamma/pkg/tiering/errors"
)

var beOrderUiEntryPointToTieringProvenance = map[orderPb.UIEntryPoint]beTieringEnumPb.Provenance{
	orderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_PLUS:     beTieringEnumPb.Provenance_PROVENANCE_INTENT_UPGRADE_ALL_PLANS_JOIN_PLUS,
	orderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_INFINITE: beTieringEnumPb.Provenance_PROVENANCE_INTENT_UPGRADE_ALL_PLANS_JOIN_INFINITE,
	orderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_PRIME:    beTieringEnumPb.Provenance_PROVENANCE_INTENT_UPGRADE_ALL_PLANS_JOIN_PRIME,
}

// GetTieringProvenanceForAddFunds computes the tiering provenance based on the entry point and trial details response.
// if user is eligible for trial then it returns the trial provenance for the eligible tier.
func GetTieringProvenanceForAddFunds(entryPoint orderPb.UIEntryPoint, trialDetailsResp *tiering.GetTrialDetailsResponse) beTieringEnumPb.Provenance {
	// override ALL_PLANS entry points with trial provenance if user is eligible for trial
	// this is a hack to enable trials without making a client change
	// We should ideally create a new orderPb.UIEntryPoint for trials and use that from the flow where the add funds is initiated
	if trialDetailsResp.GetIsEligibleForTrial() {
		eligibleTrialTier := trialDetailsResp.GetEligibleTrialTier()
		switch {
		case entryPoint == orderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_PLUS && eligibleTrialTier == tieringExtPb.Tier_TIER_FI_PLUS:
			return GetTrialProvenanceForTier(tieringExtPb.Tier_TIER_FI_PLUS)
		case entryPoint == orderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_INFINITE && eligibleTrialTier == tieringExtPb.Tier_TIER_FI_INFINITE:
			return GetTrialProvenanceForTier(tieringExtPb.Tier_TIER_FI_INFINITE)
		case entryPoint == orderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_PRIME && eligibleTrialTier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
			return GetTrialProvenanceForTier(tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3)
		}
	}

	provenance, ok := beOrderUiEntryPointToTieringProvenance[entryPoint]
	if !ok {
		return beTieringEnumPb.Provenance_PROVENANCE_ON_APP_AUTO_UPGRADE
	}

	return provenance
}

// GetCtaDeeplinkForTierAddFunds returns a deeplink for payment options if the amount is non-zero and isTieringPitchInPaymentOptionsEnabled deeplink is enabled.
// If not, it returns the deeplink for Add Funds screen
func GetCtaDeeplinkForTierAddFunds(ctx context.Context, tier tieringExtPb.Tier, amount *gmoney.Money, actorId string, isTieringPitchInPaymentOptionsEnabled bool) *deeplinkPb.Deeplink {
	switch {
	case isTieringPitchInPaymentOptionsEnabled && amount != nil && amount.GetUnits() != 0:
		dl, err := GetDeeplinkForPaymentOptions(tier, amount, actorId)
		if err != nil {
			logger.Error(ctx, "Error while creating payment options deeplink", zap.Error(err), zap.String(logger.TIER, tier.String()))
			dl = GetDeeplinkForAddFunds(tier, amount)
		}
		return dl
	default:
		return GetDeeplinkForAddFunds(tier, amount)
	}
}

func GetDeeplinkForAddFunds(feTier tieringExtPb.Tier, optionalCustomAmount *gmoney.Money) *deeplinkPb.Deeplink {
	var customAmount *typesPb.Money
	if optionalCustomAmount != nil && !money.IsZero(optionalCustomAmount) {
		customAmount = typesPb.GetFromBeMoney(optionalCustomAmount)
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TRANSFER_IN,
		ScreenOptions: &deeplinkPb.Deeplink_TransferInScreenOptions{
			TransferInScreenOptions: &deeplinkPb.TransferInScreenOptions{
				CustomAmount: customAmount,
				UiEntryPoint: getUiEntryPointString(feTier),
			},
		},
	}
}

func GetDeeplinkForPaymentOptions(feTier tieringExtPb.Tier, amount *gmoney.Money, actorId string) (*deeplinkPb.Deeplink, error) {
	transactionUIEntryPoint := getTransactionUIEntryPoint(feTier)

	screenOptions := &payScreenOptions.PaymentOptionsScreenOptions{
		Amount:                  typesPb.GetFromBeMoney(amount),
		TransactionUiEntryPoint: transactionUIEntryPoint,
		OrderedPaymentOptionTypes: []feTransactionPb.PaymentOptionType{
			feTransactionPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP,
		},
		ActorTo: actorId,
	}

	paymentOptionsDeeplink, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PAYMENT_OPTIONS_FULL_SCREEN, screenOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to construct payment options deeplink: %w", err)
	}

	return paymentOptionsDeeplink, nil
}

func getUiEntryPointString(feTier tieringExtPb.Tier) string {
	switch feTier {
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PLUS.String()
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_INFINITE.String()
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PRIME.String()
	default:
		return ""
	}
}

func getTransactionUIEntryPoint(feTier tieringExtPb.Tier) timeline.TransactionUIEntryPoint {
	switch feTier {
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PLUS
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_INFINITE
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PRIME
	default:
		return timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED
	}
}

// CalculateAmountNeededForTier calculates the amount needed for a user to reach the target tier
// based on the minimum balance requirement and current account balance
func CalculateAmountNeededForTier(targetTier tieringExtPb.Tier, mmtDetails []*tieringExtPb.MovementExternalDetails, currentBalance *gmoney.Money) (*gmoney.Money, error) {
	// Standard tier doesn't have minimum balance requirements, so return nil
	if targetTier == tieringExtPb.Tier_TIER_FI_BASIC {
		return nil, nil
	}

	// 1. Try to validate the current balance and min balance required for the target tier
	minBalance, err := GetMinBalanceFromTierOptions(targetTier, mmtDetails)
	if err != nil {
		return nil, fmt.Errorf("failed to get min balance for tier %s: %w", targetTier.String(), err)
	}

	if minBalance == nil {
		return nil, fmt.Errorf("no min balance found for tier %s", targetTier.String())
	}

	if currentBalance == nil {
		return nil, fmt.Errorf("current balance not found")
	}

	// 2. If current balance greater than or equal to min balance required return zero
	isGreater, err := money.IsGreaterThanOrEqual(currentBalance, minBalance)
	if err != nil {
		return nil, fmt.Errorf("failed to compare balances: %w", err)
	}

	if isGreater {
		return money.ZeroINR().Pb, nil
	}

	// 3. Return amount diff for target tier
	amountNeeded, err := money.Subtract(minBalance, currentBalance)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate amount needed: %w", err)
	}

	return amountNeeded, nil
}

// GetMinBalanceFromTierOptions
// if minBalance criteria is not present, it will return tieringErrors.ErrTierHasNoMinBalanceCriteria
func GetMinBalanceFromTierOptions(tier tieringExtPb.Tier, mmtDetails []*tieringExtPb.MovementExternalDetails) (*gmoney.Money, error) {
	var tierOptions []*tieringCriteriaPb.Option
	for _, mmtDetail := range mmtDetails {
		if tier == mmtDetail.GetTierName() {
			tierOptions = mmtDetail.GetOptions()
			break
		}
	}
	// If multiple options present for now taking the minBalance for latest option
	// TODO(sainath): change this in future
	for _, option := range tierOptions {
		for _, action := range option.GetActions() {
			actionCriteria := action.GetActionDetails().GetCriteria()
			switch actionCriteria := actionCriteria.(type) {
			case *tieringCriteriaPb.QualifyingCriteria_Balance:
				return actionCriteria.Balance.GetMinBalance(), nil
			case *tieringCriteriaPb.QualifyingCriteria_BalanceV2:
				return actionCriteria.BalanceV2.GetMinBalanceForUpgrade(), nil
			}
		}
	}
	return nil, pkgErrors.ErrTierHasNoMinBalanceCriteria
}
