package tiering

import (
	"fmt"

	gmoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	pkgErrors "github.com/epifi/gamma/pkg/tiering/errors"
)

func GetTrialProvenanceForTier(tier tieringExtPb.Tier) tieringEnumPb.Provenance {
	switch tier {
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return tieringEnumPb.Provenance_PROVENANCE_OPT_IN_FOR_PRIME_TRIAL
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return tieringEnumPb.Provenance_PROVENANCE_OPT_IN_FOR_INFINITE_TRIAL
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return tieringEnumPb.Provenance_PROVENANCE_OPT_IN_FOR_PLUS_TRIAL
	default:
		return tieringEnumPb.Provenance_PROVENANCE_UNSPECIFIED
	}
}

// GetTrialThresholdFromTieringPitch extracts the trial threshold for given tier from the tiering pitch response
func GetTrialThresholdFromTieringPitch(targetTier tieringExtPb.Tier, tieringPitchResp *tiering.GetTieringPitchV2Response) (*gmoney.Money, error) {
	if tieringPitchResp == nil {
		return nil, fmt.Errorf("tiering pitch response is nil")
	}

	// For other tiers, extract the criteria values from the movement details
	mmtDetails := tieringPitchResp.GetMovementDetailsList()
	for _, detail := range mmtDetails {
		if detail.GetTierName() != targetTier {
			continue
		}

		criteriaMinValues, err := GetAllCriteriaMinValuesFromOptions(detail.GetOptions())
		if err != nil {
			continue // Try the next detail if this one has no criteria
		}

		// Find the trial balance criteria
		for _, criteriaValue := range criteriaMinValues {
			if criteriaValue.Criteria == tieringEnumPb.CriteriaOptionType_BALANCE_TRIAL_AND_KYC && criteriaValue.MinValue != nil {
				return criteriaValue.MinValue, nil
			}
		}
	}

	return nil, pkgErrors.ErrTierHasNoTrialBalanceCriteria
}
