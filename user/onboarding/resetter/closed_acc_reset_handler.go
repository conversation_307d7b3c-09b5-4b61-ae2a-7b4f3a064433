package resetter

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	consentPb "github.com/epifi/gamma/api/consent"
	employmentPb "github.com/epifi/gamma/api/employment"
	omeglePb "github.com/epifi/gamma/api/omegle"
	productPb "github.com/epifi/gamma/api/product"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	usersPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	onbDao "github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
)

type ClosedSavingsAccountResetHandler struct {
	onbDao           onbDao.OnboardingDao
	consentClient    consentPb.ConsentClient
	userClient       usersPb.UsersClient
	savingsClient    savingsPb.SavingsClient
	bcClient         bankCustomerPb.BankCustomerServiceClient
	actorClient      actorPb.ActorClient
	authClient       authPb.AuthClient
	userProcessor    helper.UserProcessor
	opStatusClient   operationalStatusPb.OperationalStatusServiceClient
	employmentClient employmentPb.EmploymentClient
	productClient    productPb.ProductClient
	omegleClient     omeglePb.OmegleClient
}

func NewClosedSavingsAccountResetHandler(onbDao onbDao.OnboardingDao, consentClient consentPb.ConsentClient,
	userClient usersPb.UsersClient, savingsClient savingsPb.SavingsClient, bcClient bankCustomerPb.BankCustomerServiceClient,
	actorClient actorPb.ActorClient, authClient authPb.AuthClient,
	userProcessor helper.UserProcessor, opStatusClient operationalStatusPb.OperationalStatusServiceClient,
	employmentClient employmentPb.EmploymentClient, productClient productPb.ProductClient, omegleClient omeglePb.OmegleClient) *ClosedSavingsAccountResetHandler {
	return &ClosedSavingsAccountResetHandler{
		onbDao:           onbDao,
		consentClient:    consentClient,
		userClient:       userClient,
		savingsClient:    savingsClient,
		bcClient:         bcClient,
		actorClient:      actorClient,
		authClient:       authClient,
		userProcessor:    userProcessor,
		opStatusClient:   opStatusClient,
		employmentClient: employmentClient,
		productClient:    productClient,
		omegleClient:     omegleClient,
	}
}

// IsResetAllowed checks the below list for closed account deletion. If any of the below conditions are satisfied, it returns PermissionDenied
// 1. User has active products
// 2. Access revoked by risk
// 3. Savings account is not closed
// 4. Pan linked to the current account already has a deleted closed savings account
// 4. Closed due to MIN KYC expiry and has balance > 0
// 5. No user consent for account deletion (must be the last condition as it is used to drive UI)
func (s *ClosedSavingsAccountResetHandler) IsResetAllowed(ctx context.Context, req *IsResetAllowedRequest) error {
	onbDetails, err := s.onbDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			logger.Info(ctx, "record not found")
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in getting onboarding details", zap.Error(err))
		return err
	}

	if err = CheckNonResidentUser(ctx, s.userProcessor, s.omegleClient, onbDetails); err != nil {
		return err
	}

	if err = s.checkActiveProducts(ctx, onbDetails.GetActorId()); err != nil {
		return err
	}

	user, err := s.getUser(ctx, onbDetails.GetUserId())
	if err != nil {
		return err
	}

	if err = s.checkAccessRevokeReason(ctx, user); err != nil {
		return err
	}

	savingsAccount, err := s.getSavingsAccount(ctx, onbDetails.GetActorId())
	if err != nil {
		return err
	}
	if err = s.checkSavingsAccountState(ctx, savingsAccount.GetId()); err != nil {
		return err
	}

	if err = s.checkPanLinkedToClosedAccount(ctx, user.GetProfile().GetPAN()); err != nil {
		return err
	}

	if err = s.checkAccountBalance(ctx, savingsAccount.GetId()); err != nil {
		return err
	}
	// check for consent
	// this must be the last step as we ask the user for consent only if he is eligible
	if err = s.checkDeletionConsent(ctx, req.GetActorId()); err != nil {
		if errors.Is(epifierrors.ErrRecordNotFound, err) {
			return rpc.StatusAsError(rpc.NewStatus(uint32(onbPb.ResetUserResponse_MISSING_CONSENT), "", ""))
		}
		logger.Error(ctx, "error in checking consent", zap.Error(err))
		return err
	}
	return nil
}

func (s *ClosedSavingsAccountResetHandler) DeleteUserDetails(ctx context.Context, req *DeleteUserDetailsRequest) error {
	var (
		user *userPb.User
	)
	onb, err := s.onbDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			logger.Info(ctx, "record not found")
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in getting onboarding details", zap.Error(err))
		return err
	}
	if onb.GetUserId() == "" {
		logger.Info(ctx, "missing userId in onboarding details")
		return errors.New("missing userId in onboarding details")
	}

	if stageStatus(onb, onbPb.OnboardingStage_DEVICE_REGISTRATION).IsSuccessOrSkipped() {
		if errDeReg := s.permanentDeregisterDevice(ctx, onb.GetActorId()); errDeReg != nil {
			return errDeReg
		}
	}
	// get user
	user, err = s.getUser(ctx, onb.GetUserId())
	if err != nil {
		return err
	}

	// user not already deleted
	if user != nil {
		if err = s.deleteTokens(ctx, user); err != nil {
			return err
		}

		if err = deleteEmploymentData(ctx, s.employmentClient, req.GetActorId()); err != nil {
			return err
		}

		if err = s.deleteBankCustomer(ctx, onb.GetActorId(), onb.GetVendor()); err != nil {
			return err
		}

		if err = s.deleteUser(ctx, onb.GetUserId(), req.GetDeletionDetails()); err != nil {
			return err
		}
	}
	return nil
}

func (s *ClosedSavingsAccountResetHandler) checkDeletionConsent(ctx context.Context, actorId string) error {
	consentRes, err := s.consentClient.FetchConsent(ctx, &consentPb.FetchConsentRequest{
		ActorId:     actorId,
		ConsentType: consentPb.ConsentType_FI_CLOSED_SAVINGS_ACCOUNT_DELETION,
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(consentRes, err); rpcErr != nil {
		if consentRes.GetStatus().IsRecordNotFound() {
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error in getting user", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (s *ClosedSavingsAccountResetHandler) checkActiveProducts(ctx context.Context, actorId string) error {
	ok, err := IsActiveProductUser(ctx, s.productClient, actorId)
	if err != nil {
		return err
	}
	if ok {
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

func (s *ClosedSavingsAccountResetHandler) checkAccessRevokeReason(ctx context.Context, user *usersPb.User) error {
	reason := user.GetAccessRevokeDetails().GetReason()
	if reason.IsAccessRevokeReasonFraudulent() || reason == userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_OTHER {
		logger.Info(ctx, fmt.Sprintf("permission denied as access revoked due to %v", reason))
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

func (s *ClosedSavingsAccountResetHandler) checkSavingsAccountState(ctx context.Context, savingsAccountId string) error {
	opStatusRes, err := s.opStatusClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
		DataFreshness: operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
		AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: savingsAccountId,
		},
	})
	if rpcErr := epifigrpc.RPCError(opStatusRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting operational status", zap.Error(rpcErr))
		return rpcErr
	}
	accountOpStatus := opStatusRes.GetOperationalStatusInfo().GetOperationalStatus()
	if accountOpStatus != enums.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
		logger.Info(ctx, fmt.Sprintf("permission denied as account operational status is %v", accountOpStatus))
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

func (s *ClosedSavingsAccountResetHandler) checkAccountBalance(ctx context.Context, savingsAccountId string) error {
	cbtResp, err := s.savingsClient.GetClosedAccountBalTransferData(ctx, &savingsPb.GetClosedAccountBalTransferDataRequest{
		SavingsAccountId: savingsAccountId,
	})
	if rpcErr := epifigrpc.RPCError(cbtResp, err); rpcErr != nil {
		if !cbtResp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error while getting closed accounts balance transfer data", zap.Error(rpcErr))
		}
		return rpcErr
	}
	cbt := getLatestClosedAccountInfo(cbtResp.GetEntries())

	if cbt.HasClosedAccountZeroBalance() {
		return nil
	}
	logger.Info(ctx, "permission denied as user has balance left")
	return epifierrors.ErrPermissionDenied
}

func (s *ClosedSavingsAccountResetHandler) checkPanLinkedToClosedAccount(ctx context.Context, pan string) error {
	deletedUsers, err := s.userProcessor.GetDeletedUsersLinkedToPan(ctx, pan, usersPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT)
	if err != nil {
		logger.Error(ctx, "error in getting users linked to pan", zap.Error(err))
		return err
	}
	// account has already been deleted for reopening, not allowing to delete again
	if len(deletedUsers) > 0 {
		logger.Info(ctx, "permission denied as user pan is already linked to a closed account")
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

func getLatestClosedAccountInfo(cbts []*savingsPb.ClosedAccountBalanceTransfer) *savingsPb.ClosedAccountBalanceTransfer {
	if len(cbts) == 0 {
		return nil
	}
	sort.Slice(cbts, func(i, j int) bool {
		return cbts[i].GetUpdatedAt().AsTime().After(cbts[j].GetUpdatedAt().AsTime())
	})
	return cbts[0]
}

func (s *ClosedSavingsAccountResetHandler) getUser(ctx context.Context, userId string) (*userPb.User, error) {
	userRes, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: userId,
		},
	})
	if err = epifigrpc.RPCError(userRes, err); err != nil && !rpc.StatusFromError(err).IsRecordNotFound() {
		return nil, err
	}
	return userRes.GetUser(), nil
}

func (s *ClosedSavingsAccountResetHandler) getSavingsAccount(ctx context.Context, actorId string) (*savingsPb.SavingsAccountEssentials, error) {
	savResp, err := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(savResp, err); grpcErr != nil {
		logger.Error(ctx, "error fetching savings account for actor", zap.Error(grpcErr))
		return nil, grpcErr
	}
	return savResp.GetAccount(), nil
}

func (s *ClosedSavingsAccountResetHandler) permanentDeregisterDevice(ctx context.Context, actorId string) error {
	deActResp, errDeAct := s.authClient.DeactivateDevice(ctx, &authPb.DeactivateDeviceRequest{
		ActorId:          actorId,
		DeactivationType: authPb.DeactivateDeviceRequest_DEACTIVATION_TYPE_PERMANENT,
	})

	return epifigrpc.RPCError(deActResp, errDeAct)
}

func (s *ClosedSavingsAccountResetHandler) deleteTokens(ctx context.Context, user *userPb.User) error {
	tokenTypes := []authPb.TokenType{
		authPb.TokenType_ACCESS_TOKEN,
		authPb.TokenType_REFRESH_TOKEN,
	}
	delAuthToken, err := s.authClient.UpdateToken(ctx, &authPb.UpdateTokenRequest{
		Status: authPb.UpdateTokenRequest_DELETE,
		Identifier: &authPb.UpdateTokenRequest_PhoneNumber{
			PhoneNumber: user.GetProfile().GetPhoneNumber(),
		},
		TokenTypes:          tokenTypes,
		TokenUpdationReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_RESET_USER,
	})
	if err = epifigrpc.RPCError(delAuthToken, err); err != nil && !delAuthToken.GetStatus().IsRecordNotFound() {
		return fmt.Errorf("error in delete auth tokens: %v", err)
	}
	return nil
}

func (s *ClosedSavingsAccountResetHandler) deleteBankCustomer(ctx context.Context, actorId string, vendor commonvgpb.Vendor) error {
	delBankCustomer, err := s.bcClient.DeleteBankCustomer(ctx, &bankCustomerPb.DeleteBankCustomerRequest{
		ActorId: actorId,
		Vendor:  vendor,
	})
	if rpcErr := epifigrpc.RPCError(delBankCustomer, err); rpcErr != nil {
		if !delBankCustomer.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in deleting bank customer", zap.Error(rpcErr))
			return rpcErr
		}
		logger.Info(ctx, "bank customer record not found")
	}
	return nil
}

func (s *ClosedSavingsAccountResetHandler) deleteUser(ctx context.Context, userId string, deletionDetails *userPb.DeletionDetails) error {
	delUser, err := s.userClient.DeleteUser(ctx, &userPb.DeleteUserRequest{
		UserId:          userId,
		DeletionDetails: deletionDetails,
	})
	if err = epifigrpc.RPCError(delUser, err); err != nil {
		logger.Error(ctx, "error in deleting user", zap.Error(err))
		return err
	}
	return nil
}
