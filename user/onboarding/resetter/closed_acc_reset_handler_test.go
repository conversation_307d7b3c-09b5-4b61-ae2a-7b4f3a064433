// nolint: govet
package resetter

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	"github.com/stretchr/testify/assert"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/accounts/enums"
	opStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	authPb "github.com/epifi/gamma/api/auth"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	consentPb "github.com/epifi/gamma/api/consent"
	employmentPb "github.com/epifi/gamma/api/employment"
	productPb "github.com/epifi/gamma/api/product"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	userHelper "github.com/epifi/gamma/user/onboarding/helper/user"
)

var (
	// user details whose account is closed due to min KYC
	actorId2 = "actor-id-2"
	user2    = &userPb.User{
		Id: "user-id-2",
		Profile: &userPb.Profile{
			PhoneNumber: dummyPhoneNumber,
			PAN:         dummyPan,
		},
		AccessRevokeDetails: &userPb.AccessRevokeDetails{
			AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED,
			Reason:             userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY,
		},
	}
	riskRevokedUser = &userPb.User{
		Id: user2.GetId(),
		Profile: &userPb.Profile{
			PhoneNumber: dummyPhoneNumber,
			PAN:         dummyPan,
		},
		ActorId: actorId2,
		AccessRevokeDetails: &userPb.AccessRevokeDetails{
			AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED,
			Reason:             userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS,
		},
	}
	onbDetails2 = &onbPb.OnboardingDetails{
		ActorId:      actorId2,
		OnboardingId: "onb-id",
		UserId:       user2.GetId(),
	}
	onbDetailsFiLite = &onbPb.OnboardingDetails{
		UserId:  user2.GetId(),
		ActorId: actorId2,
		FiLiteDetails: &onbPb.FiLiteDetails{
			AccessibilityEnabledAt: timestamp.Now(),
			IsEnabled:              commontypes.BooleanEnum_TRUE,
		},
	}
	onbDetailsMissingUserId = &onbPb.OnboardingDetails{
		ActorId:      actorId1,
		OnboardingId: "onb-id",
	}

	savAccount2 = &savingsPb.Account{
		Id: "savAccId2",
	}
	essentialSavAccount = &savingsPb.SavingsAccountEssentials{
		Id: "savAccId2",
	}
	closedAccOpStatusInfo = &opStatusPb.OperationalStatusInfo{
		OperationalStatus: enums.OperationalStatus_OPERATIONAL_STATUS_CLOSED,
	}
	activeAccOpStatusInfo = &opStatusPb.OperationalStatusInfo{
		OperationalStatus: enums.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
	}
	deletedUser1 = &userPb.User{
		Profile: &userPb.Profile{
			PAN: dummyPan,
		},
		DeletionDetails: &userPb.DeletionDetails{
			DeletionReason: userPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT,
		},
	}
)

func TestClosedSavingsAccountResetHandler_IsResetAllowed(t *testing.T) {
	t.Parallel()
	latestCbt := &savingsPb.ClosedAccountBalanceTransfer{
		TransactionStatus: savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS,
		UpdatedAt:         timestamp.New(time.Now()),
	}
	latestCb2 := &savingsPb.ClosedAccountBalanceTransfer{
		TransactionStatus: savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_UNSPECIFIED,
		UpdatedAt:         timestamp.New(time.Now()),
		LastKnownBalance: &savingsPb.BalanceFromPartener{
			AvailableBalance: &moneyPb.Money{
				CurrencyCode: "INR",
			},
		},
	}
	oldCbt := &savingsPb.ClosedAccountBalanceTransfer{
		TransactionStatus: savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS,
		UpdatedAt:         timestamp.New(time.Now().Add(-24 * time.Hour)),
	}
	inactiveStatusProductInfoMap := make(map[string]*productPb.ProductInfo, 0)
	for _, productType := range GetProductTypesForSAReset() {
		if productType == productPb.ProductType_PRODUCT_TYPE_UNSPECIFIED {
			continue
		}
		inactiveStatusProductInfoMap[productType.String()] = &productPb.ProductInfo{
			ProductStatus: productPb.ProductStatus_PRODUCT_STATUS_INACTIVE,
		}
	}
	tpapActiveProductInfoMap := deepcopy.Copy(inactiveStatusProductInfoMap).(map[string]*productPb.ProductInfo)
	tpapActiveProductInfoMap[productPb.ProductType_PRODUCT_TYPE_CREDIT_CARD.String()] = &productPb.ProductInfo{
		ProductStatus: productPb.ProductStatus_PRODUCT_STATUS_ACTIVE,
	}
	type args struct {
		req *IsResetAllowedRequest
	}
	tests := []struct {
		name     string
		args     args
		wantErr  error
		wantMock func(args args, md *mockedDependencies)
	}{
		{
			name: "error in get user",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetailsFiLite, nil)
				onbDetailsLocal := onbDetailsFiLite
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: rpc.StatusAsError(rpc.StatusInternal()),
		},
		{
			name: "user has active products",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetailsFiLite, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: tpapActiveProductInfoMap,
				}, nil)
			},
			wantErr: epifierrors.ErrPermissionDenied,
		},
		{
			name: "risk revoked user",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: riskRevokedUser.GetActorId(),
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   riskRevokedUser,
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: epifierrors.ErrPermissionDenied,
		},
		{
			name: "error in getting user",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: rpc.StatusAsError(rpc.StatusInternal()),
		},
		{
			name: "error in getting savings account",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(nil, errors.New("error in getting savings account"))
			},
			wantErr: errors.New("error in getting savings account"),
		},
		{
			name: "error in getting operational status",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: essentialSavAccount,
				}, nil)
				md.opStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), &opStatusPb.GetOperationalStatusRequest{
					DataFreshness: opStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
					AccountIdentifier: &opStatusPb.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: savAccount2.GetId(),
					},
				}).Return(nil, errors.New("error in getting operational status"))
			},
			wantErr: errors.New("error in getting operational status"),
		},
		{
			name: "active savings account, permission denied",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: essentialSavAccount,
				}, nil)
				md.opStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), &opStatusPb.GetOperationalStatusRequest{
					DataFreshness: opStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
					AccountIdentifier: &opStatusPb.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: savAccount2.GetId(),
					},
				}).Return(&opStatusPb.GetOperationalStatusResponse{
					Status:                rpc.StatusOk(),
					OperationalStatusInfo: activeAccOpStatusInfo,
				}, nil)
			},
			wantErr: epifierrors.ErrPermissionDenied,
		},
		{
			name: "error in fetching deleted accounts linked to pan",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: essentialSavAccount,
				}, nil)
				md.opStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), &opStatusPb.GetOperationalStatusRequest{
					DataFreshness: opStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
					AccountIdentifier: &opStatusPb.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: savAccount2.GetId(),
					},
				}).Return(&opStatusPb.GetOperationalStatusResponse{
					Status:                rpc.StatusOk(),
					OperationalStatusInfo: closedAccOpStatusInfo,
				}, nil)
				md.mockUserProc.EXPECT().GetDeletedUsersLinkedToPan(gomock.Any(), user2.GetProfile().GetPAN(), userPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT).Return(nil, errors.New("error in getting unscoped users linked to PAN"))
			},
			wantErr: errors.New("error in getting unscoped users linked to PAN"),
		},
		{
			name: "pan already linked to a deleted closed account",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: essentialSavAccount,
				}, nil)
				md.opStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), &opStatusPb.GetOperationalStatusRequest{
					DataFreshness: opStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
					AccountIdentifier: &opStatusPb.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: savAccount2.GetId(),
					},
				}).Return(&opStatusPb.GetOperationalStatusResponse{
					Status:                rpc.StatusOk(),
					OperationalStatusInfo: closedAccOpStatusInfo,
				}, nil)
				md.mockUserProc.EXPECT().GetDeletedUsersLinkedToPan(gomock.Any(), user2.GetProfile().GetPAN(), userPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT).Return([]*userPb.User{
					deletedUser1,
				}, nil)
			},
			wantErr: epifierrors.ErrPermissionDenied,
		},
		{
			name: "minimum balance left",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: essentialSavAccount,
				}, nil)
				md.opStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), &opStatusPb.GetOperationalStatusRequest{
					DataFreshness: opStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
					AccountIdentifier: &opStatusPb.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: savAccount2.GetId(),
					},
				}).Return(&opStatusPb.GetOperationalStatusResponse{
					Status:                rpc.StatusOk(),
					OperationalStatusInfo: closedAccOpStatusInfo,
				}, nil)
				md.mockUserProc.EXPECT().GetDeletedUsersLinkedToPan(gomock.Any(), user2.GetProfile().GetPAN(), userPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT).Return(nil, nil)
				md.mockSavClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savAccount2.GetId(),
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{
						{
							ReportedClosureBalance: &moneyPb.Money{
								Units: 2,
							},
							UpdatedAt: timestamp.New(time.Now()),
						},
					},
				}, nil)
			},
			wantErr: epifierrors.ErrPermissionDenied,
		},
		{
			name: "consent not available",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: essentialSavAccount,
				}, nil)
				md.opStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), &opStatusPb.GetOperationalStatusRequest{
					DataFreshness: opStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
					AccountIdentifier: &opStatusPb.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: savAccount2.GetId(),
					},
				}).Return(&opStatusPb.GetOperationalStatusResponse{
					Status:                rpc.StatusOk(),
					OperationalStatusInfo: closedAccOpStatusInfo,
				}, nil)
				md.mockUserProc.EXPECT().GetDeletedUsersLinkedToPan(gomock.Any(), user2.GetProfile().GetPAN(), userPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT).Return(nil, nil)
				md.mockSavClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savAccount2.GetId(),
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{
						latestCbt,
						oldCbt,
					},
				}, nil)
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ActorId:     onbDetailsLocal.GetActorId(),
					ConsentType: consentPb.ConsentType_FI_CLOSED_SAVINGS_ACCOUNT_DELETION,
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consentPb.FetchConsentResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			wantErr: rpc.StatusAsError(rpc.NewStatus(uint32(onbPb.ResetUserResponse_MISSING_CONSENT), "", "")),
		},
		{
			name: "success",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: essentialSavAccount,
				}, nil)
				md.opStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), &opStatusPb.GetOperationalStatusRequest{
					DataFreshness: opStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
					AccountIdentifier: &opStatusPb.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: savAccount2.GetId(),
					},
				}).Return(&opStatusPb.GetOperationalStatusResponse{
					Status:                rpc.StatusOk(),
					OperationalStatusInfo: closedAccOpStatusInfo,
				}, nil)
				md.mockUserProc.EXPECT().GetDeletedUsersLinkedToPan(gomock.Any(), user2.GetProfile().GetPAN(), userPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT).Return(nil, nil)
				md.mockSavClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savAccount2.GetId(),
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{
						latestCbt,
						oldCbt,
					},
				}, nil)
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ActorId:     onbDetailsLocal.GetActorId(),
					ConsentType: consentPb.ConsentType_FI_CLOSED_SAVINGS_ACCOUNT_DELETION,
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consentPb.FetchConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: nil,
		},
		{
			name: "success: reported available balance is null",
			args: args{
				req: &IsResetAllowedRequest{
					ActorId: actorId2,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails2, nil)
				onbDetailsLocal := onbDetails2
				md.mockUserProc.EXPECT().IsNonResidentUser(gomock.Any(), args.req.GetActorId()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil)
				md.productClient.EXPECT().GetProductsStatus(gomock.Any(), gomock.Any()).Return(&productPb.GetProductsStatusResponse{
					Status:         rpc.StatusOk(),
					ProductInfoMap: inactiveStatusProductInfoMap,
				}, nil)
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user2,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockSavClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     onbDetails2.ActorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: essentialSavAccount,
				}, nil)
				md.opStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), &opStatusPb.GetOperationalStatusRequest{
					DataFreshness: opStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_RECENT,
					AccountIdentifier: &opStatusPb.GetOperationalStatusRequest_SavingsAccountId{
						SavingsAccountId: savAccount2.GetId(),
					},
				}).Return(&opStatusPb.GetOperationalStatusResponse{
					Status:                rpc.StatusOk(),
					OperationalStatusInfo: closedAccOpStatusInfo,
				}, nil)
				md.mockUserProc.EXPECT().GetDeletedUsersLinkedToPan(gomock.Any(), user2.GetProfile().GetPAN(), userPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT).Return(nil, nil)
				md.mockSavClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savAccount2.GetId(),
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{
						latestCb2,
						oldCbt,
					},
				}, nil)
				md.consentClient.EXPECT().FetchConsent(gomock.Any(), &consentPb.FetchConsentRequest{
					ActorId:     onbDetailsLocal.GetActorId(),
					ConsentType: consentPb.ConsentType_FI_CLOSED_SAVINGS_ACCOUNT_DELETION,
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consentPb.FetchConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := initClosedAccResetHandler(t)
			if tt.wantMock != nil {
				tt.wantMock(tt.args, md)
			}
			err := s.IsResetAllowed(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
		})
	}
}

func TestClosedSavingsAccountResetHandler_DeleteUserDetails(t *testing.T) {
	t.Parallel()
	type args struct {
		req *DeleteUserDetailsRequest
	}
	tests := []struct {
		name     string
		args     args
		wantErr  error
		wantMock func(args args, md *mockedDependencies)
	}{
		{
			name: "empty user ID",
			args: args{
				req: &DeleteUserDetailsRequest{
					ActorId: actorId1,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},

			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetailsMissingUserId, nil)
			},
			wantErr: errors.New("missing userId in onboarding details"),
		},
		{
			name: "get user internal error",
			args: args{
				req: &DeleteUserDetailsRequest{
					ActorId: actorId1,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},

			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails1, nil)
				onbDetailsLocal := onbDetails1
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			wantErr: rpc.StatusAsError(rpc.StatusInternal()),
		},
		{
			name: "delete token internal error",
			args: args{
				req: &DeleteUserDetailsRequest{
					ActorId: actorId1,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetails1, nil)
				onbDetailsLocal := onbDetails1
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user1,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockAuthClient.EXPECT().UpdateToken(gomock.Any(), &authPb.UpdateTokenRequest{
					Status: authPb.UpdateTokenRequest_DELETE,
					Identifier: &authPb.UpdateTokenRequest_PhoneNumber{
						PhoneNumber: user1.GetProfile().GetPhoneNumber(),
					},
					TokenTypes: []authPb.TokenType{
						authPb.TokenType_ACCESS_TOKEN,
						authPb.TokenType_REFRESH_TOKEN,
					},
					TokenUpdationReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_RESET_USER,
				}).Return(&authPb.UpdateTokenResponse{
					Status: rpc.StatusInternal(),
				}, nil)

			},
			wantErr: fmt.Errorf("error in delete auth tokens: %v", rpc.StatusAsError(rpc.StatusInternal())),
		},
		{
			name: "deactivate device permission denied",
			args: args{
				req: &DeleteUserDetailsRequest{
					ActorId: actorId1,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetailsDevRegSuccess, nil)
				onbDetailsLocal := onbDetailsDevRegSuccess
				md.mockAuthClient.EXPECT().DeactivateDevice(gomock.Any(), &authPb.DeactivateDeviceRequest{
					ActorId:          onbDetailsLocal.ActorId,
					DeactivationType: authPb.DeactivateDeviceRequest_DEACTIVATION_TYPE_PERMANENT,
				}).Return(&authPb.DeactivateDeviceResponse{
					Status: rpc.StatusPermissionDenied(),
				}, nil)
			},
			wantErr: rpc.StatusAsError(rpc.StatusPermissionDenied()),
		},
		{
			name: "success",
			args: args{
				req: &DeleteUserDetailsRequest{
					ActorId: actorId1,
					DeletionDetails: &userPb.DeletionDetails{
						DeletionReason: userPb.DeletionDetails_DELETION_REASON_AFU_BEFORE_DEV_REG,
					},
				},
			},
			wantMock: func(args args, md *mockedDependencies) {
				md.mockOnbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.req.GetActorId()).Return(onbDetailsDevRegSuccess, nil)
				onbDetailsLocal := onbDetailsDevRegSuccess
				md.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: onbDetailsLocal.GetUserId(),
					},
				}).Return(&userPb.GetUserResponse{
					User:   user1,
					Status: rpc.StatusOk(),
				}, nil)
				md.mockAuthClient.EXPECT().DeactivateDevice(gomock.Any(), &authPb.DeactivateDeviceRequest{
					ActorId:          onbDetailsLocal.ActorId,
					DeactivationType: authPb.DeactivateDeviceRequest_DEACTIVATION_TYPE_PERMANENT,
				}).Return(&authPb.DeactivateDeviceResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.mockAuthClient.EXPECT().UpdateToken(gomock.Any(), &authPb.UpdateTokenRequest{
					Status: authPb.UpdateTokenRequest_DELETE,
					Identifier: &authPb.UpdateTokenRequest_PhoneNumber{
						PhoneNumber: user1.GetProfile().GetPhoneNumber(),
					},
					TokenTypes: []authPb.TokenType{
						authPb.TokenType_ACCESS_TOKEN,
						authPb.TokenType_REFRESH_TOKEN,
					},
					TokenUpdationReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_RESET_USER,
				}).Return(&authPb.UpdateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.employmentClient.EXPECT().DeleteEmploymentData(gomock.Any(), &employmentPb.DeleteEmploymentDataRequest{
					ActorId: args.req.GetActorId(),
				}).Return(&employmentPb.DeleteEmploymentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.mockBankCustClient.EXPECT().DeleteBankCustomer(gomock.Any(), &bankCustomerPb.DeleteBankCustomerRequest{
					ActorId: onbDetailsLocal.GetActorId(),
					Vendor:  onbDetailsLocal.GetVendor(),
				}).Return(&bankCustomerPb.DeleteBankCustomerResponse{
					Status: rpc.StatusOk(),
				}, nil)
				md.mockUserClient.EXPECT().DeleteUser(gomock.Any(), &userPb.DeleteUserRequest{
					UserId:          onbDetailsLocal.GetUserId(),
					DeletionDetails: args.req.GetDeletionDetails(),
				}).Return(&userPb.DeleteUserResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			s, md := initClosedAccResetHandler(t)
			if tt.wantMock != nil {
				tt.wantMock(tt.args, md)
			}
			err := s.DeleteUserDetails(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
		})
	}
}
