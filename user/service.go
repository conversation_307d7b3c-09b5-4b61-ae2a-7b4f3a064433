package user

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/events"
	goutils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	leadsPb "github.com/epifi/gamma/api/leads"

	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	livPb "github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	kycPb "github.com/epifi/gamma/api/kyc"
	kycdocspb "github.com/epifi/gamma/api/kyc/docs"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/product"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/typesv2"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	pb "github.com/epifi/gamma/api/user"
	accessRevokeConsumerPb "github.com/epifi/gamma/api/user/accessrevoke/consumer"
	userEventPb "github.com/epifi/gamma/api/user/event"
	userGroup "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vgPbCustomer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer/dedupe"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/pkg/countrystdinfo"
	"github.com/epifi/gamma/pkg/email"
	"github.com/epifi/gamma/pkg/image"
	gammanames "github.com/epifi/gamma/pkg/names"
	"github.com/epifi/gamma/pkg/obfuscator"
	"github.com/epifi/gamma/pkg/onboarding"
	nriPkg "github.com/epifi/gamma/pkg/onboarding/nri"
	federalPkg "github.com/epifi/gamma/pkg/vendors/federal"
	"github.com/epifi/gamma/pkg/vendorstore"
	"github.com/epifi/gamma/user/config"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/dao"
	"github.com/epifi/gamma/user/dao/model"
	events2 "github.com/epifi/gamma/user/events"
	spdao "github.com/epifi/gamma/user/shipping_preference/dao"
	wireTypes "github.com/epifi/gamma/user/wire/types"
)

type Service struct {
	pb.UnimplementedUsersServer
	dao                                       dao.UserDao
	minUserDao                                dao.MinimalUserDao
	addressCodesDao                           dao.CKycAddressCodesDao
	vendorAreaCodesDao                        dao.VendorAreaCodesDao
	nomineeDao                                dao.NomineeDao
	shippingPrefDao                           spdao.ShippingPreferencesDao
	shippingReqDao                            spdao.ShippingPreferenceVendorRequestDao
	appInstanceIdsDao                         dao.AppInstanceIdentifierDao
	idGen                                     idgen.IdGenerator
	kycClient                                 kycPb.KycClient
	shippingProducer                          wireTypes.ShippingAddressUpdatePublisher
	addressUpdateEventPublisher               wireTypes.ShippingAddressUpdateEventPublisher
	userDevicePropertiesUpdatePublisher       wireTypes.UserDevicePropertiesUpdatePublisher
	vgClient                                  vgPbCustomer.CustomerClient
	savingsClient                             savingsPb.SavingsClient
	actorClient                               actor.ActorClient
	s3Client                                  s3.S3Client
	config                                    *config.Config
	authClient                                authPb.AuthClient
	vkycClient                                vkycPb.VKYCClient
	vgDepositClient                           vgDepositPb.DepositClient
	userDevicePropDao                         dao.IUserDevicePropertiesDao
	userPrefDao                               dao.IUserPreferenceDao
	livClient                                 livPb.LivenessClient
	accessRevokeUpdatePublisher               wireTypes.UserAccessRevokeUpdatePublisher
	GetUserInterface                          GetUserInterface
	getAddressesInterface                     getAddressesInterface
	commsClient                               commsPb.CommsClient
	eventBroker                               events.Broker
	bcClient                                  bankcust.BankCustomerServiceClient
	changeFeed                                changefeed.ChangeFeeder
	userCacheStorage                          wireTypes.UserCacheStorage
	dynConf                                   *genconf.Config
	groupClient                               userGroup.GroupClient
	empClient                                 employment.EmploymentClient
	screenerClient                            screener.ScreenerClient
	vendorStore                               vendorstore.VendorStore
	userPropertiesTxnExecutor                 storagev2.TxnExecutor
	productClient                             product.ProductClient
	apiCache                                  cache.APICacheStorage
	panClient                                 panPb.PanClient
	deleteUserPublisher                       wireTypes.DeleteUserPublisher
	processAccessRevokeCooldownDelayPublisher wireTypes.ProcessAccessRevokeCooldownDelayPublisher
	// todo(NRI): remove this post passport verification moves to KYC, Bad practice to inject onbClient, do not use apart from any get usages
	onbClient              onbPb.OnboardingClient
	kycDocExtractionClient kycdocspb.DocExtractionClient
	leadsClient            leadsPb.UserLeadSvcClient
}

type exists struct{}

var (
	NomineeSameAsUserErrorStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(pb.CreateNomineeResponse_FAILED_PRECONDITION_NOMINEE_SAME_AS_ACCOUNT_HOLDER),
			"Invalid request: Nominee cannot be the same as account holder")
	}
	GuardianSameAsUserErrorStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(pb.CreateNomineeResponse_FAILED_PRECONDITION_GUARDIAN_SAME_AS_ACCOUNT_HOLDER),
			"Invalid request: Nominee's guardian cannot be the same as account holder")
	}
	DobInvalidDateErrorStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(pb.UpdateUserResponse_STATUS_DOB_INVALID),
			"Invalid request: User's Date of birth is invalid")
	}
	DobUnderAgeErrorStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(pb.UpdateUserResponse_STATUS_DOB_UNDERAGE),
			fmt.Sprintf("Invalid request: User's age is below %v", MinimumAgeForAdulthood))
	}
	DobOverAgeErrorStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(pb.UpdateUserResponse_STATUS_DOB_OVERAGE),
			fmt.Sprintf("Invalid request: User's age is above %v", onboarding.MaximumAgeForOnboarding))
	}
)

// Factory method for creating an instance of this service. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewService(st dao.UserDao, minUserDao dao.MinimalUserDao, addressCodesDao dao.CKycAddressCodesDao, vendorAreaCodesDao dao.VendorAreaCodesDao,
	nomineeDao dao.NomineeDao, shippingPrefDao spdao.ShippingPreferencesDao, shippingPrefVendorReqDao spdao.ShippingPreferenceVendorRequestDao,
	appInstanceIdsDao dao.AppInstanceIdentifierDao, idGen idgen.IdGenerator, kycClient kycPb.KycClient, shippingProducer wireTypes.ShippingAddressUpdatePublisher, vgClient vgPbCustomer.CustomerClient,
	savingsClient savingsPb.SavingsClient, s3Client s3.S3Client, config *config.Config, actorClient actor.ActorClient,
	authClient authPb.AuthClient, vkycClient vkycPb.VKYCClient, vgDepositClient vgDepositPb.DepositClient,
	userDevicePropDao dao.IUserDevicePropertiesDao, userPrefDao dao.IUserPreferenceDao, livClient livPb.LivenessClient,
	accessRevokeUpdatePublisher wireTypes.UserAccessRevokeUpdatePublisher, commsClient commsPb.CommsClient, eventBroker events.Broker,
	bcClient bankcust.BankCustomerServiceClient, cf changefeed.ChangeFeeder, addressUpdateEventPublisher wireTypes.ShippingAddressUpdateEventPublisher,
	userDevicePropertiesUpdatePublisher wireTypes.UserDevicePropertiesUpdatePublisher, cacheStorage wireTypes.UserCacheStorage, dynConf *genconf.Config,
	groupClient userGroup.GroupClient, empClient employment.EmploymentClient, screenerClient screener.ScreenerClient, vendorStore vendorstore.VendorStore,
	userPropertiesTxnExecutor storagev2.TxnExecutor, productClient product.ProductClient, apiCache cache.APICacheStorage, panClient panPb.PanClient, deleteUserPublisher wireTypes.DeleteUserPublisher,
	onbClient onbPb.OnboardingClient, kycDocExtractionClient kycdocspb.DocExtractionClient, processAccessRevokeCooldownDelayPublisher wireTypes.ProcessAccessRevokeCooldownDelayPublisher,
	leadsClient leadsPb.UserLeadSvcClient,
) *Service {
	s := &Service{
		dao:                                 st,
		minUserDao:                          minUserDao,
		addressCodesDao:                     addressCodesDao,
		vendorAreaCodesDao:                  vendorAreaCodesDao,
		nomineeDao:                          nomineeDao,
		shippingPrefDao:                     shippingPrefDao,
		shippingReqDao:                      shippingPrefVendorReqDao,
		appInstanceIdsDao:                   appInstanceIdsDao,
		idGen:                               idGen,
		kycClient:                           kycClient,
		shippingProducer:                    shippingProducer,
		vgClient:                            vgClient,
		savingsClient:                       savingsClient,
		s3Client:                            s3Client,
		config:                              config,
		actorClient:                         actorClient,
		authClient:                          authClient,
		vkycClient:                          vkycClient,
		vgDepositClient:                     vgDepositClient,
		userDevicePropDao:                   userDevicePropDao,
		userPrefDao:                         userPrefDao,
		livClient:                           livClient,
		accessRevokeUpdatePublisher:         accessRevokeUpdatePublisher,
		commsClient:                         commsClient,
		bcClient:                            bcClient,
		eventBroker:                         eventBroker,
		changeFeed:                          cf,
		addressUpdateEventPublisher:         addressUpdateEventPublisher,
		userDevicePropertiesUpdatePublisher: userDevicePropertiesUpdatePublisher,
		userCacheStorage:                    cacheStorage,
		dynConf:                             dynConf,
		groupClient:                         groupClient,
		empClient:                           empClient,
		screenerClient:                      screenerClient,
		vendorStore:                         vendorStore,
		userPropertiesTxnExecutor:           userPropertiesTxnExecutor,
		productClient:                       productClient,
		apiCache:                            apiCache,
		panClient:                           panClient,
		deleteUserPublisher:                 deleteUserPublisher,
		onbClient:                           onbClient,
		kycDocExtractionClient:              kycDocExtractionClient,
		processAccessRevokeCooldownDelayPublisher: processAccessRevokeCooldownDelayPublisher,
		leadsClient: leadsClient,
	}
	s.GetUserInterface = s
	s.getAddressesInterface = s
	return s
}

// CreateUser RPC creates a user in the database.
func (s *Service) CreateUser(ctx context.Context, req *pb.CreateUserRequest) (*pb.CreateUserResponse, error) {
	res, errResp := s.authClient.BlockExistingAuthFactorUpdates(ctx, &authPb.BlockExistingAuthFactorUpdatesRequest{
		Email:       req.GetUser().GetProfile().GetEmail(),
		PhoneNumber: req.GetUser().GetProfile().GetPhoneNumber(),
	})
	if err := epifigrpc.RPCError(res, errResp); err != nil {
		if res.GetStatus().IsPermissionDenied() {
			logger.Error(ctx, "error when creating a user as afu is in progress for auth factor", zap.Error(err))
			return &pb.CreateUserResponse{
				Status: rpc.StatusPermissionDenied(),
			}, err
		}
		logger.Error(ctx, "error in BlockExistingAuthFactorUpdates", zap.Error(err))
		return &pb.CreateUserResponse{
			Status: rpc.StatusInternal(),
		}, err
	}
	id, err := s.dao.CreateUser(ctx, req.GetUser())

	if err != nil {
		logger.Error(ctx, "error when creating a user", zap.Error(err))
		return nil, err
	}

	return &pb.CreateUserResponse{
		Status: rpc.StatusOk(),
		User: &pb.User{
			Id:              id,
			Profile:         req.User.Profile,
			AcquisitionInfo: req.GetUser().GetAcquisitionInfo(),
		},
	}, nil
}

// GetUser RPC fetches the user by identifiers
func (s *Service) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.GetUserResponse, error) {
	var (
		user       *pb.User
		err        error
		userStatus pb.FiStatus
	)
	switch req.Identifier.(type) {
	case *pb.GetUserRequest_Id:
		if req.GetId() == "" {
			return &pb.GetUserResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("empty user id"),
			}, nil
		}
		user, err = s.dao.GetUser(ctx, req.GetId(), &model.GetUserParams{
			Unscoped: true,
		})
	case *pb.GetUserRequest_PhoneNumber:
		user, err = s.dao.GetUserByPhoneNumber(ctx, req.GetPhoneNumber())
	case *pb.GetUserRequest_EmailId:
		user, err = s.dao.GetUserByEmailId(ctx, req.GetEmailId())
	case *pb.GetUserRequest_ActorId:
		user, err = s.dao.GetUserByActorId(ctx, req.GetActorId())
	default:
		return nil, fmt.Errorf("unknown identifier")
	}

	// User not found with given parameter
	if storagev2.IsRecordNotFoundError(err) {
		return &pb.GetUserResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "Error when fetching a user", zap.Error(err))
		status := rpc.StatusInternal()
		status.SetDebugMessage(fmt.Sprintf("error in get user: %v", err))
		return &pb.GetUserResponse{
			Status: status,
		}, nil
	}

	if req.GetWantProfileImageUrl() {
		// Updating pre-signed url in old parameter
		profileImageS3FilePath := getProfileImageS3FilePath(ctx, user)
		// Update Profile image url to pre-signed url
		url, err := s.getPresignedUrl(ctx, profileImageS3FilePath)
		if err != nil { // Ignoring the url if pre-signing failed
			logger.Error(ctx, "failed to get pre-signed url, image wont be visible", zap.Error(err))
		}

		if user.GetProfile() != nil {
			user.GetProfile().ProfileImageUrl = url
		}
	}

	if req.GetWantPhotoInBase64() {
		photo, errPhoto := image.EnsureImageInBase64(ctx, user.GetProfile().GetPhoto(), s.s3Client)
		if errPhoto != nil {
			logger.Error(ctx, fmt.Sprintf("Failed to ensure image in base64: %v", errPhoto))
			return nil, errPhoto
		}
		logger.Info(ctx, fmt.Sprintf("image downloaded from S3 with len: %v", len(photo.GetImageDataBase64())))
		user.Profile.Photo.ImageDataBase64 = photo.GetImageDataBase64()
	}

	if req.GetForceGender() || req.GetForceKycAddressPinCodes() {
		if user, err = s.ensureGenderAndPinCodesPopulated(ctx, user, req.GetForceGender(), req.GetForceKycAddressPinCodes()); err != nil {
			return nil, err
		}
	}

	userStatus = pb.FiStatus_FI_STATUS_ACTIVE
	if user.GetDeletionDetails() != nil {
		userStatus = pb.FiStatus_FI_STATUS_CLOSED
	}

	return &pb.GetUserResponse{
		Status:     rpc.StatusOk(),
		User:       user,
		UserStatus: userStatus,
	}, nil

}

// GetMinimalUser returns MinimalUser object from given one of identifiers
func (s *Service) GetMinimalUser(ctx context.Context, req *pb.GetMinimalUserRequest) (*pb.GetMinimalUserResponse, error) {
	var (
		errResp = func(status *rpc.Status) (*pb.GetMinimalUserResponse, error) {
			return &pb.GetMinimalUserResponse{
				Status: status,
			}, nil
		}
		successResp = func(minUser *pb.MinimalUser) (*pb.GetMinimalUserResponse, error) {
			return &pb.GetMinimalUserResponse{
				Status:      rpc.StatusOk(),
				MinimalUser: minUser,
			}, nil
		}

		m *pb.MinimalUser
		e error
	)
	switch req.Identifier.(type) {
	case *pb.GetMinimalUserRequest_Id:
		m, e = s.minUserDao.GetMinimalUserById(ctx, req.GetId())
	case *pb.GetMinimalUserRequest_PhoneNumber:
		m, e = s.minUserDao.GetMinimalUserByPhone(ctx, req.GetPhoneNumber())
	default:
		m, e = nil, epifierrors.ErrInvalidArgument
	}
	if e != nil || m == nil {
		switch {
		case storagev2.IsRecordNotFoundError(e):
			return errResp(rpc.StatusRecordNotFound())
		default:
			logger.Error(ctx, "error in fetching minimal user", zap.Error(e))
			return errResp(rpc.StatusInternalWithDebugMsg(e.Error()))
		}
	}

	// populate actorId for minUser and in users table
	if !hasActorIdPopulated(m) {
		aId, err := s.fetchActorIdAndUpdateUser(ctx, m.GetId())
		if err != nil {
			return errResp(rpc.StatusInternalWithDebugMsg(err.Error()))
		}
		m.ActorId = aId
	}
	return successResp(m)
}

func (s *Service) fetchActorIdAndUpdateUser(ctx context.Context, userId string) (actorId string, err error) {
	aResp, err := s.actorClient.GetActorByEntityId(ctx, &actor.GetActorByEntityIdRequest{
		EntityId: userId,
		Type:     types.Actor_USER,
	})
	if err = epifigrpc.RPCError(aResp, err); err != nil {
		if aResp.GetStatus().IsRecordNotFound() {
			return "", nil
		}
		logger.Error(ctx, "error finding actorId", zap.Error(err), zap.String(logger.ID, userId))
		return "", fmt.Errorf("error fetching actorId: %w", err)
	}
	u := &pb.User{
		Id:      userId,
		ActorId: aResp.GetActor().GetId(),
	}
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		if daoErr := s.dao.UpdateUser(ctx, u, []pb.UserFieldMask{
			pb.UserFieldMask_ACTOR_ID,
		}); daoErr != nil {
			logger.Error(ctx, "error updating actorId to user", zap.Error(daoErr), zap.String(logger.ACTOR_ID_V2, u.GetActorId()))
		}
	})
	return aResp.GetActor().GetId(), nil
}

// GetUsers RPC fetches users by multiple identifier types in batch (IDs, phone numbers,
// emails, PANs, hashed phone numbers). Missing users are silently ignored
// Unscoped is only supported for userIds and PANs which includes deleted users.
func (s *Service) GetUsers(ctx context.Context, req *pb.GetUsersRequest) (*pb.GetUsersResponse, error) {
	var (
		res            = &pb.GetUsersResponse{}
		userIds        []string
		userEmailIds   []string
		userPans       []string
		userPhn        []*commontypes.PhoneNumber
		usersHashedPhn []string
		fetchedUsers   []*pb.User
	)

	for _, request := range req.GetIdentifier() {
		switch request.Identifier.(type) {
		case *pb.GetUsersRequest_GetUsersIdentifier_Id:
			userIds = append(userIds, request.GetId())
		case *pb.GetUsersRequest_GetUsersIdentifier_PhoneNumber:
			userPhn = append(userPhn, request.GetPhoneNumber())
		case *pb.GetUsersRequest_GetUsersIdentifier_EmailId:
			userEmailIds = append(userEmailIds, request.GetEmailId())
		case *pb.GetUsersRequest_GetUsersIdentifier_Pan:
			userPans = append(userPans, request.GetPan())
		case *pb.GetUsersRequest_GetUsersIdentifier_HashedPhoneNumber:
			usersHashedPhn = append(usersHashedPhn, request.GetHashedPhoneNumber())
		default:
			return nil, fmt.Errorf("unknown identifier")
		}
	}

	if len(userIds) != 0 {
		fetchedUsersByIds, err := s.getUsersByIds(ctx, userIds, req.GetUnscoped())
		if err != nil {
			logger.Error(ctx, "error getting users by Ids", zap.Error(err))
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error: %v", err))
				return res, nil
			}
		}
		fetchedUsers = append(fetchedUsers, fetchedUsersByIds...)
	}
	if len(userEmailIds) != 0 {
		fetchedUsersByEmailIds, err := s.getUsersByEmailIds(ctx, userEmailIds)
		if err != nil {
			logger.Error(ctx, "error getting users by EmailIds", zap.Error(err))
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error: %v", err))
				return res, nil
			}
		}
		fetchedUsers = append(fetchedUsers, fetchedUsersByEmailIds...)
	}
	if len(userPans) != 0 {
		fetchedUsersByPans, err := s.getUsersByPans(ctx, userPans, req.GetUnscoped())
		if err != nil {
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error getting users by Pans", zap.Error(err))
				res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error: %v", err))
				return res, nil
			} else {
				logger.Info(ctx, "no user found by pan")
			}
		}
		fetchedUsers = append(fetchedUsers, fetchedUsersByPans...)
	}
	if len(userPhn) != 0 {
		fetchedUsersByPhone, err := s.getUsersByPhone(ctx, userPhn)
		if err != nil {
			logger.Error(ctx, "error getting users by Phone", zap.Error(err))
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error: %v", err))
				return res, nil
			}
		}
		fetchedUsers = append(fetchedUsers, fetchedUsersByPhone...)
	}
	if len(usersHashedPhn) != 0 {
		fetchedUsersByHashedPhone, err := s.dao.GetUsersByHashedPhoneNumber(ctx, usersHashedPhn)
		if err != nil {
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error getting users by hashed phone", zap.Error(err))
				res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error: %v", err))
				return res, nil
			}
		}
		fetchedUsers = append(fetchedUsers, fetchedUsersByHashedPhone...)
	}

	// Update the image url in the deprecated field
	for _, fetchedUser := range fetchedUsers {
		// Updating pre-signed url in old parameter
		profileImageS3FilePath := getProfileImageS3FilePath(ctx, fetchedUser)
		// Update Profile image url to pre-signed url
		presignedUrl, err := s.getPresignedUrl(ctx, profileImageS3FilePath)
		if err != nil { // Ignoring the url if pre-signing failed
			logger.Error(ctx, "failed to get pre-signed url, image wont be visible", zap.Error(err))
		}

		if fetchedUser.GetProfile() != nil {
			fetchedUser.GetProfile().ProfileImageUrl = presignedUrl
		}
	}

	return &pb.GetUsersResponse{
		Users:  fetchedUsers,
		Status: rpc.StatusOk(),
	}, nil
}

func isAgeAboveOnbThreshold(dob *date.Date) bool {
	ts := time.Date(int(dob.GetYear()), time.Month(dob.GetMonth()), int(dob.GetDay()), 0, 0, 0, 0, time.Local)
	userAge := datetime.Age(ts)
	return userAge > onboarding.MaximumAgeForOnboarding
}

func isAgeBelowOnbThreshold(dob *date.Date) bool {
	ts := time.Date(int(dob.GetYear()), time.Month(dob.GetMonth()), int(dob.GetDay()), 0, 0, 0, 0, time.Local)
	userAge := datetime.Age(ts)
	return userAge < MinimumAgeForAdulthood
}

func (s *Service) UpdateUser(ctx context.Context, req *pb.UpdateUserRequest) (*pb.UpdateUserResponse, error) {
	maskMap := make(map[pb.UserFieldMask]struct{})
	for _, mask := range req.GetUpdateMask() {
		maskMap[mask] = struct{}{}
	}

	// age should be between 18 and 100 for user
	if _, ok := maskMap[pb.UserFieldMask_DOB]; ok && isAgeBelowOnbThreshold(req.GetUser().GetProfile().GetDateOfBirth()) {
		logger.Info(ctx, "User age is below 18")
		return &pb.UpdateUserResponse{
			Status: DobUnderAgeErrorStatus(),
		}, nil
	}
	if _, ok := maskMap[pb.UserFieldMask_DOB]; ok && isAgeAboveOnbThreshold(req.GetUser().GetProfile().GetDateOfBirth()) {
		logger.Info(ctx, "User age is above max allowed in onboarding")
		return &pb.UpdateUserResponse{
			Status: DobOverAgeErrorStatus(),
		}, nil
	}

	// If we receive a photo in base64 data, we upload it to S3 and store it as a link.
	if _, ok := maskMap[pb.UserFieldMask_PHOTO]; ok && req.GetUser().GetProfile().GetPhoto().GetImageDataBase64() != "" {
		// Upload image to s3
		path, errUpload := s.uploadBase64ImageToS3(ctx, req.GetUser().GetProfile().GetPhoto().GetImageDataBase64(), req.GetUser().GetId())
		if errUpload != nil {
			logger.Error(ctx, "failed to upload user photo to s3", zap.Error(errUpload))
			return &pb.UpdateUserResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		req.User.Profile.Photo = &commontypes.Image{
			ImageUrl: path,
		}
	}

	if _, ok := maskMap[pb.UserFieldMask_ACCESS_REVOKE_DETAILS]; ok {
		switch {
		// send email and notification stating "account disabled due to security reasons" if reason is due to fraudulent activities
		case req.GetUser().GetAccessRevokeDetails().GetAccessRevokeStatus() == pb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED &&
			req.GetUser().GetAccessRevokeDetails().GetReason().IsAccessRevokeReasonFraudulent():
			goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
				s.publishRiskyActorEvent(epificontext.CloneCtx(ctx), req.GetUser().GetId())
			})
			if s.config.Flags.EnableUpdateUserComms {
				_ = s.sendAccountBlockedNotifications(ctx, req.GetUser().GetId(), req.GetUser().GetProfile())
			}
		// app access can be updated to unblocked while simultaneously applying savings account restrictions.
		// In such cases we do not want to send communication stating bank account access has been reinstated
		case req.GetUser().GetAccessRevokeDetails().GetAccessRevokeStatus() == pb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED &&
			req.GetUser().GetAccessRevokeDetails().GetRestoreReason() != pb.AccessRestoreReason_ACCESS_RESTORE_REASON_CREDIT_FREEZE:
			if s.config.Flags.EnableUpdateUserComms {
				_ = s.sendAccountUnblockedNotifications(ctx, req.GetUser().GetProfile(), req.GetUser().GetId())
			}
		// in case of credit freeze, we want to only consider the access restore reason while updating users access revoke details
		// (access revoke reason was only passed to update savings account constraint details)
		case req.GetUser().GetAccessRevokeDetails().GetAccessRevokeStatus() == pb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED &&
			req.GetUser().GetAccessRevokeDetails().GetRestoreReason() == pb.AccessRestoreReason_ACCESS_RESTORE_REASON_CREDIT_FREEZE:
			req.User.AccessRevokeDetails.Reason = pb.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED
		}
	}

	if _, ok := maskMap[pb.UserFieldMask_PAN]; ok {
		aUser, err := s.dao.GetUser(ctx, req.GetUser().GetId(), nil)
		if err != nil {
			logger.Error(ctx, "error in get user", zap.Error(err))
			return nil, err
		}

		if s.dynConf.Flags().EnableActiveProductsByPANCheck() {
			getActiveProductsResp, errActiveProducts := s.productClient.GetActiveProductsByPAN(ctx, &product.GetActiveProductsByPANRequest{
				ExcludedActorId: aUser.GetActorId(),
				Pan:             req.GetUser().GetProfile().GetPAN(),
			})
			if errActiveProducts != nil {
				logger.Error(ctx, "failed to get active products by PAN", zap.Error(errActiveProducts))
				return &pb.UpdateUserResponse{
					Status: rpc.StatusInternalWithDebugMsg(errActiveProducts.Error()),
				}, nil
			}

			switch getActiveProductsResp.GetStatus().GetCode() {
			case uint32(product.GetActiveProductsByPANResponse_USER_NOT_FOUND), uint32(product.GetActiveProductsByPANResponse_ACTIVE_PRODUCT_NOT_FOUND):
			case rpc.StatusOk().GetCode():
				if activeProductCheck(getActiveProductsResp.GetActiveProducts()) {
					logger.Info(ctx, fmt.Sprintf("another user with active product found with current PAN: %v", getActiveProductsResp.GetActiveProducts()))
					return &pb.UpdateUserResponse{
						Status: rpc.StatusAlreadyExistsWithDebugMsg("active user present with current PAN"),
					}, nil
				}
			default:
				logger.Error(ctx, fmt.Sprintf("unknown status in GetActiveProductsByPAN : %v", getActiveProductsResp.GetStatus()))
				return &pb.UpdateUserResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}

			// TODO(Ankit): Find other users with the same PAN and soft delete them (We will only get inactive product users here)
		}
	}

	if err := s.dao.UpdateUser(ctx, req.User, req.UpdateMask); err != nil {
		if errors.Is(err, epifierrors.ErrDuplicateEntry) {
			return &pb.UpdateUserResponse{
				Status: rpc.StatusAlreadyExists(),
			}, nil
		}
		if errors.Is(err, datetime.ErrInvalidDate) {
			return &pb.UpdateUserResponse{
				Status: DobInvalidDateErrorStatus(),
			}, nil
		}
		if errors.Is(err, dao.ErrVendorIdAlreadyExists) {
			return &pb.UpdateUserResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg(err.Error()),
			}, nil
		}
		if errors.Is(err, epifierrors.ErrPermissionDenied) {
			return &pb.UpdateUserResponse{
				Status: rpc.StatusPermissionDenied(),
			}, nil
		}
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &pb.UpdateUserResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error in updating user", zap.Error(err))
		return &pb.UpdateUserResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	aUser, err := s.dao.GetUser(ctx, req.GetUser().GetId(), nil)
	if err != nil {
		logger.Error(ctx, "error in get user", zap.Error(err))
		return nil, err
	}

	if _, ok := maskMap[pb.UserFieldMask_ACTOR_ID]; ok {
		s.updateActorIdInLeadSystem(ctx, aUser)
	}

	if _, ok := maskMap[pb.UserFieldMask_ACCESS_REVOKE_DETAILS]; ok {
		s.publishUpdateAccessRevokeEvent(ctx, req.GetUser().GetId(), req.GetUser().GetAccessRevokeDetails())
	}
	return &pb.UpdateUserResponse{
		Status: rpc.StatusOk(),
		User:   aUser,
	}, nil

}

func (s *Service) updateActorIdInLeadSystem(ctx context.Context, user *pb.User) {
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		activeLeadsRes, err := s.leadsClient.GetActiveLeads(ctx, &leadsPb.GetActiveLeadsRequest{
			PhoneNumber: user.GetProfile().GetPhoneNumber(),
		})
		if err = epifigrpc.RPCError(activeLeadsRes, err); err != nil {
			logger.Error(ctx, "error in getting active leads", zap.Error(err))
			return
		}
		var leadIds []string
		for _, lead := range activeLeadsRes.GetProductTypeToActiveLeadMap() {
			leadIds = append(leadIds, lead.GetId())
		}

		if len(leadIds) == 0 {
			return
		}

		updateLeadRes, err := s.leadsClient.SetActorId(ctx, &leadsPb.SetActorIdRequest{
			LeadIds: leadIds,
			ActorId: user.GetActorId(),
			Email:   user.GetProfile().GetEmail(),
		})
		if err = epifigrpc.RPCError(updateLeadRes, err); err != nil {
			logger.Error(ctx, "error in updating actorId/email in lead system", zap.Error(err))
			return
		}
	})
}

func activeProductCheck(activeProducts []product.ProductType) bool {
	switch {
	case len(activeProducts) == 0:
		return false
	case len(activeProducts) == 1 && lo.Contains(activeProducts, product.ProductType_PRODUCT_TYPE_TPAP):
		return false
	default:
		return true
	}
}

func (s *Service) publishRiskyActorEvent(ctx context.Context, userId string) {
	actorRes, err := s.actorClient.GetActorByEntityId(ctx, &actor.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: userId,
	})
	if grpcErr := epifigrpc.RPCError(actorRes, err); grpcErr != nil {
		logger.Error(ctx, "failed to get actor by entity id in risky actor event", zap.Error(grpcErr))
		return
	}
	actorId := actorRes.GetActor().GetId()
	if actorId == "" {
		logger.Error(ctx, "actor id is empty in risky actor event")
		return
	}
	s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewRiskyActor(actorId))
}

// GetAddress RPC fetches the existing address for the given user.
// There can be multiple addresses for a user. We identify the address to be updated using addressType attribute.
func (s *Service) GetAddress(ctx context.Context, req *pb.GetAddressRequest) (*pb.GetAddressResponse, error) {
	resp := &pb.GetAddressResponse{}
	user, status := s.getUserFromId(ctx, req.GetUserId())
	resp.Status = status
	if !status.IsSuccess() {
		return resp, nil
	}

	isNRUser, _, err := s.isNonResidentPhNum(user.GetProfile().GetPhoneNumber())
	if err != nil {
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	if isNRUser {
		return s.getAddressByTypeForNRUser(ctx, user, req)
	}

	if address, err := s.getAddress(ctx, user, req.Type); err != nil {
		resp.Status = rpc.StatusInternal()
	} else {
		resp.Address = address
	}
	return resp, nil
}

// GetAllAddresses RPC fetches all addresses for a user from multiple sources. A user can have multiple addresses categorized by AddressType.
// There can be multiple addresses for a user even for a single AddressType
func (s *Service) GetAllAddresses(ctx context.Context, req *pb.GetAllAddressesRequest) (*pb.GetAllAddressesResponse, error) {
	resp := &pb.GetAllAddressesResponse{}

	user, status := s.getUserFromId(ctx, req.GetUserId())
	if !status.IsSuccess() {
		resp.Status = status
		return resp, nil
	}

	addresses, err := s.getAllAddresses(ctx, user, req.Format)

	if err != nil {
		logger.Error(ctx, "error in getting all addresses of user", zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, err
	}

	resp.Addresses = make(map[string]*types.Addresses)
	for addressType, addresses := range addresses {
		resp.Addresses[addressType.String()] = &types.Addresses{Addresses: addresses}
	}
	resp.Status = rpc.StatusOk()
	return resp, nil
}

// UpdateAddress RPC facilitates updating an address.
// There can be multiple addresses for a user. We identify the address to be updated using addressType attribute.
//
// This is intentionally separated from UpdateUser() rpc. This would help if Address is moved outside the
// user profile data.
func (s *Service) UpdateAddress(ctx context.Context, req *pb.UpdateAddressRequest) (*pb.UpdateAddressResponse, error) {
	user, err := s.dao.GetUser(ctx, req.GetUserId(), nil)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("unable to get user details for userId: %v", req.UserId), zap.Error(err))
		if storagev2.IsRecordNotFoundError(err) {
			return &pb.UpdateAddressResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}

		return &pb.UpdateAddressResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	addresses := user.GetProfile().GetAddresses()
	if addresses == nil {
		user.GetProfile().Addresses = make(map[string]*postaladdress.PostalAddress)
		addresses = user.GetProfile().Addresses
	}

	logger.Info(ctx, fmt.Sprintf("updating address for user %v, address_type %v", user.Id, req.Type.String()))
	addresses[req.Type.String()] = req.Address

	// update the database entry
	if err = s.dao.UpdateUser(ctx, user, []pb.UserFieldMask{pb.UserFieldMask_ADDRESSES}); err != nil {
		logger.Error(ctx, fmt.Sprintf("unable to update address in DB for user %v, address_type %v", req.UserId,
			req.Type.String()), zap.Error(err))
		return &pb.UpdateAddressResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	// TODO (anand): understand how the updated address is synced with all the vendors?
	return &pb.UpdateAddressResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// CreateNominee RPC to create a nominee for an actor.
// There can be multiple nominees associated with an actor.
func (s *Service) CreateNominee(ctx context.Context, req *pb.CreateNomineeRequest) (*pb.CreateNomineeResponse, error) {
	resp := &pb.CreateNomineeResponse{}

	// Check for Nominee's DOB can not be greater than today.
	now := timestampPb.New(time.Now().In(datetime.IST))
	if datetime.IsBefore(now, req.GetNominee().GetDob()) {
		logger.Info(ctx, "Failed to create nominee: nominee's DOB cannot be more than today's date")
		resp.Status = DobInvalidDateErrorStatus()
		return resp, nil
	}

	user, err := s.GetUserByActorID(ctx, req.Nominee.ActorId)
	if err != nil {
		resp.Status = rpc.StatusInternal()
		return resp, err
	}
	userName := user.GetProfile().GetPanName()

	// verify that nominee is different from account holder
	// we use name-match to verify whether nominee is different from account holder
	nomineeName := names.ParseString(req.Nominee.Name)
	matched, err := nameMatch(ctx, userName, nomineeName)
	if err != nil {
		resp.Status = rpc.StatusInternal()
		return resp, err
	}
	if matched {
		logger.Error(ctx, "Failed to create nominee: nominee cannot be same as account holder")
		resp.Status = NomineeSameAsUserErrorStatus()
		return resp, nil
	}

	// if guardian info is present for nominee, verify that guardian is different from account holder
	// we use name-match to verify whether guardian is different from account holder
	if req.Nominee.GuardianInfo != nil {
		guardianName := names.ParseString(req.Nominee.GuardianInfo.Name)
		matched, err := nameMatch(ctx, userName, guardianName)
		if err != nil {
			resp.Status = rpc.StatusInternal()
			return resp, err
		}
		if matched {
			logger.Error(ctx, "Failed to create nominee: nominee's guardian cannot be same as account holder")
			resp.Status = GuardianSameAsUserErrorStatus()
			return resp, nil
		}
	}

	nominee, err := s.nomineeDao.Create(ctx, req.Nominee)
	if err != nil {
		logger.Error(ctx, "failed to create nominee in database", zap.Any(logger.NOMINEE, req.Nominee))
		resp.Status = rpc.StatusInternal()
		return resp, err
	}

	resp.Status = rpc.StatusOk()
	resp.Nominee = nominee
	return resp, nil
}

// GetNominees RPC to fetch all nominees corresponding to an actor
func (s *Service) GetNominees(ctx context.Context, req *pb.GetNomineesRequest) (*pb.GetNomineesResponse, error) {
	resp := &pb.GetNomineesResponse{}

	nominees, err := s.nomineeDao.GetNominees(ctx, req.ActorId)
	if err != nil {
		logger.Error(ctx, "failed to get nominees using DAO for actor", zap.Any(logger.ACTOR_ID_V2, req.ActorId), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, err
	}

	if len(nominees) == 0 {
		logger.Debug(ctx, "no nominees corresponding to this actor. Setting status Record Not Found")
		resp.Status = rpc.StatusRecordNotFound()
	} else {
		resp.Status = rpc.StatusOk()
	}
	resp.Nominees = nominees
	return resp, nil
}

// GetNominee RPC to fetch nominee details for a given nominee ID and actor ID
func (s *Service) GetNominee(ctx context.Context, req *pb.GetNomineeRequest) (*pb.GetNomineeResponse, error) {
	resp := &pb.GetNomineeResponse{}
	actorID := req.ActorId
	nomineeID := req.NomineeId

	nominee, err := s.nomineeDao.GetNominee(ctx, actorID, nomineeID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx, fmt.Sprintf("no nominee found for actorID: %v and nomineeID: %v", actorID, nomineeID))
			resp.Status = rpc.StatusRecordNotFound()
		} else {
			logger.Error(ctx, "failed to get nominee from DAO",
				zap.String(logger.ACTOR_ID_V2, actorID), zap.String(logger.NOMINEE_ID, nomineeID), zap.Error(err))
			resp.Status = rpc.StatusInternal()
		}
		return resp, err
	}

	resp.Status = rpc.StatusOk()
	resp.Nominee = nominee
	return resp, nil
}

// CreateShippingPreference RPC to create shipping preference for an item intended to be shipped to the customer
func (s *Service) CreateShippingPreference(ctx context.Context, req *pb.CreateShippingPreferenceRequest) (*pb.CreateShippingPreferenceResponse, error) {
	resp := &pb.CreateShippingPreferenceResponse{}

	preference, err := s.shippingPrefDao.Create(ctx, req.Preference)
	if err != nil {
		logger.Error(ctx, "failed to create shipping preference entry in database", zap.Any("shipping preference", req.GetPreference()), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, err
	}

	resp.Status = rpc.StatusOk()
	resp.Preference = preference
	return resp, nil
}

// GetShippingPreference RPC to fetch Shipping preference for a given actor ID and shipping item
// In case no preference is found for the requested (actor ID, shipping item) set, nil response is returned with Status OK
func (s *Service) GetShippingPreference(ctx context.Context, req *pb.GetShippingPreferenceRequest) (*pb.GetShippingPreferenceResponse, error) {
	resp := &pb.GetShippingPreferenceResponse{}

	preference, err := s.shippingPrefDao.GetByActorIdAndItem(ctx, req.ActorId, req.ShippingItem)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			resp.Status = rpc.StatusRecordNotFound()
			return resp, nil
		}

		logger.Error(ctx, "failed to get shipping preference from DAO",
			zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.String(logger.SHIPPING_ITEM, req.ShippingItem.String()), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, err
	}

	resp.Status = rpc.StatusOk()
	resp.Preference = preference
	return resp, nil
}

func (s *Service) GetVendorGeoAreaCodes(ctx context.Context, req *pb.GetVendorGeoAreaCodesRequest) (*pb.GetVendorGeoAreaCodesResponse, error) {
	resp := &pb.GetVendorGeoAreaCodesResponse{}

	countryCode, stateCode, cityCode, err := GetVendorGeoAreaCodes(ctx, s.vendorAreaCodesDao, req.Vendor, req.State, req.City)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resp.Status = rpc.StatusRecordNotFound()
			return resp, nil
		}

		logger.Error(ctx, "failed to get vendor specified codes for city, state and country",
			zap.String(logger.VENDOR, req.Vendor.String()), zap.String(logger.STATE, req.State), zap.String(logger.CITY, req.City), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, err
	}

	resp.Status = rpc.StatusOk()
	resp.AreaCodes = &types.VendorGeoAreaCodes{
		Vendor:      req.Vendor,
		CountryCode: countryCode,
		StateCode:   stateCode,
		CityCode:    cityCode,
	}
	return resp, nil
}

func (s *Service) CheckShippingAddressUpdateStatus(ctx context.Context, req *pb.CheckShippingAddressUpdateStatusRequest) (*pb.CheckShippingAddressUpdateStatusResponse, error) {
	resp := &pb.CheckShippingAddressUpdateStatusResponse{}

	vendor := req.Vendor
	shippingItem := req.ShippingItem

	preference, err := s.shippingPrefDao.GetByActorIdAndItem(ctx, req.ActorId, shippingItem)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			errorMsg := fmt.Sprintf("no shipping preference found for the actor for item: %v", shippingItem)
			logger.Error(ctx, errorMsg)
			resp.Status = rpc.StatusRecordNotFoundWithDebugMsg(errorMsg)
			return resp, nil
		}
		logger.Error(ctx, "failed to get shipping preference from DAO", zap.String(logger.SHIPPING_ITEM, shippingItem.String()), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	shippingReqRecord, err := s.shippingReqDao.GetByPreferenceIdAndVendor(ctx, preference.Id, vendor)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			resp.Status = rpc.StatusRecordNotFound()
		} else {
			logger.Error(ctx, "failed to fetch shipping preference from DAO",
				zap.String(logger.SHIPPING_PREFERENCE_ID, preference.Id), zap.String(logger.VENDOR, vendor.String()), zap.Error(err))
			resp.Status = rpc.StatusInternal()
		}
		return resp, nil
	}

	resp.RequestStatus = shippingReqRecord.GetStatus()
	resp.Status = rpc.StatusOk()
	return resp, nil
}

// AddAppInstanceIdentifiers adds app instance IDs for different third party vendors. E.g. client_appsFlyer_id for AppsFlyer,
// app_instance_id for Firebase, etc. and IDs generated internally by our system, e.g. Prospect ID
func (s *Service) AddAppInstanceIdentifiers(ctx context.Context, req *pb.AddAppInstanceIdentifiersRequest) (*pb.AddAppInstanceIdentifiersResponse, error) {
	resp := &pb.AddAppInstanceIdentifiersResponse{}

	identifiers := map[types.AppInstanceIdName]string{}
	for _, identifier := range req.Identifiers {
		IdName := identifier.Name
		IdValue := identifier.Value

		if value, ok := identifiers[IdName]; ok {
			// this means that a duplicated key (ID name) exists in the list of identifiers which is not expected
			logger.Error(ctx, "Multiple occurances for same identifier key", zap.String("Identifier name", IdName.String()),
				zap.String("Identifier value 1", value), zap.String("Identifier value 2", IdValue))
			resp.Status = rpc.StatusInvalidArgumentWithDebugMsg("Multiple occurrences for same identifier key")
			return resp, nil
		}

		identifiers[IdName] = IdValue
	}

	if err := s.appInstanceIdsDao.Create(ctx, req.ActorId, identifiers); err != nil {
		logger.Error(ctx, "failed to create entry for app instance identifier in DB", zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}
	resp.Status = rpc.StatusOk()
	return resp, nil
}

// GetAppInstanceIdentifiers fetches app instance identifiers for an actor
// If request specifies only a set of ID names, then only those are returned. Success response is returned even if not
// all identifiers are found from the given list of IDs. If none is found, RecordNotFound status is returned.
func (s *Service) GetAppInstanceIdentifiers(ctx context.Context, req *pb.GetAppInstanceIdentifiersRequest) (*pb.GetAppInstanceIdentifiersResponse, error) {
	resp := &pb.GetAppInstanceIdentifiersResponse{}

	identifiersRes, err := s.appInstanceIdsDao.GetByActorId(ctx, req.ActorId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resp.Status = rpc.StatusRecordNotFound()
			return resp, nil
		}

		logger.Error(ctx, "failed to get app instance identifiers for the actor", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	resp.Identifiers = identifiersRes.Identifiers

	// if only a subset of IDs is requested, then filter out the unwanted ones
	if req.IdNames != nil {
		var identifiers []*types.AppInstanceId

		idNames := make(map[types.AppInstanceIdName]exists) // this also ensures uniqueness in the ID names
		for _, idName := range req.IdNames {
			idNames[idName] = exists{}
		}
		for _, identifier := range resp.Identifiers {
			if _, ok := idNames[identifier.Name]; ok {
				identifiers = append(identifiers, identifier)
			}
		}

		resp.Identifiers = identifiers

		if resp.Identifiers == nil {
			logger.Info(ctx, "No identifiers found for the provided set of id names", zap.Any("requested ID names", req.IdNames))
			resp.Status = rpc.StatusRecordNotFound()
			return resp, nil
		}

		if len(idNames) > len(resp.Identifiers) {
			logger.Info(ctx, "record not found for few of the requested IDs", zap.Any("requested ID names", req.IdNames))
			resp.Status = rpc.StatusOkWithDebugMsg("record not found for few of the requested IDs")
			return resp, nil
		}
	}

	resp.Status = rpc.StatusOk()
	return resp, nil
}

//  1. Looks for the address in User profile data. If found, return.
//  2. Else, query kyc temporary storage for the address type. If found, return. This is reached because we cannot
//     save the address fetched from KYC vendors permanently owing to compliance regulations.
//  3. Else, fetch the address from bank if the customer creation was successful at bank.
func (s *Service) getAddress(ctx context.Context, user *pb.User, addressType types.AddressType) (*postaladdress.PostalAddress, error) {

	// search address in user profile
	address, err := s.getAddressFromProfile(ctx, user, addressType)
	// return the address if the address type is shipping as we are only storing postal code in other address types
	if err == nil && addressType == types.AddressType_SHIPPING {
		return address, nil
	}
	// if there was an error in fetching address from profile or address type was not shipping, we need to
	// fetch the address from kyc service
	kycRecord, _, err := s.getKycRecord(ctx, user)
	if err != nil {
		return nil, err
	}
	address = kycRecord.GetPermanentAddress()
	return address, nil
}

// Looks for the address in the user profile data. If found, returns the address.
func (s *Service) getAddressFromProfile(_ context.Context, user *pb.User, addressType types.AddressType) (
	*postaladdress.PostalAddress, error) {
	addresses := user.GetProfile().GetAddresses()
	if address, ok := addresses[addressType.String()]; ok {
		return address, nil
	}
	return nil, fmt.Errorf("error in get address from profile: %w", epifierrors.ErrRecordNotFound)
}

func (s *Service) getKycRecord(ctx context.Context, user *pb.User) (*kycPb.KYCRecord, kycPb.KycType, error) {
	kycReq := &kycPb.GetKYCRecordRequest{
		ActorId: user.GetActorId(),
	}
	kycRes, err := s.kycClient.GetKYCRecord(ctx, kycReq)
	if err = epifigrpc.RPCError(kycRes, err); err != nil {
		if !kycRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in get kyc record", zap.Error(err))
		}
		return nil, 0, err
	}
	return kycRes.GetKycRecord(), kycRes.GetKycType(), nil
}

func (s *Service) DedupeCheck(ctx context.Context, req *pb.DedupeCheckRequest) (*pb.DedupeCheckResponse, error) {
	switch {
	case req.GetActorId() == "":
		logger.Info(ctx, "actor id is empty in dedupe check")
		return &pb.DedupeCheckResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid request missing actor id"),
		}, nil
	case req.GetMobileNum() == "" && req.GetPhoneNumber() == nil:
		logger.Info(ctx, "mobile number absent in request")
		return &pb.DedupeCheckResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid request missing mobile number"),
		}, nil
	case req.GetDateOfBirth() == nil:
		logger.Info(ctx, "date of birth is empty")
		return &pb.DedupeCheckResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid request missing date of birth"),
		}, nil
	}
	if req.GetRequestType() == pb.DedupeCheckRequest_REQUEST_TYPE_BEST_EFFORT {
		if resp, err := s.handleDedupeBestEffort(ctx, req); err == nil {
			logger.Info(ctx, "dedupe response returned from cache", zap.Error(err), zap.String(logger.STATE, resp.GetDedupeStatus().String()))
			return resp, nil
		}
	}
	if req.GetDedupeFlow() == pb.DedupeCheckRequest_FLOW_UNSPECIFIED {
		logger.Info(ctx, "dedupe check flow not specified for this call")
	}

	dedupeFlow, err := s.getDedupeFlow(ctx, req.GetActorId(), req.GetPassportNumber())
	if err != nil {
		return &pb.DedupeCheckResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	requestId := idgen.FederalRandomDigitsSequence("FEDDUPE", 5)
	vgReq := &vgPbCustomer.DedupeCheckRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: req.GetVendor(),
		},
		PanNumber:       req.GetPanNumber(),
		AadhaarNumber:   req.GetAadhaarNumber(),
		PassportNumber:  req.GetPassportNumber(),
		DrivingLicense:  req.GetDrivingLicense(),
		VoterId:         req.GetVoterId(),
		PhoneNumber:     req.GetPhoneNumber(),
		MobileNum:       req.GetMobileNum(),
		UidReferenceKey: req.GetUidReferenceKey(),
		DateOfBirth:     req.GetDateOfBirth(),
		EmailId:         req.GetEmailId(),
		RequestId:       requestId,
		Flow:            dedupeFlow,
	}

	vgRes, errResp := s.vgClient.DedupeCheck(ctx, vgReq)
	if err := epifigrpc.RPCError(vgRes, errResp); err != nil {
		logger.Error(ctx, "error in getting dedupe response from vendor", zap.Error(err))
		return nil, err
	}
	storeDedupeResponseAsync(ctx, req, s, vgRes, requestId)

	res := &pb.DedupeCheckResponse{
		Status:                         rpc.StatusOk(),
		CustomerId:                     vgRes.GetCustomerId(),
		CustomerName:                   vgRes.GetCustomerName(),
		DedupeStatus:                   vgRes.GetDedupeStatus(),
		CustomersLinkedWithPhoneNumber: vgRes.GetCustomersLinkedWithPhoneNumber(),
		KYCFlag:                        vgRes.GetKYCFlag(),
		CreditCardFlag:                 vgRes.GetCreditCardFlag(),
		CustomersLinkedWithEmailId:     vgRes.GetCustomersLinkedWithEmailId(),
	}
	_ = s.setDedupeResponseInCache(ctx, req, res)
	return res, nil
}

func (s *Service) getDedupeFlow(ctx context.Context, actorId string, passportNumber string) (dedupe.Flow, error) {
	// dedupe flow is required only when sending passport in request
	if passportNumber == "" {
		return 0, nil
	}
	isNonResident, err := s.isNonResident(ctx, actorId)
	if err != nil {
		return 0, err
	}

	if isNonResident {
		return dedupe.Flow_FLOW_NON_RESIDENT_ONBOARDING, nil
	}
	return 0, nil
}

func (s *Service) setDedupeResponseInCache(ctx context.Context, req *pb.DedupeCheckRequest, resp *pb.DedupeCheckResponse) error {
	return s.apiCache.SetResponse(ctx, req, resp, s.dynConf.DedupeCacheExpiry())
}

func (s *Service) handleDedupeBestEffort(ctx context.Context, req *pb.DedupeCheckRequest) (*pb.DedupeCheckResponse, error) {
	resp := &pb.DedupeCheckResponse{}
	if err := s.apiCache.GetResponse(ctx, req, resp); err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in fetching dedupe response from cache", zap.Error(err))
		}
		return nil, err
	}
	return resp, nil
}

func (s *Service) getUserFromId(ctx context.Context, userId string) (*pb.User, *rpc.Status) {
	user, err := s.dao.GetUser(ctx, userId, nil)
	if err != nil {
		logger.Error(ctx, "unable to get user details for userId", zap.Error(err))
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, rpc.StatusRecordNotFound()
		}
		return nil, rpc.StatusInternal()
	}
	return user, rpc.StatusOk()
}

func (s *Service) getActor(ctx context.Context, userId string) (*types.Actor, error) {
	resp, err := s.actorClient.GetActorByEntityId(ctx, &actor.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: userId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "failed to get actor by userId", zap.Error(err), zap.String(logger.USER_ID, userId))
		return nil, err
	}
	return resp.GetActor(), nil
}

// GenerateVendorRequestId generated request id for a given vendor.
// requestId has to be unique across vendors
func GenerateVendorRequestId(vendor commonvgpb.Vendor, reqMethod idgen.MethodName) (string, error) {
	switch vendor {
	case commonvgpb.Vendor_FEDERAL_BANK:
		return idgen.FederalRandomDigitsSequence(idgen.VendorRequestMethodName[reqMethod], 5), nil
	default:
		return "", fmt.Errorf("invalid vendor: %v", vendor)
	}
}

func (s *Service) GetActorById(ctx context.Context, actorId string) (*types.Actor, error) {
	resp, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if err != nil {
		logger.Error(ctx, "failed to get actor from Actor Service", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}
	return resp.Actor, nil
}

func (s *Service) GetUserByActorID(ctx context.Context, actorId string) (*pb.User, error) {
	actorRes, err := s.GetActorById(ctx, actorId)
	if err != nil {
		return nil, err
	}

	user, err := s.dao.GetUser(ctx, actorRes.EntityId, nil)
	if err != nil {
		logger.Error(ctx, "failed to fetch user from database", zap.String(logger.USER_ID, actorRes.EntityId), zap.Error(err))
		return nil, err
	}
	return user, nil
}

func (s *Service) DeleteUser(ctx context.Context, req *pb.DeleteUserRequest) (*pb.DeleteUserResponse, error) {
	user, err := s.dao.GetUser(ctx, req.GetUserId(), nil)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			return &pb.DeleteUserResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "failed to fetch user from database", zap.String(logger.USER_ID, req.GetUserId()), zap.Error(err))
		return nil, err
	}
	if err = s.dao.DeleteUser(ctx, req.GetUserId(), req.GetDeletionDetails()); err != nil {
		logger.Error(ctx, "failed to delete user", zap.Error(err))
		return nil, err
	}
	if _, err = s.deleteUserPublisher.Publish(ctx, &userEventPb.DeleteUserEvent{
		ActorId: user.GetActorId(),
	}); err != nil {
		logger.Error(ctx, "error in publishing delete user event", zap.Error(err))
	}
	return &pb.DeleteUserResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// Verifies if base Name matches the target name
// There are 3 criteria considered here: 1. FULL_NAME_MATCH  2. FIRST_LAST_MATCH  3. REVERSE_FIRST_LAST_MATCH
// Equality with any of these would result in a successful match
func nameMatch(ctx context.Context, baseName, targetName *commontypes.Name) (bool, error) {
	matchRes, err := gammanames.NameMatch(ctx, baseName, targetName, []gammanames.MatchingCriteria{gammanames.FULL_NAME_MATCH, gammanames.FIRST_LAST_MATCH, gammanames.REVERSE_FIRST_LAST_MATCH})
	if err != nil {
		logger.Error(ctx, "error occurred while name matching", zap.Any("base name", baseName), zap.Any("target name", targetName), zap.Error(err))
		return false, err
	}

	for _, score := range matchRes.Scores {
		if score.Score == 1 {
			return true, nil
		}
	}
	// targetName can have just one word as well. Ensure that is not the same as any of the words in the baseName
	name := targetName.ToString()
	if name == baseName.FirstName || name == baseName.MiddleName || name == baseName.LastName {
		return true, nil
	}

	return false, nil
}

func (s *Service) GetCustomerDetails(ctx context.Context, req *pb.GetCustomerDetailsRequest) (*pb.GetCustomerDetailsResponse, error) {
	internalErrResp := func(debug string) (*pb.GetCustomerDetailsResponse, error) {
		return &pb.GetCustomerDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(debug),
		}, nil
	}

	user, err := s.dao.GetUser(ctx, req.GetUserId(), &model.GetUserParams{
		Unscoped: true,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx, "user not found for given user id", zap.String(logger.USER_ID, req.GetUserId()))
			return &pb.GetCustomerDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching user",
			zap.String(logger.USER_ID, req.GetUserId()), zap.Error(err))
		return internalErrResp(err.Error())
	}

	vgRes, err := s.getVendorCustomerDetails(ctx, req.GetVendor(), req.GetProvenance(), req.GetActorId(), user)
	if err != nil {
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return &pb.GetCustomerDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		case errors.Is(err, vendorErr):
			return &pb.GetCustomerDetailsResponse{
				Status: rpc.NewStatus(uint32(pb.GetCustomerDetailsResponse_VENDOR_API_FAILURE),
					"vendor failure for fetching customer details", ""),
			}, nil
		}
		return internalErrResp(err.Error())
	}
	triggerAlertForNameAndDobMismatch(ctx, user, vgRes)
	addresses, err := s.fetchAddresses(ctx, user, req.GetVendor(), vgRes)
	if err != nil {
		logger.Error(ctx, "error while updating address", zap.String(logger.USER_ID, req.GetUserId()), zap.Error(err))
		return internalErrResp(err.Error())
	}
	logger.Debug(ctx, "fetched and parsed address from vendor successfully", zap.String(logger.USER_ID, req.GetUserId()))
	return &pb.GetCustomerDetailsResponse{
		Status:         rpc.StatusOk(),
		Addresses:      addresses,
		User:           user,
		Gender:         vgRes.GetGender(),
		CustomerName:   vgRes.GetCustomerName(),
		DateOfBirth:    vgRes.GetDateOfBirth(),
		OccupationType: vgRes.GetOccupationType(),
		Accounts:       vgRes.GetAccountList(),
	}, nil
}

// If user has changed the name or dob in KYC then we redirect the user to bank to update it in bank records. We can use this alert track such cases.
func triggerAlertForNameAndDobMismatch(ctx context.Context, user *pb.User, vgRes *vgPbCustomer.FetchCustomerDetailsResponse) {
	kycName := user.GetProfile().GetKycName()
	vendorName := vgRes.GetCustomerName()

	sanitizedKycName, err1 := gammanames.GetNameWithoutSpecialCharacters(kycName)
	sanitizedVendorName, err2 := gammanames.GetNameWithoutSpecialCharacters(vendorName)

	nameMismatch := false
	if err1 != nil || err2 != nil {
		logger.Error(ctx, "error sanitizing names for special character removal", zap.Error(err1), zap.Error(err2))
		// fallback to original logic if sanitization fails
		nameMismatch = !strings.EqualFold(kycName.ToString(), vendorName.ToString())
	} else {
		kycFullName := strings.ReplaceAll(sanitizedKycName.ToString(), " ", "")
		vendorFullName := strings.ReplaceAll(sanitizedVendorName.ToString(), " ", "")

		nameMismatch = !strings.EqualFold(kycFullName, vendorFullName)
	}

	switch {
	case nameMismatch:
		logger.Info(ctx, "KYC Name mismatch: User name does not match with vendor record (after removing special characters and spaces)")

	case vgRes.GetDateOfBirth() != nil && !datetime.DateEquals(user.GetProfile().GetDateOfBirth(), vgRes.GetDateOfBirth()):
		logger.Info(ctx, "DOB mismatch: User DOB does not match with vendor record")
	}
}

func (s *Service) storeGenderAndPinCodes(ctx context.Context, user *pb.User, vgRes *vgPbCustomer.FetchCustomerDetailsResponse) error {
	var (
		vgGender = vgRes.GetGender()
		permAddr = &postaladdress.PostalAddress{
			PostalCode: vgRes.GetPermanentAddress().GetPinCode(),
		}
		commAddr = &postaladdress.PostalAddress{
			PostalCode: vgRes.GetCommunicationAddress().GetPinCode(),
		}
	)

	var fieldUpdates []pb.UserFieldMask
	if user.GetProfile().GetKycGender() == types.Gender_GENDER_UNSPECIFIED && vgGender != types.Gender_GENDER_UNSPECIFIED {
		user.Profile.KycGender = vgGender
		fieldUpdates = append(fieldUpdates, pb.UserFieldMask_KYC_GENDER)
	}

	if user.GetProfile().GetAddresses()[types.AddressType_PERMANENT.String()].GetPostalCode() == "" && permAddr.GetPostalCode() != "" {
		if user.Profile.Addresses == nil {
			user.Profile.Addresses = map[string]*postaladdress.PostalAddress{}
		}
		user.Profile.Addresses[types.AddressType_PERMANENT.String()] = permAddr
		fieldUpdates = append(fieldUpdates, pb.UserFieldMask_ADDRESSES)
	}
	if user.GetProfile().GetAddresses()[types.AddressType_MAILING.String()].GetPostalCode() == "" && commAddr.GetPostalCode() != "" {
		if user.Profile.Addresses == nil {
			user.Profile.Addresses = map[string]*postaladdress.PostalAddress{}
		}
		user.Profile.Addresses[types.AddressType_MAILING.String()] = commAddr
		fieldUpdates = append(fieldUpdates, pb.UserFieldMask_ADDRESSES)
	}

	s.logMissingInFetchCustDetails(ctx, vgRes)
	if len(fieldUpdates) == 0 {
		return nil
	}
	if er := s.dao.UpdateUser(ctx, user, fieldUpdates); er != nil {
		logger.Error(ctx, "Error in updating gender in user profile", zap.Error(er))
		return er
	}
	logger.Info(ctx, fmt.Sprintf("updated user from vendor customer details: %v", fieldUpdates))
	return nil
}

func getChannelType(provenance pb.Provenance) (vgPbCustomer.ChannelType, error) {
	switch provenance {
	case pb.Provenance_APP:
		return vgPbCustomer.ChannelType_APP, nil
	case pb.Provenance_SHERLOCK:
		return vgPbCustomer.ChannelType_CC, nil
	default:
		return vgPbCustomer.ChannelType_CHANNEL_TYPE_UNSPECIFIED, fmt.Errorf("invalid provenance %s", provenance.String())
	}
}

// fetchAddresses converts the address fetched from vendor to user readable address.
// If we failed to convert or fetch the address from vendor for a given address type we will try to look for that address
// from the address stored with us in user profile.
func (s *Service) fetchAddresses(ctx context.Context, user *pb.User, vendor commonvgpb.Vendor, vgRes *vgPbCustomer.FetchCustomerDetailsResponse) (map[string]*postaladdress.PostalAddress, error) {
	storedAddresses := user.GetProfile().GetAddresses()
	vendorAddresses := make(map[string]*postaladdress.PostalAddress)

	addressTypes := []types.AddressType{types.AddressType_SHIPPING, types.AddressType_MAILING, types.AddressType_PERMANENT}
	for _, addressType := range addressTypes {
		switch addressType {
		case types.AddressType_SHIPPING:
			shippingAddress, err := s.convertVendorAddressToUserReadableFormatAddress(ctx, vendor, vgRes.GetShippingAddress())
			if err != nil {
				logger.Error(ctx, "failed to convert shipping address returned by vendor", zap.Error(err))
				continue
			}
			if shippingAddress != nil {
				logger.Debug(ctx, "fetched shipping address successfully", zap.String(logger.USER_ID, user.GetId()))
				vendorAddresses[addressType.String()] = shippingAddress
			} else {
				logger.Info(ctx, "empty shipping address from vendor", zap.String(logger.USER_ID, user.GetId()))
			}
		case types.AddressType_MAILING:
			mailingAddress, err := s.convertVendorAddressToUserReadableFormatAddress(ctx, vendor, vgRes.GetCommunicationAddress())
			if err != nil {
				logger.Error(ctx, "failed to convert mailing address", zap.Error(err))
				continue
			}
			if mailingAddress != nil {
				logger.Debug(ctx, "fetched mailing address successfully", zap.String(logger.USER_ID, user.GetId()))
				vendorAddresses[addressType.String()] = mailingAddress
			} else {
				logger.Info(ctx, "empty mailing address from vendor", zap.String(logger.USER_ID, user.GetId()))
			}
		case types.AddressType_PERMANENT:
			permanentAddress, err := s.convertVendorAddressToUserReadableFormatAddress(ctx, vendor, vgRes.GetPermanentAddress())
			if err != nil {
				logger.Error(ctx, "failed to convert permanent address", zap.Error(err))
				continue
			}
			if permanentAddress != nil {
				logger.Debug(ctx, "fetched permanent address successfully", zap.String(logger.USER_ID, user.GetId()))
				vendorAddresses[addressType.String()] = permanentAddress
			} else {
				logger.Info(ctx, "empty permanent address from vendor", zap.String(logger.USER_ID, user.GetId()))
			}
		default:
			logger.Error(ctx, fmt.Sprintf("invalid address type %s", addressType.String()))
		}
	}
	if storedAddresses != nil {
		for _, addressType := range addressTypes {
			_, ok1 := vendorAddresses[addressType.String()]
			// Failed to fetch address from vendor api response
			if !ok1 && addressType == types.AddressType_SHIPPING {
				storedAddress, ok := storedAddresses[addressType.String()]
				if !ok {
					logger.Info(ctx, fmt.Sprintf("address not stored in user profile for addressType %s", addressType.String()))
					continue
				}
				vendorAddresses[addressType.String()] = storedAddress
			}
		}
	}
	return vendorAddresses, nil
}

func (s *Service) convertVendorAddressToUserReadableFormatAddress(ctx context.Context, vendor commonvgpb.Vendor, address *vgPbCustomer.VendorAddress) (*postaladdress.PostalAddress, error) {
	if address.GetPinCode() == "" && address.GetCityCode() == "" && address.GetStateCode() == "" {
		// This will be for user's whose shipping address in not updated at federal's end.
		logger.Info(ctx, "address is empty")
		return nil, nil
	}
	if address.GetPinCode() == "" || address.GetCityCode() == "" || address.GetStateCode() == "" {
		return nil, fmt.Errorf("pin code %s or city code %s or state code %s cannot be empty", address.GetPinCode(), address.GetCityCode(), address.GetStateCode())
	}
	vendorArea, daoErr := s.vendorAreaCodesDao.GetCityAndStateFromCodes(ctx, vendor, address.GetCityCode(), address.GetStateCode())
	if daoErr != nil && !storagev2.IsRecordNotFoundError(daoErr) {
		return nil, fmt.Errorf("failed to fetch city and state from dao %w", daoErr)
	}
	if storagev2.IsRecordNotFoundError(daoErr) {
		logger.Info(ctx, "failed to fetch city and state from vendor dao, fetching using pincode", zap.Error(daoErr))
		vendorAreaFromPincode, err := s.getCityAndStateForPinCode(ctx, address.GetPinCode())
		if err != nil {
			return nil, fmt.Errorf("failed to fetch city and state using pincode %w", err)
		}
		vendorArea = vendorAreaFromPincode
	}

	country, err := getCountryFromCountryCode(address.GetCountryCode())
	if err != nil {
		return nil, fmt.Errorf("failed to get country from country code %w", err)
	}
	return &postaladdress.PostalAddress{
		PostalCode:         address.GetPinCode(),
		RegionCode:         country,
		AdministrativeArea: vendorArea.State,
		Locality:           vendorArea.City,
		AddressLines:       []string{address.GetAddressLine1(), address.GetAddressLine2()},
	}, nil

}

func (s *Service) getCityAndStateForPinCode(ctx context.Context, pinCode string) (*user.VendorArea, error) {
	if pinCode == "" {
		return nil, fmt.Errorf("empty pin code %w", epifierrors.ErrInvalidArgument)
	}
	res, err := s.onbClient.GetPinCodeDetails(ctx, &onbPb.GetPinCodeDetailsRequest{
		PinCode: pinCode,
		FieldMask: []onbPb.PinCodeField{
			onbPb.PinCodeField_PIN_CODE_FIELD_STATE, onbPb.PinCodeField_PIN_CODE_FIELD_DISTRICT,
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, fmt.Errorf("error getting pincode details from onboarding service %w", rpcErr)
	}
	if len(res.GetDetails()) == 0 {
		return nil, fmt.Errorf("got empty details from onboarding service %w", epifierrors.ErrRecordNotFound)
	}
	return &user.VendorArea{
		City:  res.GetDetails()[0].GetDistrict(),
		State: res.GetDetails()[0].GetState(),
	}, nil
}

func getCountryFromCountryCode(countryCode string) (string, error) {
	switch countryCode {
	case "IN":
		return "INDIA", nil
	default:
		return "", fmt.Errorf("invalid country code %s", countryCode)
	}
}

// GetDeviceAuthDetails fetches device details for a given actorId
// Returns deviceId, deviceToken, userProfileId and error
func (s *Service) getDeviceAuthDetails(ctx context.Context, actorId string) (deviceId,
	deviceToken, userProfileId string, err error) {
	getDeviceAuthResponse, err := s.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "error in getting device details", zap.Error(err))
		return "", "", "", err
	}
	if !getDeviceAuthResponse.Status.IsSuccess() {
		if !getDeviceAuthResponse.Status.IsRecordNotFound() {
			logger.Error(ctx, "unexpected status code in get device auth", zap.String(logger.STATUS, getDeviceAuthResponse.GetStatus().String()))
		}
		return "", "", "", fmt.Errorf("authClient.GetDeviceAuth() failed for actorId: %s, status: %s",
			actorId, getDeviceAuthResponse.GetStatus())
	}
	return getDeviceAuthResponse.GetDevice().GetDeviceId(),
		getDeviceAuthResponse.GetDeviceToken(),
		getDeviceAuthResponse.GetUserProfileId(),
		nil
}

func (s *Service) RefreshVKYCStatus(ctx context.Context, request *pb.RefreshVKYCStatusRequest) (*pb.RefreshVKYCStatusResponse, error) {
	vendorSavingsAccount, err := s.getSavingsAccount(ctx, request.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting savings account from vendor", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return &pb.RefreshVKYCStatusResponse{
			Status: rpc.StatusInternal(),
		}, err
	}
	// do not proceed if savings account type is not infinity
	if vendorSavingsAccount.GetSchemeType() != vgDepositPb.SchemeType_SAVINGS_INFINITY {
		logger.Error(ctx, fmt.Sprintf("cannot refresh vkyc status, vendor scheme type %v", vendorSavingsAccount.GetSchemeType().String()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		return &pb.RefreshVKYCStatusResponse{
			Status: rpc.StatusInternal(),
		}, err
	}
	approveVKYCResp, err := s.vkycClient.ApproveVKYC(ctx, &vkycPb.ApproveVKYCRequest{
		ActorId: request.GetActorId(),
	})
	if err = epifigrpc.RPCError(approveVKYCResp, err); err != nil {
		logger.Error(ctx, "error in approving vkyc", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return &pb.RefreshVKYCStatusResponse{
			Status: rpc.StatusInternal(),
		}, err
	}
	return &pb.RefreshVKYCStatusResponse{
		Status: rpc.StatusOk(),
	}, err
}

// getSavingsAccount calls vendor API and return savings account if found
func (s *Service) getSavingsAccount(ctx context.Context, actorId string) (*vgDepositPb.ListAccountResponse_Account, error) {
	// fetch device details for device_id & device_token
	authResp, err := s.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(authResp, err); err != nil {
		logger.Error(ctx, "error in getting device info from auth service for actor", zap.String("actor_id", actorId), zap.Error(err))
		return nil, err
	}

	bcResp, err := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(bcResp, err); err != nil {
		logger.Error(ctx, "error in getting bank customer", zap.Error(err))
		return nil, err
	}

	userResp, err := s.GetUser(ctx, &pb.GetUserRequest{
		Identifier: &pb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(userResp, err); err != nil {
		logger.Error(ctx, "error in getting user", zap.Error(err))
		return nil, err
	}

	requestId := idgen.FederalRandomDigitsSequence(listAccountPrefix, 5)
	resp, err := s.vgDepositClient.ListAccount(ctx, &vgDepositPb.ListAccountRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Auth: &header.Auth{
			DeviceId:    authResp.GetDevice().GetDeviceId(),
			DeviceToken: authResp.GetDeviceToken(),
		},
		RequestId:   requestId,
		CustomerId:  bcResp.GetBankCustomer().GetVendorCustomerId(),
		PhoneNumber: userResp.GetUser().GetProfile().GetPhoneNumber(),
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error in getting list of accounts from vendor", zap.Error(te))
		return nil, te
	}
	for _, account := range resp.Accounts {
		// currently we have only one savings account
		if account.GetAccountType() == accountsPb.Type_SAVINGS {
			return account, nil
		}
	}
	return nil, fmt.Errorf("savings account not found")
}

func (s *Service) getUsersByIds(ctx context.Context, userIds []string, unscoped bool) ([]*pb.User, error) {
	var (
		fetchedUsers []*pb.User
		err          error
	)
	fetchedUsers, err = s.dao.GetUsers(ctx, userIds, &model.GetUsersParams{
		Unscoped: unscoped,
	})
	if err != nil {
		return nil, err
	}
	return fetchedUsers, nil
}

func (s *Service) getUsersByEmailIds(ctx context.Context, userEmailIds []string) ([]*pb.User, error) {
	var (
		fetchedUsers []*pb.User
		err          error
	)
	fetchedUsers, err = s.dao.GetUsersByEmailIds(ctx, userEmailIds)
	if err != nil {
		return nil, err
	}
	return fetchedUsers, nil
}

// getUsersByPans fetches users by pan numbers in batch. Missing users are silently
// ignored (ErrRecordNotFound), but other errors are returned immediately.
func (s *Service) getUsersByPans(ctx context.Context, userPans []string, unscoped bool) ([]*pb.User, error) {
	var (
		fetchedUsers []*pb.User
	)
	for _, pan := range userPans {
		fetchedUsersByPan, err := s.dao.GetUsersByPAN(ctx, pan, &model.GetUsersByPANParams{
			Unscoped: unscoped,
		})

		// if error is not record not found, return error
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, err
		}

		// checking if error is nil, because if error is record not found, we don't want to append user to fetchedUsers
		if err == nil {
			fetchedUsers = append(fetchedUsers, fetchedUsersByPan...)
		}
	}
	return fetchedUsers, nil
}

// getUsersByPhone fetches users by phone numbers in batch. Missing users are silently
// ignored (ErrRecordNotFound), but other errors are returned immediately.
func (s *Service) getUsersByPhone(ctx context.Context, userPhn []*commontypes.PhoneNumber) ([]*pb.User, error) {
	var (
		fetchedUsers []*pb.User
	)
	for _, phone := range userPhn {
		user, err := s.dao.GetUserByPhoneNumber(ctx, phone)

		// if error is not record not found, return error
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, err
		}

		// checking if error is nil, because if error is record not found, we don't want to append user to fetchedUsers
		if err == nil {
			fetchedUsers = append(fetchedUsers, user)
		}
	}
	return fetchedUsers, nil
}

func (s *Service) GetVendorFormattedAddressForClient(ctx context.Context, address *postaladdress.PostalAddress, vendor commonvgpb.Vendor) (*postaladdress.PostalAddress, error) {
	formattedAddress := &postaladdress.PostalAddress{}
	switch vendor {
	case commonvgpb.Vendor_FEDERAL_BANK:
		if err := copier.Copy(formattedAddress, address); err != nil {
			logger.Error(ctx, "failed to copy address", zap.Error(err))
			return nil, fmt.Errorf("failed to copy address: %w", err)
		}

		formattedAddressLines, err := federalPkg.GetAddressLinesForVendor(address.GetAddressLines(), federalPkg.CIF_MAX_NUMBER_OF_LINES, federalPkg.CIF_MAX_LINE_LENGTH)
		if err != nil {
			logger.Error(ctx, "failed to get formatted address lines", zap.Error(err))
			return nil, fmt.Errorf("failed to get formatted address lines: %w", err)
		}
		formattedAddress.AddressLines = formattedAddressLines

		return formattedAddress, nil
	default:
		logger.Error(ctx, "Unknown vendor passed to GetVendorFormattedAddress")
		return formattedAddress, errors.New("Unknown vendor")
	}
}

func (s *Service) UpsertUserDeviceProperties(ctx context.Context, req *pb.UpsertUserDevicePropertyRequest) (*pb.UpsertUserDevicePropertyResponse, error) {
	if req.GetActorId() == "" {
		return &pb.UpsertUserDevicePropertyResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid actor id or phone number"),
		}, nil
	}

	var userDevicePropertiesToUpdate []*pb.UserDeviceProperty

	txnErr := s.userPropertiesTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		currProps, err := s.userDevicePropDao.GetUserDevicePropertiesByActorId(txnCtx, req.GetActorId(), []types.DeviceProperty{})
		// ignoring record not found error as the user UpsertUserDeviceProperties rpc
		// may be called for the first time for the actor
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in getting user device properties", zap.Error(err))
			return err
		}
		userDevicePropertiesToUpdate = findUserDevicePropertiesToUpdate(req.GetDevicePropertyValueToUpdate(), currProps, req.GetActorId(), req.GetPhoneNumber())
		if len(userDevicePropertiesToUpdate) == 0 {
			logger.Debug(txnCtx, "no new user device properties to update")
			return nil
		}

		devicePropertyList := getCommonDeviceProperties(userDevicePropertiesToUpdate, currProps)

		// soft deleting only if devicePropertyList has some values
		if len(devicePropertyList) > 0 {
			if deleteErr := s.userDevicePropDao.SoftDeleteUserDeviceProperties(txnCtx, req.ActorId, devicePropertyList); deleteErr != nil {
				logger.Error(ctx, "error in soft deleting user device properties",
					zap.Error(deleteErr))
				return deleteErr
			}
			s.PublishDevicePropertiesUpdateAsync(ctx, req.GetActorId(), devicePropertyList)
		}

		if createErr := s.userDevicePropDao.Create(txnCtx, userDevicePropertiesToUpdate); createErr != nil {
			logger.Error(ctx, "error in inserting user device properties", zap.Error(createErr))
			return createErr
		}

		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error in transactional block in UpsertUserDeviceProperty", zap.Error(txnErr))
		return nil, txnErr
	}
	return &pb.UpsertUserDevicePropertyResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// returns the device property in arr of a given propertyType, returns nil if it does not exist
func findUserDevicePropertyByPropertyType(arr []*pb.UserDeviceProperty, propertyType types.DeviceProperty) *types.DevicePropertyKeyValuePair {
	for _, deviceProperty := range arr {
		if deviceProperty.GetDeviceProperty() == propertyType {
			return &types.DevicePropertyKeyValuePair{
				DeviceProperty: propertyType,
				PropertyValue:  deviceProperty.GetPropertyValue(),
			}
		}
	}
	return nil
}

// returns array of UserDeviceProperty that needs to be updated i.e. the properties that are different from the existing
// values or that do not exist in userDevicePropArray
func findUserDevicePropertiesToUpdate(devicePropertyValuesToUpdate []*types.DevicePropertyKeyValuePair, userDevicePropArray []*pb.UserDeviceProperty, actorId string, phone string) []*pb.UserDeviceProperty {
	userDevicePropertiesToUpdate := []*pb.UserDeviceProperty{}
	for _, devicePropertyValue := range devicePropertyValuesToUpdate {
		existingDeviceProp := findUserDevicePropertyByPropertyType(userDevicePropArray, devicePropertyValue.GetDeviceProperty())
		if !isUserDevicePropertyValueEqual(devicePropertyValue, existingDeviceProp) {
			newUserDeviceProperty := &pb.UserDeviceProperty{
				ActorId:        actorId,
				PhoneNumber:    phone,
				DeviceProperty: devicePropertyValue.GetDeviceProperty(),
				PropertyValue:  devicePropertyValue.GetPropertyValue(),
			}
			userDevicePropertiesToUpdate = append(userDevicePropertiesToUpdate, newUserDeviceProperty)
		}
	}
	return userDevicePropertiesToUpdate
}

// finds the device property types that exist in both newUserDeviceProperties and existingDeviceProperties
func getCommonDeviceProperties(newUserDeviceProperties []*pb.UserDeviceProperty, existingDeviceProperties []*pb.UserDeviceProperty) []types.DeviceProperty {
	res := []types.DeviceProperty{}

	for _, deviceProp := range newUserDeviceProperties {
		if findUserDevicePropertyByPropertyType(existingDeviceProperties, deviceProp.GetDeviceProperty()) != nil {
			res = append(res, deviceProp.GetDeviceProperty())
		}
	}
	return res
}

func isUserDevicePropertyValueEqual(val1, val2 *types.DevicePropertyKeyValuePair) bool {
	return val1.GetDeviceProperty() == val2.GetDeviceProperty() &&
		proto.Equal(val1.GetPropertyValue(), val2.GetPropertyValue())
}

func (s *Service) GetUserDeviceProperties(ctx context.Context, req *pb.GetUserDevicePropertiesRequest) (*pb.GetUserDevicePropertiesResponse, error) {
	if req.GetActorId() == "" {
		return &pb.GetUserDevicePropertiesResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid actor id or phone number"),
		}, nil
	}

	if len(req.GetPropertyTypes()) == 0 {
		return &pb.GetUserDevicePropertiesResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("property types array can not be empty"),
		}, nil
	}

	props, err := s.userDevicePropDao.GetUserDevicePropertiesByActorId(ctx, req.GetActorId(), req.GetPropertyTypes())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &pb.GetUserDevicePropertiesResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error in getting user device properties for actor", zap.Error(err))
		return nil, err
	}

	return &pb.GetUserDevicePropertiesResponse{
		Status:                 rpc.StatusOk(),
		UserDevicePropertyList: props,
	}, nil
}

func (s *Service) SetUserPreferences(ctx context.Context, req *pb.SetUserPreferencesRequest) (*pb.SetUserPreferencesResponse, error) {
	if validationErr := req.Validate(); validationErr != nil {
		return &pb.SetUserPreferencesResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(validationErr.Error()),
		}, nil
	}

	var (
		preferenceTypesList []pb.PreferenceType
		userPreferencesList []*pb.UserPreference
	)
	// req.Preferences contains {type,value} pairs.
	// We construct UserPreference list out of this by adding actorId
	for _, pref := range req.GetPreferences() {
		preferenceTypesList = append(preferenceTypesList, pref.GetPreferenceType())
		userPreferencesList = append(userPreferencesList, &pb.UserPreference{
			ActorId:         req.GetActorId(),
			PreferenceType:  pref.GetPreferenceType(),
			PreferenceValue: pref.GetPreferenceValue(),
		})
	}

	if txnErr := storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		if softDelErr := s.userPrefDao.BatchSoftDelete(txnCtx, req.GetActorId(), preferenceTypesList); softDelErr != nil && !errors.Is(softDelErr, epifierrors.ErrRecordNotFound) {
			return errors.Wrap(softDelErr, "error soft deleting existing preference")
		}

		if _, createErr := s.userPrefDao.BatchCreate(txnCtx, userPreferencesList); createErr != nil {
			return errors.Wrap(createErr, "error creating preference")
		}
		return nil
	}); txnErr != nil {
		if storagev2.IsDuplicateRowError(txnErr) {
			return &pb.SetUserPreferencesResponse{
				Status: rpc.StatusAlreadyExistsWithDebugMsg(txnErr.Error()),
			}, nil
		}
		logger.Error(ctx, "error while commit txn", zap.Error(txnErr))
		return &pb.SetUserPreferencesResponse{
			Status: rpc.StatusInternalWithDebugMsg("db update error while adding user preferences"),
		}, nil
	}

	return &pb.SetUserPreferencesResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) GetUserPreferences(ctx context.Context, req *pb.GetUserPreferencesRequest) (*pb.GetUserPreferencesResponse, error) {
	if validationErr := req.Validate(); validationErr != nil {
		return &pb.GetUserPreferencesResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(validationErr.Error()),
		}, nil
	}

	prefs, err := s.userPrefDao.GetPreferencesForUser(ctx, req.GetActorId(), req.GetPreferenceTypes())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &pb.GetUserPreferencesResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "db error in getting user preferences for actor", zap.Error(err))
		return &pb.GetUserPreferencesResponse{
			Status: rpc.StatusInternalWithDebugMsg("db error in getting user preferences for actor"),
		}, nil
	}

	return &pb.GetUserPreferencesResponse{
		Status:          rpc.StatusOk(),
		UserPreferences: prefs,
	}, nil
}

func (s *Service) publishUpdateAccessRevokeEvent(ctx context.Context, userId string, details *pb.AccessRevokeDetails) {
	s.publishAccessRevokeCooldown(ctx, userId, details)

	event := &pb.AccessRevokeUpdateEvent{
		UserId:              userId,
		AccessRevokeDetails: details,
	}
	msgId, err := s.accessRevokeUpdatePublisher.Publish(ctx, event)
	if err != nil {
		logger.Error(ctx, "error in publishing access revoke update event", zap.Error(err), zap.String(logger.USER_ID, userId))
		return
	}
	logger.Info(ctx, "successfully published the access revoke update event", zap.String(logger.QUEUE_MESSAGE_ID, msgId))
}

func (s *Service) publishAccessRevokeCooldown(ctx context.Context, userId string, details *pb.AccessRevokeDetails) {
	if !details.GetReason().IsAccessRevokedWithCooldown() {
		return
	}
	// Test_AccessRevokeConfigValidation ensures that sync between IsAccessRevokedWithCooldown and AccessRevokeCooldownDuration config
	cooldownDuration := s.dynConf.AccessRevokeCooldownDuration().Get(details.GetReason().String())
	msgId, err := s.processAccessRevokeCooldownDelayPublisher.PublishWithDelay(ctx, &accessRevokeConsumerPb.ProcessAccessRevokeCooldownRequest{
		UserId:             userId,
		TimestampToProcess: timestampPb.New(timestampPb.Now().AsTime().Add(cooldownDuration)),
		Reason:             details.GetReason(),
	}, cooldownDuration)
	if err != nil {
		logger.Error(ctx, "error in publishing access revoke cooldown request", zap.Error(err), zap.String(logger.USER_ID, userId))
		return
	}
	logger.Info(ctx, "successfully published access revoke cooldown request", zap.String(logger.QUEUE_MESSAGE_ID, msgId), zap.String(logger.USER_ID, userId))
}

func (s *Service) sendAccountBlockedPN(ctx context.Context, userId string) error {
	commonFields := &fcmPb.CommonTemplateFields{
		Title:                   "⚠️ Account disabled due to security reasons",
		Body:                    "You will get an <NAME_EMAIL> with all the details. Please check and respond to re-activate your account",
		NotificationReferenceId: userId,
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
		},
	}

	messageRequest := &commsPb.SendMessageRequest{
		Medium: commsPb.Medium_NOTIFICATION,
		Type:   commsPb.QoS_BEST_EFFORT,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{
			UserId: userId,
		},
		Message: &commsPb.SendMessageRequest_Notification{
			Notification: &commsPb.NotificationMessage{
				Priority: commsPb.NotificationPriority_NORMAL,
				AndroidConfig: &commsPb.AndroidConfig{
					NotificationDelivery: commsPb.DeliveryQoS_IMMEDIATE,
				},
				Notification: &fcmPb.Notification{
					NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
					NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
						SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
							CommonTemplateFields: commonFields,
						},
					},
				},
			},
		},
		CampaignName: commsPb.CampaignName_CAMPAIGN_NAME_ONB_ACBLOCKED_D0_PN,
	}

	res, err := s.commsClient.SendMessage(ctx, messageRequest)
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "failed to send push notification", zap.String(logger.ENTITY_ID, userId))
		return err
	}

	return nil
}

func (s *Service) getBankCustomer(ctx context.Context, vendor commonvgpb.Vendor, userId string) (*bankcust.BankCustomer, error) {
	bcRes, errResp := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: vendor,
		Identifier: &bankcust.GetBankCustomerRequest_UserId{
			UserId: userId,
		},
	})
	if err := epifigrpc.RPCError(bcRes, errResp); err != nil {
		if bcRes.GetStatus().GetCode() == rpc.StatusRecordNotFound().GetCode() {
			logger.Info(ctx, "bank customer was not found for user", zap.String(logger.USER_ID, userId))
			return nil, nil
		}
		logger.Error(ctx, "error while fetching bank customer by user id", zap.String(logger.USER_ID, userId), zap.Error(err))
		return nil, err
	}
	return bcRes.GetBankCustomer(), nil
}

func (s *Service) GetAccessRevokeDetailsChangeFeed(ctx context.Context, req *pb.GetAccessRevokeDetailsChangeFeedRequest) (*pb.GetAccessRevokeDetailsChangeFeedResponse, error) {
	changeFeeds, err := s.changeFeed.GetChangeFeed(ctx, &changefeed.GetChangeFeedRequest{
		Filters: &changefeed.GetChangeFeedFilters{
			TableName:     "users",
			RowIdentifier: req.GetUserId(),
			ColumnName:    "AccessRevokeDetails",
			Limit:         int(req.GetLimit()),
		},
	})
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			return &pb.GetAccessRevokeDetailsChangeFeedResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching changefeed for access revoke details of user", zap.String(logger.USER_ID, req.GetUserId()))
		return &pb.GetAccessRevokeDetailsChangeFeedResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	var (
		changes                     []*pb.AccessRevokeDetailsChange
		mapTimeStampAccessRevokeOld = make(map[time.Time]*pb.AccessRevokeDetails)
		mapTimeStampAccessRevokeNew = make(map[time.Time]*pb.AccessRevokeDetails)
		updatedAtKeys               []time.Time
	)
	for _, changeFeed := range changeFeeds {
		updatedAt := changeFeed.UpdatedAt
		if _, ok := mapTimeStampAccessRevokeOld[updatedAt]; !ok {
			mapTimeStampAccessRevokeOld[updatedAt] = &pb.AccessRevokeDetails{}
		}
		if _, ok := mapTimeStampAccessRevokeNew[updatedAt]; !ok {
			mapTimeStampAccessRevokeNew[updatedAt] = &pb.AccessRevokeDetails{}
		}
		updatedField := strings.Join(changeFeed.ChangeLog.Path, "->")
		oldValue := changeFeed.ChangeLog.From
		newValue := changeFeed.ChangeLog.To
		isUpdated := false
		switch updatedField {
		case "AccessRevokeStatus":
			typedOldValue, errParse := strconv.Atoi(fmt.Sprintf("%v", oldValue))
			if errParse != nil {
				if oldValue == nil {
					typedOldValue = 0
				} else {
					logger.Error(ctx, "error while converting old value to access revoke status", zap.Error(errParse))
					return &pb.GetAccessRevokeDetailsChangeFeedResponse{
						Status: rpc.StatusInternal(),
					}, nil
				}
			}
			typedNewValue, errParse := strconv.Atoi(fmt.Sprintf("%v", newValue))
			if errParse != nil {
				logger.Error(ctx, "error while converting new value to access revoke status", zap.Error(errParse))
				return &pb.GetAccessRevokeDetailsChangeFeedResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			mapTimeStampAccessRevokeOld[updatedAt].AccessRevokeStatus = pb.AccessRevokeStatus(typedOldValue)
			mapTimeStampAccessRevokeNew[updatedAt].AccessRevokeStatus = pb.AccessRevokeStatus(typedNewValue)
			isUpdated = true
		case "Reason":
			typedOldValue, errParse := strconv.Atoi(fmt.Sprintf("%v", oldValue))
			if errParse != nil {
				if oldValue == nil {
					typedOldValue = 0
				} else {
					logger.Error(ctx, "error while converting old value to access revoke reason", zap.Error(errParse))
					return &pb.GetAccessRevokeDetailsChangeFeedResponse{
						Status: rpc.StatusInternal(),
					}, nil
				}
			}
			typedNewValue, errParse := strconv.Atoi(fmt.Sprintf("%v", newValue))
			if errParse != nil {
				logger.Error(ctx, "error while converting new value to access revoke reason", zap.Error(errParse))
				return &pb.GetAccessRevokeDetailsChangeFeedResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			mapTimeStampAccessRevokeOld[updatedAt].Reason = pb.AccessRevokeReason(typedOldValue)
			mapTimeStampAccessRevokeNew[updatedAt].Reason = pb.AccessRevokeReason(typedNewValue)
			isUpdated = true
		case "RestoreReason":
			typedOldValue, errParse := strconv.Atoi(fmt.Sprintf("%v", oldValue))
			if errParse != nil {
				logger.Error(ctx, "error while converting old value to access restore reason", zap.Error(errParse))
				return &pb.GetAccessRevokeDetailsChangeFeedResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			typedNewValue, errParse := strconv.Atoi(fmt.Sprintf("%v", newValue))
			if errParse != nil {
				logger.Error(ctx, "error while converting new value to access restore reason", zap.Error(errParse))
				return &pb.GetAccessRevokeDetailsChangeFeedResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			mapTimeStampAccessRevokeOld[updatedAt].RestoreReason = pb.AccessRestoreReason(typedOldValue)
			mapTimeStampAccessRevokeNew[updatedAt].RestoreReason = pb.AccessRestoreReason(typedNewValue)
			isUpdated = true
		case "Remarks":
			typedOldValue, ok := goutils.ToTypeWithOk[string](oldValue)
			if !ok {
				logger.Error(ctx, "error while converting old value to remark")
				return &pb.GetAccessRevokeDetailsChangeFeedResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			typedNewValue, ok := goutils.ToTypeWithOk[string](newValue)
			if !ok {
				logger.Error(ctx, "error while converting new value to remark")
				return &pb.GetAccessRevokeDetailsChangeFeedResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			mapTimeStampAccessRevokeOld[updatedAt].Remarks = typedOldValue
			mapTimeStampAccessRevokeNew[updatedAt].Remarks = typedNewValue
			isUpdated = true
		case "UpdatedBy":
			typedOldValue, ok := goutils.ToTypeWithOk[string](oldValue)
			if !ok {
				logger.Error(ctx, "error while converting old value to updated by")
				return &pb.GetAccessRevokeDetailsChangeFeedResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			typedNewValue, ok := goutils.ToTypeWithOk[string](newValue)
			if !ok {
				logger.Error(ctx, "error while converting new value to updated by")
				return &pb.GetAccessRevokeDetailsChangeFeedResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			mapTimeStampAccessRevokeOld[updatedAt].UpdatedBy = typedOldValue
			mapTimeStampAccessRevokeNew[updatedAt].UpdatedBy = typedNewValue
			isUpdated = true
		}
		if !lo.ContainsBy(updatedAtKeys, func(x time.Time) bool {
			return reflect.DeepEqual(updatedAt, x)
		}) && isUpdated {
			updatedAtKeys = append(updatedAtKeys, updatedAt)
		}
	}
	for _, key := range updatedAtKeys {
		change := &pb.AccessRevokeDetailsChange{
			OldValue:  mapTimeStampAccessRevokeOld[key],
			NewValue:  mapTimeStampAccessRevokeNew[key],
			UpdatedAt: timestampPb.New(key),
		}
		changes = append(changes, change)
	}
	return &pb.GetAccessRevokeDetailsChangeFeedResponse{
		Status:     rpc.StatusOk(),
		ChangeFeed: changes,
	}, nil
}

func (s *Service) UpdateAccessRevokeDetails(ctx context.Context, req *pb.UpdateAccessRevokeDetailsRequest) (*pb.UpdateAccessRevokeDetailsResponse, error) {
	if !isAccessRevokeDetailsValid(req.GetAccessRevokeDetails()) {
		logger.Info(ctx, "invalid argument in UpdateAccessRevokeDetails rpc call", zap.String("access_revoke_details", req.GetAccessRevokeDetails().String()))
		return &pb.UpdateAccessRevokeDetailsResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	var user *pb.User
	var err error
	user, err = s.getUserFromAccessRevokeIdentifier(ctx, req.GetUserIdentifier())
	if err != nil {
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			return &pb.UpdateAccessRevokeDetailsResponse{
				Status: rpc.StatusInvalidArgument(),
			}, nil
		}
		return &pb.UpdateAccessRevokeDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	switch {
	// send sms and notification stating "account disabled due to security reasons" if reason is due to fraudulent activities
	case req.GetAccessRevokeDetails().GetAccessRevokeStatus() == pb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED &&
		req.GetAccessRevokeDetails().GetReason().IsAccessRevokeReasonFraudulent():
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			s.publishRiskyActorEvent(epificontext.CloneCtx(ctx), user.GetId())
		})
		if req.GetTriggerUserComms() {
			_ = s.sendAccountBlockedNotifications(ctx, user.GetId(), user.GetProfile())
		}
	// send sms in case of unblocking (and not credit-freeze). In case of credit-freeze, savings service sets up in-app banners
	case req.GetAccessRevokeDetails().GetAccessRevokeStatus() == pb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED &&
		req.GetAccessRevokeDetails().GetRestoreReason() != pb.AccessRestoreReason_ACCESS_RESTORE_REASON_CREDIT_FREEZE:
		if req.GetTriggerUserComms() {
			_ = s.sendAccountUnblockedNotifications(ctx, user.GetProfile(), user.GetId())
		}
	}

	if err = s.dao.UpdateUser(ctx, &pb.User{
		Id:                  user.GetId(),
		AccessRevokeDetails: req.GetAccessRevokeDetails(),
	}, []pb.UserFieldMask{
		pb.UserFieldMask_ACCESS_REVOKE_DETAILS,
	}); err != nil {
		logger.Error(ctx, "error in updating user", zap.Error(err))
		return &pb.UpdateAccessRevokeDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	s.publishUpdateAccessRevokeEvent(ctx, user.GetId(), req.GetAccessRevokeDetails())

	return &pb.UpdateAccessRevokeDetailsResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) getUserFromAccessRevokeIdentifier(ctx context.Context, identifier *pb.UpdateAccessRevokeDetailsRequest_UserIdentifier) (*pb.User, error) {
	switch identifier.GetIdentifier().(type) {
	case *pb.UpdateAccessRevokeDetailsRequest_UserIdentifier_UserId:
		user, err := s.dao.GetUser(ctx, identifier.GetUserId(), nil)
		if err != nil {
			logger.Error(ctx, "error while getting user by id", zap.Error(err), zap.String(logger.USER_ID, identifier.GetUserId()))
			return nil, fmt.Errorf("error while getting user by id: %v", err)
		}
		return user, nil
	case *pb.UpdateAccessRevokeDetailsRequest_UserIdentifier_ActorId:
		user, err := s.GetUserByActorID(ctx, identifier.GetActorId())
		logger.Error(ctx, "error while getting user by id", zap.Error(err), zap.String(logger.ACTOR_ID_V2, identifier.GetActorId()))
		if err != nil {
			return nil, fmt.Errorf("error while getting user by actor_id: %v", err)
		}
		return user, nil
	default:
		return nil, epifierrors.ErrInvalidArgument
	}
}

func (s *Service) ensureGenderAndPinCodesPopulated(ctx context.Context, user *pb.User, wantGender bool, wantPincodes bool) (*pb.User, error) {
	var needVendorData bool
	isNrRes, err := s.IsNonResidentUser(ctx, &pb.IsNonResidentUserRequest{
		Identifier: &pb.IsNonResidentUserRequest_PhoneNumber{
			PhoneNumber: user.GetProfile().GetPhoneNumber(),
		},
	})
	if rpcErr := epifigrpc.RPCError(isNrRes, err); rpcErr != nil {
		logger.Error(ctx, "error in IsNonResidentUser API", zap.Error(rpcErr))
		return nil, rpcErr
	}
	if isNrRes.GetIsNonResidentUser().ToBool() {
		logger.Info(ctx, "ignoring populating gender and pin codes for NR users")
		return user, nil
	}
	if wantPincodes &&
		(user.GetProfile().GetAddresses()[types.AddressType_PERMANENT.String()].GetPostalCode() == "" ||
			user.GetProfile().GetAddresses()[types.AddressType_MAILING.String()].GetPostalCode() == "") {
		needVendorData = true
	}
	if wantGender &&
		user.GetProfile().GetKycGender() == types.Gender_GENDER_UNSPECIFIED {
		needVendorData = true
	}
	if !needVendorData {
		return user, nil
	}
	logger.Info(ctx, "making vendor call to store gender or pincode", zap.String(logger.ACTOR_ID_V2, user.GetActorId()))
	vendorCustRes, err := s.getVendorCustomerDetails(ctx, commonvgpb.Vendor_FEDERAL_BANK, pb.Provenance_APP, user.GetActorId(), user)
	if err != nil {
		return nil, err
	}
	if err = s.storeGenderAndPinCodes(ctx, user, vendorCustRes); err != nil {
		return nil, err
	}
	return s.dao.GetUser(ctx, user.GetId(), nil)
}

func isAccessRevokeDetailsValid(details *pb.AccessRevokeDetails) bool {
	return !(details.GetAccessRevokeStatus() == pb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNSPECIFIED ||
		(details.GetAccessRevokeStatus() == pb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED && details.GetReason() == pb.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED) ||
		(details.GetAccessRevokeStatus() == pb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_UNBLOCKED && details.GetRestoreReason() == pb.AccessRestoreReason_ACCESS_RESTORE_REASON_UNSCPECIFIED) ||
		(details.GetReason() == pb.AccessRevokeReason_ACCESS_REVOKE_REASON_OTHER && details.GetRemarks() == "") ||
		(details.GetRestoreReason() == pb.AccessRestoreReason_ACCESS_RESTORE_REASON_OTHERS && details.GetRemarks() == ""))
}

func (s *Service) getVendorCustomerDetails(ctx context.Context, vendor commonvgpb.Vendor, provenance pb.Provenance, actorId string, user *pb.User) (*vgPbCustomer.FetchCustomerDetailsResponse, error) {
	bcRes, bcErr := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: vendor,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(bcRes, bcErr); rpcErr != nil && !bcRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in get bank customer", zap.Error(rpcErr))
		return nil, rpcErr
	}
	customerId := bcRes.GetBankCustomer().GetVendorCustomerId()
	if customerId == "" && bcRes.GetBankCustomer().GetDedupeInfo().GetVendorCustomerId() != "" {
		customerId = bcRes.GetBankCustomer().GetDedupeInfo().GetVendorCustomerId()
	}
	if customerId == "" {
		return nil, epifierrors.ErrRecordNotFound
	}

	deviceId, deviceToken, userProfileId, err := s.getDeviceAuthDetails(ctx, actorId)
	if err != nil {
		return nil, err
	}

	requestId := idgen.FederalRandomDigitsSequence(idgen.FederalFetchCustomerDetailsPrefix, 5)

	channelType, err := getChannelType(provenance)
	if err != nil {
		logger.Error(ctx, "error while fetching channel type", zap.Error(err))
		return nil, err
	}
	vgRes, err := s.vgClient.FetchCustomerDetails(ctx, &vgPbCustomer.FetchCustomerDetailsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendor,
		},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			CustomerId:    customerId,
		},
		RequestId:   requestId,
		PhoneNumber: user.GetProfile().GetPhoneNumber(),
		ChannelType: channelType,
	})
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		logger.Error(ctx, "error in VG response for fetch customer details", zap.String(logger.REQUEST_ID, requestId), zap.Error(te))
		return nil, vendorErr
	}

	return vgRes, nil
}

func (s *Service) PublishDevicePropertiesUpdateAsync(ctx context.Context, actorId string, list []types.DeviceProperty) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		if _, err := s.userDevicePropertiesUpdatePublisher.Publish(ctx, &userEventPb.UserDevicePropertyUpdateEvent{
			ActorId:           actorId,
			UpdatedProperties: list,
		}); err != nil {
			logger.Error(ctx, "error publishing event for user fresh app install", zap.Error(err))
			return
		}
	})
}

func (s *Service) IsNonResidentUser(ctx context.Context, req *pb.IsNonResidentUserRequest) (*pb.IsNonResidentUserResponse, error) {
	if req.GetIdentifier() == nil {
		return &pb.IsNonResidentUserResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	var (
		isNonResidentUser   bool
		residentCountryCode typesv2.CountryCode
		phoneNumber         *commontypes.PhoneNumber
		err                 error
	)
	isNonResidentUser, err = isNonResidentUserFromContext(ctx, req.GetActorId())
	if err == nil && !isNonResidentUser {
		return &pb.IsNonResidentUserResponse{
			Status:              rpc.StatusOk(),
			IsNonResidentUser:   commontypes.BoolToBooleanEnum(isNonResidentUser),
			ResidentCountryCode: types.CountryCode_COUNTRY_CODE_IND,
		}, nil
	}

	switch req.GetIdentifier().(type) {
	case *pb.IsNonResidentUserRequest_PhoneNumber:
		if req.GetPhoneNumber() == nil {
			return &pb.IsNonResidentUserResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("empty phone number"),
			}, nil
		}
		phoneNumber = req.GetPhoneNumber()
	case *pb.IsNonResidentUserRequest_ActorId:
		if req.GetActorId() == "" {
			return &pb.IsNonResidentUserResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("empty actorId"),
			}, nil
		}
		userRes, getErr := s.GetUser(ctx, &pb.GetUserRequest{
			Identifier: &pb.GetUserRequest_ActorId{
				ActorId: req.GetActorId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(userRes, getErr); rpcErr != nil {
			if userRes.GetStatus().IsRecordNotFound() {
				return &pb.IsNonResidentUserResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "error in getting user", zap.Error(rpcErr))
			return nil, rpcErr
		}
		phoneNumber = userRes.GetUser().GetProfile().GetPhoneNumber()
	}

	isNonResidentUser, residentCountryCode, err = s.isNonResidentPhNum(phoneNumber)
	if err != nil {
		logger.Error(ctx, "error in checking if ph is non-resident", zap.Error(err))
		return nil, err
	}

	return &pb.IsNonResidentUserResponse{
		Status:              rpc.StatusOk(),
		IsNonResidentUser:   commontypes.BoolToBooleanEnum(isNonResidentUser),
		ResidentCountryCode: residentCountryCode,
	}, nil
}

func isNonResidentUserFromContext(ctx context.Context, actorId string) (bool, error) {
	if actorId == "" {
		return false, fmt.Errorf("actorId not passed in request")
	}

	actorIdFromContext := epificontext.ActorIdFromContext(ctx)
	if actorIdFromContext != actorId {
		return false, fmt.Errorf("request is for different actorId")
	}

	isNonResidentUserStr := epificontext.IsNonResidentUserFromContext(ctx)
	if isNonResidentUserStr == "" {
		return false, fmt.Errorf("empty flag from context")
	}
	isNonResidentUser, ok := commontypes.BooleanEnum_value[isNonResidentUserStr]
	if !ok {
		logger.Error(ctx, "isNonResidentUser flag in context is not boolean enum type", zap.String(logger.RESULT, isNonResidentUserStr))
		return false, fmt.Errorf("isNonResidentUser flag in context is not boolean enum type")
	}
	if commontypes.BooleanEnum(isNonResidentUser) == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		logger.Debug(ctx, "unspecified value in context")
		return false, fmt.Errorf("unspecified value in context")
	}

	logger.Debug(ctx, "IsNonResidentUser value from context", zap.String(logger.RESULT, isNonResidentUserStr))
	return commontypes.BooleanEnum(isNonResidentUser).ToBool(), nil
}

func (s *Service) isNonResidentPhNum(phoneNumber *commontypes.PhoneNumber) (bool, typesv2.CountryCode, error) {
	if lo.Contains(s.dynConf.WhitelistNumberHashesForUAENR().ToStringArray(), obfuscator.HashedPhoneNum(phoneNumber)) {
		return true, typesv2.CountryCode_COUNTRY_CODE_ARE, nil
	}
	if lo.Contains(s.dynConf.WhitelistNumberHashesForQatarNR().ToStringArray(), obfuscator.HashedPhoneNum(phoneNumber)) {
		return true, typesv2.CountryCode_COUNTRY_CODE_QAT, nil
	}
	if phoneNumber.GetCountryCode() == countrystdinfo.GetCountryPhoneInfo(typesv2.CountryCode_COUNTRY_CODE_IND).GetISDCode() {
		return false, typesv2.CountryCode_COUNTRY_CODE_IND, nil
	}
	if phoneNumber.GetCountryCode() == 0 {
		return false, 0, fmt.Errorf("zero country code")
	}
	if lo.Contains(nriPkg.AllowedISDCodes(), phoneNumber.GetCountryCode()) {
		return true, countrystdinfo.GetCountryCodeByISDCode(phoneNumber.GetCountryCode()), nil
	}
	return false, 0, fmt.Errorf("unsupported country")
}

// GetB2BSalaryProgramVerificationStatus performs the necessary checks to validate whether the actor/phone-number is
// a verified entity for Salary B2B program or not.
// Note: Currently it supports checks via Actor-id, Phone Number. Tomorrow this can be extended for email-ids as well.
// nolint:funlen
func (s *Service) GetB2BSalaryProgramVerificationStatus(ctx context.Context, req *pb.GetB2BSalaryProgramVerificationStatusRequest) (*pb.GetB2BSalaryProgramVerificationStatusResponse, error) {
	var (
		isB2BSalaryVerified bool
	)

	switch req.GetIdentifier().(type) {
	case *pb.GetB2BSalaryProgramVerificationStatusRequest_ActorId:
		isB2B, err := s.isB2BSalaryVerifiedUserByActorId(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in fetching B2B verification status", zap.Error(err))
			return &pb.GetB2BSalaryProgramVerificationStatusResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}

		isB2BSalaryVerified = isB2B

	case *pb.GetB2BSalaryProgramVerificationStatusRequest_PhoneNumber:
		isB2B, err := s.isB2BSalaryVerifiedPhoneNumber(ctx, req.GetPhoneNumber())
		if err != nil {
			logger.Error(ctx, "error in fetching salary B2B verification status via phone number", zap.Error(err))
			return &pb.GetB2BSalaryProgramVerificationStatusResponse{Status: rpc.StatusInternal()}, nil
		}

		isB2BSalaryVerified = isB2B

	default:
		logger.Error(ctx, "unhandled identifier for GetB2BSalaryProgramVerificationStatus", zap.Any("identifier", req.GetIdentifier()))
		return &pb.GetB2BSalaryProgramVerificationStatusResponse{Status: rpc.StatusInvalidArgument()}, nil
	}

	return &pb.GetB2BSalaryProgramVerificationStatusResponse{Status: rpc.StatusOk(), IsVerified: isB2BSalaryVerified}, nil
}

// isB2BSalaryVerifiedPhoneNumber checks whether the phone number is B2B salary verified or not via the following checks:
//  1. If a user exists by a phone number, we proceed to fetch the actor and perform full checks.
//  2. If a user doesn't exist by a phone number, we proceed to perform the user-group check for B2B_SALARY_PROGRAM.
func (s *Service) isB2BSalaryVerifiedPhoneNumber(ctx context.Context, phoneNumber *commontypes.PhoneNumber) (bool, error) {
	var (
		isB2BSalaryVerified bool
	)

	user, err := s.dao.GetUserByPhoneNumber(ctx, phoneNumber)
	switch {
	// if it's an error apart from RecordNotFound, don't continue anymore
	case err != nil && !storagev2.IsRecordNotFoundError(err):
		return false, fmt.Errorf("error fetching user using the phone number: %w", err)

	// if no user is found using the phone number, proceed for user-group checks
	case storagev2.IsRecordNotFoundError(err):
		mappingRes, errResp := s.groupClient.CheckMapping(ctx, &userGroup.CheckMappingRequest{
			UserGroup: commontypes.UserGroup_B2B_SALARY_PROGRAM,
			IdentifierValue: &userGroup.IdentifierValue{
				Identifier: &userGroup.IdentifierValue_PhoneNumber{PhoneNumber: phoneNumber},
			},
		})
		if rpcErr := epifigrpc.RPCError(mappingRes, errResp); rpcErr != nil && !mappingRes.GetStatus().IsRecordNotFound() {
			return false, fmt.Errorf("error checking for user-group mapping of salary b2b: %w", rpcErr)
		}

		isB2BSalaryVerified = mappingRes.GetStatus().IsSuccess()

	// if user is found via the phone number, fetch the actor and proceed for full checks
	case err == nil:
		isB2B, checkErr := s.isB2BSalaryVerifiedUserByActorId(ctx, user.GetActorId())
		if checkErr != nil {
			logger.Error(ctx, "error in fetching salary B2B verification status by actor-id", zap.Error(checkErr),
				zap.String(logger.ACTOR_ID_V2, user.GetActorId()),
			)
			return false, fmt.Errorf("error in fetching B2B verification status for the actor: %w", checkErr)
		}

		isB2BSalaryVerified = isB2B

	default:
		return false, fmt.Errorf("unexpected scenario occurred during salary b2b check via phone number")
	}

	return isB2BSalaryVerified, nil
}

// nolint:funlen
func (s *Service) isB2BSalaryVerifiedUserByActorId(ctx context.Context, actorId string) (bool, error) {
	var (
		errG, _  = errgroup.WithContext(ctx)
		boolChan = make(chan bool, 5)
		wg       sync.WaitGroup
	)
	userResp, getUserErr := s.GetUserInterface.GetUser(ctx, &pb.GetUserRequest{
		Identifier: &pb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	isWorkEmailVerification, wevErr := s.isScreenerWorkEmailVerificationSuccess(ctx, actorId)
	if epifigrpc.RPCError(userResp, getUserErr) == nil {
		// Check for B2B Salary Account tagging details in User Group Mapping
		errG.Go(func() error {
			wg.Add(1)
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				defer wg.Done()
				if err := s.checkMappingForB2BSalaryProgram(ctx, &userGroup.IdentifierValue{
					Identifier: &userGroup.IdentifierValue_PhoneNumber{
						PhoneNumber: userResp.GetUser().GetProfile().GetPhoneNumber(),
					},
				}); err != nil {
					return
				}
				logger.Info(ctx, "user group mapping of b2b salary program found for phone number")
				boolChan <- true
			})
			wg.Add(1)
			goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
				defer wg.Done()
				if err := s.checkMappingForB2BSalaryProgram(ctx, &userGroup.IdentifierValue{
					Identifier: &userGroup.IdentifierValue_Email{
						Email: userResp.GetUser().GetProfile().GetEmail(),
					},
				}); err != nil {
					return
				}
				logger.Info(ctx, "user group mapping of b2b salary program found for email")
				boolChan <- true
			})
			waitgroup.SafeWait(&wg, 30*time.Second)
			return nil
		})
		// Check if user has onboarded via web B2B flow and his work email verification is success
		errG.Go(func() error {
			if userResp.GetUser().GetAcquisitionInfo().GetPlatform() != commontypes.Platform_WEB ||
				userResp.GetUser().GetAcquisitionInfo().GetAcquisitionSource() != pb.AcquisitionChannel_B2B_SALARY_PROGRAM.String() {
				return nil
			}
			if !isWorkEmailVerification || wevErr != nil {
				return wevErr
			}
			logger.Info(ctx, "user is acquired through web B2B channel and his work email verification is success")
			boolChan <- true
			return nil
		})
	}
	// Check for B2B Salary Account tagging in work email verification
	errG.Go(func() error {
		employerId, empProcessChecks, err := s.getEmploymentProcessCheckForWorkEmailVerification(ctx, actorId)
		if err != nil {
			return err
		}
		if employerId != "" {
			getEmployerResp, getEpmloyerErr := s.empClient.GetEmployer(ctx, &employment.GetEmployerRequest{
				Identifier: &employment.GetEmployerRequest_EmployerId{EmployerId: employerId},
			})
			if rpcErr := epifigrpc.RPCError(getEmployerResp, getEpmloyerErr); rpcErr != nil {
				return fmt.Errorf("empClient.GetEmployer rpc failed, %w", rpcErr)
			}

			if getEmployerResp.GetEmployerInfo().GetSalaryProgramChannel() == employment.EmployerSalaryProgramChannel_B2B {
				logger.Info(ctx, "user is an employee of employer onboarded via B2B salary program channel")
				boolChan <- true
			}
		}

		if !isWorkEmailVerification || wevErr != nil {
			return wevErr
		}
		for _, processData := range empProcessChecks {
			isVerified, err := s.isWorkEmailVerifiedB2BSalaryProgramDomain(ctx, processData.GetEmploymentVerificationProcess())
			if err != nil {
				return err
			}
			if isVerified {
				logger.Info(ctx, "user's work email verification is successful through a B2B Salary Program whitelisted domain")
				boolChan <- true
			}
		}

		return nil
	})
	err := errG.Wait()
	close(boolChan)
	for res := range boolChan {
		if res {
			return true, nil
		}
	}
	return false, err
}

func (s *Service) checkMappingForB2BSalaryProgram(ctx context.Context, identifier *userGroup.IdentifierValue) error {
	resp, errResp := s.groupClient.CheckMapping(ctx, &userGroup.CheckMappingRequest{
		UserGroup:       commontypes.UserGroup_B2B_SALARY_PROGRAM,
		IdentifierValue: identifier,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		if !resp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error while fetching user group mapping", zap.Error(err))
		}
		return err
	}
	return nil
}

func (s *Service) isScreenerWorkEmailVerificationSuccess(ctx context.Context, actorId string) (bool, error) {
	getScrnrAttempts, errResp := s.screenerClient.GetScreenerAttemptsByActorId(ctx, &screener.GetScreenerAttemptsByActorIdRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if err := epifigrpc.RPCError(getScrnrAttempts, errResp); err != nil {
		if getScrnrAttempts.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		logger.Error(ctx, "error while fetching screener attempts", zap.Error(err))
		return false, err
	}
	for _, checks := range getScrnrAttempts.GetChecksMap() {
		if checks.GetCheckType() == screener.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION && checks.GetCheckResult() == screener.CheckResult_CHECK_RESULT_PASSED {
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) isWorkEmailVerifiedB2BSalaryProgramDomain(ctx context.Context, empVerificationProcess *employment.EmploymentVerificationProcess) (bool, error) {
	if empVerificationProcess.GetVerificationProcessStatus().GetWorkEmailVerificationProcessStatus() == employment.WorkEmailVerificationProcessStatus_WORK_EMAIL_VERIFICATION_PROCESS_COMPLETED &&
		empVerificationProcess.GetVerificationResult() == employment.EmploymentVerificationResult_EMPLOYMENT_VERIFICATION_RESULT_ACCEPTED {
		domain := email.GetEmailDomain(empVerificationProcess.GetMetadata().GetWorkEmailVerificationMetadata().GetEmail())
		getDomainDetails, errResp := s.empClient.GetDomainDetails(ctx, &employment.GetDomainDetailsRequest{
			Domain:     domain,
			DomainType: employment.DomainType_DOMAIN_TYPE_WORK_EMAIL,
		})
		if err := epifigrpc.RPCError(getDomainDetails, errResp); err != nil {
			if getDomainDetails.GetStatus().IsRecordNotFound() {
				return false, nil
			}
			logger.Error(ctx, "error while fetching domain details", zap.Error(err))
			return false, err
		}
		if getDomainDetails.GetDomainDetails().GetDomainClassificationDetails().GetDomainClassificationMapping()[employment.DomainClassification_DOMAIN_CLASSIFICATION_B2B_SALARY_PROGRAM.String()].GetIsEnabled() {
			logger.Info(ctx, "work email verification completed with whitelisted domain")
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) getEmploymentProcessCheckForWorkEmailVerification(ctx context.Context, actorId string) (string, []*employment.EmploymentProcessCheck, error) {
	getEmploymentInfo, errResp := s.empClient.GetEmploymentInfo(ctx, &employment.GetEmploymentInfoRequest{
		ActorId: actorId,
		ProcessNames: []employment.ProcessName{
			employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION,
		},
	})
	if err := epifigrpc.RPCError(getEmploymentInfo, errResp); err != nil {
		if getEmploymentInfo.GetStatus().IsRecordNotFound() {
			return "", nil, nil
		}
		logger.Error(ctx, "error while fetching employment info", zap.Error(err))
		return "", nil, err
	}
	return getEmploymentInfo.GetEmploymentData().GetEmployerId(), getEmploymentInfo.GetEmploymentProcessCheck(), nil
}

func storeDedupeResponseAsync(ctx context.Context, req *pb.DedupeCheckRequest, s *Service, vgres *vgPbCustomer.DedupeCheckResponse, requestId string) {
	if req.GetActorId() == "" || requestId == "" || vgres.GetDedupeStatus() == 0 {
		logger.Info(ctx, "cannot store dedupe response as invalid args")
		return
	}
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		_ = s.vendorStore.Insert(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, &commonvgpb.VendorStatus{
			Code: vgres.GetDedupeStatus().String(),
		}, vgres.GetStatus(), vendorstore.FEDERAL_API_DEDUPE_CHECK, requestId, vgres.GetRawResponse())
	})
}
