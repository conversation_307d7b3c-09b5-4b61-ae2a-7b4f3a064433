// Code generated by tools/conf_gen/dynamic_conf_gen.go
package worker

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "pgdbmigrationflag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PgdbMigrationFlag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PgdbMigrationFlag, nil
	case "isriskcheckenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRiskCheckEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsRiskCheckEnabled, nil
	case "useldcsubanalysisforincome":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseLdcSubAnalysisForIncome\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseLdcSubAnalysisForIncome, nil
	case "offerdeactivationconfig":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.OfferDeactivationConfig, nil
		case len(dynamicFieldPath) > 1:

			return obj.OfferDeactivationConfig[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.OfferDeactivationConfig, nil
	case "secondlooknextofferconfig":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SecondLookNextOfferConfig, nil
		case len(dynamicFieldPath) > 1:

			return obj.SecondLookNextOfferConfig[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.SecondLookNextOfferConfig, nil
	case "skipepfocheck":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.SkipEpfoCheck, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"SkipEpfoCheck\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.SkipEpfoCheck[dynamicFieldPath[1]], nil

		}
		return obj.SkipEpfoCheck, nil
	case "sgkycurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SgKycUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SgKycUrl, nil
	case "application":
		return obj.Application.Get(dynamicFieldPath[1:])
	case "lamf":
		return obj.Lamf.Get(dynamicFieldPath[1:])
	case "deeplinkconfig":
		return obj.DeeplinkConfig.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "creditreportconfig":
		return obj.CreditReportConfig.Get(dynamicFieldPath[1:])
	case "idfcplflowconfig":
		return obj.IdfcPlFlowConfig.Get(dynamicFieldPath[1:])
	case "cibilcreditreportfetchversions":
		return obj.CibilCreditReportFetchVersions.Get(dynamicFieldPath[1:])
	case "mandateconfig":
		return obj.MandateConfig.Get(dynamicFieldPath[1:])
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "vendorprogramlevelfeature":
		return obj.VendorProgramLevelFeature.Get(dynamicFieldPath[1:])
	case "lopeoverrideconfig":
		return obj.LopeOverrideConfig.Get(dynamicFieldPath[1:])
	case "sgetbneweligibilityflow":
		return obj.SgEtbNewEligibilityFlow.Get(dynamicFieldPath[1:])
	case "eligibilitynuggetbotfeature":
		return obj.EligibilityNuggetBotFeature.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Lamf) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "intermediatemflinkscreenwaitingtime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IntermediateMfLinkScreenWaitingTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IntermediateMfLinkScreenWaitingTime, nil
	case "experimentconfig":
		return obj.ExperimentConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Lamf", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ExperimentConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "processingfeesconfig":
		return obj.ProcessingFeesConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ExperimentConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ProcessingFeesConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "firstsegment":
		return obj.FirstSegment.Get(dynamicFieldPath[1:])
	case "secondsegment":
		return obj.SecondSegment.Get(dynamicFieldPath[1:])
	case "thirdsegment":
		return obj.ThirdSegment.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ProcessingFeesConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ProcessingFeesSegment) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "loanamountlowerlimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LoanAmountLowerLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LoanAmountLowerLimit, nil
	case "loanamountupperlimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LoanAmountUpperLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LoanAmountUpperLimit, nil
	case "absoluteprocessingfees":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AbsoluteProcessingFees\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AbsoluteProcessingFees, nil
	case "minprocessingfees":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinProcessingFees\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinProcessingFees, nil
	case "maxprocessingfees":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxProcessingFees\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxProcessingFees, nil
	case "enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enabled, nil
	case "ispercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsPercentage, nil
	case "percentageprocessingfees":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PercentageProcessingFees\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PercentageProcessingFees, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ProcessingFeesSegment", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IdfcPlFlowConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isvkycenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsVkycEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsVkycEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IdfcPlFlowConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LoanStepDetailsForOfferDeactivation) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "loansteps":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LoanSteps\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LoanSteps, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LoanStepDetailsForOfferDeactivation", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CibilCreditReportFetchVersions) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "miniosversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinIosVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinIosVersion, nil
	case "minandroidversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinAndroidVersion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinAndroidVersion, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CibilCreditReportFetchVersions", strings.Join(dynamicFieldPath, "."))
	}
}
