// nolint:funlen,staticcheck,unparam
package common

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	userPb "github.com/epifi/gamma/api/user"
	activity2 "github.com/epifi/gamma/preapprovedloan/activity"
	palAct "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/helper"
	ldProviders "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
)

func (p *Processor) CreditReportFetch(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palAct.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		creditReportVendor := getCreditReportVendor(req.GetLoanStep().GetStepName())
		skipStageFlag, err := p.creditReportHelper.SkipCreditFetchStage(ctx, req.GetLoanProgram(), req.GetLoanStep().GetActorId(), creditReportVendor, lse.GetOrchId())
		if err != nil {
			lg.Error("error in checking skipCreditFetchStage", zap.Error(err))
			return nil, errors.Wrapf(epifierrors.ErrTransient, "error in checking skipCreditFetchStage, err: %v", err.Error())
		}

		// if stage needs to be skipped, mark the loan step as success and sub status as skipped and return
		if skipStageFlag {
			lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_ALREADY_DONE
			res.LseFieldMasks = append(res.LseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			return res, nil
		}

		// Getting the deeplink provider
		dlp := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{
			Vendor:      req.GetVendor(),
			LoanProgram: req.GetLoanProgram(),
		})
		pan := ""
		userDataProvider, userDataProviderErr := p.lmsProviders.FetchUserDetailsProvider(ctx, &palPb.LoanHeader{
			LoanProgram: req.GetLoanProgram(),
			Vendor:      req.GetVendor(),
		})
		if userDataProviderErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get FetchUserDetailsProvider, error: %v", userDataProviderErr))
		}
		// if provider is not implemented for given vendor and loan program, it returns nil instead of error
		if userDataProvider != nil {
			userKycData, userKycDataErr := userDataProvider.FetchUserKycDetails(ctx, &ldProviders.FetchUserKycDetailsRequest{
				LrId: lse.GetRefId(),
				KycDetailsRequired: []ldProviders.UserKycDetailType{
					ldProviders.UserKycDetailTypePan,
				},
			})
			if userKycDataErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get FetchUserKycDetails, error: %v", userKycDataErr))
			}
			if data, ok := userKycData.KycDetailsMap[ldProviders.UserKycDetailTypePan]; ok {
				pan = data.(string)
			}
		}
		var name *commontypes.Name
		var user *userPb.User
		var userErr error
		// in case of fi core users, we can send pan empty and credit report service will fetch from user profile
		if pan == "" {
			user, userErr = p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
			if userErr != nil {
				lg.Error("failed to fetch user by actor ID", zap.Error(userErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user by actor ID: %v", userErr))
			}
			pan = helper.GetPanFromUserDataVerificationDetails(user)
			name = user.GetProfile().GetPanName()
		}

		provenance := creditReportV2Pb.Provenance_PROVENANCE_PERSONAL_LOAN
		// credit report worker sets the TnC on consent screen based on the provenance.
		// adding same provenance for FI_LITE and A2L as we need to show the same TnC for both Loan programs.
		if req.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
			provenance = creditReportV2Pb.Provenance_PROVENANCE_PERSONAL_LOAN_ATL
			if user != nil {
				user, userErr = p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
				if userErr != nil {
					lg.Error("failed to fetch user by actor ID", zap.Error(userErr))
					return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user by actor ID: %v", userErr))
				}
				name = user.GetProfile().GetPanName()
			}
			if name == nil {
				name = user.GetProfile().GetGivenName()
			}
			if name == nil {
				dvds := user.GetDataVerificationDetails().GetDataVerificationDetails()
				// looping in reverse to get the latest name entry
				for i := len(dvds) - 1; i >= 0; i-- {
					if dvds[i].GetDataType() == userPb.DataType_DATA_TYPE_PAN_NAME {
						name = dvds[i].GetPanName()
						break
					}
				}
			}
			if name == nil {
				lg.Error("failed to fetch name for the credit report fetch process")
				return nil, errors.Wrap(epifierrors.ErrPermanent, "failed to fetch name for the credit report fetch process")
			}

		}

		redirectionLink, err := dlp.GetLoansApplicationStatusPollDeeplink(ctx, dlp.GetLoanHeader(), lse.GetActorId(), lse.GetRefId(), &provider.ApplicationStatusPollDeeplinkParams{
			GetNextActionInSync: true,
		})
		if err != nil {
			lg.Error("failed to get polling screen from deeplink provider", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get deeplink v3, err: %v", err.Error()))
		}

		initiateResp, initiateRespErr := p.creditReportV2Client.StartDownloadProcess(ctx, &creditReportV2Pb.StartDownloadProcessRequest{
			ActorId:          lse.GetActorId(),
			RequestId:        lse.GetOrchId(),
			Provenance:       provenance,
			Vendor:           creditReportVendor,
			RedirectDeeplink: redirectionLink,
			Pan:              pan,
			Name:             name,
		})
		if te := epifigrpc.RPCError(initiateResp, initiateRespErr); te != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get the status of the credit report fetch process, error: %v", te))
		}

		switch initiateResp.GetProcessStatus() {
		case creditReportV2Pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED:
			activity2.MarkLoanStepSuccess(res.GetLoanStep())
			return res, nil
		case creditReportV2Pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED:
			if isNonRetryableCreditReportFetchStatus(initiateResp.GetProcessSubStatus(), getCreditReportVendor(req.GetLoanStep().GetStepName()), req.GetLoanProgram()) {
				res2, err2 := p.handleNonRetryableCreditReportFetchStatus(ctx, res.GetLoanStep(), res, req)
				if err2 != nil {
					return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("failed to handle non retryable credit report fetch status, error: %v", err2))
				}
				activity2.MarkLoanStepFail(res.GetLoanStep())
				return res2, nil
			}
			res.GetLoanStep().SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED
			res.GetLoanStep().OrchId = uuid.New().String()
			updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID})
			if updateErr != nil {
				lg.Error("failed to update lse after credit report download failure", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, "failed to update lse after credit report download failure")
			}
			res.NextAction = redirectionLink
			return res, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("credit report download failed, starting again: %v", initiateResp.GetProcessSubStatus().String()))
		case creditReportV2Pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED:
			res.NextAction = initiateResp.GetNextAction()
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CREDIT_REPORT_FETCH_INITIATED
			res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS})
			if updateErr != nil {
				lg.Error("failed to update lse after credit report download failure", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, "failed to update lse after credit report download failure")
			}
			return res, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("credit report downloand intiated "))
		default:
			res.NextAction = initiateResp.GetNextAction()
			return res, errors.Wrap(epifierrors.ErrTransient, "credit report download in progress")
		}
	})
	return actRes, actErr
}

func isNonRetryableCreditReportFetchStatus(crFetchSubStatus creditReportV2Pb.CreditReportDownloadSubStatus, vendor commonvgpb.Vendor, lp palPb.LoanProgram) bool {
	switch vendor {
	case commonvgpb.Vendor_EXPERIAN:
		return false
	case commonvgpb.Vendor_CIBIL:
		if crFetchSubStatus == creditReportV2Pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND ||
			crFetchSubStatus == creditReportV2Pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_FAILED ||
			crFetchSubStatus == creditReportV2Pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_INCOMPLETE_USER_NAME {
			return true
		}
		return false
	default:
		return false
	}
}

func getCreditReportVendor(stage palPb.LoanStepExecutionStepName) commonvgpb.Vendor {

	switch stage {
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREDIT_REPORT_FETCH, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EXPERIAN_REPORT_FETCH:
		return commonvgpb.Vendor_EXPERIAN
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CIBIL_REPORT_FETCH:
		return commonvgpb.Vendor_CIBIL
	default:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED
	}
}

func (p *Processor) handleNonRetryableCreditReportFetchStatus(ctx context.Context, lse *palPb.LoanStepExecution, res *palActivityPb.PalActivityResponse, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	areLoecAlreadyUpdated := false
	// in case of eligibility, need to mark all the active loecs across multiple lender DBs as rejected
	if req.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
		var eligibilityErr error
		areLoecAlreadyUpdated, eligibilityErr = p.handleNonRetryableCreditReportFetchStatusForEligibility(ctx, lse)
		if eligibilityErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update active loecs by actor id for eligibility program, err: %v", eligibilityErr))
		}
	}

	loecs, loecsErr := p.loecDao.GetByActorIdLoanProgramsAndStatuses(ctx, lse.GetActorId(), []palPb.LoanProgram{req.GetLoanProgram()},
		[]palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED},
		0, true)
	if loecsErr != nil && !errors.Is(loecsErr, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch latest loec, err: %v", loecsErr))
	}
	var latestLoec *palPb.LoanOfferEligibilityCriteria
	if len(loecs) > 0 {
		latestLoec = loecs[0]
	}

	lr, lrErr := p.loanRequestDao.GetById(ctx, req.GetLoanStep().GetRefId())
	if lrErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request from ref id, err : %v", lrErr))
	}

	dlProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{
		Vendor:      req.GetVendor(),
		LoanProgram: req.GetLoanProgram(),
	})

	txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
	if txnExecErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get txn executor by ownership, err: %v", txnExecErr))
	}
	txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		if err := p.loanStepExecutionDao.Update(txnCtx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		}); err != nil {
			return fmt.Errorf("failed to update lse for rejected, err: %v", err)
		}
		if !areLoecAlreadyUpdated && latestLoec != nil {
			latestLoec.Status = palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
			latestLoec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_REPORT_DOWNLOAD_FAILED
			if err := p.loecDao.Update(txnCtx, latestLoec, []palPb.LoanOfferEligibilityCriteriaFieldMask{
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
			}); err != nil {
				return fmt.Errorf("failed to update loec for approved, err: %v", err)
			}
		}
		lr.NextAction = dlProvider.GetLoanLandingInfo(dlProvider.GetLoanHeader())
		if err := p.loanRequestDao.Update(txnCtx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}); err != nil {
			return fmt.Errorf("failed to update loan request next action, err: %v", err)
		}
		return nil
	})
	if txnErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update the lse and loec in transaction, err: %v", txnErr))
	}

	res.LoanStep = lse
	return res, nil
}

var lseToDataReqType = map[palPb.LoanStepExecutionStepName]palPb.DataRequirementType{
	palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CIBIL_REPORT_FETCH:  palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_CIBIL,
	palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREDIT_REPORT_FETCH: palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_COMMON,
}

func (p *Processor) handleNonRetryableCreditReportFetchStatusForEligibility(ctx context.Context, lse *palPb.LoanStepExecution) (bool, error) {
	isLoecUpdated := false
	userFeatProp, err := helper.GetUserFeatureProperty(ctx, lse.GetActorId(), p.onbClient, p.savingsClient)
	if err != nil {
		return false, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching user feature property: %v", err))
	}

	loecs, loecsErr := helper.GetCDCEligibleLoecs(ctx, p.loecDao, lse.GetActorId(), userFeatProp.IsFiSAHolder)
	if loecsErr != nil && !errors.Is(loecsErr, epifierrors.ErrRecordNotFound) {
		return false, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get active loecs by actor id for eligibility program, err: %v", loecsErr))
	}
	for _, loec := range loecs {
		needUpdate := false
		for _, dataReq := range loec.GetDataRequirementDetails().GetDataRequirements() {
			if dataReq.GetIsCollected() == false && dataReq.GetDataRequirementType() == lseToDataReqType[lse.GetStepName()] {
				needUpdate = true
			}
		}
		if !needUpdate {
			continue
		}
		newCtx := epificontext.WithOwnership(ctx, helper.GetPalOwnership(loec.GetVendor()))
		loec.Status = palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
		loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_CREDIT_REPORT_DOWNLOAD_FAILED
		if err := p.loecDao.Update(newCtx, loec, []palPb.LoanOfferEligibilityCriteriaFieldMask{
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
		}); err != nil {
			return false, fmt.Errorf("failed to update loec with vendor: %s for rejected, err: %v", loec.GetVendor().String(), err)
		}
		isLoecUpdated = true
	}
	return isLoecUpdated, nil
}
