// nolint: dupl, funlen, gosec
package helper

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	pkgErrors "github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	date2 "google.golang.org/genproto/googleapis/type/date"
	latLngPb "google.golang.org/genproto/googleapis/type/latlng"
	money2 "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/money"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountBalanceEnums "github.com/epifi/gamma/api/accounts/balance/enums"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	locationPb "github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	esignPb "github.com/epifi/gamma/api/docs/esign"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fcmpb "github.com/epifi/gamma/api/frontend/fcm"
	mfexternalorderpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	kycPb "github.com/epifi/gamma/api/kyc"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palCommsPb "github.com/epifi/gamma/api/preapprovedloan/comms"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	sgEsignApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/esign"
	types "github.com/epifi/gamma/api/typesv2"
	accTypes "github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	typesESign "github.com/epifi/gamma/api/typesv2/esign"
	userPb "github.com/epifi/gamma/api/user"
	creditReportPb "github.com/epifi/gamma/api/user/credit_report"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	ffVgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	accountVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	savingsVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	panVgPb "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/pkg/address"
	"github.com/epifi/gamma/pkg/loans"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	palCommonGenconf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao"
	palErrors "github.com/epifi/gamma/preapprovedloan/errors"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	wireTypes "github.com/epifi/gamma/preapprovedloan/wire/types"
)

// maxLvAttemptLimit defines the maximum amount of liveness attempts returned by the liveness service
const (
	maxLvAttemptLimit                 = 100
	IdfcCutOffAmount                  = 100000
	idfcCutOffTenure                  = 24
	loanUtilityOutstandingRequestCode = "OUTSTANDINGS"
	ImageFileExt                      = "png"
	// we are updating the image in every 10hours because we are pushing data to credgenics very frequently
	userImageExpiryInSeconds = 10 * 60 * 60
)

var (
	OtpAttemptsExhaustedErr = fmt.Errorf("otp attempts exhausted")
)

type SendNotificationRequest struct {
	ActorId           string
	NotificationType  palCommsPb.PreApprovedLoanNotificationType
	LoanReqId         string
	Flow              palPb.LoanStepExecutionFlow
	Step              palPb.LoanStepExecutionStepName
	LoanAccountNumber string
	Deeplink          *deeplinkPb.Deeplink
	Amount            *money2.Money
	Date              *date2.Date
}

type SendPushNotificationPayload struct {
	Title        string
	Body         string
	Deeplink     *deeplinkPb.Deeplink
	CampaignName commsPb.CampaignName
}

type RpcHelper struct {
	palVgClient            palVgPb.PreApprovedLoanClient
	actorClient            actorPb.ActorClient
	usersClient            userPb.UsersClient
	savingsClient          savings.SavingsClient
	celestialClient        celestialPb.CelestialClient
	lvClient               livenessPb.LivenessClient
	kycClient              kycPb.KycClient
	vgCustomerClient       vgCustomerPb.CustomerClient
	authClient             authPb.AuthClient
	eSignClient            esignPb.ESignClient
	timePkg                datetimePkg.Time
	commsClient            commsPb.CommsClient
	loanStepExecutionsDao  dao.LoanStepExecutionsDao
	notifConf              *common.Notification
	orderClient            orderPb.OrderServiceClient
	payClient              payPb.PayClient
	piClient               piPb.PiClient
	accountPiClient        accountPiPb.AccountPIRelationClient
	paymentClient          paymentPb.PaymentClient
	savingsVgClient        savingsVgPb.SavingsClient
	loanActivityDao        dao.LoanActivityDao
	llPalVgClient          llVgPb.LiquiloansClient
	loanApplicantDao       dao.LoanApplicantDao
	profileClient          profilePb.ProfileClient
	bcClient               bankcust.BankCustomerServiceClient
	salaryClient           salaryPb.SalaryProgramClient
	idfcVgClient           idfc.IdfcClient
	accountVgClient        accountVgPb.AccountsClient
	creditReportClient     creditReportPb.CreditReportManagerClient
	creditReportV2Client   creditReportV2Pb.CreditReportManagerClient
	creditReportConf       *palCommonGenconf.CreditReportConfig
	onbClient              onbPb.OnboardingClient
	obfuscatorClient       obfuscator.ObfuscatorClient
	accountBalanceClient   accountBalancePb.BalanceClient
	panVgClient            panVgPb.PANClient
	fiftyFinVgClient       ffVgPb.FiftyFinClient
	segmentationClient     segmentPb.SegmentationServiceClient
	s3Client               wireTypes.PreApprovedLoanS3Client
	limitEstimatorClient   limitEstimatorPb.CreditLimitEstimatorClient
	mfExternalOrdersClient mfexternalorderpb.MFExternalOrdersClient
	userLocationClient     userLocationPb.LocationClient
	eventBroker            events.Broker
	lrDao                  dao.LoanRequestsDao
	sgEsignApiGateway      sgEsignApiGatewayPb.EsignClient
	locationClient         locationPb.LocationClient
}

func NewRpcHelper(
	palVgClient palVgPb.PreApprovedLoanClient,
	actorClient actorPb.ActorClient,
	usersClient userPb.UsersClient,
	savingsClient savings.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	lvClient livenessPb.LivenessClient,
	kycClient kycPb.KycClient,
	vgCustomerClient vgCustomerPb.CustomerClient,
	authClient authPb.AuthClient,
	eSignClient esignPb.ESignClient,
	timePkg datetimePkg.Time,
	commsClient commsPb.CommsClient,
	loanStepExecutionsDao dao.LoanStepExecutionsDao,
	notifConf *common.Notification,
	orderClient orderPb.OrderServiceClient,
	payClient payPb.PayClient, piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	paymentClient paymentPb.PaymentClient,
	savingsVgClient savingsVgPb.SavingsClient,
	loanActivityDao dao.LoanActivityDao,
	llPalVgClient llVgPb.LiquiloansClient,
	loanApplicantDao dao.LoanApplicantDao,
	profileClient profilePb.ProfileClient,
	bcClient bankcust.BankCustomerServiceClient,
	salaryClient salaryPb.SalaryProgramClient,
	idfcVgClient idfc.IdfcClient,
	accountVgClient accountVgPb.AccountsClient,
	creditReportClient creditReportPb.CreditReportManagerClient,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	creditReportConf *palCommonGenconf.CreditReportConfig,
	onbClient onbPb.OnboardingClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	panVgClient panVgPb.PANClient,
	fiftyFinVgClient ffVgPb.FiftyFinClient,
	segmentationClient segmentPb.SegmentationServiceClient,
	s3Client wireTypes.PreApprovedLoanS3Client,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	mfExternalOrdersClient mfexternalorderpb.MFExternalOrdersClient,
	userLocationClient userLocationPb.LocationClient,
	eventBroker events.Broker,
	lrDao dao.LoanRequestsDao,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	locationClient locationPb.LocationClient,
) *RpcHelper {
	return &RpcHelper{
		palVgClient:            palVgClient,
		actorClient:            actorClient,
		usersClient:            usersClient,
		savingsClient:          savingsClient,
		celestialClient:        celestialClient,
		lvClient:               lvClient,
		kycClient:              kycClient,
		vgCustomerClient:       vgCustomerClient,
		authClient:             authClient,
		eSignClient:            eSignClient,
		timePkg:                timePkg,
		commsClient:            commsClient,
		loanStepExecutionsDao:  loanStepExecutionsDao,
		notifConf:              notifConf,
		orderClient:            orderClient,
		payClient:              payClient,
		piClient:               piClient,
		accountPiClient:        accountPiClient,
		paymentClient:          paymentClient,
		savingsVgClient:        savingsVgClient,
		loanActivityDao:        loanActivityDao,
		llPalVgClient:          llPalVgClient,
		loanApplicantDao:       loanApplicantDao,
		profileClient:          profileClient,
		bcClient:               bcClient,
		salaryClient:           salaryClient,
		idfcVgClient:           idfcVgClient,
		accountVgClient:        accountVgClient,
		creditReportClient:     creditReportClient,
		creditReportV2Client:   creditReportV2Client,
		creditReportConf:       creditReportConf,
		onbClient:              onbClient,
		obfuscatorClient:       obfuscatorClient,
		accountBalanceClient:   accountBalanceClient,
		panVgClient:            panVgClient,
		fiftyFinVgClient:       fiftyFinVgClient,
		segmentationClient:     segmentationClient,
		s3Client:               s3Client,
		limitEstimatorClient:   limitEstimatorClient,
		mfExternalOrdersClient: mfExternalOrdersClient,
		userLocationClient:     userLocationClient,
		eventBroker:            eventBroker,
		lrDao:                  lrDao,
		sgEsignApiGateway:      sgEsignApiGateway,
		locationClient:         locationClient,
	}
}

func (r *RpcHelper) GetSavingsAccountDetails(ctx context.Context, actorId string, partnerBank commonvgpb.Vendor, productOffering accTypes.AccountProductOffering) (*savings.Account, error) {
	gaRes, gaErr := r.savingsClient.GetAccount(ctx, &savings.GetAccountRequest{
		Identifier: &savings.GetAccountRequest_ActorUniqueAccountIdentifier{
			ActorUniqueAccountIdentifier: &savings.ActorUniqueAccountIdentifier{
				ActorId:                actorId,
				PartnerBank:            partnerBank,
				AccountProductOffering: productOffering,
			},
		},
	})
	if gaErr != nil {
		if s, _ := status.FromError(gaErr); s.Code() == codes.NotFound {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(gaErr, fmt.Sprintf("GetAccount failed to fetch account: %s", actorId))
	}
	return gaRes.GetAccount(), nil
}

func (r *RpcHelper) GetAccountDetails(ctx context.Context, isNonFiCoreUser bool, lrId, actorId string, vendor palPb.Vendor) (string, string, error) {
	var accountNum, ifscCode string

	if isNonFiCoreUser {
		lse := &palPb.LoanStepExecution{}
		var err error
		// TODO: Refactor this logic to be part of a vendor-specific interface implementation
		if vendor == palPb.Vendor_STOCK_GUARDIAN_LSP {
			lse, err = r.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lrId, palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP)
			if err != nil {
				return "", "", errors.Wrap(err, "failed to fetch penny drop lse")
			}
			accountDetails := lse.GetDetails().GetOnboardingData().GetBankingDetails()
			accountNum = accountDetails.GetAccountNumber()
			ifscCode = accountDetails.GetIfscCode()

		} else {
			lse, err = r.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lrId, palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE)
			if err != nil {
				return "", "", errors.Wrap(err, "failed to fetch mandate lse")
			}
			accountDetails := lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed()
			accountNum = accountDetails.GetAccountNumber()
			ifscCode = accountDetails.GetIfscCode()
		}
	} else {
		savingsAccount, err := r.GetSavingsAccountDetails(ctx, actorId, commonvgpb.Vendor_FEDERAL_BANK, accTypes.AccountProductOffering_APO_REGULAR)
		if err != nil {
			return "", "", err
		}
		accountNum = savingsAccount.GetAccountNo()
		ifscCode = savingsAccount.GetIfscCode()
	}

	return accountNum, ifscCode, nil
}

func (r *RpcHelper) GetUserByActorId(ctx context.Context, actorId string) (*userPb.User, error) {
	actorRes, err := r.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to GetActorById")
	}

	userRes, err := r.usersClient.GetUser(ctx,
		&userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_Id{Id: actorRes.GetActor().GetEntityId()},
		})
	if te := epifigrpc.RPCError(userRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get user from user service")
	}
	return userRes.GetUser(), nil
}

func (r *RpcHelper) GetCustomerPermanentAddressForFederal(ctx context.Context, actorId string, isNonFiCoreUser bool) (*postaladdress.PostalAddress, error) {
	var permanentAddress *postaladdress.PostalAddress
	if isNonFiCoreUser {
		kycRecord, kycRecordErr := r.kycClient.GetKYCRecord(ctx, &kycPb.GetKYCRecordRequest{
			ActorId: actorId,
		})
		if kycRecordErr != nil {
			return nil, errors.Wrap(kycRecordErr, "error in getting kyc record from kycClient in InitiateESign in Fed NTB Flow")
		}
		permanentAddress = kycRecord.GetKycRecord().GetPermanentAddress()
	} else {
		actorRes, err := r.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
		if te := epifigrpc.RPCError(actorRes, err); te != nil {
			return nil, errors.Wrap(te, "failed to GetActorById")
		}
		customerDetails, err := r.usersClient.GetCustomerDetails(ctx, &userPb.GetCustomerDetailsRequest{
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			UserId:     actorRes.GetActor().GetEntityId(),
			ActorId:    actorId,
			Provenance: userPb.Provenance_APP,
		})
		if te := epifigrpc.RPCError(customerDetails, err); te != nil {
			return nil, errors.Wrap(te, "error in getting customerDetails")
		}

		if customerDetails.GetAddresses() != nil {
			permanentAddress = customerDetails.GetAddresses()[types.AddressType_PERMANENT.String()]
		}
	}

	return permanentAddress, nil
}

// TODO : Move behind vendor interface
// nolint:gosec
func (r *RpcHelper) GetConfirmationCodeFromVG(ctx context.Context, loanRequest *palPb.LoanRequest, loanOffer *palPb.LoanOffer) (string, error) {
	switch loanRequest.GetVendor() {
	case palPb.Vendor_FEDERAL:
		return "", errors.New("we are not calling Federal for getting any OTP in this flow")
	case palPb.Vendor_LIQUILOANS:
		applicant, err := r.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, loanRequest.GetActorId(), loanRequest.GetVendor(), loanRequest.GetLoanProgram(), loanOffer.GetProcessingInfo().GetLoanProgramVersion())
		if err != nil {
			return "", fmt.Errorf("failed to get applicant by actorID, %w", err)
		}
		vgRes, err := r.llPalVgClient.SendBorrowerAgreementOtp(ctx, &llVgPb.SendBorrowerAgreementOtpRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_LIQUILOANS,
			},
			ApplicationId: loanRequest.GetVendorRequestId(),
			ApplicantId:   applicant.GetVendorApplicantId(),
			LoanProgram:   r.ConvertToVGLoanProgram(applicant.GetLoanProgram()),
			SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[loanOffer.GetProcessingInfo().GetLoanProgramVersion()],
		})
		if te := epifigrpc.RPCError(vgRes, err); te != nil {
			return "", errors.Wrap(te, "error in GetInstantLoanOTP from VG")
		}
		return "", nil
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		lse, err := r.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequest.GetId(),
			palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS)
		if err != nil {
			return "", errors.Wrap(err, "failed to fetch kfs lse")
		}
		vgRes, err := r.sgEsignApiGateway.GenerateOtp(ctx, &sgEsignApiGatewayPb.GenerateOtpRequest{
			RequestId: lse.GetDetails().GetESignStepData().GetDocumentId(),
			ClientId:  "FI_MONEY",
		})
		if te := epifigrpc.RPCError(vgRes, err); te != nil {
			return "", errors.Wrap(te, "error in GenerateOtp from SG")
		}
		return "", nil
	default:
		return "", errors.New("unhandled vendor in GetInstantLoanOTP from VG")
	}
}

// TODO (naresh) : Move behind vendor interface
// nolint:gosec
func (r *RpcHelper) VerifyOtp(ctx context.Context, lse *palPb.LoanStepExecution, lr *palPb.LoanRequest, req *palPb.ConfirmApplicationRequest, loanOffer *palPb.LoanOffer) (bool, error) {
	switch {
	case lr.GetVendor() == palPb.Vendor_LIQUILOANS && loanOffer.GetLmsPartner() == enums.LmsPartner_LMS_PARTNER_UNSPECIFIED:
		applicant, err := r.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), loanOffer.GetProcessingInfo().GetLoanProgramVersion())
		if err != nil {
			return false, fmt.Errorf("failed to get applicant by actorID, %w", err)
		}
		// only need to check for STPL program for LL as LL doesn't have NACH complete check at their end
		if lr.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_STPL {
			mandateStatus, mandateStatusErr := r.llPalVgClient.GetMandateStatus(ctx, &llVgPb.GetMandateStatusRequest{
				Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LIQUILOANS},
				ApplicantId:   applicant.GetVendorApplicantId(),
				LoanProgram:   r.ConvertToVGLoanProgram(lr.GetLoanProgram()),
				SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[loanOffer.GetProcessingInfo().GetLoanProgramVersion()],
			})
			if te := epifigrpc.RPCError(mandateStatus, mandateStatusErr); te != nil {
				logger.Error(ctx, "failed to check LL mandate status by vendor applicant id", zap.Error(te),
					zap.String("vendor_applicant_id", applicant.GetVendorApplicantId()))
				return false, fmt.Errorf("failed to check LL mandate status by vendor applicant id, err: %w", te)
			}
			if mandateStatus.GetMandateStatus() != llVgPb.MandateStatus_MANDATE_STATUS_SUCCESS {
				logger.WarnWithCtx(ctx, "user's mandate not active, can't verify OTP", zap.String(logger.ACTOR_ID_V2, lr.GetActorId()),
					zap.String(logger.LOAN_REQUEST_ID, lr.GetId()))
				return false, errors.Wrap(epifierrors.ErrFailedPrecondition, "user's mandate not active, can't verify OTP")
			}
		}

		vgRes, err := r.llPalVgClient.VerifyBorrowerAgreementOtp(ctx, &llVgPb.VerifyBorrowerAgreementOtpRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			ApplicationId: lr.GetVendorRequestId(),
			ApplicantId:   applicant.GetVendorApplicantId(),
			DocId:         lse.GetDetails().GetESignStepData().GetDocumentId(),
			Otp:           req.GetOtp(),
			LoanProgram:   r.ConvertToVGLoanProgram(applicant.GetLoanProgram()),
			SchemeVersion: loans.LoanProgramVersionToLLSchemeVersion[loanOffer.GetProcessingInfo().GetLoanProgramVersion()],
		})
		if te := epifigrpc.RPCError(vgRes, err); te != nil {
			if err == nil && vgRes.GetStatus().IsInvalidArgument() {
				return false, nil
			}
			return false, errors.Wrap(te, "error in VerifyOtp from VG for LL")
		}
		// Successfully verified OTP
		return true, nil
	case lr.GetVendor() == palPb.Vendor_STOCK_GUARDIAN_LSP:
		vgRes, err := r.sgEsignApiGateway.VerifyOtp(ctx, &sgEsignApiGatewayPb.VerifyOtpRequest{
			RequestId: lse.GetDetails().GetESignStepData().GetDocumentId(),
			ClientId:  "FI_MONEY",
			Otp:       req.GetOtp(),
		})
		if te := epifigrpc.RPCError(vgRes, err); te != nil {
			// InvalidArgument means OTP is invalid
			if vgRes.GetStatus().IsInvalidArgument() {
				return false, nil
			}
			return false, errors.Wrap(te, "error in VerifyOtp from vendor for SG")
		}
		if vgRes.GetVerificationStatus() == sgEsignApiGatewayPb.VerifyOtpResponse_VERIFIED {
			return true, nil
		}
		// not able to verify otp
		return false, nil
	case lr.GetVendor() == palPb.Vendor_FEDERAL:
		return false, nil
	case lr.GetVendor() == palPb.Vendor_LIQUILOANS && loanOffer.GetLmsPartner() == enums.LmsPartner_LMS_PARTNER_FINFLUX:
		// for cases where we are using Fi's own LMS, OTP verification for E-sign should be done by Fi
		user, err := r.GetUserByActorId(ctx, lr.GetActorId())
		if err != nil {
			return false, fmt.Errorf("error while fetching user from actor id, %w", err)
		}

		token := req.GetToken()
		if token == "" {
			token = lse.GetDetails().GetESignStepData().GetOtpInfo().GetToken()
		}

		verifyRes, err := r.authClient.VerifyOtp(ctx, &authPb.VerifyOtpRequest{
			Device:      req.GetDevice(),
			PhoneNumber: user.GetProfile().GetPhoneNumber(),
			Token:       token,
			Otp:         req.GetOtp(),
			Email:       user.GetProfile().GetEmail(),
			Mediums:     []commsPb.Medium{commsPb.Medium_SMS},
		})
		if te := epifigrpc.RPCError(verifyRes, err); te != nil {
			// status code above 99 represents invalid OTP, return nil error
			if verifyRes.GetStatus().GetCode() >= 100 {
				return false, nil
			}
			return false, errors.Wrap(te, "error in VerifyOtp from auth")
		}
		// Successfully verified OTP
		return true, nil
	case lr.GetVendor() == palPb.Vendor_IDFC:
		user, err := r.GetUserByActorId(ctx, lr.GetActorId())
		if err != nil {
			return false, fmt.Errorf("error while fetching user from actor id, %w", err)
		}

		verifyRes, err := r.authClient.VerifyOtp(ctx, &authPb.VerifyOtpRequest{
			Device:      req.GetDevice(),
			PhoneNumber: user.GetProfile().GetPhoneNumber(),
			Token:       req.GetToken(),
			Otp:         req.GetOtp(),
			Email:       user.GetProfile().GetEmail(),
			Mediums:     []commsPb.Medium{commsPb.Medium_SMS},
		})
		if te := epifigrpc.RPCError(verifyRes, err); te != nil {
			// status code above 99 represents invalid OTP, return nil error
			if verifyRes.GetStatus().GetCode() >= 100 {
				return false, nil
			}
			return false, errors.Wrap(te, "error in VerifyOtp from auth")
		}
		// Successfully verified OTP
		return true, nil
	case lr.GetVendor() == palPb.Vendor_FIFTYFIN:
		applicant, err := r.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), palPb.Vendor_FIFTYFIN, palPb.LoanProgram_LOAN_PROGRAM_LAMF, loanOffer.GetProcessingInfo().GetLoanProgramVersion())
		if err != nil {
			return false, fmt.Errorf("failed to get applicant by actorId, %w", err)
		}
		switch lr.GetDetails().GetOtpInfo().GetOtpType() {
		case palPb.OtpType_OTP_TYPE_CAMS_PF_FETCH:
			userId, err := strconv.Atoi(applicant.GetVendorApplicantId())
			if err != nil {
				return false, fmt.Errorf("error in converting userId string to int, %w", err)
			}
			vgRes, err := r.fiftyFinVgClient.ValidateCamsOtp(ctx, &ffVgPb.ValidateCamsOtpRequest{
				UserId:    int32(userId),
				RequestId: req.GetToken(),
				Otp:       req.GetOtp(),
			})
			if te := epifigrpc.RPCError(vgRes, err); te != nil {
				// status code 100 means invalid otp entered by user
				switch {
				case vgRes.GetStatus().GetCode() == 100:
					// incorrect otp entered
					return false, nil
				case vgRes.GetStatus().GetCode() == 101:
					// otp attempts exhausted
					return false, OtpAttemptsExhaustedErr
				case vgRes.GetStatus().IsRecordNotFound():
					return true, epifierrors.ErrRecordNotFound
				default:
					return false, errors.Wrap(te, "error in ValidateCamsOtp from VG")
				}
			}
			return true, nil
		case palPb.OtpType_OTP_TYPE_KARVY_PF_FETCH:
			userId, err := strconv.Atoi(applicant.GetVendorApplicantId())
			if err != nil {
				return false, fmt.Errorf("error in converting userId string to int, %w", err)
			}
			vgRes, err := r.fiftyFinVgClient.ValidateKarvyOtp(ctx, &ffVgPb.ValidateKarvyOtpRequest{
				UserId:    int32(userId),
				RequestId: req.GetToken(),
				Otp:       req.GetOtp(),
			})
			if te := epifigrpc.RPCError(vgRes, err); te != nil {
				// status code 100 means invalid otp entered by user
				switch {
				case vgRes.GetStatus().GetCode() == 100:
					return false, nil
				case vgRes.GetStatus().IsRecordNotFound():
					return true, epifierrors.ErrRecordNotFound
				default:
					return false, errors.Wrap(te, "error in ValidateKarvyOtp from VG")
				}
			}
			return true, nil
		case palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK:
			userId, err := strconv.Atoi(applicant.GetVendorApplicantId())
			if err != nil {
				return false, fmt.Errorf("error in converting userId string to int, %w", err)
			}
			vgRes, err := r.fiftyFinVgClient.ConfirmLienCams(ctx, &ffVgPb.ConfirmLienCamsRequest{
				UserId:    int32(userId),
				RequestId: lr.GetDetails().GetOtpInfo().GetToken(),
				Otp:       req.GetOtp(),
				RefNo:     req.GetToken(),
			})
			if te := epifigrpc.RPCError(vgRes, err); te != nil {
				// status code 100 means invalid otp entered by user
				if vgRes.GetStatus().GetCode() == 100 {
					return false, nil
				}
				return false, errors.Wrap(te, "error in ValidateKarvyOtp from VG")
			}
			return true, nil
		case palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK:
			userId, err := strconv.Atoi(applicant.GetVendorApplicantId())
			if err != nil {
				return false, fmt.Errorf("error in converting userId string to int, %w", err)
			}
			vgRes, err := r.fiftyFinVgClient.ConfirmLienKarvy(ctx, &ffVgPb.ConfirmLienKarvyRequest{
				UserId:    int32(userId),
				RequestId: lr.GetDetails().GetOtpInfo().GetToken(),
				Otp:       req.GetOtp(),
				RefNo:     req.GetToken(),
			})
			if te := epifigrpc.RPCError(vgRes, err); te != nil {
				// status code 100 means invalid otp entered by user
				if vgRes.GetStatus().GetCode() == 100 {
					return false, nil
				}
				return false, errors.Wrap(te, "error in ValidateKarvyOtp from VG")
			}
			return true, nil
		case palPb.OtpType_OTP_TYPE_MF_CENTRAL_PHONE_PF_FETCH, palPb.OtpType_OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH:
			otpType := lr.GetDetails().GetOtpInfo().GetOtpType()
			verifyRes, verifyErr := r.mfExternalOrdersClient.TriggerHoldingsImportFetch(ctx, &mfexternalorderpb.TriggerHoldingsImportFetchRequest{
				ExternalId: req.GetToken(),
				ActorId:    lr.GetActorId(),
				Otp:        req.GetOtp(),
			})
			switch {
			case verifyRes.GetStatus().GetCode() == uint32(mfexternalorderpb.TriggerHoldingsImportFetchResponse_INCORRECT_OTP_ENTERED):
				logger.Info(ctx, fmt.Sprintf("mf central portfolio fetch - incorrect otp entered, otpType: %s", otpType.String()))
				return false, nil
			case verifyRes.GetStatus().GetCode() == uint32(mfexternalorderpb.TriggerHoldingsImportFetchResponse_OTP_TIME_LIMIT_EXCEEDED):
				logger.Info(ctx, fmt.Sprintf("mf central portfolio fetch - otp time limit exceeded, otpType: %s", otpType.String()))
				return false, nil
			case verifyRes.GetStatus().GetCode() == uint32(mfexternalorderpb.TriggerHoldingsImportFetchResponse_RETRIES_EXHAUSTED):
				logger.Info(ctx, fmt.Sprintf("mf central portfolio fetch - otp attempts exhausted, otpType: %s", otpType.String()))
				return false, OtpAttemptsExhaustedErr
			case verifyErr != nil || !verifyRes.GetStatus().IsSuccess():
				rpcErr := epifigrpc.RPCError(verifyRes, verifyErr)
				logger.Error(ctx, fmt.Sprintf("mf central portfolio fetch - unknown error, otpType: %s", otpType.String()), zap.Error(rpcErr))
				return false, fmt.Errorf("error while verifying mf central otp : %w", rpcErr)
			default:
				logger.Info(ctx, fmt.Sprintf("mf central portfolio fetch - portfolio fetched successfully, otpType: %s", otpType.String()))
				return true, nil
			}
		case palPb.OtpType_OTP_TYPE_MF_CENTRAL_NFT_USER_AUTH, palPb.OtpType_OTP_TYPE_MF_CENTRAL_NFT_UPDATE_DETAIL_VERIFICATION:
			otpType := lr.GetDetails().GetOtpInfo().GetOtpType()
			if otpType == palPb.OtpType_OTP_TYPE_MF_CENTRAL_NFT_USER_AUTH {
				r.sendOtpStatusEvent(ctx, palEvents.OtpStatusMfcNftUpdateDetailsVerificationOtpInitiated, lr)
			}
			verifyRes, verifyErr := r.mfExternalOrdersClient.VerifyNftOtp(ctx, &mfexternalorderpb.VerifyNftOtpRequest{
				ActorId:    req.GetActorId(),
				ExternalId: req.GetToken(),
				Otp:        req.GetOtp(),
			})
			switch {
			case verifyRes.GetStatus().GetCode() == uint32(mfexternalorderpb.VerifyNftOtpResponse_INCORRECT_OTP_ENTERED):
				logger.Info(ctx, fmt.Sprintf("incorrect otp entered by user in nft flow: %s", otpType))
				return false, nil
			case verifyRes.GetStatus().GetCode() == uint32(mfexternalorderpb.VerifyNftOtpResponse_OTP_EXPIRED):
				logger.Info(ctx, fmt.Sprintf("otp has expired for the user in nft flow: %s", otpType))
				return false, palErrors.ErrOtpExpired
			case verifyErr != nil || !verifyRes.GetStatus().IsSuccess():
				rpcErr := epifigrpc.RPCError(verifyRes, verifyErr)
				logger.Error(ctx, fmt.Sprintf("otp verification failed for nft flow, otpType: %s", otpType.String()), zap.Error(rpcErr))
				return false, fmt.Errorf("otp verification failed for nft flow : %w", rpcErr)
			default:
				if otpType == palPb.OtpType_OTP_TYPE_MF_CENTRAL_NFT_USER_AUTH {
					r.sendOtpStatusEvent(ctx, palEvents.OtpStatusMfcNftUpdateDetailsVerificationOtpGenerated, lr)
				}
				logger.Info(ctx, fmt.Sprintf("otp verification success for nft, otpType: %s", otpType.String()))
				return true, nil
			}
		default:
			return false, errors.New("unhandled otpType for 50fin in VerifyOtp")
		}
	}
	return false, errors.New("unhandled vendor in VerifyOtp")
}

func (r *RpcHelper) GetActorFromSavingsAccount(ctx context.Context, savingsAccountNumber string, vendor commonvgpb.Vendor) (*types.Actor, error) {

	savingsAccountRes, err := r.savingsClient.GetAccount(ctx, &savings.GetAccountRequest{
		Identifier: &savings.GetAccountRequest_AccountNumBankFilter{
			AccountNumBankFilter: &savings.AccountNumberBankFilter{
				AccountNumber: savingsAccountNumber,
				PartnerBank:   vendor,
			},
		},
	})
	if err != nil {
		logger.Error(ctx, "failed to map inbound txn to a savings account", zap.Error(err))
		return nil, err
	}

	entityId := savingsAccountRes.GetAccount().GetPrimaryAccountHolder()
	actorRes, err := r.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: entityId,
	})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get actor by entity id")
	}
	return actorRes.GetActor(), nil

}

func (r *RpcHelper) GetLivenessAttempt(ctx context.Context, actorId string) (*livenessPb.LivenessAttempt, error) {
	res, err := r.lvClient.GetLivenessAttempts(ctx, &livenessPb.GetLivenessAttemptsRequest{
		ActorId: actorId,
		Limit:   maxLvAttemptLimit,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error while fetching liveness attempts, not updating liveness attempt", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return nil, errors.New("error while fetching liveness attempts, not updating liveness attempt")
	}
	var palLvAttempt *livenessPb.LivenessAttempt
	for _, lva := range res.GetLivenessAttempts() {
		if lva.GetLivenessFlow() == livenessPb.LivenessFlow_PRE_APPROVED_LOANS {
			palLvAttempt = lva
			break
		}
	}
	// if not attempt is found for successful liveness then return
	if palLvAttempt == nil {
		logger.Info(ctx, "could not find a video received liveness attempt for preapproved loans", zap.String(logger.ACTOR_ID, actorId))
		return nil, fmt.Errorf("could not find a video received liveness attempt for preapproved loans, %w", epifierrors.ErrRecordNotFound)
	}

	// fire and forget for syncing liveness status
	goroutine.Run(ctx, time.Second*5, func(ctx context.Context) {
		getLivenessStatusRes, getLivenessStatusErr := r.lvClient.GetLivenessStatus(ctx, &livenessPb.GetLivenessStatusRequest{
			ActorId:   actorId,
			AttemptId: palLvAttempt.GetRequestId(),
		})
		if te := epifigrpc.RPCError(getLivenessStatusRes, getLivenessStatusErr); te != nil {
			logger.Error(ctx, "error in GetLivenessStatus", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		}
	})

	logger.Info(ctx, "successful preapproved loan, video received liveness attempt found", zap.String(logger.ACTOR_ID, actorId))
	return palLvAttempt, nil
}

func (r *RpcHelper) IsActorFullKyc(ctx context.Context, actorId string, vendor commonvgpb.Vendor) (bool, error) {
	bcResp, errResp := r.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: vendor,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
		return false, fmt.Errorf("failed to fetch bank customer by actor id: %w", err)
	}
	bankCust := bcResp.GetBankCustomer()
	if bankCust.GetDedupeInfo().GetKycLevel() == kycPb.KYCLevel_FULL_KYC {
		return true, nil
	}
	return false, nil
}

// TODO : Move behind vendor interface
func (r *RpcHelper) GetLoanAccountListForActor(ctx context.Context, actorID string) ([]*palVgPb.FetchLoanDetailsResponse_LoanDetails, error) {
	user, err := r.GetUserByActorId(ctx, actorID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get user by actorID")
	}

	custId, err := r.GetVendorCustomerId(ctx, actorID, commonvgpb.Vendor_FEDERAL_BANK)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch customer id by actor id")
	}

	vgReq := &palVgPb.FetchLoanDetailsRequest{
		Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		CustomerId:  custId,
		PhoneNumber: user.GetProfile().GetPhoneNumber(),
	}

	vgRes, err := r.palVgClient.FetchLoanDetails(ctx, vgReq)
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get customer details from VG")
	}

	return vgRes.GetLoanDetails(), nil
}

// TODO : Move behind vendor interface
func (r *RpcHelper) GetVgLoanDetails(ctx context.Context, actorID string, accountNumber string) (*palVgPb.FetchLoanDetailsResponse_LoanDetails, error) {
	user, err := r.GetUserByActorId(ctx, actorID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get user by actorID")
	}

	custId, err := r.GetVendorCustomerId(ctx, actorID, commonvgpb.Vendor_FEDERAL_BANK)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch customer id by actor id")
	}

	vgReq := &palVgPb.FetchLoanDetailsRequest{
		Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		CustomerId:  custId,
		PhoneNumber: user.GetProfile().GetPhoneNumber(),
	}

	vgRes, err := r.palVgClient.FetchLoanDetails(ctx, vgReq)
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get customer details from VG")
	}

	for _, ld := range vgRes.GetLoanDetails() {
		if ld.GetAccountNumber() == accountNumber {
			return ld, nil
		}
	}
	return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no loan details found for given account number")
}

// GetDeviceAuthDetails fetches device details for a given actorId
// Returns deviceId, deviceToken, userProfileId and error
func (r *RpcHelper) GetDeviceAuthDetails(ctx context.Context, actorId string) (deviceId,
	deviceToken, userProfileId string, err error) {
	getDeviceAuthResponse, err := r.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "error in getting device details for card  ", zap.Error(err))
		return "", "", "", err
	}
	if !getDeviceAuthResponse.Status.IsSuccess() {
		if getDeviceAuthResponse.Status.IsRecordNotFound() {
			logger.Error(ctx, "device details not found", zap.String("actorId", actorId))
		}
		return "", "", "", fmt.Errorf("authClient.GetDeviceAuth() failed for actorId: %s, status: %s",
			actorId, getDeviceAuthResponse.GetStatus())
	}
	return getDeviceAuthResponse.GetDevice().GetDeviceId(),
		getDeviceAuthResponse.GetDeviceToken(),
		getDeviceAuthResponse.GetUserProfileId(),
		nil
}

type UserDeviceLocationResponse struct {
	LocationToken string
	LatLng        *latLngPb.LatLng
}

func (r *RpcHelper) GetUserDeviceLocation(ctx context.Context, actorId string) (*UserDeviceLocationResponse, error) {
	userDevPropRes, err := r.usersClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId:       actorId,
		PropertyTypes: []types.DeviceProperty{types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN},
	})
	if err = epifigrpc.RPCError(userDevPropRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting user device location token")
	}
	var locationToken string
	for _, prop := range userDevPropRes.GetUserDevicePropertyList() {
		if prop.GetDeviceProperty() == types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN {
			locationToken = prop.GetPropertyValue().GetLocationToken()
			break
		}
	}
	if locationToken == "" {
		return nil, errors.New("location token not found")
	}
	coordinatesRes, err := r.locationClient.GetCoordinates(ctx, &locationPb.GetCoordinatesRequest{
		LocationToken: locationToken,
	})
	if err = epifigrpc.RPCError(coordinatesRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting coordinates")
	}
	if coordinatesRes.GetLatLng().GetLatitude() == 0 || coordinatesRes.GetLatLng().GetLongitude() == 0 {
		return nil, errors.Errorf("invalid coordinates: %v", coordinatesRes.GetLatLng())
	}
	return &UserDeviceLocationResponse{
		LocationToken: locationToken,
		LatLng:        coordinatesRes.GetLatLng(),
	}, nil
}

// InitiateESign initiates the e-sign process at vendor.
// The method returns the e-sign URL, the e-sign expiry and error
// This method is specifically used for the Federal.
func (r *RpcHelper) InitiateESign(ctx context.Context, actorId string, clientRequestId string, loanReq *palPb.LoanRequest, loanOffer *palPb.LoanOffer) (string, *timestampPb.Timestamp, error) {
	user, err := r.GetUserByActorId(ctx, actorId)
	if err != nil {
		return "", nil, err
	}

	name := user.GetProfile().GetKycName().ToSentenceCaseString()

	userFeatProp, err := GetUserFeatureProperty(ctx, actorId, r.onbClient, r.savingsClient)
	if err != nil {
		return "", nil, errors.Wrap(err, "failed to check if user is non fi core user or not")
	}

	permanentAddress, err := r.GetCustomerPermanentAddressForFederal(ctx, actorId, !userFeatProp.IsFiSAHolder)
	if err != nil {
		return "", nil, errors.Wrap(err, "failed to get customer permanent address")
	}

	accountNum, ifscCode, err := r.GetAccountDetails(ctx, !userFeatProp.IsFiSAHolder, loanReq.GetId(), actorId, loanReq.GetVendor())
	if err != nil {
		return "", nil, errors.Wrap(err, "failed to get account details")
	}

	esignRes, err := r.eSignClient.InitiateESign(ctx, &esignPb.InitiateESignRequest{
		ActorId:         actorId,
		ClientRequestId: clientRequestId,
		TemplateType:    typesESign.TemplateType_PRE_APPROVED_LOAN_KFS_TEMPLATE,
		TemplateOption: &typesESign.TemplateOptions{
			Options: &typesESign.TemplateOptions_KfsTemplateOptions{
				KfsTemplateOptions: &typesESign.KFSTemplateOptions{
					File: &typesESign.KFSTemplateOptions_File{
						Name:   name,
						Fields: r.getKfsFieldMap(user, name, accountNum, ifscCode, loanReq, loanOffer, permanentAddress),
					},
					Invitee: &typesESign.KFSTemplateOptions_Invitee{
						Name:        name,
						Email:       user.GetProfile().GetEmail(),
						PhoneNumber: user.GetProfile().GetPhoneNumber(),
					},
				},
			},
		},
		Vendor: esignPb.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY,
		Client: esignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2,
	})
	if te := epifigrpc.RPCError(esignRes, err); te != nil {
		return "", nil, errors.Wrap(te, "error in InitiateESign from Esign Service")
	}
	return esignRes.GetSignUrl(), esignRes.GetExpiryAt(), nil
}

func (r *RpcHelper) getKfsFieldMap(user *userPb.User, name, accountNum, ifscCode string, loanReq *palPb.LoanRequest, loanOffer *palPb.LoanOffer, permanentAddress *postaladdress.PostalAddress) map[string]string {
	dobAsTime := datetime.DateToTimestamp(user.GetProfile().GetDateOfBirth(), datetime.IST).AsTime()
	currentAge := fmt.Sprint(datetime.AgeAt(dobAsTime, r.timePkg.Now().In(datetime.IST)))
	dateToday := datetime.DateToDDMMYYYY(&date2.Date{
		Year:  int32(r.timePkg.Now().In(datetime.IST).Year()),
		Month: int32(r.timePkg.Now().In(datetime.IST).Month()),
		Day:   int32(r.timePkg.Now().In(datetime.IST).Day()),
	})
	nextEmiDateString, nextEmiDate := GetNextEmiDate(r.timePkg.Now().In(datetime.IST))
	// doing tenure-1 months because we are adding this in the next emi date
	expiryDateTime := datetime.DateToTime(nextEmiDate, datetime.IST).AddDate(0, int(loanReq.GetDetails().GetLoanInfo().GetTenureInMonths())-1, 0)
	expiryDate := datetime.TimeToDateInLoc(expiryDateTime, datetime.IST)
	expiryDateString := datetime.DateToString(expiryDate, "02-01-2006", datetime.IST)
	pfAmount, _ := money.Sum(loanReq.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee(), loanReq.GetDetails().GetLoanInfo().GetDeductions().GetGst())
	// calculating total amount payable with rounded off EMI value
	totalAmountPayableWithRoundOffEmi := money.Multiply(money.Round(loanReq.GetDetails().GetLoanInfo().GetEmiAmount(), 0), decimal.NewFromInt32(loanReq.GetDetails().GetLoanInfo().GetTenureInMonths()))
	interestAmountWithoutBpi, _ := money.Subtract(totalAmountPayableWithRoundOffEmi, loanReq.GetDetails().GetLoanInfo().GetAmount())
	totalInterestAmount, _ := money.Sum(interestAmountWithoutBpi, loanReq.GetDetails().GetLoanInfo().GetDeductions().GetAdvanceInterest())
	amountToBePaidViaBorrower, _ := money.Sum(totalAmountPayableWithRoundOffEmi, loanReq.GetDetails().GetLoanInfo().GetDeductions().GetTotalDeductions())

	var pfAmountPercentage float64
	if len(loanOffer.GetProcessingInfo().GetProcessingFee()) > 0 {
		pfAmountPercentage = loanOffer.GetProcessingInfo().GetProcessingFee()[0].GetPercentage()
	}

	mp := map[string]string{
		palVgPb.KfsOptions_Field_Name_FIELD_NAME.String():                               name,
		palVgPb.KfsOptions_Field_Name_FIELD_ADDRESS.String():                            address.ConvertPostalAddressToTitleString(permanentAddress),
		palVgPb.KfsOptions_Field_Name_FIELD_EMAIL.String():                              user.GetProfile().GetEmail(),
		palVgPb.KfsOptions_Field_Name_FIELD_MOBILE.String():                             user.GetProfile().GetPhoneNumber().ToStringNationalNumber(),
		palVgPb.KfsOptions_Field_Name_FIELD_AMOUNT.String():                             money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetAmount(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_AMOUNT_IN_WORDS.String():                    money.ToWordsInIndianFormatWithoutRupeesSuffix(loanReq.GetDetails().GetLoanInfo().GetAmount(), true),
		palVgPb.KfsOptions_Field_Name_FIELD_PERIOD.String():                             fmt.Sprintf("%d", loanReq.GetDetails().GetLoanInfo().GetTenureInMonths()),
		palVgPb.KfsOptions_Field_Name_FIELD_ROI.String():                                fmt.Sprintf("%.2f", loanReq.GetDetails().GetLoanInfo().GetInterestRate()),
		palVgPb.KfsOptions_Field_Name_FIELD_EMI.String():                                money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetEmiAmount(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_DATE.String():                               dateToday,
		palVgPb.KfsOptions_Field_Name_FIELD_PF_AMOUNT.String():                          money.ToDisplayStringInIndianFormat(pfAmount, 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_BPI.String():                                money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetDeductions().GetAdvanceInterest(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_AGE.String():                                currentAge,
		palVgPb.KfsOptions_Field_Name_FIELD_FATHER_HUSBAND_NAME.String():                fmt.Sprintf("%s %s", user.GetProfile().GetFatherName().GetFirstName(), user.GetProfile().GetFatherName().GetLastName()),
		palVgPb.KfsOptions_Field_Name_FIELD_FIRST_EMI_DATE.String():                     nextEmiDateString,
		palVgPb.KfsOptions_Field_Name_FIELD_ACCOUNT_NUMBER.String():                     accountNum,
		palVgPb.KfsOptions_Field_Name_FIELD_IFSC.String():                               ifscCode,
		palVgPb.KfsOptions_Field_Name_FIELD_APR.String():                                fmt.Sprintf("%.2f", GetApr(loanReq.GetDetails().GetLoanInfo().GetDisbursalAmount(), loanReq.GetDetails().GetLoanInfo().GetEmiAmount(), loanReq.GetDetails().GetLoanInfo().GetTenureInMonths())),
		palVgPb.KfsOptions_Field_Name_FIELD_PF_RATE.String():                            fmt.Sprintf("%.2f", pfAmountPercentage),
		palVgPb.KfsOptions_Field_Name_FIELD_EXPIRY.String():                             expiryDateString,
		palVgPb.KfsOptions_Field_Name_NET_LOAN_AMOUNT.String():                          money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetDisbursalAmount(), 0, false),
		palVgPb.KfsOptions_Field_Name_INTEREST_AMOUNT.String():                          money.ToDisplayStringInIndianFormat(totalInterestAmount, 0, false),
		palVgPb.KfsOptions_Field_Name_TOTAL_AMOUNT.String():                             money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetTotalPayable(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_LENTRA_REF_ID.String():                      "FI_" + loanReq.GetVendorRequestId(),
		palVgPb.KfsOptions_Field_Name_FIELD_SANCTION_AMOUNT.String():                    money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetAmount(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_TENURE.String():                             fmt.Sprintf("%d", loanReq.GetDetails().GetLoanInfo().GetTenureInMonths()),
		palVgPb.KfsOptions_Field_Name_FIELD_FIRST_EMI.String():                          nextEmiDateString,
		palVgPb.KfsOptions_Field_Name_FIELD_EMI_NUMBER.String():                         fmt.Sprintf("%d", loanReq.GetDetails().GetLoanInfo().GetTenureInMonths()),
		palVgPb.KfsOptions_Field_Name_FIELD_EMI_AMOUNT.String():                         money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetEmiAmount(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_PROCESSING_FEE.String():                     money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_GST.String():                                money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetDeductions().GetGst(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_RATE_OF_INTEREST.String():                   fmt.Sprintf("%.2f", loanReq.GetDetails().GetLoanInfo().GetInterestRate()),
		palVgPb.KfsOptions_Field_Name_FIELD_NET_LOAN_AMOUNT.String():                    money.ToDisplayStringInIndianFormat(loanReq.GetDetails().GetLoanInfo().GetDisbursalAmount(), 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_LOAN_AMOUNT_TO_BE_PAID_BY_BOROWWER.String(): money.ToDisplayStringInIndianFormat(amountToBePaidViaBorrower, 0, false),
		palVgPb.KfsOptions_Field_Name_FIELD_RATE_OF_INTEREST_CAPS.String():              fmt.Sprintf("%.2f", loanReq.GetDetails().GetLoanInfo().GetInterestRate()),
	}
	fillFedRpsDetailsInMap(mp, loanReq)
	return mp
}

func fillFedRpsDetailsInMap(mp map[string]string, lr *palPb.LoanRequest) {
	principalAmt := money.ToFloat(lr.GetDetails().GetLoanInfo().GetAmount())
	interestRate := lr.GetDetails().GetLoanInfo().GetInterestRate()
	tenure := lr.GetDetails().GetLoanInfo().GetTenureInMonths()

	// It is converted to a monthly rate for EMI calculation.
	monthlyRate := interestRate / 12 / 100

	// EMI calculation using the formula
	power := math.Pow(1+monthlyRate, float64(tenure))
	emi := (principalAmt * monthlyRate * power) / (power - 1)

	outstanding := principalAmt
	totalInterest := 0.0

	// Loop through each month to generate the repayment schedule.
	for month := 1; month <= 48; month++ {
		interest := outstanding * monthlyRate
		principalPaid := emi - interest
		currentEmi := emi

		// Adjust for the last month to ensure the loan is fully paid off.
		if month == int(tenure) {
			principalPaid = outstanding
			currentEmi = principalPaid + interest
		}

		outstandingAfterPayment := outstanding - principalPaid
		if outstandingAfterPayment < 0.01 {
			outstandingAfterPayment = 0
		}
		totalInterest += interest
		monthStr := strconv.Itoa(month)

		if month > int(tenure) {
			mp["INSTALMENT_NUMBER_"+monthStr] = ""
			mp["OUTSTANDING_PRINCIPAL_INSTALMENT_"+monthStr] = ""
			mp["PRINCIPAL_INSTALMENT_"+monthStr] = ""
			mp["INTEREST_INSTALMENT_"+monthStr] = ""
			mp["INSTALMENT_AMOUNT_INSTALMENT_"+monthStr] = ""
			continue
		}

		mp["INSTALMENT_NUMBER_"+monthStr] = monthStr
		mp["OUTSTANDING_PRINCIPAL_INSTALMENT_"+monthStr] = fmt.Sprintf("%.0f", outstandingAfterPayment)
		mp["PRINCIPAL_INSTALMENT_"+monthStr] = fmt.Sprintf("%.0f", principalPaid)
		mp["INTEREST_INSTALMENT_"+monthStr] = fmt.Sprintf("%.0f", interest)
		mp["INSTALMENT_AMOUNT_INSTALMENT_"+monthStr] = fmt.Sprintf("%.0f", currentEmi)

		outstanding = outstandingAfterPayment
	}
}

// TODO : move
func (r *RpcHelper) SendNotificationWithGoRoutine(ctx context.Context, req SendNotificationRequest) {
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		if err := r.SendNotification(ctx, req); err != nil {
			logger.Error(ctx, fmt.Sprintf("Failed to send Notification: %s", req.NotificationType.String()), zap.Error(err))
		}
	})
}

// TODO : move to notification specific module
func (r *RpcHelper) SendNotification(ctx context.Context, req SendNotificationRequest) error {
	// temporarily switching off pre-pay notifications as suggested by product (prajwal)
	// TODO(mounish): if pre-pay PN to be removed permanently, remove the callers everywhere and remove this check
	if req.NotificationType == palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_PRE_PAY {
		logger.Info(ctx, "skipping notification for prepay temporarily",
			zap.String(logger.ACTOR_ID_V2, req.ActorId),
			zap.String("flow", req.Flow.String()),
			zap.String(logger.LOAN_REQUEST_ID, req.LoanReqId),
		)
		return nil
	}

	user, err := r.GetUserByActorId(ctx, req.ActorId)
	if err != nil {
		return err
	}
	var loanStepExecution *palPb.LoanStepExecution
	if req.Flow != palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_UNSPECIFIED {
		loanStepExecution, err = r.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.LoanReqId, req.Flow, req.Step)
		if err != nil {
			return errors.New("Cannot get Loan Step Execution By Latest Ref Id and Flow")
		}
	}

	// If notification already sent
	if r.checkIfNotificationAlreadySent(ctx, req.NotificationType, loanStepExecution) {
		return nil
	}
	title, desc, campaignName := r.getNotificationTitleAndDescription(req.NotificationType, req)
	if title == "" || desc == "" {
		return errors.New(fmt.Sprintf("error in getting notification details, title: %v, desc: %v", title, desc))
	}
	sendMessageResponse, err := r.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_GUARANTEED,
		Medium: commsPb.Medium_NOTIFICATION,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{
			UserId: user.GetId(),
		},
		CampaignName: campaignName,
		Message: &commsPb.SendMessageRequest_Notification{
			Notification: &commsPb.NotificationMessage{
				Notification: &fcmpb.Notification{
					NotificationType: fcmpb.NotificationType_SYSTEM_TRAY,
					NotificationTemplates: &fcmpb.Notification_SystemTrayTemplate{
						SystemTrayTemplate: &fcmpb.SystemTrayTemplate{
							CommonTemplateFields: &fcmpb.CommonTemplateFields{
								Title:    title,
								Body:     desc,
								Deeplink: req.Deeplink,
							},
						},
					},
				},
			},
		},
	})
	if te := epifigrpc.RPCError(sendMessageResponse, err); te != nil {
		return errors.Wrap(te, "failed Send Notification Message")
	}

	if err = r.updateLoanStepExecutionNotificationSent(ctx, req.NotificationType, loanStepExecution, sendMessageResponse.GetMessageId()); err != nil {
		logger.Error(ctx, "Failed to update Loan Step Execution Db details with message ID")
	}
	return nil
}

// TODO : move to notification specific module
func (r *RpcHelper) getNotificationTitleAndDescription(commType palCommsPb.PreApprovedLoanNotificationType, req SendNotificationRequest) (string, string, commsPb.CampaignName) {
	title, desc := "", ""
	campaignName := commsPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED
	switch commType {
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_SUCCESSFUL_VKYC:
		title = r.notifConf.VkycSuccess.Title
		desc = r.notifConf.VkycSuccess.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_VKYC_SUCCESS
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_SUCCESSFUL_MANUAL_REVIEW:
		title = r.notifConf.ManualReviewSuccess.Title
		desc = r.notifConf.ManualReviewSuccess.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_MANUAL_REVIEW_SUCCESS
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_FAILED_VKYC:
		title = r.notifConf.VkycFailed.Title
		desc = r.notifConf.VkycFailed.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_VERIFICATION_FAILURE
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_FAILED_MANUAL_REVIEW:
		title = r.notifConf.ManualReviewFailed.Title
		desc = r.notifConf.ManualReviewFailed.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_VERIFICATION_FAILURE
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LOAN_ACCOUNT_CREATION:
		title = r.notifConf.LoanAccountCreation.Title
		desc = strings.Replace(r.notifConf.LoanAccountCreation.Desc, "#accNum", mask.GetMaskedAccountNumber(req.LoanAccountNumber, "X"), 1)
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_LOAN_ACCOUNT_ACTIVE
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LOAN_ACCOUNT_CLOSURE:
		title = r.notifConf.LoanAccountClosure.Title
		desc = fmt.Sprintf(r.notifConf.LoanAccountClosure.Desc, mask.GetMaskedAccountNumber(req.LoanAccountNumber, "X"))
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_LOAN_ACCOUNT_CLOSURE
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_PRE_PAY:
		title = r.notifConf.LoanPrePay.Title
		desc = fmt.Sprintf(r.notifConf.LoanPrePay.Desc, money.ToDisplayString(req.Amount), mask.GetMaskedAccountNumber(req.LoanAccountNumber, "X"))
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_PRE_PAID_LOAN_AMOUNT
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_VKYC_PENDING:
		title = r.notifConf.VkycPending.Title
		desc = r.notifConf.VkycPending.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_DROP_OFF_VKYC_INCOMPLETE
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LIVENESS_PENDING:
		title = r.notifConf.LivenessPending.Title
		desc = r.notifConf.LivenessPending.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_DROP_OFF_LIVENESS_PENDING_FULL_KYC
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_ESIGN_PENDING:
		title = r.notifConf.ESignPending.Title
		desc = r.notifConf.ESignPending.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_DROP_OFF_ESIGN_PENDING
	case palCommsPb.PreApprovedLoanNotificationType_PAL_NOTIFICATION_TYPE_T_ZERO_EMI_PAYMENT:
		title = r.notifConf.EMIComms.Tzero.Title
		desc = r.notifConf.EMIComms.Tzero.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_EMI_DUE_SAME_DAY_LOW_BALANCE
	case palCommsPb.PreApprovedLoanNotificationType_PAL_NOTIFICATION_TYPE_T_PLUS_EMI_PAYMENT:
		title = r.notifConf.EMIComms.Tplus.Title
		desc = r.notifConf.EMIComms.Tplus.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_EMI_MISSED_LATE_FEE
	case palCommsPb.PreApprovedLoanNotificationType_PAL_NOTIFICATION_TYPE_T_PLUS_LOW_SAVINGS_BAL_EMI_PAYMENT:
		title = r.notifConf.EMIComms.TplusLowBal.Title
		desc = r.notifConf.EMIComms.TplusLowBal.Desc
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_EMI_MISSED_LOW_BALANCE
	case palCommsPb.PreApprovedLoanNotificationType_PAL_NOTIFICATION_TYPE_T_MINUS_LOW_SAVINGS_BAL_EMI_PAYMENT:
		title = r.notifConf.EMIComms.TminusLowBal.Title
		desc = fmt.Sprintf(r.notifConf.EMIComms.TminusLowBal.Desc, money.ToDisplayStringInIndianFormat(req.Amount, 0, true), datetime.DateToString(req.Date, "02 Jan", datetime.IST))
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_EMI_DUE_UPCOMING_LOW_BALANCE
	case palCommsPb.PreApprovedLoanNotificationType_PAL_NOTIFICATION_TYPE_T_MINUS_EMI_PAYMENT:
		title = r.notifConf.EMIComms.Tminus.Title
		desc = fmt.Sprintf(r.notifConf.EMIComms.Tminus.Desc, money.ToDisplayStringInIndianFormat(req.Amount, 0, true), datetime.DateToString(req.Date, "02 Jan", datetime.IST))
		campaignName = commsPb.CampaignName_CAMPAIGN_NAME_PERSONAL_LOAN_EMI_DUE_UPCOMING_HAS_BALANCE
	default:
		// do nothing
	}
	return title, desc, campaignName
}

// TODO : move to notification specific module
func (r *RpcHelper) checkIfNotificationAlreadySent(ctx context.Context, commType palCommsPb.PreApprovedLoanNotificationType, loanStepExecution *palPb.LoanStepExecution) bool {
	switch commType {
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_SUCCESSFUL_VKYC,
		palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_FAILED_VKYC:
		if loanStepExecution.GetDetails().GetVkycStepData() != nil {
			if _, ok := loanStepExecution.GetDetails().GetVkycStepData().GetNotificationTypeIdMap()[commType.String()]; ok {
				logger.Error(ctx, "VKYC Notification Already Sent!")
				return true
			}
			if loanStepExecution.GetDetails().GetVkycStepData().GetNotificationId() != "" {
				logger.Error(ctx, " VKYC Notification already sent through notifId!")
				return true
			}
		}

	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_SUCCESSFUL_MANUAL_REVIEW,
		palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_FAILED_MANUAL_REVIEW:
		if loanStepExecution.GetDetails().GetManualReviewStepData() != nil {
			if _, ok := loanStepExecution.GetDetails().GetManualReviewStepData().GetNotificationTypeIdMap()[commType.String()]; ok {
				logger.Error(ctx, "Manual Review failed Notification Already Sent!")
				return true
			}
			if loanStepExecution.GetDetails().GetManualReviewStepData().GetNotificationId() != "" {
				logger.Error(ctx, " Manual Review Notification Already Sent!")
				return true
			}
		}
	default:
		return false
	}
	return false
}

// TODO : move to notification specific module
func (r *RpcHelper) updateLoanStepExecutionNotificationSent(ctx context.Context, commType palCommsPb.PreApprovedLoanNotificationType, loanStepExecution *palPb.LoanStepExecution, messageId string) error {
	notifTypeIdMap := make(map[string]*palPb.ListOfString)
	notifTypeIdMap[commType.String()] = &palPb.ListOfString{Values: []string{messageId}}
	switch commType {
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_SUCCESSFUL_VKYC,
		palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_FAILED_VKYC,
		palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_VKYC_PENDING:
		if loanStepExecution.GetDetails().GetVkycStepData().GetNotificationTypeIdMap() == nil {
			loanStepExecution.GetDetails().GetVkycStepData().NotificationTypeIdMap = notifTypeIdMap
		} else {
			loanStepExecution.GetDetails().GetVkycStepData().GetNotificationTypeIdMap()[commType.String()].Values = append(loanStepExecution.GetDetails().GetVkycStepData().GetNotificationTypeIdMap()[commType.String()].Values, messageId)
		}
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_SUCCESSFUL_MANUAL_REVIEW,
		palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_FAILED_MANUAL_REVIEW:
		if loanStepExecution.GetDetails().GetManualReviewStepData().GetNotificationTypeIdMap() == nil {
			loanStepExecution.GetDetails().GetManualReviewStepData().NotificationTypeIdMap = notifTypeIdMap
		} else {
			loanStepExecution.GetDetails().GetManualReviewStepData().GetNotificationTypeIdMap()[commType.String()].Values = append(loanStepExecution.GetDetails().GetManualReviewStepData().GetNotificationTypeIdMap()[commType.String()].Values, messageId)
		}
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LIVENESS_PENDING:
		if loanStepExecution.GetDetails().GetLivenessStepData().GetNotificationTypeIdMap() == nil {
			loanStepExecution.GetDetails().GetLivenessStepData().NotificationTypeIdMap = notifTypeIdMap
		} else {
			loanStepExecution.GetDetails().GetLivenessStepData().GetNotificationTypeIdMap()[commType.String()].Values = append(loanStepExecution.GetDetails().GetLivenessStepData().GetNotificationTypeIdMap()[commType.String()].Values, messageId)
		}
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_ESIGN_PENDING:
		if loanStepExecution.GetDetails().GetESignStepData().GetNotificationTypeIdMap() == nil {
			loanStepExecution.GetDetails().GetESignStepData().NotificationTypeIdMap = notifTypeIdMap
		} else {
			loanStepExecution.GetDetails().GetESignStepData().GetNotificationTypeIdMap()[commType.String()].Values = append(loanStepExecution.GetDetails().GetESignStepData().GetNotificationTypeIdMap()[commType.String()].Values, messageId)
		}
	default:
		return nil
	}
	if err := r.loanStepExecutionsDao.Update(ctx, loanStepExecution, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
	}); err != nil {
		return errors.New("Unable to update loan step execution DB")
	}
	return nil
}

// GetOrderAndTxn The method returns the order activity status, the transaction id, and error if any
func (r *RpcHelper) GetOrderAndTxn(ctx context.Context, refId string) (palPb.GetLoanActivityStatusResponse_ActivityStatus, *paymentPb.Transaction, error) {
	orderResp, err := r.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{
			ClientReqId: refId,
		},
	})
	if te := epifigrpc.RPCError(orderResp, err); te != nil {
		if orderResp.GetStatus().IsRecordNotFound() {
			return palPb.GetLoanActivityStatusResponse_UNSPECIFIED, nil, epifierrors.ErrRecordNotFound
		}
		return palPb.GetLoanActivityStatusResponse_UNSPECIFIED, nil, fmt.Errorf("failed to fetch order by client request id, %w", te)
	}

	orderTxnRes, err := r.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{
		OrderId: orderResp.GetOrder().GetId(),
	})
	if te := epifigrpc.RPCError(orderTxnRes, err); te != nil {
		return palPb.GetLoanActivityStatusResponse_UNSPECIFIED, nil, fmt.Errorf("failed to fetch order and txns by order id, %w", te)
	}

	// Getting the status from order which is used for our payment request and
	// mapping its progress status to our enum for statuses
	order := orderResp.GetOrder()
	switch {
	case order.GetStatus() == orderPb.OrderStatus_PAID:
		if len(orderTxnRes.GetOrderWithTransactions().GetTransactions()) == 0 {
			return palPb.GetLoanActivityStatusResponse_UNSPECIFIED, nil, fmt.Errorf("no transaction exists in the order")
		}
		return palPb.GetLoanActivityStatusResponse_COMPLETED, orderTxnRes.GetOrderWithTransactions().GetTransactions()[0], nil
	case order.GetStatus() == orderPb.OrderStatus_PAYMENT_FAILED, order.GetStatus() == orderPb.OrderStatus_PAYMENT_REVERSED:
		if len(orderTxnRes.GetOrderWithTransactions().GetTransactions()) > 0 {
			return palPb.GetLoanActivityStatusResponse_FAILED, orderTxnRes.GetOrderWithTransactions().GetTransactions()[0], nil
		}
		return palPb.GetLoanActivityStatusResponse_FAILED, nil, nil
	case order.GetStatus() == orderPb.OrderStatus_CREATED && order.IsExpired(true):
		logger.Info(ctx, "order had expired", zap.String(logger.ORDER_ID, order.GetId()))
		return palPb.GetLoanActivityStatusResponse_FAILED, nil, nil

	default:
		return palPb.GetLoanActivityStatusResponse_PENDING, nil, nil
	}
}

// TODO : move behind vendor interface
// GetCustomerDetailsFromVG calls VG's FetchCustomerDetails RPC and returns response
func (r *RpcHelper) GetCustomerDetailsFromVG(ctx context.Context, vendor palPb.Vendor, actorId string) (*vgCustomerPb.FetchCustomerDetailsResponse, error) {
	user, err := r.GetUserByActorId(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get user by actorID")
	}

	deviceId, deviceToken, userProfileId, err := r.GetDeviceAuthDetails(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get auth details by actorID")
	}

	custId, err := r.GetVendorCustomerId(ctx, actorId, PalVendorToVgVendor[vendor])
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch customer id by actor id")
	}
	requestId := idgen.FederalRandomDigitsSequence(idgen.FederalFetchCustomerDetailsPrefix, 5)
	vgReq := &vgCustomerPb.FetchCustomerDetailsRequest{
		Header: &commonvgpb.RequestHeader{Vendor: PalVendorToVgVendor[vendor]},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			CustomerId:    custId,
		},
		RequestId:   requestId,
		PhoneNumber: user.GetProfile().GetPhoneNumber(),
		ChannelType: vgCustomerPb.ChannelType_APP,
	}

	vgRes, err := r.vgCustomerClient.FetchCustomerDetails(ctx, vgReq)
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get customer details from VG")
	}
	return vgRes, nil
}

// TODO : move behind vendor interface
func (r *RpcHelper) GetBalance(ctx context.Context, loanAccount *palPb.LoanAccount) (*money2.Money, error) {
	deviceId, deviceToken, userProfileId, err := r.GetDeviceAuthDetails(ctx, loanAccount.GetActorId())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch device auth details for actor")
	}
	entityRes, err := r.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: loanAccount.GetActorId()})
	if te := epifigrpc.RPCError(entityRes, err); te != nil {
		return nil, fmt.Errorf("failed to get entity details by actor id ")
	}
	res, err := r.savingsVgClient.GetBalance(ctx, &savingsVgPb.GetBalanceRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
		},
		AccountNumber: loanAccount.GetAccountNumber(),
		PhoneNumber:   entityRes.GetMobileNumber(),
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, errors.Wrap(te, "failed to get loan account balance")
	}

	return res.GetLedgerBalance(), nil
}

func (r *RpcHelper) GetUtrAndPaymentTimestampFromTransactionId(ctx context.Context, transactionId string) (string, *timestampPb.Timestamp, error) {
	transactionRes, transErr := r.paymentClient.GetTransaction(ctx, &paymentPb.GetTransactionRequest{
		Identifier: &paymentPb.GetTransactionRequest_TransactionId{
			TransactionId: transactionId,
		},
		GetReqInfo: false,
	})
	if te := epifigrpc.RPCError(transactionRes, transErr); te != nil {
		return "", nil, errors.Wrap(te, "failed to fetch transaction from transaction from transaction id")
	}

	utr, utrErr := r.GetUtr(ctx, transactionRes.GetTransaction())
	if utrErr != nil || utr == "" {
		return "", nil, errors.Wrap(utrErr, "Failed to fetch UTR")
	}
	return utr, GetTransactionTimestampWithBestEffort(transactionRes.GetTransaction()), nil
}

func (r *RpcHelper) GetUtr(ctx context.Context, transaction *paymentPb.Transaction) (string, error) {
	if transaction.GetUtr() == "" {
		orderRes, orderErr := r.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{
			OrderId: transaction.GetOrderId(),
		})
		if te := epifigrpc.RPCError(orderRes, orderErr); te != nil {
			return "", errors.Wrap(te, "failed to fetch order from orderWithTransactions by order id")
		}
		return orderRes.GetOrderWithTransactions().GetOrder().GetExternalId(), nil
	}
	return transaction.GetUtr(), nil
}

func GetTransactionTimestampWithBestEffort(transaction *paymentPb.Transaction) *timestampPb.Timestamp {
	switch {
	case transaction.GetCreditedAt() != nil:
		return transaction.GetCreditedAt()
	case transaction.GetDebitedAt() != nil:
		return transaction.GetDebitedAt()
	case transaction.GetExecutionTS() != nil:
		return transaction.GetExecutionTS()
	default:
		return transaction.GetCreatedAt()
	}
}

// TODO : move to notif module
func (r *RpcHelper) CheckAndSendDropOffComms(ctx context.Context, lse *palPb.LoanStepExecution, commType palCommsPb.PreApprovedLoanNotificationType) {
	notifReq := SendNotificationRequest{
		ActorId:          lse.GetActorId(),
		LoanReqId:        lse.GetRefId(),
		Flow:             lse.GetFlow(),
		Step:             lse.GetStepName(),
		NotificationType: commType,
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		},
	}
	if !r.notifConf.DropOffComms.Enable || isBlackoutTime(r.notifConf.DropOffComms.BlackoutTimeStart, r.notifConf.DropOffComms.BlackoutTimeStop) {
		return
	}
	switch commType {
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_VKYC_PENDING:
		if r.toSendVkycPendingNotification(lse, commType) {
			r.SendNotificationWithGoRoutine(ctx, notifReq)
		}
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_ESIGN_PENDING:
		if r.toSendESignPendingNotification(lse, commType) {
			r.SendNotificationWithGoRoutine(ctx, notifReq)
		}
	case palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LIVENESS_PENDING:
		if r.toSendLivenessPendingNotification(lse, commType) {
			r.SendNotificationWithGoRoutine(ctx, notifReq)
		}
	}
}

// TODO : move to notif module
func (r *RpcHelper) toSendVkycPendingNotification(lse *palPb.LoanStepExecution, commType palCommsPb.PreApprovedLoanNotificationType) bool {
	for i, val := range r.notifConf.DropOffComms.VkycWaitingTimes {
		if lse.GetDetails().GetVkycStepData() != nil && len(lse.GetDetails().GetVkycStepData().GetNotificationTypeIdMap()[commType.String()].GetValues()) < (i+1) &&
			(lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS || lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED) &&
			datetime.IsAfter(timestampPb.New(time.Now().In(datetime.IST)), timestampPb.New(lse.GetCreatedAt().AsTime().In(datetime.IST).Add(time.Duration(val)*time.Minute))) {
			return true
		}
	}
	return false
}

// TODO : move to notif module
func (r *RpcHelper) toSendESignPendingNotification(lse *palPb.LoanStepExecution, commType palCommsPb.PreApprovedLoanNotificationType) bool {
	for i, val := range r.notifConf.DropOffComms.ESignWaitingTimes {
		if lse.GetDetails().GetESignStepData() != nil && len(lse.GetDetails().GetESignStepData().GetNotificationTypeIdMap()[commType.String()].GetValues()) < (i+1) &&
			(lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS || lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED) &&
			datetime.IsAfter(timestampPb.New(time.Now().In(datetime.IST)), timestampPb.New(lse.GetCreatedAt().AsTime().In(datetime.IST).Add(time.Duration(val)*time.Minute))) {
			return true
		}
	}
	return false
}

// TODO : move to notif module
func (r *RpcHelper) toSendLivenessPendingNotification(lse *palPb.LoanStepExecution, commType palCommsPb.PreApprovedLoanNotificationType) bool {
	for i, val := range r.notifConf.DropOffComms.LivenessWaitingTimes {
		if lse.GetDetails().GetLivenessStepData() != nil && len(lse.GetDetails().GetLivenessStepData().GetNotificationTypeIdMap()[commType.String()].GetValues()) < (i+1) &&
			(lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS || lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED) &&
			datetime.IsAfter(timestampPb.New(time.Now().In(datetime.IST)), timestampPb.New(lse.GetCreatedAt().AsTime().In(datetime.IST).Add(time.Duration(val)*time.Minute))) {
			return true
		}
	}
	return false
}

// TODO : move along with CheckAndSendDropOffComms
func isBlackoutTime(startTime, endTime string) bool {
	res, _ := datetime.CurIstTimeLieBetweenStartAndEndInHHMM(startTime, endTime)
	if res {
		return true
	}
	return false
}

func (r *RpcHelper) SendSignalSync(ctx context.Context, orchId string, signalName string, payload []byte) error {
	res, err := r.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     orchId,
				Client: workflowPb.Client_PRE_APPROVED_LOAN,
			},
		},
		SignalId:  signalName,
		Payload:   payload,
		Ownership: epificontext.OwnershipFromContext(ctx),
		// QualityOfService: celestialPb.QoS_BEST_EFFORT,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return err
	}
	return nil
}

// TODO : move to vendor interface
func (r *RpcHelper) IsPinCodeServiceable(vendor palPb.Vendor, postalCode string, loanProgram palPb.LoanProgram) bool {
	switch vendor {
	case palPb.Vendor_LIQUILOANS:
		// We're removing pin code serviceability check from BE for ATL flows. These checks will be taken care at Fi BRE
		if loanProgram == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND || loanProgram == palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL ||
			loanProgram == palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION || loanProgram == palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL {
			return true
		}
		if loanProgram == palPb.LoanProgram_LOAN_PROGRAM_FLDG ||
			loanProgram == palPb.LoanProgram_LOAN_PROGRAM_STPL {
			if llSubventionServiceablePostalCodesMap[postalCode] {
				return true
			}
			return false
		}
		if llServiceablePostalCodesMap[postalCode] {
			return true
		}
	case palPb.Vendor_ABFL:
		if abflServiceablePostalCodesMap[postalCode] {
			return true
		}
	case palPb.Vendor_FEDERAL:
		if loanProgram == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB {
			if federalNtbServiceablePostalCodesMap[postalCode] {
				return true
			}
			// TODO: mark it false once pin code list is added
			// return false
		}
		return true
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return true
	case palPb.Vendor_EPIFI_TECH:
		return true
	}
	return true
}

func (r *RpcHelper) IsRiskyUser(ctx context.Context, actorId string) (bool, error) {
	profileRes, err := r.profileClient.GetUserProfile(ctx, &profilePb.GetUserProfileRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(profileRes, err); te != nil {
		return false, errors.Wrap(te, "failed to get userProfile info for PL flow")
	}
	if len(profileRes.GetAccountsInfo()) > 0 {
		return profileRes.GetAccountsInfo()[0].GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE, nil
	}
	return false, nil
}

// TODO : move to vendor interface along with its usecases
func (r *RpcHelper) GetVendorCustomerId(ctx context.Context, actorId string, vendor commonvgpb.Vendor) (string, error) {
	bcResp, errResp := r.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: vendor,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
		return "", fmt.Errorf("failed to fetch bank customer by actor id: %w", err)
	}
	return bcResp.GetBankCustomer().GetVendorCustomerId(), nil
}

func (r *RpcHelper) GetConnFromProvider(ctx context.Context, dbResourceProvider *storage.DBResourceProvider[*gorm.DB], ow commontypes.Ownership) (*gorm.DB, error) {
	dbConn, err := dbResourceProvider.GetResourceForOwnership(ow)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "err in GetResourceForOwnership")
	}
	return gormctxv2.FromContextOrDefault(ctx, dbConn), nil
}

// TODO : move behind vendor program interface
func (r *RpcHelper) IsFullSalaryProgramActive(ctx context.Context, actorId string) (bool, error) {
	salaryStatusResp, salaryStatusErr := r.salaryClient.GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err := epifigrpc.RPCError(salaryStatusResp, salaryStatusErr); err != nil {
		return false, errors.Wrap(err, "error while fetching salary program details for user")
	}
	// if salary program status is not complete then return false
	if salaryStatusResp.GetRegistrationStatus() != salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		return false, nil
	}

	// if salary program status is complete
	// check if user is currently active in salary program
	salaryActivationResp, salaryActivationErr := r.salaryClient.GetLatestActivationDetailsActiveAtTime(ctx, &salaryPb.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: salaryStatusResp.GetRegistrationId(),
		ActiveAtTime:   timestampPb.New(time.Now().In(datetime.IST)),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user and its activation type.
		ActivationKind: salaryPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	if te := epifigrpc.RPCError(salaryActivationResp, salaryActivationErr); te != nil {
		// if we encounter record not found that means user is not active in salary program
		if salaryActivationResp.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		return false, errors.Wrap(te, "error while determining user's salary activation status")
	}
	if salaryActivationResp.GetActivationType() != salaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION {
		return false, nil
	}
	// return true when user's salary program status is completed and is currently in active state.
	return true, nil
}

// TODO : move this along with GetWebPageHtml
func (r *RpcHelper) getDebitCardCodeFromIfsc(ctx context.Context, ifscCode string) (string, error) {
	ifscResp, ifscErr := r.idfcVgClient.GetIfscDetails(ctx, &idfc.GetIfscDetailsRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IDFC},
		CorrelationId: uuid.NewString(),
		Identifier: &idfc.GetIfscDetailsRequest_Identifier{
			IfscCode: ifscCode,
		},
	})
	if rpcErr := epifigrpc.RPCError(ifscResp, ifscErr); rpcErr != nil {
		return "", errors.Wrap(rpcErr, "error while fetching ifsc details")
	}
	if len(ifscResp.GetDetails()) != 1 {
		return "", errors.New("ifsc details fetched is not one")
	}
	debitCardCode := ifscResp.GetDetails()[0].GetDebitCardCode()
	if debitCardCode == "" {
		return "", errors.New("debit card code is empty")
	}
	return debitCardCode, nil
}

// TODO : move this from rpc helper to vendor provider
// nolint:gosec
func (r *RpcHelper) GetWebPageHtml(ctx context.Context, actorId string, lrId string, vendor palPb.Vendor, lp palPb.LoanProgram, accountInfo *palPb.MandateData_BankingDetails_AccountDetails, programVersion enums.LoanProgramVersion) (string, error) {
	loanApplicant, err := r.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, actorId, vendor, lp, programVersion)
	if err != nil {
		return "", errors.Wrap(err, "failed to fetch loan applicant")
	}
	if accountInfo == nil {
		savingsAccount, savingsErr := r.GetSavingsAccountDetails(ctx, actorId, commonvgpb.Vendor_FEDERAL_BANK, accTypes.AccountProductOffering_APO_REGULAR)
		if savingsErr != nil {
			return "", errors.Wrap(savingsErr, "failed to fetch savings account details for the user")
		}
		accountInfo = &palPb.MandateData_BankingDetails_AccountDetails{
			AccountNumber:     savingsAccount.GetAccountNo(),
			AccountHolderName: savingsAccount.GetPrimaryAccountHolder(),
			IfscCode:          savingsAccount.GetIfscCode(),
		}
	}

	user, err := r.GetUserByActorId(ctx, actorId)
	if err != nil {
		return "", errors.Wrap(err, "failed to fetch user details")
	}

	switch {
	case vendor == palPb.Vendor_IDFC && lp == palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		// generate a random merchant txn id and persist it in loan step details
		merchantTxnId := idgen.RandAlphaNumericString(25)
		lse, err := r.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lrId, palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE)
		if err != nil {
			return "", errors.Wrap(err, "failed to fetch loan step execution")
		}

		// identify debit card code
		debitCardCode, dccErr := r.getDebitCardCodeFromIfsc(ctx, accountInfo.GetIfscCode())
		if dccErr != nil {
			return "", errors.Wrap(dccErr, "error while identifying debit card code")
		}

		// vg call to fetch the mandate webpage for IDFC
		mandateRegRes, err := r.idfcVgClient.GetMandateWebPage(ctx, &idfc.GetMandateWebPageRequest{
			MerchantTxnId: merchantTxnId,
			ConsumerId:    loanApplicant.GetVendorRequestId(),
			TxnAmount: &money2.Money{
				CurrencyCode: "INR",
				Units:        1,
				Nanos:        0,
			},
			MaxAmount: &money2.Money{
				CurrencyCode: "INR",
				Units:        100000,
				Nanos:        0,
			},
			DebitStartDate: datetime.TimeToDateInLoc(time.Now().AddDate(0, 0, 1), datetime.IST),
			DebitEndDate:   datetime.TimeToDateInLoc(time.Now().AddDate(10, 0, 1), datetime.IST),
			PhoneNumber:    user.GetProfile().GetPhoneNumber(),
			Email:          user.GetProfile().GetEmail(),
			BankAccountDetails: &types.BankAccountDetails{
				AccountNumber: accountInfo.GetAccountNumber(),
				AccountName:   user.GetProfile().GetPanName().ToString(),
				AccountType:   accounts.Type_SAVINGS,
				Ifsc:          accountInfo.GetIfscCode(),
			},
			DebitCardCode: debitCardCode,
		})
		if te := epifigrpc.RPCError(mandateRegRes, err); te != nil {
			return "", errors.Wrap(te, te.Error())
		}

		// persist the merchantTxnId in loan step table
		lse.GetDetails().GetMandateData().MerchantTxnId = merchantTxnId
		// set the mandate expiry to 8 minutes from now as per IDFC (this is in UTC)
		lse.GetDetails().GetMandateData().MandateLinkExpiry = timestampPb.New(time.Now().Add(8 * time.Minute))
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_INITIATED_AT_VENDOR
		updateErr := r.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS})
		if updateErr != nil {
			return "", errors.Wrap(updateErr, "failed to fetch loan step execution")
		}
		return mandateRegRes.GetHtmlResponse(), nil
	default:
		// do nothing
	}

	return "", nil
}

func (r *RpcHelper) GetSavingsAvailableBalance(ctx context.Context, actorId string, freshness accountBalanceEnums.DataFreshness, partnerBank commonvgpb.Vendor, productOffering accTypes.AccountProductOffering) (*money2.Money, error) {
	// getting saving account details of the user
	savingAccount, err := r.GetSavingsAccountDetails(ctx, actorId, partnerBank, productOffering)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get the savings account")
	}

	// getting the real time account balance of the user from savings client
	getAccountBalanceResp, getAccountBalanceErr := r.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{
			Id: savingAccount.GetId(),
		},
		ActorId:       actorId,
		DataFreshness: freshness,
	})
	if te := epifigrpc.RPCError(getAccountBalanceResp, getAccountBalanceErr); te != nil {
		return nil, errors.Wrap(te, "failed to get savings account balance")
	}

	return getAccountBalanceResp.GetAvailableBalance(), nil
}

// nolint: dupl
func (r *RpcHelper) InitCreditReportFetchV1(ctx context.Context, loanStep *palPb.LoanStepExecution, redirectionDl *deeplinkPb.Deeplink, pan string) (*deeplinkPb.Deeplink, error) {
	initiateResp, initiateRespErr := r.creditReportClient.StartDownloadProcess(ctx, &creditReportPb.StartDownloadProcessRequest{
		ActorId:          loanStep.GetActorId(),
		RequestId:        loanStep.GetOrchId(),
		Provenance:       creditReportPb.Provenance_PROVENANCE_PERSONAL_LOAN,
		Vendor:           commonvgpb.Vendor_EXPERIAN,
		RedirectDeeplink: redirectionDl,
		Pan:              pan,
	})
	if te := epifigrpc.RPCError(initiateResp, initiateRespErr); te != nil {
		return nil, errors.Wrap(te, "failed to initiate the credit report fetch process")
	}

	// if credit report download is completed or failed with this orch id, then return error
	if initiateResp.GetProcessStatus() == creditReportPb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED ||
		initiateResp.GetProcessStatus() == creditReportPb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED {
		return nil, errors.New("credit report fetch failed or already completed for this step")
	}

	return initiateResp.GetNextAction(), nil
}

// nolint: dupl
func (r *RpcHelper) InitCreditReportFetchV2(ctx context.Context, loanStep *palPb.LoanStepExecution, redirectionDl *deeplinkPb.Deeplink, pan string, vendor preapprovedloans.CreditReportVendor, provenance creditReportV2Pb.Provenance, name *commontypes.Name) (*deeplinkPb.Deeplink, error) {
	initiateResp, initiateRespErr := r.creditReportV2Client.StartDownloadProcess(ctx, &creditReportV2Pb.StartDownloadProcessRequest{
		ActorId:          loanStep.GetActorId(),
		RequestId:        loanStep.GetOrchId(),
		Provenance:       provenance,
		Vendor:           getVgVendorFromCreditReportVendor(vendor),
		RedirectDeeplink: redirectionDl,
		Pan:              pan,
		Name:             name,
	})
	if te := epifigrpc.RPCError(initiateResp, initiateRespErr); te != nil {
		return nil, errors.Wrap(te, "failed to initiate the credit report fetch process")
	}
	// if credit report download is completed or failed with this orch id, then return error
	if initiateResp.GetProcessStatus() == creditReportV2Pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_FAILED ||
		initiateResp.GetProcessStatus() == creditReportV2Pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED {
		return nil, errors.New("credit report fetch failed or already completed for this step")
	}
	return initiateResp.GetNextAction(), nil
}

func (r *RpcHelper) FetchIpAddress(ctx context.Context, actorId string) (string, error) {
	userDeviceRes, err := r.usersClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId:       actorId,
		PropertyTypes: []types.DeviceProperty{types.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN},
	})
	if te := epifigrpc.RPCError(userDeviceRes, err); te != nil {
		return "", errors.Wrap(te, "failed to get user device properties")
	}

	ipToken := ""
	for _, deviceProperty := range userDeviceRes.UserDevicePropertyList {
		if deviceProperty.GetDeviceProperty() == types.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN {
			ipToken = deviceProperty.GetPropertyValue().GetIpAddressToken()
		}
	}
	piiRes, err := r.obfuscatorClient.GetPIIFromToken(ctx, &obfuscator.GetPIIFromTokenRequest{Token: ipToken})
	if te := epifigrpc.RPCError(piiRes, err); te != nil {
		return "", errors.Wrap(te, "failed to get PII from token")
	}

	if piiRes.GetPii().IdType == types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS {
		return piiRes.GetPii().GetIdValue().GetIpAddress().GetIpAddress(), nil
	}
	return "", fmt.Errorf("ip address token not found for actorId: %v", actorId)
}

// TODO : move to vendor abstraction
func (r *RpcHelper) GetConfirmationCodeFromFiftyFin(ctx context.Context, lr *palPb.LoanRequest, userId int32, refId string, mfDetails map[string]float64) (string, error) {
	switch lr.GetDetails().GetOtpInfo().GetOtpType() {
	case palPb.OtpType_OTP_TYPE_CAMS_PF_FETCH:
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusCamsPortfolioOtpInitiated, lr)
		vgRes, err := r.fiftyFinVgClient.GenerateCamsOtp(ctx, &ffVgPb.GenerateCamsOtpRequest{UserId: userId})
		if te := epifigrpc.RPCError(vgRes, err); te != nil {
			if vgRes.GetStatus().IsRecordNotFound() {
				return "", nil
			}
			return "", errors.Wrap(te, "error in GenerateCamsOtp from VG")
		}
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusCamsPortfolioOtpGenerated, lr)
		return vgRes.GetRefNo(), nil
	case palPb.OtpType_OTP_TYPE_KARVY_PF_FETCH:
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusKarvyPortfolioOtpInitiated, lr)
		vgRes, err := r.fiftyFinVgClient.GenerateKarvyOtp(ctx, &ffVgPb.GenerateKarvyOtpRequest{UserId: userId})
		if te := epifigrpc.RPCError(vgRes, err); te != nil {
			if vgRes.GetStatus().IsRecordNotFound() {
				return "", nil
			}
			return "", errors.Wrap(te, "error in GenerateCamsOtp from VG")
		}
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusKarvyPortfolioOtpGenerated, lr)
		return vgRes.GetRequestId(), nil
	case palPb.OtpType_OTP_TYPE_CAMS_LIEN_MARK:
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusCamsLienOtpInitiated, lr)
		vgRes, err := r.fiftyFinVgClient.CreateLienCams(ctx, &ffVgPb.CreateLienCamsRequest{
			UserId:    userId,
			RequestId: refId,
			PortfolioIds: []*ffVgPb.HoldingIdUnitsMap{
				{
					Map: mfDetails,
				},
			},
		})
		if te := epifigrpc.RPCError(vgRes, err); te != nil {
			return "", errors.Wrap(te, "error in CreateLienCams from VG")
		}
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusCamsLienOtpGenerated, lr)
		return vgRes.GetRefNo(), nil
	case palPb.OtpType_OTP_TYPE_KARVY_LIEN_MARK:
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusKarvyLienOtpInitiated, lr)
		vgRes, err := r.fiftyFinVgClient.CreateLienKarvy(ctx, &ffVgPb.CreateLienKarvyRequest{
			UserId:    userId,
			RequestId: refId,
			PortfolioIds: []*ffVgPb.HoldingIdUnitsMap{
				{
					Map: mfDetails,
				},
			},
		})
		if te := epifigrpc.RPCError(vgRes, err); te != nil {
			return "", errors.Wrap(te, "error in GenerateCamsOtp from VG")
		}
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusKarvyLienOtpGenerated, lr)
		return vgRes.GetRefNo(), nil
	default:
		return "", errors.New("unhandled otpType for generate otp for fiftyfin from VG")
	}
}

func (r *RpcHelper) sendOtpStatusEvent(ctx context.Context, otpStatusEvent string, lr *palPb.LoanRequest) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		r.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewLoansOtpStatusEvent(lr, otpStatusEvent))
	})
}

// TODO : move to vendor abstraction
func (r *RpcHelper) GenerateMfCentralConfirmationCode(ctx context.Context, lr *palPb.LoanRequest, otpType palPb.OtpType,
	phone *commontypes.PhoneNumber, email string) (string, error) {
	createRes, err := r.mfExternalOrdersClient.CreateMFHoldingsImport(ctx, &mfexternalorderpb.CreateMFHoldingsImportRequest{
		ActorId:    lr.GetActorId(),
		Provenance: mfexternalorderpb.Provenance_PROVENANCE_LAMF,
	})
	if rpcErr := epifigrpc.RPCError(createRes, err); rpcErr != nil {
		return "", fmt.Errorf("error while creating mf central holding import request : %w", rpcErr)
	}
	otpMedium := &mfexternalorderpb.OtpMedium{
		OtpMediumType: mfexternalorderpb.OtpMedium_OTP_MEDIUM_TYPE_PHONE_NUMBER,
		Identifier: &mfexternalorderpb.OtpMedium_PhoneNumber{
			PhoneNumber: phone,
		},
	}
	if otpType == palPb.OtpType_OTP_TYPE_MF_CENTRAL_EMAIL_PF_FETCH {
		otpMedium = &mfexternalorderpb.OtpMedium{
			OtpMediumType: mfexternalorderpb.OtpMedium_OTP_MEDIUM_TYPE_EMAIL,
			Identifier: &mfexternalorderpb.OtpMedium_Email{
				Email: email,
			},
		}
	}
	r.sendOtpStatusEvent(ctx, palEvents.OtpStatusMfcCasDetailedOtpInitiated, lr)
	initRes, err := r.mfExternalOrdersClient.InitiateHoldingsImport(ctx, &mfexternalorderpb.InitiateHoldingsImportRequest{
		ActorId:    lr.GetActorId(),
		ExternalId: createRes.GetExternalId(),
		OtpMedium:  otpMedium,
	})
	switch {
	case initRes.GetStatus().GetCode() == uint32(mfexternalorderpb.InitiateHoldingsImportResponse_INVALID_PAN_EMAIL_COMBINATION) ||
		initRes.GetStatus().GetCode() == uint32(mfexternalorderpb.InitiateHoldingsImportResponse_INVALID_PAN_MOBILE_COMBINATION) ||
		initRes.GetStatus().GetCode() == uint32(mfexternalorderpb.InitiateHoldingsImportResponse_NO_HOLDINGS_FOUND):
		logger.Info(ctx, fmt.Sprintf("mf central portfolio fetch : no portfolio found. otpMedium: %s", otpMedium.GetOtpMediumType().String()))
		return "", nil
	case err != nil || !initRes.GetStatus().IsSuccess():
		rpcErr := epifigrpc.RPCError(initRes, err)
		logger.Error(ctx, fmt.Sprintf("mf central portfolio fetch : unknown error in generate otp. otpMedium: %s", otpMedium.GetOtpMediumType().String()), zap.Error(rpcErr))
		return "", fmt.Errorf("error while creating mf central holding import request : %w", rpcErr)
	default:
		r.sendOtpStatusEvent(ctx, palEvents.OtpStatusMfcCasDetailedOtpGenerated, lr)
		logger.Info(ctx, fmt.Sprintf("mf central portfolio fetch : successfully generated otp. otpMedium: %s", otpMedium.GetOtpMediumType().String()))
		return initRes.GetExternalId(), nil
	}
}

func (r *RpcHelper) IsActorInAnyOfTheSegments(ctx context.Context, actorId string, segmentIds []string) (bool, error) {
	if len(segmentIds) == 0 {
		return false, nil
	}

	isMemberRes, err := r.segmentationClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: segmentIds,
	})
	if rpcErr := epifigrpc.RPCError(isMemberRes, err); rpcErr != nil {
		return false, fmt.Errorf("segmentationClient.IsMember rpc call failed, err : %w", rpcErr)
	}

	for segmentId, membershipRes := range isMemberRes.GetSegmentMembershipMap() {
		if membershipRes.GetSegmentStatus() != segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND {
			logger.WarnWithCtx(ctx, "segment instance not found", zap.String(logger.SEGMENT_ID, segmentId), zap.String(logger.STATUS, membershipRes.GetSegmentStatus().String()))
			continue
		}
		if membershipRes.GetIsActorMember() {
			return true, nil
		}
	}
	return false, nil
}

// getVgVendorFromCreditReportVendor returns the CIBIL as vendor only if CreditReportVendor is set to CIBIL.
// else set EXPERIAN in all other cases to support the existing flows
func getVgVendorFromCreditReportVendor(vendor preapprovedloans.CreditReportVendor) commonvgpb.Vendor {
	if vendor == preapprovedloans.CreditReportVendor_CREDIT_REPORT_VENDOR_CIBIL {
		return commonvgpb.Vendor_CIBIL
	}
	return commonvgpb.Vendor_EXPERIAN
}

func (r *RpcHelper) FetchAppVersionInfo(ctx context.Context, actorId string) (*types.AppVersionInfo, error) {
	userDeviceRes, err := r.usersClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId:       actorId,
		PropertyTypes: []types.DeviceProperty{types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO},
	})
	if te := epifigrpc.RPCError(userDeviceRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get user device properties")
	}

	for _, deviceProperty := range userDeviceRes.GetUserDevicePropertyList() {
		if deviceProperty.GetDeviceProperty() == types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO {
			return deviceProperty.GetPropertyValue().GetAppVersionInfo(), nil
		}
	}

	return nil, fmt.Errorf("failed to get the app version details from the device property")
}

func (r *RpcHelper) UpdateLoanCommunicationAddressInUser(ctx context.Context, actorId string, address *types.PostalAddress) error {

	// getting the user details
	userRes, rpcErr := r.usersClient.GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId}})
	if err := epifigrpc.RPCError(userRes, rpcErr); err != nil {
		return errors.New(fmt.Sprintf("failed to get user details, err: %v", err))
	}

	// updating communication address of user, in order to maintain the latest address in user db.
	updateAddRes, updateAddErr := r.usersClient.UpdateAddress(ctx, &userPb.UpdateAddressRequest{
		UserId:  userRes.GetUser().GetId(),
		Type:    types.AddressType_LOAN_COMMUNICATION,
		Address: address.GetBeAddress(),
	})
	if err := epifigrpc.RPCError(updateAddRes, updateAddErr); err != nil {
		return errors.New(fmt.Sprintf("error updating address in user db, err: %v", err))
	}
	return nil
}

func (r *RpcHelper) GetMandateBankAccountDetails(ctx context.Context, la *palPb.LoanAccount) (*types.BankAccountDetails, error) {
	loanProgram := la.GetLoanProgram()
	var mandateBankAccountDetails *types.BankAccountDetails

	lr, lrErr := r.lrDao.GetByLoanAccountIdAndType(ctx, la.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
	if lrErr != nil {
		logger.Error(ctx, "failed to fetch lr from la id and type", zap.Error(lrErr))
		return nil, lrErr
	}

	// fetch the lse step to get the mandate data based on the loan program
	lseFlowName := palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION
	lseStepName := palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE

	if loanProgram == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND || loanProgram == palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL ||
		loanProgram == palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL || loanProgram == palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION {
		lseStepName = palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS
	}

	lse, err := r.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lr[0].GetId(), lseFlowName, lseStepName)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "failed to fetch lse to get the user mandate bank account details")
	}

	switch {
	case lse != nil && (loanProgram == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND || loanProgram == palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL ||
		loanProgram == palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL || loanProgram == palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION):
		mandateBankAccountDetails = &types.BankAccountDetails{
			AccountName:   lse.GetDetails().GetOnboardingData().GetBankingDetails().GetAccountHolderName(),
			AccountNumber: lse.GetDetails().GetOnboardingData().GetBankingDetails().GetAccountNumber(),
			Ifsc:          lse.GetDetails().GetOnboardingData().GetBankingDetails().GetIfscCode(),
			BankName:      lse.GetDetails().GetOnboardingData().GetBankingDetails().GetBankName(),
		}
	case lse != nil && (lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE && lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed() != nil):
		mandateBankAccountDetails = &types.BankAccountDetails{
			AccountName:   lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetAccountHolderName(),
			AccountNumber: lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetAccountNumber(),
			Ifsc:          lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetIfscCode(),
			BankName:      lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetBankName(),
		}
	default:
		// user would have set up mandate on fi-federal bank account
		savingsAcc, err := r.GetSavingsAccountDetails(ctx, la.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, accTypes.AccountProductOffering_APO_REGULAR)
		if err != nil {
			return nil, errors.Wrap(err, "failed to fetch savings account details")
		}
		mandateBankAccountDetails = &types.BankAccountDetails{
			AccountName:   savingsAcc.GetPrimaryAccountHolder(),
			AccountNumber: savingsAcc.GetAccountNo(),
			Ifsc:          savingsAcc.GetIfscCode(),
			BankName:      "Federal",
		}
	}

	return mandateBankAccountDetails, nil
}
