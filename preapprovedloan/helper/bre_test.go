package helper

import (
	"encoding/base64"
	"errors"
	"testing"

	"github.com/epifi/be-common/pkg/epifierrors"
)

var (
	subJson1String = `{"accountWiseAnalysis":[{"customParameterizedVariables":{"medianAmountSalaryLast180Days":null}}]}`
	subJson2String = `{"accountWiseAnalysis":[{"customParameterizedVariables":{"medianAmountSalaryLast180Days":100000.987}}]}`
)

func Test_getSalaryFromSubJson(t *testing.T) {
	type args struct {
		subAnalString string
	}
	tests := []struct {
		name    string
		args    args
		want    float64
		err     error
		wantErr bool
	}{
		{name: "test 1, zero salary (null case)", args: args{subAnalString: subJson1String}, want: 0, wantErr: true, err: epifierrors.ErrRecordNotFound},
		{name: "test 2, correct salary decimal value ", args: args{subAnalString: subJson2String}, want: 1_00_000.987, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			subAnalBytes := []byte(base64.StdEncoding.EncodeToString([]byte(tt.args.subAnalString)))
			got, err := getSalaryFromSubJson(subAnalBytes)
			if (err != nil) != tt.wantErr {
				t.Errorf("getSalaryFromSubJson() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				if !(errors.Is(err, tt.err)) {
					t.Errorf("getSalaryFromSubJson() got error = %v, want err %v", err, tt.err)
					return
				}
			}
			if got != tt.want {
				t.Errorf("getSalaryFromSubJson() got = %v, want %v", got, tt.want)
			}
		})
	}
}
