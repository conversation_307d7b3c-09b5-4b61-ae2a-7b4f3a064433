package kubair

import (
	"context"
	"errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	feKubairPb "github.com/epifi/gamma/api/frontend/kubair"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
)

// GetAssetLandingPage returns the top components for the asset landing page
func (s *Service) GetAssetLandingPage(ctx context.Context, req *feKubairPb.GetAssetLandingPageRequest) (*feKubairPb.GetAssetLandingPageResponse, error) {
	instrumentType, ok := types.InvestmentInstrumentType_value[req.GetRequestParams().GetInstrumentType()]
	if !ok {
		logger.Error(ctx, "invalid type of instrument type", zap.String(logger.INSTRUMENT_TYPE, req.GetRequestParams().GetInstrumentType()))
		return &feKubairPb.GetAssetLandingPageResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	topComponents, err := s.componentBuilder.GetTopComponentForLandingPage(ctx, req.GetReq().GetAuth().GetActorId(), types.InvestmentInstrumentType(instrumentType))
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &feKubairPb.GetAssetLandingPageResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusRecordNotFound()},
			}, nil
		}
		logger.Error(ctx, "error while generating top component for landing page", zap.Error(err))
		return &feKubairPb.GetAssetLandingPageResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	return &feKubairPb.GetAssetLandingPageResponse{
		RespHeader:       &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
		TopComponents:    topComponents.TopComponents,
		NavigationToggle: ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/networth/default_navigation_toggle_fd.png", 28, 28)).WithTexts(commontypes.GetTextFromStringFontColourFontStyle(topComponents.AssetName, "#F6F9FD", commontypes.FontStyle_HEADLINE_M)).WithContainerPaddingSymmetrical(12, 8).WithContainer(0, 0, 24, "#28292B").WithLeftImagePadding(4),
	}, nil
}
