Application:
  Environment: "qa"
  Name: "vendornotification"

KarzaEPANWhitelist:
  EnableWhitelist: false

KarzaVkycWhitelist:
  EnableWhitelist: false

AclSmsWhitelist:
  EnableWhitelist: false

KaleyraSmsWhitelist:
  EnableWhitelist: false

AclWhatsappWhitelist:
  EnableWhitelist: false

GupshupWhitelist:
  EnableWhitelist: false

NetCoreWhitelist:
  EnableWhitelist: false

PaisabazaarWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "***********,***************"
  SoftBlock: false

SetuWhiteList:
  EnableWhitelist: false

DPandaWhitelist:
  EnableWhitelist: false

PoshVineWhitelist:
  EnableWhitelist: false

OzonetelWhitelist:
  EnableWhitelist: false

M2PWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,************,************"
  SoftBlock: true

AbflWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "************,************,************,*************"
  SoftBlock: true

MoneyviewWhiteList:
  EnableWhitelist: true
  WhitelistedIPs: "***********,*************"
  SoftBlock: true

FederalWhitelist:
  EnableWhitelist: false

UpdateTransactionEventsPublisher:
  QueueName: "qa-payment-callback-update-queue"

InboundTxnPublisher:
  QueueName: "qa-inbound-txn-queue"

InboundUpiTxnPublisher:
  QueueName: "qa-inbound-upi-txn-queue"

InboundLoanTxnPublisher:
  QueueName: "qa-loan-inbound-transaction-queue"

CreateCardCallbackPublisher:
  QueueName: "qa-card-creation-callback-queue"

DispatchPhysicalCardCallbackPublisher:
  QueueName: "qa-card-dispatch-request-callback-queue"

CheckLivenessCallbackPublisher:
  QueueName: "qa-check-liveness-callback-queue"

UPIReqAuthEventPublisher:
  QueueName: "qa-upi-req-auth-processing-queue"

UPIReqAuthMandateEventPublisher:
  QueueName: "qa-upi-req-auth-mandate-processing-queue"

UPIReqAuthValCustEventPublisher:
  QueueName: "qa-upi-req-auth-val-cust-processing-queue"

UPIReqMandateConfirmationEventPublisher:
  QueueName: "qa-upi-req-mandate-confirmation-processing-queue"

UPIRespMandateEventPublisher:
  QueueName: "qa-upi-resp-mandate-processing-queue"

UPIRespPayEventPublisher:
  QueueName: "qa-upi-resp-pay-processing-queue"

UPIReqTxnConfirmationEventPublisher:
  QueueName: "qa-upi-req-txn-confirmation-processing-queue"

UPIReqValAddressEventPublisher:
  QueueName: "qa-upi-req-val-address-processing-queue"

UPIListPspKeysEventPublisher:
  QueueName: "qa-list-psp-keys-processing-queue"

UPIListVaePublisher:
  QueueName: "qa-list-vae-processing-queue"
  BucketName: "epifi-qa-extended-sqs"

CreateDepositCallbackPublisher:
  QueueName: "qa-create-deposit-callback-queue"

PreCloseDepositCallbackPublisher:
  QueueName: "qa-preclose-deposit-callback-queue"

FdAutoRenewCallbackPublisher:
  QueueName: "qa-deposit-maturity-action-callback-queue"

AclSmsCallbackPublisher:
  QueueName: "qa-vn-acl-sms-callback-queue"

KaleyraSmsCallbackPublisher:
  QueueName: "qa-vn-kaleyra-sms-callback-queue"

AclWhatsappCallbackPublisher:
  QueueName: "qa-vn-acl-whatsapp-callback-queue"

AclWhatsappReplyPublisher:
  QueueName: "qa-vn-acl-whatsapp-reply-queue"

GupshupWhatsappCallbackPublisher:
  QueueName: "qa-comms-gupshup-whatsapp-callback-queue"

GupshupRcsCallbackPublisher:
  QueueName: "qa-comms-gupshup-rcs-callback-queue"

NetCoreSmsCallbackPublisher:
  QueueName: "qa-comms-netcore-sms-callback-queue"

AirtelSmsCallbackPublisher:
  QueueName: "qa-comms-airtel-sms-callback-queue"

AirtelWhatsappCallbackPublisher:
  QueueName: "qa-comms-airtel-whatsapp-callback-queue"

DeviceReRegCallbackPublisher:
  QueueName: "qa-device-rereg-callback-queue"

DeviceRegSMSAckPublisher:
  QueueName: "qa-device-reg-sms-ack-queue"

CustomerCreationCallbackPublisher:
  QueueName: "qa-customer-creation-callback-queue"

BankCustCallbackPublisher:
  QueueName: "qa-bankcust-customer-creation-callback-queue"

AccountCreationCallbackPublisher:
  QueueName: "qa-savings-creation-callback-queue"

FederalVkycUpdatePublisher:
  QueueName: "qa-vn-federal-vkyc-update-queue"

UpdateShippingAddressCallbackPublisher:
  QueueName: "qa-shipping-address-update-callback-queue"

KarzaVkycAgentResponsePublisher:
  QueueName: "qa-vn-karza-vkyc-agent-response-queue"

KarzaVkycAuditorResponsePublisher:
  QueueName: "qa-vn-karza-vkyc-auditor-response-queue"

KarzaVkycCallEventPublisher:
  QueueName: "qa-vn-karza-vkyc-call-event-queue"

EmailCallbackPublisher:
  QueueName: "qa-vn-email-callback-queue"

ConsentCallbackPublisher:
  QueueName: "qa-vn-aa-consent-callback-queue"

FICallbackPublisher:
  QueueName: "qa-vn-aa-fi-callback-queue"

CardTrackingCallbackPublisher:
  QueueName: "qa-card-tracking-callback-queue"

AccountLinkStatusCallbackPublisher:
  QueueName: "qa-vn-aa-account-link-status-callback-queue"

UPIReqTxnConfirmationComplaintEventPublisher:
  QueueName: "qa-upi-req-txn-confirmation-complaint-processing-queue"

OzonetelCallDetailsPublisher:
  QueueName: "qa-vn-ozonetel-call-details-queue"

UPIRespComplaintEventPublisher:
  QueueName: "qa-upi-resp-complaint-queue"

FreshchatActionCallbackPublisher:
  QueueName: "qa-vn-freshchat-action-callback-queue"

NuggetEventCallbackPublisher:
  QueueName: "qa-nugget-event-callback-queue"

HealthInsurancePolicyIssuanceEventPublisher:
  QueueName: "qa-salaryprogram-healthinsurance-policy-issuance-completion-queue"

CCNonFinancialNotificationPublisher:
  QueueName: "qa-cc-non-financial-notification-queue"

SignalWorkflowPublisher:
  QueueName: "qa-celestial-signal-workflow-queue"

SmallcaseProcessMFHoldingsWebhookPublisher:
  QueueName: "qa-vn-process-mf-holdings-webhook-extended-queue"
  BucketName: "epifi-wealth-qa-extended-sqs"

UPIReqMapperConfirmationEventPublisher:
  QueueName: "qa-upi-req-mapper-confirmation-processing-queue"

LoansFiftyfinCallbackPublisher:
  QueueName: "qa-vn-loans-fiftyfin-callback-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "qa-celestial-initiate-procrastinator-workflow-queue"

FederalEscalationUpdateEventPublisher:
  QueueName : "qa-cx-escalation-update-queue"

CcOnboardingStateUpdateEventPublisher:
  QueueName: "qa-cc-onboarding-state-update-event-callback-queue"

VendorRewardFulfillmentPublisher:
  QueueName: "qa-vendor-reward-fulfillment-event-queue"

Server:
  Ports:
    GrpcPort: 8088
    GrpcSecurePort: 9524
    HttpPort: 9999
    HttpPProfPort: 9990

Aws:
  Region: "ap-south-1"

#json file path
PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"
CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"

SyncRespHandler:
  SyncPublisher:
    Publisher:
      QueueName: "qa-vn-sync-wrapper-queue"

Flags:
  TrimDebugMessageFromStatus: false

Secrets:
  Ids:
    #Federal
    SenderCode: "qa/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "qa/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "qa/vg-vn-vgpci/federal-auth-service-access-code"
    SenderCodeLoans: "qa/vg-vn-simulator-vgpci/federal-auth-sender-code"
    AaVgVnSecretsV1: "qa/vg-vn/aa-secrets-v1"
    SahamatiPublicKeyJwk: "qa/vn/sahamati-public-key"
    SenseforthAuthApiKey: "qa/vn/senseforth-auth-api-key"
    SprinklrAuthApiKey: "qa/vg-vn/sprinklr-auth-api-key"
    # external vendor for offers redemption
    DpandaAuthApiKey: "qa/vn/dpanda-auth-api-key"
    PoshvineAuthApiKey: "qa/vn/poshvine-auth-api-key"
    RazorpayAuthApiKey: "qa/vn/razorpay-auth-api-key"
    DpandaVnSecrets: "qa/vn/dpanda-secrets"
    PoshvineVnSecrets: "qa/vn/poshvine-secrets"
    RazorpayVnSecrets: "qa/vn/razorpay-secrets"

SavenRewardVnSecrets:
  Path: "qa/vn/saven-rewards-secrets"

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendornotification/secure.log"
  MaxSizeInMBs: 50
  MaxBackups: 20

FeatureFlags:
  AllowCustomerCallbackProcessing: true
  AllowAccountCallbackProcessing: true

AA:
  AaSecretsVersionToUse: "V1"
  EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
  FinvuCrId: "<EMAIL>"
  IPWhiteListing:
    EnableWhitelist: false
    SoftBlock: false
  OneMoneyCrId: "onemoney-aa"
  TokenIssuer: "epifi-simulator"
  VerifyApiKeyAndJws: false

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CCTransactionNotificationPublisher:
  QueueName: "qa-cc-txn-notification-queue"

CCAcsNotificationPublisher:
  QueueName: "qa-cc-acs-notification-queue"

TssWebhookCallBackPublisher:
  QueueName: "qa-tss-webhook-callback-queue"

CCStatementNotificationPublisher:
  QueueName: "qa-cc-statement-notification-queue"

CardSwitchFinancialNotificationPublisher:
  QueueName: "qa-card-switch-financial-notification-queue"

CardSwitchNonFinancialNotificationPublisher:
  QueueName: "qa-card-switch-non-financial-notification-queue"

AccountStatusCallbackPublisher:
  QueueName: "qa-account-status-callback-queue"

EnachRegistrationAuthorisationCallbackPublisher:
  QueueName: "qa-recurringpayment-creation-auth-vendor-callback-queue"

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: vendor-notification-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

KycStatusUpdatePublisher:
  QueueName: "qa-kyc-v2-update-status-queue"

CcSwitchNotificationsBucketName: "epifi-qa-cc-switch-notifications"
CcRawSwitchNotificationsBucketName: "epifi-raw-dev"
EpanCallbackBucketName: "epifi-qa-pan"
M2pFederalSwitchNotificationFilePath: "m2p/federal/%s/%s-switchTxnNotifications.csv"
RawBucketM2pFederalSwitchNotificationFilePath: "qa/data/vendor/federal_cc_reports/switch_transaction_notifications/%s/%s-switchTxnNotifications.csv"

CredgenicsCallbackStreamProducer:
  EmailStream:
    StreamName: "qa-credgenics-webhook-generic-event-publish-stream"
  SmsStream:
    StreamName: "qa-credgenics-webhook-generic-event-publish-stream"
  WhatsappStream:
    StreamName: "qa-credgenics-webhook-generic-event-publish-stream"
  CallingStream:
    StreamName: "qa-credgenics-webhook-calling-event-publish-stream"
  VoiceMessageStream:
    StreamName: "qa-credgenics-webhook-generic-event-publish-stream"

FederalBankCustKycStateChangePublisher:
  QueueName: "qa-bankcust-kyc-state-change-event-consumer-queue"

FederalResidentialStatusUpdatePublisher:
  QueueName: "qa-bank-customer-residential-status-update-consumer-queue"

FederalMobileNumberUpdatePublisher:
  QueueName: "qa-bank-customer-mobile-number-update-consumer-queue"

PgRazorpayInboundEventPublisher:
  QueueName: "qa-pg-razorpay-inbound-event-queue"

Auth:
  JwtEncryption:
    RSAPrivateKeyPEMPath: "qa/vn/auth/jwt-encryption-private-key"
  ClientCredentials:
    IdToSecretPath: "qa/vn/auth/client-credentials"

Nugget:
  NuggetAccountFreezeDummyDetails:
    U1:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: NO_FREEZE_CODE
      FreezeType: FREEZE_STATUS_UNSPECIFIED
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U2:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: NO_FREEZE_CODE
      FreezeType: ACCOUNT_FREEZE_STATUS_UNFROZEN
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U3:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: '[{"id":"***********","date":"2024-11-01T00:00:00Z","contact":{"grievanceOfficerDetails":{"name":"Sh Anup Kuruvilla John, IPS,ADGP","email":"<EMAIL>","rawData":"Sh Anup Kuruvilla John, IPS,ADGP,0471-2300042,<EMAIL>","phoneNumber":"0471-2300042"},"nodalCyberCellOfficerDetails":{"name":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT)","email":"<EMAIL>","rawData":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT),<EMAIL>"}},"state":"Kerala"}]'
    U4:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U5:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U6:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: '[{"id":"***********","date":"2024-11-01T00:00:00Z","contact":{"grievanceOfficerDetails":{"name":"Sh Anup Kuruvilla John, IPS,ADGP","email":"<EMAIL>","rawData":"Sh Anup Kuruvilla John, IPS,ADGP,0471-2300042,<EMAIL>","phoneNumber":"0471-2300042"},"nodalCyberCellOfficerDetails":{"name":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT)","email":"<EMAIL>","rawData":"Sh Arvind Sukumar, IPS,Superintendent of Police (ICT),<EMAIL>"}},"state":"Kerala"}]'
    U7:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U8:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: LEA_REPORTED_MID_LAYER
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U9:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U10:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U11:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: VKYC_ISSUE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U12:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U13:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U14:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: REKYC_OVERDUE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U15:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U16:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U17:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U18:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U19:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U20:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: UNCATEGORIZED_FREEZE
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U21:
      AccountStatus: OPERATIONAL_STATUS_DORMANT
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U22:
      AccountStatus: OPERATIONAL_STATUS_DORMANT
      ProcessedFreezeReason: LEA_REPORTED
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "-"
      FormStatus: STATUS_UNSPECIFIED
      FormExpiryDate: 28-02-2030
      LeaComplaintDetails: "-"
    U23:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE
      FormId: "FORM123"
      FormStatus: STATUS_CREATED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "-"
    U24:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE
      FormId: "FORM124"
      FormStatus: STATUS_SUBMITTED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "-"
    U25:
      AccountStatus: OPERATIONAL_STATUS_ACTIVE
      ProcessedFreezeReason: SUSPICIOUS_ACTIVITY
      FreezeType: ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE
      FormId: "FORM125"
      FormStatus: STATUS_CANCELLED
      FormExpiryDate: 28-02-2026
      LeaComplaintDetails: "re"
  NuggetTransactionDummyDetails:
    T1:
      CreatedAt: 2025-08-12 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-12 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DEBIT_CARD_CHARGES
      TransactionAmount: 500.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 12 August 2025 at 3:04 PM
    T2:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ECS_ENACH_CHARGES
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T3:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: AMB_CHARGE
      TransactionAmount: 100.46
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T4:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ANYWHERE_BANKING_CHARGE
      TransactionAmount: 4.37
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T5:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_DCC_FEE_CHARGE
      TransactionAmount: ********.25
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T6:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DEBIT_CARD_AMC_CHARGE
      TransactionAmount: 90.67
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T7:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_FOREX_MARKUP_CHARGE
      TransactionAmount: 111.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T8:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DC_TCS_FEE_CHARGE
      TransactionAmount: 123.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T9:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: FIRST_CARD_ORDER_FEE
      TransactionAmount: 1234.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T10:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: CHEQUE_BOOK_CHARGES
      TransactionAmount: 12345.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T11:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ECOM_POS_DECLINE_CHARGE
      TransactionAmount: 98.24
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T12:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ATM_DECLINE_CHARGE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T13:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: OTHER_BANK_ATM_CHARGE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T14:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: DUPLICATE_CARD_FEE
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T15:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: VENDOR_BANK
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T16:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: EXTERNAL
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 1.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T17:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 2.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T18:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: ""
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: ATM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 3.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T19:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 4.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T20:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: ECOMM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 5.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T21:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: RTGS
      Tags: ""
      TransactionAmount: 6.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T22:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: INTRA_BANK
      Tags: ""
      TransactionAmount: 7.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T23:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 10.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T24:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 200.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T25:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: NEFT
      Tags: ""
      TransactionAmount: 20000.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T26:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: SUCCESS
      ExecutedAt: 2025-08-11 15:04:05
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: IMPS
      Tags: ""
      TransactionAmount: 100000.00
      TransactionStatus: SUCCESS
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T27:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_POS_26
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T28:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_ATM_7
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: ATM
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T29:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI1215
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: EXTERNAL
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T30:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: FI_POS_6
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: POS
      PaymentProtocol: CARD
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T31:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI130
      ExecutedAt: ""
      P2P_P2M: P2M
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T32:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI129
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
    T33:
      CreatedAt: 2025-08-11 15:04:05
      ErrorCode: UPI1095
      ExecutedAt: ""
      P2P_P2M: P2P
      Provenance: USER_APP
      PaymentProtocol: UPI
      Tags: ""
      TransactionAmount: 100.00
      TransactionStatus: FAILED
      CreatedAtReadableTime: 11 August 2025 at 3:04 PM
