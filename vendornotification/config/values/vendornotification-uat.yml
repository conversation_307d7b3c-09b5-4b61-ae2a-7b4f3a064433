Application:
  Environment: "uat"
  Name: "vendornotification"

KarzaEPANWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************"
  SoftBlock: true

KarzaVkycWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,*************,**************,**************"
  SoftBlock: true

AclSmsWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**************"
  SoftBlock: true

KaleyraSmsWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: ""
  SoftBlock: true

AclWhatsappWhitelist:
  EnableWhitelist: false
  WhitelistedIPs: "**************"
  SoftBlock: true

SavenWhiteList:
  EnableWhitelist: true
  WhitelistedIPs: "************,**************,************,***********,************"
  SoftBlock: false

GupshupWhitelist:
  EnableWhitelist: false

NetCoreWhitelist:
  EnableWhitelist: false

DPandaWhitelist:
  EnableWhitelist: false

PaisabazaarWhitelist:
  EnableWhitelist: false

SetuWhiteList:
  EnableWhitelist: true
  WhitelistedIPs: "**********"
  SoftBlock: false

PoshVineWhitelist:
  EnableWhitelist: false

OzonetelWhitelist:
  EnableWhitelist: false

TssWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "***********"
  SoftBlock: false

M2PWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "*************,************,************"
  SoftBlock: true

FiftyfinWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: ""
  SoftBlock: true

AbflWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "************,************,************,*************"
  SoftBlock: true

MoneyviewWhiteList:
  EnableWhitelist: true
  WhitelistedIPs: "***********,*************"
  SoftBlock: true

CredgenicsWhitelist:
  EnableWhitelist: true
  WhitelistedIPs: "**********,**************,***********"
  SoftBlock: true

LeadsWhiteList:
  EnableWhitelist: true
  # Epifi Route all VPN IP: **************
  # financebuddha IPs: *************,*************
  # loantap IPs: *************
  # mymoneymantra IPs: **************,**************,**************
  # cashkuber IPs: **********,***********
  # Keshva Credit IPs: ************
  # cpadvisor IPs: **************
  # mymoneymantra b2c IPs:  **************,**************,**************
  # gocredit IPS: ************,*************
  # credit haat IPs: *************,************
  # fintifi IPs: *************
  # switchmyloan IPs: ***********
  WhitelistedIPs: "**************,*************,*************,*************,**************,**************,**************,**********,***********,************,**************,************,*************,*************,************,*************,***********"
  SoftBlock: false

AuthWhiteList:
  EnableWhitelist: true
  # Epifi Route all VPN IP: **************
  # financebuddha IPs: *************,*************
  # loantap IPs: *************
  # mymoneymantra IPs: **************,**************,**************
  # cashkuber IPs: **********,***********
  # Keshva IPs: ************
  # cpadvisor IPs: **************
  # mymoneymantra b2c IPs: **************,**************,**************
  # gocredit IPS: ************,*************
  # credit haat IPs: *************,************
  # fintifi IPs: *************
  # switchmyloan IPs: ***********
  WhitelistedIPs: "**************,*************,*************,*************,**************,**************,**************,**********,***********,************,**************,************,*************,*************,************,*************,***********"
  SoftBlock: false

FederalWhitelist:
  EnableWhitelist: false

VpcCidrIPPrefix: "10.7"

UpdateTransactionEventsPublisher:
  QueueName: "uat-payment-callback-update-queue"

InboundTxnPublisher:
  QueueName: "uat-inbound-txn-queue"

InboundUpiTxnPublisher:
  QueueName: "uat-inbound-upi-txn-queue"

InboundLoanTxnPublisher:
  QueueName: "uat-loan-inbound-transaction-queue"

CreateCardCallbackPublisher:
  QueueName: "uat-card-creation-callback-queue"

DispatchPhysicalCardCallbackPublisher:
  QueueName: "uat-card-dispatch-request-callback-queue"

CheckLivenessCallbackPublisher:
  QueueName: "uat-check-liveness-callback-queue"

UPIReqAuthEventPublisher:
  QueueName: "uat-upi-req-auth-processing-queue"

UPIReqAuthMandateEventPublisher:
  QueueName: "uat-upi-req-auth-mandate-processing-queue"

UPIReqAuthValCustEventPublisher:
  QueueName: "uat-upi-req-auth-val-cust-processing-queue"

UPIReqMandateConfirmationEventPublisher:
  QueueName: "uat-upi-req-mandate-confirmation-processing-queue"

UPIRespMandateEventPublisher:
  QueueName: "uat-upi-resp-mandate-processing-queue"

UPIRespPayEventPublisher:
  QueueName: "uat-upi-resp-pay-processing-queue"

UPIReqTxnConfirmationEventPublisher:
  QueueName: "uat-upi-req-txn-confirmation-processing-queue"

UPIReqValAddressEventPublisher:
  QueueName: "uat-upi-req-val-address-processing-queue"

UPIListPspKeysEventPublisher:
  QueueName: "uat-list-psp-keys-processing-queue"

UPIListVaePublisher:
  QueueName: "uat-list-vae-processing-queue"
  BucketName: "epifi-dev-extended-sqs"

CreateDepositCallbackPublisher:
  QueueName: "uat-create-deposit-callback-queue"

PreCloseDepositCallbackPublisher:
  QueueName: "uat-preclose-deposit-callback-queue"

FdAutoRenewCallbackPublisher:
  QueueName: "uat-deposit-maturity-action-callback-queue"

AclSmsCallbackPublisher:
  QueueName: "uat-vn-acl-sms-callback-queue"

KaleyraSmsCallbackPublisher:
  QueueName: "uat-vn-kaleyra-sms-callback-queue"

AclWhatsappCallbackPublisher:
  QueueName: "uat-vn-acl-whatsapp-callback-queue"

AclWhatsappReplyPublisher:
  QueueName: "uat-vn-acl-whatsapp-reply-queue"

GupshupWhatsappCallbackPublisher:
  QueueName: "uat-comms-gupshup-whatsapp-callback-queue"

GupshupRcsCallbackPublisher:
  QueueName: "uat-comms-gupshup-rcs-callback-queue"

NetCoreSmsCallbackPublisher:
  QueueName: "uat-comms-netcore-sms-callback-queue"

AirtelSmsCallbackPublisher:
  QueueName: "uat-comms-airtel-sms-callback-queue"

AirtelWhatsappCallbackPublisher:
  QueueName: "uat-comms-airtel-whatsapp-callback-queue"

DeviceReRegCallbackPublisher:
  QueueName: "uat-device-rereg-callback-queue"

DeviceRegSMSAckPublisher:
  QueueName: "uat-device-reg-sms-ack-queue"

CustomerCreationCallbackPublisher:
  QueueName: "uat-customer-creation-callback-queue"

BankCustCallbackPublisher:
  QueueName: "uat-bankcust-customer-creation-callback-queue"

AccountCreationCallbackPublisher:
  QueueName: "uat-savings-creation-callback-queue"

FederalVkycUpdatePublisher:
  QueueName: "uat-vn-federal-vkyc-update-queue"

UpdateShippingAddressCallbackPublisher:
  QueueName: "uat-shipping-address-update-callback-queue"

KarzaVkycAgentResponsePublisher:
  QueueName: "uat-vn-karza-vkyc-agent-response-queue"

KarzaVkycAuditorResponsePublisher:
  QueueName: "uat-vn-karza-vkyc-auditor-response-queue"

KarzaVkycCallEventPublisher:
  QueueName: "uat-vn-karza-vkyc-call-event-queue"

EmailCallbackPublisher:
  QueueName: "uat-vn-email-callback-queue"

ConsentCallbackPublisher:
  QueueName: "uat-vn-aa-consent-callback-queue"

FICallbackPublisher:
  QueueName: "uat-vn-aa-fi-callback-queue"

CardTrackingCallbackPublisher:
  QueueName: "uat-card-tracking-callback-queue"

AccountLinkStatusCallbackPublisher:
  QueueName: "uat-vn-aa-account-link-status-callback-queue"

UPIReqTxnConfirmationComplaintEventPublisher:
  QueueName: "uat-upi-req-txn-confirmation-complaint-processing-queue"

OzonetelCallDetailsPublisher:
  QueueName: "uat-vn-ozonetel-call-details-queue"

UPIRespComplaintEventPublisher:
  QueueName: "uat-upi-resp-complaint-queue"

FreshchatActionCallbackPublisher:
  QueueName: "uat-vn-freshchat-action-callback-queue"

NuggetEventCallbackPublisher:
  QueueName: "uat-nugget-event-callback-queue"

TssWebhookCallBackPublisher:
  QueueName: "uat-tss-webhook-callback-queue"

HealthInsurancePolicyIssuanceEventPublisher:
  QueueName: "uat-salaryprogram-healthinsurance-policy-issuance-completion-queue"

CCNonFinancialNotificationPublisher:
  QueueName: "uat-cc-non-financial-notification-queue"

SmallcaseProcessMFHoldingsWebhookPublisher:
  QueueName: "uat-vn-process-mf-holdings-webhook-extended-queue"
  BucketName: "epifi-wealth-staging-extended-sqs"

SignalWorkflowPublisher:
  QueueName: "uat-celestial-signal-workflow-queue"

UPIReqMapperConfirmationEventPublisher:
  QueueName: "uat-upi-req-mapper-confirmation-processing-queue"

LoansFiftyfinCallbackPublisher:
  QueueName: "uat-vn-loans-fiftyfin-callback-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "uat-celestial-initiate-procrastinator-workflow-queue"

FederalEscalationUpdateEventPublisher:
  QueueName : "uat-cx-escalation-update-queue"

CcOnboardingStateUpdateEventPublisher:
  QueueName: "uat-cc-onboarding-state-update-event-callback-queue"

VendorRewardFulfillmentPublisher:
  QueueName: "uat-vendor-reward-fulfillment-event-queue"

Server:
  Ports:
    GrpcPort: 8088
    GrpcSecurePort: 9524
    HttpPort: 9999
    HttpPProfPort: 9990

Aws:
  Region: "ap-south-1"

#json file path
PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"
CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"

SyncRespHandler:
  SyncPublisher:
    Publisher:
      QueueName: "uat-vn-sync-wrapper-queue"


Flags:
  TrimDebugMessageFromStatus: false

Secrets:
  Ids:
    #Federal
    SenderCode: "uat/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "uat/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "uat/vg-vn-vgpci/federal-auth-service-access-code"
    SenderCodeLoans: "uat/vg-vn-vgpci/federal-auth-service-access-code"
    AaVgVnSecretsV1: "uat/vg-vn/aa-secrets-v1"
    SahamatiPublicKeyJwk: "uat/vn/sahamati-public-key"
    SenseforthAuthApiKey: "uat/vn/senseforth-auth-api-key"
    SprinklrAuthApiKey: "uat/vg-vn/sprinklr-auth-api-key"
    # external vendor for offers redemption
    DpandaAuthApiKey: "uat/vn/dpanda-auth-api-key"
    PoshvineAuthApiKey: "uat/vn/poshvine-auth-api-key"
    RazorpayAuthApiKey: "uat/vn/razorpay-auth-api-key"
    DpandaVnSecrets: "uat/vn/dpanda-secrets"
    PoshvineVnSecrets: "uat/vn/poshvine-secrets"
    RazorpayVnSecrets: "uat/vn/razorpay-secrets"

SavenRewardVnSecrets:
  Path: "uat/vn/saven-rewards-secrets"

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendornotification/secure.log"
  MaxSizeInMBs: 50
  MaxBackups: 20

FeatureFlags:
  AllowCustomerCallbackProcessing: true
  AllowAccountCallbackProcessing: true

AA:
  AaSecretsVersionToUse: "V1"
  EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
  FinvuCrId: "<EMAIL>"
  IPWhiteListing:
    EnableWhitelist: false
    SoftBlock: false
  OneMoneyCrId: "onemoney-aa"
  TokenIssuer: "https://uattokens.sahamati.org.in/auth/realms/sahamati"
  VerifyApiKeyAndJws: true

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CCTransactionNotificationPublisher:
  QueueName: "uat-cc-txn-notification-queue"

CCAcsNotificationPublisher:
  QueueName: "uat-cc-acs-notification-queue"

CCStatementNotificationPublisher:
  QueueName: "uat-cc-statement-notification-queue"

CardSwitchFinancialNotificationPublisher:
  QueueName: "uat-card-switch-financial-notification-queue"

CardSwitchNonFinancialNotificationPublisher:
  QueueName: "uat-card-switch-non-financial-notification-queue"

AccountStatusCallbackPublisher:
  QueueName: "uat-account-status-callback-queue"

EnachRegistrationAuthorisationCallbackPublisher:
  QueueName: "uat-recurringpayment-creation-auth-vendor-callback-queue"

KycStatusUpdatePublisher:
  QueueName: "uat-kyc-v2-update-status-queue"

QuestSdk:
  Disable: true

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: vendor-notification-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

CcSwitchNotificationsBucketName: "epifi-uat-cc-switch-notifications"
CcRawSwitchNotificationsBucketName: "epifi-raw-dev"
EpanCallbackBucketName: "epifi-uat-pan"
M2pFederalSwitchNotificationFilePath: "m2p/federal/%s/%s-switchTxnNotifications.csv"
RawBucketM2pFederalSwitchNotificationFilePath: "uat/data/vendor/federal_cc_reports/switch_transaction_notifications/%s/%s-switchTxnNotifications.csv"

CredgenicsCallbackStreamProducer:
  EmailStream:
    StreamName: "uat-credgenics-webhook-generic-event-publish-stream"
  SmsStream:
    StreamName: "uat-credgenics-webhook-generic-event-publish-stream"
  WhatsappStream:
    StreamName: "uat-credgenics-webhook-generic-event-publish-stream"
  CallingStream:
    StreamName: "uat-credgenics-webhook-calling-event-publish-stream"
  VoiceMessageStream:
    StreamName: "uat-credgenics-webhook-generic-event-publish-stream"

FederalBankCustKycStateChangePublisher:
  QueueName: "uat-bankcust-kyc-state-change-event-consumer-queue"

FederalResidentialStatusUpdatePublisher:
  QueueName: "uat-bank-customer-residential-status-update-consumer-queue"

FederalMobileNumberUpdatePublisher:
  QueueName: "uat-bank-customer-mobile-number-update-consumer-queue"

PgRazorpayInboundEventPublisher:
  QueueName: "uat-pg-razorpay-inbound-event-queue"

Auth:
  JwtEncryption:
    RSAPrivateKeyPEMPath: "uat/vn/auth/jwt-encryption-private-key"
  ClientCredentials:
    IdToSecretPath: "uat/vn/auth/client-credentials"
