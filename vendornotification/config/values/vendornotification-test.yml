Application:
  Environment: "test"
  Name: "vendornotification"

KarzaEPANWhitelist:
  EnableWhitelist: false

Karza<PERSON>kyc<PERSON>hitelist:
  EnableWhitelist: false

AclSmsWhitelist:
  EnableWhitelist: false

KaleyraSmsWhitelist:
  EnableWhitelist: false

AclWhatsappWhitelist:
  EnableWhitelist: false

GupshupWhitelist:
  EnableWhitelist: false

NetCoreWhitelist:
  EnableWhitelist: false

PaisabazaarWhitelist:
  EnableWhitelist: false

SetuWhiteList:
  EnableWhitelist: false

DPandaWhitelist:
  EnableWhitelist: false

PoshVineWhitelist:
  EnableWhitelist: false

OzonetelWhitelist:
  EnableWhitelist: false

M2PWhitelist:
  EnableWhitelist: false

FederalWhitelist:
  EnableWhitelist: false

UpdateTransactionEventsPublisher:
  QueueName: "payment-callback-update-queue"

InboundTxnPublisher:
  QueueName: "inbound-txn-queue"

InboundUpiTxnPublisher:
  QueueName: "inbound-upi-txn-queue"

InboundLoanTxnPublisher:
  QueueName: "loan-inbound-transaction-queue"

CreateCardCallbackPublisher:
  QueueName: "card-creation-callback-queue"

DispatchPhysicalCardCallbackPublisher:
  QueueName: "card-dispatch-request-callback-queue"

CheckLivenessCallbackPublisher:
  QueueName: "check-liveness-callback-queue"

UPIReqAuthEventPublisher:
  QueueName: "upi-req-auth-processing-queue"

UPIReqAuthMandateEventPublisher:
  QueueName: "upi-req-auth-mandate-processing-queue"

UPIReqAuthValCustEventPublisher:
  QueueName: "upi-req-auth-val-cust-processing-queue"

UPIReqMandateConfirmationEventPublisher:
  QueueName: "upi-req-mandate-confirmation-processing-queue"

UPIRespMandateEventPublisher:
  QueueName: "upi-resp-mandate-processing-queue"

UPIRespPayEventPublisher:
  QueueName: "upi-resp-pay-processing-queue"

UPIReqTxnConfirmationEventPublisher:
  QueueName: "upi-req-txn-confirmation-processing-queue"

UPIReqValAddressEventPublisher:
  QueueName: "upi-req-val-address-processing-queue"

UPIListPspKeysEventPublisher:
  QueueName: "list-psp-keys-processing-queue"

UPIListVaePublisher:
  QueueName: "list-vae-processing-queue"
  BucketName: "epifi-dev-extended-sqs"

CreateDepositCallbackPublisher:
  QueueName: "create-deposit-callback-queue"

PreCloseDepositCallbackPublisher:
  QueueName: "preclose-deposit-callback-queue"

FdAutoRenewCallbackPublisher:
  QueueName: "deposit-maturity-action-callback-queue"

AclSmsCallbackPublisher:
  QueueName: "vn-acl-sms-callback-queue"

KaleyraSmsCallbackPublisher:
  QueueName: "vn-kaleyra-sms-callback-queue"

AclWhatsappCallbackPublisher:
  QueueName: "vn-acl-whatsapp-callback-queue"

AclWhatsappReplyPublisher:
  QueueName: "vn-acl-whatsapp-reply-queue"

GupshupWhatsappCallbackPublisher:
  QueueName: "comms-gupshup-whatsapp-callback-queue"

GupshupRcsCallbackPublisher:
  QueueName: "comms-gupshup-rcs-callback-queue"

NetCoreSmsCallbackPublisher:
  QueueName: "comms-netcore-sms-callback-queue"

AirtelSmsCallbackPublisher:
  QueueName: "comms-airtel-sms-callback-queue"

AirtelWhatsappCallbackPublisher:
  QueueName: "comms-airtel-whatsapp-callback-queue"

DeviceReRegCallbackPublisher:
  QueueName: "device-rereg-callback-queue"

DeviceRegSMSAckPublisher:
  QueueName: "device-reg-sms-ack-queue"

CustomerCreationCallbackPublisher:
  QueueName: "customer-creation-callback-queue"

BankCustCallbackPublisher:
  QueueName: "bankcust-customer-creation-callback-queue"

AccountCreationCallbackPublisher:
  QueueName: "savings-creation-callback-queue"

FederalVkycUpdatePublisher:
  QueueName: "vn-federal-vkyc-update-queue"

UpdateShippingAddressCallbackPublisher:
  QueueName: "shipping-address-update-callback-queue"

KarzaVkycAgentResponsePublisher:
  QueueName: "vn-karza-vkyc-agent-response-queue"

KarzaVkycAuditorResponsePublisher:
  QueueName: "vn-karza-vkyc-auditor-response-queue"

KarzaVkycCallEventPublisher:
  QueueName: "vn-karza-vkyc-call-event-queue"

EmailCallbackPublisher:
  QueueName: "vn-email-callback-queue"

ConsentCallbackPublisher:
  QueueName: "vn-aa-consent-callback-queue"

FICallbackPublisher:
  QueueName: "vn-aa-fi-callback-queue"

CardTrackingCallbackPublisher:
  QueueName: "card-tracking-callback-queue"

AccountLinkStatusCallbackPublisher:
  QueueName: "vn-aa-account-link-status-callback-queue"

UPIReqTxnConfirmationComplaintEventPublisher:
  QueueName: "upi-req-txn-confirmation-complaint-processing-queue"

OzonetelCallDetailsPublisher:
  QueueName: "vn-ozonetel-call-details-queue"

UPIRespComplaintEventPublisher:
  QueueName: "upi-resp-complaint-queue"

FreshchatActionCallbackPublisher:
  QueueName: "vn-freshchat-action-callback-queue"

NuggetEventCallbackPublisher:
  QueueName: "nugget-event-callback-queue"

TssWebhookCallBackPublisher:
  QueueName: "tss-webhook-callback-queue"

HealthInsurancePolicyIssuanceEventPublisher:
  QueueName: "salaryprogram-healthinsurance-policy-issuance-completion-queue"

CCNonFinancialNotificationPublisher:
  QueueName: "cc-non-financial-notification-queue"

SmallcaseProcessMFHoldingsWebhookPublisher:
  QueueName: "vn-process-mf-holdings-webhook-extended-queue"
  BucketName: "epifi-wealth-dev-extended-sqs"

SignalWorkflowPublisher:
  QueueName: "celestial-signal-workflow-queue"

UPIReqMapperConfirmationEventPublisher:
  QueueName: "upi-req-mapper-confirmation-processing-queue"

LoansFiftyfinCallbackPublisher:
  QueueName: "vn-loans-fiftyfin-callback-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "celestial-initiate-procrastinator-workflow-queue"

FederalEscalationUpdateEventPublisher:
  QueueName : "cx-escalation-update-queue"

CcOnboardingStateUpdateEventPublisher:
  QueueName: "cc-onboarding-state-update-event-callback-queue"

VendorRewardFulfillmentPublisher:
  QueueName: "vendor-reward-fulfillment-event-queue"

Server:
  Ports:
    GrpcPort: 8088
    GrpcSecurePort: 9524
    HttpPort: 9893

Aws:
  Region: "ap-south-1"

#json file path
PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"
CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"

SyncRespHandler:
  SyncPublisher:
    Publisher:
      QueueName: "vn-sync-wrapper-queue"


Flags:
  TrimDebugMessageFromStatus: false

Secrets:
  Ids:
    #Federal
    SenderCode: "EpiFi_Test_Cd"
    ServiceAccessId: "EpiFi_Fed_Test"
    ServiceAccessCode: "EFed@123"
    SenderCodeLoans: "EpiFi_Test_Cd"
    #AA
    EpifiAaPrivateKey: |
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
    SahamatiPublicKeyJwk: '{"keys":[{"kid":"mrIv4jl1Zk36xpEHNdIPPldz4XUKipQOuapGNCSQwZM","kty":"RSA","alg":"RS256","use":"sig","n":"yMzHt4zMlplvP6XvbxfE7TivX0T2w1yN7uWPX_428jlr51PqJIuv2Mv1q3QAnty9hBlz-VLw5prXvl1xUlr9BYqjBQR7ijMBRCJcXZK26u4auzyFA5YAZQ9sGGUxJrtYwHsFIXN58g6tZbQPYmQWXtaoGWTC-UUEXPb5BngiKyAcUpgmyfUM1fseQjz7V6v_LlhdWOPtEPBzx7s-CpvjoiuqEVTk6RcBd8-PTGsbuHL7r3fDb1haWdc7RJ0LbnP0rmA301wEWDdQTV_QVUN2eY8eNIRBbGmYXOzqjI5DCxAm-gL3ztDtKNQW9sLiI14YEIb4aonV36rhj8xtSDCU5Q","e":"AQAB","x5c":["MIICnzCCAYcCBgF19FMx/TANBgkqhkiG9w0BAQsFADATMREwDwYDVQQDDAhzYWhhbWF0aTAeFw0yMDExMjMwODU2MzRaFw0zMDExMjMwODU4MTRaMBMxETAPBgNVBAMMCHNhaGFtYXRpMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyMzHt4zMlplvP6XvbxfE7TivX0T2w1yN7uWPX/428jlr51PqJIuv2Mv1q3QAnty9hBlz+VLw5prXvl1xUlr9BYqjBQR7ijMBRCJcXZK26u4auzyFA5YAZQ9sGGUxJrtYwHsFIXN58g6tZbQPYmQWXtaoGWTC+UUEXPb5BngiKyAcUpgmyfUM1fseQjz7V6v/LlhdWOPtEPBzx7s+CpvjoiuqEVTk6RcBd8+PTGsbuHL7r3fDb1haWdc7RJ0LbnP0rmA301wEWDdQTV/QVUN2eY8eNIRBbGmYXOzqjI5DCxAm+gL3ztDtKNQW9sLiI14YEIb4aonV36rhj8xtSDCU5QIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCkXAAe+o6DWuO9KB8s6P5A0W6w9IpcelyuhCID2lW6TD/M4sBfM2oppGLzc4ifc2COamQHwAhKHa3FcYdk1zzIL/opbVH2ppHYftgoFuNt+Q07iu8IzWjGi9LR1PaCyMJswzFN86PU0R27qG18ZL2ayxLoPcq4ouZRTC7zgRe6VUHdFvjPJV5MqF4vlkv2eWj1RltTDissCc/mSaIjsWShWSPou7Xs6pPhS2GRi78qaPOsR6phgqSdls5eb/315wDuwbpGt30LCd+x8pqxmpw8NFqcaLCLVNaxrpHay7IVjd1LeDwZCSrosDBTO0RglrUcVHn7ydKnQL1QZ5y6/BhC"],"x5t":"YyGyTXjLrfXp0R2lwCInPGAduUw","x5t#S256":"nzzJSI2oZQ0XdV5tV54gyeCp_5ebEpCnSwADkc6yB7Q"}]}'
    SenseforthAuthApiKey: "XYZ"
    SprinklrAuthApiKey: "U3ByaW5rbHJOb25Qcm9kOm5vblByb2QjV2ViaG9va0BFdmVudA=="
    # external vendor for offers redemption
    DpandaAuthApiKey: "5003f49ff7f7d5732f4cd6e9516c65b439482d477c939b47f6e8c6a6bb0009d8"
    PoshvineAuthApiKey: "5003f49ff7f7d5732f4cd6e9516c65b439482d477c939b47f6e8c6a6bb0009d8"
    RazorpayAuthApiKey: "5003f49ff7f7d5732f4cd6e9516c65b439482d477c939b47f6e8c6a6bb0009d8"

SavenRewardVnSecrets:
  JwtVerificationKeyBase64: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendornotification/secure.log"
  MaxSizeInMBs: 50
  MaxBackups: 20

FeatureFlags:
  AllowCustomerCallbackProcessing: true
  AllowAccountCallbackProcessing: true

AA:
  TokenIssuer: "https://uattokens.sahamati.org.in/auth/realms/sahamati"
  VerifyApiKeyAndJws: false
  IPWhiteListing:
    EnableWhitelist: false
    SoftBlock: false
  OneMoneyCrId: "onemoney-aa"
  FinvuCrId: "<EMAIL>"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CCTransactionNotificationPublisher:
  QueueName: "cc-txn-notification-queue"

CCAcsNotificationPublisher:
  QueueName: "cc-acs-notification-queue"

CCStatementNotificationPublisher:
  QueueName: "cc-statement-notification-queue"

CardSwitchFinancialNotificationPublisher:
  QueueName: "card-switch-financial-notification-queue"

CardSwitchNonFinancialNotificationPublisher:
  QueueName: "card-switch-non-financial-notification-queue"

AccountStatusCallbackPublisher:
  QueueName: "account-status-callback-queue"

EnachRegistrationAuthorisationCallbackPublisher:
  QueueName: "recurringpayment-creation-auth-vendor-callback-queue"

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: vendor-notification-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

KycStatusUpdatePublisher:
  QueueName: "kyc-v2-update-status-queue"

CcSwitchNotificationsBucketName: "epifi-cc-switch-notifications"
CcRawSwitchNotificationsBucketName: "epifi-raw-dev"
EpanCallbackBucketName: "epifi-pan"
M2pFederalSwitchNotificationFilePath: "m2p/federal/%s/%s-switchTxnNotifications.csv"
RawBucketM2pFederalSwitchNotificationFilePath: "test/data/vendor/federal_cc_reports/switch_transaction_notifications/%s/%s-switchTxnNotifications.csv"

DpandaVnSecrets:
  SecretKey: "a24d9afe36ede0ec5fee5cb95f24f261"
  Iv: "d0eb1726bd9acd30"
  Mode: "AES-256-CBC"
PoshvineVnSecrets:
  SecretKey: "a24d9afe36ede0ec5fee5cb95f24f261"
  Iv: "d0eb1726bd9acd30"
  Mode: "AES-256-CBC"
RazorpayVnSecrets:
  SecretKey: "a24d9afe36ede0ec5fee5cb95f24f261"
  Iv: "d0eb1726bd9acd30"
  Mode: "AES-256-CBC"

CredgenicsCallbackStreamProducer:
  EmailStream:
    StreamName: "credgenics-webhook-generic-event-publish-stream"
  SmsStream:
    StreamName: "credgenics-webhook-generic-event-publish-stream"
  WhatsappStream:
    StreamName: "credgenics-webhook-generic-event-publish-stream"
  CallingStream:
    StreamName: "credgenics-webhook-calling-event-publish-stream"
  VoiceMessageStream:
    StreamName: "credgenics-webhook-generic-event-publish-stream"

FederalBankCustKycStateChangePublisher:
  QueueName: "bankcust-kyc-state-change-event-consumer-queue"

FederalResidentialStatusUpdatePublisher:
  QueueName: "bank-customer-residential-status-update-consumer-queue"

FederalMobileNumberUpdatePublisher:
  QueueName: "bank-customer-mobile-number-update-consumer-queue"

PgRazorpayInboundEventPublisher:
  QueueName: "pg-razorpay-inbound-event-queue"

Auth:
  JwtEncryption:
    RSAPrivateKeyPEM: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  ClientCredentials:
    IdToSecretString: "{\"loans-dsa-1\":\"loans-dsa-1-secret\"}"
