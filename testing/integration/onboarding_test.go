package integration

import (
	"context"
	"io/ioutil"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/owners"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/frontend/card"
	"github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/scripts/crud/userdata"
	"github.com/epifi/gamma/scripts/crud/waitlist"
	"github.com/epifi/gamma/testing/integration/app"
)

// TestOnboarding_E2E tests onboarding end to end i.e. till the home page.
// It tests next action at every stage.
func Onboarding_E2E(t *testing.T) {
	defer Recover(t)
	a := require.New(t)
	ctx := context.Background()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require.New(t), dbConns)
	defer closeConnFunc()

	data := createCleanUserParams()

	livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
	v, err := ioutil.ReadFile(livenessVideoFilePath)
	a.NoError(err, "Failed to read video file")
	data.Video = v

	authHeader, _ := app.DoEKYCAndLiveness(ctx, dep, data)
	app.GetKYCRecord(ctx, a, feSignupClient, authHeader)

	// User manually adds a shipping address
	app.AddShippingAddress(ctx, a, feUserClient, authHeader)

	legalNameRes := app.GetLegalName(ctx, a, feUserClient, authHeader)
	// confirm card mailing address
	cardPreferenceRes := app.ConfirmCardPreference(ctx, a, feUserClient, authHeader, types.AddressType_PERMANENT, legalNameRes.GetLegalName())
	a.Equal(deeplink.Screen_CREATE_ACCOUNT, cardPreferenceRes.GetNextAction().GetScreen())

	// register device
	app.RegisterDevice(ctx, a, feSignupClient, authHeader, false, data.SimSubIds[0], dep, data)
	nextAction := app.NextAction(ctx, a, feSignupClient, authHeader)
	app.ScreenEquals(a, deeplink.Screen_SAVINGS_ACCOUNT_SETUP_PROGRESS, nextAction.GetScreen())
	intermediateStep := nextAction.ScreenOptions.(*deeplink.Deeplink_SavingsAccountSetupProgress).SavingsAccountSetupProgress.Step
	a.Equal(deeplink.SavingsAccountSetupProgress_ACCOUNT_SETUP, intermediateStep)

	// account setup
	acctRes := app.CreateAccountSignup(ctx, a, feSignupClient, authHeader, dep, data)
	app.ScreenEquals(a, deeplink.Screen_ONBOARDING_SET_DEBIT_CARD_PIN, acctRes.GetNextAction().GetScreen())
	nextAction = app.NextAction(ctx, a, feSignupClient, authHeader)
	app.ScreenEquals(a, deeplink.Screen_ONBOARDING_SET_DEBIT_CARD_PIN, nextAction.GetScreen())

	// debit card setup
	cardPinRes := app.SetCardPin(ctx, a, feCardClient, authHeader, acctRes.GetCardDetails().GetCardId(), "", card.UIEntryPoint_ONBOARDING)
	app.ScreenEquals(a, deeplink.Screen_DEBIT_CARD_PIN_SETUP_STATUS, cardPinRes.GetNextAction().GetScreen())

	// debit pin card setup status
	pinSetupStatusResp := app.CardPinSetUpStatus(ctx, a, feCardClient, authHeader, acctRes.GetCardDetails().GetCardId())
	app.ScreenEquals(a, deeplink.Screen_INFO_ACKNOWLEDGEMENT_SCREEN, pinSetupStatusResp.GetNextAction().GetScreen())
}

// createCleanUserParams returns UserData where the user is guaranteed to not be present in the database.
func createCleanUserParams(userDataPref ...app.OverrideUserParams) *app.UserData {
	data := app.CreateUserParams(userDataPref...)
	if err := userdata.DeleteWealthData(data.Phone, epifiDbV2, simulatorDb, epifiWealthDbV2, nil, actorPgdb); err != nil {
		if storage.IsRecordNotFoundError(err) {
			logger.InfoNoCtx("no wealth data found")
		} else {
			logger.ErrorNoCtx("error deleting wealth data in create clean user params", zap.Error(err))
		}
	}
	// delete data for the user with this phone number
	// if the user doesn't exist it'll log an error which can be ignored
	userdata.DeleteUserData(epifiDbV2, actorPgdb, timelineDb, paymentInstrumentDb, simulatorDb, data.Phone, userRedisCacheStorage, actorRedisCacheStorage, piRedisCacheStorage, savingsLedgerReconCacheStorage, timelineRedisCacheStorage, savingsRedisCacheStorage, devRegRedisCacheStorage, authClient)
	// delete data for the waitlist user with this phone number
	// if the user doesn't exist it'll log an error which can be ignored.
	ctx := context.Background()
	if err := waitlist.DeleteUser(ctx, epifiDbV2, data.Phone); err != nil {
		logger.Error(ctx, "Error to delete waitlist user data", zap.Error(err))
	}
	return data
}

func Onboarding_ReLogin(t *testing.T) {
	defer Recover(t)
	ctx := context.Background()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require.New(t), dbConns)
	defer closeConnFunc()

	user1 := createCleanUserParams()
	livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
	v, err := ioutil.ReadFile(livenessVideoFilePath)
	dep.Assert.NoError(err, "Failed to read video file")
	user1.Video = v

	// onboard a new user
	authH := app.CreateSavingsAccountForUser(ctx, dep, user1)

	// sign out
	app.SignOut(ctx, dep, authH)

	// login in again
	authH, _ = app.UserWithAccessToken(ctx, dep, user1)
	res := app.NextAction(ctx, dep.Assert, dep.SignupClient, authH)
	app.ScreenEquals(dep.Assert, deeplink.Screen_INFO_ACKNOWLEDGEMENT_SCREEN, res.GetScreen())
}

func OnboardingTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is the test for onboarding E2E",
				BU:          owners.BUSINESS_UNIT_GROWTH_AND_EXPERIENCE,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: Onboarding_E2E,
		},
		{
			TestProperty: &TestProperty{
				Description: "This is the test for onboarding re login",
				BU:          owners.BUSINESS_UNIT_GROWTH_AND_EXPERIENCE,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: Onboarding_ReLogin,
		},
	}
}
