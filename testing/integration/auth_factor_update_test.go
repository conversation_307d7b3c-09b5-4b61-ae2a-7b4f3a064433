package integration

import (
	"context"
	"io/ioutil"
	"path/filepath"
	"testing"

	"github.com/epifi/gamma/testing/integration/app"

	"github.com/epifi/be-common/pkg/owners"

	"github.com/stretchr/testify/require"
)



func Onboarding_AuthFactorUpdate_OnlyPhoneNumber(t *testing.T) {
	defer Recover(t)
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=48016
	// t.Skip("skipping only phone number as it does liveness in manual review in the current prod and test config uses ATM PIN validation")
	ctx := context.Background()
	assert := require.New(t)

	dep, closeConnFunc := app.NewOnbDeps(ctx, require.New(t), dbConns)
	defer closeConnFunc()

	user1 := createCleanUserParams()
	livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
	v, err := ioutil.ReadFile(livenessVideoFilePath)
	assert.NoError(err, "Failed to read video file")
	user1.Video = v

	user2 := createCleanUserParams()
	// AFU Completed. resume onboarding
	nextAction := app.AFU_OnlyPhoneNumber(ctx, dep, user1, user2)
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
		nextAction.GetScreen().String(),
	)
}

func Onboarding_AuthFactorUpdate_OnlyEmail(t *testing.T) {
	defer Recover(t)
	ctx := context.Background()
	assert := require.New(t)

	dep, closeConnFunc := app.NewOnbDeps(ctx, require.New(t), dbConns)
	defer closeConnFunc()

	user1 := createCleanUserParams()
	livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
	v, err := ioutil.ReadFile(livenessVideoFilePath)
	assert.NoError(err, "Failed to read video file")
	user1.Video = v

	// AFU Completed. resume onboarding
	nextAction := app.AFU_OnlyEmail(ctx, dep, user1)
	// Next action is the pending onboarding next action.
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(), nextAction.GetScreen().String())
}

func Onboarding_AuthFactorUpdate_EmailDevice(t *testing.T) {
	// t.Skip("skipping this test for now as acceptance and smoke runs with different set of credential order (pinot is ignored in acceptance mandating ATM PIN check)")
	defer Recover(t)
	assert := require.New(t)
	ctx := context.Background()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require.New(t), dbConns)
	defer closeConnFunc()

	aUser := createCleanUserParams()
	livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
	v, err := ioutil.ReadFile(livenessVideoFilePath)
	assert.NoError(err, "Failed to read video file")
	aUser.Video = v
	nextAction := app.AFU_EmailDevice(ctx, dep, aUser)
	// AFU Completed. resume onboarding
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
		nextAction.GetScreen().String(),
	)
}

func Onboarding_AuthFactorUpdate_OnlyDevice(t *testing.T) {
	defer Recover(t)
	// t.Skip("skipping due to an issue in processing frame, fix it to run in smoke tests")
	assert := require.New(t)
	ctx := context.Background()
	dep, closeConnFunc := app.NewOnbDeps(ctx, require.New(t), dbConns)
	defer closeConnFunc()

	aUser := createCleanUserParams()
	livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
	v, err := ioutil.ReadFile(livenessVideoFilePath)
	assert.NoError(err, "Failed to read video file")
	aUser.Video = v
	nextAction := app.AFU_OnlyDevice(ctx, dep, aUser)
	// AFU Completed. resume onboarding
	dep.Assert.Equal(app.ActionAfterCreateSavingsAccount.String(),
		nextAction.GetScreen().String(),
	)
}

func AFUTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is the test for onboarding AFU only phone number",
				BU:          owners.BUSINESS_UNIT_GROWTH_AND_EXPERIENCE,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: Onboarding_AuthFactorUpdate_OnlyPhoneNumber,
		},
		{
			TestProperty: &TestProperty{
				Description: "This is the test for onboarding AFU only device",
				BU:          owners.BUSINESS_UNIT_GROWTH_AND_EXPERIENCE,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: Onboarding_AuthFactorUpdate_OnlyEmail,
		},
		{
			TestProperty: &TestProperty{
				Description: "This is the test for onboarding AFU email device",
				BU:          owners.BUSINESS_UNIT_GROWTH_AND_EXPERIENCE,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: Onboarding_AuthFactorUpdate_EmailDevice,
		},
		{
			TestProperty: &TestProperty{
				Description: "This is the test for onboarding AFU only device",
				BU:          owners.BUSINESS_UNIT_GROWTH_AND_EXPERIENCE,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: Onboarding_AuthFactorUpdate_OnlyDevice,
		},
	}
}
