package load

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"sync"

	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/be-common/pkg/cache"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/frontend/account/screening"
	"github.com/epifi/gamma/testing/integration/app"

	"github.com/epifi/gamma/scripts/crud/userdata"

	feUpiPb "github.com/epifi/gamma/api/frontend/account/upi"

	feTimelinePb "github.com/epifi/gamma/api/frontend/timeline"

	latLngPb "google.golang.org/genproto/googleapis/type/latlng"

	"github.com/stretchr/testify/require"
	"google.golang.org/grpc"
	"gorm.io/gorm"

	feSignupPb "github.com/epifi/gamma/api/frontend/account/signup"
	feCardPb "github.com/epifi/gamma/api/frontend/card"
	feConsentPb "github.com/epifi/gamma/api/frontend/consent"
	feDepositPb "github.com/epifi/gamma/api/frontend/deposit"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	feSavingsPb "github.com/epifi/gamma/api/frontend/savings"
	feUserPb "github.com/epifi/gamma/api/frontend/user"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage"

	"github.com/epifi/gamma/testing/load/config"
)

var (
	// frontend services client connections
	feConn             *grpc.ClientConn
	feSignupClient     feSignupPb.SignupClient
	feConsentClient    feConsentPb.ConsentClient
	feUserClient       feUserPb.UserClient
	feSavingsClient    feSavingsPb.SavingsClient
	feCardClient       feCardPb.CardClient
	feDepositClient    feDepositPb.DepositClient
	feTxnClient        feTransactionPb.TransactionClient
	feSimulationClient fePayPb.SimulationClient
	feTimelineClient   feTimelinePb.TimelineServiceClient
	feUpiClient        feUpiPb.UPIClient
	feScreeningClient  screening.ScreeningClient
	authClient         authPb.AuthClient

	// determining current directory path
	_, b, _, _     = runtime.Caller(0)
	currentDirPath = filepath.Dir(b)

	// variables to hold conf and db instances
	conf                                *config.Config
	epifiDb                             *gorm.DB
	timelineDb                          *gorm.DB
	actorDb                             *gorm.DB
	paymentInstrumentDb                 *gorm.DB
	simDb                               *gorm.DB
	userRedisCacheStorage               *cache.RedisCacheStorage
	actorRedisCacheStorage              *cache.RedisCacheStorage
	piRedisCacheStorage                 *cache.RedisCacheStorage
	savingsLedgerReconRedisCacheStorage *cache.RedisCacheStorage
	timelineRedisCacheStorage           *cache.RedisCacheStorage
	savingsRedisCacheStorage            *cache.RedisCacheStorage
	devRegCacheStorage                  *cache.RedisCacheStorage
)

var (
	basePanNumber int32 = 1000
	lck           sync.RWMutex
)

const (
	PANPrefix                = "TESPP"
	PANSuffix                = "D"
	PhoneNumberPrefix uint64 = 23
)

// cleans up the users from database created by load test suite, uses PAN number pattern (^TESPP([0-9]{4})"+"D$)
// and phone number pattern (23-ABCD-ABCD) for identifying test users
func cleanupExistingTestUsers() error {
	query := epifiDb.Raw("SELECT profile->'phone_number', profile->>'PAN' FROM users WHERE profile->'phone_number'->>'national_number' LIKE ?", "23%")
	logger.Info(context.Background(), "users to be cleaned", zap.Int64("count", query.RowsAffected))
	rows, err := query.Rows()
	defer rows.Close()
	if err != nil {
		return fmt.Errorf("failed to fetch user with given phone number: %w", err)
	}

	for rows.Next() {
		num := commontypes.PhoneNumber{}
		var pan sql.NullString
		err := rows.Scan(&num, &pan)
		if err != nil {
			return err
		}

		if isTestUser(num.NationalNumber, pan) {
			logger.InfoNoCtx("requesting deletion of user with phone", zap.Any("phone_no", num))
			userdata.DeleteUserData(epifiDb, actorDb, timelineDb, paymentInstrumentDb, simDb, &commontypes.PhoneNumber{
				CountryCode:    num.CountryCode,
				NationalNumber: num.NationalNumber,
			}, userRedisCacheStorage, actorRedisCacheStorage, piRedisCacheStorage, savingsLedgerReconRedisCacheStorage, timelineRedisCacheStorage, savingsRedisCacheStorage, devRegCacheStorage, authClient)
		}

	}

	logger.InfoNoCtx("Cleanup completed for existing test users")
	return nil
}

func isTestUser(phone uint64, pan sql.NullString) bool {
	if pan.Valid && pan.String != "" {
		// if pan was provided checks it to be in pattern ^TESPP([0-9]{4})"+"D$
		matched, err := regexp.MatchString("^"+PANPrefix+"([0-9]{4})"+PANSuffix+"$", pan.String)
		if err != nil || !matched {
			return false
		}
	}

	// checks for phone to be in pattern 23-ABCD-ABCD, here ABCD can be [0001-9999]
	x := phone % 10000
	phone = phone / 10000
	y := phone % 10000
	if x != y {
		return false
	}
	return true
}

// initialize initializes server client connections and db connections
func initialize() {
	var err error
	conf, err = config.Load()
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to load config: %v", err))
	}
	userRedisClient := storage.NewRedisClient(conf.UserRedis.Options, conf.UserRedis.IsSecure, false)
	userRedisCacheStorage = cache.NewRedisCacheStorage(userRedisClient)

	actorRedisClient := storage.NewRedisClient(conf.ActorRedis.Options, conf.ActorRedis.IsSecure, false)
	actorRedisCacheStorage = cache.NewRedisCacheStorage(actorRedisClient)

	piRedisClient := storage.NewRedisClient(conf.PiRedis.Options, conf.PiRedis.IsSecure, false)
	piRedisCacheStorage = cache.NewRedisCacheStorage(piRedisClient)

	savingsLegerReconRedisClient := storage.NewRedisClient(conf.SavingsLedgerReconRedis.Options, conf.SavingsLedgerReconRedis.IsSecure, false)
	savingsLedgerReconRedisCacheStorage = cache.NewRedisCacheStorage(savingsLegerReconRedisClient)

	timelineRedisClient := storage.NewRedisClient(conf.TimelineRedis.Options, conf.TimelineRedis.IsSecure, false)
	timelineRedisCacheStorage = cache.NewRedisCacheStorage(timelineRedisClient)

	savingsRedisClient := storage.NewRedisClient(conf.SavingsRedis.Options, conf.SavingsRedis.IsSecure, false)
	savingsRedisCacheStorage = cache.NewRedisCacheStorage(savingsRedisClient)

	devRegRedisClient := storage.NewRedisClientFromConfig(conf.DevRegRedis, false)
	devRegCacheStorage = cache.NewRedisCacheStorage(devRegRedisClient)

	initializeDbConnections()
	initServerClients()
}

func initializeDbConnections() {
	var err error
	epifiDb, err = storageV2.NewCRDBWithConfig(conf.EpifiDb, false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}

	simDb, err = storageV2.NewCRDBWithConfig(conf.SimulatorDb, false)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to db: %v", err))
	}

	timelineDb, err = storageV2.NewGormDB(conf.TimelineDb)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to timeline db,err: %v", err))
	}

	actorDb, err = storageV2.NewGormDB(conf.ActorDb)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to actor db,err: %v", err))
	}

	paymentInstrumentDb, err = storageV2.NewGormDB(conf.PaymentInstrumentDb)
	if err != nil {
		logger.Fatal(fmt.Sprintf("failed to connect to payment instrument db,err: %v", err))
	}

}

func initServerClients() {
	feConn = epifigrpc.NewConnByService(cfg.FRONTEND_SERVICE)

	feSignupClient = feSignupPb.NewSignupClient(feConn)
	feSavingsClient = feSavingsPb.NewSavingsClient(feConn)
	feConsentClient = feConsentPb.NewConsentClient(feConn)
	feUserClient = feUserPb.NewUserClient(feConn)
	feCardClient = feCardPb.NewCardClient(feConn)
	feDepositClient = feDepositPb.NewDepositClient(feConn)
	feTxnClient = feTransactionPb.NewTransactionClient(feConn)
	feSimulationClient = fePayPb.NewSimulationClient(feConn)
	feTimelineClient = feTimelinePb.NewTimelineServiceClient(feConn)
	feUpiClient = feUpiPb.NewUPIClient(feConn)
	feScreeningClient = screening.NewScreeningClient(feConn)
	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	authClient = authPb.NewAuthClient(authConn)
}

func closeConnections() {
	epifiSqlDb, _ := epifiDb.DB()
	_ = epifiSqlDb.Close()

	simSqlDb, _ := simDb.DB()
	_ = simSqlDb.Close()

	timelineSqlDb, _ := timelineDb.DB()
	_ = timelineSqlDb.Close()

	actorSqlDb, _ := actorDb.DB()
	_ = actorSqlDb.Close()

	paymentInstrumentSqlDb, _ := paymentInstrumentDb.DB()
	_ = paymentInstrumentSqlDb.Close()

	_ = logger.Log.Sync()
	epifigrpc.CloseConn(feConn)
}

// CreateNewUser generates and returns a reference to new user objects
func CreateNewUser(a *require.Assertions) *app.UserData {
	var claims struct {
		Email         string `json:"email"`
		EmailVerified bool   `json:"email_verified"`
		Name          string `json:"name"`
		Picture       string `json:"picture"`
		GivenName     string `json:"given_name"`
		FamilyName    string `json:"family_name"`
		Locale        string `json:"locale"`
		Issuer        string
	}
	claims.GivenName = "first"
	claims.FamilyName = "last"
	claims.Email = idgen.RandAlphaNumericString(10) + "<EMAIL>"
	c, _ := json.Marshal(claims)

	pan := generatePanNumber()
	phoneNum := generatePhoneNumber(pan)

	userData := &app.UserData{
		Device: &commontypes.Device{
			Manufacturer: "testManufacturer",
			Model:        "testModel",
			HwVersion:    "testHwVersion",
			SwVersion:    "testSwVersion",
			OsApiVersion: "testOsApiVersion",
			DeviceId:     idgen.RandAlphaNumericString(15),
			LatLng:       &latLngPb.LatLng{Latitude: 89.123, Longitude: 98.45},
		},

		Phone: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: phoneNum,
		},

		Name: &commontypes.Name{
			FirstName: "Byomkesh",
			LastName:  "Bakshi",
		},
		// The simulator accepts this as a valid value
		Pan:            pan,
		Video:          []byte{1, 2, 3},
		OAuthIDToken:   string(c),
		SafetyNetToken: "ValidToken",
	}

	livenessVideoFilePath := filepath.Join(currentDirPath, conf.Application.RelativeLivenessVideoFilePath)
	v, err := ioutil.ReadFile(livenessVideoFilePath)
	a.NoError(err, "Failed to read video file")
	userData.Video = v

	return userData
}

// generates a phone number from pan number which can be used for successful ckyc
func generatePhoneNumber(pan string) uint64 {
	var phoneNumber uint64
	phoneNumber = PhoneNumberPrefix * uint64(math.Pow(10, 8))

	num, err := strconv.Atoi(pan[5:9])
	if err != nil {
		logger.ErrorNoCtx("unable to generate phone number from pan, reason %w ", zap.Error(err))
	}
	phoneNumber = phoneNumber + uint64(num*10000) + uint64(num)
	return phoneNumber
}

// generates a pan number which can be used for successful ckyc
func generatePanNumber() string {
	// TODO:shubhamchand to check if atomic add can be used here
	lck.Lock()
	defer lck.Unlock()
	panNumber := fmt.Sprintf("%v%v%v", PANPrefix, basePanNumber, PANSuffix)
	basePanNumber++
	return panNumber
}

func CreateOnbDep(a *require.Assertions) *app.OnbDep {
	return &app.OnbDep{
		Assert:          a,
		DB:              epifiDb,
		SignupClient:    feSignupClient,
		ConsentClient:   feConsentClient,
		UserClient:      feUserClient,
		SavingsClient:   feSavingsClient,
		CardClient:      feCardClient,
		ScreeningClient: feScreeningClient,
	}
}
