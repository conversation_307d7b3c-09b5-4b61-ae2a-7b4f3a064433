package events

// event name constants
const (
	EventLoadedHelpCategories               = "LoadedCategoryLandingScreenServer"
	EventCategoryLoad                       = "LoadedCategoryDetailsScreenServer"
	EventFolderLoad                         = "LoadedFolderPage"
	EventArticleLoad                        = "LoadedArticleScreenServer"
	EventLLMModelResponse                   = "LLMModelResponse"
	EventIsCachedModelResponseUsed          = "IssueReportingIsCachedModelResponseUsed"
	EventIssueCategorizerResponse           = "IssueReportingCategorizerResponse"
	EventIssueFcrType                       = "IssueReportingFcrType"
	EventIssueRiskCategoryL1Type            = "InAppIssueReportingRiskL1Event"
	EventUserContextTicketEvent             = "InAppIssueReportingUserContextTicketEvent"
	EventUserContextIncidentEvent           = "InAppIssueReportingUserContextIncidentEvent"
	EventUserContextActivityEvent           = "InAppIssueReportingUserContextActivityEvent"
	EventUserContextCustomer360Event        = "InAppIssueReportingUserContextCustomer360Event"
	EventCSATLLMQueryResponse               = "CSATLLMQueryServer"
	FeedbackSurveyResponseReceivedEventName = "FeedbackSurveyResponseReceivedEvent"
)

// flow names
const (
	FlowLoadedHelpCategories = "CX"
	FlowLoadCategory         = "CX"
	FlowLoadFolder           = "CX"
	FlowLoadArticle          = "CX"
	FlowInAppIssueReporting  = "CX"
)

const (
	DBFetchErr = "error fetching db entry"
)
