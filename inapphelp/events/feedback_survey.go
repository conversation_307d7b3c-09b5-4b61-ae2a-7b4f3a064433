package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"

	feedbackEnginePb "github.com/epifi/gamma/api/inapphelp/feedback_engine"
)

type FeedbackSurveyResponseReceivedEvent struct {
	ActorId         string
	ProspectId      string
	EventId         string
	Timestamp       time.Time
	SurveyId        string
	AttemptId       string
	QuestionId      string
	ResponseDetails *feedbackEnginePb.FeedbackAnswer
	EventType       string
	EventName       string
	Properties      map[string]string
}

func NewFeedbackSurveyResponseReceivedEvent(actorId, attemptId, surveyId, questionId string, responseDetails *feedbackEnginePb.FeedbackAnswer) *FeedbackSurveyResponseReceivedEvent {
	return &FeedbackSurveyResponseReceivedEvent{
		ActorId:         actorId,
		ProspectId:      uuid.New().String(),
		EventId:         uuid.New().String(),
		Timestamp:       time.Now(),
		EventType:       events.EventTrack,
		EventName:       FeedbackSurveyResponseReceivedEventName,
		SurveyId:        surveyId,
		AttemptId:       attemptId,
		QuestionId:      questionId,
		ResponseDetails: responseDetails,
	}
}

func (c *FeedbackSurveyResponseReceivedEvent) GetEventType() string {
	return c.EventType
}

func (c *FeedbackSurveyResponseReceivedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *FeedbackSurveyResponseReceivedEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *FeedbackSurveyResponseReceivedEvent) GetEventId() string {
	return c.EventId
}

func (c *FeedbackSurveyResponseReceivedEvent) GetUserId() string {
	return c.ActorId
}

func (c *FeedbackSurveyResponseReceivedEvent) GetProspectId() string {
	return c.ProspectId
}

func (c *FeedbackSurveyResponseReceivedEvent) GetEventName() string {
	return c.EventName
}
