package kubair

import (
	"context"

	"github.com/epifi/be-common/api/rpc"

	feHeader "github.com/epifi/gamma/api/frontend/header"
	kubairFePb "github.com/epifi/gamma/api/frontend/kubair"
	insightKubairPb "github.com/epifi/gamma/api/insights/kubair"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/frontend/config/genconf"
)

type Service struct {
	kubairFePb.UnimplementedKubairServer
	genConfig          *genconf.Config
	insightKuberClient insightKubairPb.InsightKubairClient
	deeplinkBuilder    deeplink_builder.IDeeplinkBuilder
}

func NewService(genConfig *genconf.Config, insightKuberClient insightKubairPb.InsightKubairClient, deeplinkBuilder deeplink_builder.IDeeplinkBuilder) *Service {
	return &Service{
		genConfig:          genConfig,
		insightKuberClient: insightKuberClient,
		deeplinkBuilder:    deeplinkBuilder,
	}
}

func (s *Service) GetAssetLandingPage(ctx context.Context, req *kubairFePb.GetAssetLandingPageRequest) (*kubairFePb.GetAssetLandingPageResponse, error) {
	// todo(numan): to move to factory pattern as asset are onboarding
	resp, err := s.insightKuberClient.GetAssetLandingPage(ctx, req)
	if err != nil {
		return &kubairFePb.GetAssetLandingPageResponse{RespHeader: &feHeader.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	resp.FloatingActionButton = s.deeplinkBuilder.GetTalkToAIFloatingIconWithDeeplink(ctx, req.GetReq().GetAuth().GetActorId(), networth.Entrypoint_ENTRYPOINT_ASSET_LANDING)
	return resp, nil
}

func (s *Service) GetBottomComponentsForAssetLandingTab(ctx context.Context, req *kubairFePb.GetBottomComponentsForAssetLandingTabRequest) (*kubairFePb.GetBottomComponentsForAssetLandingTabResponse, error) {
	// todo(numan): to move to factory pattern as asset are onboarding
	resp, err := s.insightKuberClient.GetBottomComponentsForAssetLandingTab(ctx, req)
	if err != nil {
		return &kubairFePb.GetBottomComponentsForAssetLandingTabResponse{RespHeader: &feHeader.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	return resp, nil
}
