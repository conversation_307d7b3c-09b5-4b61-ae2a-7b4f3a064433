package indianstocks

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/connected_account"
	connectedAccountSecuritieswPb "github.com/epifi/gamma/api/connected_account/securities"
	"github.com/epifi/gamma/api/frontend/header"
	feWPb "github.com/epifi/gamma/api/frontend/investment/indianstocks"
	investmentUi "github.com/epifi/gamma/api/frontend/investment/ui"
	fePb "github.com/epifi/gamma/api/investment/indianstocks/frontend"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/frontend/config/genconf"
	brokerDetails "github.com/epifi/gamma/frontend/investment/broker_details"
	"github.com/epifi/gamma/frontend/investment/config"
	"github.com/epifi/gamma/frontend/investment/indianstocks/dashboard"
	"github.com/epifi/gamma/frontend/investment/indianstocks/dashboard/filter"
	"github.com/epifi/gamma/frontend/investment/indianstocks/data_fetcher"
	"github.com/epifi/gamma/pkg/feature/release"
)

type Service struct {
	dynConf                *genconf.Config
	connectedAccountClient connected_account.ConnectedAccountClient
	accountFilterProcessor filter.AccountFilterProcessor
	sortByFilterProcessor  filter.SortByFilterProcessor
	dashboardBuilder       dashboard.DashboardBuilder
	isinToStockDetailsMap  config.IndianStockCatalog
	caSecuritiesClient     connectedAccountSecuritieswPb.SecuritiesClient
	dataFetcherFactory     data_fetcher.IDataFetcher
	brokerDetails          brokerDetails.IBrokerDetailsGetter
	actorClient            actorPb.ActorClient
	userClient             userPb.UsersClient
	releaseEvaluator       release.IEvaluator
	deeplinkBuilder        deeplink_builder.IDeeplinkBuilder
}

func NewService(
	dynConf *genconf.Config,
	connectedAccountClient connected_account.ConnectedAccountClient,
	accountFilterProcessor filter.AccountFilterProcessor,
	sortByFilterProcessor filter.SortByFilterProcessor,
	dashboardBuilder dashboard.DashboardBuilder,
	caSecuritiesClient connectedAccountSecuritieswPb.SecuritiesClient,
	dataFetcherFactory data_fetcher.IDataFetcher,
	brokerDetails brokerDetails.IBrokerDetailsGetter,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	releaseEvaluator release.IEvaluator,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
) *Service {
	isinToStockDetailsMap, err := config.LoadIndianStocksDetailsMap(dynConf.Investment().IndianStocksDetailsConfigJson())
	if err != nil {
		logger.Panic("failed to load isin to indian stock details config", zap.Error(err))
	}
	return &Service{
		dynConf:                dynConf,
		connectedAccountClient: connectedAccountClient,
		accountFilterProcessor: accountFilterProcessor,
		sortByFilterProcessor:  sortByFilterProcessor,
		dashboardBuilder:       dashboardBuilder,
		isinToStockDetailsMap:  isinToStockDetailsMap,
		caSecuritiesClient:     caSecuritiesClient,
		brokerDetails:          brokerDetails,
		dataFetcherFactory:     dataFetcherFactory,
		actorClient:            actorClient,
		userClient:             userClient,
		releaseEvaluator:       releaseEvaluator,
		deeplinkBuilder:        deeplinkBuilder,
	}
}

// nolint: funlen
func (s *Service) GetIndianStocksDashboard(ctx context.Context, req *feWPb.GetIndianStocksDashboardRequest) (*feWPb.GetIndianStocksDashboardResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	accountSummaryDetailsMap, accSummaryErr := s.dataFetcherFactory.GetAccountSummaryDetailsMap(ctx, actorId)
	if accSummaryErr != nil {
		logger.Error(ctx, "failed to get accounts account summary for actor", zap.Error(accSummaryErr))
		if errors.Is(accSummaryErr, epifierrors.ErrRecordNotFound) {
			return s.genFailureIndianStockDashboardResp(rpc.StatusRecordNotFound())
		}
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}

	latestSyncedTs := getLatestSyncedTsFromAccounts(accountSummaryDetailsMap)

	currentPortfolioValue, err := s.getCurrentPortfolioValue(accountSummaryDetailsMap)
	if err != nil {
		logger.Error(ctx, "failed to get current portfolio value", zap.Error(err))
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}

	holdingSortByFilterValue, err := s.sortByFilterProcessor.GetSortByFilterValue(req.GetFilterValuesMap())
	if err != nil {
		logger.Error(ctx, "failed to get sort by filter values from request", zap.Error(err))
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}

	filteredAccountIds, err := s.accountFilterProcessor.GetAccountFilterValue(req.GetFilterValuesMap(), accountSummaryDetailsMap)
	if err != nil {
		logger.Error(ctx, "failed to get filtered account ids", zap.Error(err))
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}

	aggregatedHoldings, err := s.getAggregatedHoldingDetailsForAccounts(accountSummaryDetailsMap, filteredAccountIds)
	if err != nil {
		logger.Error(ctx, "failed to get aggregated holdings", zap.Error(err))
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}

	err = sortHoldings(aggregatedHoldings, holdingSortByFilterValue)
	if err != nil {
		logger.Error(ctx, "failed to sort holdings", zap.Error(err))
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}

	accountFilter, err := s.accountFilterProcessor.BuildAccountFilter(ctx, req.GetFilterValuesMap(), accountSummaryDetailsMap)
	if err != nil {
		logger.Error(ctx, "failed to generate account filter", zap.Error(err))
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}

	sortByFilter, err := s.sortByFilterProcessor.BuildSortByFilter(holdingSortByFilterValue)
	if err != nil {
		logger.Error(ctx, "failed to generate sortBy filter", zap.Error(err))
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}

	stockDashboard, err := s.buildIndianStockDashboard(currentPortfolioValue, latestSyncedTs, aggregatedHoldings, accountFilter, sortByFilter)
	if err != nil {
		logger.Error(ctx, "failed to generate indian stock dashboard", zap.Error(err))
		return s.genFailureIndianStockDashboardResp(rpc.StatusInternal())
	}
	return &feWPb.GetIndianStocksDashboardResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Dashboard:            stockDashboard,
		FloatingActionButton: s.deeplinkBuilder.GetTalkToAIFloatingIconWithDeeplink(ctx, actorId, networth.Entrypoint_ENTRYPOINT_INDIAN_STOCKS_DASHBOARD),
	}, nil
}

func (s *Service) buildIndianStockDashboard(portfolioValue *money.Money, lastSyncedAt *timestamppb.Timestamp, holdings []*fePb.HoldingDetails,
	accountFilter *investmentUi.HorizontalFilter, sortByFilter *investmentUi.HorizontalFilter) (*investmentUi.InstrumentDashboard, error) {

	topSection, err := s.dashboardBuilder.BuildDefaultStaticTopSection(portfolioValue, "CURRENT VALUE", lastSyncedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to generate top section of dashboard : %w", err)
	}
	bottomSection, err := s.dashboardBuilder.BuildDefaultBottomSection(holdings, accountFilter, sortByFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to generate bottom section of dashboard : %w", err)
	}
	return &investmentUi.InstrumentDashboard{
		TopSection:    topSection,
		BottomSection: bottomSection,
	}, nil
}

func (s *Service) genFailureIndianStockDashboardResp(status *rpc.Status) (*feWPb.GetIndianStocksDashboardResponse, error) {
	return &feWPb.GetIndianStocksDashboardResponse{
		RespHeader: &header.ResponseHeader{
			Status:    status,
			ErrorView: s.errorViewWithTitle(internalServerErrorTitle, internalServerErrorDescription),
		},
	}, nil
}

func (s *Service) getAggregatedHoldingDetailsForAccounts(allAccountsMap map[string]*fePb.AccountSummary, accountIds []string) ([]*fePb.HoldingDetails, error) {
	var resHoldings []*fePb.HoldingDetails
	IsinToHoldingInfoMap := make(map[string]*fePb.HoldingDetails)
	for _, accountId := range accountIds {
		summaryDetails, found := allAccountsMap[accountId]
		if !found {
			return nil, fmt.Errorf("account %s not found in allAccountsMap", accountId)
		}
		for _, holdingInfo := range summaryDetails.GetHoldingDetails() {
			isin := holdingInfo.GetIsin()
			if _, holdingFound := IsinToHoldingInfoMap[isin]; !holdingFound {
				IsinToHoldingInfoMap[isin] = holdingInfo
			} else {
				curHolding := IsinToHoldingInfoMap[isin]
				mergedHolding, err := mergeHolding(curHolding, holdingInfo)
				if err != nil {
					return nil, fmt.Errorf("failed to merge holdings : %w", err)
				}
				IsinToHoldingInfoMap[isin] = mergedHolding
			}
		}
	}
	for _, holdingInfo := range IsinToHoldingInfoMap {
		resHoldings = append(resHoldings, holdingInfo)
	}
	return s.populateNameIconAndConvertToHoldingDetails(resHoldings), nil
}

func mergeHolding(holdingA, holdingB *fePb.HoldingDetails) (*fePb.HoldingDetails, error) {
	if holdingA.GetIsin() != holdingB.GetIsin() {
		return nil, fmt.Errorf("merge cannot be done since isin in 2 holdings are different %s %s", holdingA.GetIsin(), holdingB.GetIsin())
	}

	holdingACurrVal := holdingA.GetCurrentValue()
	holdingBCurrVal := holdingB.GetCurrentValue()
	// There is a possibility that no current value is present for holdings for a particular investment instrument,
	// ignoring addition of current value for such instruments to portfolio value
	if holdingACurrVal == nil {
		holdingACurrVal = moneyPb.ZeroINR().GetPb()
	}

	if holdingBCurrVal == nil {
		holdingBCurrVal = moneyPb.ZeroINR().GetPb()
	}

	currVal, err := moneyPb.Sum(holdingACurrVal, holdingBCurrVal)
	if err != nil {
		return nil, fmt.Errorf("failed to sum amount: %w", err)
	}
	return &fePb.HoldingDetails{
		Isin:                              holdingA.GetIsin(),
		Units:                             holdingA.GetUnits() + holdingB.GetUnits(),
		IsinDescription:                   holdingA.GetIsinDescription(),
		ActivityFilter:                    holdingA.GetActivityFilter(),
		InvestmentInstrumentDetailsFilter: holdingA.GetInvestmentInstrumentDetailsFilter(),
		CurrentValue:                      currVal,
	}, nil
}

func (s *Service) populateNameIconAndConvertToHoldingDetails(holdingsInfo []*fePb.HoldingDetails) []*fePb.HoldingDetails {
	var holdingsDetails []*fePb.HoldingDetails
	for _, info := range holdingsInfo {
		iconUrl, name := s.isinToStockDetailsMap.GetStockIconAndName(info.GetIsin(), info.GetIsinDescription())
		holdingsDetails = append(holdingsDetails, &fePb.HoldingDetails{
			Isin:                              info.GetIsin(),
			Units:                             info.GetUnits(),
			CurrentValue:                      info.GetCurrentValue(),
			IsinDescription:                   info.GetIsinDescription(),
			ActivityFilter:                    info.GetActivityFilter(),
			InvestmentInstrumentDetailsFilter: info.GetInvestmentInstrumentDetailsFilter(),
			Name:                              name,
			IconUrl:                           iconUrl,
		})
	}
	return holdingsDetails
}

func sortHoldings(holdings []*fePb.HoldingDetails, holdingsSortBy fePb.HoldingsSortBy) error {
	if holdingsSortBy == fePb.HoldingsSortBy_HOLDINGS_SORT_BY_UNSPECIFIED {
		holdingsSortBy = filter.DefaultHoldingSort
	}
	if err := validateHoldings(holdings); err != nil {
		return fmt.Errorf("validate holdings failed, skipping sorting")
	}
	var sortingFunc func(i, j int) bool
	switch holdingsSortBy {
	case fePb.HoldingsSortBy_HOLDINGS_SORT_BY_ALPHABETICAL_A_TO_Z:
		sortingFunc = func(i, j int) bool {
			return holdings[i].Name < holdings[j].Name
		}
	case fePb.HoldingsSortBy_HOLDINGS_SORT_BY_ALPHABETICAL_Z_TO_A:
		sortingFunc = func(i, j int) bool {
			return holdings[i].Name > holdings[j].Name
		}
	case fePb.HoldingsSortBy_HOLDINGS_SORT_BY_CURRENT_VALUE_LOW_TO_HIGH:
		sortingFunc = func(i, j int) bool {
			cmp, _ := moneyPb.CompareV2(holdings[i].CurrentValue, holdings[j].CurrentValue)
			return cmp <= 0
		}
	case fePb.HoldingsSortBy_HOLDINGS_SORT_BY_CURRENT_VALUE_HIGH_TO_LOW:
		sortingFunc = func(i, j int) bool {
			cmp, _ := moneyPb.CompareV2(holdings[i].CurrentValue, holdings[j].CurrentValue)
			return cmp > 0
		}
	default:
		return fmt.Errorf("unhandled sorting type : %v", holdingsSortBy)
	}
	sort.Slice(holdings, sortingFunc)
	return nil
}

func (s *Service) getCurrentPortfolioValue(detailsMap map[string]*fePb.AccountSummary) (*money.Money, error) {
	var err error
	portfolioValue := moneyPb.ZeroINR().GetPb()
	for _, summary := range detailsMap {
		// TODO: fix post identifying the root issue. https://epifi.slack.com/archives/C0623V4V3RS/p1715414183427459
		if s.dynConf.Investment().IsIndianStocksPortfolioValueCalculated() {
			for _, holdingDetails := range summary.GetHoldingDetails() {
				// Hypothesis is AA data could be incorrect here for total portfolio value hence using individual holding values sum
				// NOTE: when changing here also change the corresponding logic to calculate value for net-worth widget (equity and etf)
				portfolioValue, err = moneyPb.Sum(portfolioValue, holdingDetails.GetCurrentValue())
				if err != nil {
					return nil, fmt.Errorf("failed to sum portfolio value with holdings current value : %w", err)
				}
			}
		} else {
			portfolioValue, err = moneyPb.Sum(portfolioValue, summary.GetCurrentValue())
			if err != nil {
				return nil, fmt.Errorf("failed to sum portfolio value with account current value : %w", err)
			}
		}
	}
	return portfolioValue, nil
}

func getLatestSyncedTsFromAccounts(accounts map[string]*fePb.AccountSummary) *timestamppb.Timestamp {
	var ts *timestamppb.Timestamp
	for _, acc := range accounts {
		if acc.GetAccountDetails().GetLastSyncedAt().AsTime().After(ts.AsTime()) {
			ts = acc.GetAccountDetails().GetLastSyncedAt()
		}
	}
	return ts
}

func validateHoldings(holdings []*fePb.HoldingDetails) error {
	for _, holding := range holdings {
		if holding == nil {
			return fmt.Errorf("holding cannot be nil, cannot sort")
		}
		if holding.CurrentValue == nil {
			return fmt.Errorf("current value in holding is nil, cannot sort")
		}
		if holding.Name == "" {
			return fmt.Errorf("holding name cannot be empty, failure")
		}
	}
	return nil
}
