// nolint: dupl,funlen,unparam,goconst
package connected_account

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	fiToFiCaHelper "github.com/epifi/gamma/frontend/connected_account/fi_to_fi_helper"

	"github.com/epifi/gamma/api/frontend/deeplinkv2"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	insightsPkg "github.com/epifi/gamma/insights/pkg"

	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/durationpb"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/events"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/frontend/connected_account/features"
	appFeedbackPb "github.com/epifi/gamma/api/inapphelp/app_feedback"
	"github.com/epifi/gamma/api/typesv2/ui"
	caErrors "github.com/epifi/gamma/frontend/connected_account/errors"
	tieringEvents "github.com/epifi/gamma/frontend/events"
	panPkg "github.com/epifi/gamma/pkg/pan"
	pkgUser "github.com/epifi/gamma/pkg/user"

	"github.com/epifi/gamma/api/frontend/account/signup"
	feConnectedAccountCommonPb "github.com/epifi/gamma/api/frontend/connected_account/common"
	feConnectedAccountFeaturesPb "github.com/epifi/gamma/api/frontend/connected_account/features"
	onboardingPkg "github.com/epifi/gamma/pkg/onboarding"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/segment"
	caDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/frontend/connected_account/factory"
	facModel "github.com/epifi/gamma/frontend/connected_account/factory/model"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	goUtils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	accountPb "github.com/epifi/gamma/api/accounts"
	beActorPb "github.com/epifi/gamma/api/actor"
	beAuthPb "github.com/epifi/gamma/api/auth"
	beCaPb "github.com/epifi/gamma/api/connected_account"
	beCaEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	beCaExtPb "github.com/epifi/gamma/api/connected_account/external"
	consentPb "github.com/epifi/gamma/api/consent"
	depositPb "github.com/epifi/gamma/api/deposit"
	feConnectedAccPb "github.com/epifi/gamma/api/frontend/connected_account"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/deposit"
	feDepositPb "github.com/epifi/gamma/api/frontend/deposit"
	"github.com/epifi/gamma/api/frontend/header"
	beNetWorthPb "github.com/epifi/gamma/api/insights/networth"
	model "github.com/epifi/gamma/api/insights/networth/model"
	beSavingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	beUserPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/deposit/utils"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	displayTextRange   = "%s to %s"
	finvuVuaTemplate   = "%s@finvu"
	checkBoxFontColor  = "#8D8D8D"
	FiLiteCheckBoxText = "I understand this data will be available for <a style=\"color: #00B899\" href=\"%s\">90 days</a>."
)

var (
	fiLiteCheckboxItem = func(tncUrl string) *widget.CheckboxItem {
		return &widget.CheckboxItem{
			DisplayText: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: fmt.Sprintf(FiLiteCheckBoxText, tncUrl),
				},
				FontColor:        checkBoxFontColor,
				FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				FontColorOpacity: 100,
			},
			IsChecked: false,
		}
	}
)

var newBanksList = []types.Bank{types.Bank_UCO, types.Bank_KARNATAKA, types.Bank_INDIAN_OVERSEAS, types.Bank_INDIA, types.Bank_INDIAN, types.Bank_PUNJAB_AND_SIND}

var frequencyMap = map[beCaEnumPb.FrequencyUnit]string{
	beCaEnumPb.FrequencyUnit_FREQUENCY_UNIT_HOUR:  "hourly",
	beCaEnumPb.FrequencyUnit_FREQUENCY_UNIT_DAY:   "daily",
	beCaEnumPb.FrequencyUnit_FREQUENCY_UNIT_MONTH: "monthly",
	beCaEnumPb.FrequencyUnit_FREQUENCY_UNIT_YEAR:  "yearly",
}

var DataLifeUnitMap = map[beCaEnumPb.DataLifeUnit]string{
	beCaEnumPb.DataLifeUnit_DATA_LIFE_DAY:   "hour",
	beCaEnumPb.DataLifeUnit_DATA_LIFE_MONTH: "month",
	beCaEnumPb.DataLifeUnit_DATA_LIFE_YEAR:  "year",
	beCaEnumPb.DataLifeUnit_DATA_LIFE_INF:   "Lifetime",
}

type Service struct {
	feConnectedAccPb.UnimplementedConnectedAccountServer
	beConnectedAccClient  beCaPb.ConnectedAccountClient
	connectedAccountConf  *config.ConnectedAccount
	beUserClient          beUserPb.UsersClient
	beActorClient         beActorPb.ActorClient
	beSavingsClient       beSavingsPb.SavingsClient
	beAuthClient          beAuthPb.AuthClient
	conf                  *genconf.Config
	beConsentClient       consentPb.ConsentClient
	userGroupClient       group.GroupClient
	releaseEvaluator      release.IEvaluator
	caFlowDLFactory       factory.ICaFlowDLFactory
	beSegmentationClient  segment.SegmentationServiceClient
	onbClient             onbPb.OnboardingClient
	depositsClient        depositPb.DepositClient
	netWorth              beNetWorthPb.NetWorthClient
	accountBalanceClient  accountBalancePb.BalanceClient
	userAttributesFetcher pkgUser.UserAttributesFetcher
	eventBroker           events.Broker
	panProcessor          insightsPkg.IPanProcessor
	fiToFiCaHelperSvc     fiToFiCaHelper.IConnectFiToFiHelperSvc
	deeplinkBuilder       deeplink_builder.IDeeplinkBuilder
}

func NewService(cAccClient beCaPb.ConnectedAccountClient, connectedAccountConf *config.ConnectedAccount,
	beUserClient beUserPb.UsersClient, beActorClient beActorPb.ActorClient,
	beSavingsClient beSavingsPb.SavingsClient, beAuthClient beAuthPb.AuthClient,
	conf *genconf.Config, beConsentClient consentPb.ConsentClient, userGroupClient group.GroupClient,
	releaseEvaluator release.IEvaluator, caFlowDLFactory factory.ICaFlowDLFactory,
	beSegmentationClient segment.SegmentationServiceClient, onbClient onbPb.OnboardingClient, depositsClient depositPb.DepositClient,
	netWorth beNetWorthPb.NetWorthClient, accountBalanceClient accountBalancePb.BalanceClient, userAttributesFetcher pkgUser.UserAttributesFetcher, eventBroker events.Broker,
	panProcessor insightsPkg.IPanProcessor, fiToFiCaHelperSvc fiToFiCaHelper.IConnectFiToFiHelperSvc, deeplinkBuilder deeplink_builder.IDeeplinkBuilder) *Service {
	return &Service{
		beConnectedAccClient:  cAccClient,
		connectedAccountConf:  connectedAccountConf,
		beUserClient:          beUserClient,
		beActorClient:         beActorClient,
		beSavingsClient:       beSavingsClient,
		beAuthClient:          beAuthClient,
		conf:                  conf,
		beConsentClient:       beConsentClient,
		userGroupClient:       userGroupClient,
		releaseEvaluator:      releaseEvaluator,
		caFlowDLFactory:       caFlowDLFactory,
		beSegmentationClient:  beSegmentationClient,
		onbClient:             onbClient,
		depositsClient:        depositsClient,
		netWorth:              netWorth,
		accountBalanceClient:  accountBalanceClient,
		userAttributesFetcher: userAttributesFetcher,
		eventBroker:           eventBroker,
		panProcessor:          panProcessor,
		fiToFiCaHelperSvc:     fiToFiCaHelperSvc,
		deeplinkBuilder:       deeplinkBuilder,
	}
}

func (s *Service) InitiateConsent(ctx context.Context, request *feConnectedAccPb.InitiateConsentRequest) (*feConnectedAccPb.InitiateConsentResponse, error) {
	logger.Info(ctx, "received InitiateConsent request", zap.String(logger.ACTOR_ID, request.GetReq().GetAuth().GetActorId()),
		zap.Any(logger.CONSENT_REQUEST_PURPOSE, request.GetConsentRequestPurpose()), zap.Any(logger.AA_ENTITY, request.GetAaEntity()), zap.Any(logger.CA_FLOW, request.GetCaFlowName()))
	res := &feConnectedAccPb.InitiateConsentResponse{}
	actorId := request.GetReq().GetAuth().GetActorId()
	vua := request.GetVua()
	entity := request.GetAaEntity()
	purpose := request.GetConsentRequestPurpose()
	caFlowName := goUtils.Enum(request.GetCaFlowName(), beCaEnumPb.CAFlowName_value, beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT)
	connectionFlowId := request.GetConnectionFlowId()

	if err := validateInitiateConsentRequest(vua, entity, purpose); err != nil {
		logger.Error(ctx, "request is not valid", zap.Error(err), zap.String("vua", vua), zap.Any("aaEntity", entity), zap.Any("consent request purpose", purpose))
		return &feConnectedAccPb.InitiateConsentResponse{Status: rpc.StatusInvalidArgument()}, nil
	}
	// autofill vua if missing
	if vua == "" {
		user, err := s.GetUserByActorId(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error while fetching user by actor id", zap.Error(err), zap.String("aaEntity", actorId))
			return &feConnectedAccPb.InitiateConsentResponse{Status: rpc.StatusInternal()}, nil
		}
		phoneNumber := user.GetProfile().GetPhoneNumber().ToStringNationalNumber()
		vua = fmt.Sprintf(finvuVuaTemplate, phoneNumber)
	}
	var numOfConsentHandlesToGenerate int32 = 1
	reqNumOfConsentHandlesToGenerate := request.GetNumOfConsentHandlesToGenerate()
	if reqNumOfConsentHandlesToGenerate != 0 {
		numOfConsentHandlesToGenerate = reqNumOfConsentHandlesToGenerate
	}
	consentRes, err := s.beConnectedAccClient.StartConsentFlow(ctx, &beCaPb.StartConsentFlowRequest{
		ActorId:                       actorId,
		Vua:                           vua,
		AaEntity:                      getBeAaEntity(entity),
		ConsentRequestPurpose:         getBeConsentRequestPurpose(purpose),
		CaFlowName:                    caFlowName,
		NumOfConsentHandlesToGenerate: numOfConsentHandlesToGenerate,
	})

	// return flow based deeplink in case of rpc error in backend rpc
	if rpcErr := epifigrpc.RPCError(consentRes, err); rpcErr != nil {
		logger.Error(ctx, "error while invoking consent flow", zap.Error(rpcErr))
		res.Status = rpc.StatusInternal()
		consentFailureDl, consentFailureDlErr := s.getConsentFailureCaseDeeplink(ctx, caFlowName, actorId, connectionFlowId)
		if consentFailureDlErr != nil {
			return res, nil
		}
		res.NextAction = consentFailureDl
		return res, nil
	}
	caFlowDlImplementation, implErr := s.caFlowDLFactory.GetCAFlowDeeplinkProcessor(caFlowName)
	if implErr != nil {
		logger.Error(ctx, "error while invoking consent flow", zap.Error(implErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	caFlowNextActionDeeplink, err := caFlowDlImplementation.GetDlForPostConsentApproval(ctx, &facModel.CaFlowRequestData{
		ActorId:           actorId,
		ConsentHandleList: consentRes.GetConsentHandleList(),
		ConnectionFlowId:  request.GetConnectionFlowId(),
	})
	if err != nil {
		errMsg := fmt.Sprintf("Deeplink for %s with state post consent approval can't be generated", caFlowName.String())
		logger.Error(ctx, errMsg, zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Status = rpc.StatusOk()
	res.ConsentHandle = consentRes.GetConsentHandleList()[0]
	res.ConsentHandleList = consentRes.GetConsentHandleList()
	res.NextAction = caFlowNextActionDeeplink
	return res, nil
}

func (s *Service) FetchConsentParams(ctx context.Context, request *feConnectedAccPb.FetchConsentParamsRequest) (*feConnectedAccPb.FetchConsentParamsResponse, error) {
	logger.Info(ctx, "received FetchConsentParams request", zap.String(logger.ACTOR_ID, request.GetReq().GetAuth().GetActorId()))
	paramsRes, err := s.beConnectedAccClient.GetConsentParams(ctx, &beCaPb.GetConsentParamsRequest{})
	if epifigrpc.RPCError(paramsRes, err) != nil {
		logger.Error(ctx, "error while fetching params from BE", zap.Error(err))
		return &feConnectedAccPb.FetchConsentParamsResponse{
			Status:     rpc.StatusInternal(),
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	return &feConnectedAccPb.FetchConsentParamsResponse{
		Status:      rpc.StatusOk(),
		Consent:     convertToFeConsent(paramsRes),
		ConsentMeta: convertToFeConsentMeta(paramsRes),
		RespHeader:  &header.ResponseHeader{Status: rpc.StatusOk()},
		ConsentParams: map[string]string{
			consentParamsPurposeTitle: paramsRes.GetPurpose(),
			consentParamsValidityTitle: parseDisplayTextRange(paramsRes.GetDataRangeFrom().AsTime(),
				paramsRes.GetDataRangeTo().AsTime()),
			consentParamsDetailsFetchTitle:   consentParamsDetailsFetched,
			consentParamsDataLifeTitle:       parseDisplayTextDataLife(paramsRes.GetDataLife()),
			consentParamsFetchFrequencyTitle: parseDisplayTextFrequency(paramsRes.GetFrequency()),
		},
		AaEntityConsentBottomInfoText: s.getAaEntityConsentBottomText(request.GetAaEntity()),
		ConsentDetailsHtmlString:      getConsentValidityDurationText(paramsRes),
	}, nil
}

func (s *Service) GetLinkedAaAccounts(ctx context.Context, request *feConnectedAccPb.GetLinkedAaAccountsRequest) (*feConnectedAccPb.GetLinkedAaAccountsResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	accountsRes, accountsErr := s.beConnectedAccClient.GetLinkedAaAccounts(ctx, &beCaPb.GetLinkedAaAccountsRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(accountsRes, accountsErr); rpcErr != nil {
		logger.Error(ctx, "error while fetching accounts for an actor from be", zap.Error(rpcErr))
		if accountsRes.GetStatus().IsRecordNotFound() {
			return &feConnectedAccPb.GetLinkedAaAccountsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &feConnectedAccPb.GetLinkedAaAccountsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	accounts, err := s.convertToFeAccounts(accountsRes.GetAccounts())
	if err != nil {
		logger.Error(ctx, "error converting to Fe accounts", zap.Error(err))
		return &feConnectedAccPb.GetLinkedAaAccountsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &feConnectedAccPb.GetLinkedAaAccountsResponse{
		Status:   rpc.StatusOk(),
		Accounts: accounts,
	}, nil
}

func (s *Service) GetAccountDetails(ctx context.Context, req *feConnectedAccPb.GetAccountDetailsRequest) (*feConnectedAccPb.GetAccountDetailsResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	accountRes, accountErr := s.beConnectedAccClient.GetAccountDetails(ctx, &beCaPb.GetAccountDetailsRequest{
		ActorId:                actorId,
		AccountId:              req.GetAccountId(),
		AccountDetailsMaskList: []beCaExtPb.AccountDetailsMask{beCaExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY},
	})
	if rpcErr := epifigrpc.RPCError(accountRes, accountErr); rpcErr != nil {
		if accountRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "account details not found for account id", zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
			return &feConnectedAccPb.GetAccountDetailsResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "error getting account details for account id", zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
		return &feConnectedAccPb.GetAccountDetailsResponse{Status: rpc.StatusInternal()}, nil
	}
	accDetail, err := s.convertToFeAccountDetail(accountRes)
	if err != nil {
		logger.Error(ctx, "error parsing be account details to fe account details for account id",
			zap.String(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &feConnectedAccPb.GetAccountDetailsResponse{Status: rpc.StatusInternal()}, nil
	}
	accActions, err := s.getAccountActions(ctx, accountRes.GetAccountDetails())
	if err != nil {
		logger.Error(ctx, "error getting account actions for account id",
			zap.String(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &feConnectedAccPb.GetAccountDetailsResponse{Status: rpc.StatusInternal()}, nil
	}

	consentRenewalPopup, consentRenewalBanner, err := s.getConsentRenewalPopupAndBanner(ctx, req.GetReq().GetAuth().GetActorId(), req.GetAccountId())
	if err != nil {
		// suppressing the error since account details are fetched in default flow
		logger.Error(ctx, "failed to get consent renewal popup and banner",
			zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
	}

	return &feConnectedAccPb.GetAccountDetailsResponse{
		Status:                  rpc.StatusOk(),
		AccountDetail:           accDetail,
		AccountActionOptionList: accActions,
		ConsentRenewalPopup:     consentRenewalPopup,
		ActionBanner:            consentRenewalBanner,
	}, nil
}

func (s *Service) GetConnectedAccounts(ctx context.Context, request *feConnectedAccPb.GetConnectedAccountsRequest) (*feConnectedAccPb.GetConnectedAccountsResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	beAccStatusList := getBeAccountStatusList(request.GetAccountStatusList())
	accountsRes, accountsErr := s.beConnectedAccClient.GetAccounts(ctx, &beCaPb.GetAccountsRequest{
		ActorId: actorId, AccountFilterList: beAccStatusList})
	if rpcErr := epifigrpc.RPCError(accountsRes, accountsErr); rpcErr != nil {
		if accountsRes.GetStatus().IsRecordNotFound() {
			// Client expects status ok with empty list in not found case since AA is in separate process
			return &feConnectedAccPb.GetConnectedAccountsResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		logger.Error(ctx, "error while fetching accounts for an actor from be", zap.Error(rpcErr))
		return &feConnectedAccPb.GetConnectedAccountsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	accounts, err := s.convertToFeAccountDetails(accountsRes.GetAccountDetailsList())
	if err != nil {
		logger.Error(ctx, "error converting to Fe accounts", zap.Error(err))
		return &feConnectedAccPb.GetConnectedAccountsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &feConnectedAccPb.GetConnectedAccountsResponse{
		Status:            rpc.StatusOk(),
		AccountDetailList: accounts,
	}, nil
}

func (s *Service) GetRelatedAccountsForDisconnect(ctx context.Context, request *feConnectedAccPb.GetRelatedAccountsForDisconnectRequest) (
	*feConnectedAccPb.GetRelatedAccountsForDisconnectResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	resp, err := s.beConnectedAccClient.GetRelatedAccountsForDisconnect(ctx, &beCaPb.GetRelatedAccountsForDisconnectRequest{
		AccountId: request.GetAccountId(), ActorId: actorId})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "cannot fetch related connected account for disconnect", zap.String(logger.ACCOUNT_ID,
			request.GetAccountId()), zap.Error(err))
		if resp.GetStatus().IsRecordNotFound() {
			return &feConnectedAccPb.GetRelatedAccountsForDisconnectResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		return &feConnectedAccPb.GetRelatedAccountsForDisconnectResponse{Status: rpc.StatusInternal()}, nil
	}
	accList, err := s.convertToFeAccountDetails(resp.GetAccountDetailList())
	if err != nil {
		logger.Error(ctx, "error converting be acc list to fe account list", zap.String(logger.ACCOUNT_ID,
			request.GetAccountId()), zap.Error(err))
		return &feConnectedAccPb.GetRelatedAccountsForDisconnectResponse{Status: rpc.StatusInternal()}, nil
	}
	aaEntityList := s.extractAaEntityListFromAccList(resp.GetAccountDetailList())
	ConfirmBottomSheet := resp.GetConfirmBottomSheet().GetDisconnectAccountConfirmScreenOptions()
	entityMetaDataList, entityMetaDataListErr := s.getEntityMetaDataList(aaEntityList)
	if entityMetaDataListErr != nil || len(entityMetaDataList) == 0 {
		logger.Error(ctx, "error while forming aa entity meta data", zap.Error(err))
		return &feConnectedAccPb.GetRelatedAccountsForDisconnectResponse{Status: rpc.StatusInternal()}, nil
	}
	ConfirmBottomSheet.AaEntityMeta = entityMetaDataList[0]
	return &feConnectedAccPb.GetRelatedAccountsForDisconnectResponse{
		Status:             rpc.StatusOk(),
		AccountDetailList:  accList,
		ConsentHandleList:  resp.GetConsentHandleList(),
		ConsentIdList:      resp.GetConsentIdList(),
		ConfirmBottomSheet: resp.GetConfirmBottomSheet(),
	}, nil
}

func (s *Service) DeleteAccount(ctx context.Context, request *feConnectedAccPb.DeleteAccountRequest) (*feConnectedAccPb.DeleteAccountResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	resp, err := s.beConnectedAccClient.DeleteAccount(ctx, &beCaPb.DeleteAccountRequest{AccountId: request.GetAccountId(), ActorId: actorId})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "cannot delete connected account for user", zap.String(logger.ACCOUNT_ID,
			request.GetAccountId()), zap.Error(err))
		if resp.GetStatus().IsRecordNotFound() {
			return &feConnectedAccPb.DeleteAccountResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		return &feConnectedAccPb.DeleteAccountResponse{Status: rpc.StatusInternal()}, nil
	}
	return &feConnectedAccPb.DeleteAccountResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) CheckReoobe(ctx context.Context, req *feConnectedAccPb.CheckReoobeRequest) (*feConnectedAccPb.CheckReoobeResponse, error) {
	resp, err := s.beConnectedAccClient.CheckReoobe(ctx, &beCaPb.CheckReoobeRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Vua:     req.GetVua(),
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "cannot check reoobe for user", zap.Error(err))
		return &feConnectedAccPb.CheckReoobeResponse{Status: rpc.StatusInternal()}, nil
	}
	accList, err := s.convertToFeAccountDetails(resp.GetAccountDetailList())
	if err != nil {
		logger.Error(ctx, "error converting be acc list to fe account list", zap.Error(err))
		return &feConnectedAccPb.CheckReoobeResponse{Status: rpc.StatusInternal()}, nil
	}
	return &feConnectedAccPb.CheckReoobeResponse{Status: rpc.StatusOk(), AccountDetailList: accList, OldVua: resp.GetOldVua()}, nil
}

func (s *Service) HandleReoobe(ctx context.Context, req *feConnectedAccPb.HandleReoobeRequest) (*feConnectedAccPb.HandleReoobeResponse, error) {
	var accountIdList []string
	for _, acc := range req.GetAccountDetailList() {
		accountIdList = append(accountIdList, acc.GetAccountId())
	}
	resp, err := s.beConnectedAccClient.HandleReoobe(ctx, &beCaPb.HandleReoobeRequest{
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		Vua:           req.GetVua(),
		AccountIdList: accountIdList,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "cannot handle reoobe for user", zap.Error(err))
		return &feConnectedAccPb.HandleReoobeResponse{Status: rpc.StatusInternal()}, nil
	}
	return &feConnectedAccPb.HandleReoobeResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) GetAvailableFips(ctx context.Context, req *feConnectedAccPb.GetAvailableFipsRequest) (*feConnectedAccPb.GetAvailableFipsResponse, error) {
	resp, err := s.beConnectedAccClient.GetAvailableFips(ctx, &beCaPb.GetAvailableFipsRequest{ActorId: req.GetReq().GetAuth().GetActorId(), AppPlatform: req.GetReq().GetAuth().GetDevice().GetPlatform(), AppVersion: req.GetReq().GetAppVersionCode()})

	// in case there is an error in calling the backend method
	if err = epifigrpc.RPCError(resp, err); err != nil {
		// if the error is RecordNotFound return empty list with an Ok Status Response
		if resp.GetStatus().IsRecordNotFound() {
			return &feConnectedAccPb.GetAvailableFipsResponse{
				Status: rpc.StatusOk(),
			}, nil
		}

		logger.Error(ctx, "error fetching list of available FIPs", zap.Error(err))
		return &feConnectedAccPb.GetAvailableFipsResponse{
			Status:      rpc.StatusInternal(),
			FipMetaList: nil,
		}, nil
	}

	// convert the response to a final list including other info
	var finalList []*feConnectedAccPb.FipMeta
	// don't return these banks for older versions
	isOlderVersion := false
	if req.GetReq().GetAuth().GetDevice().GetPlatform() == commontypes.Platform_ANDROID && req.GetReq().GetAppVersionCode() < s.conf.ConnectedAccount().MinVersionCheckForNewBanks() {
		isOlderVersion = true
	}

	// fetch the meta of a bank by calling the GetFipMetaByBank in the pkg file
	// if no error is returned, append them to the list
	// if there is error, return internal error
	for _, respBank := range resp.GetBankList() {
		isNewBank := false
		if isOlderVersion {
			for _, newBank := range newBanksList {
				if respBank == newBank {
					isNewBank = true
					break
				}
			}
		}
		if isNewBank {
			continue
		}
		respMeta, respMetaError := caPkg.GetFipMetaByBank(respBank)
		if respMetaError != nil {
			logger.Error(ctx, "error while fetching bank meta from pkg", zap.Error(respMetaError))
			return &feConnectedAccPb.GetAvailableFipsResponse{
				Status:      rpc.StatusInternal(),
				FipMetaList: nil,
			}, nil
		}

		finalList = append(finalList, &feConnectedAccPb.FipMeta{
			Bank:    respBank,
			Name:    respMeta.Name,
			LogoUrl: respMeta.LogoUrl,
		})
	}

	return &feConnectedAccPb.GetAvailableFipsResponse{
		Status:      rpc.StatusOk(),
		FipMetaList: finalList,
	}, nil
}

func (s *Service) CreateBankPreference(ctx context.Context, req *feConnectedAccPb.CreateBankPreferenceRequest) (*feConnectedAccPb.CreateBankPreferenceResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	bankList := req.GetBankList()

	resp, err := s.beConnectedAccClient.CreateBankPreference(ctx, &beCaPb.CreateBankPreferenceRequest{
		ActorId:  actorId,
		BankList: bankList,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error creating bank preferences", zap.Error(err))
		return &feConnectedAccPb.CreateBankPreferenceResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &feConnectedAccPb.CreateBankPreferenceResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) ResumeAccountSync(ctx context.Context, req *feConnectedAccPb.ResumeAccountSyncRequest) (*feConnectedAccPb.ResumeAccountSyncResponse, error) {
	resp, err := s.beConnectedAccClient.GetConsentsForAccount(ctx, &beCaPb.GetConsentsForAccountRequest{
		AccountId:         req.GetAccountId(),
		ConsentStatusList: []beCaEnumPb.ConsentStatus{beCaEnumPb.ConsentStatus_CONSENT_STATUS_PAUSED},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error getting consents for account", zap.String(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &feConnectedAccPb.ResumeAccountSyncResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	var chList []string
	for _, con := range resp.GetConsentDetailsList() {
		chList = append(chList, con.GetConsentHandle())
	}
	if len(chList) == 0 {
		logger.Error(ctx, "no paused consents for the account", zap.String(logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &feConnectedAccPb.ResumeAccountSyncResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &feConnectedAccPb.ResumeAccountSyncResponse{
		Status:            rpc.StatusOk(),
		ConsentHandleList: chList,
	}, nil
}

// getDataPullFailureOrProgressCaseDeeplink returns deeplink for either data pull failure screen or data pull in progress screen on the basis of current poll count
func (s *Service) getDataPullFailureOrProgressCaseDeeplink(ctx context.Context,
	caFlowName beCaEnumPb.CAFlowName, actorId string, connectionFlowId string) (*deeplinkPb.Deeplink, error) {
	caFlowDlImplementation, implErr := s.caFlowDLFactory.GetCAFlowDeeplinkProcessor(caFlowName)
	if implErr != nil {
		return nil, errors.Wrap(implErr, "CaFlow name factory method not implemented")
	}
	caFlowDeeplink, dlErr := caFlowDlImplementation.GetDlForDataPullFailure(ctx, &facModel.CaFlowRequestData{
		ActorId:          actorId,
		ConnectionFlowId: connectionFlowId,
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "failed to get deeplink for data pull failure case")
	}
	return caFlowDeeplink, nil
}

// getConsentFailureCaseDeeplink returns the deeplink for consent failure screen
func (s *Service) getConsentFailureCaseDeeplink(
	ctx context.Context,
	caFlowName beCaEnumPb.CAFlowName,
	actorId string,
	connectionFlowId string,
) (*deeplinkPb.Deeplink, error) {
	caFlowDlImplementation, implErr := s.caFlowDLFactory.GetCAFlowDeeplinkProcessor(caFlowName)
	if implErr != nil {
		return nil, errors.Wrap(implErr, "CaFlow name factory method not implemented")
	}
	caFlowDeeplink, dlErr := caFlowDlImplementation.GetDlForConsentFailure(ctx, &facModel.CaFlowRequestData{
		ActorId:          actorId,
		ConnectionFlowId: connectionFlowId,
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "failed to get deeplink for consent failure case")
	}
	return caFlowDeeplink, nil
}

// getDataPullSuccessCaseDeeplink returns the deeplink for data pull success screen
func (s *Service) getDataPullSuccessCaseDeeplink(ctx context.Context, caFlowName beCaEnumPb.CAFlowName, actorId string,
	consentHandleList []string, connectionFlowId string) (*deeplinkPb.Deeplink, error) {
	var consentHandleToAccIdsListMap map[string]*beCaPb.GetAccountIdsByConsentHandleListResponse_AccountIdList
	if toFetchAccIdsForConsentHandlesForCaFlow(caFlowName) {
		consentHandleToAccIdsResp, getMapErr := s.beConnectedAccClient.GetAccountIdsByConsentHandleList(ctx, &beCaPb.GetAccountIdsByConsentHandleListRequest{
			ConsentHandleList: consentHandleList,
		})
		if rpcErr := epifigrpc.RPCError(consentHandleToAccIdsResp, getMapErr); rpcErr != nil {
			return nil, errors.Wrap(rpcErr, "error fetching consent handle to acc ids map")
		}
		consentHandleToAccIdsListMap = consentHandleToAccIdsResp.GetConsentHandleToAccountIdsMap()
	}
	caFlowDlImplementation, implErr := s.caFlowDLFactory.GetCAFlowDeeplinkProcessor(caFlowName)
	if implErr != nil {
		return nil, errors.Wrap(implErr, "CaFlow name factory method not implemented")
	}
	caFlowDeeplink, dlErr := caFlowDlImplementation.GetDlForDataPullSuccess(ctx, &facModel.CaFlowRequestData{
		ActorId:                      actorId,
		ConsentHandleList:            consentHandleList,
		ConsentHandleToAccIdsListMap: consentHandleToAccIdsListMap,
		ConnectionFlowId:             connectionFlowId,
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "get deeplink for data pull success case failed")
	}
	return caFlowDeeplink, nil
}

// toFetchAccIdsForConsentHandlesForCaFlow checks for a given ca flow name whether to allow fetching acc ids map for consent handles
func toFetchAccIdsForConsentHandlesForCaFlow(caFlowName beCaEnumPb.CAFlowName) bool {
	return caFlowName == beCaEnumPb.CAFlowName_CA_FLOW_NAME_AA_SALARY
}

// TODO(rajEpiFi) introduce DL implementation for unexpected internal error failures instead of just returning internal error

// nolint:dupl
func (s *Service) GetDataPullStatusFromConsentHandle(ctx context.Context, request *feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest) (*feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse, error) {
	// current_poll_count represents what is the current polling count of data pull status.
	// this field initial value is 0, is checked against MaxDataPullStatusPollAttempts(which is 10) in each poll
	currentPollCount := request.GetCurrentPollCount()
	consentHandleList := request.GetConsentHandleList()
	if consentHandleList == nil {
		consentHandleList = []string{request.GetConsentHandle()}
	}
	var actorId string
	var caFlowName beCaEnumPb.CAFlowName
	for _, consentHandle := range consentHandleList {
		req := &beCaPb.GetDataFetchAttemptsRequest{
			PageContext: &rpc.PageContextRequest{
				PageSize: 1,
			},
			Filter: &beCaPb.GetDataFetchAttemptsRequest_ConsentHandle{
				ConsentHandle: consentHandle,
			},
		}
		resp, err := s.beConnectedAccClient.GetDataFetchAttempts(ctx, req)
		// return internal error if response Status is different from OK
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			logger.Error(ctx, "error getting data fetch attempts", zap.Error(rpcErr))
			return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		consentRequest := resp.GetConsentRequest()
		// Assuming this rpc gets invoked for unique combination of (actorId + ca flow), set actorId and caFlowName once
		if actorId == "" {
			actorId = consentRequest.GetActorId()
		}
		if caFlowName == beCaEnumPb.CAFlowName_CA_FLOW_NAME_UNSPECIFIED {
			caFlowName = consentRequest.GetCaFlowName()
		}
		// If Consent handle is itself Failed/Rejected then return consent failure in response
		if consentRequest.GetConsentHandleStatus() == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_FAILED ||
			consentRequest.GetConsentHandleStatus() == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_REJECTED {
			caFlowDeeplinkRes, err := s.getConsentFailureCaseDeeplink(ctx, consentRequest.GetCaFlowName(), consentRequest.GetActorId(), request.GetConnectionFlowId())
			if err != nil {
				logger.Error(ctx, "failure in getting failure case deeplink", zap.Error(err))
				return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status:             rpc.StatusOk(),
				DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_FAILED,
				NextPollSeconds:    -1,
				MaxAllowedAttempts: s.conf.ConnectedAccount().MaxDataPullStatusPollAttempts(),
				Deeplink:           caFlowDeeplinkRes,
			}, nil
		}
		dfaList := resp.GetDataFetchAttemptDetailsList()
		var latestDfaDetail *beCaExtPb.DataFetchAttemptDetails
		var latestDataPullStatus feConnectedAccPb.DataPullStatus
		if len(dfaList) != 0 {
			latestDfaDetail = dfaList[0]
			latestDataPullStatus = convertToDataPullStatus(latestDfaDetail.GetFetchStatus())
		}
		// Continue polling logic if Consent handle status is still pending or data fetch attempt list is nil
		if consentRequest.GetConsentHandleStatus() == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING ||
			dfaList == nil || len(dfaList) == 0 || latestDataPullStatus == feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_IN_PROGRESS {
			// if currentPollCount value exceeds MaxDataPullStatusPollAttempts (i.e. this is the 11th poll which is the last poll as MaxDataPullStatusPollAttempts value is configured as 10)
			// then return DataPullStatus as Failed and NextPollSeconds as -1 so that client stops polling
			if currentPollCount > s.conf.ConnectedAccount().MaxDataPullStatusPollAttempts() {
				caFlowDeeplink, err := s.getDataPullFailureOrProgressCaseDeeplink(ctx, consentRequest.GetCaFlowName(), consentRequest.GetActorId(), request.GetConnectionFlowId())
				if err != nil {
					logger.Error(ctx, "failure in getting data pull deeplink", zap.Error(err))
					return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
						Status: rpc.StatusInternal(),
					}, nil
				}
				return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
					Status:             rpc.StatusOk(),
					DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_FAILED,
					NextPollSeconds:    -1,
					MaxAllowedAttempts: s.conf.ConnectedAccount().MaxDataPullStatusPollAttempts(),
					Deeplink:           caFlowDeeplink,
				}, nil
			}
			// if currentPollCount value does not exceed MaxDataPullStatusPollAttempts, set DataPullStatus as IN_PROGRESS and increase the CurrentPollCount
			return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status:             rpc.StatusOk(),
				DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_IN_PROGRESS,
				NextPollSeconds:    s.conf.ConnectedAccount().DataPullStatusNextPoll(),
				MaxAllowedAttempts: s.conf.ConnectedAccount().MaxDataPullStatusPollAttempts(),
				CurrentPollCount:   currentPollCount + 1,
			}, nil
		}

		// if data pull status has failed, client should stop polling. hence return NextPollSeconds = -1
		if latestDataPullStatus == feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_FAILED {
			caFlowDeeplinkDataPullFailureRes, caFlowDeeplinkDataPullDlErr := s.getDataPullFailureOrProgressCaseDeeplink(ctx, consentRequest.GetCaFlowName(), consentRequest.GetActorId(), request.GetConnectionFlowId())
			if caFlowDeeplinkDataPullDlErr != nil {
				logger.Error(ctx, "failure in getting data pull deeplink in case when data pull status is in progress", zap.Error(caFlowDeeplinkDataPullDlErr))
				return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
			return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status:             rpc.StatusOk(),
				DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_FAILED,
				NextPollSeconds:    -1,
				MaxAllowedAttempts: s.conf.ConnectedAccount().MaxDataPullStatusPollAttempts(),
				Deeplink:           caFlowDeeplinkDataPullFailureRes,
			}, nil
		}

		// if data pull status has succeeded, client should stop polling. hence return NextPollSeconds = -1
		if latestDataPullStatus == feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_SUCCESSFUL {
			continue
		}
	}
	// If rpc did not return while looping over the consent handles, this implies all the consent handles were successful
	// Return Success case deeplink in this case
	caFlowDeeplinkSuccessRes, caFlowSuccessDlErr := s.getDataPullSuccessCaseDeeplink(ctx,
		caFlowName, actorId, consentHandleList, request.GetConnectionFlowId())
	if caFlowSuccessDlErr != nil {
		logger.Error(ctx, "failure in getting data pull success deeplink", zap.Error(caFlowSuccessDlErr))
		return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
		Status:             rpc.StatusOk(),
		DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_SUCCESSFUL,
		NextPollSeconds:    -1,
		MaxAllowedAttempts: s.conf.ConnectedAccount().MaxDataPullStatusPollAttempts(),
		Deeplink:           caFlowDeeplinkSuccessRes,
	}, nil
}

func (s *Service) GetAllowedConfig(ctx context.Context, req *feConnectedAccPb.GetAllowedConfigRequest) (*feConnectedAccPb.GetAllowedConfigResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	caFlow := goUtils.Enum(req.GetCaFlowName(), beCaEnumPb.CAFlowName_value, beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT)

	goroutine.Run(ctx, goroutine.DefaultTimeout, func(ctx context.Context) {
		s.eventBroker.AddToBatch(
			epificontext.WithEventAttributes(ctx),
			tieringEvents.NewClickedConnectButtonAABE(
				actorId, caFlow.String()))
	})

	resp, err := s.beConnectedAccClient.GetAllowedConfig(ctx, &beCaPb.GetAllowedConfigRequest{
		ActorId: actorId, AppPlatform: req.GetReq().GetAuth().GetDevice().GetPlatform(), AppVersion: req.GetReq().GetAppVersionCode(), CaFlowName: caFlow})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error getting config from backend for actor", zap.Error(err))
		return &feConnectedAccPb.GetAllowedConfigResponse{Status: rpc.StatusInternal()}, nil
	}

	beFipMetaList := resp.GetAllowedFipList()

	fipMetaIndexMap := make(map[string]int)
	for i, fipMeta := range beFipMetaList {
		fipMetaIndexMap[fipMeta.GetFipId()] = i
	}

	var allowedFipMetaList []*beCaExtPb.FipConfig
	if len(req.GetFipIdList()) == 0 {
		allowedFipMetaList = beFipMetaList
	} else {
		for _, fipId := range req.GetFipIdList() {
			fipMeta := beFipMetaList[fipMetaIndexMap[fipId]]
			allowedFipMetaList = append(allowedFipMetaList, fipMeta)
		}
	}

	isFetchPermittedFipConfigV2Allowed, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(
		types.Feature_AA_PERMITTED_FIP_CONFIG_V2).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error checking if get permitted fip config feature is enabled, considering V1 flow for permittedFip", zap.Any(logger.ACTOR_ID_V2, actorId))
	}

	var fipMetaList []*feConnectedAccPb.FipMeta
	fipAccDiscoveryIdentifiers := make(map[string]*feConnectedAccPb.AccountDiscoveryIdentifiers, 0)
	groupedMetaData := make(map[string][]*feConnectedAccPb.FipMeta)
	for _, fip := range allowedFipMetaList {
		var fiTypeMetaList []*feConnectedAccPb.FiTypeMeta
		if isFetchPermittedFipConfigV2Allowed {
			fiTypeMetaList = getFeFiTypeMetaV2(fip.GetFiTypeConfigList(), fip.GetFipId())
		} else {
			fiTypeMetaList = getFeFiTypeMeta(fip.GetFiTypeConfigList(), req.GetCaFlowName(), fip.GetFipId())
		}
		if len(fiTypeMetaList) == 0 {
			continue
		}
		fipMeta := &feConnectedAccPb.FipMeta{
			Bank:                         fip.GetBank(),
			Name:                         fip.GetName(),
			LogoUrl:                      fip.GetLogoUrl(),
			FipId:                        fip.GetFipId(),
			OtpLength:                    fip.GetOtpLength(),
			OtpPattern:                   fip.GetOtpPattern(),
			FiTypeMetaList:               fiTypeMetaList,
			BankSmsHeader:                fip.GetBankSmsHeader(),
			ShowSkipButtonTimeout:        fip.GetShowSkipButtonTimeout(),
			IsAccountLevelConsentEnabled: fip.GetIsAccountLevelConsentEnabled(),
		}
		fipMetaList = append(fipMetaList, fipMeta)
		accDiscoveryIdentifiers, accDiscoveryIdentifiersErr := s.getAccDiscoveryIdentifiers(ctx, actorId, fip.GetFiTypeConfigList())
		if accDiscoveryIdentifiersErr != nil {
			logger.Error(ctx, "error getting account discovery identifiers", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return &feConnectedAccPb.GetAllowedConfigResponse{Status: rpc.StatusInternal()}, nil
		}
		if len(accDiscoveryIdentifiers.GetAccDiscoveryIdentifiers()) != 0 {
			fipAccDiscoveryIdentifiers[fip.GetFipId()] = accDiscoveryIdentifiers
		}
		groupedMetaData[fip.GetFipAccountType()] = append(groupedMetaData[fip.GetFipAccountType()], fipMeta)
	}
	entityMetaDataList, entityMetaDataListErr := s.getEntityMetaDataList(req.GetAaEntityList())
	if entityMetaDataListErr != nil {
		logger.Error(ctx, "error while forming aa entity meta data list", zap.Error(err))
		return &feConnectedAccPb.GetAllowedConfigResponse{Status: rpc.StatusInternal()}, nil
	}
	discoveryTitleText, discoverySubtitleSearchingText, discoverySubtitleText, discoveryLoadingText, cantSeeAccountText, discoveryCtaText := s.getFlowBasedDiscoveryScreenParams(ctx, caFlow)
	noAccDiscoveredTitleText, noAccDiscoveredSubTitleText, noAccDiscoveredFipIconText, fipListNotDiscoveryText, noAccDiscoveredProceedDeeplinkText := s.getFlowBasedNoAccDiscoverScreenParams(ctx, caFlow)
	sdkFlowUiParams := s.getSdkFlowUiParams(ctx, caFlow, resp.GetPhoneNumber())
	groupedFipMetaData := s.getGroupedFipMetaDataByTypes(groupedMetaData)

	autoReadTimeoutAllDiscoveredFipsOtp := s.conf.ConnectedAccount().AutoReadTimeoutAllDiscoveredFipsOtp()
	if req.GetCaFlowName() == beCaEnumPb.CAFlowName_CA_FLOW_NAME_NET_WORTH_IND_STOCKS.String() {
		autoReadTimeoutAllDiscoveredFipsOtp = s.conf.ConnectedAccount().AutoReadTimeoutForIndStocksDiscoveredFipsOtp()
	}

	return &feConnectedAccPb.GetAllowedConfigResponse{
		Status:                                 rpc.StatusOk(),
		FipMetaList:                            fipMetaList,
		AccountDiscoveryTitleText:              s.conf.ConnectedAccount().AccountDiscoveryTitleText(),
		AccountDiscoverySubtitleText:           formatTextWithMobileNumber(s.conf.ConnectedAccount().AccountDiscoverySubtitleText(), resp.GetPhoneNumber()),
		FinvuAccountDiscoveryTimeoutSeconds:    s.conf.ConnectedAccount().FinvuAccountDiscoveryTimeoutSeconds(),
		OnemoneyAccountDiscoveryTimeoutSeconds: s.conf.ConnectedAccount().OnemoneyAccountDiscoveryTimeoutSeconds(),
		V2FlowParams: &feConnectedAccPb.V2FlowParams{
			AccountDiscoveryTitleText:             discoveryTitleText,
			AccountDiscoverySubtitleText:          formatTextWithMobileNumber(discoverySubtitleText, resp.GetPhoneNumber()),
			CtaText:                               discoveryCtaText,
			AccountDiscoverySubtitleSearchingText: formatTextWithMobileNumber(discoverySubtitleSearchingText, resp.GetPhoneNumber()),
			AccountDiscoveryLoadingText:           discoveryLoadingText,
			CantSeeYourAccountsText:               cantSeeAccountText,
		},
		UseFinvuAsyncDiscovery: s.isAsyncDiscoveryEnabled(req.GetReq().GetAuth().GetDevice().GetPlatform(), req.GetReq().GetAppVersionCode()),
		NoAccountsDiscoveredTextParams: &feConnectedAccPb.NoAccountsDiscoveredTextParams{
			TitleText:           noAccDiscoveredTitleText,
			SubTitleText:        formatTextWithMobileNumber(noAccDiscoveredSubTitleText, resp.GetPhoneNumber()),
			FipIcon:             noAccDiscoveredFipIconText,
			FipNotDiscoveryText: fipListNotDiscoveryText,
			ProceedDeeplinkText: noAccDiscoveredProceedDeeplinkText,
		},
		AccountsDiscoveryIdentifiers:        fipAccDiscoveryIdentifiers,
		AaEntityMetaList:                    entityMetaDataList,
		SdkFlowParams:                       sdkFlowUiParams,
		GroupedFipMetaDataByTypes:           groupedFipMetaData,
		AutoReadTimeoutAllDiscoveredFipsOtp: autoReadTimeoutAllDiscoveredFipsOtp,
	}, nil
}

func (s *Service) GetHomeSummary(ctx context.Context, req *feConnectedAccPb.GetHomeSummaryRequest) (
	*feConnectedAccPb.GetHomeSummaryResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	getAllowedFiTypeToDisplayOnHomeSummary, errGetFiTypes := s.getAllowedFiTypeForHomeSummary(ctx, actorId)
	if errGetFiTypes != nil {
		logger.Error(ctx, fmt.Sprintf("Error finding which fi types are enabled on home for actor: %v, err: %v", actorId, errGetFiTypes))
		return &feConnectedAccPb.GetHomeSummaryResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("Error finding which fi types are enabled for home summary for actor: %v, err: %v", actorId, errGetFiTypes)),
			},
		}, nil
	}
	getFeatResp, errGetFeat := s.onbClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getFeatResp, errGetFeat); grpcErr != nil {
		logger.Error(ctx, "failed to get feature details from onboarding", zap.Error(grpcErr))
		return &feConnectedAccPb.GetHomeSummaryResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(grpcErr.Error()),
			},
		}, nil
	}
	isFiLiteUser := getFeatResp.GetIsFiLiteUser()
	homeTileList, err := s.getHomeAccountTiles(ctx, actorId, isFiLiteUser, getAllowedFiTypeToDisplayOnHomeSummary, req.GetReq())
	if err != nil {
		if errors.Is(err, caErrors.ErrNoAccountAccessLevel) {
			logger.Error(ctx, "account has no access level", zap.Error(err))
			return &feConnectedAccPb.GetHomeSummaryResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusPermissionDeniedWithDebugMsg(err.Error())},
			}, nil
		}
		logger.Error(ctx, "error fetching home account tiles", zap.Error(err))
		return &feConnectedAccPb.GetHomeSummaryResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	bal, err := getCollectiveBalance(homeTileList)
	if err != nil {
		logger.Error(ctx, "error getting collective balance", zap.Error(err))
		return &feConnectedAccPb.GetHomeSummaryResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	fabIcon := s.deeplinkBuilder.GetTalkToAIFloatingIconWithDeeplink(ctx, req.GetReq().GetAuth().GetActorId(), networth.Entrypoint_ENTRYPOINT_BANK_ACCOUNT_PAGE)
	defaultResponse := &feConnectedAccPb.GetHomeSummaryResponse{
		RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
		CollectiveBalance:    bal,
		HomeAccountTileList:  homeTileList,
		FloatingActionButton: fabIcon,
	}

	checkEligibilityResp, err := s.beConnectedAccClient.CheckEligibilityForConsentRenewal(ctx, &beCaPb.CheckEligibilityForConsentRenewalRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(checkEligibilityResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to check eligibility for consent renewal", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return defaultResponse, nil
	}

	if !checkEligibilityResp.GetEligible() {
		return defaultResponse, nil
	}

	// prepare consent renewal entrypoint
	logger.Debug(ctx, "actor eligible for renewal", zap.String(logger.ACTOR_ID_V2, actorId))

	getAccountsResponse, getAccountsErr := s.beConnectedAccClient.GetAccountsForRenewal(ctx, &beCaPb.GetAccountsForRenewalRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(getAccountsResponse, getAccountsErr); err != nil {
		logger.Error(ctx, "error in fetching accounts for renewal", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return defaultResponse, nil
	}
	renewalAccountsList := getAccountsResponse.GetRenewalAccountDetailsList()

	// no accounts found eligible for renewal. Most likely in the case if the user has already renewed.
	// The eligibility check would be passed since the segmentation data is 1 day stale. If the user has already
	// renewed, eligibility would still be true from segmentation service for the rest of the day
	if len(renewalAccountsList) == 0 {
		logger.Debug(ctx, "no accounts found for renewal", zap.String(logger.ACTOR_ID_V2, actorId))
		return defaultResponse, nil
	}

	// map to store eligible renewal accounts and its consent expiry time
	accountIdMap := make(map[string]time.Time)
	minConsentExpiry := datetime.TIME_MAX
	for _, account := range renewalAccountsList {
		accountIdMap[account.GetAccountDetails().GetAccountId()] = account.GetConsentExpiry().AsTime()
		minConsentExpiry = datetime.Min(minConsentExpiry, account.GetConsentExpiry().AsTime())
	}

	// replace account tile deeplink to renewal deeplink if the account is eligible for renewal
	// modify tile tag for expiring or expired
	for _, homeTile := range homeTileList {
		accountId := homeTile.GetAccountId()
		fipId := homeTile.GetFipId()
		if consentExpiry, ok := accountIdMap[accountId]; ok {
			tileTag := getTileTagForBalanceDashboardRenewal(consentExpiry)
			// fip specific consent renewal
			initiateConsentRenewalDeeplink, err := GetDeeplinkToInitiateConsentRenewal(fipId)
			if err != nil {
				logger.Error(ctx, "error in fetching deeplink for initiating renewal", zap.String(logger.FIP_ID, fipId), zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				// ignore an account tile in case of error and process th next tile
				continue
			}
			homeTile.DataSyncDisplayTag = tileTag
			homeTile.DeeplinkV2 = initiateConsentRenewalDeeplink
		}
	}

	// do consent renewal for fip which is expiring the earliest
	renewalCta, renewalCtaErr := getRenewalCtaForConsentRenewalBalanceDashboardEntrypoint()
	if renewalCtaErr != nil {
		logger.Error(ctx, "error in returning renewal cta in balance dashboard", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(renewalCtaErr))
	}

	// change color of collective balance to highlight renewal
	if renewalCta != nil {
		bal.BalanceText.FontColor = CollectiveBalanceConsentRenewalFontColor
	}

	popupOptions, popUpErr := createPopupForConsentRenewalBalanceDashboardEntrypoint(minConsentExpiry, s.conf.ConnectedAccount().BalanceDashboardRenewalPopupDismissDuration())
	if popUpErr != nil {
		logger.Error(ctx, "error in generating popup for consent renewal in balance dashboard", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(popUpErr))
	}

	consentRenewalPopup, popUpErr := createConsentRenewalPopUp(minConsentExpiry, s.conf.ConnectedAccount().BalanceDashboardRenewalPopupDismissDuration(), BalanceDashboardRenewalPopupTitleText)
	if popUpErr != nil {
		logger.Error(ctx, "error in generating popup for consent renewal in balance dashboard", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(popUpErr))
	}

	return &feConnectedAccPb.GetHomeSummaryResponse{
		RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
		CollectiveBalance:    bal,
		HomeAccountTileList:  homeTileList,
		Popup:                popupOptions,
		RenewalCta:           renewalCta,
		ConsentRenewalPopup:  consentRenewalPopup,
		FloatingActionButton: fabIcon,
	}, nil
}

func (s *Service) GetRelatedAccountsForDelete(ctx context.Context, req *feConnectedAccPb.GetRelatedAccountsForDeleteRequest) (
	*feConnectedAccPb.GetRelatedAccountsForDeleteResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	beResp, beErr := s.beConnectedAccClient.GetRelatedAccountsForDelete(ctx,
		&beCaPb.GetRelatedAccountsForDeleteRequest{AccountId: req.GetAccountId(), ActorId: actorId})
	if beErr = epifigrpc.RPCError(beResp, beErr); beErr != nil {
		logger.Error(ctx, "error getting related accounts for delete from be", zap.String(
			logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(beErr))
		return &feConnectedAccPb.GetRelatedAccountsForDeleteResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	accList, err := s.convertToFeAccountDetails(beResp.GetAccountDetailList())
	if err != nil {
		logger.Error(ctx, "error converting be acc list to fe account list", zap.String(
			logger.ACCOUNT_ID, req.GetAccountId()), zap.Error(err))
		return &feConnectedAccPb.GetRelatedAccountsForDeleteResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	return &feConnectedAccPb.GetRelatedAccountsForDeleteResponse{
		RespHeader:         &header.ResponseHeader{Status: rpc.StatusOk()},
		AccountDetailList:  accList,
		ConfirmBottomSheet: beResp.GetConfirmBottomSheet(),
	}, nil
}

func (s *Service) GetLandingPageOnConnect(ctx context.Context, req *feConnectedAccPb.GetLandingPageOnConnectRequest) (
	*feConnectedAccPb.GetLandingPageOnConnectResponse, error) {
	newDl, oldDl, wealthDepErr := s.getDeeplinkForConnectedAccounts(ctx, req.GetReq(), req.GetCaFlowName(), req.GetCaFlowId())
	if wealthDepErr != nil {
		if errors.Is(wealthDepErr, epifierrors.ErrPermissionDenied) {
			return &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusPermissionDenied()},
			}, nil
		}
		logger.Error(ctx, "error in getting deeplink for connected accounts", zap.Error(wealthDepErr))
		return &feConnectedAccPb.GetLandingPageOnConnectResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	return &feConnectedAccPb.GetLandingPageOnConnectResponse{
		RespHeader:  &header.ResponseHeader{Status: rpc.StatusOk()},
		Deeplink:    oldDl,
		NewDeeplink: newDl,
	}, nil
}

func (s *Service) getParamsToStartSDK(
	ctx context.Context,
	usr *beUserPb.User,
	appPlatform commontypes.Platform,
	appVersion uint32,
	actorId string,
	caFlowName beCaEnumPb.CAFlowName,
	aaEntity beCaEnumPb.AaEntity,
	caFlowId string,
	fipIds []string,
) (*deeplinkPb.ConnectedAccountsOptions, error) {
	useV2Flow, v2FlowFlagErr := s.isV2FlowEnabled(ctx, usr, appPlatform, appVersion)
	if v2FlowFlagErr != nil {
		return nil, errors.Wrap(v2FlowFlagErr, "error determining flag for v2 flow")
	}

	dpAaEntity := s.convertToDeeplinkAaEntity(convertToFeAaEntity(aaEntity))
	useTokenAuth, useTokenAuthErr := s.isTokenAuthEnabled(ctx, actorId, aaEntity)
	logger.Info(ctx, "token auth flag", zap.Any("tokenAuthFlag", useTokenAuth))

	if useTokenAuthErr != nil {
		return nil, errors.Wrap(useTokenAuthErr, "error determining flag for using token auth")
	}
	return &deeplinkPb.ConnectedAccountsOptions{
		MobileNumber: usr.GetProfile().GetPhoneNumber(),
		// passing pan name as profile name is deprecated
		Name:                   usr.GetProfile().GetPanName().ToString(),
		AaEntity:               dpAaEntity,
		FiuId:                  s.conf.ConnectedAccount().FiuId(),
		AaTncMessage:           s.getTncMessageFromEntity(dpAaEntity),
		UseV2Flow:              useV2Flow,
		UseTokenAuthentication: useTokenAuth,
		CaFlowName:             caFlowName.String(),
		CaFlowId:               caFlowId,
		FipList:                fipIds,
		Version:                s.GetConnectedAccountSDKVersion(ctx, actorId, useV2Flow, appPlatform, appVersion, caFlowName),
	}, nil
}

func (s *Service) getParamsToSDKScreenOptions(ctx context.Context, usr *beUserPb.User, appPlatform commontypes.Platform, appVersion uint32, actorId string, caFlowName beCaEnumPb.CAFlowName) (*feConnectedAccountCommonPb.AaSdkLoginOptions, error) {
	resp, err := s.beConnectedAccClient.GetAaEntityForConnect(ctx, &beCaPb.GetAaEntityForConnectRequest{
		ActorId:     actorId,
		AppVersion:  appVersion,
		AppPlatform: appPlatform,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return nil, errors.Wrap(rpcErr, "error while getting aa entity from caClient")
	}

	useV2Flow, v2FlowFlagErr := s.isV2FlowEnabled(ctx, usr, appPlatform, appVersion)
	if v2FlowFlagErr != nil {
		return nil, errors.Wrap(v2FlowFlagErr, "error determining flag for v2 flow")
	}

	dpAaEntity := getFrontendEnumForAaEntity(resp.GetAaEntity())

	useTokenAuth, useTokenAuthErr := s.isTokenAuthEnabled(ctx, actorId, resp.GetAaEntity())
	logger.Info(ctx, "token auth flag", zap.Any("tokenAuthFlag", useTokenAuth))

	if useTokenAuthErr != nil {
		return nil, errors.Wrap(useTokenAuthErr, "error determining flag for using token auth")
	}
	return &feConnectedAccountCommonPb.AaSdkLoginOptions{
		MobileNumber:           usr.GetProfile().GetPhoneNumber(),
		Name:                   usr.GetProfile().GetPanName().ToString(),
		AaEntity:               dpAaEntity,
		FiuId:                  s.conf.ConnectedAccount().FiuId(),
		AaTncMessage:           s.getTncMessageFromAaEntity(dpAaEntity),
		UseV2Flow:              useV2Flow,
		UseTokenAuthentication: useTokenAuth,
		CaFlowName:             caFlowName.String(),
	}, nil
}

func (s *Service) getDeeplinkToStartSDK(
	ctx context.Context,
	userRes *beUserPb.User,
	requestHeader *header.RequestHeader,
	aaEntity beCaEnumPb.AaEntity,
	caFlowName string,
	caFlowId string,
	fipIds []string,
) (*deeplinkPb.Deeplink, error) {
	actorId := requestHeader.GetAuth().GetActorId()
	appVersion := requestHeader.GetAppVersionCode()
	appPlatform := requestHeader.GetAuth().GetDevice().GetPlatform()

	// CaFlowName tells the client which screen to land after AA third-party SDK invocation is completed.
	// Client simply passes this enum to InitiateConsent request, and it returns the screen details based on the enum value
	getCaFlowNameEnum := goUtils.Enum(caFlowName, beCaEnumPb.CAFlowName_value, beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT)
	sdkLoginOptions, err := s.getParamsToStartSDK(ctx, userRes,
		appPlatform, appVersion, actorId, getCaFlowNameEnum, aaEntity, caFlowId, fipIds)
	if err != nil {
		return nil, err
	}
	sdkDl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CONNECTED_ACCOUNTS_SDK,
		ScreenOptions: &deeplinkPb.Deeplink_ConnectedAccountsOptions{
			ConnectedAccountsOptions: sdkLoginOptions,
		},
	}
	// For fi lite users who start CA onboarding without PAN and DOB, we need to collect their pan, dob and
	// wealth TnC consent by compliance
	if userRes.GetProfile().GetPAN() == "" && s.isPanRequiredFlow(ctx, actorId, getCaFlowNameEnum) {
		isWealthBuilderPanFormEnabled, evalErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM).WithActorId(actorId))
		if evalErr != nil {
			return nil, fmt.Errorf("error in evaluating wealth builder pan collection form release config: %w", evalErr)
		}

		switch {
		case isWealthBuilderPanFormEnabled:
			// pan collection form will be shown if no verified pan is available irrespective of if there is some unverified pan available
			// this allows user to update their pan if they have entered a wrong pan
			dl, err := insightsPkg.GetWealthBuilderDataCollectionDeeplink(sdkDl, networthBeFePb.ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_PAN)
			if err != nil {
				return nil, fmt.Errorf("error in getting wealth builder pan collection deeplink: %w", err)
			}
			return dl, nil
		default:
			return &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
			}, nil
		}
	}
	return sdkDl, nil
}

func (s *Service) isPanRequiredFlow(ctx context.Context, actorId string, caFlowName beCaEnumPb.CAFlowName) bool {
	isEnabled, err := s.isFeatureEnabled(ctx, actorId, types.Feature_FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB)
	if err != nil || !isEnabled {
		return true
	}
	logger.Info(ctx, fmt.Sprintf("checking if pan is really required for flow name %v", caFlowName))
	switch caFlowName {
	case beCaEnumPb.CAFlowName_CA_FLOW_NAME_NET_WORTH_IND_STOCKS, beCaEnumPb.CAFlowName_CA_FLOW_NAME_NPS:
		return true
	default:
		return false
	}
}

func (s *Service) getConnectedAccountsBenefitsDlForSdk(nextScreen *deeplinkPb.Deeplink, fiLiteCheckBox *widget.CheckboxItem, caFlowName string, aaEntity beCaEnumPb.AaEntity) (*deeplinkPb.Deeplink, error) {
	cbi := s.getWealthTncCheckboxItem(aaEntity)
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_ConnectedAccountBenefitsOptions{
			ConnectedAccountBenefitsOptions: &deeplinkPb.ConnectedAccountBenefitsOptions{
				CaFlowName: caFlowName,
				ProceedCta: &deeplinkPb.Cta{
					Text:         "PROCEED",
					Deeplink:     nextScreen,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				WealthTncCheckBox: cbi,
				FiLiteCheckBox:    fiLiteCheckBox,
			},
		},
	}, nil
}

func (s *Service) getUserReoobeInfo(ctx context.Context, actorId string) (*beUserPb.User, []*beCaExtPb.AccountDetails, error) {
	var usr *beUserPb.User
	var beReoobeResp *beCaPb.CheckReoobeResponse
	userCallErrGrp, userCtx := errgroup.WithContext(ctx)
	userCallErrGrp.Go(func() error {
		// Fetch user's mobile number and name from actor and user service
		userRes, userErr := s.GetUserByActorId(userCtx, actorId)
		if userErr != nil {
			return errors.Wrap(userErr, "error getting user by actor id")
		}
		usr = userRes
		return nil
	})
	reoobeCallErrGrp, reoobeCtx := errgroup.WithContext(ctx)
	reoobeCallErrGrp.Go(func() error {
		reoobeResp, reoobeErr := s.beConnectedAccClient.CheckReoobe(reoobeCtx, &beCaPb.CheckReoobeRequest{
			ActorId: actorId,
		})
		if reoobeErr = epifigrpc.RPCError(reoobeResp, reoobeErr); reoobeErr != nil {
			return errors.Wrap(reoobeErr, "error checking reoobe for user")
		}
		beReoobeResp = reoobeResp
		return nil
	})
	userErr := userCallErrGrp.Wait()
	if userErr != nil {
		return nil, nil, userErr
	}

	reoobeErr := reoobeCallErrGrp.Wait()
	if reoobeErr != nil {
		return nil, nil, reoobeErr
	}

	return usr, beReoobeResp.GetAccountDetailList(), nil
}

type caDeeplinkData struct {
	usr             *beUserPb.User
	reoobeAccounts  []*beCaExtPb.AccountDetails
	getAaEntityResp *beCaPb.GetAaEntityForConnectResponse
	fiLiteCheckBox  *widget.CheckboxItem
	isFiLiteUser    bool
}

func (s *Service) gatherDataForCaDeeplink(ctx context.Context, requestHeader *header.RequestHeader, caFlowName, fipId string) (*caDeeplinkData, error) {
	var (
		usr             *beUserPb.User
		reoobeAccounts  []*beCaExtPb.AccountDetails
		getAaEntityResp *beCaPb.GetAaEntityForConnectResponse
		fiLiteCheckBox  *widget.CheckboxItem
		isFiLiteUser    bool
	)

	actorId := requestHeader.GetAuth().GetActorId()
	appVersion := requestHeader.GetAppVersionCode()
	appPlatform := requestHeader.GetAuth().GetDevice().GetPlatform()

	// get user info and reoobed info
	errGrp, errGrpCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		var getUserReoobeInfoErr error
		usr, reoobeAccounts, getUserReoobeInfoErr = s.getUserReoobeInfo(errGrpCtx, actorId)
		return getUserReoobeInfoErr
	})

	// get user aa entity
	errGrp.Go(func() error {
		var getAaEntityErr error
		// ToDo: replace this with handling for CA flow name in GetAaEntityForConnect
		if caFlowName == beCaEnumPb.CAFlowName_CA_FLOW_NAME_NET_WORTH_IND_STOCKS.String() && s.conf.ConnectedAccount().ForceFinvuForIndianSecurities() {
			getAaEntityResp = &beCaPb.GetAaEntityForConnectResponse{
				Status:   rpc.StatusOk(),
				AaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
			}
			return nil
		}
		getAaEntityResp, getAaEntityErr = s.beConnectedAccClient.GetAaEntityForConnect(errGrpCtx, &beCaPb.GetAaEntityForConnectRequest{
			ActorId:     actorId,
			AppVersion:  appVersion,
			AppPlatform: appPlatform,
			FipId:       fipId,
		})
		return errors.Wrap(epifigrpc.RPCError(getAaEntityResp, getAaEntityErr), "error while getting aa entity from caClient")
	})

	// get feature details from onboarding client and construct fi-lite checkbox item
	errGrp.Go(func() error {
		isFiLite, err := s.isFiLiteUser(ctx, actorId)
		if err != nil {
			return fmt.Errorf("failed to check fi lite status for actor: %w", err)
		}

		if isFiLite {
			isFiLiteUser = true
			fiLiteCheckBox = fiLiteCheckboxItem(s.conf.LegalDocuments().FiTncUrl)
		}

		return nil
	})

	err := errGrp.Wait()
	if err != nil {
		return nil, err
	}

	return &caDeeplinkData{
		usr:             usr,
		reoobeAccounts:  reoobeAccounts,
		getAaEntityResp: getAaEntityResp,
		fiLiteCheckBox:  fiLiteCheckBox,
		isFiLiteUser:    isFiLiteUser,
	}, nil
}

func (s *Service) isFiLiteUser(ctx context.Context, actorId string) (bool, error) {
	getFeatureDetailsResp, getFeatureDetailsErr := s.onbClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(getFeatureDetailsResp, getFeatureDetailsErr); rpcErr != nil {
		return false, errors.Wrap(rpcErr, "error in onboarding client getting feature details")
	}

	return getFeatureDetailsResp.GetIsFiLiteUser(), nil
}

func (s *Service) getDeeplinkForConnectedAccounts(
	ctx context.Context,
	requestHeader *header.RequestHeader,
	caFlowName string,
	caFlowId string,
) (*deeplinkPb.Deeplink, *deeplinkPb.Deeplink, error) {
	data, err := s.gatherDataForCaDeeplink(ctx, requestHeader, caFlowName, "")
	if err != nil {
		return nil, nil, err
	}

	aaEntity := data.getAaEntityResp.GetAaEntity()
	// default benefits screen to initiate connected accounts
	sdkDeeplink, err := s.getDeeplinkToStartSDK(ctx, data.usr, requestHeader, aaEntity, caFlowName, caFlowId, nil)
	if err != nil {
		return nil, nil, err
	}

	dl, err := s.getConnectedAccountsBenefitsDlForSdk(sdkDeeplink, data.fiLiteCheckBox, caFlowName, aaEntity)
	if err != nil {
		return nil, nil, err
	}

	// User has not done reoobe
	if len(data.reoobeAccounts) == 0 {
		return dl, dl, nil
	}

	// user has done reoobe then redirect user to handle reoobe flow
	newDl, err := s.getConnectedAccountsBenefitsDlForReoobe(ctx, data.reoobeAccounts, sdkDeeplink, data.fiLiteCheckBox, aaEntity)
	if err != nil {
		return nil, nil, err
	}
	return newDl, dl, nil
}

func (s *Service) getConnectedAccountsBenefitsDlForReoobe(_ context.Context, beAccList []*beCaExtPb.AccountDetails,
	nextScreen *deeplinkPb.Deeplink, fiLiteCheckBox *widget.CheckboxItem, aaEntity beCaEnumPb.AaEntity) (*deeplinkPb.Deeplink, error) {
	proceedDl, err := s.getDeeplinkForHandleReoobe(beAccList, nextScreen)
	if err != nil {
		return nil, err
	}

	cbi := s.getWealthTncCheckboxItemFromScreenOptions(aaEntity)
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_ConnectedAccountBenefitsOptions{
			ConnectedAccountBenefitsOptions: &deeplinkPb.ConnectedAccountBenefitsOptions{
				ProceedCta: &deeplinkPb.Cta{
					Text:         "PROCEED",
					Deeplink:     proceedDl,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				WealthTncCheckBox: cbi,
				FiLiteCheckBox:    fiLiteCheckBox,
			},
		},
	}, nil
}

func (s *Service) isTokenAuthEnabled(ctx context.Context, actorId string, aaEntity beCaEnumPb.AaEntity) (commontypes.BooleanEnum, error) {
	if aaEntity != beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU {
		return commontypes.BooleanEnum_FALSE, nil
	}
	// check if feature is enabled for the actor id, user group and stickiness constraint or not
	releaseConstraint := release.NewCommonConstraintData(types.Feature_AA_FINVU_TOKEN_AUTHENTICATION).WithActorId(actorId)
	isEnabled, err := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		return commontypes.BooleanEnum_FALSE, errors.Wrap(err, fmt.Sprintf("error evaluating feature release : %v", releaseConstraint))
	}
	if isEnabled {
		return commontypes.BooleanEnum_TRUE, nil
	}

	return commontypes.BooleanEnum_FALSE, nil
}

func (s *Service) isV2FlowEnabled(ctx context.Context, userRes *beUserPb.User, appPlatform commontypes.Platform, appVersion uint32) (bool, error) {
	// Return false if appVersion is less than the min version
	// Decide on user group otherwise
	// nolint : exhaustive
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		if appVersion < s.conf.ConnectedAccount().V2FlowParams().MinVersionAndroid() {
			return false, nil
		}
	case commontypes.Platform_IOS:
		if appVersion < s.conf.ConnectedAccount().V2FlowParams().MinVersionIos() {
			return false, nil
		}
	}
	// If v2 flow is already enabled for the user, no check is needed on user group
	if s.conf.ConnectedAccount().V2FlowParams().UseV2Flow() {
		return true, nil
	}
	// Get user group for the user
	usrGroupResp, ugErr := s.userGroupClient.GetGroupsMappedToEmail(ctx, &group.GetGroupsMappedToEmailRequest{Email: userRes.GetProfile().GetEmail()})
	if userGrpErr := epifigrpc.RPCError(usrGroupResp, ugErr); userGrpErr != nil {
		return false, errors.Wrap(userGrpErr, fmt.Sprintf("error getting user groups by email : %s", userRes.GetProfile().GetEmail()))
	}
	// Send user to v2 flow if user is EITHER in internal group regardless of platform type OR if platform is Android.
	if isUserInTargetGroup(commontypes.UserGroup_INTERNAL, usrGroupResp.GetGroups()) || appPlatform == commontypes.Platform_ANDROID {
		return true, nil
	}
	// Return false otherwise
	return false, nil
}

func isUserInTargetGroup(targetGrp commontypes.UserGroup, grpList []commontypes.UserGroup) bool {
	for _, grp := range grpList {
		if grp == targetGrp {
			return true
		}
	}
	return false
}

func (s *Service) getTncMessageFromEntity(aaEntity deeplinkPb.ConnectedAccountsOptions_AaEntity) string {
	switch aaEntity {
	case deeplinkPb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY:
		return "I agree to OneMoney’s <a href=\"" + s.conf.LegalDocuments().AaOnemoneyTncUrl + "\">Terms & Conditions</a>"
	case deeplinkPb.ConnectedAccountsOptions_AA_ENTITY_FINVU:
		return "I agree to Finvu’s <a href=\"" + s.conf.LegalDocuments().AaFinvuTncUrl + "\">Terms & Conditions</a>"
	default:
		return ""
	}
}

func (s *Service) getTncMessageFromAaEntity(aaEntity types.AaEntity) string {
	switch aaEntity {
	case types.AaEntity_AA_ENTITY_AA_ONE_MONEY:
		return "I agree to OneMoney’s <a href=\"" + s.conf.LegalDocuments().AaOnemoneyTncUrl + "\">Terms & Conditions</a>"
	case types.AaEntity_AA_ENTITY_AA_FINVU:
		return "I agree to Finvu’s <a href=\"" + s.conf.LegalDocuments().AaFinvuTncUrl + "\">Terms & Conditions</a>"
	default:
		return ""
	}
}

func (s *Service) getWealthTncCheckboxItemFromScreenOptions(aaEntity beCaEnumPb.AaEntity) *widget.CheckboxItem {
	switch aaEntity {
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU:
		return s.getWealthTncCheckboxItem(beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU)
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY:
		return s.getWealthTncCheckboxItem(beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY)
	default:
		return nil
	}
}

func (s *Service) getWealthTncCheckboxItem(aaEntity beCaEnumPb.AaEntity) *widget.CheckboxItem {
	cbi := &widget.CheckboxItem{
		Id: consentPb.ConsentType_FI_WEALTH_TNC.String(),
		DisplayText: &commontypes.Text{
			FontColor:        checkBoxFontColor,
			FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
			FontColorOpacity: 100,
		},
		IsChecked: true,
	}
	switch aaEntity {
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY:
		cbi.GetDisplayText().DisplayValue =
			&commontypes.Text_Html{Html: fmt.Sprintf("I agree & accept <a style=\"color: #00B899\" href=\"%s\">epiFi Wealth TnC</a> and <a style=\"color: #00B899\" href=\"%s\">OneMoney TnC</a>",
				s.conf.LegalDocuments().FiWealthTncUrl, s.conf.LegalDocuments().AaOnemoneyTncUrl)}
	case beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU:
		cbi.GetDisplayText().DisplayValue =
			&commontypes.Text_Html{Html: fmt.Sprintf("I agree & accept <a style=\"color: #00B899\" href=\"%s\">epiFi Wealth TnC</a> and <a style=\"color: #00B899\" href=\"%s\">Finvu TnC</a>",
				s.conf.LegalDocuments().FiWealthTncUrl, s.conf.LegalDocuments().AaFinvuTncUrl)}
	default:
		return nil
	}
	return cbi
}

func (s *Service) convertToDeeplinkAaEntity(entity feConnectedAccPb.AaEntity) deeplinkPb.ConnectedAccountsOptions_AaEntity {
	switch entity {
	case feConnectedAccPb.AaEntity_AA_ENTITY_AA_FINVU:
		return deeplinkPb.ConnectedAccountsOptions_AA_ENTITY_FINVU
	case feConnectedAccPb.AaEntity_AA_ENTITY_AA_ONE_MONEY:
		return deeplinkPb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY
	default:
		return deeplinkPb.ConnectedAccountsOptions_AA_ENTITY_UNSPECIFIED
	}
}

func (s *Service) getDeeplinkForHandleReoobe(beAccList []*beCaExtPb.AccountDetails, nextScreen *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error) {
	feAccList, err := s.convertToFeAccountDetails(beAccList)
	if err != nil {
		return nil, errors.Wrap(err, "error converting to fe account details")
	}
	var dlAccList []*deeplinkPb.ConnectedAccountHandleReoobeOptions_AccountDetail
	for _, feAcc := range feAccList {
		dlAccList = append(dlAccList, &deeplinkPb.ConnectedAccountHandleReoobeOptions_AccountDetail{
			AccountId:           feAcc.GetAccountId(),
			MaskedAccountNumber: feAcc.GetMaskedAccountNumber(),
			AccountType:         feAcc.GetAccountType(),
			FipLogoUrl:          feAcc.GetFipLogoUrl(),
			FipName:             feAcc.GetFipName(),
			FiType:              feAcc.GetFiType(),
		})
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CONNECTED_ACCOUNT_HANDLE_REOOBE,
		ScreenOptions: &deeplinkPb.Deeplink_ConnectedAccountHandleReoobeOptions{
			ConnectedAccountHandleReoobeOptions: &deeplinkPb.ConnectedAccountHandleReoobeOptions{
				AccountDetailList: dlAccList,
				NextScreen:        nextScreen,
				Title: &commontypes.Text{
					DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetTitleText},
					FontColor:        ReeobeBottomsheetTitleFontColor,
					FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetTitleFontStyle},
					FontColorOpacity: DefaultFontOpacity,
				},
				Body: &commontypes.Text{
					DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetBodyText},
					FontColor:        ReeobeBottomsheetBodyFontColor,
					FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetBodyFontStyle},
					FontColorOpacity: DefaultFontOpacity,
				},
				Footer: &commontypes.Text{
					DisplayValue:     &commontypes.Text_Html{Html: ReeobeBottomsheetFooterText},
					FontColor:        ReeobeBottomsheetFooterFontColor,
					FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetFooterFontStyle},
					FontColorOpacity: DefaultFontOpacity,
				},
				CtaList: []*deeplinkPb.Cta{
					{
						Type:         deeplinkPb.Cta_DONE,
						Text:         ReeobeBottomsheetBackCtaText,
						DisplayTheme: deeplinkPb.Cta_SECONDARY,
					},
					{
						Type:         deeplinkPb.Cta_CONTINUE,
						Text:         ReeobeBottomsheetProceedCtaText,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
					},
				},
			},
		},
	}, nil
}

// ConnectAccount : Deprecated. Use GetLandingPageOnConnect for connected account RPC
func (s *Service) ConnectAccount(ctx context.Context, req *feConnectedAccPb.ConnectAccountRequest) (*feConnectedAccPb.ConnectAccountResponse, error) {
	newDl, _, wealthDepErr := s.getDeeplinkForConnectedAccounts(ctx, req.GetReq(), beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(), "")
	if wealthDepErr != nil {
		return &feConnectedAccPb.ConnectAccountResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	return &feConnectedAccPb.ConnectAccountResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Deeplink:   newDl,
	}, nil
}

func (s *Service) GetConnectedAccountEntryPoints(ctx context.Context, req *feConnectedAccPb.GetConnectedAccountEntryPointsRequest) (*feConnectedAccPb.GetConnectedAccountEntryPointsResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	if !s.conf.ConnectedAccount().IsConnectedAccountEnabled() {
		logger.Info(ctx, "global connected accounts flag is off")
		return &feConnectedAccPb.GetConnectedAccountEntryPointsResponse{
			RespHeader:                &header.ResponseHeader{Status: rpc.StatusOk()},
			IsConnectedAccountEnabled: commontypes.BooleanEnum_FALSE,
		}, nil
	}

	// This deeplink is used for each entry points, which will only have Screen name i.e. `CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW`
	// (No Screen Options required), client on receiving deeplink with this screen will call `GetLandingPageOnConnect` RPC each time.
	// This was done so that while connecting account from any entry point, deeplink with updated/current state is returned.
	dl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW,
	}

	entryPointMap, getAllEntryPointsErr := s.getAllEntryPoints(ctx, actorId, dl)
	if getAllEntryPointsErr != nil {
		if errors.Is(getAllEntryPointsErr, epifierrors.ErrPermissionDenied) {
			logger.Error(ctx, "error in getting connected accounts entry points", zap.Error(getAllEntryPointsErr))
			return &feConnectedAccPb.GetConnectedAccountEntryPointsResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusPermissionDenied()},
			}, nil
		}
		logger.Error(ctx, "error getting all entry points", zap.Error(getAllEntryPointsErr))
		return &feConnectedAccPb.GetConnectedAccountEntryPointsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	return &feConnectedAccPb.GetConnectedAccountEntryPointsResponse{
		RespHeader:                &header.ResponseHeader{Status: rpc.StatusOk()},
		EntryPointMap:             entryPointMap,
		IsConnectedAccountEnabled: commontypes.BooleanEnum_TRUE,
	}, nil
}

func (s *Service) getAllEntryPoints(ctx context.Context, actorId string, dl *deeplinkPb.Deeplink) (map[string]*feConnectedAccPb.EntryPointOptions, error) {
	var homeEntryPointOptions, searchEntryPointOptions, analyserEntryPointOptions,
		profileEntryPointOptions, accountManagerEntryPointOptions, allTransactionsEntryPointOptions,
		searchBannerEntryPointOptions *feConnectedAccPb.EntryPointOptions
	var homeEntryErr, searchEntryErr, analyserEntryErr,
		profileEntryErr, accountManagerEntryErr, allTransactionsEntryErr,
		searchBannerEntryErr error
	// home entry
	homeEntryErrGrp, _ := errgroup.WithContext(ctx)
	homeEntryErrGrp.Go(func() error {
		if homeEntryPointOptions, homeEntryErr = s.getHomeEntry(ctx, actorId, dl); homeEntryErr != nil {
			return homeEntryErr
		}
		return nil
	})
	// search entry
	searchEntryErrGrp, _ := errgroup.WithContext(ctx)
	searchEntryErrGrp.Go(func() error {
		if searchEntryPointOptions, searchEntryErr = s.getSearchEntry(ctx, dl); searchEntryErr != nil {
			return searchEntryErr
		}
		return nil
	})
	// analyser entry
	analyserEntryErrGrp, _ := errgroup.WithContext(ctx)
	analyserEntryErrGrp.Go(func() error {
		if analyserEntryPointOptions, analyserEntryErr = s.getAnalyserEntry(ctx, dl); analyserEntryErr != nil {
			return analyserEntryErr
		}
		return nil
	})
	// profile entry
	profileEntryErrGrp, _ := errgroup.WithContext(ctx)
	profileEntryErrGrp.Go(func() error {
		if profileEntryPointOptions, profileEntryErr = s.getProfileEntry(ctx, dl, actorId); profileEntryErr != nil {
			return profileEntryErr
		}
		return nil
	})
	// account manager entry
	accountManagerEntryErrGrp, _ := errgroup.WithContext(ctx)
	accountManagerEntryErrGrp.Go(func() error {
		if accountManagerEntryPointOptions, accountManagerEntryErr = s.getAccountManagerEntry(ctx, dl); accountManagerEntryErr != nil {
			return accountManagerEntryErr
		}
		return nil
	})
	// all transactions manager entry
	allTransactionsEntryErrGrp, _ := errgroup.WithContext(ctx)
	allTransactionsEntryErrGrp.Go(func() error {
		if allTransactionsEntryPointOptions, allTransactionsEntryErr = s.getAllTransactionsEntry(ctx, dl); allTransactionsEntryErr != nil {
			return allTransactionsEntryErr
		}
		return nil
	})
	// search banner entry
	searchBannerEntryErrGrp, _ := errgroup.WithContext(ctx)
	searchBannerEntryErrGrp.Go(func() error {
		if searchBannerEntryPointOptions, searchBannerEntryErr = s.getSearchBannerEntry(ctx, dl); searchBannerEntryErr != nil {
			return searchBannerEntryErr
		}
		return nil
	})
	homeEntryErr = homeEntryErrGrp.Wait()
	searchEntryErr = searchEntryErrGrp.Wait()
	analyserEntryErr = analyserEntryErrGrp.Wait()
	profileEntryErr = profileEntryErrGrp.Wait()
	accountManagerEntryErr = accountManagerEntryErrGrp.Wait()
	allTransactionsEntryErr = allTransactionsEntryErrGrp.Wait()
	searchBannerEntryErr = searchBannerEntryErrGrp.Wait()
	if entryErr := handleError(homeEntryErr, searchEntryErr, analyserEntryErr, profileEntryErr, accountManagerEntryErr, allTransactionsEntryErr, searchBannerEntryErr); entryErr != nil {
		return nil, entryErr
	}
	return populateEntryPointMap(homeEntryPointOptions, searchEntryPointOptions, analyserEntryPointOptions,
		profileEntryPointOptions, accountManagerEntryPointOptions, allTransactionsEntryPointOptions,
		searchBannerEntryPointOptions), nil
}

func populateEntryPointMap(homeEntryPointOptions, searchEntryPointOptions, analyserEntryPointOptions,
	profileEntryPointOptions, accountManagerEntryPointOptions, allTransactionsEntryPointOptions,
	searchBannerEntryPointOptions *feConnectedAccPb.EntryPointOptions) map[string]*feConnectedAccPb.EntryPointOptions {
	entryPointMap := make(map[string]*feConnectedAccPb.EntryPointOptions)
	entryPointMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_HOME.String()] = homeEntryPointOptions
	entryPointMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_SEARCH.String()] = searchEntryPointOptions
	entryPointMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_ANALYSER.String()] = analyserEntryPointOptions
	entryPointMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_PROFILE.String()] = profileEntryPointOptions
	entryPointMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_ACCOUNT_MANAGER.String()] = accountManagerEntryPointOptions
	entryPointMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_ALL_TRANSACTIONS.String()] = allTransactionsEntryPointOptions
	entryPointMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_SEARCH_BANNER.String()] = searchBannerEntryPointOptions
	return entryPointMap
}

func handleError(homeEntryErr, searchEntryErr, analyserEntryErr,
	profileEntryErr, accountManagerEntryErr, allTransactionsEntryErr,
	searchBannerEntryErr error) error {
	switch {
	case homeEntryErr != nil:
		return errors.Wrap(homeEntryErr, "error in home entry point")
	case searchEntryErr != nil:
		return errors.Wrap(searchEntryErr, "error in search entry point")
	case analyserEntryErr != nil:
		return errors.Wrap(analyserEntryErr, "error in analyser entry point")
	case profileEntryErr != nil:
		return errors.Wrap(profileEntryErr, "error in profile entry point")
	case accountManagerEntryErr != nil:
		return errors.Wrap(accountManagerEntryErr, "error in account manager entry point")
	case allTransactionsEntryErr != nil:
		return errors.Wrap(allTransactionsEntryErr, "error in all transactions entry point")
	case searchBannerEntryErr != nil:
		return errors.Wrap(searchBannerEntryErr, "error in search banner entry point")
	}
	return nil
}

func (s *Service) getHomeEntry(ctx context.Context, actorId string, dl *deeplinkPb.Deeplink) (*feConnectedAccPb.EntryPointOptions, error) {
	// number of active connected accounts should be 0
	caAccsResp, err := s.beConnectedAccClient.GetAllAccounts(ctx, &beCaPb.GetAllAccountsRequest{
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
		ActorId:           actorId,
		AccountFilterList: []beCaExtPb.AccountFilter{beCaExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE, beCaExtPb.AccountFilter_ACCOUNT_FILTER_DISCONNECTED},
	})
	if caAccsErr := epifigrpc.RPCError(caAccsResp, err); caAccsErr != nil {
		if !caAccsResp.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(caAccsErr, "error in getting all connected accounts")
		}
	}
	if len(caAccsResp.GetAccountDetailsList()) != 0 {
		logger.Info(ctx, "ignoring home entry banner, already have active connected accounts", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feConnectedAccPb.EntryPointOptions{
			Enabled: false,
		}, nil
	}
	return &feConnectedAccPb.EntryPointOptions{
		Enabled:  s.conf.ConnectedAccount().HomeEntryPoint().Enabled(),
		Text:     s.conf.ConnectedAccount().HomeEntryPoint().Text(),
		LogoUrl:  s.conf.ConnectedAccount().HomeEntryPoint().LogoUrl(),
		Deeplink: dl,
	}, nil
}

func (s *Service) getSearchEntry(_ context.Context, dl *deeplinkPb.Deeplink) (*feConnectedAccPb.EntryPointOptions, error) {
	return &feConnectedAccPb.EntryPointOptions{
		Enabled:  s.conf.ConnectedAccount().SearchEntryPoint().Enabled(),
		Text:     s.conf.ConnectedAccount().SearchEntryPoint().Text(),
		LogoUrl:  s.conf.ConnectedAccount().SearchEntryPoint().LogoUrl(),
		Deeplink: dl,
	}, nil
}

func (s *Service) getAnalyserEntry(_ context.Context, dl *deeplinkPb.Deeplink) (*feConnectedAccPb.EntryPointOptions, error) {
	return &feConnectedAccPb.EntryPointOptions{
		Enabled:  s.conf.ConnectedAccount().AnalyserEntryPoint().Enabled(),
		Text:     s.conf.ConnectedAccount().AnalyserEntryPoint().Text(),
		LogoUrl:  s.conf.ConnectedAccount().AnalyserEntryPoint().LogoUrl(),
		Deeplink: dl,
	}, nil
}

func (s *Service) getProfileEntry(ctx context.Context, dl *deeplinkPb.Deeplink, actorId string) (*feConnectedAccPb.EntryPointOptions, error) {
	featResp, err := s.onbClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(featResp, err); err != nil {
		logger.Error(ctx, "error in get feature details", zap.Error(err))
		return nil, err
	}

	enabled := s.conf.ConnectedAccount().ProfileEntryPoint().Enabled()
	if featResp.GetIsFiLiteUser() {
		enabled = false
	}

	return &feConnectedAccPb.EntryPointOptions{
		Enabled:  enabled,
		Text:     s.conf.ConnectedAccount().ProfileEntryPoint().Text(),
		LogoUrl:  s.conf.ConnectedAccount().ProfileEntryPoint().LogoUrl(),
		Deeplink: dl,
	}, nil
}

func (s *Service) getAccountManagerEntry(_ context.Context, dl *deeplinkPb.Deeplink) (*feConnectedAccPb.EntryPointOptions, error) {
	return &feConnectedAccPb.EntryPointOptions{
		Enabled:  s.conf.ConnectedAccount().AccountManagerEntryPoint().Enabled(),
		Text:     s.conf.ConnectedAccount().AccountManagerEntryPoint().Text(),
		LogoUrl:  s.conf.ConnectedAccount().AccountManagerEntryPoint().LogoUrl(),
		Deeplink: dl,
	}, nil
}

func (s *Service) getAllTransactionsEntry(_ context.Context, dl *deeplinkPb.Deeplink) (*feConnectedAccPb.EntryPointOptions, error) {
	return &feConnectedAccPb.EntryPointOptions{
		Enabled:  s.conf.ConnectedAccount().AllTransactionsEntryPoint().Enabled(),
		Text:     s.conf.ConnectedAccount().AllTransactionsEntryPoint().Text(),
		LogoUrl:  s.conf.ConnectedAccount().AllTransactionsEntryPoint().LogoUrl(),
		Deeplink: dl,
	}, nil
}

func (s *Service) getSearchBannerEntry(_ context.Context, dl *deeplinkPb.Deeplink) (*feConnectedAccPb.EntryPointOptions, error) {
	return &feConnectedAccPb.EntryPointOptions{
		Enabled:  s.conf.ConnectedAccount().SearchBannerEntryPoint().Enabled(),
		Text:     s.conf.ConnectedAccount().SearchBannerEntryPoint().Text(),
		LogoUrl:  s.conf.ConnectedAccount().SearchBannerEntryPoint().LogoUrl(),
		Deeplink: dl,
	}, nil
}

func (s *Service) GetAuthToken(ctx context.Context, req *feConnectedAccPb.GetAuthTokenRequest) (*feConnectedAccPb.GetAuthTokenResponse, error) {
	if req.GetAaEntity() != feConnectedAccPb.AaEntity_AA_ENTITY_AA_FINVU {
		return &feConnectedAccPb.GetAuthTokenResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInvalidArgument()}}, nil
	}
	// Get JWT token
	resp, err := s.beConnectedAccClient.GetAuthToken(ctx, &beCaPb.GetAuthTokenRequest{AaEntity: getBeAaEntity(req.GetAaEntity())})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return &feConnectedAccPb.GetAuthTokenResponse{RespHeader: &header.ResponseHeader{Status: resp.GetStatus()}}, nil
	}

	return &feConnectedAccPb.GetAuthTokenResponse{RespHeader: &header.ResponseHeader{
		Status: rpc.StatusOk()}, Token: resp.GetToken(), ExpiryDurationMinutes: resp.GetExpiryDurationMinutes()}, nil
}

// GetSdkExitDeeplink RPC provides the deeplink for exit from SDK.
// This RPC is used to get the deeplink show the next screen when the user is exited from SDK, According to the exit point or the exit operation
// that was last performed along with its reason, This RPC also takes CA Flow nome to show the exit screens specific to service which
// is using the connected account flow.
func (s *Service) GetSdkExitDeeplink(ctx context.Context, request *feConnectedAccPb.GetSdkExitDeeplinkRequest) (*feConnectedAccPb.GetSdkExitDeeplinkResponse, error) {
	caFlowNameFromReq := request.GetCaFlowName()
	caFlowName := beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT
	if caFlowNameVal, ok := beCaEnumPb.CAFlowName_value[caFlowNameFromReq]; ok {
		caFlowName = beCaEnumPb.CAFlowName(caFlowNameVal)
	}
	caFlowDlImplementation, implErr := s.caFlowDLFactory.GetCAFlowDeeplinkProcessor(caFlowName)
	if implErr != nil {
		logger.Error(ctx, "error in getting ca flow type implementation from factory", zap.Error(implErr))
		return &feConnectedAccPb.GetSdkExitDeeplinkResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	caFlowDeeplink, dlErr := caFlowDlImplementation.GetDlForExitScreen(ctx, &facModel.CaFlowRequestData{
		SdkExitOperation: request.GetExitOperation(),
		SdkExitReason:    request.GetExitReason(),
		ActorId:          request.GetReq().GetAuth().GetActorId(),
		AaEntity:         request.GetAaEntity(),
	})
	if dlErr != nil {
		logger.Error(ctx, "error in getting get exit screen deeplink method implementation in factory", zap.Error(dlErr))
		return &feConnectedAccPb.GetSdkExitDeeplinkResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	return &feConnectedAccPb.GetSdkExitDeeplinkResponse{
		RespHeader:         &header.ResponseHeader{Status: rpc.StatusOk()},
		ExitScreenDeeplink: caFlowDeeplink,
	}, nil
}

// GetRelatedAccountsForRenewal RPC returns all accounts eligible for consent renewal and the minimum consent expiry date.
// It takes zero or more fipIds as an optional argument for input.
// If no fipId is given it returns account of a fip corresponding to which the consent is expiring the earliest.
// Otherwise, returns accounts of given fips which are eligible for renewal
func (s *Service) GetRelatedAccountsForRenewal(ctx context.Context, actorId string, fipIdList ...string) ([]*beCaExtPb.AccountDetails, *timestamppb.Timestamp, error) {
	if actorId == "" {
		return nil, nil, errors.New("actorId cannot be empty")
	}

	beRenewalAccountDetailsResponse, err := s.beConnectedAccClient.GetAccountsForRenewal(ctx, &beCaPb.GetAccountsForRenewalRequest{ActorId: actorId})
	if err != nil {
		return nil, nil, err
	}

	accountList := beRenewalAccountDetailsResponse.GetRenewalAccountDetailsList()
	// if no fipId is given, consider accounts of only one fip whose corresponding consent is going to expire
	// the earliest
	if len(fipIdList) == 0 {
		minConsentExpiry := timestamppb.New(datetime.TIME_MAX)
		fipId := ""
		for _, account := range accountList {
			if account.GetConsentExpiry().AsTime().Before(minConsentExpiry.AsTime()) {
				minConsentExpiry = account.GetConsentExpiry()
				fipId = account.GetAccountDetails().GetFipId()
			}
		}
		fipIdList = append(fipIdList, fipId)
	}

	minConsentExpiry := timestamppb.New(datetime.TIME_MAX)
	var renewalAccountList []*beCaExtPb.AccountDetails
	for _, fipId := range fipIdList {
		for _, account := range accountList {
			if account.GetAccountDetails().GetFipId() == fipId {
				renewalAccountList = append(renewalAccountList, account.GetAccountDetails())
				if account.GetConsentExpiry().AsTime().Before(minConsentExpiry.AsTime()) {
					minConsentExpiry = account.GetConsentExpiry()
				}
			}
		}
	}
	return renewalAccountList, minConsentExpiry, nil
}

func (s *Service) GetLandingPageForRenewal(ctx context.Context, req *feConnectedAccPb.GetLandingPageForRenewalRequest) (*feConnectedAccPb.GetLandingPageForRenewalResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	appVersion := req.GetReq().GetAppVersionCode()
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	fipIdList := req.GetFipIdList()

	accountList, _, err := s.GetRelatedAccountsForRenewal(ctx, actorId, fipIdList...)
	if err != nil {
		return &feConnectedAccPb.GetLandingPageForRenewalResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	usr, reoobeAccountsList, err := s.getUserReoobeInfo(ctx, actorId)
	if err != nil {
		return &feConnectedAccPb.GetLandingPageForRenewalResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	renewalDeeplink, err := s.getRenewalDeeplink(ctx, usr, appPlatform, appVersion, actorId, accountList)
	if err != nil {
		return &feConnectedAccPb.GetLandingPageForRenewalResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	// User is not reoobed
	if len(reoobeAccountsList) == 0 {
		return &feConnectedAccPb.GetLandingPageForRenewalResponse{
			RespHeader:      &header.ResponseHeader{Status: rpc.StatusOk()},
			RenewalDeeplink: renewalDeeplink,
		}, nil
	}

	reoobeDeeplink, err := s.getDeeplinkForHandleReoobe(reoobeAccountsList, renewalDeeplink)
	if err != nil {
		return nil, err
	}

	return &feConnectedAccPb.GetLandingPageForRenewalResponse{
		RespHeader:      &header.ResponseHeader{Status: rpc.StatusOk()},
		RenewalDeeplink: reoobeDeeplink,
	}, nil
}

func (s *Service) getRenewalDeeplink(ctx context.Context, usr *beUserPb.User, appPlatform commontypes.Platform, appVersion uint32, actorId string, accountList []*beCaExtPb.AccountDetails) (*deeplinkPb.Deeplink, error) {
	renewalCaFlowName := beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_RENEWAL
	sdkLoginOptions, err := s.getParamsToSDKScreenOptions(ctx, usr, appPlatform, appVersion, actorId, renewalCaFlowName)
	if err != nil {
		return nil, err
	}

	renewalAccountList := make([]*feConnectedAccountCommonPb.AccountDetails, 0, len(accountList))
	for _, account := range accountList {
		renewalAccountList = append(renewalAccountList, convertToFeAccountDetails(account))
	}
	screenOptions, err := deeplinkv3.GetScreenOptionV2(&caDlOptions.ConsentRenewalSdkLoginScreenOptions{
		Title:                ConsentRenewalSdkLoginScreenTitleText,
		TitleForLoggedInUser: ConsentRenewalSdkLoginScreenTitleTextForLoggedInUser,
		AccountDetailsList:   renewalAccountList,
		SdkLoginInfo:         sdkLoginOptions,
	})
	if err != nil {
		return nil, err
	}

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_CONSENT_RENEWAL_SDK_LOGIN_SCREEN,
		ScreenOptionsV2: screenOptions,
	}, nil
}

func convertToFeAccountDetails(account *beCaExtPb.AccountDetails) *feConnectedAccountCommonPb.AccountDetails {
	return &feConnectedAccountCommonPb.AccountDetails{
		AccountId:              account.GetAccountId(),
		MaskedAccountNumber:    account.GetMaskedAccountNumber(),
		IfscCode:               account.GetIfscCode(),
		FiType:                 convertToFeAccInstrumentType(account.GetAccInstrumentType()),
		AccountType:            convertAccountTypeForFrontend(account.GetAccountType()),
		AccountTypeDisplayName: convertAccountTypeToExternalDisplayText(account.GetAccountType()),
		LinkedRefNumber:        account.GetLinkedAccountRef(),
		AaEntity:               getFrontendEnumForAaEntity(account.GetAaEntity()),
		FipMeta: &feConnectedAccountCommonPb.FipMeta{
			Bank:           account.GetFipMeta().GetBank(),
			Name:           account.GetFipMeta().GetName(),
			FipId:          account.GetFipMeta().GetFipId(),
			DisplayName:    account.GetFipMeta().GetDisplayName(),
			LogoUrl:        account.GetFipMeta().GetLogoUrl(),
			FiTypeMetaList: nil,
			OtpLength:      account.GetFipMeta().GetOtpLength(),
			OtpPattern:     account.GetFipMeta().GetOtpPattern(),
		},
	}
}

func (s *Service) GetLandingPageForConnectingFiToFi(ctx context.Context, request *feConnectedAccPb.GetLandingPageForConnectingFiToFiRequest) (*feConnectedAccPb.GetLandingPageForConnectingFiToFiResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	appVersion := request.GetReq().GetAppVersionCode()
	appPlatform := request.GetReq().GetAuth().GetDevice().GetPlatform()
	aaEntity := request.GetAaEntity()
	usr, reoobeAccountsList, err := s.getUserReoobeInfo(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "GetLandingPageForConnectingFiToFi: unable to determine usr reeobe info",
			zap.String(logger.USER_ID, usr.GetId()), zap.Error(err))
		return &feConnectedAccPb.GetLandingPageForConnectingFiToFiResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())},
		}, nil
	}
	connectFiToFiDeeplink, err := s.getDeeplinkForConnectingFiToFi(ctx, usr, appPlatform, appVersion, actorId, aaEntity)
	if err != nil {
		logger.Error(ctx, "GetLandingPageForConnectingFiToFi: error determining deeplink for fi to fi flow init", zap.Error(err))
		return &feConnectedAccPb.GetLandingPageForConnectingFiToFiResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())},
		}, nil
	}

	// User has not reoobed
	if len(reoobeAccountsList) == 0 {
		return &feConnectedAccPb.GetLandingPageForConnectingFiToFiResponse{
			RespHeader:             &header.ResponseHeader{Status: rpc.StatusOk()},
			InitFiToFiFlowDeeplink: connectFiToFiDeeplink,
		}, nil
	}

	reoobeDeeplink, err := s.getDeeplinkForHandleReoobe(reoobeAccountsList, connectFiToFiDeeplink)
	if err != nil {
		logger.Error(ctx, "GetLandingPageForConnectingFiToFi: error determining reoobe deeplink", zap.Error(err))
		return &feConnectedAccPb.GetLandingPageForConnectingFiToFiResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())},
		}, nil
	}

	return &feConnectedAccPb.GetLandingPageForConnectingFiToFiResponse{
		RespHeader:             &header.ResponseHeader{Status: rpc.StatusOk()},
		InitFiToFiFlowDeeplink: reoobeDeeplink,
	}, nil
}

func (s *Service) getDeeplinkForConnectingFiToFi(ctx context.Context, usr *beUserPb.User, appPlatform commontypes.Platform,
	appVersion uint32, actorId string, aaEntity types.AaEntity) (*deeplinkPb.Deeplink, error) {

	sdkInitParams, err := s.getSdkInitializationParams(ctx, usr, appPlatform, appVersion, actorId, aaEntity,
		beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECT_FI_TO_FI)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("getDeeplinkForConnectingFiToFi: unable to determine sdk init params for %v", actorId))
	}
	fiFedSavingsAcc, err := s.getFiSavingBankAccount(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("getDeeplinkForConnectingFiToFi: unable to fetch fiFedSavingsAcc for %v", actorId))
	}
	fedSavingBankAccNo := fiFedSavingsAcc.GetAccount().GetAccountNo()

	fipDetail, err := caPkg.GetFipMetaById(caPkg.FederalFipId)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("getDeeplinkForConnectingFiToFi: error while fetching fipDetail from GetFipMetaById for %v", actorId))
	}

	sdkInitScreenOptions, err := deeplinkv3.GetScreenOptionV2(&caDlOptions.FiToFiFlowSdkInitialisationScreenOptions{
		AaLoginBottomSheet: &features.FiToFiAaLoginBottomSheet{
			AaLoginOtpInfo: &feConnectedAccountCommonPb.TextDisplayInfo{
				Title:       FiToFiBottomSheetAaLoginDisplayTitle,
				Description: formatTextWithMobileNumber(FiToFiBottomSheetAaLoginDisplayDesc, usr.GetProfile().GetPhoneNumber()),
			},
			AaAlreadyLoggedInInfo: &feConnectedAccountCommonPb.TextDisplayInfo{
				Title: FiToFiBottomSheetAaAlreadyLoginDisplayTitle,
			},
			DisplayAccountDetails: []*feConnectedAccountCommonPb.AccountDetails{
				{
					MaskedAccountNumber:    fedSavingBankAccNo[len(fedSavingBankAccNo)-4:],
					IfscCode:               caPkg.FiFederalIfscCode,
					FiType:                 strings.ToUpper(caPkg.Deposit),
					AccountType:            caPkg.Savings,
					AccountTypeDisplayName: FederalSavingAccount,
					FipMeta: &feConnectedAccountCommonPb.FipMeta{
						Bank:        fipDetail.Bank,
						Name:        fipDetail.Name,
						FipId:       fipDetail.Id,
						DisplayName: "Federal",
						LogoUrl:     FederalFipLogoURL,
					},
				},
			},
		},
		FiToFiDiscoveryBottomSheet: &features.FiToFiDiscoveryBottomSheet{
			DiscoveryInProgressInfo: &feConnectedAccountCommonPb.TextDisplayInfo{
				Title:       FiToFiBottomSheetAccDiscoveryDisplayTitle,
				Description: FiToFiBottomSheetAccDiscoveryDisplayDesc,
			},
			NumRetries: s.conf.ConnectedAccount().MaxRetryAllowedForFiToFiAccountDiscoveryFailureCase(),
		},
		FiToFiLinkFipBottomSheet: &features.FiToFiLinkFipBottomSheet{
			BankOtpInfo: &feConnectedAccountCommonPb.TextDisplayInfo{
				Title:       FiToFiBottomSheetFipLoginDisplayTitle,
				Description: FiToFiBottomSheetFipLoginDisplayDesc,
			},
		},
		SdkInitArgs: sdkInitParams,
	})
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("getDeeplinkForConnectingFiToFi: error determining sdkInitScreenOptions for %v", actorId))
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_CA_INIT_SDK_FI_TO_FI_FLOW_SCREEN,
		ScreenOptionsV2: sdkInitScreenOptions,
	}, nil
}

func (s *Service) getSdkInitializationParams(ctx context.Context, usr *beUserPb.User, appPlatform commontypes.Platform,
	appVersion uint32, actorId string, aaEntity types.AaEntity, caFlowName beCaEnumPb.CAFlowName) (*feConnectedAccountCommonPb.AaSdkLoginOptions, error) {

	useV2Flow, v2FlowFlagErr := s.isV2FlowEnabled(ctx, usr, appPlatform, appVersion)
	if v2FlowFlagErr != nil {
		return nil, errors.Wrap(v2FlowFlagErr, fmt.Sprintf("getSdkInitParams: error determining flag for v2 flow for %v", actorId))
	}

	useTokenAuth, useTokenAuthErr := s.isTokenAuthEnabled(ctx, actorId, ConvertToBeAaEntity(aaEntity))
	if useTokenAuthErr != nil {
		return nil, errors.Wrap(useTokenAuthErr, fmt.Sprintf("getSdkInitParams: error determining flag for using token auth for %v", actorId))
	}
	logger.Info(ctx, "deeplink params", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.CA_FLOW, caFlowName),
		zap.Any("isTokenAuthFlagEnabled", useTokenAuth), zap.Any("useV2Flow", useV2Flow))

	return &feConnectedAccountCommonPb.AaSdkLoginOptions{
		MobileNumber:           usr.GetProfile().GetPhoneNumber(),
		Name:                   usr.GetProfile().GetPanName().ToString(),
		AaEntity:               aaEntity,
		FiuId:                  s.conf.ConnectedAccount().FiuId(),
		AaTncMessage:           s.getTncMessageFromAaEntity(aaEntity),
		UseV2Flow:              useV2Flow,
		UseTokenAuthentication: useTokenAuth,
		CaFlowName:             caFlowName.String(),
	}, nil
}

func (s *Service) getFiSavingBankAccount(ctx context.Context, actorId string) (*beSavingsPb.GetAccountResponse, error) {
	savingsResp, err := s.beSavingsClient.GetAccount(ctx, &beSavingsPb.GetAccountRequest{
		Identifier: &beSavingsPb.GetAccountRequest_ActorId{
			ActorId: actorId,
		},
	})
	// savings GetAccount response does not have rpc.Status field because of which epifi RPCError check cannot be used here
	if err != nil || savingsResp.GetAccount() == nil {
		if err == nil {
			err = errors.New("getFiSavingBankAccount: savings account object is nil")
		}
		if errors.Is(err, status.Error(codes.NotFound, "record not found")) {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, err.Error())
		}
		return nil, errors.Wrap(err, "getFiSavingBankAccount: error while fetching savings account details")
	}
	return savingsResp, nil
}

// GetConsentHandleStatus RPC returns consent handle status given consent handle in request.
//
//nolint:gocritic
func (s *Service) GetConsentHandleStatus(ctx context.Context, req *feConnectedAccPb.GetConsentHandleStatusRequest) (
	*feConnectedAccPb.GetConsentHandleStatusResponse, error) {
	consentHandle := req.GetConsentHandle()
	currentPollCount := req.GetCurrentPollCount()
	maxPollCountAllowed := s.conf.ConnectedAccount().MaxAllowedConsentHandleStatusPoll()
	nextPollDuration := s.conf.ConnectedAccount().ConsentHandleStatusNextPollDuration()

	consentRequestRes, err := s.beConnectedAccClient.GetConsentRequestDetails(ctx,
		&beCaPb.GetConsentRequestDetailsRequest{
			ConsentHandle: consentHandle,
		})
	if rpcErr := epifigrpc.RPCError(consentRequestRes, err); rpcErr != nil {
		logger.Error(ctx, "error while invoking GetConsentRequestDetails", zap.Error(rpcErr))
		if consentRequestRes.GetStatus().IsRecordNotFound() {
			return &feConnectedAccPb.GetConsentHandleStatusResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFoundWithDebugMsg(fmt.Sprintf(
					"record not found error while invoking GetConsentRequestDetails: %v", rpcErr.Error()))},
			}, nil
		}
		return &feConnectedAccPb.GetConsentHandleStatusResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
				"internal error while invoking GetConsentRequestDetails: %v", rpcErr.Error()))},
		}, nil
	}
	consentHandleStatus := consentRequestRes.GetConsentRequest().GetConsentHandleStatus()
	caFlowName := consentRequestRes.GetConsentRequest().GetCaFlowName()

	switch {
	case consentHandleStatus == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY:
		// isAnyAccOtherThanFiFederalAccConnected, err := s.IsAnyAccOtherThanFiFederalAccConnected(ctx,
		//	consentRequestRes.GetConsentRequest().GetActorId())
		// if err != nil {
		//	logger.Error(ctx, fmt.Sprintf(
		//		"error while determining if any account other than Federal saving bank account connected"+
		//			" for actorId:%v ",
		//		consentRequestRes.GetConsentRequest().GetActorId()), zap.Error(err))
		//	return &feConnectedAccPb.GetConsentHandleStatusResponse{
		//		RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
		//			"error while determining if any account other than Federal saving bank account connected"+
		//				" for actorId:%v ,err: %v",
		//			consentRequestRes.GetConsentRequest().GetActorId(), err.Error()))},
		//	}, nil
		// }
		var screenOptions *anypb.Any
		// if isAnyAccOtherThanFiFederalAccConnected {
		screenOptions, err = getScreenOptForFiToFiTerminalSuccessScreen(FiToFiSuccessTerminalScreenBottomSheetTitle,
			FiToFiSuccessTerminalScreenBottomSheetDescA,
			durationpb.New(time.Duration(FiToFiSuccessTerminalScreenBottomSheetAutoDismissDuration)*(time.Second)),
			nil)
		// } else {
		//	screenOptions, err = getScreenOptForFiToFiTerminalSuccessScreen(FiToFiSuccessTerminalScreenBottomSheetTitle,
		//		FiToFiSuccessTerminalScreenBottomSheetDescB,
		//		nil, []*deeplinkPb.Cta{
		//			{
		//				Type:         deeplinkPb.Cta_DONE,
		//				Text:         FiToFiSuccessTerminalScreenBottomSheetConnectAccLaterCta,
		//				DisplayTheme: deeplinkPb.Cta_SECONDARY,
		//			},
		//			{
		//				Type:         deeplinkPb.Cta_DONE,
		//				Text:         FiToFiSuccessTerminalScreenBottomSheetConnectAccCta,
		//				DisplayTheme: deeplinkPb.Cta_PRIMARY,
		//				Deeplink: &deeplinkPb.Deeplink{
		//					Screen: deeplinkPb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW,
		//				},
		//			},
		//		})
		// }
		if err != nil {
			logger.Error(ctx, "GetConsentHandleStatus: failure in determining success terminal screen", zap.Error(err))
			return &feConnectedAccPb.GetConsentHandleStatusResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
					"GetConsentHandleStatus: failure in determining success terminal screen: %v", zap.Error(err)))},
			}, nil
		}
		return &feConnectedAccPb.GetConsentHandleStatusResponse{
			RespHeader:          &header.ResponseHeader{Status: rpc.StatusOk()},
			ConsentHandleStatus: feConnectedAccountCommonPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY,
			Deeplink: &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_CA_FI_TO_FI_FLOW_TERMINAL_SCREEN,
				ScreenOptionsV2: screenOptions,
			},
			CurrentPollCount: int32(-1),
		}, nil

	case consentHandleStatus == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING &&
		currentPollCount < maxPollCountAllowed:
		return &feConnectedAccPb.GetConsentHandleStatusResponse{
			RespHeader:          &header.ResponseHeader{Status: rpc.StatusOk()},
			ConsentHandleStatus: feConnectedAccountCommonPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING,
			NextPollDuration:    durationpb.New(nextPollDuration),
			CurrentPollCount:    currentPollCount + 1,
		}, nil

	case consentHandleStatus == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_FAILED ||
		consentHandleStatus == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_REJECTED ||
		(consentHandleStatus == beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING &&
			currentPollCount >= maxPollCountAllowed):
		caFlowDlImplementation, implErr := s.caFlowDLFactory.GetCAFlowDeeplinkProcessor(caFlowName)
		if implErr != nil {
			logger.Error(ctx, fmt.Sprintf(
				"CaFlow name factory method not implemented case with consentHandleStatus: %v",
				consentHandleStatus), zap.Error(implErr))
			return &feConnectedAccPb.GetConsentHandleStatusResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(
					"CaFlow name factory method not implemented case with consentHandleStatus")},
			}, nil
		}
		caFlowDeeplink, dlErr := caFlowDlImplementation.GetDlForConsentFailure(
			ctx,
			&facModel.CaFlowRequestData{
				AaEntity:         ConvertToTypesAaEntity(consentRequestRes.GetConsentRequest().GetAaEntity()),
				ConnectionFlowId: req.GetConnectionFlowId(),
			})
		if dlErr != nil {
			logger.Error(ctx, fmt.Sprintf(
				"failure in getting CaFlowDeeplink for consent failure case with consentHandleStatus: %v",
				consentHandleStatus), zap.Error(dlErr))
			return &feConnectedAccPb.GetConsentHandleStatusResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
					"failure in getting CaFlowDeeplink for consent failure case with consentHandleStatus: %v, err: %v, ",
					consentHandleStatus, dlErr.Error()))},
			}, nil
		}
		return &feConnectedAccPb.GetConsentHandleStatusResponse{
			RespHeader:          &header.ResponseHeader{Status: rpc.StatusOk()},
			ConsentHandleStatus: s.ConvertToFeCommonConsentHandleStatus(consentHandleStatus),
			Deeplink:            caFlowDeeplink,
			CurrentPollCount:    int32(-1),
		}, nil

	default:
		logger.Error(ctx, "unexpected consent handle status received: CONSENT_HANDLE_STATUS_UNSPECIFIED")
		return &feConnectedAccPb.GetConsentHandleStatusResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("unexpected consent " +
				"handle status received: " +
				"CONSENT_HANDLE_STATUS_UNSPECIFIED")},
			ConsentHandleStatus: feConnectedAccountCommonPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_UNSPECIFIED,
			CurrentPollCount:    int32(-1),
		}, nil
	}
}

func getScreenOptForFiToFiTerminalSuccessScreen(title, desc string, autoDismissDuration *durationpb.Duration,
	ctaList []*deeplinkPb.Cta) (*anypb.Any, error) {
	return deeplinkv3.GetScreenOptionV2(&caDlOptions.FiToFiFlowTerminalScreenOptions{
		Title: commontypes.GetTextFromStringFontColourFontStyle(title, FiToFiSuccessTerminalScreenBottomSheetTitleFontColor,
			FiToFiSuccessTerminalScreenBottomSheetTitleFontStyle),
		Desc: commontypes.GetTextFromStringFontColourFontStyle(desc, FiToFiSuccessTerminalScreenBottomSheetDescFontColor,
			FiToFiSuccessTerminalScreenBottomSheetDescFontStyle),
		ConsentStatusSuccessIcon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: FiToFiSuccessTerminalScreenBottomSheetSuccessScreenIconURL,
					},
					ImageType: commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{
						Width:  FiToFiSuccessTerminalScreenBottomSheetIconWidth,
						Height: FiToFiSuccessTerminalScreenBottomSheetIconHeight,
					},
				},
			},
		},
		AutoDismissDuration:         autoDismissDuration,
		ProceedCtas:                 ctaList,
		FiFlowTerminalScreenPurpose: feConnectedAccountFeaturesPb.FiToFiFlowTerminalScreenPurpose_FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_CONSENT_STATUS_SUCCESS,
		BgColor: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{
			BlockColour: FiToFiSuccessTerminalScreenBottomSheetBgColor}},
	})
}

func getPanDobDeeplink(sdkDlBytes []byte) *deeplinkPb.Deeplink {
	consents := []*deeplinkPb.Consent{
		{
			Text: onboardingPkg.FiWealthTncText,
			TextV2: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: onboardingPkg.FiWealthTncText,
				},
			},
			Consent: deeplinkPb.ConsentTypeUrl_FI_WEALTH_TNC.String(),
			ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
				{
					Title:       onboardingPkg.KnowMoreTitle,
					Description: onboardingPkg.KnowMoreDescription,
					TitleV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.KnowMoreTitle,
						},
					},
					DescriptionV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.KnowMoreDescription,
						},
					},
				},
				{
					Title:       onboardingPkg.CKYCFailureTitle,
					Description: onboardingPkg.CKYCFailureDescription,
					TitleV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.CKYCFailureTitle,
						},
					},
					DescriptionV2: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: onboardingPkg.CKYCFailureTitle,
						},
					},
				},
			},
		},
		{
			Text: onboardingPkg.FiPrivacyPolicyText,
			TextV2: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: onboardingPkg.FiPrivacyPolicyText,
				},
			},
			Consent: deeplinkPb.ConsentTypeUrl_FI_PRIVACY_POLICY.String(),
			ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
				{
					Title:       onboardingPkg.EligibilityChecksTitle,
					Description: onboardingPkg.EligibilityChecksDescription,
				},
			},
		},
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_REGISTER_CKYC,
		ScreenOptions: &deeplinkPb.Deeplink_RegisterCkycScreenOptions{
			RegisterCkycScreenOptions: &deeplinkPb.RegisterCKYCScreenOptions{
				Title:      onboardingPkg.PanDobTitleForCa,
				Subtitle:   onboardingPkg.PanDobSubtitleForCa,
				IsEditable: commontypes.BooleanEnum_TRUE,
				EntryPoint: signup.EntryPoint_ENTRY_POINT_CONNECTED_ACCOUNTS.String(),
				Consents:   consents,
				BackAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_HOME,
				},
				BackActionV2: &deeplinkPb.BackAction{
					ShowButton: commontypes.BooleanEnum_TRUE,
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_HOME,
					},
				},
				Blob:           sdkDlBytes,
				FeedbackScreen: appFeedbackPb.AppScreen_APP_SCREEN_PAN_DOB_DROP_OFF.String(),
			},
		},
	}
}

// nolint:funlen,dupl
func getPanDobDeeplinkV2(sdkDeeplink *deeplinkPb.Deeplink) *deeplinkPb.Deeplink {
	return panPkg.GetNsdlPanDobForm(&panPkg.GetPanDobFormReq{
		ShouldFetchFromServer:      false,
		IsEditable:                 true,
		ShowFiWealthTncConsent:     true,
		ShowFiPrivacyPolicyConsent: true,
		Title:                      onboardingPkg.PanDobTitleForCa,
		Subtitle:                   onboardingPkg.PanDobSubtitleForCa,
		BackAction: &deeplinkPb.BackAction{
			ShowButton: commontypes.BooleanEnum_TRUE,
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_HOME,
			},
		},
		NextAction: sdkDeeplink,
	})

	// todo[obed]: remove the commented code after supporting info popup in consent checkbox.
	//  the commented code is kept in place to use the copy

	// consents := []*deeplinkPb.Consent{
	// 	{
	// 		Text: onboardingPkg.FiWealthTncText,
	// 		TextV2: &commontypes.Text{
	// 			DisplayValue: &commontypes.Text_Html{
	// 				Html: onboardingPkg.FiWealthTncText,
	// 			},
	// 		},
	// 		Consent: deeplinkPb.ConsentTypeUrl_FI_WEALTH_TNC.String(),
	// 		ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
	// 			{
	// 				Title:       onboardingPkg.KnowMoreTitle,
	// 				Description: onboardingPkg.KnowMoreDescription,
	// 				TitleV2: &commontypes.Text{
	// 					DisplayValue: &commontypes.Text_PlainString{
	// 						PlainString: onboardingPkg.KnowMoreTitle,
	// 					},
	// 				},
	// 				DescriptionV2: &commontypes.Text{
	// 					DisplayValue: &commontypes.Text_PlainString{
	// 						PlainString: onboardingPkg.KnowMoreDescription,
	// 					},
	// 				},
	// 			},
	// 			{
	// 				Title:       onboardingPkg.CKYCFailureTitle,
	// 				Description: onboardingPkg.CKYCFailureDescription,
	// 				TitleV2: &commontypes.Text{
	// 					DisplayValue: &commontypes.Text_PlainString{
	// 						PlainString: onboardingPkg.CKYCFailureTitle,
	// 					},
	// 				},
	// 				DescriptionV2: &commontypes.Text{
	// 					DisplayValue: &commontypes.Text_PlainString{
	// 						PlainString: onboardingPkg.CKYCFailureTitle,
	// 					},
	// 				},
	// 			},
	// 		},
	// 	},
	// 	{
	// 		Text: onboardingPkg.FiPrivacyPolicyText,
	// 		TextV2: &commontypes.Text{
	// 			DisplayValue: &commontypes.Text_Html{
	// 				Html: onboardingPkg.FiPrivacyPolicyText,
	// 			},
	// 		},
	// 		Consent: deeplinkPb.ConsentTypeUrl_FI_PRIVACY_POLICY.String(),
	// 		ConsentInfos: []*deeplinkPb.Consent_ConsentInfo{
	// 			{
	// 				Title:       onboardingPkg.EligibilityChecksTitle,
	// 				Description: onboardingPkg.EligibilityChecksDescription,
	// 			},
	// 		},
	// 	},
	// }

	// isEditable := !req.IsFiLiteUser

	// isEditable := false
	// inputFields := []*formPkg.InputField{
	// 	{
	// 		Label:                      commontypes.GetTextFromStringFontColourFontStyle("NAME", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
	// 		IsEditable:                 isEditable,
	// 		FieldId:                    formPkg.FieldIdentifier_FIELD_IDENTIFIER_PAN_NAME.String(),
	// 		ShouldFetchValueFromServer: true,
	// 		FieldType:                  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT,
	// 	},
	// 	{
	// 		Label:                      commontypes.GetTextFromStringFontColourFontStyle("DATE OF BIRTH", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
	// 		IsEditable:                 isEditable,
	// 		FieldId:                    formPkg.FieldIdentifier_FIELD_IDENTIFIER_PAN_DATE_OF_BIRTH.String(),
	// 		ShouldFetchValueFromServer: true,
	// 		FieldType:                  formPkg.InputFieldType_INPUT_FIELD_TYPE_DATE,
	// 	},
	// 	{
	// 		Label:                      commontypes.GetTextFromStringFontColourFontStyle("PAN CARD NUMBER", "#B9B9B9", commontypes.FontStyle_OVERLINE_1),
	// 		IsEditable:                 isEditable,
	// 		FieldId:                    formPkg.FieldIdentifier_FIELD_IDENTIFIER_PAN.String(),
	// 		ShouldFetchValueFromServer: true,
	// 		FieldType:                  formPkg.InputFieldType_INPUT_FIELD_TYPE_TEXT,
	// 	},
	// {
	//	Label:                      commontypes.GetTextFromStringFontColourFontStyle(CkycConsentText, "#6A6D70", commontypes.FontStyle_BODY_4),
	//	IsEditable:                 true,
	//	FieldId:                    formPkg.FieldIdentifier_FIELD_IDENTIFIER_ONBOARDING_FEDERAL_TNCS.String(),
	//	ShouldFetchValueFromServer: false,
	//	FieldType:                  formPkg.InputFieldType_INPUT_FIELD_TYPE_CHECKBOX,
	// },
	// }
	// screenOptionsV2 := deeplinkv3.GetScreenOptionV2WithoutError(&form.UserDetailsFormScreenOptions{
	// 	Title:    typesPb.GetPlainStringText(onboardingPkg.PanDobTitleForCa),
	// 	Subtitle: typesPb.GetPlainStringText(onboardingPkg.PanDobSubtitleForCa),
	// 	Flow:     form.UpdateUserDetailsFlow_UPDATE_USER_DETAILS_FLOW_NSDL_PAN.String(),
	// 	BackAction: &deeplinkPb.BackAction{
	// 		ShowButton: commontypes.BooleanEnum_TRUE,
	// 		Deeplink: &deeplinkPb.Deeplink{
	// 			Screen: deeplinkPb.Screen_HOME,
	// 		},
	// 	},
	// 	NextAction: sdkDeeplink,
	// 	Ctas: []*deeplinkPb.Cta{
	// 		{
	// 			Type:         deeplinkPb.Cta_CONTINUE,
	// 			Text:         "Continue",
	// 			DisplayTheme: deeplinkPb.Cta_PRIMARY,
	// 		},
	// 	},
	// 	InputFields: inputFields,
	// })
	// return &deeplinkPb.Deeplink{
	// 	Screen:          deeplinkPb.Screen_USER_DETAILS_FORM,
	// 	ScreenOptionsV2: screenOptionsV2,
	// }
}

func getSdkDeeplinkFromPanDobDl(deeplink *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error) {
	if deeplink.GetScreen().String() == deeplinkPb.Screen_CONNECTED_ACCOUNTS_SDK.String() {
		return deeplink, nil
	}
	var dl deeplinkPb.Deeplink
	if err := json.Unmarshal(deeplink.GetRegisterCkycScreenOptions().GetBlob(), &dl); err != nil {
		return nil, err
	}
	return &dl, nil
}

//nolint:funlen
func (s *Service) GetAllDepositAccounts(ctx context.Context, request *feConnectedAccPb.GetAllDepositAccountsRequest) (
	*feConnectedAccPb.GetAllDepositAccountsResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	var (
		activeFiFDAccTiles, maturedFiFDAccTiles                   []*feConnectedAccPb.HomeAccountTile
		activeFiSDAccTiles, maturedFiSDAccTiles                   []*feConnectedAccPb.HomeAccountTile
		aaFDAccTiles, activeAaFDAccTiles, maturedAaFDAccTiles     []*feConnectedAccPb.HomeAccountTile
		aaRDAccTiles, activeAaRDAccTiles, maturedAaRDAccTiles     []*feConnectedAccPb.HomeAccountTile
		activeUserFdInvestmentTiles, maturedUserFdInvestmentTiles []*feConnectedAccPb.HomeAccountTile
		activeUserRdInvestmentTiles, maturedUserRdInvestmentTiles []*feConnectedAccPb.HomeAccountTile
		allFiDepAccTiles, allAADepAccTiles                        []*feConnectedAccPb.HomeAccountTile
		aaFDAccDetails, aaRDAccDetails                            []*beCaExtPb.AccountDetails
		todayEOD                                                  = datetime.EndOfDay(time.Now())
		err                                                       error
	)

	fiDepBalance := money.ZeroINR().GetPb()

	allFiDepositAccounts, accErr := s.getAllFiDepositAccounts(ctx, actorId)
	if accErr != nil {
		logger.Error(ctx, "failure in getting All Fi deposits accounts", zap.Error(accErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &feConnectedAccPb.GetAllDepositAccountsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error fetching FI SD,FD deposit accounts list: for actorId:%v ,err: %v", actorId, accErr.Error()))},
		}, nil
	}

	for _, fiDepAcc := range allFiDepositAccounts.GetAccounts() {
		switch fiDepAcc.GetType() {
		case accountPb.Type_FIXED_DEPOSIT:
			fiFDAccTile, fiFdAccErr := s.getFiDepAccTiles(fiDepAcc, allFiDepositAccounts.GetDepositAccountIdToGoalDetails())
			if fiFdAccErr != nil {
				logger.Error(ctx, "failure in getting Fi FD account tile", zap.Error(fiFdAccErr), zap.String(logger.ACTOR_ID_V2, actorId))
				return &feConnectedAccPb.GetAllDepositAccountsResponse{
					RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
						"error creating FI SD account tile: for actorId:%v ,err: %v", actorId, fiFdAccErr.Error()))},
				}, nil
			}
			if fiDepAcc.GetState() == depositPb.DepositState_CLOSED || fiDepAcc.GetState() == depositPb.DepositState_PRECLOSED {
				maturedFiFDAccTiles = append(maturedFiFDAccTiles, fiFDAccTile)
			} else {
				activeFiFDAccTiles = append(activeFiFDAccTiles, fiFDAccTile)
			}
		case accountPb.Type_SMART_DEPOSIT:
			fiSDAccTile, fiSdAccErr := s.getFiDepAccTiles(fiDepAcc, allFiDepositAccounts.GetDepositAccountIdToGoalDetails())
			if fiSdAccErr != nil {
				logger.Error(ctx, "failure in getting Fi SD account tile", zap.Error(fiSdAccErr),
					zap.String(logger.ACTOR_ID_V2, actorId))
				return &feConnectedAccPb.GetAllDepositAccountsResponse{
					RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
						"error creating FI FD account tile: for actorId:%v ,err: %v", actorId, fiSdAccErr.Error()))},
				}, nil
			}
			if fiDepAcc.GetState() == depositPb.DepositState_CLOSED || fiDepAcc.GetState() == depositPb.DepositState_PRECLOSED {
				maturedFiSDAccTiles = append(maturedFiSDAccTiles, fiSDAccTile)
			} else {
				activeFiSDAccTiles = append(activeFiSDAccTiles, fiSDAccTile)
			}
		}
		if fiDepAcc.GetState() == depositPb.DepositState_CREATED {
			fiDepBalance, err = money.Sum(fiDepBalance, fiDepAcc.GetPrincipalAmount())
			if err != nil {
				logger.Error(ctx, "failure in summing Fi deposits account balances", zap.Error(err),
					zap.String(logger.ACTOR_ID_V2, actorId))
				return &feConnectedAccPb.GetAllDepositAccountsResponse{
					RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
						"error summing FI FD Balance: for actorId:%v ,err: %v", actorId, err.Error()))},
				}, nil
			}
		}
	}

	allUserDeclaredInvestments, invErr := s.getAllUserDeclaredInvestments(ctx, actorId)
	if invErr != nil {
		logger.Error(ctx, "failure in getting all investments by user", zap.Error(invErr),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return &feConnectedAccPb.GetAllDepositAccountsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
				"error fetching user declared invesntments: for actorId:%v ,err: %v", actorId, invErr.Error()))},
		}, nil
	}

	for _, userInvDetails := range allUserDeclaredInvestments.GetInvestmentDetails() {
		userInv := userInvDetails.GetInvestmentDeclaration()
		InvCurrValue := userInvDetails.GetCurrentValue()
		switch userInv.GetInstrumentType() {
		case types.InvestmentInstrumentType_FIXED_DEPOSIT:
			userFdInvTile, userInvTileErr := s.getUserInvestmentTile(userInv, InvCurrValue)
			if userInvTileErr != nil {
				logger.Error(ctx, "failure in getting user FD investment tile", zap.Error(userInvTileErr),
					zap.String(logger.ACTOR_ID_V2, actorId))
				return &feConnectedAccPb.GetAllDepositAccountsResponse{
					RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
						"error creating user FD investment tile: for actorId:%v ,err: %v", actorId, userInvTileErr.Error()))},
				}, nil
			}
			if userInv.GetMaturityDate() == nil || userInv.GetMaturityDate().GetSeconds() <= 0 || userInv.GetMaturityDate().AsTime().After(todayEOD) {
				activeUserFdInvestmentTiles = append(activeUserFdInvestmentTiles, userFdInvTile)
				fiDepBalance, err = money.Sum(fiDepBalance, InvCurrValue)
			} else {
				maturedUserFdInvestmentTiles = append(maturedUserFdInvestmentTiles, userFdInvTile)
			}
		case types.InvestmentInstrumentType_RECURRING_DEPOSIT:
			userRdInvTile, userInvTileErr := s.getUserInvestmentTile(userInv, InvCurrValue)
			if userInvTileErr != nil {
				logger.Error(ctx, "failure in getting user RD investment tile", zap.Error(userInvTileErr),
					zap.String(logger.ACTOR_ID_V2, actorId))
				return &feConnectedAccPb.GetAllDepositAccountsResponse{
					RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
						"error creating user RD investment tile: for actorId:%v ,err: %v", actorId, userInvTileErr.Error()))},
				}, nil
			}
			if userInv.GetMaturityDate() == nil || userInv.GetMaturityDate().GetSeconds() <= 0 || userInv.GetMaturityDate().AsTime().After(todayEOD) {
				activeUserRdInvestmentTiles = append(activeUserRdInvestmentTiles, userRdInvTile)
				fiDepBalance, err = money.Sum(fiDepBalance, InvCurrValue)
			} else {
				maturedUserRdInvestmentTiles = append(maturedUserRdInvestmentTiles, userRdInvTile)
			}
		}

		if err != nil {
			logger.Error(ctx, "failure in summing user declared investment", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return &feConnectedAccPb.GetAllDepositAccountsResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
					"error in summing user declared investment: for actorId:%v ,err: %v", actorId, err.Error()))},
			}, nil
		}
	}

	// nolint:ineffassign
	allFiDepAccTiles = append(allFiDepAccTiles, activeFiFDAccTiles...)
	// nolint:ineffassign
	allFiDepAccTiles = append(allFiDepAccTiles, maturedFiFDAccTiles...)
	// nolint:ineffassign
	allFiDepAccTiles = append(allFiDepAccTiles, activeFiSDAccTiles...)
	// nolint:ineffassign
	allFiDepAccTiles = append(allFiDepAccTiles, maturedFiSDAccTiles...)

	allAADepositAccDetails, aaDepAccErr := s.getAllAADepositAccounts(ctx, actorId)
	if aaDepAccErr != nil {
		logger.Error(ctx, "failure in getting all AA deposits accounts", zap.Error(aaDepAccErr),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return &feConnectedAccPb.GetAllDepositAccountsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
				"error fetching deposit accounts list : for actorId:%v ,err: %v", actorId, aaDepAccErr.Error()))},
		}, nil
	}

	for _, depAccDetails := range allAADepositAccDetails {
		switch depAccDetails.GetAccInstrumentType() {
		case beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT:
			aaRDAccDetails = append(aaRDAccDetails, depAccDetails)
		case beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT:
			aaFDAccDetails = append(aaFDAccDetails, depAccDetails)
		}
	}

	aaFDAccTiles, accErr = s.getHomeTileList(ctx, aaFDAccDetails, actorId)
	if accErr != nil {
		logger.Error(ctx, "failure in getting AA FD home account tile", zap.Error(accErr),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return &feConnectedAccPb.GetAllDepositAccountsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
				"error creating FD connected deposit account tile: for actorId:%v ,err: %v", actorId, accErr.Error()))},
		}, nil
	}

	activeAaFDAccTiles, maturedAaFDAccTiles = segregateActiveAndMaturedTiles(aaFDAccTiles)
	allAADepAccTiles = append(allAADepAccTiles, aaFDAccTiles...)

	aaRDAccTiles, accErr = s.getHomeTileList(ctx, aaRDAccDetails, actorId)
	if accErr != nil {
		logger.Error(ctx, "failure in getting AA RD home account tile", zap.Error(accErr),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return &feConnectedAccPb.GetAllDepositAccountsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
				"error creating RD connected deposit account tile: for actorId:%v ,err: %v", actorId, accErr.Error()))},
		}, nil
	}

	activeAaRDAccTiles, maturedAaRDAccTiles = segregateActiveAndMaturedTiles(aaRDAccTiles)
	allAADepAccTiles = append(allAADepAccTiles, aaRDAccTiles...)

	allDepBalance, err := getCollectiveBalance(allAADepAccTiles)
	if err != nil {
		logger.Error(ctx, "failure in getting AA collective balance", zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return &feConnectedAccPb.GetAllDepositAccountsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
				"error calculating total collective Deposit balance: for actorId:%v ,err: %v", actorId, accErr.Error()))},
		}, nil
	}
	collectiveBal := fiDepBalance
	if allDepBalance.GetBalance() != nil {
		collectiveBal, err = money.Sum(allDepBalance.GetBalance().GetBeMoney(), fiDepBalance)
		if err != nil {
			logger.Error(ctx, "failure in getting AA,FI collective balance", zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, actorId))
			return &feConnectedAccPb.GetAllDepositAccountsResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf(
					"error summing FI SD/FD Balance: for actorId:%v ,err: %v", actorId, err.Error()))},
			}, nil
		}
	}
	allDepBalance.Balance = types.GetFromBeMoney(collectiveBal)
	//nolint:gocritic
	aggregatedFdDepositsTiles := append(activeAaFDAccTiles, activeFiFDAccTiles...)

	// make a tile list of all matured deposits
	//nolint:gocritic
	allMaturedFiDepositTiles := append(maturedFiFDAccTiles, maturedFiSDAccTiles...)
	//nolint:gocritic
	allMaturedAaDepositTiles := append(maturedAaFDAccTiles, maturedAaRDAccTiles...)
	//nolint:gocritic
	allMaturedUserInvestmentTiles := append(maturedUserFdInvestmentTiles, maturedUserRdInvestmentTiles...)
	//nolint:gocritic
	allMaturedTiles := append(allMaturedFiDepositTiles, allMaturedUserInvestmentTiles...)
	allMaturedTiles = append(allMaturedTiles, allMaturedAaDepositTiles...)

	allDepBalance.BalanceText.FontStyle = &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_3XL}
	allDepBalance.BalanceText.DisplayValue = &commontypes.Text_PlainString{PlainString: getDisplayMoneyWithSymbol(collectiveBal)}
	var depositAccSections []*feConnectedAccPb.DepositAccountsSection
	var depAccSec *feConnectedAccPb.DepositAccountsSection
	//nolint:gocritic
	allActiveFdTiles := append(activeUserFdInvestmentTiles, aggregatedFdDepositsTiles...)
	if len(allActiveFdTiles) != 0 {
		depAccSec = getDepositAccSection(FixedDeposits, accountPb.Type_FIXED_DEPOSIT,
			allActiveFdTiles,
			&ui.IconTextComponent{
				LeftIcon: &commontypes.Image{
					ImageUrl: NetWorthDepositSectionInfoCtaUrl,
					Width:    NetWorthDepositSectionInfoCtaImageWidth,
					Height:   NetWorthDepositSectionInfoCtaImageHeight,
				},
			})
		depositAccSections = append(depositAccSections, depAccSec)
	}
	if len(activeFiSDAccTiles) != 0 {
		depAccSec = getDepositAccSection(SmartDeposits, accountPb.Type_SMART_DEPOSIT,
			activeFiSDAccTiles,
			&ui.IconTextComponent{
				RightIcon: &commontypes.Image{
					ImageUrl: NetWorthDepositSectionInfoCtaUrl,
					Width:    NetWorthDepositSectionInfoCtaImageWidth,
					Height:   NetWorthDepositSectionInfoCtaImageHeight,
				},
				Deeplink: nil,
			})
		depositAccSections = append(depositAccSections, depAccSec)
	}
	//nolint:gocritic
	allActiveRdTiles := append(activeUserRdInvestmentTiles, activeAaRDAccTiles...)
	if len(allActiveRdTiles) != 0 {
		depAccSec = getDepositAccSection(RecurringDeposits, accountPb.Type_RECURRING_DEPOSIT,
			append(activeUserRdInvestmentTiles, activeAaRDAccTiles...),
			nil)
		depositAccSections = append(depositAccSections, depAccSec)
	}
	if len(allMaturedTiles) != 0 {
		depAccSec = getDepositAccSection(MaturedAndClosedDeposits, accountPb.Type_TYPE_UNSPECIFIED,
			allMaturedTiles,
			nil)
		depositAccSections = append(depositAccSections, depAccSec)
	}
	return &feConnectedAccPb.GetAllDepositAccountsResponse{
		RespHeader:              &header.ResponseHeader{Status: rpc.StatusOk()},
		DepositAccountsSections: depositAccSections,
		TotalBalance:            allDepBalance,
		TotalBalanceText: &commontypes.Text{
			FontColor:    TotalDepositedTextFontColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: TotalDepositedText},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_3},
		},
		ConnectDepositAccCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CONTINUE,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			IconUrl:      ConnectMoreDepositsAccIconUrl,
			Text:         ConnectMoreDepositsAccText,
			Deeplink:     deeplink_builder.ManualAssetNewFormDeeplink(networthBePb.AssetType_ASSET_TYPE_FIXED_DEPOSITS),
		},
	}, nil
}

func getDepositAccSection(sectionName string, accType accountPb.Type, accTiles []*feConnectedAccPb.HomeAccountTile, infoCta *ui.IconTextComponent) *feConnectedAccPb.DepositAccountsSection {
	if len(accTiles) == 0 {
		return nil
	}
	return &feConnectedAccPb.DepositAccountsSection{
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: sectionName},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
			},
		},
		Type:                accType,
		HomeAccountTileList: accTiles,
		InfoCta:             infoCta,
	}
}

// segregates tiles into active and matured ones. if maturity detail is not available, tile is treated as active.
func segregateActiveAndMaturedTiles(allTiles []*feConnectedAccPb.HomeAccountTile) (active, matured []*feConnectedAccPb.HomeAccountTile) {
	var todayEOD = datetime.EndOfDay(time.Now())
	for _, tile := range allTiles {
		var maturityDate *timestamppb.Timestamp
		switch tile.GetAccountDetailMeta().(type) {
		case *feConnectedAccPb.HomeAccountTile_DepositAccountMeta:
			maturityDate = nil
		case *feConnectedAccPb.HomeAccountTile_TermDepositMeta:
			maturityDate = tile.GetTermDepositMeta().GetMaturityDate()
		case *feConnectedAccPb.HomeAccountTile_RecurringDepositMeta:
			maturityDate = tile.GetRecurringDepositMeta().GetMaturityDate()
		}

		// adding maturity date seconds <= 0 check since data from AA can be wrong
		if maturityDate == nil || maturityDate.GetSeconds() <= 0 || maturityDate.AsTime().After(todayEOD) {
			active = append(active, tile)
		} else {
			matured = append(matured, tile)
		}
	}

	return active, matured
}

//nolint:unparam, funlen
func (s *Service) getUserInvestmentTile(userDecInvestment *model.InvestmentDeclaration, invCurrValue *moneyPb.Money) (*feConnectedAccPb.HomeAccountTile, error) {
	var (
		investmentTenure int
		depName          string
		yesterdayEOD     = datetime.EndOfDay(time.Now().Add(-24 * time.Hour))
	)
	switch userDecInvestment.GetDeclarationDetails().GetDetails().(type) {
	case *model.OtherDeclarationDetails_FixedDepositDeclarationDetails:
		depName = userDecInvestment.GetDeclarationDetails().GetFixedDepositDeclarationDetails().GetDepositName()
	case *model.OtherDeclarationDetails_RecurringDepositDeclarationDetails:
		depName = userDecInvestment.GetDeclarationDetails().GetRecurringDepositDeclarationDetails().GetDepositName()
	}
	maturityDate := userDecInvestment.GetMaturityDate().AsTime()
	openingData := userDecInvestment.GetInvestedAt().AsTime()
	if openingData.Before(maturityDate) {
		investmentTenure = int((maturityDate.Sub(openingData)).Hours() / 24)
	}
	interestRate := userDecInvestment.GetInterestRate()
	tileSubHeading := fmt.Sprintf("%s %s • %s %s", strconv.Itoa(investmentTenure), Days, fmt.Sprintf("%v", interestRate), Interest)
	if userDecInvestment.GetMaturityDate() != nil && userDecInvestment.GetMaturityDate().AsTime().Before(yesterdayEOD) {
		tileSubHeading = fmt.Sprintf("%s %s", ClosedOnText, datetime.DateToString(getDateFromTime(userDecInvestment.GetMaturityDate().AsTime()),
			dateFormat, datetime.IST))
	}
	return &feConnectedAccPb.HomeAccountTile{
		AccountId:             userDecInvestment.GetId(),
		TileClickToastMessage: ToastMessageOnTapAccTile,
		AccountSource:         feConnectedAccPb.AccountSource_ACCOUNT_SOURCE_MANUALLY_ADDED_ACC,
		TileBgColor:           AccountTileDefaultBgColor,
		AccTileSubHeading: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    AccTileSubHeadingText,
					DisplayValue: &commontypes.Text_PlainString{PlainString: tileSubHeading},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
				},
			},
		},
		AccTileHeading: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    AccTileHeadingText,
					DisplayValue: &commontypes.Text_PlainString{PlainString: depName},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
				},
			},
		},
		AccTileLogo: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: UserDeclaredInvestmentIconUrl,
					},
					ImageType: commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{
						Width:  FDDepAccTileLogoWidth,
						Height: FdDepAccTileLogoHeight,
					},
				},
			},
		},
		BalanceMeta: &feConnectedAccPb.BalanceMeta{
			Balance:           types.GetFromBeMoney(invCurrValue),
			BalanceVisibility: feConnectedAccPb.BalanceVisibility_BALANCE_VISIBILITY_STANDARD,
			ProgressCovered:   -1,
			BalanceText: &commontypes.Text{
				FontColor: AccTileBalanceTextFontColor,
				DisplayValue: &commontypes.Text_PlainString{PlainString: getDisplayMoneyWithSymbol(
					invCurrValue)},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
			},
		},
		DeeplinkV2: deeplink_builder.ManualAssetPrefilledFormDeeplink(userDecInvestment.GetExternalId()),
	}, nil
}

//nolint:unparam, funlen
func (s *Service) getFiDepAccTiles(fiFdAccDetails *depositPb.DepositAccount, goalDetails map[string]*depositPb.ListDepositAccountsResponse_GoalDetails) (*feConnectedAccPb.HomeAccountTile, error) {
	var (
		tileBGColor        string
		displayTag         *ui.IconTextComponent
		tileSubHeadingText string
		tileLogo           *commontypes.VisualElement
		progressCovered    float32
	)

	switch fiFdAccDetails.GetType() {
	case accountPb.Type_SMART_DEPOSIT:
		tileBGColor = FiSDAccTileBgColor
		// nolint:ineffassign
		tileSubHeadingText = MaturingSoonText
		tileLogo = &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: fiFdAccDetails.GetDepositIcon().GetImageUrl(),
					},
					ImageType: commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{
						Width:  SdDepAccTileLogoWidth,
						Height: SdDepAccTileLogoHeight,
					},
				},
			},
		}
		fiFdDepStatus := getDepositStatusText(&feDepositPb.DepositAccount{
			State:        deposit.FromBeToFeDepositState(fiFdAccDetails.GetState()),
			MaturityDate: fiFdAccDetails.GetMaturityDate(),
			ClosureInfo: &feDepositPb.ClosureInfo{
				OriginalDepositBalance: types.GetFromBeMoney(fiFdAccDetails.GetClosureInfo().GetOriginalDepositBalance()),
				InterestAmount:         types.GetFromBeMoney(fiFdAccDetails.GetClosureInfo().GetInterestAmount()),
				GrossAmount:            types.GetFromBeMoney(fiFdAccDetails.GetClosureInfo().GetGrossAmount()),
				TdsAmount:              types.GetFromBeMoney(fiFdAccDetails.GetClosureInfo().GetTdsAmount()),
				NetAmount:              types.GetFromBeMoney(fiFdAccDetails.GetClosureInfo().GetNetAmount()),
				PreClosureAmount:       types.GetFromBeMoney(fiFdAccDetails.GetClosureInfo().GetPreClosureAmount()),
				CloseValueDate:         fiFdAccDetails.GetClosureInfo().GetCloseValueDate(),
			},
			GoalDetails: getGoalDetailsFromMap(fiFdAccDetails.GetId(), goalDetails),
		})
		tileSubHeadingText = fiFdDepStatus
		progressCovered = 0.5
		if fiFdAccDetails.GetState() == depositPb.DepositState_CLOSED || fiFdAccDetails.GetState() == depositPb.DepositState_PRECLOSED {
			progressCovered = 1
		}

	case accountPb.Type_FIXED_DEPOSIT:
		tileBGColor = FiFDAccTileBgColor
		displayTag = &ui.IconTextComponent{
			LeftIcon: &commontypes.Image{
				ImageUrl: FiFDAccTileDisplayTag,
				Width:    FiFDAccTileDisplayTagWidth,
				Height:   FiFDAccTileDisplayTagHeight,
			},
		}
		tileLogo = &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: fiFdAccDetails.GetDepositIcon().GetImageUrl(),
					},
					ImageType: commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{
						Width:  FDDepAccTileLogoWidth,
						Height: FdDepAccTileLogoHeight,
					},
				},
			},
		}
		progressCovered = -1
		tileSubHeadingText = fmt.Sprintf("%s %s", MaturingOnText,
			fiFdAccDetails.GetMaturityDate().AsTime().In(datetime.IST).Format(dateFormat))

		if fiFdAccDetails.GetRenewInfo() != nil && fiFdAccDetails.GetRenewInfo().GetIsAutoRenewable() {
			tileSubHeadingText = fmt.Sprintf("%s %s", RenewsOnText,
				fiFdAccDetails.GetMaturityDate().AsTime().In(datetime.IST).Format(dateFormat))
		}
		if fiFdAccDetails.GetState() == depositPb.DepositState_CLOSED {
			tileSubHeadingText = fmt.Sprintf("%s %s", MaturedOnText,
				fiFdAccDetails.GetMaturityDate().AsTime().In(datetime.IST).Format(dateFormat))
		} else if fiFdAccDetails.GetState() == depositPb.DepositState_PRECLOSED {
			tileSubHeadingText = fmt.Sprintf("%s %s", ClosedOnText,
				fiFdAccDetails.GetClosureInfo().GetCloseValueDate().AsTime().In(datetime.IST).Format(dateFormat))
		}

	default:

	}

	return &feConnectedAccPb.HomeAccountTile{
		AccountId:          fiFdAccDetails.GetId(),
		AccountSource:      feConnectedAccPb.AccountSource_ACCOUNT_SOURCE_FI_CREATED_ACC,
		TileBgColor:        tileBGColor,
		DataSyncDisplayTag: displayTag,
		AccTileSubHeading: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    AccTileSubHeadingText,
					DisplayValue: &commontypes.Text_PlainString{PlainString: tileSubHeadingText},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
				},
			},
		},
		AccTileHeading: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    AccTileHeadingText,
					DisplayValue: &commontypes.Text_PlainString{PlainString: fiFdAccDetails.GetName()},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
				},
			},
		},
		AccTileLogo: tileLogo,
		BalanceMeta: &feConnectedAccPb.BalanceMeta{
			Balance:           types.GetFromBeMoney(fiFdAccDetails.GetPrincipalAmount()),
			BalanceVisibility: feConnectedAccPb.BalanceVisibility_BALANCE_VISIBILITY_STANDARD,
			ProgressCovered:   progressCovered,
			BalanceText: &commontypes.Text{
				FontColor: AccTileBalanceTextFontColor,
				DisplayValue: &commontypes.Text_PlainString{PlainString: getDisplayMoneyWithSymbol(
					fiFdAccDetails.GetPrincipalAmount())},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
			}},
		AccountType: fiFdAccDetails.GetType().String(),
	}, nil
}

func (s *Service) getAllFiDepositAccounts(ctx context.Context, actorId string) (*depositPb.ListDepositAccountsResponse, error) {
	allDepAccounts, err := s.depositsClient.ListDepositAccounts(ctx, &depositPb.ListDepositAccountsRequest{
		ActorId: actorId,
		Types:   []accountPb.Type{accountPb.Type_SMART_DEPOSIT, accountPb.Type_FIXED_DEPOSIT},
		States: []depositPb.DepositState{depositPb.DepositState_PRECLOSED, depositPb.DepositState_CLOSED,
			depositPb.DepositState_CREATED},
		Provenances: []depositPb.DepositAccountProvenance{depositPb.DepositAccountProvenance_USER_APP, depositPb.DepositAccountProvenance_REWARDS_APP,
			depositPb.DepositAccountProvenance_VENDOR_RECONCILIATION, depositPb.DepositAccountProvenance_CREDIT_CARD},
	})
	if getAllDepAccountsErr := epifigrpc.RPCError(allDepAccounts, err); getAllDepAccountsErr != nil {
		if allDepAccounts.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "no deposit account for actor", zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, nil
		}
		logger.Error(ctx, "error in fetching list of deposit accounts from Deposits Service", zap.Error(getAllDepAccountsErr))
		return nil, getAllDepAccountsErr
	}
	return allDepAccounts, nil
}

func (s *Service) getAllAADepositAccounts(ctx context.Context, actorId string) ([]*beCaExtPb.AccountDetails, error) {
	depositsAccList, err := s.beConnectedAccClient.GetAllAccounts(ctx, &beCaPb.GetAllAccountsRequest{
		PageContext: &rpc.PageContextRequest{
			PageSize: 100,
		},
		ActorId:           actorId,
		AccountFilterList: []beCaExtPb.AccountFilter{beCaExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE},
		AccInstrumentTypeList: []beCaEnumPb.AccInstrumentType{beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT,
			beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT},
	})
	if rpcErr := epifigrpc.RPCError(depositsAccList, err); rpcErr != nil {
		if depositsAccList.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "No RD/TD deposit type account is connected for the user,", zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, nil
		}
		return nil, errors.Wrap(rpcErr, fmt.Sprint("error in fetching list of RD/TD connected accounts", zap.String(logger.ACTOR_ID_V2, actorId)))
	}
	return depositsAccList.GetAccountDetailsList(), nil
}

func (s *Service) getAllUserDeclaredInvestments(ctx context.Context, actorId string) (*beNetWorthPb.GetInvestmentDeclarationsResponse, error) {
	userDeclaredInvestmentList, err := s.netWorth.GetInvestmentDeclarations(ctx, &beNetWorthPb.GetInvestmentDeclarationsRequest{
		PageContext: &rpc.PageContextRequest{
			PageSize: 100,
		},
		ActorId: actorId,
		InstrumentTypes: []types.InvestmentInstrumentType{types.InvestmentInstrumentType_FIXED_DEPOSIT,
			types.InvestmentInstrumentType_RECURRING_DEPOSIT},
	})
	if rpcErr := epifigrpc.RPCError(userDeclaredInvestmentList, err); rpcErr != nil {
		if userDeclaredInvestmentList.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "No DeclaredInvestments account declared for the user,", zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, nil
		}
		return nil, errors.Wrap(rpcErr, fmt.Sprint("error in fetching list of declared investments", zap.String(logger.ACTOR_ID_V2, actorId)))
	}
	return userDeclaredInvestmentList, nil
}

func getDateFromTime(t time.Time) *date.Date {
	year, month, day := t.In(datetime.IST).Date()
	return &date.Date{
		Year:  int32(year),
		Month: int32(month),
		Day:   int32(day),
	}
}

func getDepositStatusText(depositAccount *feDepositPb.DepositAccount) string {

	if depositAccount.GetState() == feDepositPb.DepositState_CLOSED {
		return fmt.Sprintf("Matured on %s",
			depositAccount.GetMaturityDate().AsTime().In(datetime.IST).Format(dateFormat))
	}

	if depositAccount.GetState() == feDepositPb.DepositState_PRECLOSED {
		return fmt.Sprintf("Closed on %s",
			depositAccount.GetClosureInfo().GetCloseValueDate().AsTime().In(datetime.IST).Format(dateFormat))
	}

	depositDurationLeft := time.Until(depositAccount.GetMaturityDate().AsTime().In(datetime.IST))
	depositDurationLeftInText := utils.GetDurationInText(depositDurationLeft)

	if depositAccount.GetGoalDetails() == nil {
		return fmt.Sprintf("%s left", depositDurationLeftInText)

	}

	targetAmount := depositAccount.GetGoalDetails().GetTargetAmount().GetBeMoney()
	targetAmountString := money.ToDisplayStringInIndianFormat(targetAmount, 0, true)
	return fmt.Sprintf("🎯 %s • %s left", targetAmountString, depositDurationLeftInText)
}

func getGoalDetailsFromMap(depositAccountId string,
	depositIdToGoalDetails map[string]*depositPb.ListDepositAccountsResponse_GoalDetails) *deposit.DepositGoalDetails {

	goalDetail, ok := depositIdToGoalDetails[depositAccountId]
	if !ok {
		return nil
	}

	return &deposit.DepositGoalDetails{
		GoalId:       goalDetail.GetGoalId(),
		TargetAmount: types.GetFromBeMoney(goalDetail.GetTargetAmount()),
	}
}

func (s *Service) GetBanks(ctx context.Context, req *feConnectedAccPb.GetBanksRequest) (*feConnectedAccPb.GetBanksResponse, error) {
	caFlowName := goUtils.Enum(req.GetCaFlowName(), beCaEnumPb.CAFlowName_value, beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT)
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	allFipMetasResp, err := s.beConnectedAccClient.GetAllFipMetas(ctx, &beCaPb.GetAllFipMetasRequest{CaFlowName: caFlowName, AppPlatform: appPlatform})
	if rpcErr := epifigrpc.RPCError(allFipMetasResp, err); rpcErr != nil {
		return &feConnectedAccPb.GetBanksResponse{RespHeader: &header.ResponseHeader{Status: allFipMetasResp.GetStatus()}}, nil
	}

	// get default aa entity for actor based on user group and actor hash bucket
	getAaEntityResp, getAaEntityErr := s.beConnectedAccClient.GetAaEntityForConnect(ctx, &beCaPb.GetAaEntityForConnectRequest{
		AppPlatform: req.GetReq().GetAuth().GetDevice().GetPlatform(),
		AppVersion:  req.GetReq().GetAuth().GetDevice().GetAppVersion(),
		ActorId:     req.GetReq().GetAuth().GetActorId(),
	})
	if rpcErr := epifigrpc.RPCError(getAaEntityResp, getAaEntityErr); rpcErr != nil {
		return &feConnectedAccPb.GetBanksResponse{RespHeader: &header.ResponseHeader{Status: getAaEntityResp.GetStatus()}}, nil
	}

	defaultAaEntity := getAaEntityResp.GetAaEntity()

	var (
		allBanksInfoList     []*feConnectedAccPb.GetBanksResponse_BankInfo
		popularBanksInfoList []*feConnectedAccPb.GetBanksResponse_BankInfo
		fipIssueList         []string
	)

	for _, fipMeta := range allFipMetasResp.GetFipMetaList() {
		fipAaEntity := fipMeta.GetPreferredAaEntity()
		if fipAaEntity == beCaEnumPb.AaEntity_AA_ENTITY_UNSPECIFIED {
			fipAaEntity = defaultAaEntity
		}

		fipIssue := convertBankIssueTypeToFeString(fipMeta.GetBankIssueType())
		if fipIssue != "" {
			fipIssueList = append(fipIssueList, fipIssue)
		}
		if fipMeta.GetIsPopular() {
			popularBanksInfoList = append(popularBanksInfoList, getBankDisplayInfo(fipMeta.GetFipId(), fipMeta.GetDisplayName(),
				fipMeta.GetLogoUrl(), fipIssue, colors.ColorSnow, 44, 44, commontypes.FontStyle_BODY_3, fipAaEntity))
		}
		allBanksInfoList = append(allBanksInfoList, getBankDisplayInfo(fipMeta.GetFipId(), fipMeta.GetDisplayName(),
			fipMeta.GetLogoUrl(), fipIssue, colors.ColorSnow, 40, 40, commontypes.FontStyle_BODY_3, fipAaEntity))

	}

	response := &feConnectedAccPb.GetBanksResponse{
		RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
		PopularBankListTitle: commontypes.GetTextFromStringFontColourFontStyle(PopularBanksTitle, colors.ColorSnow, commontypes.FontStyle_HEADLINE_S),
		AllBankListTitle:     commontypes.GetTextFromStringFontColourFontStyle(AllBanksTitle, colors.ColorSnow, commontypes.FontStyle_HEADLINE_S),
		DefaultIssueMessage:  getIssueMessage(DefaultErrorTitle, DefaultErrorSubTitle),
		FipIssue:             bankIssueToDisplayMessageMap(fipIssueList),
		AllBankList:          allBanksInfoList,
		PopularBankList:      popularBanksInfoList,
		FinvuTnc:             s.getWealthTncCheckboxItem(beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU),
		OnemoneyTnc:          s.getWealthTncCheckboxItem(beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY),
	}
	return response, nil
}

func (s *Service) GetSdkDeeplink(ctx context.Context, req *feConnectedAccPb.GetSdkDeeplinkRequest) (*feConnectedAccPb.GetSdkDeeplinkResponse, error) {
	caFlowName := goUtils.Enum(req.GetCaFlowName(), beCaEnumPb.CAFlowName_value, beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT)
	caDlData, err := s.gatherDataForCaDeeplink(ctx, req.GetReq(), caFlowName.String(), req.GetFipId())
	if err != nil {
		logger.Error(ctx, "failed to gather data for CA deeplink while generating SDK deeplink", zap.Error(err))
		return &feConnectedAccPb.GetSdkDeeplinkResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("failed to gather data for CA deeplink while generating SDK deeplink: %v", err))},
		}, nil
	}

	sdkDeeplink, err := s.getDeeplinkToStartSDK(ctx, caDlData.usr, req.GetReq(), caDlData.getAaEntityResp.GetAaEntity(), caFlowName.String(), req.GetCaFlowId(), []string{req.GetFipId()})
	if err != nil {
		logger.Error(ctx, "failed to get deeplink to start CA sdk while generating SDK deeplink", zap.Error(err))
		return &feConnectedAccPb.GetSdkDeeplinkResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("failed to get deeplink to start CA sdk while generating SDK deeplink: %v", err))},
		}, nil
	}
	return &feConnectedAccPb.GetSdkDeeplinkResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Deeplink:   sdkDeeplink,
	}, nil
}

func (s *Service) GetConnectedAccountSDKVersion(ctx context.Context, actorId string, isV2FlowEnabled bool, appPlatform commontypes.Platform, appVersion uint32, caFlowName beCaEnumPb.CAFlowName) deeplinkv2.Version {
	enable, err := s.isFeatureEnabled(ctx, actorId, types.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW)
	if err != nil {
		logger.Error(ctx, "failed to get feature enabled state", zap.Error(err))
		enable = false
	}
	if enable {
		switch caFlowName {
		case beCaEnumPb.CAFlowName_CA_FLOW_NAME_AA_SALARY,
			beCaEnumPb.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION:
			// Fall through to v2/v1 logic for salary flows
		default:
			return deeplinkv2.Version_VERSION_V3
		}
	}
	if isV2FlowEnabled {
		return deeplinkv2.Version_VERSION_V2
	}
	return deeplinkv2.Version_VERSION_V1
}
