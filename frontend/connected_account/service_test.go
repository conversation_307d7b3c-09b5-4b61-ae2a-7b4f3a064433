//nolint:goconst,goimports
package connected_account

import (
	"context"
	"flag"
	"fmt"
	"os"
	"reflect"
	"testing"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"

	deeplinkBuilderMocks "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder/mocks"

	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	insightsPkg "github.com/epifi/gamma/insights/pkg"

	"github.com/epifi/gamma/api/frontend/deeplinkv2"
	typesV2 "github.com/epifi/gamma/api/typesv2"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	money2 "github.com/epifi/be-common/pkg/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountBalanceMock "github.com/epifi/gamma/api/accounts/balance/mocks"
	feConnectedAccountCommonPb "github.com/epifi/gamma/api/frontend/connected_account/common"
	feCaFeaturesFiToFiPb "github.com/epifi/gamma/api/frontend/connected_account/features"
	caDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mocks4 "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/frontend/connected_account/factory"
	"github.com/epifi/gamma/frontend/connected_account/factory/processor"
	mocks7 "github.com/epifi/gamma/frontend/connected_account/fi_to_fi_helper/mocks"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	mock_release "github.com/epifi/gamma/pkg/feature/release/mocks"
	pkgUser "github.com/epifi/gamma/pkg/user"
	pkgUserMocks "github.com/epifi/gamma/pkg/user/mocks"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"

	actorPb "github.com/epifi/gamma/api/actor"
	mocks3 "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/auth"
	beCaPb "github.com/epifi/gamma/api/connected_account"
	beCaEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	beCaExtPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/connected_account/mocks"
	"github.com/epifi/gamma/api/consent"
	mocks6 "github.com/epifi/gamma/api/consent/mocks"
	feConnectedAccPb "github.com/epifi/gamma/api/frontend/connected_account"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkpb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	homePb "github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/savings"
	mocks5 "github.com/epifi/gamma/api/savings/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	homeTypesPb "github.com/epifi/gamma/api/typesv2/home"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	mocks2 "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	beCaProcessor "github.com/epifi/gamma/frontend/connected_account/factory/processor"
	"github.com/epifi/gamma/frontend/test"
	femocks "github.com/epifi/gamma/frontend/test/mocks"
	"github.com/epifi/gamma/pkg/feature/release"
)

var (
	conf   *config.ConnectedAccount
	feConf *config.Config
	gconf  *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	feConf, gconf, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_CreateBankPreference(t *testing.T) {
	ctr := gomock.NewController(t)
	mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
	defer ctr.Finish()

	var bankList1 = []typesPb.Bank{typesPb.Bank_CITI, typesPb.Bank_BARODA}
	var bankList3 = []typesPb.Bank{typesPb.Bank_HSBC}

	type fields struct {
		beConnectedAccClient beCaPb.ConnectedAccountClient
		connectedAccountConf *config.ConnectedAccount
	}
	type args struct {
		ctx   context.Context
		req   *feConnectedAccPb.CreateBankPreferenceRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *feConnectedAccPb.CreateBankPreferenceResponse
		wantErr bool
	}{
		{
			name: "1- success, new banks not yet enabled",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.CreateBankPreferenceRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					BankList: bankList1,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().CreateBankPreference(gomock.Any(), &beCaPb.CreateBankPreferenceRequest{
						ActorId:  "actor-id-1",
						BankList: bankList1,
					}).Return(&beCaPb.CreateBankPreferenceResponse{Status: rpcPb.StatusOk()}, nil),
				},
			},
			want: &feConnectedAccPb.CreateBankPreferenceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "1- success, new banks not yet enabled",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.CreateBankPreferenceRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					BankList: bankList1,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().CreateBankPreference(gomock.Any(), &beCaPb.CreateBankPreferenceRequest{
						ActorId:  "actor-id-1",
						BankList: bankList1,
					}).Return(&beCaPb.CreateBankPreferenceResponse{Status: rpcPb.StatusOk()}, nil),
				},
			},
			want: &feConnectedAccPb.CreateBankPreferenceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "2 - success, empty bank list",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.CreateBankPreferenceRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
						},
					},
					BankList: []typesPb.Bank{},
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().CreateBankPreference(gomock.Any(), &beCaPb.CreateBankPreferenceRequest{
						ActorId:  "actor-id-2",
						BankList: []typesPb.Bank{},
					}).Return(&beCaPb.CreateBankPreferenceResponse{Status: rpcPb.StatusOk()}, nil),
				},
			},
			want: &feConnectedAccPb.CreateBankPreferenceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "3- failed, error creating bank preferences",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.CreateBankPreferenceRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-3",
						},
					},
					BankList: bankList3,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().CreateBankPreference(gomock.Any(), &beCaPb.CreateBankPreferenceRequest{
						ActorId:  "actor-id-3",
						BankList: bankList3,
					}).Return(&beCaPb.CreateBankPreferenceResponse{Status: rpcPb.StatusInternal()}, nil),
				},
			},
			want: &feConnectedAccPb.CreateBankPreferenceResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				beConnectedAccClient: tt.fields.beConnectedAccClient,
				connectedAccountConf: tt.fields.connectedAccountConf,
			}
			got, err := s.CreateBankPreference(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBankPreference() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("CreateBankPreference() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestService_InitiateConsent(t *testing.T) {
	ctr := gomock.NewController(t)
	mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
	mockUsersClient := mocks2.NewMockUsersClient(ctr)
	mockActorClient := mocks3.NewMockActorClient(ctr)
	mockCaFlowFactory := femocks.NewMockICaFlowDLFactory(ctr)
	defer ctr.Finish()

	const (
		om          = feConnectedAccPb.AaEntity_AA_ENTITY_AA_ONE_MONEY
		finvu       = feConnectedAccPb.AaEntity_AA_ENTITY_AA_FINVU
		consentFlow = feConnectedAccPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_CONSENT_FLOW
		authFlow    = feConnectedAccPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_AA_AUTH_FLOW
	)

	type fields struct {
		beConnectedAccClient beCaPb.ConnectedAccountClient
		connectedAccountConf *config.ConnectedAccount
		beUserClient         userPb.UsersClient
		beActorClient        actorPb.ActorClient
		caFlowDLFactory      factory.ICaFlowDLFactory
	}
	type args struct {
		ctx     context.Context
		request *feConnectedAccPb.InitiateConsentRequest
		mocks   []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *feConnectedAccPb.InitiateConsentResponse
		wantErr bool
	}{
		{
			name: "1 - failed, finvu consent flow, no vua",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					Vua:                   "",
					AaEntity:              finvu,
					ConsentRequestPurpose: consentFlow,
					CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "2 - failed, om consent flow, no vua",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
						},
					},
					Vua:                   "",
					AaEntity:              om,
					ConsentRequestPurpose: consentFlow,
					CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER.String(),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "3 - failed, om auth flow, no vua",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-3",
						},
					},
					Vua:                   "",
					AaEntity:              om,
					ConsentRequestPurpose: authFlow,
					CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER.String(),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "4 - success, finvu auth flow, no vua",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-4",
						},
					},
					Vua:                   "",
					AaEntity:              finvu,
					ConsentRequestPurpose: authFlow,
					CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER.String(),
				},
				mocks: []interface{}{
					mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: "actor-id-4"}).
						Return(&actorPb.GetActorByIdResponse{
							Status: rpcPb.StatusOk(),
							Actor: &typesPb.Actor{
								Id:       "actor-id-4",
								EntityId: "actor-entity-id-4",
							},
						}, nil),
					mockUsersClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_Id{
							Id: "actor-entity-id-4",
						},
					}).Return(&userPb.GetUserResponse{
						Status: rpcPb.StatusOk(),
						User: &userPb.User{
							Id: "actor-entity-id-4",
							Profile: &userPb.Profile{
								PhoneNumber: &commontypes.PhoneNumber{
									NationalNumber: 4,
									CountryCode:    91,
								},
							},
						},
					}, nil),
					mockCaClient.EXPECT().StartConsentFlow(gomock.Any(), &beCaPb.StartConsentFlowRequest{
						ActorId:                       "actor-id-4",
						Vua:                           "4@finvu",
						AaEntity:                      getBeAaEntity(finvu),
						ConsentRequestPurpose:         getBeConsentRequestPurpose(authFlow),
						CaFlowName:                    beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER,
						NumOfConsentHandlesToGenerate: 1,
					}).Return(
						&beCaPb.StartConsentFlowResponse{
							Status:            rpcPb.StatusOk(),
							ConsentHandle:     "consent-handle-4",
							ConsentHandleList: []string{"consent-handle-4"},
						}, nil),
					mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(mockCaClient), nil),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "5 - success, finvu auth flow, with vua",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-5",
						},
					},
					Vua:                   "5@finvu",
					AaEntity:              finvu,
					ConsentRequestPurpose: authFlow,
					CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER.String(),
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().StartConsentFlow(gomock.Any(), &beCaPb.StartConsentFlowRequest{
						ActorId:                       "actor-id-5",
						Vua:                           "5@finvu",
						AaEntity:                      getBeAaEntity(finvu),
						ConsentRequestPurpose:         getBeConsentRequestPurpose(authFlow),
						CaFlowName:                    beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER,
						NumOfConsentHandlesToGenerate: 1,
					}).Return(
						&beCaPb.StartConsentFlowResponse{
							Status:            rpcPb.StatusOk(),
							ConsentHandle:     "consent-handle-5",
							ConsentHandleList: []string{"consent-handle-5"},
						}, nil),
					mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(mockCaClient), nil),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "6 - success, om consent flow, with vua",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-6",
						},
					},
					Vua:                   "6@onemoney",
					AaEntity:              om,
					ConsentRequestPurpose: consentFlow,
					CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER.String(),
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().StartConsentFlow(gomock.Any(), &beCaPb.StartConsentFlowRequest{
						ActorId:                       "actor-id-6",
						Vua:                           "6@onemoney",
						AaEntity:                      getBeAaEntity(om),
						ConsentRequestPurpose:         getBeConsentRequestPurpose(consentFlow),
						CaFlowName:                    beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER,
						NumOfConsentHandlesToGenerate: 1,
					}).Return(
						&beCaPb.StartConsentFlowResponse{
							Status:            rpcPb.StatusOk(),
							ConsentHandle:     "consent-handle-5",
							ConsentHandleList: []string{"consent-handle-5"},
						}, nil),
					mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(mockCaClient), nil),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "7 - success, finvu consent flow, with vua",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-7",
						},
					},
					Vua:                   "7@finvu",
					AaEntity:              finvu,
					ConsentRequestPurpose: consentFlow,
					CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER.String(),
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().StartConsentFlow(gomock.Any(), &beCaPb.StartConsentFlowRequest{
						ActorId:                       "actor-id-7",
						Vua:                           "7@finvu",
						AaEntity:                      getBeAaEntity(finvu),
						ConsentRequestPurpose:         getBeConsentRequestPurpose(consentFlow),
						CaFlowName:                    beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER,
						NumOfConsentHandlesToGenerate: 1,
					}).Return(
						&beCaPb.StartConsentFlowResponse{
							Status:            rpcPb.StatusOk(),
							ConsentHandle:     "consent-handle-7",
							ConsentHandleList: []string{"consent-handle-7"},
						}, nil),
					mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(mockCaClient), nil),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "8 - failed, om auth flow, with vua",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-8",
						},
					},
					Vua:                   "8@onemoney",
					AaEntity:              om,
					ConsentRequestPurpose: authFlow,
					CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_ONBOARDING_SCREENER.String(),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "9 - success, finvu consent flow, if caFlowName is nil in request then StartConsentFlow should be called with CaFlowName Connected Account default flow",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-9",
						},
					},
					Vua:                   "9@finvu",
					AaEntity:              finvu,
					ConsentRequestPurpose: consentFlow,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().StartConsentFlow(gomock.Any(), &beCaPb.StartConsentFlowRequest{
						ActorId:                       "actor-id-9",
						Vua:                           "9@finvu",
						AaEntity:                      getBeAaEntity(finvu),
						ConsentRequestPurpose:         getBeConsentRequestPurpose(consentFlow),
						CaFlowName:                    beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
						NumOfConsentHandlesToGenerate: 1,
					}).Return(
						&beCaPb.StartConsentFlowResponse{
							Status:            rpcPb.StatusOk(),
							ConsentHandle:     "consent-handle-9",
							ConsentHandleList: []string{"consent-handle-7"},
						}, nil),
					mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(mockCaClient), nil),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},

		{
			name: "10 - success, finvu consent flow, if caFlowName is nil in request and if StartConsentFlow returns Internal then final response should be internal",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUsersClient,
				beActorClient:        mockActorClient,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.InitiateConsentRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-10",
						},
					},
					Vua:                   "10@finvu",
					AaEntity:              finvu,
					ConsentRequestPurpose: consentFlow,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().StartConsentFlow(gomock.Any(), &beCaPb.StartConsentFlowRequest{
						ActorId:                       "actor-id-10",
						Vua:                           "10@finvu",
						AaEntity:                      getBeAaEntity(finvu),
						ConsentRequestPurpose:         getBeConsentRequestPurpose(consentFlow),
						CaFlowName:                    beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
						NumOfConsentHandlesToGenerate: 1,
					}).Return(
						&beCaPb.StartConsentFlowResponse{
							Status: rpcPb.StatusInternal(),
						}, nil),
					mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(mockCaClient), nil),
				},
			},
			want:    &feConnectedAccPb.InitiateConsentResponse{Status: rpcPb.StatusInternal()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{
				beConnectedAccClient: tt.fields.beConnectedAccClient,
				connectedAccountConf: tt.fields.connectedAccountConf,
				beUserClient:         tt.fields.beUserClient,
				beActorClient:        tt.fields.beActorClient,
				caFlowDLFactory:      tt.fields.caFlowDLFactory,
			}
			got, err := s.InitiateConsent(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateConsent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("CreateBankPreference() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestService_GetAvailableFips(t *testing.T) {
	ctr := gomock.NewController(t)
	mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
	defer ctr.Finish()

	var bankList1 = []typesPb.Bank{typesPb.Bank_CITI, typesPb.Bank_BARODA}

	type fields struct {
		beConnectedAccClient beCaPb.ConnectedAccountClient
		connectedAccountConf *config.ConnectedAccount
		feConf               *genconf.Config
	}
	type args struct {
		ctx   context.Context
		req   *feConnectedAccPb.GetAvailableFipsRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *feConnectedAccPb.GetAvailableFipsResponse
		wantErr bool
	}{
		{
			name: "1 - success",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				feConf:               gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetAvailableFipsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetAvailableFips(gomock.Any(), &beCaPb.GetAvailableFipsRequest{
						ActorId: "actor-id-1",
					}).Return(&beCaPb.GetAvailableFipsResponse{
						Status:   rpcPb.StatusOk(),
						BankList: bankList1,
					}, nil),
				},
			},
			want:    &feConnectedAccPb.GetAvailableFipsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "2 - success, recordNotFound",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				feConf:               gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetAvailableFipsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
						},
					},
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetAvailableFips(gomock.Any(), &beCaPb.GetAvailableFipsRequest{
						ActorId: "actor-id-2",
					}).Return(&beCaPb.GetAvailableFipsResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil),
				},
			},
			want:    &feConnectedAccPb.GetAvailableFipsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				beConnectedAccClient: tt.fields.beConnectedAccClient,
				connectedAccountConf: tt.fields.connectedAccountConf,
				conf:                 tt.fields.feConf,
			}
			got, err := s.GetAvailableFips(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAvailableFips() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("CreateBankPreference() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestService_GetLandingPageOnConnect(t *testing.T) {
	var (
		cbiOm = &widget.CheckboxItem{
			Id: consent.ConsentType_FI_WEALTH_TNC.String(),
			DisplayText: &commontypes.Text{
				FontColor:        "#8D8D8D",
				FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				FontColorOpacity: 100,
				DisplayValue:     &commontypes.Text_Html{Html: fmt.Sprintf("I agree & accept <a style=\"color: #00B899\" href=\"%s\">epiFi Wealth TnC</a> and <a style=\"color: #00B899\" href=\"%s\">OneMoney TnC</a>", feConf.LegalDocuments.FiWealthTncUrl, feConf.LegalDocuments.AaOnemoneyTncUrl)},
			},
			IsChecked: true,
		}
		cbiFinvu = &widget.CheckboxItem{
			Id: consent.ConsentType_FI_WEALTH_TNC.String(),
			DisplayText: &commontypes.Text{
				FontColor:        "#8D8D8D",
				FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				FontColorOpacity: 100,
				DisplayValue:     &commontypes.Text_Html{Html: fmt.Sprintf("I agree & accept <a style=\"color: #00B899\" href=\"%s\">epiFi Wealth TnC</a> and <a style=\"color: #00B899\" href=\"%s\">Finvu TnC</a>", feConf.LegalDocuments.FiWealthTncUrl, feConf.LegalDocuments.AaFinvuTncUrl)},
			},
			IsChecked: true,
		}
		onemoneyTncMessage = "I agree to OneMoney’s <a href=\"" + feConf.LegalDocuments.AaOnemoneyTncUrl + "\">Terms & Conditions</a>"
		finvuTncMessage    = "I agree to Finvu’s <a href=\"" + feConf.LegalDocuments.AaFinvuTncUrl + "\">Terms & Conditions</a>"
	)

	wealthBuilderPanCollectionDl, err := insightsPkg.GetWealthBuilderDataCollectionDeeplink(&deeplinkpb.Deeplink{
		Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
		ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
			ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
				MobileNumber: &commontypes.PhoneNumber{
					CountryCode:    91,
					NationalNumber: **********,
				},
				// passing pan name as profile name is deprecated
				Name:                   "Hardik",
				AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
				FiuId:                  "",
				AaTncMessage:           onemoneyTncMessage,
				UseV2Flow:              false,
				UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
				CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
				Version:                deeplinkv2.Version_VERSION_V1,
			},
		},
	}, networthBeFePb.ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_PAN)
	if err != nil {
		t.Fatalf("failed to create wealth builder pan collectiondeeplnk: %v", err)
	}

	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
	mockUserClient := mocks2.NewMockUsersClient(ctr)
	mockActorClient := mocks3.NewMockActorClient(ctr)
	mockSavingsClient := mocks5.NewMockSavingsClient(ctr)

	mockBeConsentClient := mocks6.NewMockConsentClient(ctr)
	mockReleaseEvaluator := mock_release.NewMockIEvaluator(ctr)
	mockOnbClient := mocks4.NewMockOnboardingClient(ctr)

	type fields struct {
		UnimplementedConnectedAccountServer feConnectedAccPb.UnimplementedConnectedAccountServer
		beConnectedAccClient                beCaPb.ConnectedAccountClient
		connectedAccountConf                *config.ConnectedAccount
		beUserClient                        userPb.UsersClient
		beActorClient                       actorPb.ActorClient
		conf                                *genconf.Config
		beConsentClient                     consent.ConsentClient
		releaseEvaluator                    release.IEvaluator
		beSavingsClient                     savings.SavingsClient
		onbClient                           onbPb.OnboardingClient
	}
	type args struct {
		ctx   context.Context
		req   *feConnectedAccPb.GetLandingPageOnConnectRequest
		mocks []interface{}
	}
	type getActorByIdMock struct {
		enable bool
		status *rpcPb.Status
		actor  *typesPb.Actor
	}
	type checkReoobeMock struct {
		enable         bool
		status         *rpcPb.Status
		accountDetails []*beCaExtPb.AccountDetails
	}
	type getFeatureDetailsMock struct {
		enable       bool
		status       *rpcPb.Status
		isFiLiteUser bool
		featureInfo  *onbPb.FeatureInfo
	}
	type getAaEntityMock struct {
		enable   bool
		status   *rpcPb.Status
		aaEntity beCaEnumPb.AaEntity
	}
	type getUserMock struct {
		enable bool
		status *rpcPb.Status
		user   *userPb.User
	}
	type getFipMetaMock struct {
		enable  bool
		status  *rpcPb.Status
		fipMeta []*beCaExtPb.FipMeta
	}
	type getReleaseEvaluateMock struct {
		enable    bool
		isEnabled bool
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		getActorByIdMock
		checkReoobeMock
		getFeatureDetailsMock
		getAaEntityMock
		getUserMock
		getFipMetaMock
		getReleaseEvaluateMock
		setupMocks func(m *mockFields)
		want       *feConnectedAccPb.GetLandingPageOnConnectResponse
		wantErr    bool
	}{
		{
			name: "Wealth onb of user is completed : actor fetch failed",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beSavingsClient:      mockSavingsClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusInternal(),
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusInternal(),
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable: true,
				status: rpcPb.StatusOk(),
			},
			getUserMock: getUserMock{
				enable: false,
			},
			getFipMetaMock: getFipMetaMock{
				enable: false,
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable: false,
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()},
			},
			wantErr: false,
		},
		{
			name: "Wealth onb of user is completed : user fetch failed",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-2",
					EntityId: "user-id-2",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusOk(),
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable: true,
				status: rpcPb.StatusOk(),
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusInternal(),
			},
			getFipMetaMock: getFipMetaMock{
				enable: false,
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable: false,
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()},
			},
			wantErr: false,
		},
		{
			name: "Wealth onb of user is completed : reoobe check failed",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beSavingsClient:      mockSavingsClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-2",
					EntityId: "user-id-2",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusInternal(),
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable: true,
				status: rpcPb.StatusOk(),
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-2", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: false,
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable: false,
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()},
			},
			wantErr: false,
		},
		{
			name: "6 Wealth onb of user is completed : user eligible for reoobe",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beConsentClient:      mockBeConsentClient,
				beSavingsClient:      mockSavingsClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-2",
					EntityId: "user-id-2",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusOk(),
				accountDetails: []*beCaExtPb.AccountDetails{
					{
						FipId:               "HDFC-FIP",
						AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						LinkedAccountRef:    "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						ActorId:             "actor-id-2",
						MaskedAccountNumber: "XXXXX32596",
						AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
						IfscCode:            "HDFC0002558",
						AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
						AccountType: &beCaExtPb.AccountTypeConfig{AccType: &beCaExtPb.AccountTypeConfig_DepositAccountType{
							DepositAccountType: beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS}},
					},
				},
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable:   true,
				status:   rpcPb.StatusOk(),
				aaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-2", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
					PAN:     "Hardik",
					DateOfBirth: &date.Date{
						Year:  2000,
						Month: 1,
						Day:   1,
					},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: true,
				status: rpcPb.StatusOk(),
				fipMeta: []*beCaExtPb.FipMeta{
					{FipId: "HDFC-FIP", Name: "HDFC Bank", LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png"},
				},
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable: false,
			},
			setupMocks: func(m *mockFields) {
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-2")).Return(false, nil)
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
										ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
											MobileNumber: &commontypes.PhoneNumber{
												CountryCode:    91,
												NationalNumber: **********,
											},
											// passing pan name as profile name is deprecated
											Name:                   "Hardik",
											AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
											FiuId:                  "",
											AaTncMessage:           onemoneyTncMessage,
											UseV2Flow:              gconf.ConnectedAccount().V2FlowParams().UseV2Flow(),
											UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
											CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
				NewDeeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_HANDLE_REOOBE,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountHandleReoobeOptions{
										ConnectedAccountHandleReoobeOptions: &deeplinkpb.ConnectedAccountHandleReoobeOptions{
											AccountDetailList: []*deeplinkpb.ConnectedAccountHandleReoobeOptions_AccountDetail{
												{
													AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
													MaskedAccountNumber: "XXXXX32596",
													AccountType:         "SAVINGS",
													FipLogoUrl:          "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
													FipName:             "HDFC Bank",
													FiType:              "DEPOSIT",
												},
											},
											NextScreen: &deeplinkpb.Deeplink{
												Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
												ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
													ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
														MobileNumber: &commontypes.PhoneNumber{
															CountryCode:    91,
															NationalNumber: **********,
														},
														// passing pan name as profile name is deprecated
														Name:                   "Hardik",
														AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
														FiuId:                  "",
														AaTncMessage:           onemoneyTncMessage,
														UseV2Flow:              gconf.ConnectedAccount().V2FlowParams().UseV2Flow(),
														UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
														CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
													},
												},
											},
											Title: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetTitleText},
												FontColor:        ReeobeBottomsheetTitleFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetTitleFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Body: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetBodyText},
												FontColor:        ReeobeBottomsheetBodyFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetBodyFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Footer: &commontypes.Text{
												DisplayValue:     &commontypes.Text_Html{Html: ReeobeBottomsheetFooterText},
												FontColor:        ReeobeBottomsheetFooterFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetFooterFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											CtaList: []*deeplinkPb.Cta{
												{
													Type:         deeplinkPb.Cta_DONE,
													Text:         ReeobeBottomsheetBackCtaText,
													DisplayTheme: deeplinkPb.Cta_SECONDARY,
												},
												{
													Type:         deeplinkPb.Cta_CONTINUE,
													Text:         ReeobeBottomsheetProceedCtaText,
													DisplayTheme: deeplinkPb.Cta_PRIMARY,
												},
											},
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
			},
			wantErr: false,
		},
		{
			name: "Wealth onb of user is completed : user not eligible for reoobe/consent already found",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beConsentClient:      mockBeConsentClient,
				beSavingsClient:      mockSavingsClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-2",
					EntityId: "user-id-2",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable:         true,
				status:         rpcPb.StatusOk(),
				accountDetails: nil,
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable:   true,
				status:   rpcPb.StatusOk(),
				aaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-2", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
					PAN:     "Hardik",
					DateOfBirth: &date.Date{
						Year:  2000,
						Month: 1,
						Day:   1,
					},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: false,
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable: false,
			},
			setupMocks: func(m *mockFields) {
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-2")).Return(false, nil)
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
										ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
											MobileNumber: &commontypes.PhoneNumber{
												CountryCode:    91,
												NationalNumber: **********,
											},
											// passing pan name as profile name is deprecated
											Name:                   "Hardik",
											AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
											FiuId:                  "",
											AaTncMessage:           onemoneyTncMessage,
											UseV2Flow:              gconf.ConnectedAccount().V2FlowParams().UseV2Flow(),
											UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
											CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
				NewDeeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
										ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
											MobileNumber: &commontypes.PhoneNumber{
												CountryCode:    91,
												NationalNumber: **********,
											},
											// passing pan name as profile name is deprecated
											Name:                   "Hardik",
											AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
											FiuId:                  "",
											AaTncMessage:           onemoneyTncMessage,
											UseV2Flow:              gconf.ConnectedAccount().V2FlowParams().UseV2Flow(),
											UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
											CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
			},
			wantErr: false,
		},
		{
			name: "success: IOS",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beConsentClient:      mockBeConsentClient,
				beSavingsClient:      mockSavingsClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-2",
							Device: &commontypes.Device{
								Platform: commontypes.Platform_IOS,
							},
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-2",
					EntityId: "user-id-2",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusOk(),
				accountDetails: []*beCaExtPb.AccountDetails{
					{
						FipId:               "HDFC-FIP",
						AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						LinkedAccountRef:    "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						ActorId:             "actor-id-2",
						MaskedAccountNumber: "XXXXX32596",
						AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
						IfscCode:            "HDFC0002558",
						AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
						AccountType: &beCaExtPb.AccountTypeConfig{AccType: &beCaExtPb.AccountTypeConfig_DepositAccountType{
							DepositAccountType: beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS}},
					},
				},
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable:   true,
				status:   rpcPb.StatusOk(),
				aaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-2", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
					PAN:     "Hardik",
					DateOfBirth: &date.Date{
						Year:  2000,
						Month: 1,
						Day:   1,
					},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: true,
				status: rpcPb.StatusOk(),
				fipMeta: []*beCaExtPb.FipMeta{
					{FipId: "HDFC-FIP", Name: "HDFC Bank", LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png"},
				},
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable: false,
			},
			setupMocks: func(m *mockFields) {
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-2")).Return(false, nil)
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
										ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
											MobileNumber: &commontypes.PhoneNumber{
												CountryCode:    91,
												NationalNumber: **********,
											},
											// passing pan name as profile name is deprecated
											Name:                   "Hardik",
											AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
											FiuId:                  "",
											AaTncMessage:           onemoneyTncMessage,
											UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
											CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
											Version:                deeplinkv2.Version_VERSION_V1,
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
				NewDeeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_HANDLE_REOOBE,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountHandleReoobeOptions{
										ConnectedAccountHandleReoobeOptions: &deeplinkpb.ConnectedAccountHandleReoobeOptions{
											AccountDetailList: []*deeplinkpb.ConnectedAccountHandleReoobeOptions_AccountDetail{
												{
													AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
													MaskedAccountNumber: "XXXXX32596",
													AccountType:         "SAVINGS",
													FipLogoUrl:          "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
													FipName:             "HDFC Bank",
													FiType:              "DEPOSIT",
												},
											},
											NextScreen: &deeplinkpb.Deeplink{
												Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
												ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
													ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
														MobileNumber: &commontypes.PhoneNumber{
															CountryCode:    91,
															NationalNumber: **********,
														},
														// passing pan name as profile name is deprecated
														Name:                   "Hardik",
														AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
														FiuId:                  "",
														AaTncMessage:           onemoneyTncMessage,
														UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
														CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
														Version:                deeplinkv2.Version_VERSION_V1,
													},
												},
											},
											Title: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetTitleText},
												FontColor:        ReeobeBottomsheetTitleFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetTitleFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Body: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetBodyText},
												FontColor:        ReeobeBottomsheetBodyFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetBodyFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Footer: &commontypes.Text{
												DisplayValue:     &commontypes.Text_Html{Html: ReeobeBottomsheetFooterText},
												FontColor:        ReeobeBottomsheetFooterFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetFooterFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											CtaList: []*deeplinkPb.Cta{
												{
													Type:         deeplinkPb.Cta_DONE,
													Text:         ReeobeBottomsheetBackCtaText,
													DisplayTheme: deeplinkPb.Cta_SECONDARY,
												},
												{
													Type:         deeplinkPb.Cta_CONTINUE,
													Text:         ReeobeBottomsheetProceedCtaText,
													DisplayTheme: deeplinkPb.Cta_PRIMARY,
												},
											},
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
			},
			wantErr: false,
		},
		{
			name: "success: Android(app version matches)",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beConsentClient:      mockBeConsentClient,
				beSavingsClient:      mockSavingsClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-7",
							Device: &commontypes.Device{
								Platform:   commontypes.Platform_ANDROID,
								AppVersion: 2000,
							},
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-7",
					EntityId: "user-id-7",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusOk(),
				accountDetails: []*beCaExtPb.AccountDetails{
					{
						FipId:               "HDFC-FIP",
						AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						LinkedAccountRef:    "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						ActorId:             "actor-id-2",
						MaskedAccountNumber: "XXXXX32596",
						AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
						IfscCode:            "HDFC0002558",
						AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
						AccountType: &beCaExtPb.AccountTypeConfig{AccType: &beCaExtPb.AccountTypeConfig_DepositAccountType{
							DepositAccountType: beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS}},
					},
				},
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable:   true,
				status:   rpcPb.StatusOk(),
				aaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-2", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
					PAN:     "Hardik",
					DateOfBirth: &date.Date{
						Year:  2000,
						Month: 1,
						Day:   1,
					},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: true,
				status: rpcPb.StatusOk(),
				fipMeta: []*beCaExtPb.FipMeta{
					{FipId: "HDFC-FIP", Name: "HDFC Bank", LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png"},
				},
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable: false,
			},
			setupMocks: func(m *mockFields) {
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-7")).Return(false, nil)
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
										ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
											MobileNumber: &commontypes.PhoneNumber{
												CountryCode:    91,
												NationalNumber: **********,
											},
											// passing pan name as profile name is deprecated
											Name:                   "Hardik",
											AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
											FiuId:                  "",
											AaTncMessage:           onemoneyTncMessage,
											UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
											CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
											Version:                deeplinkv2.Version_VERSION_V1,
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
				NewDeeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_HANDLE_REOOBE,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountHandleReoobeOptions{
										ConnectedAccountHandleReoobeOptions: &deeplinkpb.ConnectedAccountHandleReoobeOptions{
											AccountDetailList: []*deeplinkpb.ConnectedAccountHandleReoobeOptions_AccountDetail{
												{
													AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
													MaskedAccountNumber: "XXXXX32596",
													AccountType:         "SAVINGS",
													FipLogoUrl:          "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
													FipName:             "HDFC Bank",
													FiType:              "DEPOSIT",
												},
											},
											NextScreen: &deeplinkpb.Deeplink{
												Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
												ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
													ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
														MobileNumber: &commontypes.PhoneNumber{
															CountryCode:    91,
															NationalNumber: **********,
														},
														// passing pan name as profile name is deprecated
														Name:                   "Hardik",
														AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
														FiuId:                  "",
														AaTncMessage:           onemoneyTncMessage,
														UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
														CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
														Version:                deeplinkv2.Version_VERSION_V1,
													},
												},
											},
											Title: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetTitleText},
												FontColor:        ReeobeBottomsheetTitleFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetTitleFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Body: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetBodyText},
												FontColor:        ReeobeBottomsheetBodyFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetBodyFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Footer: &commontypes.Text{
												DisplayValue:     &commontypes.Text_Html{Html: ReeobeBottomsheetFooterText},
												FontColor:        ReeobeBottomsheetFooterFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetFooterFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											CtaList: []*deeplinkPb.Cta{
												{
													Type:         deeplinkPb.Cta_DONE,
													Text:         ReeobeBottomsheetBackCtaText,
													DisplayTheme: deeplinkPb.Cta_SECONDARY,
												},
												{
													Type:         deeplinkPb.Cta_CONTINUE,
													Text:         ReeobeBottomsheetProceedCtaText,
													DisplayTheme: deeplinkPb.Cta_PRIMARY,
												},
											},
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
			},
			wantErr: false,
		},
		{
			name: "success: Android(app version does not matches)",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beConsentClient:      mockBeConsentClient,
				beSavingsClient:      mockSavingsClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-7",
							Device: &commontypes.Device{
								Platform:   commontypes.Platform_ANDROID,
								AppVersion: 0,
							},
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-7",
					EntityId: "user-id-7",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusOk(),
				accountDetails: []*beCaExtPb.AccountDetails{
					{
						FipId:               "HDFC-FIP",
						AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						LinkedAccountRef:    "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						ActorId:             "actor-id-2",
						MaskedAccountNumber: "XXXXX32596",
						AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
						IfscCode:            "HDFC0002558",
						AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
						AccountType: &beCaExtPb.AccountTypeConfig{AccType: &beCaExtPb.AccountTypeConfig_DepositAccountType{
							DepositAccountType: beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS}},
					},
				},
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable:   true,
				status:   rpcPb.StatusOk(),
				aaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-2", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
					PAN:     "Hardik",
					DateOfBirth: &date.Date{
						Year:  2000,
						Month: 1,
						Day:   1,
					},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: true,
				status: rpcPb.StatusOk(),
				fipMeta: []*beCaExtPb.FipMeta{
					{FipId: "HDFC-FIP", Name: "HDFC Bank", LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png"},
				},
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable: false,
			},
			setupMocks: func(m *mockFields) {
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-7")).Return(false, nil)
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
										ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
											MobileNumber: &commontypes.PhoneNumber{
												CountryCode:    91,
												NationalNumber: **********,
											},
											// passing pan name as profile name is deprecated
											Name:                   "Hardik",
											AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
											FiuId:                  "",
											AaTncMessage:           onemoneyTncMessage,
											UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
											CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
											Version:                deeplinkv2.Version_VERSION_V1,
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
				NewDeeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_HANDLE_REOOBE,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountHandleReoobeOptions{
										ConnectedAccountHandleReoobeOptions: &deeplinkpb.ConnectedAccountHandleReoobeOptions{
											AccountDetailList: []*deeplinkpb.ConnectedAccountHandleReoobeOptions_AccountDetail{
												{
													AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
													MaskedAccountNumber: "XXXXX32596",
													AccountType:         "SAVINGS",
													FipLogoUrl:          "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
													FipName:             "HDFC Bank",
													FiType:              "DEPOSIT",
												},
											},
											NextScreen: &deeplinkpb.Deeplink{
												Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
												ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
													ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
														MobileNumber: &commontypes.PhoneNumber{
															CountryCode:    91,
															NationalNumber: **********,
														},
														// passing pan name as profile name is deprecated
														Name:                   "Hardik",
														AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
														FiuId:                  "",
														AaTncMessage:           onemoneyTncMessage,
														UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
														CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
														Version:                deeplinkv2.Version_VERSION_V1,
													},
												},
											},
											Title: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetTitleText},
												FontColor:        ReeobeBottomsheetTitleFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetTitleFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Body: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetBodyText},
												FontColor:        ReeobeBottomsheetBodyFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetBodyFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Footer: &commontypes.Text{
												DisplayValue:     &commontypes.Text_Html{Html: ReeobeBottomsheetFooterText},
												FontColor:        ReeobeBottomsheetFooterFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetFooterFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											CtaList: []*deeplinkPb.Cta{
												{
													Type:         deeplinkPb.Cta_DONE,
													Text:         ReeobeBottomsheetBackCtaText,
													DisplayTheme: deeplinkPb.Cta_SECONDARY,
												},
												{
													Type:         deeplinkPb.Cta_CONTINUE,
													Text:         ReeobeBottomsheetProceedCtaText,
													DisplayTheme: deeplinkPb.Cta_PRIMARY,
												},
											},
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
			},
			wantErr: false,
		},
		{
			name: "success: finvu, useToken true",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beConsentClient:      mockBeConsentClient,
				releaseEvaluator:     mockReleaseEvaluator,
				beSavingsClient:      mockSavingsClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-7",
							Device: &commontypes.Device{
								Platform:   commontypes.Platform_IOS,
								AppVersion: 100,
							},
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-7",
					EntityId: "user-id-7",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusOk(),
				accountDetails: []*beCaExtPb.AccountDetails{
					{
						FipId:               "HDFC-FIP",
						AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						LinkedAccountRef:    "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						ActorId:             "actor-id-2",
						MaskedAccountNumber: "XXXXX32596",
						AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
						IfscCode:            "HDFC0002558",
						AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
						AccountType: &beCaExtPb.AccountTypeConfig{AccType: &beCaExtPb.AccountTypeConfig_DepositAccountType{
							DepositAccountType: beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS}},
					},
				},
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable:   true,
				status:   rpcPb.StatusOk(),
				aaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_FINVU,
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-2", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
					PAN:     "Hardik",
					DateOfBirth: &date.Date{
						Year:  2000,
						Month: 1,
						Day:   1,
					},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: true,
				status: rpcPb.StatusOk(),
				fipMeta: []*beCaExtPb.FipMeta{
					{FipId: "HDFC-FIP", Name: "HDFC Bank", LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png"},
				},
			},
			getReleaseEvaluateMock: getReleaseEvaluateMock{
				enable:    true,
				isEnabled: true,
			},
			setupMocks: func(m *mockFields) {
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-7")).Return(false, nil)
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiFinvu,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
										ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
											MobileNumber: &commontypes.PhoneNumber{
												CountryCode:    91,
												NationalNumber: **********,
											},
											// passing pan name as profile name is deprecated
											Name:                   "Hardik",
											AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_FINVU,
											FiuId:                  "",
											AaTncMessage:           finvuTncMessage,
											UseTokenAuthentication: commontypes.BooleanEnum_TRUE,
											CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
											Version:                deeplinkv2.Version_VERSION_V1,
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
				NewDeeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiFinvu,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_HANDLE_REOOBE,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountHandleReoobeOptions{
										ConnectedAccountHandleReoobeOptions: &deeplinkpb.ConnectedAccountHandleReoobeOptions{
											AccountDetailList: []*deeplinkpb.ConnectedAccountHandleReoobeOptions_AccountDetail{
												{
													AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
													MaskedAccountNumber: "XXXXX32596",
													AccountType:         "SAVINGS",
													FipLogoUrl:          "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
													FipName:             "HDFC Bank",
													FiType:              "DEPOSIT",
												},
											},
											NextScreen: &deeplinkpb.Deeplink{
												Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
												ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
													ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
														MobileNumber: &commontypes.PhoneNumber{
															CountryCode:    91,
															NationalNumber: **********,
														},
														// passing pan name as profile name is deprecated
														Name:                   "Hardik",
														AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_FINVU,
														FiuId:                  "",
														AaTncMessage:           finvuTncMessage,
														UseTokenAuthentication: commontypes.BooleanEnum_TRUE,
														CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
														Version:                deeplinkv2.Version_VERSION_V1,
													},
												},
											},
											Title: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetTitleText},
												FontColor:        ReeobeBottomsheetTitleFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetTitleFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Body: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetBodyText},
												FontColor:        ReeobeBottomsheetBodyFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetBodyFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Footer: &commontypes.Text{
												DisplayValue:     &commontypes.Text_Html{Html: ReeobeBottomsheetFooterText},
												FontColor:        ReeobeBottomsheetFooterFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetFooterFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											CtaList: []*deeplinkPb.Cta{
												{
													Type:         deeplinkPb.Cta_DONE,
													Text:         ReeobeBottomsheetBackCtaText,
													DisplayTheme: deeplinkPb.Cta_SECONDARY,
												},
												{
													Type:         deeplinkPb.Cta_CONTINUE,
													Text:         ReeobeBottomsheetProceedCtaText,
													DisplayTheme: deeplinkPb.Cta_PRIMARY,
												},
											},
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
			},
			wantErr: false,
		},
		{
			name: "missing PAN DOB details, return PAN DOB screen",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beConsentClient:      mockBeConsentClient,
				beSavingsClient:      mockSavingsClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-7",
							Device: &commontypes.Device{
								Platform:   commontypes.Platform_ANDROID,
								AppVersion: 2000,
							},
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-7",
					EntityId: "user-id-7",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusOk(),
				accountDetails: []*beCaExtPb.AccountDetails{
					{
						FipId:               "HDFC-FIP",
						AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						LinkedAccountRef:    "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
						ActorId:             "actor-id-2",
						MaskedAccountNumber: "XXXXX32596",
						AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
						IfscCode:            "HDFC0002558",
						AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
						AccountType: &beCaExtPb.AccountTypeConfig{AccType: &beCaExtPb.AccountTypeConfig_DepositAccountType{
							DepositAccountType: beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS}},
					},
				},
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable:   true,
				status:   rpcPb.StatusOk(),
				aaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-7", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: true,
				status: rpcPb.StatusOk(),
				fipMeta: []*beCaExtPb.FipMeta{
					{FipId: "HDFC-FIP", Name: "HDFC Bank", LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png"},
				},
			},
			setupMocks: func(m *mockFields) {
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-7")).Return(false, nil)
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM).WithActorId("actor-id-7")).Return(false, nil)
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB).WithActorId("actor-id-7")).Return(false, nil)
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
				NewDeeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text: "PROCEED",
								Deeplink: &deeplinkpb.Deeplink{
									Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_HANDLE_REOOBE,
									ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountHandleReoobeOptions{
										ConnectedAccountHandleReoobeOptions: &deeplinkpb.ConnectedAccountHandleReoobeOptions{
											AccountDetailList: []*deeplinkpb.ConnectedAccountHandleReoobeOptions_AccountDetail{
												{
													AccountId:           "3695f88d-93f3-4f7f-a0ac-b54f2792a7ee",
													MaskedAccountNumber: "XXXXX32596",
													AccountType:         "SAVINGS",
													FipLogoUrl:          "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
													FipName:             "HDFC Bank",
													FiType:              "DEPOSIT",
												},
											},
											NextScreen: &deeplinkPb.Deeplink{
												Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
											},
											Title: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetTitleText},
												FontColor:        ReeobeBottomsheetTitleFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetTitleFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Body: &commontypes.Text{
												DisplayValue:     &commontypes.Text_PlainString{PlainString: ReeobeBottomsheetBodyText},
												FontColor:        ReeobeBottomsheetBodyFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetBodyFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											Footer: &commontypes.Text{
												DisplayValue:     &commontypes.Text_Html{Html: ReeobeBottomsheetFooterText},
												FontColor:        ReeobeBottomsheetFooterFontColor,
												FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: ReeobeBottomsheetFooterFontStyle},
												FontColorOpacity: DefaultFontOpacity,
											},
											CtaList: []*deeplinkPb.Cta{
												{
													Type:         deeplinkPb.Cta_DONE,
													Text:         ReeobeBottomsheetBackCtaText,
													DisplayTheme: deeplinkPb.Cta_SECONDARY,
												},
												{
													Type:         deeplinkPb.Cta_CONTINUE,
													Text:         ReeobeBottomsheetProceedCtaText,
													DisplayTheme: deeplinkPb.Cta_PRIMARY,
												},
											},
										},
									},
								},
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
			},
			wantErr: false,
		},
		{
			name: "missing PAN DOB details, return unverified pan collection screen for wealth builder",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beConsentClient:      mockBeConsentClient,
				beSavingsClient:      mockSavingsClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetLandingPageOnConnectRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-7",
							Device: &commontypes.Device{
								Platform:   commontypes.Platform_ANDROID,
								AppVersion: 2000,
							},
						},
					}},
			},
			getActorByIdMock: getActorByIdMock{
				enable: true,
				status: rpcPb.StatusOk(),
				actor: &typesPb.Actor{
					Id:       "actor-id-7",
					EntityId: "user-id-7",
				},
			},
			checkReoobeMock: checkReoobeMock{
				enable: true,
				status: rpcPb.StatusOk(),
			},
			getFeatureDetailsMock: getFeatureDetailsMock{
				enable:       true,
				status:       rpcPb.StatusOk(),
				isFiLiteUser: false,
			},
			getAaEntityMock: getAaEntityMock{
				enable:   true,
				status:   rpcPb.StatusOk(),
				aaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
			},
			getUserMock: getUserMock{
				enable: true,
				status: rpcPb.StatusOk(),
				user: &userPb.User{Id: "user-id-7", Profile: &userPb.Profile{
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					PanName: &commontypes.Name{FirstName: "Hardik"},
				}},
			},
			getFipMetaMock: getFipMetaMock{
				enable: false,
			},
			setupMocks: func(m *mockFields) {
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-7")).Return(false, nil)
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM).WithActorId("actor-id-7")).Return(true, nil)
				m.mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB).WithActorId("actor-id-7")).Return(false, nil)
			},
			want: &feConnectedAccPb.GetLandingPageOnConnectResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text:         "PROCEED",
								Deeplink:     wealthBuilderPanCollectionDl,
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
				NewDeeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
					ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
						ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
							WealthTncCheckBox: cbiOm,
							ProceedCta: &deeplinkpb.Cta{
								Text:         "PROCEED",
								Deeplink:     wealthBuilderPanCollectionDl,
								DisplayTheme: deeplinkpb.Cta_PRIMARY,
								Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
							},
						}},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
			mockUserClient := mocks2.NewMockUsersClient(ctr)
			mockActorClient := mocks3.NewMockActorClient(ctr)
			mockSavingsClient := mocks5.NewMockSavingsClient(ctr)

			mockBeConsentClient := mocks6.NewMockConsentClient(ctr)
			mockReleaseEvaluator := mock_release.NewMockIEvaluator(ctr)
			mockOnbClient := mocks4.NewMockOnboardingClient(ctr)

			// temp solutions to setup mocks using function till we are able to migrate all mocks to this pattern
			// current mock setup solution in this test donot allow setting multiple calls to same mock :(
			if tt.setupMocks != nil {
				m := &mockFields{
					mockReleaseEvaluator: mockReleaseEvaluator,
				}
				tt.setupMocks(m)
				mockReleaseEvaluator = m.mockReleaseEvaluator
			}

			s := &Service{
				UnimplementedConnectedAccountServer: tt.fields.UnimplementedConnectedAccountServer,
				beConnectedAccClient:                mockCaClient,
				connectedAccountConf:                tt.fields.connectedAccountConf,
				beUserClient:                        mockUserClient,
				beActorClient:                       mockActorClient,
				conf:                                tt.fields.conf,
				beConsentClient:                     mockBeConsentClient,
				releaseEvaluator:                    mockReleaseEvaluator,
				beSavingsClient:                     mockSavingsClient,
				onbClient:                           mockOnbClient,
			}

			if tt.getActorByIdMock.enable {
				mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
					Status: tt.getActorByIdMock.status,
					Actor:  tt.getActorByIdMock.actor,
				}, nil)
			}

			if tt.checkReoobeMock.enable {
				mockCaClient.EXPECT().CheckReoobe(gomock.Any(), gomock.Any()).Return(&beCaPb.CheckReoobeResponse{
					Status:            tt.checkReoobeMock.status,
					AccountDetailList: tt.checkReoobeMock.accountDetails,
				}, nil)
			}

			if tt.getFeatureDetailsMock.enable {
				mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       tt.getFeatureDetailsMock.status,
					IsFiLiteUser: tt.getFeatureDetailsMock.isFiLiteUser,
					FeatureInfo:  tt.getFeatureDetailsMock.featureInfo,
				}, nil)
			}

			if tt.getAaEntityMock.enable {
				mockCaClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{
					Status:   tt.getAaEntityMock.status,
					AaEntity: tt.getAaEntityMock.aaEntity,
				}, nil)
			}

			if tt.getUserMock.enable {
				mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					User:   tt.getUserMock.user,
					Status: tt.getUserMock.status,
				}, nil)
			}

			if tt.getFipMetaMock.enable {
				mockCaClient.EXPECT().GetFipMeta(gomock.Any(), gomock.Any()).Return(&beCaPb.GetFipMetaResponse{
					Status:      tt.getFipMetaMock.status,
					FipMetaList: tt.getFipMetaMock.fipMeta,
				}, nil)
			}

			if tt.getReleaseEvaluateMock.enable {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(tt.getReleaseEvaluateMock.isEnabled, nil)
			}

			got, err := s.GetLandingPageOnConnect(tt.args.ctx, tt.args.req)
			time.Sleep(time.Second)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLandingPageOnConnect() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&deeplinkPb.ConnectedAccountsOptions{}, "version"),
			}

			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetLandingPageOnConnect() (-want +got) %s", diff)
			}
		})
	}
}

func TestService_GetHomeSummary(t *testing.T) {
	type args struct {
		ctx context.Context
		req *feConnectedAccPb.GetHomeSummaryRequest
	}

	accountDetailsMap := map[string]*beCaPb.AccountProfileSummaryDetails{
		"acc-id-1": {
			AccountDetails: &beCaExtPb.AccountDetails{
				AccountId:         "acc-id-1",
				AccountStatus:     beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
				AccInstrumentType: beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
				FipId:             "HDFC-FIP",
				FipMeta: &beCaExtPb.FipMeta{
					FipId:       "HDFC-FIP",
					DisplayName: "HDFC",
					Bank:        typesPb.Bank_HDFC,
				},
			},
			Summary: &beCaPb.AccountProfileSummaryDetails_DepositSummary{
				DepositSummary: &beCaExtPb.DepositSummary{
					AccountId:            "acc-id-1",
					DepositAccountType:   beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS,
					DepositAccountStatus: beCaEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_ACTIVE,
					CurrentBalance:       &money.Money{CurrencyCode: "INR", Units: 500},
				},
			},
		},
		"acc-id-2": {
			AccountDetails: &beCaExtPb.AccountDetails{
				AccountId:         "acc-id-2",
				AccountStatus:     beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
				AccInstrumentType: beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
				FipId:             "AXIS001",
				FipMeta: &beCaExtPb.FipMeta{
					FipId:       "AXIS001",
					DisplayName: "AXIS",
					Bank:        typesPb.Bank_AXIS,
				},
			},
			Summary: &beCaPb.AccountProfileSummaryDetails_DepositSummary{
				DepositSummary: &beCaExtPb.DepositSummary{
					AccountId:            "acc-id-2",
					DepositAccountType:   beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS,
					DepositAccountStatus: beCaEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_ACTIVE,
					CurrentBalance:       &money.Money{CurrencyCode: "INR", Units: 300},
				},
			},
		},
	}

	type setupMocks func(
		mockCaClient *mocks.MockConnectedAccountClient,
		mockSavingsClient *mocks5.MockSavingsClient,
		mockOnbClient *mocks4.MockOnboardingClient,
		mockAccountBalance *accountBalanceMock.MockBalanceClient,
		mockReleaseEvaluator *mock_release.MockIEvaluator,
		mockFiToFiHelperSvc *mocks7.MockIConnectFiToFiHelperSvc,
		mockDeeplinkBuilder *deeplinkBuilderMocks.MockIDeeplinkBuilder,
	)

	tests := []struct {
		name       string
		args       args
		setupMocks setupMocks
		want       *feConnectedAccPb.GetHomeSummaryResponse
		wantErr    bool
	}{
		{
			name: "error fetching savings account by actor id",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetHomeSummaryRequest{
					Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id-1"}},
				},
			},
			setupMocks: func(mockCaClient *mocks.MockConnectedAccountClient, mockSavingsClient *mocks5.MockSavingsClient, mockOnbClient *mocks4.MockOnboardingClient, mockAccountBalance *accountBalanceMock.MockBalanceClient, mockReleaseEvaluator *mock_release.MockIEvaluator, mockFiToFiHelperSvc *mocks7.MockIConnectFiToFiHelperSvc, mockDeeplinkBuilder *deeplinkBuilderMocks.MockIDeeplinkBuilder) {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				mockCaClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{Status: rpcPb.StatusInternal()}, nil)
			},
			want: &feConnectedAccPb.GetHomeSummaryResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()},
			},
			wantErr: false,
		},
		{
			name: "error getting savings balance",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetHomeSummaryRequest{
					Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id-1"}},
				},
			},
			setupMocks: func(mockCaClient *mocks.MockConnectedAccountClient, mockSavingsClient *mocks5.MockSavingsClient, mockOnbClient *mocks4.MockOnboardingClient, mockAccountBalance *accountBalanceMock.MockBalanceClient, mockReleaseEvaluator *mock_release.MockIEvaluator, mockFiToFiHelperSvc *mocks7.MockIConnectFiToFiHelperSvc, mockDeeplinkBuilder *deeplinkBuilderMocks.MockIDeeplinkBuilder) {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1"}}, nil)
				mockAccountBalance.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{Status: rpcPb.StatusInternal()}, nil)
				mockCaClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{Status: rpcPb.StatusInternal()}, nil)
			},
			want: &feConnectedAccPb.GetHomeSummaryResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()},
			},
			wantErr: false,
		},
		{
			name: "successful response",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetHomeSummaryRequest{
					Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id-1"}},
				},
			},
			setupMocks: func(mockCaClient *mocks.MockConnectedAccountClient, mockSavingsClient *mocks5.MockSavingsClient, mockOnbClient *mocks4.MockOnboardingClient, mockAccountBalance *accountBalanceMock.MockBalanceClient, mockReleaseEvaluator *mock_release.MockIEvaluator, mockFiToFiHelperSvc *mocks7.MockIConnectFiToFiHelperSvc, mockDeeplinkBuilder *deeplinkBuilderMocks.MockIDeeplinkBuilder) {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mockFiToFiHelperSvc.EXPECT().IsFiToFiFlowEnabled(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1"}}, nil)
				mockAccountBalance.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status:           rpcPb.StatusOk(),
					AvailableBalance: &money.Money{CurrencyCode: "INR", Units: 500},
				}, nil)
				mockCaClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{
					Status: rpcPb.StatusOk(),
					AccountDetailsList: []*beCaExtPb.AccountDetails{
						{AccountId: "acc-id-1", FipId: "AXIS001"},
						{AccountId: "acc-id-2", FipId: "HDFC-FIP"},
					},
				}, nil)
				mockCaClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountDetailsBulkResponse{
					Status:            rpcPb.StatusOk(),
					AccountDetailsMap: accountDetailsMap,
				}, nil)
				mockCaClient.EXPECT().CheckEligibilityForConsentRenewal(gomock.Any(), gomock.Any()).Return(&beCaPb.CheckEligibilityForConsentRenewalResponse{
					Status:   rpcPb.StatusOk(),
					Eligible: false,
				}, nil)
				mockDeeplinkBuilder.EXPECT().GetTalkToAIFloatingIconWithDeeplink(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &feConnectedAccPb.GetHomeSummaryResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
			},
			wantErr: false,
		},
		{
			name: "error getting connected accounts",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetHomeSummaryRequest{
					Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id-1"}},
				},
			},
			setupMocks: func(mockCaClient *mocks.MockConnectedAccountClient, mockSavingsClient *mocks5.MockSavingsClient, mockOnbClient *mocks4.MockOnboardingClient, mockAccountBalance *accountBalanceMock.MockBalanceClient, mockReleaseEvaluator *mock_release.MockIEvaluator, mockFiToFiHelperSvc *mocks7.MockIConnectFiToFiHelperSvc, mockDeeplinkBuilder *deeplinkBuilderMocks.MockIDeeplinkBuilder) {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mockFiToFiHelperSvc.EXPECT().IsFiToFiFlowEnabled(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1"}}, nil)
				mockAccountBalance.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status:           rpcPb.StatusOk(),
					AvailableBalance: &money.Money{CurrencyCode: "INR", Units: 500},
				}, nil)
				mockCaClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{Status: rpcPb.StatusInternal()}, nil)
			},
			want: &feConnectedAccPb.GetHomeSummaryResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()},
			},
			wantErr: false,
		},
		{
			name: "error getting account details",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetHomeSummaryRequest{
					Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id-1"}},
				},
			},
			setupMocks: func(mockCaClient *mocks.MockConnectedAccountClient, mockSavingsClient *mocks5.MockSavingsClient, mockOnbClient *mocks4.MockOnboardingClient, mockAccountBalance *accountBalanceMock.MockBalanceClient, mockReleaseEvaluator *mock_release.MockIEvaluator, mockFiToFiHelperSvc *mocks7.MockIConnectFiToFiHelperSvc, mockDeeplinkBuilder *deeplinkBuilderMocks.MockIDeeplinkBuilder) {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mockFiToFiHelperSvc.EXPECT().IsFiToFiFlowEnabled(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1"}}, nil)
				mockAccountBalance.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status:           rpcPb.StatusOk(),
					AvailableBalance: &money.Money{CurrencyCode: "INR", Units: 500},
				}, nil)
				mockCaClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{
					Status: rpcPb.StatusOk(),
					AccountDetailsList: []*beCaExtPb.AccountDetails{
						{AccountId: "acc-id-1", FipId: "AXIS001"},
						{AccountId: "acc-id-2", FipId: "HDFC-FIP"},
					},
				}, nil)
				mockCaClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountDetailsBulkResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			want: &feConnectedAccPb.GetHomeSummaryResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()},
			},
			wantErr: false,
		},
		{
			name: "error account access is revoked",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetHomeSummaryRequest{
					Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id-1"}},
				},
			},
			setupMocks: func(mockCaClient *mocks.MockConnectedAccountClient, mockSavingsClient *mocks5.MockSavingsClient, mockOnbClient *mocks4.MockOnboardingClient, mockAccountBalance *accountBalanceMock.MockBalanceClient, mockReleaseEvaluator *mock_release.MockIEvaluator, mockFiToFiHelperSvc *mocks7.MockIConnectFiToFiHelperSvc, mockDeeplinkBuilder *deeplinkBuilderMocks.MockIDeeplinkBuilder) {
				mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "id-1",
						Constraints: &savings.AccountConstraints{
							AccessLevel: savings.AccessLevel_ACCESS_LEVEL_NO_ACCESS,
						},
					},
				}, nil)
				mockCaClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{
					Status: rpcPb.StatusOk(),
					AccountDetailsList: []*beCaExtPb.AccountDetails{
						{AccountId: "acc-id-1", FipId: "AXIS001"},
						{AccountId: "acc-id-2", FipId: "HDFC-FIP"},
					},
				}, nil)
				mockCaClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountDetailsBulkResponse{
					Status:            rpcPb.StatusOk(),
					AccountDetailsMap: accountDetailsMap,
				}, nil)
			},
			want: &feConnectedAccPb.GetHomeSummaryResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusPermissionDenied()},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockCaClient := mocks.NewMockConnectedAccountClient(ctrl)
			mockUserClient := mocks2.NewMockUsersClient(ctrl)
			mockActorClient := mocks3.NewMockActorClient(ctrl)
			mockSavingsClient := mocks5.NewMockSavingsClient(ctrl)
			mockOnbClient := mocks4.NewMockOnboardingClient(ctrl)
			mockAccountBalance := accountBalanceMock.NewMockBalanceClient(ctrl)
			mockReleaseEvaluator := mock_release.NewMockIEvaluator(ctrl)
			mockFiToFiHelperSvc := mocks7.NewMockIConnectFiToFiHelperSvc(ctrl)
			mockDeeplinkBuilder := deeplinkBuilderMocks.NewMockIDeeplinkBuilder(ctrl)

			if tt.setupMocks != nil {
				tt.setupMocks(mockCaClient, mockSavingsClient, mockOnbClient, mockAccountBalance, mockReleaseEvaluator, mockFiToFiHelperSvc, mockDeeplinkBuilder)
			}

			s := &Service{
				beConnectedAccClient: mockCaClient,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				beSavingsClient:      mockSavingsClient,
				releaseEvaluator:     mockReleaseEvaluator,
				conf:                 gconf,
				onbClient:            mockOnbClient,
				accountBalanceClient: mockAccountBalance,
				fiToFiCaHelperSvc:    mockFiToFiHelperSvc,
				deeplinkBuilder:      mockDeeplinkBuilder,
			}

			got, err := s.GetHomeSummary(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetHomeSummary() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.want != nil && got != nil {
				if tt.want.GetRespHeader().GetStatus().GetCode() != got.GetRespHeader().GetStatus().GetCode() {
					t.Errorf("GetHomeSummary() status code = %v, want %v", got.GetRespHeader().GetStatus().GetCode(), tt.want.GetRespHeader().GetStatus().GetCode())
				}
			}
		})
	}
}

func TestService_GetDataPullStatusFromConsentHandle(t *testing.T) {
	t.Parallel()
	type fields struct {
		beConnectedAccClient beCaPb.ConnectedAccountClient
		connectedAccountConf *config.ConnectedAccount
		conf                 *genconf.Config
		caFlowDLFactory      factory.ICaFlowDLFactory
	}
	type mockStruct struct {
		mockCaClient      *mocks.MockConnectedAccountClient
		mockCaFlowFactory *femocks.MockICaFlowDLFactory
	}
	type args struct {
		ctx context.Context
		req *feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func(md *mockStruct)
		want    *feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					ConsentHandle: "consent-handle-1",
				},
			},
			mock: func(md *mockStruct) {
				md.mockCaClient.EXPECT().GetDataFetchAttempts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetDataFetchAttemptsResponse{
					Status: rpcPb.StatusOk(),
					DataFetchAttemptDetailsList: []*beCaExtPb.DataFetchAttemptDetails{{
						Id:                 "id-1",
						ActorId:            "actor-id-1",
						ConsentReferenceId: "consent-reference-id-",
						FetchStatus:        beCaEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED,
						FipIds:             []string{"HDFC-FIP"},
					}},
					ConsentRequest: &beCaPb.ConsentRequest{
						Id:                    "test-id",
						ActorId:               "test-actor-id",
						TransactionId:         "test-txn-id",
						ConsentHandle:         "test-handle",
						ConsentHandleStatus:   beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY,
						CreatedAt:             timestamppb.Now(),
						UpdatedAt:             timestamppb.Now(),
						Status:                beCaEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
						ConsentRequestPurpose: beCaEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_CONSENT_FLOW,
						CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
					},
				}, nil)
				md.mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(md.mockCaClient), nil)
			},
			want: &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status:          rpcPb.StatusOk(),
				DataPullStatus:  feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_SUCCESSFUL,
				NextPollSeconds: -1,
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
					ScreenOptions: beCaProcessor.GetCAConsentTerminalScreenOptions(beCaProcessor.ConnectAccountSuccessScreenHeading, beCaProcessor.ImportDataSuccessScreenSubHeading, beCaProcessor.ConnectAccountSuccessScreenIcon, &deeplinkpb.Cta{
						Type: deeplinkpb.Cta_DONE,
						Text: beCaProcessor.ImportDataSuccessScreenCta,
						Deeplink: &deeplinkpb.Deeplink{
							Screen: deeplinkpb.Screen_SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN,
						},
						DisplayTheme: deeplinkpb.Cta_PRIMARY,
						Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
					}, true),
				},
				MaxAllowedAttempts: feConf.ConnectedAccount.MaxDataPullStatusPollAttempts,
			},
			wantErr: false,
		},
		{
			name: "error: GetDataFetchAttempts Record not found, in such case we are sending Status as OK along with Consent Request in response",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					ConsentHandle:    "consent-handle-1",
					CurrentPollCount: 0,
				},
			},
			mock: func(md *mockStruct) {
				md.mockCaClient.EXPECT().GetDataFetchAttempts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetDataFetchAttemptsResponse{
					Status: rpcPb.StatusOk(),
					ConsentRequest: &beCaPb.ConsentRequest{
						Id:                    "test-id",
						ActorId:               "test-actor-id",
						TransactionId:         "test-txn-id",
						ConsentHandle:         "test-handle",
						ConsentHandleStatus:   beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY,
						CreatedAt:             timestamppb.Now(),
						UpdatedAt:             timestamppb.Now(),
						Status:                beCaEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
						ConsentRequestPurpose: beCaEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_CONSENT_FLOW,
						CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
					},
				}, nil)
			},
			want: &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status:             rpcPb.StatusOk(),
				DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_IN_PROGRESS,
				NextPollSeconds:    feConf.ConnectedAccount.DataPullStatusNextPoll,
				MaxAllowedAttempts: feConf.ConnectedAccount.MaxDataPullStatusPollAttempts,
				CurrentPollCount:   1,
			},
			wantErr: false,
		},
		{
			name: "error: GetDataFetchAttempts internal server",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					ConsentHandle: "consent-handle-1",
				},
			},
			mock: func(md *mockStruct) {
				md.mockCaClient.EXPECT().GetDataFetchAttempts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetDataFetchAttemptsResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			want: &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "calling from some unimplemented processor",

			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					ConsentHandle:    "consent-handle-1",
					CurrentPollCount: 100,
				},
			},
			mock: func(md *mockStruct) {
				md.mockCaClient.EXPECT().GetDataFetchAttempts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetDataFetchAttemptsResponse{
					Status: rpcPb.StatusOk(),
					DataFetchAttemptDetailsList: []*beCaExtPb.DataFetchAttemptDetails{{
						Id:                 "id-1",
						ActorId:            "actor-id-1",
						ConsentReferenceId: "consent-reference-id-",
						FetchStatus:        beCaEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED,
						FipIds:             []string{"HDFC-FIP"},
					}},
					ConsentRequest: nil,
				}, nil)
				md.mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(nil, errors.New("unimplemented processor"))
			},
			want: &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Consent Handle Status in Consent Request is marked as Failed",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					ConsentHandle:    "consent-handle-1",
					CurrentPollCount: 100,
				},
			},
			mock: func(md *mockStruct) {
				md.mockCaClient.EXPECT().GetDataFetchAttempts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetDataFetchAttemptsResponse{
					Status: rpcPb.StatusOk(),
					DataFetchAttemptDetailsList: []*beCaExtPb.DataFetchAttemptDetails{{
						Id:                 "id-1",
						ActorId:            "actor-id-1",
						ConsentReferenceId: "consent-reference-id-",
						FetchStatus:        beCaEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION,
						FipIds:             []string{"HDFC-FIP"},
					}},
					ConsentRequest: &beCaPb.ConsentRequest{
						Id:                    "test-id",
						ActorId:               "test-actor-id",
						TransactionId:         "test-txn-id",
						ConsentHandle:         "test-handle",
						ConsentHandleStatus:   beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_FAILED,
						CreatedAt:             timestamppb.Now(),
						UpdatedAt:             timestamppb.Now(),
						Status:                beCaEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
						ConsentRequestPurpose: beCaEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_CONSENT_FLOW,
						CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
					},
				}, nil)
				md.mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(md.mockCaClient), nil)
			},
			want: &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status:             rpcPb.StatusOk(),
				DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_FAILED,
				NextPollSeconds:    -1,
				MaxAllowedAttempts: feConf.ConnectedAccount.MaxDataPullStatusPollAttempts,
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
					ScreenOptions: beCaProcessor.GetCAConsentTerminalScreenOptions(beCaProcessor.ConnectAccountFailureScreenHeading, beCaProcessor.ConnectAccountFailureScreenSubHeading, beCaProcessor.ConnectAccountFailureScreenIcon, &deeplinkpb.Cta{
						Type: deeplinkpb.Cta_DONE,
						Text: beCaProcessor.ConnectAccountFailureScreenCta,
						Deeplink: &deeplinkpb.Deeplink{
							Screen: deeplinkpb.Screen_SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN,
						},
						DisplayTheme: deeplinkpb.Cta_PRIMARY,
						Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
					}, false),
				},
			},
			wantErr: false,
		},
		{
			name: "Consent Handle Status in Consent Request is marked as Pending",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					ConsentHandle:    "consent-handle-1",
					CurrentPollCount: 0,
				},
			},
			mock: func(md *mockStruct) {
				md.mockCaClient.EXPECT().GetDataFetchAttempts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetDataFetchAttemptsResponse{
					Status: rpcPb.StatusOk(),
					DataFetchAttemptDetailsList: []*beCaExtPb.DataFetchAttemptDetails{{
						Id:                 "id-1",
						ActorId:            "actor-id-1",
						ConsentReferenceId: "consent-reference-id-",
						FetchStatus:        beCaEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED,
						FipIds:             []string{"HDFC-FIP"},
					}},
					ConsentRequest: &beCaPb.ConsentRequest{
						Id:                    "test-id",
						ActorId:               "test-actor-id",
						TransactionId:         "test-txn-id",
						ConsentHandle:         "test-handle",
						ConsentHandleStatus:   beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING,
						CreatedAt:             timestamppb.Now(),
						UpdatedAt:             timestamppb.Now(),
						Status:                beCaEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
						ConsentRequestPurpose: beCaEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_CONSENT_FLOW,
						CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
					},
				}, nil)
			},
			want: &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status:             rpcPb.StatusOk(),
				DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_IN_PROGRESS,
				NextPollSeconds:    feConf.ConnectedAccount.DataPullStatusNextPoll,
				MaxAllowedAttempts: feConf.ConnectedAccount.MaxDataPullStatusPollAttempts,
				CurrentPollCount:   1,
			},
			wantErr: false,
		},
		{
			name: "Getting empty Data fetch attempts list",
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetDataPullStatusFromConsentHandleRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					ConsentHandle:    "consent-handle-1",
					CurrentPollCount: 100,
				},
			},
			mock: func(md *mockStruct) {
				md.mockCaClient.EXPECT().GetDataFetchAttempts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetDataFetchAttemptsResponse{
					Status:                      rpcPb.StatusOk(),
					DataFetchAttemptDetailsList: nil,
					ConsentRequest: &beCaPb.ConsentRequest{
						Id:                    "test-id",
						ActorId:               "test-actor-id",
						TransactionId:         "test-txn-id",
						ConsentHandle:         "test-handle",
						ConsentHandleStatus:   beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY,
						CreatedAt:             timestamppb.Now(),
						UpdatedAt:             timestamppb.Now(),
						Status:                beCaEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_IN_PROGRESS,
						ConsentRequestPurpose: beCaEnumPb.ConsentRequestPurpose_CONSENT_REQUEST_PURPOSE_CONSENT_FLOW,
						CaFlowName:            beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT,
					},
				}, nil)
				md.mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowDefaultConnectedAccountProcessor(md.mockCaClient), nil)
			},
			want: &feConnectedAccPb.GetDataPullStatusFromConsentHandleResponse{
				Status:             rpcPb.StatusOk(),
				DataPullStatus:     feConnectedAccPb.DataPullStatus_DATA_PULL_STATUS_FAILED,
				NextPollSeconds:    -1,
				MaxAllowedAttempts: feConf.ConnectedAccount.MaxDataPullStatusPollAttempts,
				Deeplink: &deeplinkpb.Deeplink{
					Screen: deeplinkpb.Screen_CA_POST_CONSENT_TERMINAL_STATE_SCREEN,
					ScreenOptions: beCaProcessor.GetCAConsentTerminalScreenOptions(beCaProcessor.ConnectAccountSuccessScreenHeading, beCaProcessor.ImportDataPendingScreenSubHeading, beCaProcessor.ConnectAccountSuccessScreenIcon, &deeplinkpb.Cta{
						Type: deeplinkpb.Cta_DONE,
						Text: beCaProcessor.ImportDataPendingScreenCta,
						Deeplink: &deeplinkpb.Deeplink{
							Screen: deeplinkpb.Screen_SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN,
						},
						DisplayTheme: deeplinkpb.Cta_PRIMARY,
						Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
					}, false),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
			mockCaFlowFactory := femocks.NewMockICaFlowDLFactory(ctr)
			defer ctr.Finish()
			if tt.mock != nil {
				tt.mock(&mockStruct{
					mockCaClient:      mockCaClient,
					mockCaFlowFactory: mockCaFlowFactory,
				})
			}
			s := &Service{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				conf:                 gconf,
				caFlowDLFactory:      mockCaFlowFactory,
			}
			got, err := s.GetDataPullStatusFromConsentHandle(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDataPullStatusFromConsentHandle() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDataPullStatusFromConsentHandle() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_FetchConsentParams(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
	mockUserClient := mocks2.NewMockUsersClient(ctr)
	mockActorClient := mocks3.NewMockActorClient(ctr)
	type fields struct {
		UnimplementedConnectedAccountServer feConnectedAccPb.UnimplementedConnectedAccountServer
		beConnectedAccClient                beCaPb.ConnectedAccountClient
		connectedAccountConf                *config.ConnectedAccount
		beUserClient                        userPb.UsersClient
		beActorClient                       actorPb.ActorClient
		beSavingsClient                     savings.SavingsClient
		beAuthClient                        auth.AuthClient
		conf                                *genconf.Config
		beConsentClient                     consent.ConsentClient
	}
	type args struct {
		ctx     context.Context
		request *feConnectedAccPb.FetchConsentParamsRequest
		mocks   []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *feConnectedAccPb.FetchConsentParamsResponse
		wantErr bool
	}{
		{
			name: "test for consent expiry < 1 year",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
			},
			args: args{
				ctx:     context.Background(),
				request: &feConnectedAccPb.FetchConsentParamsRequest{Req: nil},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetConsentParams(gomock.Any(), gomock.Any()).Return(&beCaPb.GetConsentParamsResponse{
						Status:        rpcPb.StatusOk(),
						DataRangeFrom: timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST)),
						DataRangeTo:   timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
						Purpose:       "Personal Finance Management",
						ConsentExpiry: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, datetime.IST)),
						ConsentTypes:  &beCaPb.ConsentTypes{ConsentTypeList: []beCaEnumPb.ConsentType{beCaEnumPb.ConsentType_CONSENT_TYPE_SUMMARY}},
						Frequency: &beCaPb.Frequency{
							Unit: beCaEnumPb.FrequencyUnit_FREQUENCY_UNIT_DAY,
							Val:  24,
						},
					}, nil),
				},
			},
			want: &feConnectedAccPb.FetchConsentParamsResponse{
				Status: rpcPb.StatusOk(),
				Consent: &feConnectedAccPb.Consent{
					From: &typesPb.Date{
						Year:  2020,
						Month: 12,
						Day:   31,
					},
					To: &typesPb.Date{
						Year:  2021,
						Month: 12,
						Day:   31,
					},
					DisplayTextRange:       "31 December, 2020 to 31 December, 2021",
					DisplayTextValidPeriod: "166 days",
				},
				ConsentMeta: &feConnectedAccPb.ConsentMeta{
					DisplayTextConsentPurpose:     "Personal Finance Management",
					DisplayTextConsentTypesShared: "SUMMARY",
					ConsentTypesShared:            []string{"CONSENT_TYPE_SUMMARY"},
					DisplayTextFetchFrequency:     "DAILY",
					FetchFrequency: &feConnectedAccPb.Frequency{
						Unit: feConnectedAccPb.FrequencyUnit_FREQUENCY_UNIT_DAY,
						Val:  24,
					},
				},
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
			},
			wantErr: false,
		},
		{
			name: "test for consent expiry > 1 year",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
			},
			args: args{
				ctx:     context.Background(),
				request: &feConnectedAccPb.FetchConsentParamsRequest{Req: nil},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetConsentParams(gomock.Any(), gomock.Any()).Return(&beCaPb.GetConsentParamsResponse{
						Status:        rpcPb.StatusOk(),
						DataRangeFrom: timestamppb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, datetime.IST)),
						DataRangeTo:   timestamppb.New(time.Date(2022, 1, 1, 0, 0, 0, 0, datetime.IST)),
						Purpose:       "Personal Finance Management",
						ConsentExpiry: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST)),
						ConsentTypes:  &beCaPb.ConsentTypes{ConsentTypeList: []beCaEnumPb.ConsentType{beCaEnumPb.ConsentType_CONSENT_TYPE_SUMMARY}},
						Frequency: &beCaPb.Frequency{
							Unit: beCaEnumPb.FrequencyUnit_FREQUENCY_UNIT_DAY,
							Val:  24,
						},
					}, nil),
				},
			},
			want: &feConnectedAccPb.FetchConsentParamsResponse{
				Status: rpcPb.StatusOk(),
				Consent: &feConnectedAccPb.Consent{
					From: &typesPb.Date{
						Year:  2020,
						Month: 12,
						Day:   31,
					},
					To: &typesPb.Date{
						Year:  2021,
						Month: 12,
						Day:   31,
					},
					DisplayTextRange:       "31 December, 2020 to 31 December, 2021",
					DisplayTextValidPeriod: "2 years",
				},
				ConsentMeta: &feConnectedAccPb.ConsentMeta{
					DisplayTextConsentPurpose:     "Personal Finance Management",
					DisplayTextConsentTypesShared: "SUMMARY",
					ConsentTypesShared:            []string{"CONSENT_TYPE_SUMMARY"},
					DisplayTextFetchFrequency:     "DAILY",
					FetchFrequency: &feConnectedAccPb.Frequency{
						Unit: feConnectedAccPb.FrequencyUnit_FREQUENCY_UNIT_DAY,
						Val:  24,
					},
				},
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{
				UnimplementedConnectedAccountServer: tt.fields.UnimplementedConnectedAccountServer,
				beConnectedAccClient:                tt.fields.beConnectedAccClient,
				connectedAccountConf:                tt.fields.connectedAccountConf,
				beUserClient:                        tt.fields.beUserClient,
				beActorClient:                       tt.fields.beActorClient,
				beSavingsClient:                     tt.fields.beSavingsClient,
				beAuthClient:                        tt.fields.beAuthClient,
				conf:                                tt.fields.conf,
				beConsentClient:                     tt.fields.beConsentClient,
			}
			got, err := s.FetchConsentParams(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchConsentParams() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.NotNil(t, got)
		})
	}
}

func TestService_GetConnectedAccountEntryPoints(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
	mockUserClient := mocks2.NewMockUsersClient(ctr)
	mockActorClient := mocks3.NewMockActorClient(ctr)
	mockSavingsClient := mocks5.NewMockSavingsClient(ctr)
	mockBeConsentClient := mocks6.NewMockConsentClient(ctr)
	mockOnbClient := mocks4.NewMockOnboardingClient(ctr)
	dl := &deeplinkpb.Deeplink{
		Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW,
	}
	entryPointsMap := make(map[string]*feConnectedAccPb.EntryPointOptions)
	entryPointsMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_HOME.String()] = &feConnectedAccPb.EntryPointOptions{
		Enabled:  true,
		Text:     gconf.ConnectedAccount().HomeEntryPoint().Text(),
		LogoUrl:  gconf.ConnectedAccount().HomeEntryPoint().LogoUrl(),
		Deeplink: dl,
	}
	entryPointsMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_SEARCH.String()] = &feConnectedAccPb.EntryPointOptions{
		Enabled:  true,
		Text:     gconf.ConnectedAccount().SearchEntryPoint().Text(),
		LogoUrl:  gconf.ConnectedAccount().SearchEntryPoint().LogoUrl(),
		Deeplink: dl,
	}
	entryPointsMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_ANALYSER.String()] = &feConnectedAccPb.EntryPointOptions{
		Enabled:  true,
		Text:     gconf.ConnectedAccount().AnalyserEntryPoint().Text(),
		LogoUrl:  gconf.ConnectedAccount().AnalyserEntryPoint().LogoUrl(),
		Deeplink: dl,
	}
	entryPointsMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_PROFILE.String()] = &feConnectedAccPb.EntryPointOptions{
		Enabled:  true,
		Text:     gconf.ConnectedAccount().ProfileEntryPoint().Text(),
		LogoUrl:  gconf.ConnectedAccount().ProfileEntryPoint().LogoUrl(),
		Deeplink: dl,
	}
	entryPointsMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_ACCOUNT_MANAGER.String()] = &feConnectedAccPb.EntryPointOptions{
		Enabled:  true,
		Text:     gconf.ConnectedAccount().AccountManagerEntryPoint().Text(),
		LogoUrl:  gconf.ConnectedAccount().AccountManagerEntryPoint().LogoUrl(),
		Deeplink: dl,
	}
	entryPointsMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_ALL_TRANSACTIONS.String()] = &feConnectedAccPb.EntryPointOptions{
		Enabled:  true,
		Text:     gconf.ConnectedAccount().AllTransactionsEntryPoint().Text(),
		LogoUrl:  gconf.ConnectedAccount().AllTransactionsEntryPoint().LogoUrl(),
		Deeplink: dl,
	}
	entryPointsMap[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_SEARCH_BANNER.String()] = &feConnectedAccPb.EntryPointOptions{
		Enabled:  true,
		Text:     gconf.ConnectedAccount().SearchBannerEntryPoint().Text(),
		LogoUrl:  gconf.ConnectedAccount().SearchBannerEntryPoint().LogoUrl(),
		Deeplink: dl,
	}
	entryPointsMapWithoutHome := make(map[string]*feConnectedAccPb.EntryPointOptions)
	for key, value := range entryPointsMap {
		entryPointsMapWithoutHome[key] = value
	}
	entryPointsMapWithoutHome[feConnectedAccPb.EntryPointType_ENTRY_POINT_TYPE_HOME.String()] = &feConnectedAccPb.EntryPointOptions{
		Enabled: false,
	}
	type fields struct {
		UnimplementedConnectedAccountServer feConnectedAccPb.UnimplementedConnectedAccountServer
		beConnectedAccClient                beCaPb.ConnectedAccountClient
		connectedAccountConf                *config.ConnectedAccount
		beUserClient                        userPb.UsersClient
		beActorClient                       actorPb.ActorClient
		beSavingsClient                     savings.SavingsClient
		conf                                *genconf.Config
		consentClient                       consent.ConsentClient
		onbClient                           onbPb.OnboardingClient
	}
	type args struct {
		ctx     context.Context
		request *feConnectedAccPb.GetConnectedAccountEntryPointsRequest
		mocks   []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *feConnectedAccPb.GetConnectedAccountEntryPointsResponse
		wantErr bool
	}{
		{
			name: "home entry: no need to display, already have connected accounts",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beSavingsClient:      mockSavingsClient,
				consentClient:        mockBeConsentClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.GetConnectedAccountEntryPointsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetAllAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAllAccountsResponse{
						Status:             rpcPb.StatusOk(),
						AccountDetailsList: []*beCaExtPb.AccountDetails{{}},
					}, nil),
					mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
						ActorId: "actor-id-1",
					}).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil),
				},
			},
			want: &feConnectedAccPb.GetConnectedAccountEntryPointsResponse{
				RespHeader:                &header.ResponseHeader{Status: rpcPb.StatusOk()},
				EntryPointMap:             entryPointsMapWithoutHome,
				IsConnectedAccountEnabled: commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "home entry: display",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beSavingsClient:      mockSavingsClient,
				consentClient:        mockBeConsentClient,
				onbClient:            mockOnbClient,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.GetConnectedAccountEntryPointsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetAllAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAllAccountsResponse{
						Status:             rpcPb.StatusOk(),
						AccountDetailsList: []*beCaExtPb.AccountDetails{},
					}, nil),
					mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
						ActorId: "actor-id-1",
					}).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil),
				},
			},
			want: &feConnectedAccPb.GetConnectedAccountEntryPointsResponse{
				RespHeader:                &header.ResponseHeader{Status: rpcPb.StatusOk()},
				EntryPointMap:             entryPointsMap,
				IsConnectedAccountEnabled: commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "home entry, error: error in CA client",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				beUserClient:         mockUserClient,
				beActorClient:        mockActorClient,
				conf:                 gconf,
				beSavingsClient:      mockSavingsClient,
				consentClient:        mockBeConsentClient,
			},
			args: args{
				ctx: context.Background(),
				request: &feConnectedAccPb.GetConnectedAccountEntryPointsRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetAllAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAllAccountsResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
			},
			want: &feConnectedAccPb.GetConnectedAccountEntryPointsResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{
				UnimplementedConnectedAccountServer: tt.fields.UnimplementedConnectedAccountServer,
				beConnectedAccClient:                tt.fields.beConnectedAccClient,
				connectedAccountConf:                tt.fields.connectedAccountConf,
				beUserClient:                        tt.fields.beUserClient,
				beActorClient:                       tt.fields.beActorClient,
				beSavingsClient:                     tt.fields.beSavingsClient,
				conf:                                tt.fields.conf,
				beConsentClient:                     tt.fields.consentClient,
				onbClient:                           tt.fields.onbClient,
			}
			got, err := s.GetConnectedAccountEntryPoints(tt.args.ctx, tt.args.request)
			time.Sleep(time.Second)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConnectedAccountEntryPoints() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetConnectedAccountEntryPoints() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetAuthToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockCaClient := mocks.NewMockConnectedAccountClient(ctrl)
	type fields struct {
		beConnectedAccClient beCaPb.ConnectedAccountClient
	}
	type args struct {
		ctx   context.Context
		req   *feConnectedAccPb.GetAuthTokenRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *feConnectedAccPb.GetAuthTokenResponse
		wantErr bool
	}{
		{
			name: "#1 success",
			fields: fields{
				beConnectedAccClient: mockCaClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetAuthTokenRequest{
					AaEntity: feConnectedAccPb.AaEntity_AA_ENTITY_AA_FINVU,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetAuthToken(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAuthTokenResponse{
						Token:  "jwt-token-1",
						Status: rpcPb.StatusOk(),
					}, nil),
				},
			},
			want:    &feConnectedAccPb.GetAuthTokenResponse{Token: "jwt-token-1", RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()}},
			wantErr: false,
		},
		{
			name: "#2 invalid argument from be client",
			fields: fields{
				beConnectedAccClient: mockCaClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetAuthTokenRequest{
					AaEntity: feConnectedAccPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
				},
			},
			want:    &feConnectedAccPb.GetAuthTokenResponse{RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInvalidArgument()}},
			wantErr: false,
		},
		{
			name: "#3 internal error",
			fields: fields{
				beConnectedAccClient: mockCaClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetAuthTokenRequest{
					AaEntity: feConnectedAccPb.AaEntity_AA_ENTITY_AA_FINVU,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetAuthToken(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAuthTokenResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
			},
			want:    &feConnectedAccPb.GetAuthTokenResponse{RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternal()}},
			wantErr: false,
		},
		{
			name: "#4 unspecified aa entity",
			fields: fields{
				beConnectedAccClient: mockCaClient,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetAuthTokenRequest{
					AaEntity: feConnectedAccPb.AaEntity_AA_ENTITY_UNSPECIFIED,
				},
			},
			want:    &feConnectedAccPb.GetAuthTokenResponse{RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInvalidArgument()}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				beConnectedAccClient: tt.fields.beConnectedAccClient,
			}
			got, err := s.GetAuthToken(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetJwtToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.NotNil(t, got)
			if err == nil && !tt.want.GetRespHeader().GetStatus().IsEqualTo(got.GetRespHeader().GetStatus()) {
				t.Errorf("GetJwtToken() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetConnectedAccountsSummaryForHome(t *testing.T) {
	onemoneyTncMessage := "I agree to OneMoney’s <a href=\"" + feConf.LegalDocuments.AaOnemoneyTncUrl + "\">Terms & Conditions</a>"
	balDisplayString1 := money2.ToDisplayStringWithSuffixAndPrecision(&money.Money{
		CurrencyCode: "INR",
		Units:        420,
		Nanos:        0,
	}, false, true, 1, money2.IndianNumberSystem)
	balDisplayString2 := money2.ToDisplayStringWithSuffixAndPrecision(&money.Money{
		CurrencyCode: "INR",
		Units:        220,
		Nanos:        0,
	}, false, true, 1, money2.IndianNumberSystem)

	comingSoonDl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_CONNECTED_ACCOUNT_TRANSACTIONS_COMING_SOON,
		ScreenOptions: &deeplinkPb.Deeplink_ConnectedAccountTransactionsComingSoon{
			ConnectedAccountTransactionsComingSoon: &deeplinkPb.ConnectedAccountTransactionsComingSoonOptions{
				Title:    fmt.Sprintf(txnComingSoonTitle, "HDFC"),
				SubTitle: txnComingSoonSubtitle,
				Cta: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_DONE,
					Text:         "Okay",
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
			},
		},
	}

	dl := &deeplinkpb.Deeplink{
		Screen: deeplinkpb.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN,
		ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountBenefitsOptions{
			ConnectedAccountBenefitsOptions: &deeplinkpb.ConnectedAccountBenefitsOptions{
				ProceedCta: &deeplinkpb.Cta{
					Text: "PROCEED",
					Deeplink: &deeplinkpb.Deeplink{
						Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_SDK,
						ScreenOptions: &deeplinkpb.Deeplink_ConnectedAccountsOptions{
							ConnectedAccountsOptions: &deeplinkpb.ConnectedAccountsOptions{
								MobileNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
								// passing pan name as profile name is deprecated
								Name:                   "Hardik",
								AaEntity:               deeplinkpb.ConnectedAccountsOptions_AA_ENTITY_ONE_MONEY,
								FiuId:                  "",
								AaTncMessage:           onemoneyTncMessage,
								UseV2Flow:              gconf.ConnectedAccount().V2FlowParams().UseV2Flow(),
								UseTokenAuthentication: commontypes.BooleanEnum_FALSE,
								CaFlowName:             beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
							},
						},
					},
					DisplayTheme: deeplinkpb.Cta_PRIMARY,
					Status:       deeplinkpb.Cta_CTA_STATUS_ENABLED,
				},
				WealthTncCheckBox: &widget.CheckboxItem{
					Id: consent.ConsentType_FI_WEALTH_TNC.String(),
					DisplayText: &commontypes.Text{
						FontColor:        "#8D8D8D",
						FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
						FontColorOpacity: 100,
						DisplayValue:     &commontypes.Text_Html{Html: fmt.Sprintf("I agree & accept <a style=\"color: #00B899\" href=\"%s\">epiFi Wealth TnC</a> and <a style=\"color: #00B899\" href=\"%s\">OneMoney TnC</a>", feConf.LegalDocuments.FiWealthTncUrl, feConf.LegalDocuments.AaOnemoneyTncUrl)},
					},
					IsChecked: true,
				},
				CaFlowName: beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String(),
			}},
	}

	footerTicker1 := &homePb.HomeDashboard_FooterTicker{
		TickerItems: []*homePb.HomeDashboard_FooterTicker_TickerItem{
			{
				TickerContent: ui.NewITC().
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle(ZeroStateTickerText, ZeroStateTickerTextFontColor, commontypes.FontStyle_SUBTITLE_S)).
					WithDeeplink(dl),
			},
		},
		TickerItemsPrivacy: []*homePb.HomeDashboard_FooterTicker_TickerItem{
			{
				TickerContent: ui.NewITC().
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle(ZeroStateTickerText, ZeroStateTickerTextFontColor, commontypes.FontStyle_SUBTITLE_S)).
					WithDeeplink(dl),
			},
		},
	}
	footerTicker2 := &homePb.HomeDashboard_FooterTicker{
		TickerItems: []*homePb.HomeDashboard_FooterTicker_TickerItem{
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth(gconf.ConnectedAccount().FiBankIconUrl(), CaDashFipLogoIconHeight, CaDashFipLogoIconWidth).
					WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle(fiBank+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
						commontypes.GetTextFromStringFontColourFontStyle("**4321"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
					).
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle(balDisplayString1+" ", CaDashTickerMoneyValueTextColor, commontypes.FontStyle_SUBTITLE_S)).
					WithDeeplink(primarySavingsAccSummaryDl()).
					WithContainerPadding(CaHomeDashboardTickerTopPadding, CaHomeDashboardTickerRightPadding, CaHomeDashboardTickerBottomPadding, CaHomeDashboardTickerLeftPadding),
			},
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png", CaDashFipLogoIconHeight, CaDashFipLogoIconWidth).
					WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle("HDFC"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
						commontypes.GetTextFromStringFontColourFontStyle("**1234"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
					).
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle(balDisplayString2+" ", CaDashTickerMoneyValueTextColor, commontypes.FontStyle_SUBTITLE_S)).
					WithDeeplink(comingSoonDl).
					WithContainerPadding(CaHomeDashboardTickerTopPadding, CaHomeDashboardTickerRightPadding, CaHomeDashboardTickerBottomPadding, CaHomeDashboardTickerLeftPadding),
			},
		},
		TickerItemsPrivacy: []*homePb.HomeDashboard_FooterTicker_TickerItem{
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth(gconf.ConnectedAccount().FiBankIconUrl(), CaDashFipLogoIconHeight, CaDashFipLogoIconWidth).
					WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle(fiBank+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
						commontypes.GetTextFromStringFontColourFontStyle("**4321"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
					).
					WithDeeplink(primarySavingsAccSummaryDl()).
					WithContainerPadding(CaHomeDashboardTickerTopPadding, CaHomeDashboardTickerRightPadding, CaHomeDashboardTickerBottomPadding, CaHomeDashboardTickerLeftPadding),
			},
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png", CaDashFipLogoIconHeight, CaDashFipLogoIconWidth).
					WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle("HDFC"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
						commontypes.GetTextFromStringFontColourFontStyle("**1234"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
					).
					WithDeeplink(comingSoonDl).
					WithContainerPadding(CaHomeDashboardTickerTopPadding, CaHomeDashboardTickerRightPadding, CaHomeDashboardTickerBottomPadding, CaHomeDashboardTickerLeftPadding),
			},
		},
	}
	footerTicker3 := &homePb.HomeDashboard_FooterTicker{
		TickerItems: []*homePb.HomeDashboard_FooterTicker_TickerItem{
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth(gconf.ConnectedAccount().FiBankIconUrl(), CaDashFipLogoIconHeight, CaDashFipLogoIconWidth).
					WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle(fiBank+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
						commontypes.GetTextFromStringFontColourFontStyle("**4321"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
					).
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle(balDisplayString1+" ", CaDashTickerMoneyValueTextColor, commontypes.FontStyle_SUBTITLE_S)).
					WithDeeplink(primarySavingsAccSummaryDl()).
					WithContainerPadding(CaHomeDashboardTickerTopPadding, CaHomeDashboardTickerRightPadding, CaHomeDashboardTickerBottomPadding, CaHomeDashboardTickerLeftPadding),
			},
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png", CaDashFipLogoIconHeight, CaDashFipLogoIconWidth).
					WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle("HDFC"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
						commontypes.GetTextFromStringFontColourFontStyle("**5678"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
					).
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle(balDisplayString2+" ", CaDashTickerMoneyValueTextColor, commontypes.FontStyle_SUBTITLE_S)).
					WithDeeplink(comingSoonDl).
					WithContainerPadding(CaHomeDashboardTickerTopPadding, CaHomeDashboardTickerRightPadding, CaHomeDashboardTickerBottomPadding, CaHomeDashboardTickerLeftPadding),
			},
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth(CaDashWarningTickerIconUrl, CaDashWarningTickerIconHeight, CaDashWarningTickerIconWidth).
					WithTexts(getStoppedSyncWarningText(1)).
					WithLeftImagePadding(CaDashWarningTickerIconTextPadding).
					WithDeeplink(getStopSyncedWarningPopupDeeplink(accountSummaryDl, 2, 1)),
				TickerType: homePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_WARNING_ORANGE,
			},
		},
		TickerItemsPrivacy: []*homePb.HomeDashboard_FooterTicker_TickerItem{
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth(gconf.ConnectedAccount().FiBankIconUrl(), CaDashFipLogoIconHeight, CaDashFipLogoIconWidth).
					WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle(fiBank+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
						commontypes.GetTextFromStringFontColourFontStyle("**4321"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
					).
					WithDeeplink(primarySavingsAccSummaryDl()).
					WithContainerPadding(CaHomeDashboardTickerTopPadding, CaHomeDashboardTickerRightPadding, CaHomeDashboardTickerBottomPadding, CaHomeDashboardTickerLeftPadding),
			},
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png", CaDashFipLogoIconHeight, CaDashFipLogoIconWidth).
					WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle("HDFC"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
						commontypes.GetTextFromStringFontColourFontStyle("**5678"+" ", CaDashTickerBankNameTextColor, commontypes.FontStyle_SUBTITLE_S),
					).
					WithDeeplink(comingSoonDl).
					WithContainerPadding(CaHomeDashboardTickerTopPadding, CaHomeDashboardTickerRightPadding, CaHomeDashboardTickerBottomPadding, CaHomeDashboardTickerLeftPadding),
			},
			{
				TickerContent: ui.NewITC().
					WithLeftVisualElementUrlHeightAndWidth(CaDashWarningTickerIconUrl, CaDashWarningTickerIconHeight, CaDashWarningTickerIconWidth).
					WithTexts(getStoppedSyncWarningText(1)).
					WithLeftImagePadding(CaDashWarningTickerIconTextPadding).
					WithDeeplink(getStopSyncedWarningPopupDeeplink(accountSummaryDl, 2, 1)),
				TickerType: homePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_WARNING_ORANGE,
			},
		},
	}
	type mockStruct struct {
		beConnectedAccClient  *mocks.MockConnectedAccountClient
		conf                  *genconf.Config
		beUserClient          *mocks2.MockUsersClient
		beActorClient         *mocks3.MockActorClient
		beSavingsClient       *mocks5.MockSavingsClient
		beConsentClient       *mocks6.MockConsentClient
		onbClient             *mocks4.MockOnboardingClient
		accountBalanceClient  *accountBalanceMock.MockBalanceClient
		releaseClient         *mock_release.MockIEvaluator
		userAttributesFetcher *pkgUserMocks.MockUserAttributesFetcher
	}
	type args struct {
		req *feConnectedAccPb.GetConnectedAccountsSummaryForHomeRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *feConnectedAccPb.GetConnectedAccountsSummaryForHomeResponse
		wantErr  bool
		mockFunc func(*mockStruct)
	}{
		{
			name: "success, non zero case",
			args: args{
				req: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
				},
			},
			mockFunc: func(m *mockStruct) {
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-1")).Return(false, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId("actor-id-1")).Return(false, nil)
				m.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil).Times(2)
				m.beSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1"}}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{Status: rpcPb.StatusOk(), AvailableBalance: &money.Money{CurrencyCode: "INR", Units: 420, Nanos: 69}}, nil)
				m.beConnectedAccClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{Status: rpcPb.StatusOk(), AccountDetailsList: []*beCaExtPb.AccountDetails{{AccountId: "acc-id-2",
					FipId:         "HDFC-FIP",
					FipMeta:       &beCaExtPb.FipMeta{LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png"},
					AccountStatus: beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
				}}}, nil)
				m.beConnectedAccClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountDetailsBulkResponse{
					Status: rpcPb.StatusOk(),
					AccountDetailsMap: map[string]*beCaPb.AccountProfileSummaryDetails{
						"acc-id-2": {
							AccountDetails: &beCaExtPb.AccountDetails{
								AccountId:           "acc-id-2",
								MaskedAccountNumber: "XXXXXX9123",
								IfscCode:            "test-ifsc",
								AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
								AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
								LastSyncedAt:        timestamppb.Now(),
								AaEntity:            beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
								FipId:               "HDFC-FIP",
								FipMeta: &beCaExtPb.FipMeta{
									FipId: "HDFC-FIP",
								},
							},
							Summary: &beCaPb.AccountProfileSummaryDetails_DepositSummary{
								DepositSummary: &beCaExtPb.DepositSummary{
									AccountId:            "acc-id-1",
									DepositAccountType:   beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS,
									DepositAccountStatus: beCaEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_ACTIVE,
									CurrentBalance: &money.Money{
										CurrencyCode: "INR",
										Units:        220,
									},
									BalanceDate: timestamppb.Now(),
									IfscCode:    "test-ifsc",
								},
							},
						},
					},
				}, nil)
				// deeplink mocks
				m.beConnectedAccClient.EXPECT().CheckReoobe(gomock.Any(), gomock.Any()).Return(&beCaPb.CheckReoobeResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
				m.beConnectedAccClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{Status: rpcPb.StatusOk(), AaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY}, nil)
				m.beActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actor-id-7",
						EntityId: "user-id-7",
					},
				}, nil)
				m.beUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpcPb.StatusOk(),
					User: &userPb.User{Id: "user-id-7", Profile: &userPb.Profile{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						PanName: &commontypes.Name{FirstName: "Hardik"},
						PAN:     "Hardik",
						DateOfBirth: &date.Date{
							Year:  2000,
							Month: 1,
							Day:   1,
						},
					}},
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: "actor-id-1",
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				DashboardInfo: &homePb.HomeDashboard{
					Title: &commontypes.Text{
						FontColor: HomeDashboardTitleFontColor,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: HomeDashboardTitle,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
						},
					},
					Body: &homePb.HomeDashboard_Body{
						PrivacyModeImage: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  PrivacyModeImageUrl,
							Width:     int32(PrivacyModeImageWidth),
							Height:    int32(PrivacyModeImageHeight),
						},
						MoneyValue: []*commontypes.Text{
							{
								FontColor: MoneyCurrencyTextFontColor,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "₹",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_RUPEE_XL},
							},
							{
								FontColor: MoneyUnitsTextFontColor,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "640",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
							},
						},
						MoneyValueV2: []*ui.IconTextComponent{
							ui.NewITC().WithTexts(&commontypes.Text{
								FontColor: MoneyCurrencyTextFontColor,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "₹",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_RUPEE_XL},
							}),
							ui.NewITC().WithTexts(&commontypes.Text{
								FontColor: MoneyUnitsTextFontColor,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "640",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
							}),
						},
						DashboardState: homePb.HomeDashboard_Body_STATE_NON_ZERO,
					},
					AdditionalSettingsIcons: []*homePb.Icon{
						{
							Title: &commontypes.Text{
								FontColor:    EyeCaHomeDashboardIconTextColor,
								DisplayValue: &commontypes.Text_PlainString{PlainString: EyeCaHomeDashboardIconText},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
							},
							TitleOnSelection: &commontypes.Text{
								FontColor:    EyeCaHomeDashboardIconTextColor,
								DisplayValue: &commontypes.Text_PlainString{PlainString: EyeCaHomeDashboardIconTextOnSelection},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
							},
							IconImage: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  EyeCaHomeDashboardIconImageUrl,
							},
							IconImageOnSelection: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  EyeCaHomeDashboardIconImageOnSelectionUrl,
							},
							IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
							ActionType: homePb.Icon_ACTION_TYPE_TOGGLE_PRIVACY,
							BgColour:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DashboardIconsBgColour}},
						},
						{
							Title: &commontypes.Text{
								FontColor:    LinkCaHomeDashboardIconTextColor,
								DisplayValue: &commontypes.Text_PlainString{PlainString: LinkCaHomeDashboardIconText},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
							},
							IconImage: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  LinkCaHomeDashboardIconImageUrl,
							},
							IconType:   homeTypesPb.IconType_CONNECT_ACCOUNT,
							ActionType: homePb.Icon_ACTION_TYPE_DEEPLINK,
							Action: &homePb.Icon_Deeplink{
								Deeplink: dl,
							},
							BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DashboardIconsBgColour}},
						},
					},
					Footer: []*ui.IconTextComponent{
						{
							LeftIcon: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  gconf.ConnectedAccount().FiBankIconUrl(),
								Width:     int32(FooterLeftCaHomeDashboardIconWidth),
								Height:    int32(FooterLeftCaHomeDashboardIconHeight),
							},
							Texts: []*commontypes.Text{
								{
									FontColor: FooterBalanceTextFontColor,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "420.0",
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
									},
								},
							},
							RightIcon: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  FooterRightIconUrl,
								Width:     int32(FooterRightIconWidth),
								Height:    int32(FooterRightIconHeight),
							},
							LeftImgTxtPadding: int32(FooterLeftImageTxtPadding),
							Deeplink:          accountSummaryDl,
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								BgColor:       FooterBgColor,
								CornerRadius:  int32(FooterCornerRadius),
								Height:        int32(FooterBalanceTextHeight),
								Width:         int32(FooterBalanceTextWidth),
								LeftPadding:   int32(FooterLeftPadding),
								RightPadding:  int32(FooterRightPadding),
								TopPadding:    int32(FooterTopPadding),
								BottomPadding: int32(FooterBottomPadding),
							},
						},
						{
							LeftIcon: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
								Width:     int32(FooterLeftCaHomeDashboardIconWidth),
								Height:    int32(FooterLeftCaHomeDashboardIconHeight),
							},
							Texts: []*commontypes.Text{
								{
									FontColor: FooterBalanceTextFontColor,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "220.0",
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
									},
								},
							},
							LeftImgTxtPadding: int32(FooterLeftImageTxtPadding),
							Deeplink:          accountSummaryDl,
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								BgColor:       FooterBgColor,
								CornerRadius:  int32(FooterCornerRadius),
								Height:        int32(FooterBalanceTextHeight),
								Width:         int32(FooterBalanceTextWidth),
								LeftPadding:   int32(FooterLeftPadding),
								RightPadding:  int32(FooterRightPadding),
								TopPadding:    int32(FooterTopPadding),
								BottomPadding: int32(FooterBottomPadding),
							},
						},
					},
					PrivacyModeFooter: []*ui.IconTextComponent{
						{
							LeftIcon: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  gconf.ConnectedAccount().FiBankIconUrl(),
								Width:     int32(FooterLeftCaHomeDashboardIconWidth),
								Height:    int32(FooterLeftCaHomeDashboardIconHeight),
							},
							Deeplink: accountSummaryDl,
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								BgColor:       FooterBgColor,
								CornerRadius:  int32(FooterCornerRadius),
								Height:        int32(FooterBalanceTextHeight),
								Width:         int32(FooterBalanceTextWidth),
								LeftPadding:   int32(FooterLeftPadding),
								RightPadding:  int32(FooterRightPadding),
								TopPadding:    int32(FooterTopPadding),
								BottomPadding: int32(FooterBottomPadding),
							},
						},
						{
							LeftIcon: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
								Width:     int32(FooterLeftCaHomeDashboardIconWidth),
								Height:    int32(FooterLeftCaHomeDashboardIconHeight),
							},
							Deeplink: accountSummaryDl,
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								BgColor:       FooterBgColor,
								CornerRadius:  int32(FooterCornerRadius),
								Height:        int32(FooterBalanceTextHeight),
								Width:         int32(FooterBalanceTextWidth),
								LeftPadding:   int32(FooterLeftPadding),
								RightPadding:  int32(FooterRightPadding),
								TopPadding:    int32(FooterTopPadding),
								BottomPadding: int32(FooterBottomPadding),
							},
						},
					},
					DashboardFooter:     &homePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: nil},
					Deeplink:            accountSummaryDl,
					Shadow:              ui.GetDashboardShadow(),
					DashboardBackground: homePb.GetHomeDashboardSectionBackground(),
					BorderColor:         homePb.GetHomeDashboardBorderColor(),
				},
			},
			wantErr: false,
		},
		{
			name: "success, zero state",
			args: args{
				req: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
				},
			},
			mockFunc: func(m *mockStruct) {
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-1")).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId("actor-id-1")).Return(false, nil)
				m.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil).Times(2)
				m.beSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1"}}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{Status: rpcPb.StatusOk(), AvailableBalance: &money.Money{CurrencyCode: "INR", Units: 420, Nanos: 69}}, nil)
				m.beConnectedAccClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{Status: rpcPb.StatusRecordNotFound()}, nil)
				// deeplink mocks
				m.beConnectedAccClient.EXPECT().CheckReoobe(gomock.Any(), gomock.Any()).Return(&beCaPb.CheckReoobeResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
				m.beConnectedAccClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{Status: rpcPb.StatusOk(), AaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY}, nil)
				m.beActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actor-id-7",
						EntityId: "user-id-7",
					},
				}, nil)
				m.beUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpcPb.StatusOk(),
					User: &userPb.User{Id: "user-id-7", Profile: &userPb.Profile{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						PanName: &commontypes.Name{FirstName: "Hardik"},
						PAN:     "Hardik",
						DateOfBirth: &date.Date{
							Year:  2000,
							Month: 1,
							Day:   1,
						},
					}},
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: "actor-id-1",
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				DashboardInfo: &homePb.HomeDashboard{
					Title: &commontypes.Text{
						FontColor: HomeDashboardTitleFontColor,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: HomeDashboardTitle,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
						},
					},
					Body: &homePb.HomeDashboard_Body{
						DashboardIcons: []*homePb.Icon{
							{
								IconImage: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  ZeroStateIconImageUrl,
									Width:     int32(ZeroStateIconImageWidth),
									Height:    int32(ZeroStateIconImageHeight),
								},
								Action: &homePb.Icon_Deeplink{
									Deeplink: dl,
								},
								IconType:   homeTypesPb.IconType_CONNECT_ACCOUNT,
								ActionType: homePb.Icon_ACTION_TYPE_DEEPLINK,
								Title: &commontypes.Text{
									FontColor: ZeroStateIconFontColor,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: ZeroStateIconDisplayValue,
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
									},
								},
								BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: ZeroStateIconBgColor}},
							},
						},
						DashboardState: homePb.HomeDashboard_Body_STATE_ZERO,
					},
					AdditionalSettingsIcons: []*homePb.Icon{
						{
							Title: &commontypes.Text{
								FontColor:    EyeCaHomeDashboardIconTextColor,
								DisplayValue: &commontypes.Text_PlainString{PlainString: EyeCaHomeDashboardIconText},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
							},
							TitleOnSelection: &commontypes.Text{
								FontColor:    EyeCaHomeDashboardIconTextColor,
								DisplayValue: &commontypes.Text_PlainString{PlainString: EyeCaHomeDashboardIconTextOnSelection},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
							},
							IconImage: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  EyeCaHomeDashboardIconImageUrl,
							},
							IconImageOnSelection: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  EyeCaHomeDashboardIconImageOnSelectionUrl,
							},
							IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
							ActionType: homePb.Icon_ACTION_TYPE_TOGGLE_PRIVACY,
							BgColour:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DashboardIconsBgColour}},
						},
						{
							Title: &commontypes.Text{
								FontColor:    LinkCaHomeDashboardIconTextColor,
								DisplayValue: &commontypes.Text_PlainString{PlainString: LinkCaHomeDashboardIconText},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
							},
							IconImage: &commontypes.Image{
								ImageType: commontypes.ImageType_PNG,
								ImageUrl:  LinkCaHomeDashboardIconImageUrl,
							},
							IconType:   homeTypesPb.IconType_CONNECT_ACCOUNT,
							ActionType: homePb.Icon_ACTION_TYPE_DEEPLINK,
							Action: &homePb.Icon_Deeplink{
								Deeplink: dl,
							},
							BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DashboardIconsBgColour}},
						},
					},
					Footer: []*ui.IconTextComponent{
						{
							Texts: []*commontypes.Text{
								{
									FontColor: ZeroStateFooterPropertiesTextFontColor,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: ZeroStateFooterPropertiesTextDisplayValue,
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
									},
								},
							},
						},
					},
					PrivacyModeFooter: []*ui.IconTextComponent{
						{
							Texts: []*commontypes.Text{
								{
									FontColor: ZeroStateFooterPropertiesTextFontColor,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: ZeroStateFooterPropertiesTextDisplayValue,
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
									},
								},
							},
						},
					},
					DashboardFooter:     &homePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: nil},
					Deeplink:            dl,
					Shadow:              ui.GetDashboardShadow(),
					DashboardBackground: homePb.GetHomeDashboardSectionBackground(),
					BorderColor:         homePb.GetHomeDashboardBorderColor(),
				},
			},
			wantErr: false,
		},
		{
			name: "v2, zero state",
			args: args{
				req: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					DashboardVersion: homePb.DashboardVersion_DASHBOARD_VERSION_V2,
				},
			},
			mockFunc: func(m *mockStruct) {
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-1")).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId("actor-id-1")).Return(false, nil)
				m.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil).Times(2)
				m.beSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1"}}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{Status: rpcPb.StatusOk(), AvailableBalance: &money.Money{CurrencyCode: "INR", Units: 420, Nanos: 69}}, nil)
				m.beConnectedAccClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{Status: rpcPb.StatusRecordNotFound()}, nil)
				// deeplink mocks
				m.beConnectedAccClient.EXPECT().CheckReoobe(gomock.Any(), gomock.Any()).Return(&beCaPb.CheckReoobeResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
				m.beConnectedAccClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{Status: rpcPb.StatusOk(), AaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY}, nil)
				m.beActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actor-id-7",
						EntityId: "user-id-7",
					},
				}, nil)
				m.beUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpcPb.StatusOk(),
					User: &userPb.User{Id: "user-id-7", Profile: &userPb.Profile{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						PanName: &commontypes.Name{FirstName: "Hardik"},
						PAN:     "Hardik",
						DateOfBirth: &date.Date{
							Year:  2000,
							Month: 1,
							Day:   1,
						},
					}},
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: "actor-id-1",
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				DashboardInfo: &homePb.HomeDashboard{
					Title: &commontypes.Text{
						FontColor: HomeDashboardTitleFontColorV2,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: HomeDashboardTitleV2,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_S,
						},
					},
					Body: &homePb.HomeDashboard_Body{
						DashboardIcons: []*homePb.Icon{
							{
								IconImage: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  ZeroStateIconImageUrlV2,
									Width:     int32(ZeroStateIconImageWidthV2),
									Height:    int32(ZeroStateIconImageHeightV2),
								},
								Action: &homePb.Icon_Deeplink{
									Deeplink: dl,
								},
								IconType:   homeTypesPb.IconType_CONNECT_ACCOUNT,
								ActionType: homePb.Icon_ACTION_TYPE_DEEPLINK,
								Title: &commontypes.Text{
									FontColor: ZeroStateIconFontColorV2,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: ZeroStateIconDisplayValueV2,
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_BUTTON_S,
									},
								},
								BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: ZeroStateIconBgColorV2}},
							},
						},
						DashboardState: homePb.HomeDashboard_Body_STATE_ZERO,
					},
					Deeplink:            dl,
					Shadow:              ui.GetDashboardShadow(),
					FooterTicker:        footerTicker1,
					DashboardFooter:     &homePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker1},
					DashboardBackground: homePb.GetHomeDashboardSectionBackground(),
					BorderColor:         homePb.GetHomeDashboardBorderColor(),
				},
			},
			wantErr: false,
		},
		{
			name: "v2, non zero case",
			args: args{
				req: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					DashboardVersion: homePb.DashboardVersion_DASHBOARD_VERSION_V2,
				},
			},
			mockFunc: func(m *mockStruct) {
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-1")).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId("actor-id-1")).Return(false, nil)
				m.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil).Times(2)
				m.beSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1", AccountNo: "********"}}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{Status: rpcPb.StatusOk(), AvailableBalance: &money.Money{CurrencyCode: "INR", Units: 420, Nanos: 69}}, nil)
				m.beConnectedAccClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{Status: rpcPb.StatusOk(), AccountDetailsList: []*beCaExtPb.AccountDetails{{AccountId: "acc-id-2",
					FipId:               "HDFC-FIP",
					FipMeta:             &beCaExtPb.FipMeta{LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png", DisplayName: "HDFC", Name: "hdfc"},
					AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
					MaskedAccountNumber: "**1234",
				}}}, nil)
				m.beConnectedAccClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountDetailsBulkResponse{
					Status: rpcPb.StatusOk(),
					AccountDetailsMap: map[string]*beCaPb.AccountProfileSummaryDetails{
						"acc-id-2": {
							AccountDetails: &beCaExtPb.AccountDetails{
								AccountId:           "acc-id-2",
								MaskedAccountNumber: "XXXXXX9123",
								IfscCode:            "test-ifsc",
								AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
								AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
								LastSyncedAt:        timestamppb.Now(),
								AaEntity:            beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
								FipId:               "HDFC-FIP",
								FipMeta: &beCaExtPb.FipMeta{
									Name:        "HDFC",
									DisplayName: "HDFC",
									LogoUrl:     "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
									FipId:       "HDFC-FIP",
								},
							},
							Summary: &beCaPb.AccountProfileSummaryDetails_DepositSummary{
								DepositSummary: &beCaExtPb.DepositSummary{
									AccountId:            "acc-id-2",
									DepositAccountType:   beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS,
									DepositAccountStatus: beCaEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_ACTIVE,
									CurrentBalance: &money.Money{
										CurrencyCode: "INR",
										Units:        220,
									},
									BalanceDate: timestamppb.Now(),
									IfscCode:    "test-ifsc",
								},
							},
						},
					},
				}, nil)
				// deeplink mocks
				m.beConnectedAccClient.EXPECT().CheckReoobe(gomock.Any(), gomock.Any()).Return(&beCaPb.CheckReoobeResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
				m.beConnectedAccClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{Status: rpcPb.StatusOk(), AaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY}, nil)
				m.beActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actor-id-7",
						EntityId: "user-id-7",
					},
				}, nil)
				m.beUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpcPb.StatusOk(),
					User: &userPb.User{Id: "user-id-7", Profile: &userPb.Profile{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						PanName: &commontypes.Name{FirstName: "Hardik"},
						PAN:     "Hardik",
						DateOfBirth: &date.Date{
							Year:  2000,
							Month: 1,
							Day:   1,
						},
					}},
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: "actor-id-1",
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				DashboardInfo: &homePb.HomeDashboard{
					Title: &commontypes.Text{
						FontColor: HomeDashboardTitleFontColorV2,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: HomeDashboardTitleV2,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_S,
						},
					},
					Body: &homePb.HomeDashboard_Body{
						PrivacyModeImage: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  PrivacyModeImageUrl,
							Width:     int32(PrivacyModeImageWidth),
							Height:    int32(PrivacyModeImageHeight),
						},
						DashboardIcons: []*homePb.Icon{
							{
								IconImage: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  EyeCaHomeDashboardIconImageUrlV2,
								},
								IconImageOnSelection: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  EyeCaHomeDashboardIconImageOnSelectionUrlV2,
								},
								IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
								ActionType: homePb.Icon_ACTION_TYPE_TOGGLE_PRIVACY,
								BgColour:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DashboardIconsBgColourV2}},
							},
							{
								IconImage: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  LinkCaHomeDashboardIconImageUrlV2,
								},
								IconType:   homeTypesPb.IconType_CONNECT_ACCOUNT,
								ActionType: homePb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &homePb.Icon_Deeplink{
									Deeplink: dl,
								},
								BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DashboardIconsBgColourV2}},
							},
						},
						MoneyValue: []*commontypes.Text{
							{
								FontColor: MoneyCurrencyTextFontColorV2,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "₹",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_RUPEE_XL},
							},
							{
								FontColor: MoneyUnitsTextFontColorV2,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "640",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
							},
						},
						MoneyValueV2: []*ui.IconTextComponent{
							ui.NewITC().WithTexts(&commontypes.Text{
								FontColor: MoneyCurrencyTextFontColorV2,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "₹",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_RUPEE_XL},
							}),
							ui.NewITC().WithTexts(&commontypes.Text{
								FontColor: MoneyUnitsTextFontColorV2,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "640",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
							}),
						},
						DashboardState: homePb.HomeDashboard_Body_STATE_NON_ZERO,
					},
					Deeplink:            accountSummaryDl,
					Shadow:              ui.GetDashboardShadow(),
					FooterTicker:        footerTicker2,
					DashboardFooter:     &homePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker2},
					DashboardBackground: homePb.GetHomeDashboardSectionBackground(),
					BorderColor:         homePb.GetHomeDashboardBorderColor(),
				},
			},
			wantErr: false,
		},
		{
			name: "v2, with expired account",
			args: args{
				req: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					DashboardVersion: homePb.DashboardVersion_DASHBOARD_VERSION_V2,
				},
			},
			mockFunc: func(m *mockStruct) {
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW).WithActorId("actor-id-1")).Return(true, nil)
				m.releaseClient.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(typesV2.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId("actor-id-1")).Return(false, nil)
				m.onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpcPb.StatusOk(),
					IsFiLiteUser: false,
				}, nil).Times(2)
				m.beSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{Account: &savings.Account{Id: "id-1", AccountNo: "********"}}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{Status: rpcPb.StatusOk(), AvailableBalance: &money.Money{CurrencyCode: "INR", Units: 420, Nanos: 69}}, nil)
				m.beConnectedAccClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountsResponse{Status: rpcPb.StatusOk(), AccountDetailsList: []*beCaExtPb.AccountDetails{
					{
						AccountId:           "acc-id-1",
						FipId:               "HDFC-FIP",
						FipMeta:             &beCaExtPb.FipMeta{LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png", DisplayName: "HDFC", Name: "hdfc"},
						AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_STOPPED,
						MaskedAccountNumber: "**1234",
					},
					{
						AccountId:           "acc-id-3",
						FipId:               "HDFC-FIP",
						FipMeta:             &beCaExtPb.FipMeta{LogoUrl: "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png", DisplayName: "HDFC", Name: "hdfc"},
						AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
						MaskedAccountNumber: "**5678",
					},
				}}, nil)
				m.beConnectedAccClient.EXPECT().GetAccountDetailsBulk(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAccountDetailsBulkResponse{
					Status: rpcPb.StatusOk(),
					AccountDetailsMap: map[string]*beCaPb.AccountProfileSummaryDetails{
						"acc-id-1": {
							AccountDetails: &beCaExtPb.AccountDetails{
								MaskedAccountNumber: "XXXXXX4321",
								AccountId:           "acc-id-1",
								IfscCode:            "test-ifsc",
								AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_STOPPED,
								AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
								LastSyncedAt:        timestamppb.Now(),
								AaEntity:            beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
								FipId:               "HDFC-FIP",
								FipMeta: &beCaExtPb.FipMeta{
									FipId:       "HDFC-FIP",
									DisplayName: "HDFC",
									Name:        "HDFC",
									LogoUrl:     "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
								},
							},
							Summary: &beCaPb.AccountProfileSummaryDetails_DepositSummary{
								DepositSummary: &beCaExtPb.DepositSummary{
									AccountId:            "acc-id-1",
									DepositAccountType:   beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS,
									DepositAccountStatus: beCaEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_ACTIVE,
									CurrentBalance: &money.Money{
										CurrencyCode: "INR",
										Units:        220,
									},
									BalanceDate: timestamppb.Now(),
									IfscCode:    "test-ifsc",
								},
							},
						},
						"acc-id-3": {
							AccountDetails: &beCaExtPb.AccountDetails{
								AccountId:           "acc-id-3",
								MaskedAccountNumber: "XXXXXX5678",
								IfscCode:            "test-ifsc",
								AccountStatus:       beCaEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
								AccInstrumentType:   beCaEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
								LastSyncedAt:        timestamppb.Now(),
								AaEntity:            beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
								FipId:               "HDFC-FIP",
								FipMeta: &beCaExtPb.FipMeta{
									FipId:       "HDFC-FIP",
									DisplayName: "HDFC",
									LogoUrl:     "https://epifi-icons.pointz.in/connectedaccounts/banks/HDFC.png",
								},
							},
							Summary: &beCaPb.AccountProfileSummaryDetails_DepositSummary{
								DepositSummary: &beCaExtPb.DepositSummary{
									AccountId:            "acc-id-3",
									DepositAccountType:   beCaEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS,
									DepositAccountStatus: beCaEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_ACTIVE,
									CurrentBalance: &money.Money{
										CurrencyCode: "INR",
										Units:        220,
									},
									BalanceDate: timestamppb.Now(),
									IfscCode:    "test-ifsc",
								},
							},
						},
					},
				}, nil)
				// deeplink mocks
				m.beConnectedAccClient.EXPECT().CheckReoobe(gomock.Any(), gomock.Any()).Return(&beCaPb.CheckReoobeResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
				m.beConnectedAccClient.EXPECT().GetAaEntityForConnect(gomock.Any(), gomock.Any()).Return(&beCaPb.GetAaEntityForConnectResponse{Status: rpcPb.StatusOk(), AaEntity: beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY}, nil)
				m.beActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actor-id-7",
						EntityId: "user-id-7",
					},
				}, nil)
				m.beUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
					Status: rpcPb.StatusOk(),
					User: &userPb.User{Id: "user-id-7", Profile: &userPb.Profile{
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
						PanName: &commontypes.Name{FirstName: "Hardik"},
						PAN:     "Hardik",
						DateOfBirth: &date.Date{
							Year:  2000,
							Month: 1,
							Day:   1,
						},
					}},
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: "actor-id-1",
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			want: &feConnectedAccPb.GetConnectedAccountsSummaryForHomeResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
				DashboardInfo: &homePb.HomeDashboard{
					Title: &commontypes.Text{
						FontColor: HomeDashboardTitleFontColorV2,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: HomeDashboardTitleV2,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_S,
						},
					},
					Body: &homePb.HomeDashboard_Body{
						PrivacyModeImage: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  PrivacyModeImageUrl,
							Width:     int32(PrivacyModeImageWidth),
							Height:    int32(PrivacyModeImageHeight),
						},
						DashboardIcons: []*homePb.Icon{
							{
								IconImage: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  EyeCaHomeDashboardIconImageUrlV2,
								},
								IconImageOnSelection: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  EyeCaHomeDashboardIconImageOnSelectionUrlV2,
								},
								IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
								ActionType: homePb.Icon_ACTION_TYPE_TOGGLE_PRIVACY,
								BgColour:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DashboardIconsBgColourV2}},
							},
							{
								IconImage: &commontypes.Image{
									ImageType: commontypes.ImageType_PNG,
									ImageUrl:  LinkCaHomeDashboardIconImageUrlV2,
								},
								IconType:   homeTypesPb.IconType_CONNECT_ACCOUNT,
								ActionType: homePb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &homePb.Icon_Deeplink{
									Deeplink: dl,
								},
								BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DashboardIconsBgColourV2}},
							},
						},
						MoneyValue: []*commontypes.Text{
							{
								FontColor: MoneyCurrencyTextFontColorV2,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "₹",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_RUPEE_XL},
							},
							{
								FontColor: MoneyUnitsTextFontColorV2,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "640",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
							},
						},
						MoneyValueV2: []*ui.IconTextComponent{
							ui.NewITC().WithTexts(&commontypes.Text{
								FontColor: MoneyCurrencyTextFontColorV2,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "₹",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_RUPEE_XL},
							}),
							ui.NewITC().WithTexts(&commontypes.Text{
								FontColor: MoneyUnitsTextFontColorV2,
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "640",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
							}),
						},
						DashboardState: homePb.HomeDashboard_Body_STATE_NON_ZERO,
					},
					Deeplink:            accountSummaryDl,
					Shadow:              ui.GetDashboardShadow(),
					FooterTicker:        footerTicker3,
					DashboardFooter:     &homePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker3},
					DashboardBackground: homePb.GetHomeDashboardSectionBackground(),
					BorderColor:         homePb.GetHomeDashboardBorderColor(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockCaClient := mocks.NewMockConnectedAccountClient(ctrl)
			mockUserClient := mocks2.NewMockUsersClient(ctrl)
			mockActorClient := mocks3.NewMockActorClient(ctrl)
			mockSavingsClient := mocks5.NewMockSavingsClient(ctrl)
			mockBeConsentClient := mocks6.NewMockConsentClient(ctrl)
			mockOnbClient := mocks4.NewMockOnboardingClient(ctrl)
			mockAccountBalanceClient := accountBalanceMock.NewMockBalanceClient(ctrl)
			mockReleaseEvaluator := mock_release.NewMockIEvaluator(ctrl)
			mockUserAttributesFetcher := pkgUserMocks.NewMockUserAttributesFetcher(ctrl)
			if tt.mockFunc != nil {
				tt.mockFunc(&mockStruct{
					beConnectedAccClient:  mockCaClient,
					conf:                  gconf,
					beUserClient:          mockUserClient,
					beActorClient:         mockActorClient,
					beSavingsClient:       mockSavingsClient,
					beConsentClient:       mockBeConsentClient,
					onbClient:             mockOnbClient,
					accountBalanceClient:  mockAccountBalanceClient,
					releaseClient:         mockReleaseEvaluator,
					userAttributesFetcher: mockUserAttributesFetcher,
				})
			}

			s := &Service{
				beConnectedAccClient:  mockCaClient,
				beUserClient:          mockUserClient,
				beActorClient:         mockActorClient,
				beSavingsClient:       mockSavingsClient,
				beConsentClient:       mockBeConsentClient,
				conf:                  gconf,
				onbClient:             mockOnbClient,
				accountBalanceClient:  mockAccountBalanceClient,
				releaseEvaluator:      mockReleaseEvaluator,
				userAttributesFetcher: mockUserAttributesFetcher,
			}
			got, err := s.GetConnectedAccountsSummaryForHome(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConnectedAccountsSummaryForHome() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&deeplinkpb.ConnectedAccountsOptions{}, "version"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetConnectedAccountsSummaryForHome() mismatch (-want +got): \n%s", diff)
				return
			}
		})
	}
}

func TestService_GetConsentHandleStatus(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCaClient := mocks.NewMockConnectedAccountClient(ctr)
	mockCaFlowFactory := femocks.NewMockICaFlowDLFactory(ctr)

	screenOptions, _ := deeplinkv3.GetScreenOptionV2(&caDlOptions.FiToFiFlowTerminalScreenOptions{
		Title: commontypes.GetTextFromStringFontColourFontStyle(processor.FiToFiConsentFailureOMBottomSheetTitle, processor.FiToFiTerminalFailureBottomSheetTitleFontColor, processor.FiToFiTerminalFailureBottomSheetTitleFontStyle),
		Desc:  commontypes.GetTextFromStringFontColourFontStyle(processor.FiToFiConsentFailureBottomSheetDesc, processor.FiToFiTerminalFailureBottomSheetDescFontColor, processor.FiToFiTerminalFailureBottomSheetDescFontStyle),
		ConsentStatusSuccessIcon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: processor.FiToFiTerminalFailureBottomSheetIconUrl,
					},
					ImageType: commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{
						Width:  processor.FiToFiTerminalFailureBottomSheetIconWidth,
						Height: processor.FiToFiTerminalFailureBottomSheetIconHeight,
					},
				},
			},
		},
		ProceedCtas: []*deeplinkPb.Cta{
			{
				Type:         deeplinkPb.Cta_DONE,
				Text:         processor.FiToFiConsentFailureBottomSheetCtaText,
				DisplayTheme: deeplinkPb.Cta_SECONDARY,
			},
		},
		FiFlowTerminalScreenPurpose: feCaFeaturesFiToFiPb.FiToFiFlowTerminalScreenPurpose_FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_CONSENT_STATUS_FAILURE,
		BgColor:                     &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: processor.FiToFiTerminalFailureBottomSheetBgColor}},
	})

	type fields struct {
		beConnectedAccClient beCaPb.ConnectedAccountClient
		connectedAccountConf *config.ConnectedAccount
		conf                 *genconf.Config
		caFlowDLFactory      factory.ICaFlowDLFactory
	}
	type args struct {
		ctx   context.Context
		req   *feConnectedAccPb.GetConsentHandleStatusRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *feConnectedAccPb.GetConsentHandleStatusResponse
		wantErr bool
	}{
		{
			name: "#1 consent request details not found",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				conf:                 gconf,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetConsentHandleStatusRequest{
					ConsentHandle:    "consent-handle-1",
					CurrentPollCount: 0,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetConsentRequestDetails(gomock.Any(), gomock.Any()).Return(&beCaPb.
						GetConsentRequestDetailsResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("consent request not found")}, nil),
				},
			},
			want: &feConnectedAccPb.GetConsentHandleStatusResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusRecordNotFoundWithDebugMsg(fmt.Sprintf(
					"record not found error while invoking GetConsentRequestDetails: %v", rpcPb.StatusAsError(rpcPb.StatusRecordNotFoundWithDebugMsg("consent request not found")).Error()))},
			},
			wantErr: false,
		},
		{
			name: "#2 internal error while finding the consent request ",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				conf:                 gconf,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetConsentHandleStatusRequest{
					ConsentHandle:    "consent-handle-2",
					CurrentPollCount: 0,
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetConsentRequestDetails(gomock.Any(), gomock.Any()).Return(&beCaPb.
						GetConsentRequestDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg(
						"internal error while finding consent request")}, nil),
				},
			},
			want: &feConnectedAccPb.GetConsentHandleStatusResponse{
				RespHeader: &header.ResponseHeader{Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf(
					"internal error while invoking GetConsentRequestDetails: %v",
					rpcPb.StatusAsError(rpcPb.StatusInternalWithDebugMsg("internal error while finding consent request")).Error()))},
			},
			wantErr: false,
		},
		{
			name: "#3 consent handle status pending",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				conf:                 gconf,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetConsentHandleStatusRequest{
					ConsentHandle: "consent-handle-3",
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetConsentRequestDetails(gomock.Any(), gomock.Any()).Return(&beCaPb.
						GetConsentRequestDetailsResponse{Status: rpcPb.StatusOk(),
						ConsentRequest: &beCaPb.ConsentRequest{
							ConsentHandle:       "consent-handle-3",
							ConsentHandleStatus: beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING,
						},
					}, nil),
				},
			},
			want: &feConnectedAccPb.GetConsentHandleStatusResponse{
				RespHeader:          &header.ResponseHeader{Status: rpcPb.StatusOk()},
				ConsentHandleStatus: feConnectedAccountCommonPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING,
				NextPollDuration:    durationpb.New(gconf.ConnectedAccount().ConsentHandleStatusNextPollDuration()),
				CurrentPollCount:    1,
			},
			wantErr: false,
		},
		{
			name: "#4 consent handle status rejected",
			fields: fields{
				beConnectedAccClient: mockCaClient,
				connectedAccountConf: conf,
				conf:                 gconf,
				caFlowDLFactory:      mockCaFlowFactory,
			},
			args: args{
				ctx: context.Background(),
				req: &feConnectedAccPb.GetConsentHandleStatusRequest{
					ConsentHandle: "consent-handle-4",
				},
				mocks: []interface{}{
					mockCaClient.EXPECT().GetConsentRequestDetails(gomock.Any(), gomock.Any()).Return(&beCaPb.
						GetConsentRequestDetailsResponse{Status: rpcPb.StatusOk(),
						ConsentRequest: &beCaPb.ConsentRequest{
							ConsentHandle:       "consent-handle-4",
							ConsentHandleStatus: beCaEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_REJECTED,
							CaFlowName:          beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECT_FI_TO_FI,
							AaEntity:            beCaEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
						},
					}, nil),
					mockCaFlowFactory.EXPECT().GetCAFlowDeeplinkProcessor(gomock.Any()).Return(beCaProcessor.NewCaFlowConnectFiToFiProcessor(), nil),
				},
			},
			want: &feConnectedAccPb.GetConsentHandleStatusResponse{
				RespHeader:          &header.ResponseHeader{Status: rpcPb.StatusOk()},
				ConsentHandleStatus: feConnectedAccountCommonPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_REJECTED,
				Deeplink: &deeplinkPb.Deeplink{
					Screen:          deeplinkPb.Screen_CA_FI_TO_FI_FLOW_TERMINAL_SCREEN,
					ScreenOptionsV2: screenOptions,
				},
				CurrentPollCount: int32(-1),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				beConnectedAccClient: tt.fields.beConnectedAccClient,
				connectedAccountConf: tt.fields.connectedAccountConf,
				conf:                 tt.fields.conf,
				caFlowDLFactory:      tt.fields.caFlowDLFactory,
			}
			got, err := s.GetConsentHandleStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConsentHandleStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetConsentHandleStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}
