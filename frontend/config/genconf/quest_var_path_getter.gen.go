// Code generated by tools/conf_gen/dynamic_conf_gen.go
package genconf

import (
	"fmt"
	"strings"

	"github.com/epifi/be-common/api/pkg/web"
	pkgweb "github.com/epifi/be-common/pkg/web"
)

// GetQuestVariableValue returns th value for a variable path relative to the Config object.
func (obj *Config) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "RewardsFrontendMeta":
		return obj.RewardsFrontendMeta().GetQuestVariableValue(questVariablePath[1:])
	case "Fittt":
		return obj.Fittt().GetQuestVariableValue(questVariablePath[1:])
	case "Card":
		return obj.Card().GetQuestVariableValue(questVariablePath[1:])
	case "ReferralsV1":
		return obj.ReferralsV1().GetQuestVariableValue(questVariablePath[1:])
	case "SalaryProgram":
		return obj.SalaryProgram().GetQuestVariableValue(questVariablePath[1:])
	case "Deposit":
		return obj.Deposit().GetQuestVariableValue(questVariablePath[1:])
	case "AnalyserParams":
		return obj.AnalyserParams().GetQuestVariableValue(questVariablePath[1:])
	case "P2PInvestment":
		return obj.P2PInvestment().GetQuestVariableValue(questVariablePath[1:])
	case "Lending":
		return obj.Lending().GetQuestVariableValue(questVariablePath[1:])
	case "HomeRevampParams":
		return obj.HomeRevampParams().GetQuestVariableValue(questVariablePath[1:])
	case "LiteHomeRevampParams":
		return obj.LiteHomeRevampParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeExploreConfig":
		return obj.HomeExploreConfig().GetQuestVariableValue(questVariablePath[1:])
	case "Tiering":
		return obj.Tiering().GetQuestVariableValue(questVariablePath[1:])
	case "USStocks":
		return obj.USStocks().GetQuestVariableValue(questVariablePath[1:])
	case "CreditCard":
		return obj.CreditCard().GetQuestVariableValue(questVariablePath[1:])
	case "AskFiHomeSearchBarConfig":
		return obj.AskFiHomeSearchBarConfig().GetQuestVariableValue(questVariablePath[1:])
	case "AMBEntrypointBannerParams":
		return obj.AMBEntrypointBannerParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeFeatureQuestFlags":
		return obj.HomeFeatureQuestFlags().GetQuestVariableValue(questVariablePath[1:])
	case "TieringNotchConfig":
		return obj.TieringNotchConfig().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for Config", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the RewardsFrontendMeta object.
func (obj *RewardsFrontendMeta) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsDebitCardOfferWidgetHomeEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsDebitCardOfferWidgetHomeEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isDebitCardOfferWidgetHomeEnabled())
	case "NonCCUsersCatalogOffersExcludedCategoryTags":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NonCCUsersCatalogOffersExcludedCategoryTags\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.nonCCUsersCatalogOffersExcludedCategoryTags())
	case "OffersCatalogPageConfig":
		return obj.OffersCatalogPageConfig().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for RewardsFrontendMeta", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the OffersCatalogPageConfig object.
func (obj *OffersCatalogPageConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsQuestEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsQuestEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isQuestEnabled())
	case "SectionsConfig":
		return obj.SectionsConfig().GetQuestVariableValue(questVariablePath[1:])
	case "OffersOrderingConfig":
		return obj.OffersOrderingConfig().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for OffersCatalogPageConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the SectionsConfig object.
func (obj *SectionsConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "OffersSection":
		return obj.OffersSection().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for SectionsConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AlignmentValues object.
func (obj *AlignmentValues) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Left":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Left\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.left())
	case "Right":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Right\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.right())
	case "Top":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Top\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.top())
	case "Bottom":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Bottom\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.bottom())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AlignmentValues", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the OffersSection object.
func (obj *OffersSection) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsHorizontalScroll":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsHorizontalScroll\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isHorizontalScroll())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for OffersSection", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the OffersOrderingConfig object.
func (obj *OffersOrderingConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "OfferPotentialAffordabilityMultiplier":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OfferPotentialAffordabilityMultiplier\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.offerPotentialAffordabilityMultiplier())
	case "ExchangerOfferPotentialAffordabilityMultiplier":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExchangerOfferPotentialAffordabilityMultiplier\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.exchangerOfferPotentialAffordabilityMultiplier())
	case "IsDynamicSortingEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsDynamicSortingEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isDynamicSortingEnabled())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for OffersOrderingConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the Fittt object.
func (obj *Fittt) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "SDPreclosureMessageBox":
		return obj.SDPreclosureMessageBox().GetQuestVariableValue(questVariablePath[1:])
	case "MyRulesPage":
		return obj.MyRulesPage().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for Fittt", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the FitMessageBox object.
func (obj *FitMessageBox) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Title":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.title())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for FitMessageBox", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the MyRulesPage object.
func (obj *MyRulesPage) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "NewRulesCTAText":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NewRulesCTAText\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.newRulesCTAText())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for MyRulesPage", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the Card object.
func (obj *Card) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "DashboardV2Config":
		return obj.DashboardV2Config().GetQuestVariableValue(questVariablePath[1:])
	case "OrderPhysicalDCQuest":
		return obj.OrderPhysicalDCQuest().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for Card", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the DcDashboardV2Config object.
func (obj *DcDashboardV2Config) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsDashboardV2EnabledByQuest":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsDashboardV2EnabledByQuest\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isDashboardV2EnabledByQuest())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for DcDashboardV2Config", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the OrderPhysicalDCQuest object.
func (obj *OrderPhysicalDCQuest) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isEnabled())
	case "HomeWidgetTitleString":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HomeWidgetTitleString\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.homeWidgetTitleString())
	case "HomeWidgetImageUrl":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HomeWidgetImageUrl\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.homeWidgetImageUrl())
	case "HomeWidgetBgColour":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HomeWidgetBgColour\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.homeWidgetBgColour())
	case "HomeWidgetShadowColour":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HomeWidgetShadowColour\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.homeWidgetShadowColour())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for OrderPhysicalDCQuest", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the ReferralsV1 object.
func (obj *ReferralsV1) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "HomeWidgetV2SegmentedComponentId":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HomeWidgetV2SegmentedComponentId\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.homeWidgetV2SegmentedComponentId())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for ReferralsV1", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the CtaButton object.
func (obj *CtaButton) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "WrapContent":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WrapContent\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.wrapContent())
	case "FontColor":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FontColor\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.fontColor())
	case "BgColor":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BgColor\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.bgColor())
	case "Text":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.text())
	case "Padding":
		return obj.Padding().GetQuestVariableValue(questVariablePath[1:])
	case "Margin":
		return obj.Margin().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for CtaButton", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the SalaryProgram object.
func (obj *SalaryProgram) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "AaSalaryConfig":
		return obj.AaSalaryConfig().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for SalaryProgram", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AaSalaryConfig object.
func (obj *AaSalaryConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "EnableAaSalaryAmountSetupVersionV2":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableAaSalaryAmountSetupVersionV2\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enableAaSalaryAmountSetupVersionV2())
	case "AaSalaryAmountSetupBackConfirmationPopup":
		return obj.AaSalaryAmountSetupBackConfirmationPopup().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AaSalaryConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AaSalaryAmountSetupBackConfirmationPopup object.
func (obj *AaSalaryAmountSetupBackConfirmationPopup) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Enable":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enable())
	case "Icon":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Icon\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.icon())
	case "Title":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.title())
	case "SubTitle":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SubTitle\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.subTitle())
	case "Cta1Text":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Cta1Text\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.cta1Text())
	case "Cta2Text":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Cta2Text\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.cta2Text())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AaSalaryAmountSetupBackConfirmationPopup", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the Deposit object.
func (obj *Deposit) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "QuestFDRetentionFeatureIsEnable":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QuestFDRetentionFeatureIsEnable\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.questFDRetentionFeatureIsEnable())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for Deposit", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AnalyserParams object.
func (obj *AnalyserParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Experiment":
		return obj.Experiment().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AnalyserParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AnalyserExperiment object.
func (obj *AnalyserExperiment) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Widgets":
		return obj.Widgets().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AnalyserExperiment", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AnalyserWidgets object.
func (obj *AnalyserWidgets) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "CreditScoreAnalyser":
		return obj.CreditScoreAnalyser().GetQuestVariableValue(questVariablePath[1:])
	case "TopCategoriesAnalyser":
		return obj.TopCategoriesAnalyser().GetQuestVariableValue(questVariablePath[1:])
	case "TopMerchantsAnalyser":
		return obj.TopMerchantsAnalyser().GetQuestVariableValue(questVariablePath[1:])
	case "TimeSpendAnalyser":
		return obj.TimeSpendAnalyser().GetQuestVariableValue(questVariablePath[1:])
	case "MutualFundLandingPage":
		return obj.MutualFundLandingPage().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AnalyserWidgets", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AnalyserWidget object.
func (obj *AnalyserWidget) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Header":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Header\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.header())
	case "Footer":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Footer\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.footer())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AnalyserWidget", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the P2PInvestment object.
func (obj *P2PInvestment) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "QuestDashboardBanners":
		return obj.QuestDashboardBanners().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for P2PInvestment", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the P2PInvestmentQuestDashboardBanner object.
func (obj *P2PInvestmentQuestDashboardBanner) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "BannerJson":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BannerJson\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.bannerJson())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for P2PInvestmentQuestDashboardBanner", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the Lending object.
func (obj *Lending) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "PreApprovedLoan":
		return obj.PreApprovedLoan().GetQuestVariableValue(questVariablePath[1:])
	case "SecuredLoanParams":
		return obj.SecuredLoanParams().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for Lending", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the PreApprovedLoan object.
func (obj *PreApprovedLoan) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsAlternateAccountFlowEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAlternateAccountFlowEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isAlternateAccountFlowEnabled())
	case "IsAlternateAccountFlowEnabledForLL":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAlternateAccountFlowEnabledForLL\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isAlternateAccountFlowEnabledForLL())
	case "RestrictShowingMultipleOffers":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RestrictShowingMultipleOffers\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.restrictShowingMultipleOffers())
	case "IsUnCollapsedOfferDetailsEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsUnCollapsedOfferDetailsEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isUnCollapsedOfferDetailsEnabled())
	case "LoanOfferDetailsScreenV2":
		return obj.LoanOfferDetailsScreenV2().GetQuestVariableValue(questVariablePath[1:])
	case "LoanApplicationDetailsScreenV2":
		return obj.LoanApplicationDetailsScreenV2().GetQuestVariableValue(questVariablePath[1:])
	case "LoanDetailsSelectionV2Flow":
		return obj.LoanDetailsSelectionV2Flow().GetQuestVariableValue(questVariablePath[1:])
	case "OfferDetailsV3Config":
		return obj.OfferDetailsV3Config().GetQuestVariableValue(questVariablePath[1:])
	case "A2LLandingScreenConfig":
		return obj.A2LLandingScreenConfig().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for PreApprovedLoan", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the LoanOfferDetailsScreenV2 object.
func (obj *LoanOfferDetailsScreenV2) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "LoanConstraintsComponent":
		return obj.LoanConstraintsComponent().GetQuestVariableValue(questVariablePath[1:])
	case "LoanInfoComponent":
		return obj.LoanInfoComponent().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for LoanOfferDetailsScreenV2", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the LoanConstraintsComponent object.
func (obj *LoanConstraintsComponent) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Version":
		return obj.Version().GetQuestVariableValue(questVariablePath[1:])
	case "CtaButton":
		return obj.CtaButton().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for LoanConstraintsComponent", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the Version object.
func (obj *Version) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Minor":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Minor\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.minor())
	case "Major":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Major\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.major())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for Version", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the LoanInfoComponent object.
func (obj *LoanInfoComponent) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Version":
		return obj.Version().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for LoanInfoComponent", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the LoanApplicationDetailsScreenV2 object.
func (obj *LoanApplicationDetailsScreenV2) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "ApplicationLoanInfoComponentVersion":
		return obj.ApplicationLoanInfoComponentVersion().GetQuestVariableValue(questVariablePath[1:])
	case "ApplicationEmiInfoComponentVersion":
		return obj.ApplicationEmiInfoComponentVersion().GetQuestVariableValue(questVariablePath[1:])
	case "ApplicationTncInfoComponentVersion":
		return obj.ApplicationTncInfoComponentVersion().GetQuestVariableValue(questVariablePath[1:])
	case "ApplicationBenefitsInfoComponentVersion":
		return obj.ApplicationBenefitsInfoComponentVersion().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for LoanApplicationDetailsScreenV2", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the LoanDetailsSelectionV2Flow object.
func (obj *LoanDetailsSelectionV2Flow) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isEnabled())
	case "IsRewardsEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsRewardsEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isRewardsEnabled())
	case "SkipAmountSelectionScreen":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipAmountSelectionScreen\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.skipAmountSelectionScreen())
	case "ShowMultipleOfferSelection":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowMultipleOfferSelection\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showMultipleOfferSelection())
	case "ShowCollapsedBreakdownView":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowCollapsedBreakdownView\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showCollapsedBreakdownView())
	case "ShowDiscountedPf":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowDiscountedPf\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showDiscountedPf())
	case "ShowCongratulatoryText":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowCongratulatoryText\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showCongratulatoryText())
	case "ShowAprPopup":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowAprPopup\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showAprPopup())
	case "ShowUserTestimonials":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowUserTestimonials\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showUserTestimonials())
	case "ShowExtraCtaInfo":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowExtraCtaInfo\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showExtraCtaInfo())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for LoanDetailsSelectionV2Flow", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the OfferDetailsV3Config object.
func (obj *OfferDetailsV3Config) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isEnabled())
	case "SkipAmountSelectionScreen":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipAmountSelectionScreen\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.skipAmountSelectionScreen())
	case "ShowInterestRate":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowInterestRate\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showInterestRate())
	case "ShowZeroPreClosureTag":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowZeroPreClosureTag\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showZeroPreClosureTag())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for OfferDetailsV3Config", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the OfferDetailsV4Config object.
func (obj *OfferDetailsV4Config) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isEnabled())
	case "SkipAmountSelectionScreen":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipAmountSelectionScreen\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.skipAmountSelectionScreen())
	case "ShowInterestRate":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowInterestRate\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showInterestRate())
	case "ShowZeroPreClosureTag":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowZeroPreClosureTag\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showZeroPreClosureTag())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for OfferDetailsV4Config", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the A2LLandingScreenConfig object.
func (obj *A2LLandingScreenConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Header":
		return obj.Header().GetQuestVariableValue(questVariablePath[1:])
	case "BenefitsGrid":
		return obj.BenefitsGrid().GetQuestVariableValue(questVariablePath[1:])
	case "Banner":
		return obj.Banner().GetQuestVariableValue(questVariablePath[1:])
	case "Footer":
		return obj.Footer().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for A2LLandingScreenConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the A2LLandingScreenHeader object.
func (obj *A2LLandingScreenHeader) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Title":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.title())
	case "Subtitle":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Subtitle\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.subtitle())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for A2LLandingScreenHeader", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the A2LLandingScreenBenefitsGrid object.
func (obj *A2LLandingScreenBenefitsGrid) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "TopLeftBenefit":
		return obj.TopLeftBenefit().GetQuestVariableValue(questVariablePath[1:])
	case "TopRightBenefit":
		return obj.TopRightBenefit().GetQuestVariableValue(questVariablePath[1:])
	case "BottomLeftBenefit":
		return obj.BottomLeftBenefit().GetQuestVariableValue(questVariablePath[1:])
	case "BottomRightBenefit":
		return obj.BottomRightBenefit().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for A2LLandingScreenBenefitsGrid", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the A2LLandingScreenBenefit object.
func (obj *A2LLandingScreenBenefit) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IconUrl":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconUrl\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.iconUrl())
	case "Text":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.text())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for A2LLandingScreenBenefit", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the A2LLandingScreenBanner object.
func (obj *A2LLandingScreenBanner) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Text":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.text())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for A2LLandingScreenBanner", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the A2LLandingScreenFooter object.
func (obj *A2LLandingScreenFooter) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "PrimaryCTAText":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PrimaryCTAText\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.primaryCTAText())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for A2LLandingScreenFooter", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the SecuredLoanParams object.
func (obj *SecuredLoanParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "SecuredLoanExperiments":
		return obj.SecuredLoanExperiments().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for SecuredLoanParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the SecuredLoanExperiments object.
func (obj *SecuredLoanExperiments) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "SkipInfoPage":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipInfoPage\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.skipInfoPage())
	case "SkipLandingPage":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipLandingPage\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.skipLandingPage())
	case "ChangeButtonTextLandingPage":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ChangeButtonTextLandingPage\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.changeButtonTextLandingPage())
	case "IsDeclarationCheckboxJourneyPage":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsDeclarationCheckboxJourneyPage\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isDeclarationCheckboxJourneyPage())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for SecuredLoanExperiments", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the HomeRevampParams object.
func (obj *HomeRevampParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "EnableHomeProfileInfoV2":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableHomeProfileInfoV2\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enableHomeProfileInfoV2())
	case "EnableSADashboardAddMoneyV2":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSADashboardAddMoneyV2\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enableSADashboardAddMoneyV2())
	case "LowBalAlertParams":
		return obj.LowBalAlertParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutParams":
		return obj.HomeLayoutParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeShortcutParams":
		return obj.HomeShortcutParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2Params":
		return obj.HomeLayoutV2Params().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2LiteParams":
		return obj.HomeLayoutV2LiteParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2LiteCCParams":
		return obj.HomeLayoutV2LiteCCParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2LitePLParams":
		return obj.HomeLayoutV2LitePLParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2FiNRParams":
		return obj.HomeLayoutV2FiNRParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2D0To7Params":
		return obj.HomeLayoutV2D0To7Params().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2D8To14Params":
		return obj.HomeLayoutV2D8To14Params().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2D15To28Params":
		return obj.HomeLayoutV2D15To28Params().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2WealthAnalyserParams":
		return obj.HomeLayoutV2WealthAnalyserParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2WealthAnalyserNoAssetParams":
		return obj.HomeLayoutV2WealthAnalyserNoAssetParams().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2UpiTPAPConnected":
		return obj.HomeLayoutV2UpiTPAPConnected().GetQuestVariableValue(questVariablePath[1:])
	case "HomeLayoutV2SADropOffParams":
		return obj.HomeLayoutV2SADropOffParams().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for HomeRevampParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the LowBalAlertParams object.
func (obj *LowBalAlertParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "BalTitle":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BalTitle\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.balTitle())
	case "BalColor":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BalColor\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.balColor())
	case "BalImage":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BalImage\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.balImage())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for LowBalAlertParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the HomeLayoutParams object.
func (obj *HomeLayoutParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "LayoutJson":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LayoutJson\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.layoutJson())
	case "ExploreSectionsJson":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExploreSectionsJson\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.exploreSectionsJson())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for HomeLayoutParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the HomeShortcutParams object.
func (obj *HomeShortcutParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "ShouldShowExploreFiShortcut":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShouldShowExploreFiShortcut\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.shouldShowExploreFiShortcut())
	case "DefaultShortcutsFromExperiment":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultShortcutsFromExperiment\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.defaultShortcutsFromExperiment())
	case "Title":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.title())
	case "UserTypeToShortcutParamsMap":
		if len(questVariablePath) < 3 {
			return nil, fmt.Errorf("incomplete variable path")
		}
		mapVal, exist := obj.UserTypeToShortcutParamsMap().Load(questVariablePath[1])
		if !exist {
			return nil, fmt.Errorf("invalid quest variable path %q for UserTypeToShortcutParamsMap", strings.Join(questVariablePath, "."))
		}
		return mapVal.GetQuestVariableValue(questVariablePath[2:])

	default:
		return nil, fmt.Errorf("invalid quest variable path %q for HomeShortcutParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the UserTypeSpecificShortcutParams object.
func (obj *UserTypeSpecificShortcutParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "ShowExploreFiShortcut":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShowExploreFiShortcut\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.showExploreFiShortcut())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for UserTypeSpecificShortcutParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the HomeLayoutV2Params object.
func (obj *HomeLayoutV2Params) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "DashboardVersion":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DashboardVersion\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.dashboardVersion())
	case "ZeroStateDashboardCardVariant":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ZeroStateDashboardCardVariant\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.zeroStateDashboardCardVariant())
	case "LayoutV2Json":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LayoutV2Json\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.layoutV2Json())
	case "DefaultSelectedIconIdForBottomNavBarWidget":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultSelectedIconIdForBottomNavBarWidget\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.defaultSelectedIconIdForBottomNavBarWidget())
	case "BgColorForBottomNavBarWidget":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BgColorForBottomNavBarWidget\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.bgColorForBottomNavBarWidget())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for HomeLayoutV2Params", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the HomeExploreConfig object.
func (obj *HomeExploreConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "EnableAskFiSection":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableAskFiSection\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enableAskFiSection())
	case "EnableFeedbackSection":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableFeedbackSection\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enableFeedbackSection())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for HomeExploreConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the Tiering object.
func (obj *Tiering) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "EnableHeroV3Component":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableHeroV3Component\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enableHeroV3Component())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for Tiering", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AMBScreenConfig object.
func (obj *AMBScreenConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "MaxShortfallAmount":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxShortfallAmount\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.maxShortfallAmount())
	case "TierShortfallAmountMap":
		if len(questVariablePath) < 3 {
			return nil, fmt.Errorf("incomplete variable path")
		}
		mapVal, exist := obj.TierShortfallAmountMap().Load(questVariablePath[1])
		if !exist {
			return nil, fmt.Errorf("invalid quest variable path %q for TierShortfallAmountMap", strings.Join(questVariablePath, "."))
		}
		return mapVal.GetQuestVariableValue(questVariablePath[2:])

	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AMBScreenConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AMBTextContent object.
func (obj *AMBTextContent) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "RewardsAtStakeText":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RewardsAtStakeText\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.rewardsAtStakeText())
	case "AMBOutOfReachText":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AMBOutOfReachText\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.aMBOutOfReachText())
	case "LearningItems":
		if len(questVariablePath) < 3 {
			return nil, fmt.Errorf("incomplete variable path")
		}
		mapVal, exist := obj.LearningItems().Load(questVariablePath[1])
		if !exist {
			return nil, fmt.Errorf("invalid quest variable path %q for LearningItems", strings.Join(questVariablePath, "."))
		}
		return mapVal.GetQuestVariableValue(questVariablePath[2:])

	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AMBTextContent", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the LearningItem object.
func (obj *LearningItem) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Title":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.title())
	case "StorifiLink":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StorifiLink\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.storifiLink())
	case "Image":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Image\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.image())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for LearningItem", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the ShortFallAmount object.
func (obj *ShortFallAmount) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Amount":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Amount\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.amount())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for ShortFallAmount", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AMBEntrypointBannerParams object.
func (obj *AMBEntrypointBannerParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsAMBEnabledByQuest":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsAMBEnabledByQuest\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isAMBEnabledByQuest())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AMBEntrypointBannerParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the USStocks object.
func (obj *USStocks) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsCollectionPriceDisabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsCollectionPriceDisabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isCollectionPriceDisabled())
	case "IsDoublePinAddFundFlowEnabled":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsDoublePinAddFundFlowEnabled\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isDoublePinAddFundFlowEnabled())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for USStocks", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the CreditCard object.
func (obj *CreditCard) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "SimplifiOnboardingQuestConfig":
		return obj.SimplifiOnboardingQuestConfig().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for CreditCard", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the SimplifiOnboardingQuestConfig object.
func (obj *SimplifiOnboardingQuestConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "EnableEditDeposit":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEditDeposit\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enableEditDeposit())
	case "DisableConsentBottomSheet":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableConsentBottomSheet\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.disableConsentBottomSheet())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for SimplifiOnboardingQuestConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the AskFiHomeSearchBarConfig object.
func (obj *AskFiHomeSearchBarConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "HomeSearchBarWidgetTypeVariant":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HomeSearchBarWidgetTypeVariant\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.homeSearchBarWidgetTypeVariant())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for AskFiHomeSearchBarConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the PaymentOptionsConfig object.
func (obj *PaymentOptionsConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "TieringPitchInPaymentOptionsParams":
		return obj.TieringPitchInPaymentOptionsParams().GetQuestVariableValue(questVariablePath[1:])
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for PaymentOptionsConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the TieringPitchInPaymentOptionsParams object.
func (obj *TieringPitchInPaymentOptionsParams) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "TierPlusTopSectionUrl":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TierPlusTopSectionUrl\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.tierPlusTopSectionUrl())
	case "TierInfiniteTopSectionUrl":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TierInfiniteTopSectionUrl\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.tierInfiniteTopSectionUrl())
	case "TierPrimeTopSectionUrl":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TierPrimeTopSectionUrl\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.tierPrimeTopSectionUrl())
	case "FederalIconLogo":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FederalIconLogo\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.federalIconLogo())
	case "SavingsBalanceIconUrl":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SavingsBalanceIconUrl\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.savingsBalanceIconUrl())
	case "AddFundsAdditionalTextIconURL":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AddFundsAdditionalTextIconURL\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.addFundsAdditionalTextIconURL())
	case "SelfTransferText":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SelfTransferText\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.selfTransferText())
	case "SavingsBalanceString":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SavingsBalanceString\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.savingsBalanceString())
	case "UpgradeScreenTitle":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UpgradeScreenTitle\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.upgradeScreenTitle())
	case "AddFundsAdditionalText":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AddFundsAdditionalText\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.addFundsAdditionalText())
	case "PaymentOptionsTitleFadedFontColor":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PaymentOptionsTitleFadedFontColor\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.paymentOptionsTitleFadedFontColor())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for TieringPitchInPaymentOptionsParams", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the HomeFeatureQuestFlags object.
func (obj *HomeFeatureQuestFlags) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "EnableUnderlayUpiComponent":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableUnderlayUpiComponent\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.enableUnderlayUpiComponent())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for HomeFeatureQuestFlags", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the TieringNotchConfig object.
func (obj *TieringNotchConfig) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "UpgradeJourneyConf":
		return obj.UpgradeJourneyConf().GetQuestVariableValue(questVariablePath[1:])
	case "EarningPotentialConf":
		return obj.EarningPotentialConf().GetQuestVariableValue(questVariablePath[1:])
	case "SegmentConfMap":
		if len(questVariablePath) < 3 {
			return nil, fmt.Errorf("incomplete variable path")
		}
		mapVal, exist := obj.SegmentConfMap().Load(questVariablePath[1])
		if !exist {
			return nil, fmt.Errorf("invalid quest variable path %q for SegmentConfMap", strings.Join(questVariablePath, "."))
		}
		return mapVal.GetQuestVariableValue(questVariablePath[2:])

	default:
		return nil, fmt.Errorf("invalid quest variable path %q for TieringNotchConfig", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the UpgradeJourneyConf object.
func (obj *UpgradeJourneyConf) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "SegmentExpiry":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SegmentExpiry\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.segmentExpiry())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for UpgradeJourneyConf", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the EarningPotentialConf object.
func (obj *EarningPotentialConf) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "LowPotentialLimit":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LowPotentialLimit\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.lowPotentialLimit())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for EarningPotentialConf", strings.Join(questVariablePath, "."))
	}
}

// GetQuestVariableValue returns th value for a variable path relative to the SegmentConf object.
func (obj *SegmentConf) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "Priority":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Priority\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.priority())
	case "UseRealtime":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseRealtime\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.useRealtime())
	case "SegmentId":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SegmentId\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.segmentId())
	case "SegmentTemplate":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SegmentTemplate\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.segmentTemplate())
	default:
		return nil, fmt.Errorf("invalid quest variable path %q for SegmentConf", strings.Join(questVariablePath, "."))
	}
}
