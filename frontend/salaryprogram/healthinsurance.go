package salaryprogram

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"
	"time"

	header2 "github.com/epifi/gamma/pkg/frontend/header"

	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/gamma/api/frontend/deeplink"

	"github.com/samber/lo"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/salaryprogram"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	types "github.com/epifi/gamma/api/typesv2"
)

// GetHealthInsurancePolicyInformationPage returns the details to be shown on health insurance policy information page.
// nolint: gocritic
func (s *Service) GetHealthInsurancePolicyInformationPage(ctx context.Context, req *fePb.HealthInsurancePolicyInformationPageRequest) (*fePb.HealthInsurancePolicyInformationPageResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	rewardOffersRes, err := s.rewardOffersClient.GetRewardOffersForScreen(ctx, &beRewardOffersPb.GetRewardOffersForScreenRequest{
		ActorId: actorId,
		Filter: &beRewardOffersPb.GetRewardOffersForScreenRequest_Filter{
			OfferTypes: []beRewardsPb.RewardOfferType{beRewardsPb.RewardOfferType_SALARY_PROGRAM_OFFER},
		},
	})
	if grpcError := epifigrpc.RPCError(rewardOffersRes, err); grpcError != nil {
		logger.Error(ctx, "error fetching reward offers for health insurance PolicyInformationPage", zap.Error(err), zap.Any(logger.RPC_STATUS, rewardOffersRes.GetStatus()))
		return &fePb.HealthInsurancePolicyInformationPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Errorf("error in GetHealthInsurancePolicyInformationPage rpc call:%w", err).Error())},
		}, nil
	}
	rewardOffers := rewardOffersRes.GetRewardOffers()
	var healthInsurancePolicyType string
	for _, rewardOffer := range rewardOffers {
		salaryProgramHealthInsurance := s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(rewardOffer.GetId())
		if salaryProgramHealthInsurance != nil {
			healthInsurancePolicyType = salaryProgramHealthInsurance.HealthInsurancePolicyType()
			break
		}
	}
	if healthInsurancePolicyType == "" {
		logger.Error(ctx, "HealthInsuranceType cannot be empty for GetHealthInsurancePolicyInformationPage")
		return &fePb.HealthInsurancePolicyInformationPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Errorf("HealthInsuranceType cannot be empty for GetHealthInsurancePolicyInformationPage").Error())},
		}, nil
	}

	tabs := []*fePb.HealthInsurancePolicyInformationPageResponse_TabData{getKeyInfoTabForPolicyInformationPage(healthInsurancePolicyType), getInclusionsTabForPolicyInformationPage(healthInsurancePolicyType), getHowItWorksTabForPolicyInformationPage(healthInsurancePolicyType)}

	return &fePb.HealthInsurancePolicyInformationPageResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Tabs:       tabs,
	}, nil
}

// nolint: funlen, gocritic,dupl
func getKeyInfoTabForPolicyInformationPage(healthInsurancePolicyType string) *fePb.HealthInsurancePolicyInformationPageResponse_TabData {
	var infoSections []*fePb.HealthInsurancePolicyInformationPageResponse_InfoSection
	// Information for SUPER_TOP_UP_INSURANCE
	if healthInsurancePolicyType == healthinsurancePb.HealthInsurancePolicyType_SUPER_TOP_UP_INSURANCE.String() {
		// key value info section containing info like age eligibility, policy type etc.
		// nolint: gocritic
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_KeyValueInfoSection{
				KeyValueInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_KeyValueInfoSection{
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_KeyValueInfoSection_KeyValueInfo{
						{
							Key:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "AGE ELIGIBILITY"}, FontColor: steelColorHexCode},
							Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "18 to 65 Years"}, FontColor: greyColorHexCode},
						},
						{
							Key:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "POLICY TYPE"}, FontColor: steelColorHexCode},
							Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Individual"}, FontColor: greyColorHexCode},
						},
						{
							Key:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "COVER TENURE"}, FontColor: steelColorHexCode},
							Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "1 Year"}, FontColor: greyColorHexCode},
						},
					},
				},
			},
		})

		// "Claims settlement methods" info section
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Claims settlement methods"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Both cashless claims & reimbursements are supported"}, FontColor: greyColorHexCode}},
						}},
				},
			},
		})

		// "Waiting period details" info section
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Waiting period details"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Waiting period is the amount of time you need to wait from the activation of your policy to be able to use the insurance benefits"}, FontColor: greyColorHexCode},
								}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Accidents are covered from Day One. No waiting period."}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Initial waiting period: 30 days"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Waiting period for specified diseases: 2 years"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Waiting period for pre-existing diseases: 2 years"}, FontColor: greyColorHexCode}},
						},
					},
				},
			},
		})

		// "Premium payment frequency" info section
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Premium payment frequency: Monthly"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Premiums are paid every month by Fi Money."}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "The policy will be active as long as your salary benefits are active on Fi Money."}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "The policy will lapse if you are inactive on Fi Money, but a new policy can be availed when you reactivate salary benefits."}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "For a claim being made in any particular month, the claims settlement shall be done after the recovery (deduction) of the premium amounts of the pending dues."}, FontColor: greyColorHexCode}},
						},
					},
				},
			},
		})
	} else {
		// information for BASE_HEALTH_INSURANCE
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_KeyValueInfoSection{
				KeyValueInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_KeyValueInfoSection{
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_KeyValueInfoSection_KeyValueInfo{
						{
							Key:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "AGE ELIGIBILITY"}, FontColor: steelColorHexCode},
							Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "18 to 65 Years"}, FontColor: greyColorHexCode},
						},
						{
							Key:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "POLICY TYPE"}, FontColor: steelColorHexCode},
							Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Individual"}, FontColor: greyColorHexCode},
						},
						{
							Key:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "SUM INSURED"}, FontColor: steelColorHexCode},
							Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "up to ₹2 Lacs"}, FontColor: greyColorHexCode},
						},
						{
							Key:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "COVER TENURE"}, FontColor: steelColorHexCode},
							Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "1 Year"}, FontColor: greyColorHexCode},
						},
					},
				},
			},
		})

		// "Claims settlement methods" info section
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Claims settlement methods"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Both cashless claims & reimbursements are supported"}, FontColor: greyColorHexCode}},
						}},
				},
			},
		})

		// "Waiting period details" info section
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Waiting period details"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Waiting period is the amount of time you need to wait from the activation of your policy to be able to use the insurance benefits"}, FontColor: greyColorHexCode},
								}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Accidents are covered from Day One. No waiting period."}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Initial waiting period: 30 days"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Waiting period for specified diseases: 2 years"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Waiting period for pre-existing diseases: 2 years"}, FontColor: greyColorHexCode}},
						},
					},
				},
			},
		})

		// "Premium payment frequency" info section
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Premium payment frequency: Monthly"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Premiums are paid every month by Fi Money."}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "The policy will be active as long as your salary benefits are active on Fi Money."}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "The policy will lapse if you are inactive on Fi Money, but a new policy can be availed when you reactivate salary benefits."}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "For a claim being made in any particular month, the claims settlement shall be done after the recovery (deduction) of the premium amounts of the pending dues."}, FontColor: greyColorHexCode}},
						},
					},
				},
			},
		})
	}
	return &fePb.HealthInsurancePolicyInformationPageResponse_TabData{
		Title:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Key info"}, FontColor: nightColorHexCode},
		IconUrl: "https://epifi-icons.pointz.in/salaryprogram/key-info.png",
		Data: &fePb.HealthInsurancePolicyInformationPageResponse_TabData_KeyInfoTab{
			KeyInfoTab: &fePb.HealthInsurancePolicyInformationPageResponse_KeyInfoTab{InfoSections: infoSections},
		},
	}
}

// nolint: funlen, dupl, gocritic
func getInclusionsTabForPolicyInformationPage(healthInsurancePolicyType string) *fePb.HealthInsurancePolicyInformationPageResponse_TabData {
	var infoSections []*fePb.HealthInsurancePolicyInformationPageResponse_InfoSection
	// Info for SUPER_TOP_UP_INSURANCE
	if healthInsurancePolicyType == healthinsurancePb.HealthInsurancePolicyType_SUPER_TOP_UP_INSURANCE.String() {
		// "What is included" info section
		// nolint: gocritic
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "What is included"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Inpatient hospitalization (including AYUSH)"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Day care treatment"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Hospital room rent cover for Single Private Rooms"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Pre & post-hospitalisation expense cover for 30 and 60 days respectively"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Domiciliary (at-home) hospitalisation & organ donor coverage"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Road ambulance cover per event is ₹1,500"}, FontColor: greyColorHexCode}},
						},
					},
				},
			},
		})

		// "What is not included" info section
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "What is not included"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Treatment received outside of India"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Expenses related to sterility and infertility"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Cosmetic or plastic surgery"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Unproven treatments"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Breach of law"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Note: The list is only indicative. Please refer to policy terminology documentation for detailed information on exclusions and other terms & conditions."}, FontColor: greyColorHexCode}},
						},
					},
				},
			},
		})
	} else { // information for BASE_HEALTH_INSURANCE
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "What is included"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Inpatient hospitalization (including AYUSH)"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Day care treatment"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Hospital room rent cover for Single Private Rooms"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Pre & post-hospitalisation expense cover for 30 and 60 days respectively"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Domiciliary (at-home) hospitalisation & organ donor coverage"}, FontColor: greyColorHexCode}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Road ambulance cover per event is ₹1,500"}, FontColor: greyColorHexCode}},
						},
					},
				},
			},
		})

		// "What is not included" info section
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "What is not included"}, FontColor: nightColorHexCode},
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Treatment received outside of India"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Expenses related to sterility and infertility"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Cosmetic or plastic surgery"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Unproven treatments"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Breach of law"}, FontColor: greyColorHexCode},
							},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_BulletPoint{
								BulletPoint: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Note: The list is only indicative. Please refer to policy terminology documentation for detailed information on exclusions and other terms & conditions."}, FontColor: greyColorHexCode}},
						},
					},
				},
			},
		})
	}

	return &fePb.HealthInsurancePolicyInformationPageResponse_TabData{
		Title:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Inclusions"}, FontColor: nightColorHexCode},
		IconUrl: "https://epifi-icons.pointz.in/salaryprogram/star-border-only.png",
		Data: &fePb.HealthInsurancePolicyInformationPageResponse_TabData_InclusionsInfoTab{
			InclusionsInfoTab: &fePb.HealthInsurancePolicyInformationPageResponse_InclusionsInfoTab{InfoSections: infoSections},
		},
	}
}

// nolint: funlen,dupl,gocritic
func getHowItWorksTabForPolicyInformationPage(healthInsurancePolicyType string) *fePb.HealthInsurancePolicyInformationPageResponse_TabData {
	var infoSections []*fePb.HealthInsurancePolicyInformationPageResponse_InfoSection
	// Info for SUPER_TOP_UP_INSURANCE
	if healthInsurancePolicyType == healthinsurancePb.HealthInsurancePolicyType_SUPER_TOP_UP_INSURANCE.String() {
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Let’s say you have an existing health insurance cover of ₹3 lakh, and you then take a Super Top-up cover of ₹6 lakh (with total coverage coming up to ₹9 lakh). Now, with the threshold limit of ₹3 lakh, in case of a claim, here are the scenarios applicable:"}, FontColor: greyColorHexCode},
								}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Title:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Scenario 1"}, FontColor: nightColorHexCode},
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "If there is a single claim of ₹2 lakh in a year, your current policy will cover you for ₹2 lakh."}, FontColor: greyColorHexCode},
								}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Title:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Scenario 2"}, FontColor: nightColorHexCode},
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "If there is a single claim of ₹5 lakh in a year, your current policy will cover you for ₹3 lakh and the Super Top-up plan will cover you for the remaining ₹2 lakh."}, FontColor: greyColorHexCode},
								}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Title:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Scenario 3"}, FontColor: nightColorHexCode},
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "If there is a single claim of ₹9 lakh in a year, your current policy will cover you for ₹3 lakh and the remaining claim amount of ₹6 lakh will be covered by the Super Top-up medical insurance plan."}, FontColor: greyColorHexCode},
								}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Title:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Scenario 4"}, FontColor: nightColorHexCode},
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "If there are two claims in a year, one for ₹3 lakh (Claim 1) and another for ₹2.5 lakh (Claim 2), your current policy will cover the ‘Claim 1’ amount of ₹3 lakh, and the total coverage is exhausted, then the ‘Claim 2’ amount (₹2.5 lakh) will be covered by this Super Top-up plan (even though the ‘Claim 2’ amount is less than the threshold limit)"}, FontColor: greyColorHexCode},
								}},
						},
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Title:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Scenario 5"}, FontColor: nightColorHexCode},
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "If there are two claims in a year, one for ₹5 lakh (Claim 1) and another for ₹4 lakh (claim 2), the regular policy will cover you for up to ₹3 lakh (Claim 1) and the Super Top-up policy will cover the remaining ₹2 lakh amount (a portion of ‘Claim 1’). The entire ‘Claim 2’ amount (₹4 lakh) will be covered by the Super Top-up plan since the regular policy coverage will be exhausted at this point."}, FontColor: greyColorHexCode},
								}},
						},
					},
				},
			},
		})
	} else { // information for BASE_HEALTH_INSURANCE
		infoSections = append(infoSections, &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection{
			Section: &fePb.HealthInsurancePolicyInformationPageResponse_InfoSection_AdditionalInfoSection{
				AdditionalInfoSection: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection{
					Infos: []*fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info{
						{
							Data: &fePb.HealthInsurancePolicyInformationPageResponse_AdditionalInfoSection_Info_ParagraphInfo{
								ParagraphInfo: &fePb.HealthInsurancePolicyInformationPageResponse_ParagraphInfo{
									Content: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "The policy has a sum insured available of  ₹2 Lacs. A health card will be issued to the individual who avails this policy. It is similar to an identity card. THis card would entitle you to avail cashless hospitalization facility at our network hospital. A health card mentions the contact details of the TPA. in case of a medical emergency, you can call on these numbers for queries and clarifications. This card needs to be displayed at the time of admission in the hospital along with a valid passport size photo, identification and address proof (as applicable)."}, FontColor: greyColorHexCode},
								}},
						},
					},
				},
			},
		})
	}

	return &fePb.HealthInsurancePolicyInformationPageResponse_TabData{
		Title:   &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "How it works"}, FontColor: nightColorHexCode},
		IconUrl: "https://epifi-icons.pointz.in/salaryprogram/info.png",
		Data: &fePb.HealthInsurancePolicyInformationPageResponse_TabData_HowItWorksTab{
			HowItWorksTab: &fePb.HealthInsurancePolicyInformationPageResponse_HowItWorksTab{InfoSections: infoSections},
		},
	}
}

// GetHealthInsurancePolicyPurchaseRedirectionInfo returns the redirection details for allowing a user to purchase a health-insurance policy,
// for purchasing a policy, the user needs to be re-directed from Fi app to the vendor web-app where they can complete the policy purchase.
// nolint: dupl
func (s *Service) GetHealthInsurancePolicyPurchaseRedirectionInfo(ctx context.Context, req *fePb.HealthInsurancePolicyPurchaseRedirectionInfoRequest) (*fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	empInfo, empErr := s.getCurrentEmployerOfActor(ctx, actorId)
	if empErr != nil {
		logger.Debug(ctx, "error fetching employer info by actorId", zap.Error(empErr))
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Errorf("error fetching reward offers for PolicyPurchaseRedirectionInfo:%w", empErr).Error())}}, nil
	}

	if empInfo.GetSalaryProgramChannel() == fePb.EmployerSalaryProgramChannel_B2C {
		logger.Error(ctx, "B2C employer cannot purchase health insurance policy", zap.String(logger.ACTOR_ID_V2, actorId))
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("B2C employer cannot purchase health insurance policy")}}, nil
	}

	rewardOffersRes, err := s.rewardOffersClient.GetRewardOffersForScreen(ctx, &beRewardOffersPb.GetRewardOffersForScreenRequest{
		ActorId: actorId,
		Filter: &beRewardOffersPb.GetRewardOffersForScreenRequest_Filter{
			OfferTypes: []beRewardsPb.RewardOfferType{beRewardsPb.RewardOfferType_SALARY_PROGRAM_OFFER},
		},
	})
	if grpcError := epifigrpc.RPCError(rewardOffersRes, err); grpcError != nil {
		logger.Error(ctx, "error fetching reward offers for PolicyPurchaseRedirectionInfo", zap.Error(err), zap.Any(logger.RPC_STATUS, rewardOffersRes.GetStatus()))
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Errorf("error fetching reward offers for PolicyPurchaseRedirectionInfo:%w", err).Error())}}, nil
	}
	rewardOffers := rewardOffersRes.GetRewardOffers()
	var healthInsurancePolicyType string
	for _, rewardOffer := range rewardOffers {
		salaryProgramHealthInsurance := s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(rewardOffer.GetId())
		if salaryProgramHealthInsurance != nil {
			healthInsurancePolicyType = salaryProgramHealthInsurance.HealthInsurancePolicyType()
			break
		}
	}
	if healthInsurancePolicyType == "" {
		logger.Error(ctx, "HealthInsuranceType cannot be empty for PolicyPurchaseRedirectionInfo")
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("HealthInsuranceType cannot be empty for PolicyPurchaseRedirectionInfo")}}, nil
	}

	if empInfo.GetSalaryProgramChannel() == fePb.EmployerSalaryProgramChannel_B2B &&
		(!apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().HealthInsuranceOnsurityPolicyFlowsConfig())) &&
		lo.Contains([]string{healthinsurancePb.HealthInsurancePolicyType_BASE_HEALTH_INSURANCE.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A2C.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_1A.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_DIAMOND_PLUS_1A.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_GHI_GPA_RUBY_1A.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_GHI_GPA_OPAL_1A.String(),
		}, healthInsurancePolicyType) {
		logger.Error(ctx, "BHI for B2B which is onsurity flows is not allowed due to min app version failure", zap.String(logger.ACTOR_ID_V2, actorId))
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{
			RespHeader: header2.BottomSheetErrResp(rpc.StatusFailedPreconditionWithDebugMsg("BHI for B2B which is onsurity flows is not allowed due to min app version failure"),
				"", "Policy Purchase not allowed", "Please upgrade your app", "")}, nil
	}

	initiatePurchaseRes, err := s.healthInsuranceClient.InitiatePolicyPurchase(ctx, &healthinsurancePb.InitiatePolicyPurchaseRequest{
		ActorId:    actorId,
		PolicyType: healthinsurancePb.HealthInsurancePolicyType(healthinsurancePb.HealthInsurancePolicyType_value[healthInsurancePolicyType]),
	})
	if rpcErr := epifigrpc.RPCError(initiatePurchaseRes, err); rpcErr != nil {
		logger.Error(ctx, "healthInsuranceClient.InitiatePolicyPurchase rpc call failed", zap.Error(rpcErr))
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusFromErrorWithDefaultInternal(rpcErr)}}, nil
	}

	switch initiatePurchaseRes.GetPolicyPurchaseRedirectionInfo().(type) {
	case *healthinsurancePb.InitiatePolicyPurchaseResponse_RiskcovryInfo:
		riskcovryInfo := initiatePurchaseRes.GetPolicyPurchaseRedirectionInfo().(*healthinsurancePb.InitiatePolicyPurchaseResponse_RiskcovryInfo)
		// TODO: remove this after debugging the testing of redirection url
		if lo.Contains(GetHardCodedActorsForDisplayingRedirectionURL(), actorId) {
			logger.Info(ctx, "initiate policy purchase redirection URL", zap.Any("redirection_url", riskcovryInfo.RiskcovryInfo.GetRedirectionUrl()), zap.String(logger.ACTOR_ID_V2, actorId))
		}
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{
			RespHeader:     &header.ResponseHeader{Status: rpc.StatusOk()},
			RedirectionUrl: initiatePurchaseRes.GetPolicyPurchaseRedirectionUrl(),
			PolicyPurchaseRedirectionInfo: &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse_RiskcovryInfo{
				RiskcovryInfo: &fePb.RiskcovryInfo{
					RedirectionUrl: riskcovryInfo.RiskcovryInfo.GetRedirectionUrl(),
				},
			},
		}, nil

	case *healthinsurancePb.InitiatePolicyPurchaseResponse_OnsurityInfo:
		onsurityInfo := initiatePurchaseRes.GetPolicyPurchaseRedirectionInfo().(*healthinsurancePb.InitiatePolicyPurchaseResponse_OnsurityInfo).OnsurityInfo
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{
			RespHeader:     &header.ResponseHeader{Status: rpc.StatusOk()},
			RedirectionUrl: initiatePurchaseRes.GetPolicyPurchaseRedirectionUrl(),
			PolicyPurchaseRedirectionInfo: &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse_OnsurityInfo{
				OnsurityInfo: &fePb.OnsurityInfo{
					InfoInputScreen: GetOnsurityInputFormDeeplink(onsurityInfo.GetName(), onsurityInfo.GetGender(),
						onsurityInfo.GetDateOfBirth(), onsurityInfo.GetPhoneNumber(), []types.Gender{types.Gender_MALE, types.Gender_FEMALE, types.Gender_OTHER}),
				},
			},
		}, nil
	default:
		return &fePb.HealthInsurancePolicyPurchaseRedirectionInfoResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("invaid vendor for health insurance")},
		}, nil
	}
}

// GetIssuedHealthInsurancePoliciesRedirectionInfo returns the redirection details for viewing already issued health-insurance policies of a user,
// for viewing all the issued policies, we want to re-direct the user from Fi app to vendor web-app where they can view all their current/past policies.
// nolint: dupl,gocritic
func (s *Service) GetIssuedHealthInsurancePoliciesRedirectionInfo(ctx context.Context, req *fePb.IssuedHealthInsurancePoliciesRedirectionInfoRequest) (*fePb.IssuedHealthInsurancePoliciesRedirectionInfoResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	issuedPoliciesRedirectionRes, err := s.healthInsuranceClient.GetIssuedPoliciesRedirectionInfo(ctx, &healthinsurancePb.IssuedPoliciesRedirectionInfoRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(issuedPoliciesRedirectionRes, err); rpcErr != nil {
		logger.Error(ctx, "healthInsuranceClient.GetIssuedPoliciesRedirectionInfo rpc call failed", zap.Error(rpcErr))
		return &fePb.IssuedHealthInsurancePoliciesRedirectionInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error())}}, nil
	}

	// TODO: remove this after debugging the testing of redirection url
	if lo.Contains(GetHardCodedActorsForDisplayingRedirectionURL(), actorId) {
		logger.Info(ctx, "issued policies redirection URL", zap.Any("redirection_url", issuedPoliciesRedirectionRes.GetRedirectionUrl()), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	res := &fePb.IssuedHealthInsurancePoliciesRedirectionInfoResponse{
		RespHeader:     &header.ResponseHeader{Status: rpc.StatusOk()},
		RedirectionUrl: issuedPoliciesRedirectionRes.GetRedirectionUrl(),
	}

	switch issuedPoliciesRedirectionRes.GetVendorBasedRedirectionInfo().(type) {
	case *healthinsurancePb.IssuedPoliciesRedirectionInfoResponse_RiskcovryRedirectionUrl:
		res.VendorBasedRedirectionUrl = &fePb.IssuedHealthInsurancePoliciesRedirectionInfoResponse_RiskcovryRedirectionUrl{
			RiskcovryRedirectionUrl: issuedPoliciesRedirectionRes.GetVendorBasedRedirectionInfo().(*healthinsurancePb.IssuedPoliciesRedirectionInfoResponse_RiskcovryRedirectionUrl).RiskcovryRedirectionUrl}
	case *healthinsurancePb.IssuedPoliciesRedirectionInfoResponse_OnsurityRedirectionUrl:
		res.VendorBasedRedirectionUrl = &fePb.IssuedHealthInsurancePoliciesRedirectionInfoResponse_OnsurityRedirectionInfo{
			OnsurityRedirectionInfo: &deeplink.Deeplink{
				Screen: deeplink.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: &deeplink.Deeplink_ExternalRedirectionScreenOptions{
					ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
						ExternalUrl: issuedPoliciesRedirectionRes.GetVendorBasedRedirectionInfo().(*healthinsurancePb.IssuedPoliciesRedirectionInfoResponse_OnsurityRedirectionUrl).OnsurityRedirectionUrl},
				},
			},
		}
	}

	return res, nil
}

func (s *Service) getHealthInsurancePolicyFAQDocUrl(ctx context.Context, healthInsuranceOfferId string) (string, error) {
	policyFAQDocS3Path := s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(healthInsuranceOfferId).HealthInsurancePolicyFAQsDocS3Path()
	if policyFAQDocS3Path == "" {
		return "", fmt.Errorf("empty s3 file path for policy faqs doc")
	}
	policyFAQDocSignedUrl, err := s.salaryProgramBucketS3Client.GetPreSignedUrl(ctx, policyFAQDocS3Path, 30*time.Minute)
	if err != nil {
		return "", fmt.Errorf("error generating s3 signed url for policy FAQ doc, err : %w", err)
	}
	return policyFAQDocSignedUrl, nil
}

func (s *Service) getHealthInsurancePolicyClaimProcessDocUrl(ctx context.Context, healthInsuranceOfferId string) (string, error) {
	policyClaimProcessDocS3Path := s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(healthInsuranceOfferId).HealthInsurancePolicyClaimProcessDocS3Path()
	if policyClaimProcessDocS3Path == "" {
		return "", fmt.Errorf("empty s3 file path for policy claim process doc")
	}
	policyClaimProcessDocSignedUrl, err := s.salaryProgramBucketS3Client.GetPreSignedUrl(ctx, policyClaimProcessDocS3Path, 30*time.Minute)
	if err != nil {
		return "", fmt.Errorf("error generating s3 signed url for policy claim process doc, err : %w", err)
	}
	return policyClaimProcessDocSignedUrl, nil
}

func (s *Service) getHealthInsuranceCashlessHospitalListDocUrl(ctx context.Context, healthInsuranceOfferId string) (string, error) {
	cashlessHospitalListDocS3Path := s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(healthInsuranceOfferId).HealthInsuranceCashlessHospitalListDocS3Path()
	if cashlessHospitalListDocS3Path == "" {
		return "", fmt.Errorf("empty s3 file path for cashless hospital list doc")
	}
	cashlessHospitalListDocSignedUrl, err := s.salaryProgramBucketS3Client.GetPreSignedUrl(ctx, cashlessHospitalListDocS3Path, 30*time.Minute)
	if err != nil {
		return "", fmt.Errorf("error generating s3 signed url for cashless hospital list doc, err : %w", err)
	}
	return cashlessHospitalListDocSignedUrl, nil
}

func (s *Service) getHealthInsuranceTncsDocUrl(ctx context.Context, healthInsuranceOfferId string) (string, error) {
	logger.DebugNoCtx("TncsDoc:", zap.Any("TncsDoc", s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(healthInsuranceOfferId).HealthInsuranceTncsDocS3Path()))
	tncsDocS3Path := s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(healthInsuranceOfferId).HealthInsuranceTncsDocS3Path()
	if tncsDocS3Path == "" {
		return "", fmt.Errorf("empty s3 file path for healthinsurance tncs doc")
	}
	tncsDocSignedUrl, err := s.salaryProgramBucketS3Client.GetPreSignedUrl(ctx, tncsDocS3Path, 30*time.Minute)
	if err != nil {
		return "", fmt.Errorf("error generating s3 signed url for healthinsurance tncs doc, err : %w", err)
	}
	return tncsDocSignedUrl, nil
}

// HealthInsurancePurchaseNewPolicy returns the redirection details after purchasing the policy for doing next set of actions.
// nolint: funlen
func (s *Service) HealthInsurancePurchaseNewPolicy(ctx context.Context, req *fePb.HealthInsurancePurchaseNewPolicyRequest) (*fePb.HealthInsurancePurchaseNewPolicyResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	rewardOffersRes, err := s.rewardOffersClient.GetRewardOffersForScreen(ctx, &beRewardOffersPb.GetRewardOffersForScreenRequest{
		ActorId: actorId,
		Filter: &beRewardOffersPb.GetRewardOffersForScreenRequest_Filter{
			OfferTypes: []beRewardsPb.RewardOfferType{beRewardsPb.RewardOfferType_SALARY_PROGRAM_OFFER},
		},
	})
	if grpcError := epifigrpc.RPCError(rewardOffersRes, err); grpcError != nil {
		logger.Error(ctx, "error fetching reward offers for HealthInsurancePurchaseNewPolicy", zap.Error(err), zap.Any(logger.RPC_STATUS, rewardOffersRes.GetStatus()))
		return &fePb.HealthInsurancePurchaseNewPolicyResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
	}

	rewardOffers := rewardOffersRes.GetRewardOffers()
	var healthInsurancePolicyType string
	for _, rewardOffer := range rewardOffers {
		salaryProgramHealthInsurance := s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(rewardOffer.GetId())
		if salaryProgramHealthInsurance != nil {
			healthInsurancePolicyType = salaryProgramHealthInsurance.HealthInsurancePolicyType()
			break
		} else {
			logger.Error(ctx, "reward offer id not configured in HealthInsuranceRewardOfferIdToPolicyConfigMap", zap.String(logger.REWARD_OFFER_ID, rewardOffer.GetId()))
		}
	}
	if healthInsurancePolicyType == "" {
		logger.Error(ctx, "HealthInsuranceType cannot be empty for PolicyPurchaseRedirectionInfo", zap.Int("length", len(rewardOffers)))
		return &fePb.HealthInsurancePurchaseNewPolicyResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("HealthInsuranceType cannot be empty for PolicyPurchaseRedirectionInfo")}}, nil
	}

	switch req.GetInputDetails().(type) {
	case *fePb.HealthInsurancePurchaseNewPolicyRequest_OnsurityPlan_:
		if req.GetInputDetails().(*fePb.HealthInsurancePurchaseNewPolicyRequest_OnsurityPlan_) == nil || req.GetInputDetails().(*fePb.HealthInsurancePurchaseNewPolicyRequest_OnsurityPlan_).OnsurityPlan == nil {
			return &fePb.HealthInsurancePurchaseNewPolicyResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("Onsurity input details are nil")}}, nil
		}

		onsurityNewPolicyPurchaseDetails := req.GetInputDetails().(*fePb.HealthInsurancePurchaseNewPolicyRequest_OnsurityPlan_).OnsurityPlan

		phoneNumber, err := commontypes.ParsePhoneNumber("91" + onsurityNewPolicyPurchaseDetails.GetPhoneNumber())
		if err != nil {
			return &fePb.HealthInsurancePurchaseNewPolicyResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
		}

		gender := types.Gender(types.Gender_value[strings.ToUpper(onsurityNewPolicyPurchaseDetails.GetGender())])
		if gender == types.Gender_GENDER_UNSPECIFIED {
			return &fePb.HealthInsurancePurchaseNewPolicyResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("invalid gender string passed")}}, nil
		}

		dob, err := datetime.ParseStringTimeStampProto(OnsurityInputPageDobDateLayout, onsurityNewPolicyPurchaseDetails.GetDob())
		if err != nil {
			return &fePb.HealthInsurancePurchaseNewPolicyResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("failed to convert string to timestamp,%v", err))}}, nil
		}

		initiatePurchaseRes, err := s.healthInsuranceClient.IssueNewPolicy(ctx, &healthinsurancePb.IssueNewPolicyRequest{
			ActorId: actorId,
			Name: &commontypes.Name{
				FirstName: onsurityNewPolicyPurchaseDetails.GetFullName(),
			},
			PhoneNumber:  phoneNumber,
			Gender:       gender,
			DateOfBirth:  datetime.TimeToDateInLoc(dob.AsTime(), nil),
			PolicyVendor: commonvgpb.Vendor_ONSURITY,
			PolicyType:   healthinsurancePb.HealthInsurancePolicyType(healthinsurancePb.HealthInsurancePolicyType_value[healthInsurancePolicyType]),
		})
		if rpcErr := epifigrpc.RPCError(initiatePurchaseRes, err); rpcErr != nil {
			logger.Error(ctx, "healthInsuranceClient.IssueNewPolicy rpc call failed", zap.Error(rpcErr))
			return &fePb.HealthInsurancePurchaseNewPolicyResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error())}}, nil
		}

		return &fePb.HealthInsurancePurchaseNewPolicyResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: &deeplink.Deeplink_ExternalRedirectionScreenOptions{
					ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
						ExternalUrl: OnsurityAppDeeplink,
					},
				},
			},
		}, nil
	default:
		logger.Error(ctx, "invalid policy vendor type received")
		return &fePb.HealthInsurancePurchaseNewPolicyResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("invalid policy vendor type received")},
		}, nil
	}
}
