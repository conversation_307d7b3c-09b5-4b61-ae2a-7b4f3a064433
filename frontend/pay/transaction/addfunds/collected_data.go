package addfunds

import (
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	tieringPb "github.com/epifi/gamma/api/tiering"
)

// CollectedData is the data getter struct for gathered data response for add funds with setters and getters
type CollectedData struct {
	bankCustomerData *bankCustPb.GetBankCustomerResponse
	isV3EnabledData  bool
	isV4EnabledData  bool
	trialsResponse   *tieringPb.GetTrialDetailsResponse
}

func (d *CollectedData) TrialsResponse() *tieringPb.GetTrialDetailsResponse {
	if d != nil {
		return d.trialsResponse
	}
	return nil
}

func (d *CollectedData) BankCustomerData() (defaultVal *bankCustPb.GetBankCustomerResponse) {
	if d != nil {
		return d.bankCustomerData
	}
	return defaultVal
}

func (d *CollectedData) IsV3EnabledData() (defaultVal bool) {
	if d != nil {
		return d.isV3EnabledData
	}
	return defaultVal
}

func (d *CollectedData) IsV4EnabledData() (defaultVal bool) {
	if d != nil {
		return d.isV4EnabledData
	}
	return defaultVal
}

func (d *CollectedData) SetBankCustomerData(data *bankCustPb.GetBankCustomerResponse) {
	if d != nil {
		d.bankCustomerData = data
	}
}

func (d *CollectedData) SetIsV3EnabledData(isEnabled bool) {
	if d != nil {
		d.isV3EnabledData = isEnabled
	}
}

func (d *CollectedData) SetIsV4EnabledData(isEnabled bool) {
	if d != nil {
		d.isV4EnabledData = isEnabled
	}
}

func (d *CollectedData) SetTrialsResponse(trialsResponse *tieringPb.GetTrialDetailsResponse) {
	if d != nil {
		d.trialsResponse = trialsResponse
	}
}
