// nolint: dupl,goimports
package transaction

import (
	"google.golang.org/protobuf/proto"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/tiering"
	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/tiering/amb/ui"
	"github.com/epifi/gamma/frontend/tiering/helper"

	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	fePb "github.com/epifi/gamma/api/frontend"
	timelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	payPb "github.com/epifi/gamma/api/pay"
	typesPb "github.com/epifi/gamma/api/typesv2"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/collect"
	dataCollector "github.com/epifi/gamma/frontend/pay/transaction/payment_options/data_collector"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/intent"
	neftImps "github.com/epifi/gamma/frontend/pay/transaction/payment_options/neft_imps"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/providers"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/tpap"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	paymentOptionsTitleFontColor = "#313234"

	footerLeftImgUrl               = "https://epifi-icons.pointz.in/tiering/add_funds/shield.png"
	footerLeftImgHeight      int32 = 24
	footerLeftImgWidth       int32 = 24
	footerLeftImgPadding     int32 = 4
	footerTitleText                = "POWERED BY"
	footerTextFontColor            = "#6A6D70"
	footerLogosUrl                 = "https://epifi-icons.pointz.in/add_funds/payment_options/footer_logos.png"
	footerLogosUrlFederal          = "https://epifi-icons.pointz.in/federal_only.png"
	footerLogosImgHeight     int32 = 14
	footerLogosImgWidth      int32 = 130
	footerRigthImgTxtPadding int32 = 6

	bottomSheetBgColor = "#E6E9ED"
)

var (
	// uiEntrypointToVendor represents the vendor via which the .
	// This will help to identify vendor specific details
	uiEntrypointToVendor = map[timelinePb.TransactionUIEntryPoint]fePb.Vendor{
		timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT:               fePb.Vendor_RAZORPAY,
		timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION: fePb.Vendor_RAZORPAY,
	}
	// order in which the various payment options should be displayed in the payments page
	paymentOptionsOrderedList = []txnPb.PaymentOptionType{
		txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP,
		txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_CARD,
		txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NETBANKING,
		txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NEFT_IMPS,
		txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT,
		txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT,
	}
)

func (s *Service) GetPaymentOptions(ctx context.Context, req *txnPb.GetPaymentOptionsRequest) (*txnPb.GetPaymentOptionsResponse, error) {
	evalRes, evalErr := s.releaseEvaluator.Evaluate(ctx, &release.CommonConstraintData{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Feature: typesPb.Feature_FEATURE_ENABLE_PAYMENT_OPTIONS_V1,
	})
	if evalErr == nil && evalRes {
		return s.GetPaymentOptionsV1(ctx, req)
	}
	actorId := req.GetReq().GetAuth().GetActorId()
	// 1. Collect data needed to build payment options
	collectedData, collectDataErr := s.paymentOptionsDataCollector.CollectData(ctx, actorId, req.GetTransactionUiEntryPoint())
	if collectDataErr != nil {
		logger.Error(ctx, "error collecting data for payment options", zap.Error(collectDataErr))
		return &txnPb.GetPaymentOptionsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error collecting data for payment options"),
			},
		}, nil
	}
	// 2. Get options display from config which defines the order and availability of payment options
	// TODO(sainath): Round up amount to int64 and pass that instead, if needed
	optionsDisplay, getOptionsErr := s.serverConf.PaymentOptionsConfig.GetOptionsDisplay(req.GetTransactionUiEntryPoint(), req.GetAmount().GetUnits(), config.TPAP_ACCOUNTS_COOL_OFF_NOT_IN_COOL_OFF)
	if getOptionsErr != nil {
		logger.Error(ctx, "error getting optionsDisplay from config", zap.Error(getOptionsErr))
		return &txnPb.GetPaymentOptionsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting optionsDisplay from config"),
			},
		}, nil
	}

	// TEMP - adding this check to enable new intent based add funds flow for select users for testing
	// if the user is allowed to use intent based add funds, then we add the intent option to the payment options
	isIntentAllowed, isAllowedErr := s.isIntentAddFundsAllowedForUser(ctx, actorId)
	if isAllowedErr != nil {
		logger.Error(ctx, "error checking if intent add funds is allowed for user", zap.Error(isAllowedErr))
	}

	// 3. Build payment options with the data collected and config
	paymentOptions, buildPaymentOptionsErr := s.buildPaymentOptions(ctx, req, collectedData, optionsDisplay)
	if buildPaymentOptionsErr != nil {
		logger.Error(ctx, "error building payment options", zap.Error(buildPaymentOptionsErr))
		return &txnPb.GetPaymentOptionsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error building payment options"),
			},
		}, nil
	}
	// TEMP(for testing new add funds flow) - show intent option to user if they are in allowed user group regardless of config
	if isIntentAllowed {
		intentOption, getIntentOptionErr := intent.GetIntentOption(ctx, s.conf, req.GetAmount(), collectedData, buildIntentArgs(collectedData, optionsDisplay), req.GetTransactionUiEntryPoint())
		if getIntentOptionErr != nil {
			return nil, fmt.Errorf("error fetching intent option: %w", getIntentOptionErr)
		}
		paymentOptions = append(paymentOptions, intentOption)

		// Adding collect as well to test the new add funds flow
		collectOption, getCollectOptionErr := collect.GetCollectOption(ctx, s.conf, req.GetAmount(), collectedData, buildCollectArgs(collectedData, optionsDisplay), req.GetTransactionUiEntryPoint())
		if getCollectOptionErr != nil {
			return nil, fmt.Errorf("error fetching collect option: %w", getCollectOptionErr)
		}
		paymentOptions = append(paymentOptions, collectOption)
	}

	conf, err := s.conf.PaymentOptionsConfig().GetDisplayInfoConfig(req.GetTransactionUiEntryPoint())
	if err != nil {
		logger.Error(ctx, "error in fetching display info config", zap.Error(err))
		return &txnPb.GetPaymentOptionsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetch display info config"),
			},
		}, nil
	}

	if len(paymentOptions) == 0 {
		logger.WarnWithCtx(ctx, "no payment options found for user", zap.String(logger.UI_ENTRY_POINT, req.GetTransactionUiEntryPoint().String()))
	}
	return &txnPb.GetPaymentOptionsResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Title:                 commontypes.GetTextFromStringFontColourFontStyle(conf.ScreenTitle(), paymentOptionsTitleFontColor, commontypes.FontStyle_HEADLINE_M),
		PaymentOptions:        paymentOptions,
		Footer:                getAddFundsFooter(footerLogosUrl),
		BackgroundColour:      widgetPb.GetBlockBackgroundColour(bottomSheetBgColor),
		OrchestrationMetadata: req.GetOrchestrationMetadata(),
	}, nil
}

func (s *Service) GetPaymentOptionsV1(ctx context.Context, req *txnPb.GetPaymentOptionsRequest) (*txnPb.GetPaymentOptionsResponse, error) {
	var (
		paymentOptions = make([]*txnPb.GetPaymentOptionsResponse_PaymentOption, 0)
		clientMetaData = &payPb.ClientIdentificationTxnMetaData{}
		actorId        = req.GetReq().GetAuth().GetActorId()
	)
	epifiActorId := req.GetReq().GetAuth().GetActorId()
	clientMetaDataBytes := req.GetOrchestrationMetadata()
	if clientMetaDataBytes != nil {
		decryptRes, decryptErr := s.payV1Client.GetPlainData(ctx, &payPb.GetPlainDataRequest{
			SignedData: clientMetaDataBytes,
		})
		if te := epifigrpc.RPCError(decryptRes, decryptErr); te != nil {
			logger.Error(ctx, "error decrypting orchestration metadata", zap.Error(te))
			return &txnPb.GetPaymentOptionsResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpcPb.StatusInternalWithDebugMsg("error decrypting orchestration metadata"),
				},
			}, nil
		}
		if unmarshalErr := proto.Unmarshal(decryptRes.GetPlainData(), clientMetaData); unmarshalErr != nil {
			logger.Error(ctx, "error in unmarshalling client metadata", zap.Error(unmarshalErr))
		}
	}

	logger.Debug(
		ctx,
		"received call to GetPaymentOptionsV1 with params",
		zap.Any("payment_options", req.GetOrderedPaymentOptionTypes()),
		zap.String(logger.CLIENT_REQUEST_ID, clientMetaData.GetClientReqId()),
		zap.String(logger.TO_ACTOR_ID, clientMetaData.GetTargetActorId()),
		zap.String(logger.PI_TO, clientMetaData.GetDomainOrderData().GetToPi()),
		zap.String(logger.PI_FROM, clientMetaData.GetDomainOrderData().GetFromPi()),
	)

	// 1. Collect data needed to build payment options
	collectedData, collectDataErr := s.paymentOptionsDataCollector.CollectData(ctx, epifiActorId, req.GetTransactionUiEntryPoint())
	if collectDataErr != nil {
		logger.Error(ctx, "error collecting data for payment options", zap.Error(collectDataErr))
		return &txnPb.GetPaymentOptionsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error collecting data for payment options"),
			},
		}, nil
	}
	optionsDisplay, getOptionsErr := s.serverConf.PaymentOptionsConfig.GetOptionsDisplay(req.GetTransactionUiEntryPoint(), req.GetAmount().GetUnits(), config.TPAP_ACCOUNTS_COOL_OFF_NOT_IN_COOL_OFF)
	if getOptionsErr != nil {
		logger.Error(ctx, "error getting optionsDisplay from config", zap.Error(getOptionsErr))
		return &txnPb.GetPaymentOptionsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error getting optionsDisplay from config"),
			},
		}, nil
	}

	// TEMP - adding this check to enable new intent based add funds flow for select users for testing
	// if the user is allowed to use intent based add funds, then we add the intent option to the payment options
	isIntentAllowed, isAllowedErr := s.isIntentAddFundsAllowedForUser(ctx, epifiActorId)
	if isAllowedErr != nil {
		logger.Error(ctx, "error checking if intent add funds is allowed for user", zap.Error(isAllowedErr))
	}

	payerAccountDetails := &payPb.AccountDetails{}
	if len(clientMetaData.GetDomainOrderData().GetAccountsEligibleForPaymentFulfillment()) != 0 {
		payerAccountDetails = clientMetaData.GetDomainOrderData().GetAccountsEligibleForPaymentFulfillment()[0]
	}

	logger.Debug(ctx,
		"GetPaymentOptionsV1 flow logs",
		zap.Bool("isIntentAllowed", isIntentAllowed),
		zap.String(logger.ACCOUNT_NUMBER, mask.GetMaskedString(mask.DontMaskFirstFourChars, payerAccountDetails.GetAccountNumber())),
		zap.String(logger.IFSC_CODE, mask.GetMaskedString(mask.DontMaskFirstFourChars, payerAccountDetails.GetIfscCode())),
		zap.String(logger.CLIENT_REQUEST_ID, clientMetaData.GetClientReqId()),
		zap.String(logger.TO_ACTOR_ID, clientMetaData.GetTargetActorId()))

	// vendor can remain unspecified since vendor in this context will be used for whitelisting i.e
	// for a particular vendor, certain payment modes, features will be applicable. So if the vendor
	// is unspecified, those modes will not be available which is expected.
	vendor := uiEntrypointToVendor[req.GetTransactionUiEntryPoint()]
	for _, option := range s.paymentOptions {
		// TEMP(for testing new add funds flow) - show intent and collect option to user if they are in allowed user group regardless of config
		if (option.GetPaymentOptionType() == txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT || option.GetPaymentOptionType() == txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT) && isIntentAllowed {
			paymentOption, err := option.GetPaymentOption(ctx, collectedData, req.GetAmount(), vendor, optionsDisplay, req.GetTransactionUiEntryPoint(), payerAccountDetails)
			if err != nil {
				logger.Error(ctx, "error in getting payment option data",
					zap.String(logger.PAYMENT_TYPE, option.GetPaymentOptionType().String()),
					zap.String(logger.CLIENT_REQUEST_ID, clientMetaData.GetClientReqId()),
					zap.String(logger.TO_ACTOR_ID, clientMetaData.GetTargetActorId()),
					zap.String(logger.VENDOR, vendor.String()),
					zap.Error(err))
				continue
			}
			if paymentOption == nil {
				logger.Debug(
					ctx,
					"no payment option found for the given user",
					zap.String(logger.CLIENT_REQUEST_ID, clientMetaData.GetClientReqId()),
					zap.String(logger.TO_ACTOR_ID, clientMetaData.GetTargetActorId()),
					zap.String("payment_option_type", option.GetPaymentOptionType().String()))
				continue
			}
			paymentOptions = append(paymentOptions, paymentOption)
			continue
		}
		if !option.IsPaymentOptionApplicable(&providers.IsPaymentOptionApplicableRequest{
			PaymentAmount:               req.GetAmount(),
			CollectedData:               collectedData,
			UiEntryPoint:                req.GetTransactionUiEntryPoint(),
			OptionsDisplay:              optionsDisplay,
			Vendor:                      vendor,
			RequestedPaymentOptionTypes: req.GetOrderedPaymentOptionTypes(),
		}) {
			continue
		}
		paymentOption, err := option.GetPaymentOption(ctx, collectedData, req.GetAmount(), vendor, optionsDisplay, req.GetTransactionUiEntryPoint(), payerAccountDetails)
		if err != nil {
			logger.Error(ctx, "error in getting payment option data",
				zap.String(logger.PAYMENT_TYPE, option.GetPaymentOptionType().String()),
				zap.Error(err))
			continue
		}
		if paymentOption == nil {
			continue
		}
		paymentOptions = append(paymentOptions, paymentOption)
	}
	paymentOptionSortingOrder := req.GetOrderedPaymentOptionTypes()
	if len(paymentOptionSortingOrder) == 0 {
		paymentOptionSortingOrder = paymentOptionsOrderedList
	}
	paymentOptions = sortPaymentOptionsInTheOrderForDisplay(paymentOptions, paymentOptionSortingOrder)
	dropOffScreen, err := s.buildDropPaymentOptionsDropOffScreen(actorId, req.GetTransactionUiEntryPoint(), collectedData)
	if err != nil {
		logger.Error(ctx, "error building drop off screen", zap.Error(err))
	}

	conf, err := s.conf.PaymentOptionsConfig().GetDisplayInfoConfig(req.GetTransactionUiEntryPoint())
	if err != nil {
		logger.Error(ctx, "error in fetching display info config", zap.Error(err))
		return &txnPb.GetPaymentOptionsResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error fetch display info config"),
			},
		}, nil
	}

	if len(paymentOptions) == 0 {
		logger.WarnWithCtx(ctx, "no payment options found for user", zap.String(logger.CLIENT_REQUEST_ID, clientMetaData.GetClientReqId()), zap.String(logger.TO_ACTOR_ID, clientMetaData.GetTargetActorId()), zap.String(logger.UI_ENTRY_POINT, req.GetTransactionUiEntryPoint().String()))
	}

	topSection := s.buildTopSection(ctx, req.GetAmount(), typesPb.GetFromBeMoney(collectedData.SavingsAccountBalance()), req.GetTransactionUiEntryPoint(), collectedData)
	title := s.buildTitle(ctx, conf, collectedData, req.GetTransactionUiEntryPoint())
	additionalTextBelowCta := s.buildAdditionalTextBelowCtaComponent(ctx, collectedData, req.GetTransactionUiEntryPoint())

	return &txnPb.GetPaymentOptionsResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Title:                  title,
		PaymentOptions:         paymentOptions,
		Footer:                 getAddFundsFooter(getFooterLogoUrl(vendor, paymentOptions)),
		BackgroundColour:       widgetPb.GetBlockBackgroundColour(bottomSheetBgColor),
		OrchestrationMetadata:  req.GetOrchestrationMetadata(),
		DropOffScreen:          dropOffScreen,
		TopSection:             topSection,
		AdditionalTextBelowCta: additionalTextBelowCta,
	}, nil
}

// sortPaymentOptionsInTheOrderForDisplay sorts the payment options in a pre-defined order.
func sortPaymentOptionsInTheOrderForDisplay(paymentOptions []*txnPb.GetPaymentOptionsResponse_PaymentOption, paymentOptionSortingOrder []txnPb.PaymentOptionType) []*txnPb.GetPaymentOptionsResponse_PaymentOption {
	paymentOptionsMap := make(map[txnPb.PaymentOptionType]*txnPb.GetPaymentOptionsResponse_PaymentOption)
	// first we map the payment option type to the payment option
	for _, paymentOption := range paymentOptions {
		paymentOptionsMap[paymentOption.GetPaymentOptionType()] = paymentOption
	}
	sortedOptions := make([]*txnPb.GetPaymentOptionsResponse_PaymentOption, 0)
	// secondly we create a second list in the order of the sorted payment option type
	for _, paymentOptionType := range paymentOptionSortingOrder {
		paymentOptionForType, ok := paymentOptionsMap[paymentOptionType]
		if !ok {
			continue
		}
		sortedOptions = append(sortedOptions, paymentOptionForType)
	}
	return sortedOptions
}

func (s *Service) buildDropPaymentOptionsDropOffScreen(actorId string, uiEntryPoint timelinePb.TransactionUIEntryPoint, collectedData *dataCollector.CollectedData) (*deeplink.Deeplink, error) {
	tier, ok := helper.TransactionUIEntryPointToExternalTierMap[uiEntryPoint]
	if !ok {
		// no drop off screen for non Tiering entry points
		return nil, nil
	}

	// disable drop off screen for trial users
	if collectedData.TrialsResponse().GetIsEligibleForTrial() &&
		collectedData.TrialsResponse().GetEligibleTrialTier() == helper.TransactionUIEntryPointToExternalTierMap[uiEntryPoint] {
		return nil, nil
	}

	return s.getTieringDropOffForPaymentOptions(actorId, tier, collectedData)
}

func (s *Service) getTieringDropOffForPaymentOptions(actorId string, tier tieringExternalPb.Tier, collectedData *dataCollector.CollectedData) (*deeplink.Deeplink, error) {
	bottomSheet, err := helper.GetDropOffBottomSheet(&tiering.GetDropOffBottomSheetRequest{
		ActorId:            actorId,
		TieringPitchMethod: tiering.GetDropOffBottomSheetRequest_ADD_FUNDS,
		TierToPitch:        tier,
	}, collectedData.TieringEssentials().GetTierCriteriaMinValuesMap(), collectedData.TieringEssentials().GetIsUSStocksAccountActive())
	if err != nil {
		return nil, fmt.Errorf("error getting drop off bottom sheet: %w", err)
	}

	return bottomSheet.GetDeeplink(), nil
}

func (s *Service) buildPaymentOptions(ctx context.Context, req *txnPb.GetPaymentOptionsRequest, collectedData *dataCollector.CollectedData,
	optionsDisplay *config.OptionsDisplay) ([]*txnPb.GetPaymentOptionsResponse_PaymentOption, error) {
	var paymentOptions []*txnPb.GetPaymentOptionsResponse_PaymentOption
	for _, section := range optionsDisplay.SectionsToDisplay {
		switch section {
		case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP:
			isAmountInAllowedThresholdForNonCoolOff := req.GetAmount().GetUnits() <= s.conf.PaymentOptionsConfig().TpapOptionsConfig().MaxAmountAllowed()
			tpapOption, getTpapOptionErr := tpap.GetTpapOption(ctx, s.conf, req.GetAmount(), collectedData,
				buildTpapArgs(collectedData, optionsDisplay, isAmountInAllowedThresholdForNonCoolOff, req.GetTransactionUiEntryPoint()), req.GetTransactionUiEntryPoint())
			if getTpapOptionErr != nil {
				return nil, fmt.Errorf("error fetching tpap option: %w", getTpapOptionErr)
			}
			paymentOptions = append(paymentOptions, tpapOption)
		case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT:
			intentOption, getIntentOptionErr := intent.GetIntentOption(ctx, s.conf, req.GetAmount(), collectedData, buildIntentArgs(collectedData, optionsDisplay), req.GetTransactionUiEntryPoint())
			if getIntentOptionErr != nil {
				return nil, fmt.Errorf("error fetching intent option: %w", getIntentOptionErr)
			}
			paymentOptions = append(paymentOptions, intentOption)
		case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT:
			collectOption, getCollectOptionErr := collect.GetCollectOption(ctx, s.conf, req.GetAmount(), collectedData, buildCollectArgs(collectedData, optionsDisplay), req.GetTransactionUiEntryPoint())
			if getCollectOptionErr != nil {
				return nil, fmt.Errorf("error fetching collect option: %w", getCollectOptionErr)
			}
			paymentOptions = append(paymentOptions, collectOption)
		case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NEFT_IMPS:
			neftImpsOption, getNeftImpsOptionErr := neftImps.GetNeftImpsOption(ctx, s.conf, req.GetAmount(), collectedData, buildNeftImpsArgs(collectedData, optionsDisplay), req.GetTransactionUiEntryPoint())
			if getNeftImpsOptionErr != nil {
				return nil, fmt.Errorf("error fetching neft imps option: %w", getNeftImpsOptionErr)
			}
			paymentOptions = append(paymentOptions, neftImpsOption)
		}
	}
	return paymentOptions, nil
}

// getFooterLogoUrl returns the URL for the footer logo based on the vendor and payment options.
func getFooterLogoUrl(vendor fePb.Vendor, paymentOptions []*txnPb.GetPaymentOptionsResponse_PaymentOption) string {
	// For Razorpay vendor, no logo needed
	if vendor == fePb.Vendor_RAZORPAY {
		return ""
	}

	for _, option := range paymentOptions {
		switch option.GetPaymentOptionType() {
		case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP,
			txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT,
			txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT:
			// For UPI related payment options, show ` Powered by BHIM UPI FEDERAL`
			return footerLogosUrl
		case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_CARD,
			txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NEFT_IMPS,
			txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NETBANKING:
			// For these options, show only `powered by FEDERAL`
			return footerLogosUrlFederal
		}
	}
	return ""
}

func getAddFundsFooter(footerLogosUrl string) *uiPb.IconTextComponent {
	// Don't show footer if no logo URL
	if footerLogosUrl == "" {
		return nil
	}

	return &uiPb.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(footerTitleText, footerTextFontColor, commontypes.FontStyle_SUBTITLE_XS),
		},
		RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(footerLogosUrl, footerLogosImgHeight, footerLogosImgWidth),
		RightImgTxtPadding: footerRigthImgTxtPadding,
	}
}

func buildTpapArgs(_ *dataCollector.CollectedData, optionsDisplay *config.OptionsDisplay,
	isAmountInAllowedThresholdForNonCoolOff bool, entryPoint timelinePb.TransactionUIEntryPoint) *tpap.Args {
	var args []func(*tpap.Args)
	// show tag if all accounts are unavailable
	if !isAmountInAllowedThresholdForNonCoolOff {
		args = append(args, tpap.WithToShowTag())
	}
	if lo.Contains(optionsDisplay.SectionsToExpand, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP) {
		args = append(args, tpap.WithExpandCollapsibleState(txnPb.ExpandCollapseState_EXPAND_COLLAPSE_STATE_EXPANDED))
	}
	if lo.Contains(optionsDisplay.UnavailableSections, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP) {
		args = append(args,
			tpap.WithExpandCollapsibleState(txnPb.ExpandCollapseState_EXPAND_COLLAPSE_STATE_COLLAPSED),
			tpap.WithPaymentOptionState(txnPb.AvailabilityState_AVAILABILITY_STATE_UNAVAILABLE))
	} else {
		args = append(args, tpap.WithPaymentOptionState(txnPb.AvailabilityState_AVAILABILITY_STATE_AVAILABLE))
	}
	if entryPoint != timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_AA_SALARY {
		args = append(args, tpap.WithToShowAddMoreAccounts())
	}
	return tpap.NewTpapArgs(args...)
}

func buildIntentArgs(_ *dataCollector.CollectedData, optionsDisplay *config.OptionsDisplay) *intent.Args {
	var args []func(*intent.Args)
	// for now, intent option is always collapsible
	args = append(args, intent.WithIsCollapsible())
	if lo.Contains(optionsDisplay.SectionsToExpand, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT) {
		args = append(args, intent.WithExpandCollapsibleState(txnPb.ExpandCollapseState_EXPAND_COLLAPSE_STATE_EXPANDED))
	}
	if lo.Contains(optionsDisplay.UnavailableSections, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT) {
		args = append(args, intent.WithExpandCollapsibleState(txnPb.ExpandCollapseState_EXPAND_COLLAPSE_STATE_COLLAPSED),
			intent.WithPaymentOptionState(txnPb.AvailabilityState_AVAILABILITY_STATE_UNAVAILABLE))
	} else {
		args = append(args, intent.WithPaymentOptionState(txnPb.AvailabilityState_AVAILABILITY_STATE_AVAILABLE))
	}
	return intent.NewIntentArgs(args...)
}

func buildCollectArgs(_ *dataCollector.CollectedData, optionsDisplay *config.OptionsDisplay) *collect.Args {
	var args []func(*collect.Args)
	// for now, collect option is always collapsible
	args = append(args, collect.WithIsCollapsible())
	if lo.Contains(optionsDisplay.SectionsToExpand, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT) {
		args = append(args, collect.WithExpandCollapsibleState(txnPb.ExpandCollapseState_EXPAND_COLLAPSE_STATE_EXPANDED))
	}
	if lo.Contains(optionsDisplay.UnavailableSections, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT) {
		args = append(args, collect.WithExpandCollapsibleState(txnPb.ExpandCollapseState_EXPAND_COLLAPSE_STATE_COLLAPSED),
			collect.WithPaymentOptionState(txnPb.AvailabilityState_AVAILABILITY_STATE_UNAVAILABLE))
	} else {
		args = append(args, collect.WithPaymentOptionState(txnPb.AvailabilityState_AVAILABILITY_STATE_AVAILABLE))
	}
	return collect.NewCollectArgs(args...)
}

func buildNeftImpsArgs(_ *dataCollector.CollectedData, optionsDisplay *config.OptionsDisplay) *neftImps.Args {
	var args []func(*neftImps.Args)
	// for now, neft/imps option is always collapsible
	args = append(args, neftImps.WithIsCollapsible())
	if lo.Contains(optionsDisplay.SectionsToExpand, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NEFT_IMPS) {
		args = append(args, neftImps.WithExpandCollapsibleState(txnPb.ExpandCollapseState_EXPAND_COLLAPSE_STATE_EXPANDED))
	}
	if lo.Contains(optionsDisplay.UnavailableSections, txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NEFT_IMPS) {
		args = append(args, neftImps.WithExpandCollapsibleState(txnPb.ExpandCollapseState_EXPAND_COLLAPSE_STATE_COLLAPSED),
			neftImps.WithPaymentOptionState(txnPb.AvailabilityState_AVAILABILITY_STATE_UNAVAILABLE))
	} else {
		args = append(args, neftImps.WithPaymentOptionState(txnPb.AvailabilityState_AVAILABILITY_STATE_AVAILABLE))
	}
	return neftImps.NewNeftImpsArgs(args...)
}

// buildTieringPitchSection creates a vertical layout section with tier-specific banner
func (s *Service) buildTieringPitchSection(ctx context.Context, uiEntryPoint timelinePb.TransactionUIEntryPoint) *components.Component {
	var bannerUrl string
	switch uiEntryPoint {
	case timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PLUS:
		bannerUrl = s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().TierPlusTopSectionUrl(ctx)
	case timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_INFINITE:
		bannerUrl = s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().TierInfiniteTopSectionUrl(ctx)
	case timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PRIME:
		bannerUrl = s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().TierPrimeTopSectionUrl(ctx)
	default:
		// Return nil if no banner URL for this entry point
		return nil
	}

	return &components.Component{
		Content: ui.GetAnyWithoutError(commontypes.GetVisualElementImageFromUrl(bannerUrl)),
	}
}

// buildTitle returns the title of the payment options screen
func (s *Service) buildTitle(ctx context.Context, conf *genconf.PaymentOptionDisplayInfo, collectedData *dataCollector.CollectedData, transactionUiEntryPoint timelinePb.TransactionUIEntryPoint) *commontypes.Text {
	switch {
	case collectedData.IsTieringPitchInPaymentOptionsEnabled() && helper.IsAllPlansEntryPoint(transactionUiEntryPoint):
		return commontypes.GetTextFromStringFontColourFontStyle(s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().UpgradeScreenTitle(ctx), s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().PaymentOptionsTitleFadedFontColor(ctx), commontypes.FontStyle_HEADLINE_S)
	default:
		return commontypes.GetTextFromStringFontColourFontStyle(conf.ScreenTitle(), paymentOptionsTitleFontColor, commontypes.FontStyle_HEADLINE_M)
	}
}

// buildTopSection creates a vertical layout section with payment details
func (s *Service) buildTopSection(ctx context.Context, requestAmount *typesPb.Money, savingsBalance *typesPb.Money, transactionUiEntryPoint timelinePb.TransactionUIEntryPoint, collectedData *dataCollector.CollectedData) *sections.Section {

	switch {
	case collectedData.IsTieringPitchInPaymentOptionsEnabled() && helper.IsAllPlansEntryPoint(transactionUiEntryPoint):
		// This section is for tiering pitch on payment options screen
		// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/%F0%9F%9B%A0%EF%B8%8F-Central-Growth---Workfile?node-id=18361-52460&t=McMzG5DQhzZKEMj6-0
		return &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					Components: []*components.Component{
						{
							Content: ui.GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_XL)),
						},
						s.getSelfTransferComponent(ctx),
						{
							Content: ui.GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_M)),
						},
						getAmountComponent(requestAmount),
						s.getBalanceComponent(ctx, savingsBalance),
						{
							Content: ui.GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_M)),
						},
						s.buildTieringPitchSection(ctx, transactionUiEntryPoint),
						{
							Content: ui.GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_M)),
						},
					},
				},
			},
		}
	default:
		return nil
	}
}

// getSelfTransferComponent creates the "Self transfer" component for the bottom section
func (s *Service) getSelfTransferComponent(ctx context.Context) *components.Component {
	iconTextComponent := &uiPb.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().FederalIconLogo(ctx), 20, 20),
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().SelfTransferText(ctx), "#333333", commontypes.FontStyle_HEADLINE_M),
		},
	}

	return &components.Component{
		Content: ui.GetAnyWithoutError(iconTextComponent),
	}
}

// getAmountComponent creates the amount component for the bottom section
func getAmountComponent(requestAmount *typesPb.Money) *components.Component {
	var requestAmountText string
	if requestAmount != nil {
		requestAmountText = money.GetDisplayString(requestAmount.GetBeMoney(), 0, false, false, money.IndianNumberSystem)
	} else {
		requestAmountText = "0"
	}

	iconTextComponent := &uiPb.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle("₹", "#B2B5B9", commontypes.FontStyle_NUMBERS_3),
			commontypes.GetTextFromStringFontColourFontStyle(requestAmountText, "#313234", commontypes.FontStyle_HEADLINE_4XL),
		},
	}

	return &components.Component{
		Content: ui.GetAnyWithoutError(iconTextComponent),
	}
}

// getBalanceComponent creates the savings balance component for the bottom section
func (s *Service) getBalanceComponent(ctx context.Context, savingsBalance *typesPb.Money) *components.Component {
	var balanceText string
	if savingsBalance != nil {
		balanceText = fmt.Sprintf(s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().SavingsBalanceString(ctx), money.GetDisplayString(savingsBalance.GetBeMoney(), 0, false, false, money.IndianNumberSystem))
	} else {
		balanceText = fmt.Sprintf(s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().SavingsBalanceString(ctx), "0")
	}

	iconTextComponent := &uiPb.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().SavingsBalanceIconUrl(ctx), 16, 16),
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(balanceText, "#929599", commontypes.FontStyle_SUBTITLE_XS),
		},
	}

	return &components.Component{
		Content: ui.GetAnyWithoutError(iconTextComponent),
	}
}

func (s *Service) buildAdditionalTextBelowCtaComponent(ctx context.Context, collectedData *dataCollector.CollectedData, transactionUiEntryPoint timelinePb.TransactionUIEntryPoint) *uiPb.IconTextComponent {
	switch {
	case collectedData.IsTieringPitchInPaymentOptionsEnabled() && helper.IsAllPlansEntryPoint(transactionUiEntryPoint):
		return &uiPb.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().AddFundsAdditionalTextIconURL(ctx), 16, 16),
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(s.conf.PaymentOptionsConfig().TieringPitchInPaymentOptionsParams().AddFundsAdditionalText(ctx), "#007A56", commontypes.FontStyle_SUBTITLE_XS),
			},
		}
	default:
		return nil
	}
}
