package transaction

import (
	"context"
	"fmt"

	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	durationPb "google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	tieringPkg "github.com/epifi/gamma/pkg/tiering"

	categorizerPb "github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/frontend/timeline"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	pay2 "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	tieringPb "github.com/epifi/gamma/api/tiering"
	beTieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	typePb "github.com/epifi/gamma/api/typesv2"
	types "github.com/epifi/gamma/api/typesv2"
	saClosureDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/sa_closure"
	salaryDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	infoPb "github.com/epifi/gamma/api/typesv2/info"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	errors2 "github.com/epifi/gamma/pkg/frontend/errors"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/pay/payerrorcode"
)

const (
	PAYMENT_FAILED                   = "Payment Failed"
	SUCCESS_RESPONSE_CODE            = "00"
	DEVICE_FINGERPRINT_MISMATCH      = "Device FingerPrint Mismatch"
	DEVICE_FINGERPRINT_MISMATCH_CODE = "U66"
)

const (
	aaSalarySuccessTitle   = "Transfer successful! %s cashback activated"
	aaSalarySuccessDesc    = "You will now earn %s cashback for all UPI or Debit Card spends via Fi"
	aaSalarySuccessIconUrl = "https://epifi-icons.pointz.in/tiering/aasalary/transfer_success.png"
	aaSalarySuccessCtaText = "See overall benefits"

	aaSalaryFailureTitle                  = "Transfer could not be completed"
	aaSalaryFailureDesc                   = "Any money debited from your account will be refunded in 48 hours. You can wait for sometime and try the transfer again."
	aaSalaryFailureIconUrl                = "https://epifi-icons.pointz.in/tiering/aasalary/transfer_failed.png"
	aaSalaryFailureCtaText                = "Ok, got it"
	pendingChargesMoneyAddedScreenCtaText = "Ok, got it"
	pendingChargesMoneyAddedTitle         = "Money added successfully"
	pendingChargesMoneyAddedTitleColor    = "#313234"
	pendingChargesMoneyAddedTitleFont     = commontypes.FontStyle_HEADLINE_L
	pendingChargesMoneyAddedIconUrl       = "https://epifi-icons.s3.ap-south-1.amazonaws.com/savingsAccountClosure/tickMark.png"
	pendingChargesMoneyAddedIconUrlWidth  = 200
	pendingChargesMoneyAddedIconUrlHeight = 200
	pendingChargesMoneyAddedSubTitle      = "Maintain this amount in your savings account so that pending charges can be auto-debited by the end of today"
	pendingChargesMoneyAddedSubTitleFont  = commontypes.FontStyle_BODY_S
	pendingChargesMoneyAddedSubTitleColor = "#6A6D70"
	pendingChargesScreenBgColor           = "#FFFFFF"
)

var (
	StatusPaymentFailed rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(transaction.GetOrderStatusResponse_PAYMENT_FAILED), PAYMENT_FAILED)
	}

	statusDeviceFingerPrintMismatch rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(transaction.GetOrderStatusResponse_DEVICE_FINGERPRINT_MISMATCH), DEVICE_FINGERPRINT_MISMATCH)
	}

	beStatusToFeStatusForAddFunds = map[orderPb.OrderStatus]fePayPb.OrderStatus{
		orderPb.OrderStatus_CREATED:                    fePayPb.OrderStatus_PAYMENT_IN_PROGRESS,
		orderPb.OrderStatus_IN_PAYMENT:                 fePayPb.OrderStatus_PAYMENT_IN_PROGRESS,
		orderPb.OrderStatus_PAID:                       fePayPb.OrderStatus_PAYMENT_IN_PROGRESS,
		orderPb.OrderStatus_PAYMENT_FAILED:             fePayPb.OrderStatus_PAYMENT_FAILED,
		orderPb.OrderStatus_IN_SETTLEMENT:              fePayPb.OrderStatus_PAYMENT_IN_PROGRESS,
		orderPb.OrderStatus_SETTLEMENT_FAILED:          fePayPb.OrderStatus_PAYMENT_FAILED,
		orderPb.OrderStatus_SETTLED:                    fePayPb.OrderStatus_PAYMENT_SUCCESS,
		orderPb.OrderStatus_COLLECT_REGISTERED:         fePayPb.OrderStatus_COLLECT_REGISTERED,
		orderPb.OrderStatus_COLLECT_IN_PROGRESS:        fePayPb.OrderStatus_COLLECT_REGISTRATION_IN_PROGRESS,
		orderPb.OrderStatus_COLLECT_FAILED:             fePayPb.OrderStatus_COLLECT_REGISTRATION_FAILED,
		orderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYER: fePayPb.OrderStatus_COLLECT_DECLINED,
		orderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYEE: fePayPb.OrderStatus_COLLECT_CANCELLED,
		orderPb.OrderStatus_PAYMENT_REVERSED:           fePayPb.OrderStatus_PAYMENT_FAILED,
	}

	recurringPaymentStatusToInternalOrderStatus = map[enums.OrderStatus]orderPb.OrderStatus{
		enums.OrderStatus_ORDER_STATUS_PAID:        orderPb.OrderStatus_PAID,
		enums.OrderStatus_ORDER_STATUS_FAILED:      orderPb.OrderStatus_PAYMENT_FAILED,
		enums.OrderStatus_ORDER_STATUS_IN_PROGRESS: orderPb.OrderStatus_IN_PAYMENT,
	}

	txnDetailedStatusRawCodeToStatusMap = map[string]rpc.StatusFactory{
		DEVICE_FINGERPRINT_MISMATCH_CODE: statusDeviceFingerPrintMismatch,
	}

	ErrNotAddFundsOrder = errors.New("order doesn't belong to add funds")

	// TODO ADD pg tags
	isOrderTagEligibleForPg = []orderPb.OrderTag{orderPb.OrderTag_RAZORPAY}
)

// GetOrderStatus
/**
1. Get order details from the order service
2. Perform validations against the order
	a. Does the actor have permission to access the order?
3. Translate order status to a client presentable status
4. This also takes care of the case that this order is for a recurring payment registration.
In that case the order id provided is the recurring payment id and based on the recurring payment
state, a corresponding order status is required.
**/
// nolint:funlen
func (s *Service) GetOrderStatus(ctx context.Context, req *transaction.GetOrderStatusRequest) (*transaction.GetOrderStatusResponse, error) {
	res := &transaction.GetOrderStatusResponse{
		RespHeader: &header.ResponseHeader{},
		RetryTimer: durationPb.New(s.conf.PayParams().RetryTimerDurationGetOrderStatus()),
	}
	res.Status = &rpc.Status{}
	actorId := req.GetReq().GetAuth().GetActorId()
	var (
		orderWithTxnEntry     *orderPb.OrderWithTransactions
		orderEntry            *orderPb.Order
		txn                   *paymentPb.Transaction
		orderStatus           *rpc.Status
		errorView             *transaction.ErrorView
		feStatus              fePayPb.OrderStatus
		ok                    bool
		isAddFunds            bool
		txnMetadata           = &pay2.ClientIdentificationTxnMetaData{}
		postPaymentNextAction *deeplink.Deeplink
	)
	if len(req.GetOrchestrationMetadata()) != 0 {
		decryptRes, decryptErr := s.payV1Client.GetPlainData(ctx, &pay2.GetPlainDataRequest{
			SignedData: req.GetOrchestrationMetadata(),
		})
		if te := epifigrpc.RPCError(decryptRes, decryptErr); te != nil {
			logger.Error(ctx, "error in decrypting orch data", zap.Error(te), zap.String(logger.ORDER_ID, req.GetOrderId()))
			res.Status = rpc.StatusInternal()
			res.RespHeader.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
			return res, nil
		}
		// attempting to unmarshal orch data if it is not null
		unmErr := proto.Unmarshal(decryptRes.GetPlainData(), txnMetadata)
		if unmErr != nil {
			logger.Error(ctx, "error in unmarshalling orch data", zap.Error(unmErr))
			res.Status = rpc.StatusInternal()
			res.RespHeader.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
			return res, nil
		}
		if len(txnMetadata.GetTargetActorId()) != 0 {
			actorId = txnMetadata.GetTargetActorId()
		}
	}

	beGetCategoriesReq := &categorizerPb.GetCategoriesRequest{
		ActorId: actorId,
		Id: &categorizerPb.GetCategoriesRequest_OrderId{
			OrderId: req.GetOrderId(),
		},
	}

	// embedding ownership into the context so that the db resolution can be done correctly since
	// order is an entity-segregated entity.`
	ctx = epificontext.WithOwnership(ctx, txnMetadata.GetEntityOwnership())

	orderWithTxnEntry, orderStatus = s.beGetOrderWithTxn(ctx, req.GetOrderId())
	switch {
	case orderStatus.IsRecordNotFound():
		// adding a check to see if the order is actually a recurring payment registration order
		// if it is, we need to fetch the status from the recurring payment service
		recurringPaymentRes, err := s.recurringPaymentPgClient.GetOrderStatus(ctx, &paymentgateway.GetOrderStatusRequest{
			RecurringPaymentId: req.GetOrderId(),
			ClientRequestId:    txnMetadata.GetClientReqId(),
		})
		if te := epifigrpc.RPCError(recurringPaymentRes, err); te != nil {
			logger.Error(ctx, "error in fetching order status from recurring payment service", zap.Error(te))
			res.Status = rpc.StatusInternal()
			res.RespHeader.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
			return res, nil
		}
		orderStat := recurringPaymentStatusToInternalOrderStatus[recurringPaymentRes.GetOrderStatus()]
		feStatus = beStatusToFeStatus[orderStat]
		res.Order = &fePayPb.OrderEvent{
			Id:                 req.GetOrderId(),
			Status:             feStatus,
			OrderStatusIconUrl: getOrderStatusUrl(feStatus, s.orderStatusIconUrl),
		}
		if isStatusTerminal(feStatus) {
			res.NextAction = txnMetadata.GetDomainOrderData().GetNextAction()
		}
		res.Status = rpc.StatusOk()
		res.RespHeader.Status = rpc.StatusOk()
		return res, nil
	case orderStatus != nil:
		logger.Error(ctx, "error in fetching order with txn from be", zap.String(logger.STATUS_CODE, orderStatus.String()))
		res.Status = orderStatus
		res.ErrorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
		res.RespHeader.Status = orderStatus
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	}

	orderEntry = orderWithTxnEntry.GetOrder()

	if s.isPermissionDeniedForOrder(orderEntry, actorId) {
		logger.Error(ctx, fmt.Sprintf("Permission denied for orderEntry id %v to actor id %v", orderEntry.GetId(), actorId))
		res.Status = rpc.StatusPermissionDenied()
		res.ErrorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
		res.RespHeader.Status = rpc.StatusPermissionDenied()
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	}

	logger.Info(ctx, "Order",
		zap.String(logger.ORDER_ID, orderEntry.Id),
		zap.String("status", orderEntry.Status.String()))

	if isOrderEligibleForPg(orderWithTxnEntry) {
		orderWithTxnEntry, orderStatus = s.handlePgOrder(ctx, req.GetOrderId())
	}

	if len(orderWithTxnEntry.GetTransactions()) > 0 {
		txn = orderWithTxnEntry.GetTransactions()[0]
	}

	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := req.GetReq().GetAppVersionCode()
	addFundsTxn, status, nextAction, err := s.handleAddFundsOrder(ctx, orderWithTxnEntry, actorId, appPlatform, appVersion)
	switch {
	case err != nil && !errors.Is(err, ErrNotAddFundsOrder):
		logger.Error(ctx, "error handling add funds order", zap.String(logger.ORDER_ID, orderEntry.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.RespHeader.Status = rpc.StatusInternal()
		res.ErrorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	case err == nil:
		isAddFunds = true
		txn = addFundsTxn
		feStatus = status
		res.NextAction = nextAction
	default:
		feStatus, ok = beStatusToFeStatus[orderEntry.GetStatus()]
		if !ok {
			res.RespHeader.Status = rpc.StatusUnknown()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
			res.Status = rpc.StatusUnknown()
			return res, nil
		}

	}

	categoriesResp, err := s.categorizerClient.GetCategories(ctx, beGetCategoriesReq)
	if err != nil {
		logger.Error(ctx, "error in getting category in receipt page", zap.Error(err), zap.String(logger.ORDER_ID, req.GetOrderId()))
	} else {
		for _, category := range categoriesResp.GetDisplayCategory() {
			if txnCategory, ok := DisplayEnumToCategoryDetailMap[category]; ok {
				res.TxnCategories = append(res.TxnCategories, txnCategory)
			} else {
				logger.Error(ctx, "missing category in DisplayEnumToCategoryDetailMap", zap.String(logger.TXN_CATEGORY, category.String()), zap.String(logger.ORDER_ID, req.GetOrderId()))
			}
		}
		res.IsCategoryEditable = categoriesResp.GetCanUpdateCategory()
	}

	res.Order = &fePayPb.OrderEvent{
		Id:     orderEntry.Id,
		Status: feStatus,
		Amount: &typePb.Money{
			CurrencyCode: orderEntry.Amount.CurrencyCode,
			Units:        orderEntry.Amount.Units,
			Decimals:     orderEntry.Amount.Nanos,
		},
		Remarks:            txn.GetRemarks(),
		LastUpdatedAt:      orderEntry.UpdatedAt,
		Type:               s.getOrderEventType(orderEntry),
		OrderStatusIconUrl: getOrderStatusUrl(feStatus, s.orderStatusIconUrl),
	}

	// Get the utr of first transaction
	if len(orderWithTxnEntry.GetTransactions()) > 0 {
		res.Order.TransactionId = orderWithTxnEntry.GetTransactions()[0].GetUtrForDisplay(pay.TranIdDelimiter)
	}

	// for intent based payment we are using P2P fund transfer as workflow
	if isOrderInTerminal(orderEntry.GetStatus()) &&
		orderEntry.GetWorkflow() == orderPb.OrderWorkflow_P2P_FUND_TRANSFER {
		paymentDetails, err := pay.GetPaymentDetailsFromPayload(orderEntry)
		if err != nil {
			logger.Error(ctx, "error fetching payment details", zap.Error(err))
			res.RespHeader.Status = rpc.StatusInternal()
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
			return res, nil
		}
		res.IntentResponseParams = &transaction.IntentResponseParams{
			TxnId:        paymentDetails.GetReqId(),
			TrTxnRef:     paymentDetails.GetMerchantRefId(),
			ResponseCode: SUCCESS_RESPONSE_CODE,
		}
		if txn != nil {
			res.IntentResponseParams.ApprovalRefNumber = txn.GetUtr()
		}
	}

	if isOrderFailed(orderEntry.GetStatus()) {
		txnDetailedStatus := txn.GetTxnDetailedStatusForErrorView()

		switch {
		case txnDetailedStatus == nil:
			errorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
		case txnDetailedStatus.ErrorCategory == paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR:
			errorView = proto.Clone(DefaultErrorViewOrderStatusCheck).(*transaction.ErrorView)
		case actorId == orderEntry.GetFromActorId():
			prioritisedErrCode := payerrorcode.GetPrioritisedErrorCode(txnDetailedStatus.GetStatusCodePayer(), txnDetailedStatus.GetStatusCodePayee())
			errorView = GetErrorView(prioritisedErrCode)
		default:
			prioritisedErrCode := payerrorcode.GetPrioritisedErrorCode(txnDetailedStatus.GetStatusCodePayer(), txnDetailedStatus.GetStatusCodePayee())
			errorView = GetErrorView(prioritisedErrCode)
		}

		if res.IntentResponseParams != nil && txnDetailedStatus != nil {
			res.IntentResponseParams.ResponseCode = txnDetailedStatus.GetRawStatusCode()
		}
		if isAddFunds {
			// add funds is the additions of funds to Fi account from another account
			// of same user. We want to show always show payer level error codes in case
			// of add funds because in cases like user enters invalid pin while authorising
			// payment, then it's good to show the error to user for the same on Fi screen .
			errorView = GetErrorView(txnDetailedStatus.GetStatusCodePayer())
			errorView.Actions = []*timeline.TimelineAction{
				RetryActionButton,
				ChangeMethodActionButton,
			}
		}

		// in case or collect order, we do not want to show retry action to user as order will be already marked failed
		if orderEntry.IsCollectRequest() {
			errorView.Actions = filterTimelineActions(errorView.Actions, timeline.TimelineAction_RETRY)
		}

		if errorView.Actions, err = s.filterAddFundsOptionForTpapAccount(ctx, txn.GetPiFrom(), actorId, errorView.GetActions()); err != nil {
			logger.Error(ctx, "error while filtering add funds option for a tpap account",
				zap.String(logger.PI_ID, txn.GetPiFrom()),
				zap.String(logger.ACTOR_ID_V2, actorId),
				zap.Error(err))

			res.Status = rpc.StatusInternal()
			res.RespHeader.Status = rpc.StatusInternal()
			return res, nil
		}

		res.ErrorView = errorView
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, errorView)

		// setting post payment next action in case txn is failed
		postPaymentNextAction, err = s.getPostPaymentNextAction(ctx, orderEntry, txn, actorId, feStatus, errorView, res.GetOrder().GetTransactionId(), orderWithTxnEntry)
		if err != nil {
			logger.Error(ctx, "failed to get next action for fund transfer", zap.String(logger.ORDER_ID, orderEntry.GetId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			res.RespHeader.Status = rpc.StatusInternal()
			return res, nil
		}
		if postPaymentNextAction != nil && res.NextAction == nil && !isAddFunds {
			res.NextAction = postPaymentNextAction
		}
		// For some special cases we want to send custom status codes to the client - eg. Device FingerPrint Mismatch
		txnRawStatus, ok := txnDetailedStatusRawCodeToStatusMap[txnDetailedStatus.GetRawStatusCode()]
		if ok {
			res.RespHeader.Status = txnRawStatus()
			res.Status = txnRawStatus()
			return res, nil
		}

		res.RespHeader.Status = statusPaymentFailed()
		res.Status = StatusPaymentFailed()
		return res, nil
	}
	// setting post payment next action in case payment is successful
	postPaymentNextAction, err = s.getPostPaymentNextAction(ctx, orderEntry, txn, actorId, feStatus, nil, res.GetOrder().GetTransactionId(), orderWithTxnEntry)
	if err != nil {
		logger.Error(ctx, "failed to get next action for fund transfer", zap.String(logger.ORDER_ID, orderEntry.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}
	if postPaymentNextAction != nil && res.NextAction == nil && !isAddFunds {
		res.NextAction = postPaymentNextAction
	}

	res.RespHeader.Status = rpc.StatusOk()
	res.Status = rpc.StatusOk()
	return res, nil
}

func isStatusTerminal(status fePayPb.OrderStatus) bool {
	return status == fePayPb.OrderStatus_PAYMENT_SUCCESS || status == fePayPb.OrderStatus_PAYMENT_FAILED
}

// getOrderEventType returns order event type based on the workflow
// this type used to identify an order event in the timeline and render message accordingly
func (s *Service) getOrderEventType(orderEntry *orderPb.Order) fePayPb.OrderEvent_Type {
	switch {
	case orderEntry.GetWorkflow() == orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		orderEntry.GetWorkflow() == orderPb.OrderWorkflow_ADD_FUNDS, orderEntry.GetWorkflow() == orderPb.OrderWorkflow_SECURED_CC_FEDERAL_FD_FUND_TRANSFER:
		return fePayPb.OrderEvent_PAY
	case orderEntry.IsCollectRequest():
		return fePayPb.OrderEvent_COLLECT
	default:
		return fePayPb.OrderEvent_TYPE_UNSPECIFIED
	}
}

// isPermissionDeniedForOrder validates order status request by checking if requesting actor has
// access to the order or not
func (s *Service) isPermissionDeniedForOrder(orderEntry *orderPb.Order, currentActorId string) bool {
	switch orderEntry.GetWorkflow() {
	case orderPb.OrderWorkflow_URN_TRANSFER, orderPb.OrderWorkflow_ADD_FUNDS, orderPb.OrderWorkflow_ADD_FUNDS_COLLECT:
		return orderEntry.GetToActorId() != currentActorId
	case orderPb.OrderWorkflow_P2P_COLLECT_SHORT_CIRCUIT, orderPb.OrderWorkflow_P2P_COLLECT:
		return orderEntry.GetFromActorId() != currentActorId && orderEntry.GetToActorId() != currentActorId
	default:
		return orderEntry.GetFromActorId() != currentActorId
	}
}

// isOrderFailed takes the order status and returns true if the order failed
// returns false if the order is in progress/success
// order status denoting order failure - OrderStatus_COLLECT_FAILED, OrderStatus_COLLECT_FAILED
func isOrderFailed(status orderPb.OrderStatus) bool {
	return status == orderPb.OrderStatus_COLLECT_FAILED ||
		status == orderPb.OrderStatus_PAYMENT_FAILED ||
		status == orderPb.OrderStatus_SETTLEMENT_FAILED
}

// isOrderInTerminal takes the order status and returns true if the order is in success/failure
func isOrderInTerminal(status orderPb.OrderStatus) bool {
	return status == orderPb.OrderStatus_PAID || isOrderFailed(status)
}

// getPostPaymentNextAction returns the next action to be taken by the client
// will be populated only for order with ui entry point as ONBOARD_ADD_FUNDS & ONBOARD_ADD_FUNDS_PRE_FUNDING
func (s *Service) getNextAction(ctx context.Context, actorId string, orderEntry *orderPb.Order) (*deeplink.Deeplink, error) {
	nextActionRes, err := s.onboardingClient.GetNextAction(ctx, &onbPb.GetNextActionRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(nextActionRes, err); err != nil {
		logger.Error(ctx, "failed to fetch onboarding progress from onboarding service", zap.Error(err))
		return nil, fmt.Errorf("failed to fetch onboarding progress from onboarding service, err: %v", err)
	}
	// In case of Prefunding we don't need to get next action from tiering, hence doing an early return.
	if orderEntry.GetUiEntryPoint() == orderPb.UIEntryPoint_ONBOARD_ADD_FUNDS_PRE_FUNDING {
		return nextActionRes.GetNextAction(), nil
	}
	nextActionForTiering, getNextActionForTieringErr := s.getNextActionForTiering(ctx, actorId, orderEntry, nextActionRes.GetNextAction())
	if getNextActionForTieringErr != nil {
		logger.Error(ctx, "error getting next action for tiering", zap.Error(getNextActionForTieringErr))
		return nextActionRes.GetNextAction(), nil
	}
	return nextActionForTiering, nil
}

// nolint: funlen
func (s *Service) getNextActionForAaSalary(ctx context.Context, actorId string, orderEntry *orderPb.Order) (*deeplink.Deeplink, error) {
	confParamsResp, confParamsErr := s.tieringClient.GetConfigParams(ctx, &tieringPb.GetConfigParamsRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(confParamsResp, confParamsErr); rpcErr != nil {
		logger.Error(ctx, "error while fetching config params for actor", zap.Error(rpcErr))
	}
	isTieringAllPlansV2Enabled := confParamsResp.GetIsMultipleWaysToEnterTieringEnabledForActor()

	aaSalaryResp, getAaSalaryRespErr := s.salaryProgramClient.GetAaSalaryDetails(ctx, &salaryPb.GetAaSalaryDetailsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(aaSalaryResp, getAaSalaryRespErr); rpcErr != nil {
		logger.Error(ctx, "error fetching aa salary details", zap.Error(rpcErr))
		return nil, fmt.Errorf("error fetching aa salary details: %w", rpcErr)
	}
	salaryBand := aaSalaryResp.GetSalaryBand()
	if salaryBand == salaryEnumsPb.SalaryBand_SALARY_BAND_UNSPECIFIED {
		return nil, nil
	}
	switch orderEntry.GetStatus() {
	case orderPb.OrderStatus_PAID:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_AA_SALARY_PROGRAM_FLOWS_TERMINAL_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryDlPb.AaSalaryProgramFlowsTerminalScreenOptions{
				Title: commontypes.GetTextFromStringFontColourFontStyle(
					fmt.Sprintf(aaSalarySuccessTitle, getPercentageStringForSalaryBand(salaryBand)),
					"#FFFFFF",
					commontypes.FontStyle_HEADLINE_XL,
				),
				Desc: commontypes.GetTextFromStringFontColourFontStyle(
					fmt.Sprintf(aaSalarySuccessDesc, getPercentageStringForSalaryBand(salaryBand)),
					"#929599",
					commontypes.FontStyle_BODY_S),
				StatusBasedIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(aaSalarySuccessIconUrl, 100, 100),
				ProceedCta: &deeplink.Cta{
					Type:         deeplink.Cta_CONTINUE,
					Text:         aaSalarySuccessCtaText,
					DisplayTheme: deeplink.Cta_PRIMARY,
					Deeplink:     tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, isTieringAllPlansV2Enabled),
				},
				BgColor:      widgetPb.GetBlockBackgroundColour("#18191B"),
				ShowConfetti: true,
			}),
		}, nil
	case orderPb.OrderStatus_PAYMENT_FAILED:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_AA_SALARY_PROGRAM_FLOWS_TERMINAL_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryDlPb.AaSalaryProgramFlowsTerminalScreenOptions{
				Title: commontypes.GetTextFromStringFontColourFontStyle(
					aaSalaryFailureTitle,
					"#FFFFFF",
					commontypes.FontStyle_HEADLINE_XL,
				),
				Desc: commontypes.GetTextFromStringFontColourFontStyle(
					aaSalaryFailureDesc,
					"#929599",
					commontypes.FontStyle_BODY_S),
				StatusBasedIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(aaSalaryFailureIconUrl, 100, 100),
				ProceedCta: &deeplink.Cta{
					Type:         deeplink.Cta_CONTINUE,
					Text:         aaSalaryFailureCtaText,
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
				BgColor:      widgetPb.GetBlockBackgroundColour("#18191B"),
				ShowConfetti: false,
			}),
		}, nil
	default:
		return nil, nil
	}
}

func (s *Service) getNextActionForAddFundsUss(orderEntry *orderPb.Order) (*deeplink.Deeplink, error) {
	switch orderEntry.GetStatus() {
	case orderPb.OrderStatus_PAID:
		dl := &deeplink.Deeplink{}
		err := orderEntry.GetPostPaymentDeeplink().UnmarshalTo(dl)
		if err != nil {
			return nil, fmt.Errorf("failed to generate next action deeplink: %w", err)
		}
		return dl, nil
	default:
		return nil, nil
	}
}

func (s *Service) getNextActionForAddFundsPendingChargesAccountClosure(orderEntry *orderPb.Order) (*deeplink.Deeplink, error) {
	switch orderEntry.GetStatus() {
	case orderPb.OrderStatus_PAID:
		screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&saClosureDlPb.SaClosureRequestSubmittedScreenOptions{
			FullScreenInfoView: &infoPb.FullScreenInfoView{
				Image: commontypes.GetVisualElementFromUrlHeightAndWidth(pendingChargesMoneyAddedIconUrl, pendingChargesMoneyAddedIconUrlHeight, pendingChargesMoneyAddedIconUrlWidth),
				Title: commontypes.GetTextFromStringFontColourFontStyle(pendingChargesMoneyAddedTitle, pendingChargesMoneyAddedTitleColor, pendingChargesMoneyAddedTitleFont),
				Body:  commontypes.GetTextFromHtmlStringFontColourFontStyle(pendingChargesMoneyAddedSubTitle, pendingChargesMoneyAddedSubTitleColor, pendingChargesMoneyAddedSubTitleFont),
				Ctas: []*deeplink.Cta{
					{
						Type: deeplink.Cta_CUSTOM,
						Text: pendingChargesMoneyAddedScreenCtaText,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_HOME,
						},
						DisplayTheme: deeplink.Cta_PRIMARY,
					},
				},
				BackgroundColor: pendingChargesScreenBgColor,
			},
		})
		if err != nil {
			return nil, errors.Wrap(err, "failed to get deeplink screen options")
		}

		return &deeplink.Deeplink{
			Screen:          deeplink.Screen_SA_CLOSURE_REQUEST_SUBMITTED_SCREEN,
			ScreenOptionsV2: screenOptionsV2,
		}, nil
	default:
		return nil, nil
	}
}

func getPercentageStringForSalaryBand(band salaryEnumsPb.SalaryBand) string {
	switch band {
	case salaryEnumsPb.SalaryBand_SALARY_BAND_1:
		return "1%"
	case salaryEnumsPb.SalaryBand_SALARY_BAND_2:
		return "2%"
	case salaryEnumsPb.SalaryBand_SALARY_BAND_3:
		return "3%"
	default:
		return "0%"
	}
}

func (s *Service) getNextActionForTiering(ctx context.Context, actorId string, orderEntry *orderPb.Order, nextAction *deeplink.Deeplink) (*deeplink.Deeplink, error) {
	isFeatureEnabled, evaluatorErr := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_ONB_ADD_FUNDS_TIERING_SUCCESS).WithActorId(actorId),
	)
	if evaluatorErr != nil {
		return nil, errors.Wrap(evaluatorErr, "error in feature evaluator")
	}
	if !isFeatureEnabled {
		logger.Info(ctx, "onb add funds upgrade success not enabled")
		return nextAction, nil
	}

	tierUpgradeRes, tierUpgradeErr := s.tieringClient.Upgrade(ctx, &tieringPb.UpgradeRequest{
		ActorId:    actorId,
		Provenance: beTieringEnumPb.Provenance_PROVENANCE_ONB_ADD_FUNDS,
	})
	if rpcErr := epifigrpc.RPCError(tierUpgradeRes, tierUpgradeErr); rpcErr != nil {
		return nil, errors.Wrap(rpcErr, "error upgrading tier for user")
	}
	toTier := tierUpgradeRes.GetToTier()
	// show upgrade success screen only for plus and infinite
	if toTier != tieringExtPb.Tier_TIER_FI_PLUS && toTier != tieringExtPb.Tier_TIER_FI_INFINITE {
		logger.Info(ctx, "user's movement not eligible for upgrade success screen")
		return nextAction, nil
	}
	// Get deeplink for onboarding add funds tiering upgrade success screen
	upgradeDl, getDlErr := s.tieringDeeplinkManager.GetOnbAddFundsUpgradeSuccessDeeplink(ctx, toTier, actorId, nextAction)
	if getDlErr != nil {
		return nil, errors.Wrap(getDlErr, "error getting onb add funds upgrade success deeplink")
	}
	return upgradeDl, nil
}

// handleAddFundsOrder checks if the order belong to add funds flow and returns
// transaction for the second leg of add funds flow, status to be displayed by client, next action
// if the order does not belong to add funds flow return ErrNotAddFundsOrder
// update : if add funds was done via a tiering pitch, and tier upgrade is successful for the user
// upgrade success screen is returned as next action
func (s *Service) handleAddFundsOrder(ctx context.Context,
	orderWithTxnEntry *orderPb.OrderWithTransactions,
	actorId string, appPlatform commontypes.Platform, appVersion uint32) (
	*paymentPb.Transaction,
	fePayPb.OrderStatus,
	*deeplink.Deeplink,
	error) {

	var (
		txn        *paymentPb.Transaction
		nextAction *deeplink.Deeplink
		feStatus   fePayPb.OrderStatus
		err        error
		orderEntry = orderWithTxnEntry.GetOrder()
	)

	isTpapAddFundsOrder, tpapAddFundsOrderCheckErr := pay.IsTpapAddFundsOrder(ctx, s.piClient, s.savingsClient, s.accountPiRelationClient, orderWithTxnEntry)
	if tpapAddFundsOrderCheckErr != nil {
		// gracefully ignore error and log
		logger.Error(ctx, "error checking if order is of tpap add fund order or not", zap.Error(tpapAddFundsOrderCheckErr))
	}

	// if it's neither the regular add-funds nor the TPAP add-funds, early return
	if !orderEntry.IsAddFundsOrder() && !isTpapAddFundsOrder {
		return nil, 0, nil, ErrNotAddFundsOrder
	}

	// figure out the next action for the order
	switch orderEntry.GetUiEntryPoint() {
	case orderPb.UIEntryPoint_ONBOARD_ADD_FUNDS:
		nextAction, err = s.getNextAction(ctx, actorId, orderEntry)
		if err != nil {
			return nil, fePayPb.OrderStatus_ORDER_STATUS_UNSPECIFIED, nil, fmt.Errorf("error getting next action: %v", err)
		}
	case orderPb.UIEntryPoint_ONBOARD_ADD_FUNDS_PRE_FUNDING:
		// In case of Prefunding We will be sending Next Action only when 1st leg Succeeds.
		if orderEntry.GetStatus() == orderPb.OrderStatus_IN_SETTLEMENT {
			nextAction, err = s.getNextAction(ctx, actorId, orderEntry)
			if err != nil {
				return nil, fePayPb.OrderStatus_ORDER_STATUS_UNSPECIFIED, nil, fmt.Errorf("error getting next action for prefunding: %v", err)
			}
		}
	case orderPb.UIEntryPoint_AA_SALARY:
		nextAction, err = s.getNextActionForAaSalary(ctx, actorId, orderEntry)
		if err != nil {
			logger.Error(ctx, "error getting next action for aa salary", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		}
	case orderPb.UIEntryPoint_ADD_FUNDS_USS:
		nextAction, err = s.getNextActionForAddFundsUss(orderEntry)
		if err != nil {
			logger.Error(ctx, "error getting next action for add funds uss flow", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		}
	case orderPb.UIEntryPoint_ACCOUNT_CLOSURE_PENDING_CHARGES:
		nextAction, err = s.getNextActionForAddFundsPendingChargesAccountClosure(orderEntry)
		if err != nil {
			logger.Error(ctx, "error getting next action for add funds uss flow", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		}
	default:
		nextAction, err = s.handleNonOnboardingAddFundsAndGetNextAction(ctx, actorId, orderEntry, appPlatform, appVersion, isTpapAddFundsOrder)
		// We do not want to fail in case we are unable to get next action for tiering add funds screen - log and continue
		if err != nil {
			logger.Error(ctx, "error getting next action for non-onboarding add funds", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		}
	}

	// The following logic extracts the Txn in consideration where Add-Funds went via 2-legged flow, i.e. the Regular Add-Funds.
	// If it's 2-legged, there will be two txns within the Order, i.e. User -> Pool Account -> Fi SA.
	// And if that's not the case, we pick up the only Txn present within this Order.
	if len(orderWithTxnEntry.GetTransactions()) > 1 {
		txn = orderWithTxnEntry.GetTransactions()[1]
	} else if len(orderWithTxnEntry.GetTransactions()) == 1 {
		txn = orderWithTxnEntry.GetTransactions()[0]
	}

	// client will give priority to next action even if the order is in progress
	feStatus, err = getAddFundsFeOrderStatus(isTpapAddFundsOrder, orderEntry.GetStatus())
	if err != nil {
		return nil, 0, nil, fmt.Errorf("error getting FE Order Status: %w", err)
	}

	return txn, feStatus, nextAction, nil
}

// getAddFundsFeOrderStatus converts the BE Order status to FE Order status for Add Funds.
// It also caters for both type of ways via which Add-Funds can happen, i.e. TPAP and Regular
func getAddFundsFeOrderStatus(isTpapAddFunds bool, beOrderStatus orderPb.OrderStatus) (feOrderStatus fePayPb.OrderStatus, err error) {
	if isTpapAddFunds {
		feStatus, ok := beStatusToFeStatus[beOrderStatus]
		if !ok {
			return fePayPb.OrderStatus_ORDER_STATUS_UNSPECIFIED, fmt.Errorf("BE to FE Order Status mapping not present for TPAP Add Funds. BE Status: %s", beOrderStatus)
		}

		return feStatus, nil
	}

	feStatus, ok := beStatusToFeStatusForAddFunds[beOrderStatus]
	if !ok {
		return fePayPb.OrderStatus_ORDER_STATUS_UNSPECIFIED, fmt.Errorf("BE to FE Order Status mapping not present for Regular Add Funds. BE Status: %s", beOrderStatus)
	}

	return feStatus, nil
}

// Gets next action for add funds whose ui entry point is not onboarding
// Check if user's tier can be upgraded or not
// If upgrade is successful, we populate next action with tiering upgrade success screen
// If it fails, we return nil in next action
func (s *Service) handleNonOnboardingAddFundsAndGetNextAction(ctx context.Context, actorId string, order *orderPb.Order,
	appPlatform commontypes.Platform, appVersion uint32, isTpapAddFundsOrder bool) (*deeplink.Deeplink, error) {

	// if it is an add funds order and not a terminal order, do not return a next action
	if order.IsAddFundsOrder() && !order.GetStatus().IsTerminalForAddFunds() {
		return nil, nil
	}

	// if it is a tpap add funds order and not a terminal order, do not return a next action
	if isTpapAddFundsOrder && order.GetStatus() != orderPb.OrderStatus_PAID {
		return nil, nil
	}

	// skip tier orchestration if user initiated add funds from AMB details entry point
	if order.GetUiEntryPoint() == orderPb.UIEntryPoint_AMB_DETAILS {
		return nil, nil
	}

	trialDetailsResp, trialDetailsErr := s.tieringClient.GetTrialDetails(ctx, &tieringPb.GetTrialDetailsRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(trialDetailsResp, trialDetailsErr); rpcErr != nil {
		return nil, errors.Wrap(rpcErr, "error getting trial details for user")
	}

	provenance := tieringPkg.GetTieringProvenanceForAddFunds(order.GetUiEntryPoint(), trialDetailsResp)
	tierUpgradeRes, tierUpgradeErr := s.tieringClient.Upgrade(ctx, &tieringPb.UpgradeRequest{
		ActorId:    actorId,
		Provenance: provenance,
	})
	if rpcErr := epifigrpc.RPCError(tierUpgradeRes, tierUpgradeErr); rpcErr != nil {
		return nil, errors.Wrap(rpcErr, "error upgrading tier for user")
	}
	toTier := tierUpgradeRes.GetToTier()
	if toTier.IsBaseTier() {
		return nil, nil
	}

	if !s.isTieringEnabledForAppVersionAndAppPlatform(ctx, appPlatform, appVersion) {
		return nil, nil
	}

	// Get deeplink for tiering upgrade success screen
	upgradeDl, err := s.tieringDeeplinkManager.GetUpgradeSuccessDeeplink(ctx, toTier, actorId, appPlatform, appVersion)
	if err != nil {
		return nil, errors.Wrap(err, "error getting upgrade success deeplink")
	}

	logger.Debug(ctx, "next action", zap.Any("deeplink", upgradeDl))
	return upgradeDl, nil
}

func (s *Service) isTieringEnabledForAppVersionAndAppPlatform(ctx context.Context, appPlatform commontypes.Platform, appVersion uint32) bool {
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		return appVersion >= s.conf.Tiering().TieringFeature().MinVersionAndroid()
	case commontypes.Platform_IOS:
		return appVersion >= s.conf.Tiering().TieringFeature().MinVersionIos()
	default:
		logger.Error(ctx, "received unidentifiable app platform, returning false", zap.Any(logger.PLATFORM, appPlatform))
		return false
	}
}

// getOrderStatusUrl return order status icon url. It will map Success/Fail/Pending status only,
// For rest other status it will send empty string.
func getOrderStatusUrl(feStatus fePayPb.OrderStatus, orderStatusIconUrl *config.OrderStatusIconUrl) string {
	switch feStatus {
	case fePayPb.OrderStatus_PAYMENT_SUCCESS:
		return orderStatusIconUrl.Success
	case fePayPb.OrderStatus_PAYMENT_IN_PROGRESS:
		return orderStatusIconUrl.Pending
	case fePayPb.OrderStatus_PAYMENT_FAILED:
		return orderStatusIconUrl.Fail
	default:
		return ""

	}

}

func isPostPaymentDisable(order *orderPb.Order) bool {
	return lo.Contains([]orderPb.OrderWorkflow{
		orderPb.OrderWorkflow_P2P_COLLECT,
		orderPb.OrderWorkflow_ADD_FUNDS,
		orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
		orderPb.OrderWorkflow_P2P_COLLECT_SHORT_CIRCUIT,
	}, order.GetWorkflow()) || len(lo.Intersect([]orderPb.OrderTag{
		orderPb.OrderTag_MUTUAL_FUND,
		orderPb.OrderTag_DEBIT_CARD_CHARGES,
		orderPb.OrderTag_US_STOCKS,
	}, order.GetTags())) > 0
}

// getPostPaymentNextAction decide the if post-payment screen is applicable for order & actor.
// It will check release feature and order workflow to decide it.
func (s *Service) getPostPaymentNextAction(ctx context.Context, order *orderPb.Order, txn *paymentPb.Transaction, actorId string, feStatus fePayPb.OrderStatus, errorView *transaction.ErrorView, feTxnId string, orderWithTxn *orderPb.OrderWithTransactions) (*deeplink.Deeplink, error) {
	if order.GetPostPaymentDeeplink() == nil {
		// Workflow check if post payment screen is enabled or not.
		if isPostPaymentDisable(order) {
			return nil, nil
		}

		releaseConstraint := release.NewCommonConstraintData(types.Feature_POST_PAYMENT_SCREEN).WithActorId(actorId)
		isReleased, err := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
		if err != nil {
			return nil, fmt.Errorf("failed to evaluate feature Feature POST_PAYMENT_SCREEN, %w", err)
		}
		if !isReleased {
			return nil, nil
		}

		postPaymentDeeplink, err := s.getPostPaymentScreen(ctx, feStatus, typePb.GetFromBeMoney(order.GetAmount()),
			order.GetToActorId(), txn, feTxnId, order.GetId(), order.GetFromActorId(), errorView, orderWithTxn)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch next action postPaymentDeeplink: %w", err)
		}
		return postPaymentDeeplink, nil
	}

	if !shouldPropagateOrderPostPaymentDeeplink(order.GetUiEntryPoint(), feStatus) {
		return nil, nil
	}

	dl := &deeplink.Deeplink{}
	err := order.GetPostPaymentDeeplink().UnmarshalTo(dl)
	if err != nil {
		return nil, fmt.Errorf("failed to generate next action deeplink: %w", err)
	}

	return dl, nil
}

// shouldPropagateOrderPostPaymentDeeplink : can be used to house all the logic to decide whether the post-payment-deeplink stored in the Order should be propagated to the client or not.
// Currently, it depends upon UI_ENTRY_POINT and OrderStatus, though, can be extended.
func shouldPropagateOrderPostPaymentDeeplink(uiEntryPoint orderPb.UIEntryPoint, feStatus fePayPb.OrderStatus) bool {
	if feStatus == fePayPb.OrderStatus_PAYMENT_FAILED && uiEntryPoint == orderPb.UIEntryPoint_PHYSICAL_DEBIT_CARD_CHARGES {
		return false
	}
	if uiEntryPoint == orderPb.UIEntryPoint_ADD_FUNDS_USS && feStatus != fePayPb.OrderStatus_PAYMENT_SUCCESS {
		return false
	}
	return true
}

// filterTimelineActions - removes the given timeline action type from the list of timeline actions passed
func filterTimelineActions(actions []*timeline.TimelineAction, actionToRemove timeline.TimelineAction_ActionType) []*timeline.TimelineAction {
	var finalActions []*timeline.TimelineAction
	for _, action := range actions {
		if action.GetAction() != actionToRemove {
			finalActions = append(finalActions, action)
		}
	}
	return finalActions
}

// filterAddFundsOptionForTpapAccount: removes TRANSFER_IN timeline action type (which corresponds to Add Funds action) if exists for TPAP account
func (s *Service) filterAddFundsOptionForTpapAccount(ctx context.Context, piFrom, actorId string, actions []*timeline.TimelineAction) ([]*timeline.TimelineAction, error) {
	var finalActions []*timeline.TimelineAction

	for _, action := range actions {
		if action.GetAction() != timeline.TimelineAction_TRANSFER_IN {
			finalActions = append(finalActions, action)
		} else {

			isInternalAccountPi, err := s.isInternalAccountPi(ctx, piFrom, actorId)
			if err != nil {
				// skipping the action instead of failing the loop when no internal account is found.
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					continue
				}

				return nil, fmt.Errorf("error while checking if pi is internal or not , err = %w", err)
			}
			// TRANSFER_IN action (Add Funds) action is only allowed for internal account
			if isInternalAccountPi {
				finalActions = append(finalActions, action)
			}
		}
	}
	return finalActions, nil
}

func isOrderEligibleForPg(orderWithTxnEntry *orderPb.OrderWithTransactions) bool {
	if lo.Some(isOrderTagEligibleForPg, orderWithTxnEntry.GetOrder().GetTags()) {
		return true
	}

	return false
}
