package providers

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/mask"

	"github.com/epifi/gamma/frontend/tiering/helper"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	payPb "github.com/epifi/gamma/api/pay"
	types "github.com/epifi/gamma/api/typesv2"
	upiScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/common"
	dataCollector "github.com/epifi/gamma/frontend/pay/transaction/payment_options/data_collector"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/providers/args"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/tpap"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

const (
	noteText                             = "You can only transfer a total of ₹5000 in the first 24 hours from a newly linked account"
	noteTextColor                        = "#6A6D70"
	noteLeftImgTextPadding         int32 = 12
	noteCornerRadius               int32 = 20
	noteLeftPadding                int32 = 20
	noteRightPadding               int32 = 20
	noteTopPadding                 int32 = 12
	noteBottomPadding              int32 = 12
	noteBorderColor                      = "#E7E7E7"
	noteBorderWidth                int32 = 1
	noteLeftVisualElementImgUrl          = "https://epifi-icons.pointz.in/tiering/add_funds/info_icon.png"
	noteLeftVisualElementImgHeight int32 = 24
	noteLeftVisualElementImgWidth  int32 = 24

	addAccountsCtaText                     = "Add more accounts"
	addAccountsCtaTextFontColor            = "#00B899"
	addMoreCtaContainerBgColor             = "#F6F9FD"
	addMoreCtaContainerCornerRadius  int32 = 19
	addMoreCtaContainerTopPadding    int32 = 12
	addMoreCtaContainerBottomPadding int32 = 12
	addMoreCtaContainerLeftPadding   int32 = 24
	addMoreCtaContainerRightPadding  int32 = 24

	upiAccountTextFontColor               = "#6A6D70"
	upiAccountTextDisabledFontColor       = "#B2B5B9"
	upiAccountLeftImgPadding        int32 = 8
	upiAccountLeftImgHeight         int32 = 32
	upiAccountLeftImgWidth          int32 = 32

	checkboxText      = "By proceeding I consent to Epifi Tech reactivating the displayed accounts with my UPI ID"
	checkboxFontColor = "#313234"
)

var (
	unavailableTag = func() *uiPb.IconTextComponent {
		return &uiPb.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("UNAVAILABLE", "#6A6D70", commontypes.FontStyle_OVERLINE_2XS_CAPS),
			},
			ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
				BgColor:       "#EFF2F6",
				CornerRadius:  9,
				LeftPadding:   8,
				RightPadding:  8,
				TopPadding:    2,
				BottomPadding: 1,
			},
		}
	}
)

type TpapPaymentOptionProvider struct {
	conf *genconf.Config
}

var _ IPaymentOption = &TpapPaymentOptionProvider{}

func NewTpapPaymentOptionProvider(conf *genconf.Config) *TpapPaymentOptionProvider {
	return &TpapPaymentOptionProvider{
		conf: conf,
	}
}

func (t *TpapPaymentOptionProvider) GetPaymentOptionArgs(collectedData *dataCollector.CollectedData, displayOptions *config.OptionsDisplay, paymentAmount *types.Money) args.IPaymentOptionsArgs {
	var args []func(*tpap.Args)
	totalAccounts := len(collectedData.TpapAccountsData())
	unAvailableAccounts := 0
	// show tag if all accounts are unavailable
	if unAvailableAccounts == totalAccounts {
		args = append(args, tpap.WithToShowTag())
	}
	// show note if any one account is unavailable
	if unAvailableAccounts >= 1 {
		args = append(args, tpap.WithToShowNote())
	}
	if lo.Contains(displayOptions.SectionsToExpand, transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP) {
		args = append(args, tpap.WithExpandCollapsibleState(transaction.ExpandCollapseState_EXPAND_COLLAPSE_STATE_EXPANDED))
	}
	if lo.Contains(displayOptions.UnavailableSections, transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP) {
		args = append(args,
			tpap.WithExpandCollapsibleState(transaction.ExpandCollapseState_EXPAND_COLLAPSE_STATE_COLLAPSED),
			tpap.WithPaymentOptionState(transaction.AvailabilityState_AVAILABILITY_STATE_UNAVAILABLE))
	} else {
		args = append(args, tpap.WithPaymentOptionState(transaction.AvailabilityState_AVAILABILITY_STATE_AVAILABLE))
	}
	return tpap.NewTpapArgs(args...)
}

// nolint: funlen
func (t *TpapPaymentOptionProvider) GetPaymentOption(ctx context.Context, collectedData *dataCollector.CollectedData, paymentAmount *types.Money, vendor frontend.Vendor, optionsDisplay *config.OptionsDisplay, uiEntryPoint timeline.TransactionUIEntryPoint, accountDetails *payPb.AccountDetails) (*transaction.GetPaymentOptionsResponse_PaymentOption, error) {
	var tag, note *uiPb.IconTextComponent
	paymentOptionArgs := t.GetPaymentOptionArgs(collectedData, optionsDisplay, paymentAmount)

	if paymentOptionArgs.ToShowTag() {
		tag = paymentOptionArgs.PaymentOptionTag()
	}
	// TODO(sainath): Make it extensible to support other notes as well
	if paymentOptionArgs.ToShowNote() {
		note = &uiPb.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(noteText, noteTextColor, commontypes.FontStyle_BODY_XS),
			},
			LeftImgTxtPadding: noteLeftImgTextPadding,
			ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
				CornerRadius:  noteCornerRadius,
				LeftPadding:   noteLeftPadding,
				RightPadding:  noteRightPadding,
				TopPadding:    noteTopPadding,
				BottomPadding: noteBottomPadding,
				BorderColor:   noteBorderColor,
				BorderWidth:   noteBorderWidth,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(noteLeftVisualElementImgUrl, noteLeftVisualElementImgHeight, noteLeftVisualElementImgWidth),
		}
	}

	// Get data collector configuration to check if add more accounts CTA should be shown
	dataCollectorConfig, err := t.conf.PaymentOptionsConfig().GetDataCollectorConfig(uiEntryPoint)
	if err != nil {
		return nil, errors.Wrap(err, "error in fetching data collector config for entrypoint")
	}

	var addMoreAccountsCta *uiPb.IconTextComponent
	if !dataCollectorConfig.DisallowExternalTpapAccountListing() {
		tpapConnectNewAccountsDeeplink, getTpapDlErr := deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
			UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
		})
		if getTpapDlErr != nil {
			return nil, errors.Wrap(getTpapDlErr, "error in getting tpap deeplink")
		}
		addMoreAccountsCta = &uiPb.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(addAccountsCtaText, addAccountsCtaTextFontColor, commontypes.FontStyle_BUTTON_S),
			},
			Deeplink: tpapConnectNewAccountsDeeplink,
			ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
				BgColor:       addMoreCtaContainerBgColor,
				CornerRadius:  addMoreCtaContainerCornerRadius,
				LeftPadding:   addMoreCtaContainerLeftPadding,
				RightPadding:  addMoreCtaContainerRightPadding,
				TopPadding:    addMoreCtaContainerTopPadding,
				BottomPadding: addMoreCtaContainerBottomPadding,
			},
		}
	}
	isAmountInAllowedThresholdForCoolOff := paymentAmount.GetUnits() <= t.conf.AddFundsParams().AddFundsV4Params().TpapOptionThreshold().UpperBound()
	isAmountInAllowedThresholdForNonCoolOff := paymentAmount.GetUnits() <= t.conf.PaymentOptionsConfig().TpapOptionsConfig().MaxAmountAllowed()
	upiAccounts, getUpiAccsErr := getUpiAccounts(collectedData, isAmountInAllowedThresholdForCoolOff, isAmountInAllowedThresholdForNonCoolOff)
	if getUpiAccsErr != nil {
		return nil, errors.Wrap(getUpiAccsErr, "error getting upi accounts data")
	}

	PaymentOptionCheckbox := &widgetPb.CheckboxItem{
		DisplayText: commontypes.GetTextFromStringFontColourFontStyle(checkboxText, checkboxFontColor, commontypes.FontStyle_BODY_4),
		IsChecked:   true,
	}

	// When tiering pitch is enabled and user comes on payment options screen from all plans page,
	// removing check box to display AdditionalText component to avoid too many footer texts
	if collectedData.IsTieringPitchInPaymentOptionsEnabled() && helper.IsAllPlansEntryPoint(uiEntryPoint) {
		PaymentOptionCheckbox = nil
	}
	// Do not display for recharge related payments
	if uiEntryPoint == timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_RECHARGE_PAYMENT {
		PaymentOptionCheckbox = nil
	}
	return &transaction.GetPaymentOptionsResponse_PaymentOption{
		OptionTitle:         common.GetPaymentOptionTitle(uiEntryPoint, transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP, t.conf.PaymentOptionsConfig()),
		Tag:                 tag,
		ExpandCollapseState: paymentOptionArgs.ExpandCollapsibleState(),
		IsCollapsible:       true,
		DownArrow:           common.GetDownArrow(),
		PaymentOptionState:  paymentOptionArgs.PaymentOptionState(),
		PaymentOptionType:   transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP,
		BackgroundColour:    widgetPb.GetBlockBackgroundColour(common.PaymentOptionBgColor),
		PaymentOptionCta:    common.GetPaymentOptionCtaV2(paymentAmount, uiEntryPoint, transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP),
		Option: &transaction.GetPaymentOptionsResponse_PaymentOption_UpiAccountsOption{
			UpiAccountsOption: &transaction.TpapPaymentOption{
				UpiAccounts:        upiAccounts,
				Note:               note,
				AddMoreAccountsCta: addMoreAccountsCta,
			},
		},
		PaymentOptionCheckbox: PaymentOptionCheckbox,
	}, nil
}

func (t *TpapPaymentOptionProvider) GetPaymentOptionType() transaction.PaymentOptionType {
	return transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP
}

func (t *TpapPaymentOptionProvider) IsPaymentOptionApplicable(req *IsPaymentOptionApplicableRequest) bool {
	maxAmountAllowed := t.conf.PaymentOptionsConfig().TpapOptionsConfig().MaxAmountAllowed()
	paymentAmtFloat, _ := moneyPkg.ToDecimal(req.PaymentAmount.GetBeMoney()).Float64()
	if paymentAmtFloat > float64(maxAmountAllowed) {
		return false
	}
	switch req.UiEntryPoint {
	case timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_SECURED_CC:
		return false
	default:
		return true
	}
}

func getUpiAccounts(collectedData *dataCollector.CollectedData, isAmountInAllowedThresholdForCoolOff, isAmountInAllowedThresholdForNonCoolOff bool) ([]*transaction.TpapPaymentOption_UpiAccount, error) {
	var upiAccounts []*transaction.TpapPaymentOption_UpiAccount
	for _, accountData := range collectedData.TpapAccountsData() {
		textFontColor := upiAccountTextFontColor
		var tag *uiPb.IconTextComponent
		availabilityState := transaction.AvailabilityState_AVAILABILITY_STATE_AVAILABLE
		var opacity int32 = 100
		if !isAmountInAllowedThresholdForNonCoolOff {
			textFontColor = upiAccountTextDisabledFontColor
			tag = unavailableTag()
			availabilityState = transaction.AvailabilityState_AVAILABILITY_STATE_UNAVAILABLE
			opacity = 50
		}
		if accountData.UpiAccount().GetStatus() == upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE {
			availabilityState = transaction.AvailabilityState_AVAILABILITY_STATE_INACTIVE
		}
		var derivedAccountId string
		var getDerivedAccIdErr error
		if accountData.UpiAccount().IsInternal() {
			derivedAccountId, getDerivedAccIdErr = payPkg.GetEncodedDerivedAccountId(accountData.UpiAccount().GetAccountRefId(), accountData.UpiAccount().GetId(), "")
		} else {
			derivedAccountId, getDerivedAccIdErr = payPkg.GetEncodedDerivedAccountId("", accountData.UpiAccount().GetId(), "")
		}
		if getDerivedAccIdErr != nil {
			return nil, fmt.Errorf("error getting encode derived accourt id: %w", getDerivedAccIdErr)
		}
		upiAccounts = append(upiAccounts,
			&transaction.TpapPaymentOption_UpiAccount{
				UpiAccountDisplayData: &uiPb.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle(accountData.UpiAccount().GetBankName()+" "+accountData.UpiAccount().GetMaskedAccountNumber(),
							textFontColor, commontypes.FontStyle_SUBTITLE_S),
					},
					LeftImgTxtPadding: upiAccountLeftImgPadding,
					LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(accountData.BankLogoUrl(), upiAccountLeftImgHeight, upiAccountLeftImgWidth),
				},
				Tag:                   tag,
				AvailabilityState:     availabilityState,
				Opacity:               opacity,
				IsRadioButtonSelected: false,
				UpiAccountDetailsForPayment: &transaction.TpapPaymentOption_UpiAccountDetailsForPayment{
					// Note: We are using the GetMaskedAccountNumber function on an already masked account number, which will replace the 'X' mask with 'x'.
					// Example: XXXXXX1234 -> xxxxxx1234
					MaskedAccountNumber: mask.GetMaskedAccountNumber(accountData.UpiAccount().GetMaskedAccountNumber(), ""),
					IfscCode:            accountData.UpiAccount().GetIfscCode(),
					DerivedAccountId:    derivedAccountId,
					AccountType:         accountData.UpiAccount().GetAccountType(),
				},
			})
	}
	return upiAccounts, nil
}
