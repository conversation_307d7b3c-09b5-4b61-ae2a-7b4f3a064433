package data_collector

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/samber/lo"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	accounts2 "github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	balanceMocks "github.com/epifi/gamma/api/accounts/balance/mocks"
	"github.com/epifi/gamma/api/actor/mocks"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	"github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMock "github.com/epifi/gamma/api/savings/mocks"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	mocks3 "github.com/epifi/gamma/api/tiering/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	upiOnboardingMocksPb "github.com/epifi/gamma/api/upi/onboarding/mocks"
	mocks2 "github.com/epifi/gamma/api/user/group/mocks"
	mock_user "github.com/epifi/gamma/api/user/mocks"
	frontendconf "github.com/epifi/gamma/frontend/config"
	frontendgenconf "github.com/epifi/gamma/frontend/config/genconf"
	tieringDataMocks "github.com/epifi/gamma/frontend/tiering/data_collector/mocks"
	"github.com/epifi/gamma/frontend/tiering/helper"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
)

// Helper function to convert static config to genconf for testing
func getABDynConfFromStaticConf(staticConf *releaseConfig.ABFeatureReleaseConfig) *releaseGenConf.ABFeatureReleaseConfig {
	g := &releaseGenConf.ABFeatureReleaseConfig{}
	g.Init()
	_ = g.Set(staticConf, false, nil)
	return g
}

// Helper function to create ABFeatureReleaseConfig for testing
func createTestABFeatureReleaseConfig(isTieringPitchEnabled bool) *releaseGenConf.ABFeatureReleaseConfig {
	return getABDynConfFromStaticConf(&releaseConfig.ABFeatureReleaseConfig{
		FeatureConstraints: map[string]*releaseConfig.ABFeatureConstraintConfig{
			typesPb.Feature_FEATURE_TIERING_PITCH_IN_PAYMENT_OPTIONS.String(): {
				ConstraintConfig: &releaseConfig.ConstraintConfig{
					AppVersionConstraintConfig: &releaseConfig.AppVersionConstraintConfig{
						MinAndroidVersion: lo.Ternary(isTieringPitchEnabled, 0, 1),
					},
				},
			},
		},
	})
}

// Helper function to create genconf.Config for testing
func createTestGenConf() *frontendgenconf.Config {
	// Use the actual config loading for tests - this will load from config files
	gconf, err := frontendgenconf.LoadOnlyStaticConf()
	if err != nil {
		// Fallback to creating a minimal config if loading fails
		gconf = &frontendgenconf.Config{}
		gconf.Init("test")
	}
	return gconf
}

func TestDataCollectorService_CollectData(t *testing.T) {

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockSavingsClient := savingsMock.NewMockSavingsClient(ctr)
	mockBalanceClient := balanceMocks.NewMockBalanceClient(ctr)
	mockAccountPIRelationClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockUpiOnbClient := upiOnboardingMocksPb.NewMockUpiOnboardingClient(ctr)
	mockAuthClient := authMocks.NewMockAuthClient(ctr)
	mockTieringDataCollector := tieringDataMocks.NewMockDataCollector(ctr)
	mockActorClient := mocks.NewMockActorClient(ctr)
	mockUserClient := mock_user.NewMockUsersClient(ctr)
	mockGrpClient := mocks2.NewMockGroupClient(ctr)
	mockTieringClient := mocks3.NewMockTieringClient(ctr)

	type mockGetAccountsList struct {
		enable bool
		req    *savingsPb.GetAccountsListRequest
		resp   *savingsPb.GetAccountsListResponse
		err    error
	}

	type mockGetAccounts struct {
		enable bool
		req    *upiOnbPb.GetAccountsRequest
		resp   *upiOnbPb.GetAccountsResponse
		err    error
	}

	type mockGetPiByAccountId struct {
		enable bool
		req    *accountPiPb.GetPiByAccountIdRequest
		resp   *accountPiPb.GetPiByAccountIdResponse
		err    error
	}

	type mockGetAccountBalance struct {
		enable bool
		req    *accountBalancePb.GetAccountBalanceRequest
		resp   *accountBalancePb.GetAccountBalanceResponse
		err    error
	}

	type mockGetTieringEssentials struct {
		enable  bool
		actorId string
		resp    *helper.TieringFeEssentials
		err     error
	}

	type mockGetTrialsResponse struct {
		enable bool
		req    *tieringPb.GetTrialDetailsRequest
		resp   *tieringPb.GetTrialDetailsResponse
		err    error
	}

	type args struct {
		actorId      string
		uiEntryPoint timeline.TransactionUIEntryPoint
	}
	tests := []struct {
		name                     string
		args                     args
		want                     *CollectedData
		mockGetAccountsList      mockGetAccountsList
		mockGetAccounts          mockGetAccounts
		mockGetPiByAccountId     mockGetPiByAccountId
		mockGetAccountBalance    mockGetAccountBalance
		mockGetTieringEssentials mockGetTieringEssentials
		mockGetTrialsResponse    mockGetTrialsResponse
		isTieringPitchEnabled    bool // Whether FEATURE_TIERING_PITCH_IN_PAYMENT_OPTIONS should be enabled
		wantErr                  bool
	}{
		{
			name: "should return vpa id of highest APO account and sorted saving acc list according to APO data for internal accounts with tpap account",
			args: args{
				actorId:      "actor-1",
				uiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS,
			},
			mockGetAccountsList: mockGetAccountsList{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				resp: &savingsPb.GetAccountsListResponse{
					Accounts: []*savingsPb.Account{
						{
							Id:      "FI-NRO-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:      "FI-NRE-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccounts: mockGetAccounts{
				enable: true,
				req: &upiOnbPb.GetAccountsRequest{
					ActorId: "actor-1",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				resp: &upiOnbPb.GetAccountsResponse{
					Accounts: []*upiOnbPb.UpiAccount{
						{
							Id:           "upi-nro-1",
							AccountRefId: "",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRO,
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "FI-NRE-1",
					AccountType: accounts2.Type_SAVINGS,
					PiTypes: []paymentinstrument.PaymentInstrumentType{
						paymentinstrument.PaymentInstrumentType_UPI,
					},
					PiStates: []paymentinstrument.PaymentInstrumentState{
						paymentinstrument.PaymentInstrumentState_CREATED,
						paymentinstrument.PaymentInstrumentState_VERIFIED,
					},
				},
				resp: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*paymentinstrument.PaymentInstrument{
						{
							Identifier: &paymentinstrument.PaymentInstrument_Upi{
								Upi: &paymentinstrument.Upi{
									Vpa: "vpa-nre",
								},
							},
							Type:  paymentinstrument.PaymentInstrumentType_UPI,
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockGetAccountBalance: mockGetAccountBalance{
				enable: true,
				req: &accountBalancePb.GetAccountBalanceRequest{
					Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: "FI-NRE-1"},
					DataFreshness: enums.DataFreshness_HISTORICAL,
				},
				resp: &accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				err: nil,
			},
			isTieringPitchEnabled: false,
			mockGetTrialsResponse: mockGetTrialsResponse{
				enable: true,
				req: &tieringPb.GetTrialDetailsRequest{
					ActorId: "actor-1",
				},
				resp: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			want: &CollectedData{
				savingsAccDetails: []*savingsPb.Account{
					{
						Id: "FI-NRE-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRE,
						},
						ActorId: "actor-1",
					},
					{
						Id: "FI-NRO-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRO,
						},
						ActorId: "actor-1",
					},
				},
				vpaId: "vpa-nre",
				tpapAccountsData: []*TpapAccountData{
					{
						upiAccount: &upiOnbPb.UpiAccount{
							Id:           "upi-nro-1",
							AccountRefId: "",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRO,
						},
					},
				},
				tieringEssentials:                     nil,
				isTieringPitchInPaymentOptionsEnabled: false,
				savingsAccountBalance: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
				trialsResponse: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
			},
			wantErr: false,
		},
		{
			name: "should return vpa id of highest APO account and sorted saving acc list according to APO data for internal accounts",
			args: args{
				actorId:      "actor-1",
				uiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS,
			},
			mockGetAccountsList: mockGetAccountsList{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				resp: &savingsPb.GetAccountsListResponse{
					Accounts: []*savingsPb.Account{
						{
							Id:      "FI-NRO-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:      "FI-NRE-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccounts: mockGetAccounts{
				enable: true,
				req: &upiOnbPb.GetAccountsRequest{
					ActorId: "actor-1",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				resp: &upiOnbPb.GetAccountsResponse{
					Accounts: []*upiOnbPb.UpiAccount{},
					Status:   rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "FI-NRE-1",
					AccountType: accounts2.Type_SAVINGS,
					PiTypes: []paymentinstrument.PaymentInstrumentType{
						paymentinstrument.PaymentInstrumentType_UPI,
					},
					PiStates: []paymentinstrument.PaymentInstrumentState{
						paymentinstrument.PaymentInstrumentState_CREATED,
						paymentinstrument.PaymentInstrumentState_VERIFIED,
					},
				},
				resp: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*paymentinstrument.PaymentInstrument{
						{
							Identifier: &paymentinstrument.PaymentInstrument_Upi{
								Upi: &paymentinstrument.Upi{
									Vpa: "vpa-nre",
								},
							},
							Type:  paymentinstrument.PaymentInstrumentType_UPI,
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			isTieringPitchEnabled: false,
			mockGetTrialsResponse: mockGetTrialsResponse{
				enable: true,
				req: &tieringPb.GetTrialDetailsRequest{
					ActorId: "actor-1",
				},
				resp: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccountBalance: mockGetAccountBalance{
				enable: true,
				req: &accountBalancePb.GetAccountBalanceRequest{
					Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: "FI-NRE-1"},
					DataFreshness: enums.DataFreshness_HISTORICAL,
				},
				resp: &accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				err: nil,
			},
			want: &CollectedData{
				savingsAccDetails: []*savingsPb.Account{
					{
						Id: "FI-NRE-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRE,
						},
						ActorId: "actor-1",
					},
					{
						Id: "FI-NRO-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRO,
						},
						ActorId: "actor-1",
					},
				},
				vpaId:                                 "vpa-nre",
				tieringEssentials:                     nil,
				isTieringPitchInPaymentOptionsEnabled: false,
				savingsAccountBalance: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
				trialsResponse: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
			},
			wantErr: false,
		},
		{
			name: "should return vpa id of account(with highest APO) and sorted saving acc list according to APO data for savings accounts with tpap accounts(excluding NRO internal account)",
			args: args{
				actorId:      "actor-1",
				uiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS,
			},
			mockGetAccountsList: mockGetAccountsList{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				resp: &savingsPb.GetAccountsListResponse{
					Accounts: []*savingsPb.Account{
						{
							Id:      "FI-NRO-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:      "FI-NRE-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccounts: mockGetAccounts{
				enable: true,
				req: &upiOnbPb.GetAccountsRequest{
					ActorId: "actor-1",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				resp: &upiOnbPb.GetAccountsResponse{
					Accounts: []*upiOnbPb.UpiAccount{
						{
							Id:           "upi-nro-1",
							AccountRefId: "",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRO,
						},
						{
							Id:           "vpa-nro",
							AccountRefId: "FI-NRO-1",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRO,
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "FI-NRE-1",
					AccountType: accounts2.Type_SAVINGS,
					PiTypes: []paymentinstrument.PaymentInstrumentType{
						paymentinstrument.PaymentInstrumentType_UPI,
					},
					PiStates: []paymentinstrument.PaymentInstrumentState{
						paymentinstrument.PaymentInstrumentState_CREATED,
						paymentinstrument.PaymentInstrumentState_VERIFIED,
					},
				},
				resp: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*paymentinstrument.PaymentInstrument{
						{
							Identifier: &paymentinstrument.PaymentInstrument_Upi{
								Upi: &paymentinstrument.Upi{
									Vpa: "vpa-nre",
								},
							},
							Type:  paymentinstrument.PaymentInstrumentType_UPI,
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			isTieringPitchEnabled: false,
			mockGetTrialsResponse: mockGetTrialsResponse{
				enable: true,
				req: &tieringPb.GetTrialDetailsRequest{
					ActorId: "actor-1",
				},
				resp: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccountBalance: mockGetAccountBalance{
				enable: true,
				req: &accountBalancePb.GetAccountBalanceRequest{
					Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: "FI-NRE-1"},
					DataFreshness: enums.DataFreshness_HISTORICAL,
				},
				resp: &accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				err: nil,
			},
			want: &CollectedData{
				savingsAccDetails: []*savingsPb.Account{
					{
						Id: "FI-NRE-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRE,
						},
						ActorId: "actor-1",
					},
					{
						Id: "FI-NRO-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRO,
						},
						ActorId: "actor-1",
					},
				},
				vpaId: "vpa-nre",
				tpapAccountsData: []*TpapAccountData{
					{
						upiAccount: &upiOnbPb.UpiAccount{
							Id:           "upi-nro-1",
							AccountRefId: "",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRO,
						},
					},
				},
				tieringEssentials:                     nil,
				isTieringPitchInPaymentOptionsEnabled: false,
				savingsAccountBalance: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
				trialsResponse: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
			},
			wantErr: false,
		},
		{
			name: "should return vpa id of account(with highest APO) and sorted saving acc list according to APO data for savings accounts with tpap accounts(including NRE internal account)",
			args: args{
				actorId:      "actor-1",
				uiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS,
			},
			mockGetAccountsList: mockGetAccountsList{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				resp: &savingsPb.GetAccountsListResponse{
					Accounts: []*savingsPb.Account{
						{
							Id:      "FI-NRO-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:      "FI-NRE-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccounts: mockGetAccounts{
				enable: true,
				req: &upiOnbPb.GetAccountsRequest{
					ActorId: "actor-1",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				resp: &upiOnbPb.GetAccountsResponse{
					Accounts: []*upiOnbPb.UpiAccount{
						{
							Id:           "upi-nro-1",
							AccountRefId: "",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRO,
						},
						{
							Id:           "vpa-nrE",
							AccountRefId: "FI-NRE-1",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRE,
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "FI-NRE-1",
					AccountType: accounts2.Type_SAVINGS,
					PiTypes: []paymentinstrument.PaymentInstrumentType{
						paymentinstrument.PaymentInstrumentType_UPI,
					},
					PiStates: []paymentinstrument.PaymentInstrumentState{
						paymentinstrument.PaymentInstrumentState_CREATED,
						paymentinstrument.PaymentInstrumentState_VERIFIED,
					},
				},
				resp: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*paymentinstrument.PaymentInstrument{
						{
							Identifier: &paymentinstrument.PaymentInstrument_Upi{
								Upi: &paymentinstrument.Upi{
									Vpa: "vpa-nre",
								},
							},
							Type:  paymentinstrument.PaymentInstrumentType_UPI,
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			isTieringPitchEnabled: false,
			mockGetTrialsResponse: mockGetTrialsResponse{
				enable: true,
				req: &tieringPb.GetTrialDetailsRequest{
					ActorId: "actor-1",
				},
				resp: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccountBalance: mockGetAccountBalance{
				enable: true,
				req: &accountBalancePb.GetAccountBalanceRequest{
					Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: "FI-NRE-1"},
					DataFreshness: enums.DataFreshness_HISTORICAL,
				},
				resp: &accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				err: nil,
			},
			want: &CollectedData{
				savingsAccDetails: []*savingsPb.Account{
					{
						Id: "FI-NRE-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRE,
						},
						ActorId: "actor-1",
					},
					{
						Id: "FI-NRO-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRO,
						},
						ActorId: "actor-1",
					},
				},
				vpaId: "vpa-nre",
				tpapAccountsData: []*TpapAccountData{
					{
						upiAccount: &upiOnbPb.UpiAccount{
							Id:           "upi-nro-1",
							AccountRefId: "",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRO,
						},
					},
					{
						upiAccount: &upiOnbPb.UpiAccount{
							Id:           "vpa-nrE",
							AccountRefId: "FI-NRE-1",
							ActorId:      "actor-1",
							Apo:          account.AccountProductOffering_APO_NRE,
						},
					},
				},
				tieringEssentials:                     nil,
				isTieringPitchInPaymentOptionsEnabled: false,
				savingsAccountBalance: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
				trialsResponse: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
			},
			wantErr: false,
		},
		{
			name: "should return only internal accounts and no external TPAP accounts for recharge payment entry point",
			args: args{
				actorId:      "actor-1",
				uiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_RECHARGE_PAYMENT,
			},
			mockGetAccountsList: mockGetAccountsList{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				resp: &savingsPb.GetAccountsListResponse{
					Accounts: []*savingsPb.Account{
						{
							Id:      "account-id-1",
							ActorId: "actor-1",
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccounts: mockGetAccounts{
				enable: true,
				req: &upiOnbPb.GetAccountsRequest{
					ActorId: "actor-1",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				resp: &upiOnbPb.GetAccountsResponse{
					Accounts: []*upiOnbPb.UpiAccount{
						{
							Id:           "vpa-internal",
							AccountRefId: "account-ref-id",
							AccountType:  accounts2.Type_SAVINGS,
							ActorId:      "actor-1",
						},
						{
							Id:           "vpa-external-1",
							AccountRefId: "",
							AccountType:  accounts2.Type_SAVINGS,
							ActorId:      "actor-1",
						},
						{
							Id:           "vpa-external-2",
							AccountRefId: "",
							AccountType:  accounts2.Type_CURRENT,
							ActorId:      "actor-1",
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "account-id-1",
					AccountType: accounts2.Type_SAVINGS,
					PiTypes: []paymentinstrument.PaymentInstrumentType{
						paymentinstrument.PaymentInstrumentType_UPI,
					},
					PiStates: []paymentinstrument.PaymentInstrumentState{
						paymentinstrument.PaymentInstrumentState_CREATED,
						paymentinstrument.PaymentInstrumentState_VERIFIED,
					},
				},
				resp: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*paymentinstrument.PaymentInstrument{
						{
							Identifier: &paymentinstrument.PaymentInstrument_Upi{
								Upi: &paymentinstrument.Upi{
									Vpa: "vpa-internal",
								},
							},
							Type:  paymentinstrument.PaymentInstrumentType_UPI,
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockGetAccountBalance: mockGetAccountBalance{
				enable: true,
				req: &accountBalancePb.GetAccountBalanceRequest{
					Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: "account-id-1"},
					DataFreshness: enums.DataFreshness_HISTORICAL,
				},
				resp: &accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				err: nil,
			},
			want: &CollectedData{
				savingsAccDetails: []*savingsPb.Account{
					{
						Id:      "account-id-1",
						ActorId: "actor-1",
					},
				},
				vpaId: "vpa-internal",
				tpapAccountsData: []*TpapAccountData{
					{
						upiAccount: &upiOnbPb.UpiAccount{
							Id:           "vpa-internal",
							AccountRefId: "account-ref-id",
							ActorId:      "actor-1",
							AccountType:  accounts2.Type_SAVINGS,
						},
					},
				},
				savingsAccountBalance: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
				trialsResponse: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
			},
			wantErr: false,
		},
		{
			name: "should not return rupay along with rest of tpap accounts when entry point is Transfer_In",
			args: args{
				actorId:      "actor-1",
				uiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_TRANSFER_IN,
			},
			mockGetAccountsList: mockGetAccountsList{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				resp: &savingsPb.GetAccountsListResponse{
					Accounts: []*savingsPb.Account{
						{
							Id:      "account-id-1",
							ActorId: "actor-1",
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccounts: mockGetAccounts{
				enable: true,
				req: &upiOnbPb.GetAccountsRequest{
					ActorId: "actor-1",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				resp: &upiOnbPb.GetAccountsResponse{
					Accounts: []*upiOnbPb.UpiAccount{
						{
							Id:           "vpa-internal",
							AccountRefId: "account-ref-id",
							AccountType:  accounts2.Type_SAVINGS,
							ActorId:      "actor-1",
						},
						{
							Id:           "vpa-1",
							AccountRefId: "",
							AccountType:  accounts2.Type_SAVINGS,
							ActorId:      "actor-1",
						},
						{
							Id:           "vpa-2",
							AccountRefId: "",
							AccountType:  accounts2.Type_CREDIT,
							ActorId:      "actor-1",
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "account-id-1",
					AccountType: accounts2.Type_SAVINGS,
					PiTypes: []paymentinstrument.PaymentInstrumentType{
						paymentinstrument.PaymentInstrumentType_UPI,
					},
					PiStates: []paymentinstrument.PaymentInstrumentState{
						paymentinstrument.PaymentInstrumentState_CREATED,
						paymentinstrument.PaymentInstrumentState_VERIFIED,
					},
				},
				resp: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*paymentinstrument.PaymentInstrument{
						{
							Identifier: &paymentinstrument.PaymentInstrument_Upi{
								Upi: &paymentinstrument.Upi{
									Vpa: "vpa-internal",
								},
							},
							Type:  paymentinstrument.PaymentInstrumentType_UPI,
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			isTieringPitchEnabled: false,
			mockGetTrialsResponse: mockGetTrialsResponse{
				enable: true,
				req: &tieringPb.GetTrialDetailsRequest{
					ActorId: "actor-1",
				},
				resp: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccountBalance: mockGetAccountBalance{
				enable: true,
				req: &accountBalancePb.GetAccountBalanceRequest{
					Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: "account-id-1"},
					DataFreshness: enums.DataFreshness_HISTORICAL,
				},
				resp: &accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				err: nil,
			},
			want: &CollectedData{
				savingsAccDetails: []*savingsPb.Account{
					{
						Id:      "account-id-1",
						ActorId: "actor-1",
					},
				},
				vpaId: "vpa-internal",
				tpapAccountsData: []*TpapAccountData{
					{
						upiAccount: &upiOnbPb.UpiAccount{
							Id:           "vpa-1",
							AccountRefId: "",
							ActorId:      "actor-1",
							AccountType:  accounts2.Type_SAVINGS,
						},
					},
				},
				tieringEssentials:                     nil,
				isTieringPitchInPaymentOptionsEnabled: false,
				savingsAccountBalance: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
				trialsResponse: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				}},
			wantErr: false,
		},
		{
			name: "should fetch tiering essentials and feature flag for tiering entry point UI_ENTRY_POINT_ALL_PLANS_JOIN_PLUS",
			args: args{
				actorId:      "actor-1",
				uiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PLUS,
			},
			mockGetAccountsList: mockGetAccountsList{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				resp: &savingsPb.GetAccountsListResponse{
					Accounts: []*savingsPb.Account{
						{
							Id:      "FI-NRE-1",
							ActorId: "actor-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccounts: mockGetAccounts{
				enable: true,
				req: &upiOnbPb.GetAccountsRequest{
					ActorId: "actor-1",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				resp: &upiOnbPb.GetAccountsResponse{
					Accounts: []*upiOnbPb.UpiAccount{},
					Status:   rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "FI-NRE-1",
					AccountType: accounts2.Type_SAVINGS,
					PiTypes: []paymentinstrument.PaymentInstrumentType{
						paymentinstrument.PaymentInstrumentType_UPI,
					},
					PiStates: []paymentinstrument.PaymentInstrumentState{
						paymentinstrument.PaymentInstrumentState_CREATED,
						paymentinstrument.PaymentInstrumentState_VERIFIED,
					},
				},
				resp: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*paymentinstrument.PaymentInstrument{
						{
							Identifier: &paymentinstrument.PaymentInstrument_Upi{
								Upi: &paymentinstrument.Upi{
									Vpa: "vpa-nre",
								},
							},
							Type:  paymentinstrument.PaymentInstrumentType_UPI,
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockGetTieringEssentials: mockGetTieringEssentials{
				enable:  true,
				actorId: "actor-1",
				resp: &helper.TieringFeEssentials{
					CurrentTier:              tieringExtPb.Tier_TIER_FI_PLUS,
					TierCriteriaMinValuesMap: make(helper.TierCriteriaMinValuesMap),
					IsUSStocksAccountActive:  true,
				},
				err: nil,
			},
			isTieringPitchEnabled: true,
			mockGetTrialsResponse: mockGetTrialsResponse{
				enable: true,
				req: &tieringPb.GetTrialDetailsRequest{
					ActorId: "actor-1",
				},
				resp: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccountBalance: mockGetAccountBalance{
				enable: true,
				req: &accountBalancePb.GetAccountBalanceRequest{
					Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: "FI-NRE-1"},
					DataFreshness: enums.DataFreshness_HISTORICAL,
				},
				resp: &accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				err: nil,
			},
			want: &CollectedData{
				savingsAccDetails: []*savingsPb.Account{
					{
						Id: "FI-NRE-1",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRE,
						},
						ActorId: "actor-1",
					},
				},
				vpaId: "vpa-nre",
				tieringEssentials: &helper.TieringFeEssentials{
					CurrentTier:              tieringExtPb.Tier_TIER_FI_PLUS,
					TierCriteriaMinValuesMap: make(helper.TierCriteriaMinValuesMap),
					IsUSStocksAccountActive:  true,
				},
				isTieringPitchInPaymentOptionsEnabled: true,
				savingsAccountBalance: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
				trialsResponse: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				}},
			wantErr: false,
		},
		{
			name: "should fetch tiering essentials and feature flag for tiering entry point UI_ENTRY_POINT_ALL_PLANS_JOIN_INFINITE",
			args: args{
				actorId:      "actor-2",
				uiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_INFINITE,
			},
			mockGetAccountsList: mockGetAccountsList{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-2"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				resp: &savingsPb.GetAccountsListResponse{
					Accounts: []*savingsPb.Account{
						{
							Id:      "FI-NRE-2",
							ActorId: "actor-2",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccounts: mockGetAccounts{
				enable: true,
				req: &upiOnbPb.GetAccountsRequest{
					ActorId: "actor-2",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				resp: &upiOnbPb.GetAccountsResponse{
					Accounts: []*upiOnbPb.UpiAccount{},
					Status:   rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetPiByAccountId: mockGetPiByAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "FI-NRE-2",
					AccountType: accounts2.Type_SAVINGS,
					PiTypes: []paymentinstrument.PaymentInstrumentType{
						paymentinstrument.PaymentInstrumentType_UPI,
					},
					PiStates: []paymentinstrument.PaymentInstrumentState{
						paymentinstrument.PaymentInstrumentState_CREATED,
						paymentinstrument.PaymentInstrumentState_VERIFIED,
					},
				},
				resp: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*paymentinstrument.PaymentInstrument{
						{
							Identifier: &paymentinstrument.PaymentInstrument_Upi{
								Upi: &paymentinstrument.Upi{
									Vpa: "vpa-nre-2",
								},
							},
							Type:  paymentinstrument.PaymentInstrumentType_UPI,
							State: paymentinstrument.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockGetTieringEssentials: mockGetTieringEssentials{
				enable:  true,
				actorId: "actor-2",
				resp: &helper.TieringFeEssentials{
					CurrentTier:              tieringExtPb.Tier_TIER_FI_INFINITE,
					TierCriteriaMinValuesMap: make(helper.TierCriteriaMinValuesMap),
					IsUSStocksAccountActive:  false,
				},
				err: nil,
			},
			isTieringPitchEnabled: true,
			mockGetTrialsResponse: mockGetTrialsResponse{
				enable: true,
				req: &tieringPb.GetTrialDetailsRequest{
					ActorId: "actor-2",
				},
				resp: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockGetAccountBalance: mockGetAccountBalance{
				enable: true,
				req: &accountBalancePb.GetAccountBalanceRequest{
					Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: "FI-NRE-2"},
					DataFreshness: enums.DataFreshness_HISTORICAL,
				},
				resp: &accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
						Nanos:        0,
					},
				},
				err: nil,
			},
			want: &CollectedData{
				savingsAccDetails: []*savingsPb.Account{
					{
						Id: "FI-NRE-2",
						SkuInfo: &savingsPb.SKUInfo{
							AccountProductOffering: account.AccountProductOffering_APO_NRE,
						},
						ActorId: "actor-2",
					},
				},
				vpaId: "vpa-nre-2",
				tieringEssentials: &helper.TieringFeEssentials{
					CurrentTier:              tieringExtPb.Tier_TIER_FI_INFINITE,
					TierCriteriaMinValuesMap: make(helper.TierCriteriaMinValuesMap),
					IsUSStocksAccountActive:  false,
				},
				isTieringPitchInPaymentOptionsEnabled: true,
				savingsAccountBalance: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        1000,
					Nanos:        0,
				},
				trialsResponse: &tieringPb.GetTrialDetailsResponse{
					Status: rpc.StatusOk(),
				}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID)

			// Create configurations and service for this test case
			abFeatureReleaseConfig := createTestABFeatureReleaseConfig(tt.isTieringPitchEnabled)
			conf := createTestGenConf()

			// Create the service for this test case
			s := &DataCollectorService{
				gconf: conf,
				conf: &frontendconf.Config{
					BankNameToLogoUrlMap: map[string]string{
						"Federal Bank":   "https://epifi-icons.pointz.in/bank/logo/federal_bank_264px.png",
						"Axis Bank Ltd.": "https://epifi-icons.pointz.in/bank/logo/axis_264px.png",
					},
				},
				savingsClient:           mockSavingsClient,
				balanceClient:           mockBalanceClient,
				accountPIRelationClient: mockAccountPIRelationClient,
				upiOnbClient:            mockUpiOnbClient,
				authClient:              mockAuthClient,
				tieringDataCollector:    mockTieringDataCollector,
				abEvaluator:             helper.GetABEvaluatorOfFeature[string](mockActorClient, mockUserClient, mockGrpClient, abFeatureReleaseConfig, func(str string) string { return str }),
				tieringClient:           mockTieringClient,
			}
			if tt.mockGetAccountsList.enable {
				mockSavingsClient.EXPECT().GetAccountsList(ctx, tt.mockGetAccountsList.req).Return(tt.mockGetAccountsList.resp, tt.mockGetAccountsList.err)
			}
			if tt.mockGetAccounts.enable {
				mockUpiOnbClient.EXPECT().GetAccounts(ctx, tt.mockGetAccounts.req).Return(tt.mockGetAccounts.resp, tt.mockGetAccounts.err)
			}
			if tt.mockGetPiByAccountId.enable {
				mockAccountPIRelationClient.EXPECT().GetPiByAccountId(ctx, tt.mockGetPiByAccountId.req).Return(tt.mockGetPiByAccountId.resp, tt.mockGetPiByAccountId.err)
			}
			if tt.mockGetAccountBalance.enable {
				mockBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), tt.mockGetAccountBalance.req).Return(tt.mockGetAccountBalance.resp, tt.mockGetAccountBalance.err)
			}
			if tt.mockGetTieringEssentials.enable {
				mockTieringDataCollector.EXPECT().GetTieringEssentials(ctx, tt.mockGetTieringEssentials.actorId).Return(tt.mockGetTieringEssentials.resp, tt.mockGetTieringEssentials.err)
			}
			if tt.mockGetTrialsResponse.enable {
				mockTieringClient.EXPECT().GetTrialDetails(ctx, gomock.Any()).Return(tt.mockGetTrialsResponse.resp, tt.mockGetTrialsResponse.err).AnyTimes()
			}

			// Mock the actor client GetActorById call for user group constraint evaluation
			mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

			got, err := s.CollectData(ctx, tt.args.actorId, tt.args.uiEntryPoint)
			if (err != nil) != tt.wantErr {
				t.Errorf("CollectData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CollectData() got = %v, want %v", got, tt.want)
			}
		})
	}
}
