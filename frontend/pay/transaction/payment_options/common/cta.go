package common

import (
	"fmt"

	moneyPkg "github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	typesPb "github.com/epifi/gamma/api/typesv2"
)

func DcChargesCtaFunc(_ *typesPb.Money, _ txnPb.PaymentOptionType) *deeplinkPb.Cta {
	cta := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CONTINUE,
		Text:         "Continue to payment",
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
	return cta
}

func DefaultCtaWithZeroHandling(amount *typesPb.Money, _ txnPb.PaymentOptionType) *deeplinkPb.Cta {
	amountRsInStr := moneyPkg.ToDisplayStringInIndianFormat(amount.GetBeMoney(), 2, true)
	textValue := ""
	// if the amount is 0, then the text on the CTA is going to be different
	if moneyPkg.IsZero(amount.GetBeMoney()) {
		textValue = authoriseText
	} else {
		textValue = fmt.Sprintf(ctaText, amountRsInStr)
	}
	cta := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CONTINUE,
		Text:         textValue,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
	return cta
}

func AllPlansCtaFunc(_ *typesPb.Money, _ txnPb.PaymentOptionType) *deeplinkPb.Cta {
	cta := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CONTINUE,
		Text:         "Add funds to upgrade",
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
	return cta
}

func RechargeCtaFunc(amount *typesPb.Money, _ txnPb.PaymentOptionType) *deeplinkPb.Cta {
	amountRsInStr := moneyPkg.ToDisplayStringInIndianFormat(amount.GetBeMoney(), 2, true)
	textValue := ""
	// if the amount is 0, then the text on the CTA is going to be different
	if moneyPkg.IsZero(amount.GetBeMoney()) {
		textValue = authoriseText
	} else {
		textValue = fmt.Sprintf(payCtaText, amountRsInStr)
	}
	cta := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CONTINUE,
		Text:         textValue,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
	return cta
}
