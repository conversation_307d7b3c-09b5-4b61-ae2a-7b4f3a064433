package common

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	moneyPkg "github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/config/genconf"
)

const (
	downArrowImgUrl            = "https://epifi-icons.pointz.in/tiering/add_funds/down_arrow.png"
	downArrowImgHeight   int32 = 24
	downArrowImgWidth    int32 = 24
	ctaText                    = "Add %s"
	authoriseText              = "Authorise"
	PaymentOptionBgColor       = "#FFFFFF"
	payCtaText                 = "Pay %s"
)

type paymentOptionCtaProvider func(amount *typesPb.Money, paymentOptionType txnPb.PaymentOptionType) *deeplinkPb.Cta

var (
	// paymentOptionCtaProviderMap - can be used to add any specific handling required by BUs to show for different cta content for different PaymentOptionTypes,
	// This provides flexibility and easiness in such when there might be different kinds of handling required on different condition for eg -
	//
	// 1. CTA text is of format `Add %s` where %s should be replaceable by amountString,
	// 2. CTA text is of format `Continue to format` where, there are no replaceable character in test format
	//
	// The above scenario can hold true for same UiEntryPoint and different PaymentOptions as well, so having this ProviderMap can solve for these scenarios
	// All the required handling can be a separate func in `cta.go` file and mapping can be added here for uiEntryPoint.
	paymentOptionCtaProviderMap = map[timeline.TransactionUIEntryPoint]paymentOptionCtaProvider{
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_PHYSICAL_DEBIT_CARD_CHARGES:   DcChargesCtaFunc,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT:               DefaultCtaWithZeroHandling,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION: DefaultCtaWithZeroHandling,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_INFINITE:       AllPlansCtaFunc,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PLUS:           AllPlansCtaFunc,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PRIME:          AllPlansCtaFunc,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_RECHARGE_PAYMENT:              RechargeCtaFunc,
	}

	// specific default cta handling for PaymentOptionType_COLLECT
	collectPoDefaultCta = func(amount *typesPb.Money) *deeplinkPb.Cta {
		amountRsInStr := moneyPkg.ToDisplayStringInIndianFormat(amount.GetBeMoney(), 2, true)
		return &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CONTINUE,
			Text:         fmt.Sprintf("Verify & add %s", amountRsInStr),
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		}
	}
)

// GetPaymentOptionCtaV2 - takes amount, uiEntryPoint and PaymentOptionsType as args and returns cta for the PaymentOption,
// first it checks if there exists any specific handling for uiEntryPoint and PaymentOptionType and returns the cta based on that,
// if no specific handling exists it returns a default CTA
func GetPaymentOptionCtaV2(amount *typesPb.Money, uiEntrypoint timeline.TransactionUIEntryPoint, paymentOptionType txnPb.PaymentOptionType) *deeplinkPb.Cta {
	poCtaProviderFunc, ok := paymentOptionCtaProviderMap[uiEntrypoint]
	if !ok {
		if paymentOptionType == txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT {
			return collectPoDefaultCta(amount)
		}
		return GetPaymentOptionCta(amount)
	}
	return poCtaProviderFunc(amount, paymentOptionType)
}

// GetPaymentOptionCta - takes amount as args and returns default CTA for paymentOptions
func GetPaymentOptionCta(amount *typesPb.Money) *deeplinkPb.Cta {
	amountRsInStr := moneyPkg.ToDisplayStringInIndianFormat(amount.GetBeMoney(), 2, true)
	return &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CONTINUE,
		Text:         fmt.Sprintf(ctaText, amountRsInStr),
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
}

func GetDownArrow() *commontypes.VisualElement {
	return commontypes.GetVisualElementFromUrlHeightAndWidth(downArrowImgUrl, downArrowImgHeight, downArrowImgWidth)
}

// GetPaymentOptionTitle - takes uiEntryPoint and PaymentOptionsType and return Title component of type *commontypes.Text,
// if config for the passed uiEntryPoint is not present for PaymentOptionsType then return the Title constructed with default config,
// NOTE: Currently being used for TPAP PaymentOptionType only but can be extended in same way for other PaymentOptionTypes as well if required
func GetPaymentOptionTitle(uiEntrypoint timeline.TransactionUIEntryPoint, paymentOption txnPb.PaymentOptionType, gconf *genconf.PaymentOptionsConfig) *commontypes.Text {
	poDisplayInfo, _ := gconf.GetDisplayInfoConfig(uiEntrypoint)
	switch paymentOption {
	case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_TPAP:
		return commontypes.GetTextFromStringFontColourFontStyle(poDisplayInfo.TpapPaymentOptionDisplayInfo().Title(), poDisplayInfo.TpapPaymentOptionDisplayInfo().FontColor(), commontypes.FontStyle_SUBTITLE_S)
	case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_COLLECT:
		return commontypes.GetTextFromStringFontColourFontStyle(poDisplayInfo.CollectPaymentOptionDisplayInfo().Title(), poDisplayInfo.CollectPaymentOptionDisplayInfo().FontColor(), commontypes.FontStyle_SUBTITLE_S)
	case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT:
		return commontypes.GetTextFromStringFontColourFontStyle(poDisplayInfo.IntentPaymentOptionDisplayInfo().Title(), poDisplayInfo.IntentPaymentOptionDisplayInfo().FontColor(), commontypes.FontStyle_SUBTITLE_S)
	case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NEFT_IMPS:
		return commontypes.GetTextFromStringFontColourFontStyle(poDisplayInfo.NeftImpsPaymentOptionDisplayInfo().Title(), poDisplayInfo.NeftImpsPaymentOptionDisplayInfo().FontColor(), commontypes.FontStyle_SUBTITLE_S)
	case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_CARD:
		return commontypes.GetTextFromStringFontColourFontStyle(poDisplayInfo.CardPaymentOptionDisplayInfo().Title(), poDisplayInfo.CardPaymentOptionDisplayInfo().FontColor(), commontypes.FontStyle_SUBTITLE_S)
	case txnPb.PaymentOptionType_PAYMENT_OPTION_TYPE_NETBANKING:
		return commontypes.GetTextFromStringFontColourFontStyle(poDisplayInfo.NetbankingPaymentOptionDisplayInfo().Title(), poDisplayInfo.NetbankingPaymentOptionDisplayInfo().FontColor(), commontypes.FontStyle_SUBTITLE_S)
	default:
		return nil
	}
}
