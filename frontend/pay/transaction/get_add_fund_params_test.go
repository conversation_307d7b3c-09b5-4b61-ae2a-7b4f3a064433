package transaction

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/stretchr/testify/assert"

	rpcPb "github.com/epifi/be-common/api/rpc"
	mockEvents "github.com/epifi/be-common/pkg/events/mocks"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	bankCustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	timelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentInstrumentPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	rewardoffersMock "github.com/epifi/gamma/api/rewards/rewardoffers/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMock "github.com/epifi/gamma/api/savings/mocks"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/external"
	mockTiering "github.com/epifi/gamma/api/tiering/mocks"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingMocks "github.com/epifi/gamma/api/upi/onboarding/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/test"
	mockDataCollector "github.com/epifi/gamma/frontend/tiering/data_collector/mocks"
	feTieringMocks "github.com/epifi/gamma/frontend/tiering/test/mocks"
	mockRelease "github.com/epifi/gamma/pkg/feature/release/mocks"
)

var (
	feConf *config.Config
	gconf  *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	feConf, gconf, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

// nolint
func BenchmarkService_GetAddFundParams(b *testing.B) {
	ctrl := gomock.NewController(b)
	mockActorClient := actorMocks.NewMockActorClient(ctrl)
	mockSavingsClient := savingsMock.NewMockSavingsClient(ctrl)
	mockBankCustClient := bankCustMocks.NewMockBankCustomerServiceClient(ctrl)
	mockOrderClient := orderMocks.NewMockOrderServiceClient(ctrl)
	mockRewardsOfferClient := rewardoffersMock.NewMockRewardOffersClient(ctrl)
	mockTieringAFManager := feTieringMocks.NewMockTieringAddFundsManager(ctrl)
	mockEvaluator := mockRelease.NewMockIEvaluator(ctrl)
	mockUpiOnboardingClient := upiOnboardingMocks.NewMockUpiOnboardingClient(ctrl)
	mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctrl)
	mockEventBroker := mockEvents.NewMockBroker(ctrl)
	defer ctrl.Finish()
	s := &Service{
		conf:                          gconf,
		actorClient:                   mockActorClient,
		savingsClient:                 mockSavingsClient,
		bankCustClient:                mockBankCustClient,
		orderClient:                   mockOrderClient,
		rewardsOfferClient:            mockRewardsOfferClient,
		tieringAddFundsManager:        mockTieringAFManager,
		releaseEvaluator:              mockEvaluator,
		upiOnboardingClient:           mockUpiOnboardingClient,
		accountPiRelationClient:       mockAccountPiClient,
		eventBroker:                   mockEventBroker,
		AddFundsScreenParams:          gconf.AddFundsScreenParams(),
		FirstTransactionRewardsBanner: gconf.FirstTransactionRewardsBanner(),
	}
	for i := 0; i < b.N; i++ {
		// mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
		// 	Status: rpcPb.StatusOk(),
		// 	Actor: &typesPb.Actor{
		// 		Id:       "actor-1",
		// 		EntityId: "entity-1",
		// 	},
		// }, nil)
		mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
			Account: &savingsPb.Account{
				AccountNo: "1234",
			},
		}, nil).Times(1)
		// mockBankCustClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{
		// 	Status: rpcPb.StatusOk(),
		// 	BankCustomer: &bankcust.BankCustomer{
		// 		KycLevelUpdateFlow: bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_DEDUPE_CUSTOMER,
		// 	},
		// }, nil)
		// mockOrderClient.EXPECT().GetOrdersForActor(gomock.Any(), gomock.Any()).Return(&orderPb.GetOrdersForActorResponse{
		// 	Status: rpcPb.StatusOk(),
		// }, nil)
		// mockRewardsOfferClient.EXPECT().GetRewardOffersForScreen(gomock.Any(), gomock.Any()).Return(&rewardoffers.GetRewardOffersForScreenResponse{
		// 	Status: rpcPb.StatusOk(),
		// }, nil)
		mockTieringAFManager.EXPECT().GetAddFundsDetails(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, &tieringPb.TieringPitchAddFundsDetails{
			CurrentTier: tieringEnumPb.Tier_TIER_FI_BASIC,
			CurrentTierMinAmount: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        0,
			},
			NextTier: tieringEnumPb.Tier_TIER_FI_PLUS,
			NextTierMinAmount: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
			CurrentBalanceAmount: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			MinAmount: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        0,
			},
			SuggestedAmount: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
			IsRetentionPitch: false,
		}, nil)
		mockEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
		mockUpiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(&upiOnbPb.GetAccountsResponse{
			Status: rpcPb.StatusOk(),
		}, nil)
		mockUpiOnboardingClient.EXPECT().IsTpapEnabledForActor(gomock.Any(), gomock.Any()).Return(&upiOnbPb.IsTpapEnabledForActorResponse{
			Status:    rpcPb.StatusOk(),
			IsEnabled: true,
		}, nil)
		mockAccountPiClient.EXPECT().GetPiByAccountId(gomock.Any(), gomock.Any()).Return(&accountPIPb.GetPiByAccountIdResponse{
			Status: rpcPb.StatusOk(),
			PaymentInstruments: []*paymentInstrumentPb.PaymentInstrument{
				{
					Type:  paymentInstrumentPb.PaymentInstrumentType_UPI,
					State: paymentInstrumentPb.PaymentInstrumentState_CREATED,
					Identifier: &paymentInstrumentPb.PaymentInstrument_Upi{
						Upi: &paymentInstrumentPb.Upi{
							Vpa:  "test@vpa",
							Name: "test",
						},
					},
				},
			},
		}, nil)
		mockOrderClient.EXPECT().PersistAddFundOption(gomock.Any(), gomock.Any()).Return(&orderPb.PersistAddFundOptionResponse{
			Status: rpcPb.StatusOk(),
		}, nil).AnyTimes()
		mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()
		s.GetAddFundParams(context.Background(), &txnPb.GetAddFundParamsRequest{
			Req: &headerPb.RequestHeader{
				AppVersionCode: 2000,
				Auth: &headerPb.AuthHeader{
					ActorId: "actor-1",
					Device: &commontypes.Device{
						AppVersion: 2000,
						Platform:   commontypes.Platform_ANDROID,
					},
				},
			},
			UiEntryPoint:            txnPb.UIEntryPoint_TRANSFER_IN,
			TransactionUiEntryPoint: timelinePb.TransactionUIEntryPoint_UI_ENTRY_POINT_TRANSFER_IN,
		})
	}
}

func TestService_emitAddFundsParamsLoadedEvent(t *testing.T) {
	tests := []struct {
		name                string
		tierAtTimeResp      *tieringPb.GetTierAtTimeResponse
		tierAtTimeErr       error
		balanceResp         *gmoney.Money
		balanceErr          error
		expectTier          string
		expectBalanceBucket string
	}{
		{
			name: "successfully emits event with correct tier and balance",
			tierAtTimeResp: &tieringPb.GetTierAtTimeResponse{
				TierInfo: &tieringEnumPb.TierInfo{
					Tier: tieringEnumPb.Tier_TIER_FI_PLUS,
				},
			},
			balanceResp: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			expectTier:          "TIER_FI_PLUS",
			expectBalanceBucket: "3001-5000",
		},
		{
			name:           "tiering client error emits event with empty tier",
			tierAtTimeResp: nil,
			tierAtTimeErr:  assert.AnError,
			balanceResp: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        5000,
			},
			expectTier:          "",
			expectBalanceBucket: "3001-5000",
		},
		{
			name: "balance fetch error emits event with empty balance bucket",
			tierAtTimeResp: &tieringPb.GetTierAtTimeResponse{
				TierInfo: &tieringEnumPb.TierInfo{
					Tier: tieringEnumPb.Tier_TIER_FI_PLUS,
				},
			},
			balanceResp:         nil,
			balanceErr:          assert.AnError,
			expectTier:          "TIER_FI_PLUS",
			expectBalanceBucket: "0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			tieringClient := mockTiering.NewMockTieringClient(ctrl)
			dataCollector := mockDataCollector.NewMockDataCollector(ctrl)
			eventBroker := mockEvents.NewMockBroker(ctrl)

			s := &Service{
				tieringClient: tieringClient,
				dataCollector: dataCollector,
				eventBroker:   eventBroker,
			}

			ctx := context.Background()
			actorId := "test-actor"
			uiEntryPoint := txnPb.UIEntryPoint_ALL_PLANS_JOIN_PLUS.String()
			isTieringPitchEnabled := true

			tieringClient.EXPECT().GetTierAtTime(gomock.Any(), gomock.Any()).Return(tt.tierAtTimeResp, tt.tierAtTimeErr)
			dataCollector.EXPECT().GetBalance(gomock.Any(), actorId).Return(tt.balanceResp, tt.balanceErr)

			eventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).DoAndReturn(
				func(_ context.Context, evt interface{}) error {
					return nil
				},
			)

			s.emitAddFundsParamsLoadedEvent(ctx, actorId, uiEntryPoint, isTieringPitchEnabled, nil)
		})
	}
}
