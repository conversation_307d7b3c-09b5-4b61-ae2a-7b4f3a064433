package networth

import (
	"context"
	"fmt"
	"strconv"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPb "github.com/epifi/be-common/pkg/money"

	ui2 "github.com/epifi/gamma/api/frontend/investment/ui"
	"github.com/epifi/gamma/api/insights/networth/enums"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"

	"github.com/epifi/gamma/frontend/insights/networth/data_fetcher"

	networthBePb "github.com/epifi/gamma/api/insights/networth"

	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/common"

	enumsPb "github.com/epifi/gamma/api/frontend/insights/networth/enums"
	feNetworthUi "github.com/epifi/gamma/api/frontend/insights/networth/ui"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	feErrors "github.com/epifi/gamma/api/frontend/errors"
	networthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"

	networthCommon "github.com/epifi/gamma/frontend/insights/networth/common"
	"github.com/epifi/gamma/frontend/insights/networth/generator/widget"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"

	"github.com/epifi/gamma/api/frontend/header"

	feNetworthPb "github.com/epifi/gamma/api/frontend/insights/networth"
)

func (s *Service) GetConnectMoreAssetsScreen(ctx context.Context, req *feNetworthPb.GetConnectMoreAssetsScreenRequest) (*feNetworthPb.GetConnectMoreAssetsScreenResponse, error) {
	connectMoreAssetsResp, err := s.getConnectMoreAssetsScreenResponse(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to get wealth builder landing section: ", zap.Error(err))
		return s.getErrorResponse("failed to get wealth builder landing section"), nil
	}

	return connectMoreAssetsResp, nil
}

func (s *Service) getConnectMoreAssetsScreenResponse(ctx context.Context, req *feNetworthPb.GetConnectMoreAssetsScreenRequest) (*feNetworthPb.GetConnectMoreAssetsScreenResponse, error) {
	var (
		wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection
		sectionCreationErr           error
		dashboardHeader              *feNetworthUi.DashboardHeader
		toggleComponent              *ui2.AssetLandingToggleComponent
		liabilityCta                 *ui.IconTextComponent
	)

	actorId := req.GetReq().GetAuth().GetActorId()
	networthDashboardConfig, err := s.networthConfig.GetNetworthDashboardConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get networth dashboard config while connecting all assets: %w", err)
	}

	isConnectMoreScreenV2Enabled := s.isConnectMoreScreenV2Enabled(ctx)
	switch {
	case isConnectMoreScreenV2Enabled:
		categoriesDataMap, err := s.dataFetcher.GetCategoriesValue(ctx, actorId, enums.NetWorthDashBoardType_NET_WORTH)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get categories value while connecting all assets")
		}
		wealthBuilderLandingSections, sectionCreationErr = s.getWealthBuilderWidgetsV2(ctx, actorId, networthDashboardConfig, categoriesDataMap)
		dashboardHeader = s.getDashboardHeader(networthDashboardConfig, categoriesDataMap)
		liabilityCta = ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.LiabilitiesInfoCta, colors.ColorSupportingCherry900, commontypes.FontStyle_SUBTITLE_XS)).
			WithContainerBackgroundColor(colors.ColorSupportingCherry100).WithContainerCornerRadius(40).WithContainerPadding(4, 12, 4, 12)
		toggleComponent = buildToggleComponent(wealthBuilderLandingSections)
	default:
		wealthBuilderLandingSections, sectionCreationErr = s.getWealthBuilderWidgetsV1(ctx, req, actorId, networthDashboardConfig)
	}
	if sectionCreationErr != nil {
		return nil, fmt.Errorf("failed to create wealth builder landing sections: %w", sectionCreationErr)
	}

	connectMoreAssetsResp := &feNetworthPb.GetConnectMoreAssetsScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Title:                        commontypes.GetTextFromStringFontColourFontStyle("Link all your finances in one place & unlock  insights", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		CardImage:                    commontypes.GetVisualElementImageFromUrl(common.CardImage).WithProperties(&commontypes.VisualElementProperties{Width: 332, Height: 80}),
		WealthBuilderLandingSections: wealthBuilderLandingSections,
		DashboardHeader:              dashboardHeader,
		ToggleComponent:              toggleComponent,
		LiabilityTrackingCta:         liabilityCta,
		FloatingActionButton:         s.deeplinkBuilder.GetTalkToAIFloatingIconWithDeeplink(ctx, req.GetReq().GetAuth().GetActorId(), networth.Entrypoint_ENTRYPOINT_WB_DASHBOARD),
	}

	return connectMoreAssetsResp, nil
}

func (s *Service) getWealthBuilderWidgetsV1(ctx context.Context, req *feNetworthPb.GetConnectMoreAssetsScreenRequest, actorId string, networthDashboardConfig *feNetworthPb.NetWorthDashboardConfig) ([]*feNetworthUi.WealthBuilderLandingSection, error) {
	var assetWidgets, liabilityWidgets []*feNetworthUi.WidgetV2
	// Collecting widgets for assets
	for _, assetType := range req.GetRequestParams().GetAssetTypes() {
		widgetData := s.getWidgetDataForAssetType(assetType, networthDashboardConfig)
		wbLandingWidget, widgetErr := s.getWbLandingWidget(ctx, actorId, widgetData)
		if widgetErr != nil {
			return nil, fmt.Errorf("failed to generate wealth builder zero widget for asset type: %v, error: %w", assetType, widgetErr)
		}
		if wbLandingWidget != nil {
			assetWidgets = append(assetWidgets, wbLandingWidget)
		}
	}

	// Collecting widgets for liabilities
	for _, liabilityType := range req.GetRequestParams().GetLiabilityTypes() {
		widgetData := s.getWidgetDataForLiabilityType(liabilityType, networthDashboardConfig)
		wbLandingWidget, widgetErr := s.getWbLandingWidget(ctx, actorId, widgetData)
		if widgetErr != nil {
			return nil, fmt.Errorf("failed to generate wealth builder zero widget for liability type: %v, error: %w", liabilityType, widgetErr)
		}
		if wbLandingWidget != nil {
			liabilityWidgets = append(liabilityWidgets, wbLandingWidget)
		}
	}
	var wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection
	for _, section := range networthDashboardConfig.GetSections() {
		if section.GetSectionType().String() == enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS.String() {
			assetSection := &feNetworthUi.WealthBuilderLandingSection{
				Id:          section.GetWealthLandingSectionId(),
				SectionType: enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS,
				SectionHeaderGenericState: &feNetworthUi.SectionHeaderGenericState{
					Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.AssetsSectionHeader, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)),
				},
				Widgets: assetWidgets,
			}
			wealthBuilderLandingSections = append(wealthBuilderLandingSections, assetSection)
		}
		if section.GetSectionType().String() == enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES.String() {
			liabilitySection := &feNetworthUi.WealthBuilderLandingSection{
				Id:          section.GetWealthLandingSectionId(),
				SectionType: enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES,
				SectionHeaderGenericState: &feNetworthUi.SectionHeaderGenericState{
					Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.LoanSectionHeader, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)),
				},
				Widgets: liabilityWidgets,
			}
			wealthBuilderLandingSections = append(wealthBuilderLandingSections, liabilitySection)
		}
	}

	return wealthBuilderLandingSections, nil
}

func (s *Service) getWealthBuilderWidgetsV2(ctx context.Context, actorId string, networthDashboardConfig *feNetworthPb.NetWorthDashboardConfig, categoriesDataMap map[feNetworthPb.NetworthCategory]*data_fetcher.CategoryData) ([]*feNetworthUi.WealthBuilderLandingSection, error) {
	categoriesStatusMap, err := s.dataFetcher.GetCategoriesStatus(ctx, actorId, categoriesDataMap, enums.NetWorthDashBoardType_NET_WORTH)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get categories status while connecting all assets")
	}
	refreshDetailsRes, err := s.networthClient.GetNetWorthInstrumentsRefreshDetails(ctx, &networthBePb.GetNetWorthInstrumentsRefreshDetailsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(refreshDetailsRes, err); grpcErr != nil {
		logger.Error(ctx, "failed to get net worth instrument refresh details while connecting all assets", zap.Error(err))
	}
	refreshDetailsMap := s.dataFetcher.ConvertRefreshDetailsToMap(ctx, refreshDetailsRes.GetInstrumentRefreshSummary())

	// Build all dashboard sections (assets, liabilities)
	var wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection
	isAllWidgetsRequired := true
	for _, sectionConfig := range networthDashboardConfig.GetSections() {
		if sectionConfig.GetSectionType() == feNetworthPb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS ||
			sectionConfig.GetSectionType() == feNetworthPb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES {
			generatedSection, sectionErr := s.sectionGenerator.GenerateWealthBuilderLandingSection(ctx, actorId, sectionConfig, categoriesDataMap, categoriesStatusMap, refreshDetailsMap, isAllWidgetsRequired)
			if sectionErr != nil {
				return nil, fmt.Errorf("failed to generate net worth section while connecting all assets %s: %w", sectionConfig.GetSectionType(), sectionErr)
			}
			wealthBuilderLandingSections = append(wealthBuilderLandingSections, generatedSection)
		}
	}

	return wealthBuilderLandingSections, nil
}

func (s *Service) getWidgetDataForAssetType(assetType string, netWorthDashboardConfig *feNetworthPb.NetWorthDashboardConfig) *widget.WidgetDataDetails {
	widgetData := s.createWidgetData()

	for _, section := range netWorthDashboardConfig.GetSections() {
		if section.GetSectionType().String() == enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS.String() {
			for _, widgetDetails := range section.GetWidgets() {
				assetEnum := networthBePb.AssetType(networthBePb.AssetType_value[assetType])
				if widgetDetails.GetCategory() == common.AssetTypeToCategoryMap[assetEnum] {
					widgetData.WidgetData.Name = widgetDetails.GetCategory()
					widgetData.WidgetConfig = widgetDetails
					widgetData.NetWorthCategoryType = common.AssetTypeToCategoryMap[assetEnum]
					break
				}
			}
		}
	}
	return widgetData
}

func (s *Service) getWidgetDataForLiabilityType(liabilityType string, netWorthDashboardConfig *feNetworthPb.NetWorthDashboardConfig) *widget.WidgetDataDetails {
	widgetData := s.createWidgetData()

	for _, section := range netWorthDashboardConfig.GetSections() {
		if section.GetSectionType().String() == enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES.String() && liabilityType != "" {
			for _, widgetDetails := range section.GetWidgets() {
				liabilityEnum := networthBePb.LiabilityType(networthBePb.LiabilityType_value[liabilityType])
				if widgetDetails.GetCategory() == common.LiabilityTypeToCategoryMap[liabilityEnum] {
					widgetData.WidgetData.Name = widgetDetails.GetCategory()
					widgetData.WidgetConfig = widgetDetails
					widgetData.NetWorthCategoryType = common.LiabilityTypeToCategoryMap[liabilityEnum]
					break
				}
			}
		}
	}
	return widgetData
}

func (s *Service) createWidgetData() *widget.WidgetDataDetails {
	widgetData := &widget.WidgetDataDetails{}
	widgetData.WidgetWidth = networthCommon.SecondaryWidgetWidth
	widgetData.WidgetState = feNetworthPb.NetworthWidgetState_NETWORTH_WIDGET_STATE_ACTIVE
	widgetData.WidgetData = &data_fetcher.CategoryData{}
	widgetData.WidgetData.Value = moneyPb.ZeroINR().GetPb()
	widgetData.WidgetData.ComputationStatus = networthBePb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND
	widgetData.NetWorthCategoryStatus = feNetworthPb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_UNINITIALIZED
	return widgetData
}

func (s *Service) getWbLandingWidget(ctx context.Context, actorId string, widgetData *widget.WidgetDataDetails) (*feNetworthUi.WidgetV2, error) {
	widgetGenFactory, genErr := s.widgetGeneratorFactory.GetGenerator(widgetData.WidgetConfig)
	if genErr != nil {
		return nil, fmt.Errorf("failed to get wealth builder widget generator for category while connecting all assets: %s, err: %w", widgetData.WidgetConfig.GetCategory(), genErr)
	}
	wbLandingWidget, widgetErr := widgetGenFactory.GenerateZeroStateWealthBuilderLandingWidget(ctx, actorId, widgetData)
	if widgetErr != nil {
		return nil, fmt.Errorf("failed to generate wealth builder zero widget for category while connecting all assets: %v, error: %w", widgetData, widgetErr)
	}
	return wbLandingWidget, nil
}

func (s *Service) getErrorResponse(errStr string) *feNetworthPb.GetConnectMoreAssetsScreenResponse {
	return &feNetworthPb.GetConnectMoreAssetsScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg(errStr),
			// add error view
			ErrorView: &feErrors.ErrorView{
				Type: feErrors.ErrorViewType_FULL_SCREEN,
				Options: &feErrors.ErrorView_FullScreenErrorView{
					FullScreenErrorView: &feErrors.FullScreenErrorView{
						Title:    "Something went wrong",
						Subtitle: "We were unable to load your assets. Please try again in some time.",
						Ctas: []*feErrors.CTA{
							{
								Text:         "Ok, got it",
								Action:       networthDeeplink.HomeDeeplink(),
								DisplayTheme: feErrors.CTA_PRIMARY,
							},
						},
						ImageUrl: "https://epifi-icons.pointz.in/networth/thundercloud.png",
					},
				},
			},
		},
	}
}

//nolint:gosec
func (s *Service) isConnectMoreScreenV2Enabled(ctx context.Context) bool {
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	if appPlatform == commontypes.Platform_ANDROID && uint32(appVersion) >= s.feConfig.NetworthConfig().AppVersionsForConnectMoreAssetsScreenV2().MinVersionAndroid() {
		return true
	}
	if appPlatform == commontypes.Platform_IOS && uint32(appVersion) >= s.feConfig.NetworthConfig().AppVersionsForConnectMoreAssetsScreenV2().MinVersionIos() {
		return true
	}
	return false
}

func (s *Service) getDashboardHeader(networthDashboardConfig *feNetworthPb.NetWorthDashboardConfig, categoriesDataMap map[feNetworthPb.NetworthCategory]*data_fetcher.CategoryData) *feNetworthUi.DashboardHeader {
	assetSectionConfig := networthDashboardConfig.GetSections()[0]
	for _, widgetConfig := range assetSectionConfig.GetWidgets() {
		category := widgetConfig.GetCategory()
		if categoryData, exists := categoriesDataMap[category]; exists {
			if categoryData.ComputationStatus == networthBePb.ComputationStatus_COMPUTATION_STATUS_SUCCESS {
				return &feNetworthUi.DashboardHeader{
					DashboardHeader: &feNetworthUi.DashboardHeader_SectionHeaderConnectedState{
						SectionHeaderConnectedState: &feNetworthUi.DashboardHeaderConnectedState{
							Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.YourWealth, colors.ColorSupportingJade100, commontypes.FontStyle_HEADLINE_M).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
							WealthDisplayDetails: &feNetworthUi.WealthDisplayDetails{
								CurrencySymbol:    commontypes.GetTextFromStringFontColourFontStyle("₹", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_CURRENCY_XL),
								TotalDisplayValue: commontypes.GetTextFromStringFontColourFontStyle("0", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_NUMBER_3XL),
							},
							Tags: nil,
							VisibilityDetails: &feNetworthUi.VisibilityDetails{
								Hide: commontypes.GetVisualElementFromUrlHeightAndWidth(common.InfoHide, 28, 20),
								Show: commontypes.GetVisualElementFromUrlHeightAndWidth(common.InfoShow, 28, 20),
							},
						},
					},
				}
			}
		}
	}

	return &feNetworthUi.DashboardHeader{
		DashboardHeader: &feNetworthUi.DashboardHeader_SectionHeaderZeroState{
			SectionHeaderZeroState: &feNetworthUi.DashboardHeaderZeroState{
				Title: commontypes.GetTextFromStringFontColourFontStyle(common.ZeroStateTitle, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_XL).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
			},
		},
	}
}

// buildToggleComponent : Helper method to build AssetLandingToggleComponent used on liabilities section
func buildToggleComponent(wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection) *ui2.AssetLandingToggleComponent {
	noOfAssets, noOfLiabilities := getAssetAndLiabilityCount(wealthBuilderLandingSections)
	toggleComponent := &ui2.AssetLandingToggleComponent{
		ToggleValue_1:        buildToggleComponentToggleValue("Assets", ui2.LandingToggleValue_LANDING_TOGGLE_VALUE_TRACK.String(), noOfAssets),
		ToggleValue_2:        buildToggleComponentToggleValue("Liabilities", ui2.LandingToggleValue_LANDING_TOGGLE_VALUE_HIDE.String(), noOfLiabilities),
		SelectBgColor:        colors.ColorDarkBase,
		BgColor:              colors.ColorDarkLayer2,
		SelectBorderProperty: getBorderProperty(),
		BorderProperty:       getBorderProperty(),
	}
	toggleComponent.GetToggleValue_1().IsSelected = true
	return toggleComponent
}

// buildToggleComponentToggleValue : Helper method responsible to create toggle values for buildToggleComponent for given displayValue and value
func buildToggleComponentToggleValue(displayValue, value string, countOfWidgets int32) *ui2.AssetLandingToggleValue {
	countOfWidgetsInString := strconv.FormatInt(int64(countOfWidgets), 10) // base 10
	rightComponent := ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(countOfWidgetsInString, colors.ColorSnow, commontypes.FontStyle_HEADLINE_XS)).
		WithContainer(20, 20, 50, colors.ColorOnDarkDisabled700)
	selectedRightComponent := ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(countOfWidgetsInString, colors.ColorSnow, commontypes.FontStyle_HEADLINE_XS)).
		WithContainer(20, 20, 50, common.ColorSentiment)
	if countOfWidgets == 0 {
		rightComponent = nil
		selectedRightComponent = nil
	}
	return &ui2.AssetLandingToggleValue{
		Text:                   commontypes.GetTextFromStringFontColourFontStyle(displayValue, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_M),
		SelectedText:           commontypes.GetTextFromStringFontColourFontStyle(displayValue, colors.ColorSnow, commontypes.FontStyle_HEADLINE_M),
		ToggleValue:            value,
		RightComponent:         rightComponent,
		SelectedRightComponent: selectedRightComponent,
	}
}

func getAssetAndLiabilityCount(wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection) (int32, int32) {
	noOfAssets := int32(0)
	noOfLiabilities := int32(0)
	for _, section := range wealthBuilderLandingSections {
		for _, widgetV2 := range section.GetWidgets() {
			if widgetV2.GetState() == feNetworthUi.WidgetStateV2_WIDGET_STATE_V2_INITIALIZED {
				switch section.GetSectionType() {
				case enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS:
					noOfAssets++
				case enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES:
					noOfLiabilities++
				}
			}
		}
	}
	return noOfAssets, noOfLiabilities
}

func getBorderProperty() *properties.BorderProperty {
	return &properties.BorderProperty{
		BorderThickness: 1,
		BorderColor:     common.WealthBuilderComponentsBorderColor,
		CornerRadius:    24,
	}
}
