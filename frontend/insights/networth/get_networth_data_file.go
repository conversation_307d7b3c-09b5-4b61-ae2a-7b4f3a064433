package networth

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	goutils "github.com/epifi/be-common/pkg/go_utils"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"
	feNetworthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthEnums "github.com/epifi/gamma/api/insights/networth/enums"
	"github.com/epifi/gamma/frontend/insights/networth/events"
)

// GetNetworthDataFile generates a comprehensive financial data file containing user's networth summary,
// mutual fund holdings, account aggregator data, credit reports, EPF details, and transaction history (includes both bank and mf transactions).
// Returns base64-encoded JSON data as a downloadable text file, with concurrent data fetching for performance.
func (s *Service) GetNetworthDataFile(ctx context.Context, req *feNetworthPb.GetNetworthDataFileRequest) (*feNetworthPb.GetNetworthDataFileResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	// check if net worth data file types array is non-empty, if yes, then simply convert and share it BE rpc
	networthDataFileTypes := make([]networthEnums.NetworthDataFileType, 0)
	if len(req.GetNetWorthDataFileTypes()) != 0 {
		for _, fileType := range req.GetNetWorthDataFileTypes() {
			networthDataFileTypes = append(networthDataFileTypes, goutils.Enum(fileType, networthEnums.NetworthDataFileType_value, networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_UNSPECIFIED))
		}
	}

	var networthDataFileType networthEnums.NetworthDataFileType
	if req.GetReqPayload() != "" {
		var getFileTypeErr error
		networthDataFileType, getFileTypeErr = s.getNetWorthDataFileType(req.GetReqPayload())
		if getFileTypeErr != nil {
			logger.Error(ctx, "error getting file enum type", zap.Error(getFileTypeErr))
			return &feNetworthPb.GetNetworthDataFileResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInvalidArgument(),
				},
			}, nil
		}
	}
	networthDataFileTypes = append(networthDataFileTypes, networthDataFileType)
	networthDataFileTypes = filterUnspecifiedFileTypes(networthDataFileTypes)
	// default to net worth data if both payload and array fields are empty
	// this can happen for a set of app versions before adding req payload field
	if len(networthDataFileTypes) == 0 {
		networthDataFileTypes = []networthEnums.NetworthDataFileType{networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA}
	}

	networthDataRes, networthDataErr := s.networthClient.GetNetworthDataFile(ctx, &networthPb.GetNetworthDataFileRequest{
		ActorId:               actorId,
		NetWorthDataFileTypes: networthDataFileTypes,
	})
	if rpcErr := epifigrpc.RPCError(networthDataRes, networthDataErr); rpcErr != nil {
		logger.Error(ctx, "error in getting networth data file", zap.Error(rpcErr))
		return &feNetworthPb.GetNetworthDataFileResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewNetworthDataFileGenerationEvent(actorId))
	})

	return &feNetworthPb.GetNetworthDataFileResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NetworthFile: networthDataRes.GetNetworthFile(),
	}, nil
}

func filterUnspecifiedFileTypes(networthDataFileTypes []networthEnums.NetworthDataFileType) []networthEnums.NetworthDataFileType {
	newFileTypes := make([]networthEnums.NetworthDataFileType, 0)
	for _, fileType := range networthDataFileTypes {
		if fileType != networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_UNSPECIFIED {
			newFileTypes = append(newFileTypes, fileType)
		}
	}
	newFileTypes = lo.Uniq(newFileTypes)
	return newFileTypes
}

func (s *Service) getNetWorthDataFileType(payload string) (networthEnums.NetworthDataFileType, error) {
	reqPayload, err := s.getNetworthDataFilePurposeFromPayload(payload)
	if err != nil {
		return networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_UNSPECIFIED, errors.Wrap(err, "error in getting net worth data file purpose")
	}
	var networthDataFileType networthEnums.NetworthDataFileType
	switch reqPayload.GetNetworthDataFileType() {
	case feNetworthPb.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA:
		networthDataFileType = networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NETWORTH_DATA
	case feNetworthPb.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA:
		networthDataFileType = networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA
	default:
		return networthEnums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_UNSPECIFIED, nil
	}
	return networthDataFileType, nil
}

func (s *Service) getNetworthDataFilePurposeFromPayload(requestPayload string) (*feNetworthPb.NetworthDataFileRequestPayload, error) {
	fileRequestPayload := &feNetworthPb.NetworthDataFileRequestPayload{}
	if requestPayload == "" {
		return fileRequestPayload, nil
	}
	unmarshalOptions := protojson.UnmarshalOptions{DiscardUnknown: true}
	if unmarshalErr := unmarshalOptions.Unmarshal([]byte(requestPayload), fileRequestPayload); unmarshalErr != nil {
		return nil, errors.Wrap(unmarshalErr, "error in unmarshaling payload")
	}
	return fileRequestPayload, nil
}
