package wealth_analyser_report

import (
	"context"
	"fmt"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"

	investmentBePb "github.com/epifi/gamma/api/analyser/investment"
	"github.com/epifi/gamma/api/frontend/deeplink"
	feSecretsPb "github.com/epifi/gamma/api/frontend/insights/secrets"
	netWorthPb "github.com/epifi/gamma/api/insights/networth"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	netWorthModelPb "github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	deeplinkSecrets "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	netWorthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"
	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
)

type MfDataProvider struct {
	investmentAnalyticsClient investmentBePb.InvestmentAnalyticsClient
	refreshDeeplink           netWorthDeeplink.ISecretRefreshDeeplinkProvider
	netWorthClient            networthBePb.NetWorthClient
	deeplinkBuilder           deeplink_builder.IDeeplinkBuilder
}

func NewMfDataProvider(
	investmentAnalyticsClient investmentBePb.InvestmentAnalyticsClient,
	refreshDeeplink netWorthDeeplink.ISecretRefreshDeeplinkProvider,
	netWorthClient networthBePb.NetWorthClient,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
) *MfDataProvider {
	return &MfDataProvider{
		investmentAnalyticsClient: investmentAnalyticsClient,
		refreshDeeplink:           refreshDeeplink,
		netWorthClient:            netWorthClient,
		deeplinkBuilder:           deeplinkBuilder,
	}
}

var MfDataProviderWireSet = wire.NewSet(NewMfDataProvider)

func (m *MfDataProvider) GetWealthAnalyserReport(ctx context.Context, req *WealthAnalyserReportRequest) (*WealthAnalyserReportResponse, error) {
	grp, grpCtx := errgroup.WithContext(ctx)
	var mfAnalytics *investmentBePb.GetMFSchemeAnalyticsResponse
	var mfNetWorthSummary *networthBePb.GetNetWorthValueResponse
	var redirectDeepLink *deeplink.Deeplink
	grp.Go(func() error {
		var err error
		mfAnalytics, err = m.investmentAnalyticsClient.GetMFSchemeAnalytics(grpCtx, &investmentBePb.GetMFSchemeAnalyticsRequest{
			ActorId: req.ActorId,
		})
		if err1 := epifigrpc.RPCError(mfAnalytics, err); err1 != nil {
			return fmt.Errorf("failed to fetch mf scheme analytics: %w", err1)
		}
		return nil
	})

	grp.Go(func() error {
		var err error
		mfNetWorthSummary, err = m.netWorthClient.GetNetWorthValue(grpCtx, &networthBePb.GetNetWorthValueRequest{
			ActorId:    req.ActorId,
			AssetTypes: []netWorthPb.AssetType{netWorthPb.AssetType_ASSET_TYPE_MUTUAL_FUND},
		})
		if err1 := epifigrpc.RPCError(mfNetWorthSummary, err); err1 != nil {
			return errors.Wrap(err, "unable to fetch net worth summary")
		}
		return nil
	})

	grp.Go(func() error {
		var err error
		redirectDeepLink, err = m.refreshDeeplink.GetRedirectionDeeplinkForSecretRefresh(grpCtx, &netWorthDeeplink.RefreshSecretParams{
			ActorId:              req.ActorId,
			RedirectDeepLink:     getWealthAnalyserDeeplink(req.ReportType.String()),
			NetworthRefreshAsset: netWorthModelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
		})
		if err != nil && !errors.Is(err, secretErrors.NoRefreshRequired) {
			return errors.Wrap(err, "failed to fetch redirection deeplink")
		}
		return nil
	})

	if err := grp.Wait(); err != nil {
		if mfAnalytics.GetStatus().IsRecordNotFound() {
			return &WealthAnalyserReportResponse{
				RedirectDeeplink: getMfImportScreenDeeplink(),
			}, nil
		}
		logger.Error(ctx, "failed to get data for mf wealth analyser report", zap.Error(err))
		return nil, errors.Wrap(err, "failed to fetch data for mf wealth analyser report")
	}

	portfolioDetails := mfAnalytics.GetPortfolio().GetPortfolio().GetPortfolioDetails()
	eventPropertiesDashboard := getEventPropertiesWithCta(WealthAnalyserMf)
	portFolioValue := mfNetWorthSummary.GetAssetValues()[0].GetValue()

	wealthAnalyserReportDashboard := &feSecretsPb.WealthAnalyserReportDashboard{
		TopCenterValue:    getTopCenterValue(portFolioValue, MutualFundValueTitle),
		LeftBottomValue:   getLeftBottomValue(portfolioDetails.GetInvestedValue()),
		CentreBottomValue: getCentreBottomValue(portfolioDetails.GetUnrealisedReturns()),
		RightBottomValue:  getRightBottomValue(portfolioDetails.GetXIRR().GetValue()),
		BgColor:           colors.ColorBlack,
		DividerColor:      dividerColor,
		SeeAllCta:         getSeeAllCta(fmt.Sprintf("SEE %d FUNDS", len(mfAnalytics.GetSchemes())), deeplinkSecrets.GetSecretAnalyserScreenDeeplink(TopPerformingMfScheme)),
		EventProperties:   eventPropertiesDashboard,
		Footer:            getDashboardFooter(),
	}

	eventPropertiesReport := getEventProperties(WealthAnalyserMf)

	wealthAnalyserReport := &feSecretsPb.WealthAnalyserReport{
		Dashboard:            wealthAnalyserReportDashboard,
		SecretsCollection:    req.SecretLandingPage,
		Footer:               getFooter(),
		EventProperties:      eventPropertiesReport,
		FloatingActionButton: m.deeplinkBuilder.GetTalkToAIFloatingIconWithDeeplink(ctx, req.ActorId, networth.Entrypoint_ENTRYPOINT_MF_REPORT_PAGE),
	}

	switch {
	case moneyPb.IsZero(portfolioDetails.GetPortfolioValue()):
		wealthAnalyserReport.RefreshBanner = getRefreshBanner(NoMutualFundsRefreshBanner, WealthAnalyserMf, getMfImportScreenDeeplink())
		wealthAnalyserReport.EventProperties[ConnectionState] = "No"
	case redirectDeepLink != nil:
		wealthAnalyserReport.RefreshBanner = getDataStaleRefreshBanner(DataStaleText, WealthAnalyserMf, redirectDeepLink)
	default:
		// do nothing
	}

	return &WealthAnalyserReportResponse{
		WealthAnalyserReport: wealthAnalyserReport,
	}, nil
}
