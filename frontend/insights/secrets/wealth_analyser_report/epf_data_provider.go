package wealth_analyser_report

import (
	"context"
	"fmt"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	feSecretsPb "github.com/epifi/gamma/api/frontend/insights/secrets"
	"github.com/epifi/gamma/api/insights/epf"
	epfPb "github.com/epifi/gamma/api/insights/epf"
	netWorthModelPb "github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/api/typesv2/ui"
	feEpf "github.com/epifi/gamma/frontend/insights/epf"
	netWorthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"
	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
)

type EpfDataProvider struct {
	epfClient       epfPb.EpfClient
	refreshDeeplink netWorthDeeplink.ISecretRefreshDeeplinkProvider
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder
}

func NewEpfDataProvider(
	epfClient epfPb.EpfClient,
	refreshDeeplink netWorthDeeplink.ISecretRefreshDeeplinkProvider,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
) *EpfDataProvider {
	return &EpfDataProvider{
		epfClient:       epfClient,
		refreshDeeplink: refreshDeeplink,
		deeplinkBuilder: deeplinkBuilder,
	}
}

var EpfDataProviderWireSet = wire.NewSet(NewEpfDataProvider)

func (e *EpfDataProvider) GetWealthAnalyserReport(ctx context.Context, req *WealthAnalyserReportRequest) (*WealthAnalyserReportResponse, error) {
	getUanAccountsResponse, getUanAccountsResponseErr := e.epfClient.GetUANAccounts(ctx, &epf.GetUANAccountsRequest{
		ActorId: req.ActorId,
	})
	if err := epifigrpc.RPCError(getUanAccountsResponse, getUanAccountsResponseErr); err != nil {
		if getUanAccountsResponse.GetStatus().IsRecordNotFound() {
			return &WealthAnalyserReportResponse{
				RedirectDeeplink: getEpfImportScreenDeepLink(),
			}, nil
		}

		logger.Error(ctx, "error while calling GetUANAccounts", zap.Error(err))
		return nil, fmt.Errorf("rpc error while calling GetUANAccounts")
	}

	totalPfBalance, latestSyncTs, err := feEpf.CalculateTotalBalanceAndLatestSync(getUanAccountsResponse.GetUanAccounts())
	if err != nil {
		logger.Error(ctx, "error while calculating total amount and latest sync time", zap.Error(err))
		return nil, fmt.Errorf("error while calculating total amount and latest sync time")
	}

	redirectDeepLink, err := e.refreshDeeplink.GetRedirectionDeeplinkForSecretRefresh(ctx, &netWorthDeeplink.RefreshSecretParams{
		ActorId:              req.ActorId,
		RedirectDeepLink:     getWealthAnalyserDeeplink(req.ReportType.String()),
		NetworthRefreshAsset: netWorthModelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
	})
	if err != nil && !errors.Is(err, secretErrors.NoRefreshRequired) {
		logger.Error(ctx, "error while GetRedirectionDeeplinkForSecretRefresh", zap.Error(err))
		return nil, errors.Wrap(err, "failed to fetch redirection deeplink")
	}

	eventPropertiesDashboard := getEventPropertiesWithCta(WealthAnalyserEpf)
	eventPropertiesReport := getEventProperties(WealthAnalyserEpf)

	wealthAnalyserReport := &feSecretsPb.WealthAnalyserReport{
		Dashboard: &feSecretsPb.WealthAnalyserReportDashboard{
			TopCenterValue: getTopCenterValue(money.AmountINR(int64(totalPfBalance)).GetPb(), EpfValueTitle),
			BgColor:        colors.ColorBlack,
			SeeAllCta:      getSeeAllCta(fmt.Sprintf("SEE %d ACCOUNTS", len(getUanAccountsResponse.GetUanAccounts())), &deeplink.Deeplink{Screen: deeplink.Screen_EPF_DASHBOARD}),
			LastSync: ui.NewITC().WithTexts(commontypes.
				GetTextFromStringFontColourFontStyle(fmt.Sprintf(lastSyncedInfo, latestSyncTs.Format("2 January, 2006")), colors.ColorOnDarkDisabled700, commontypes.FontStyle_OVERLINE_2XS_CAPS)),
			EventProperties: eventPropertiesDashboard,
			Footer:          getDashboardFooter(),
		},
		SecretsCollection:    req.SecretLandingPage,
		Footer:               getFooter(),
		EventProperties:      eventPropertiesReport,
		FloatingActionButton: e.deeplinkBuilder.GetTalkToAIFloatingIconWithDeeplink(ctx, req.ActorId, networth.Entrypoint_ENTRYPOINT_EPF_REPORT_PAGE),
	}

	switch {
	case totalPfBalance <= 0:
		wealthAnalyserReport.RefreshBanner = getRefreshBanner(NoEpfRefreshBanner, WealthAnalyserEpf, getEpfImportScreenDeepLink())
		wealthAnalyserReport.EventProperties[ConnectionState] = "No"
	case redirectDeepLink != nil:
		wealthAnalyserReport.RefreshBanner = getDataStaleRefreshBanner(DataStaleText, WealthAnalyserEpf, redirectDeepLink)
	}

	return &WealthAnalyserReportResponse{
		WealthAnalyserReport: wealthAnalyserReport,
	}, nil
}
