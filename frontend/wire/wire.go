//go:build wireinject

// The build tag makes sure the stub is not built in the final build.
//
//go:generate wire
package wire

import (
	"go.uber.org/zap"

	"github.com/epifi/gamma/analyser/dataprovider"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	analyserVariablesPb "github.com/epifi/gamma/api/analyser/variables"
	totpPb "github.com/epifi/gamma/api/auth/totp"
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	"github.com/epifi/gamma/frontend/auth/totp"
	portfolioTrackerBuilder "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder"
	portfolioTrackerUi "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/portfolio_tracker_ui"
	assetWiseDistributionBuilder "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/portfolio_tracker_ui/asset_wise_distribution_builder"
	strategyImpl "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"github.com/google/wire"

	cardcipb "github.com/epifi/gamma/api/card/currencyinsights"
	upPb "github.com/epifi/gamma/api/comms/user_preference"
	datasharingpb "github.com/epifi/gamma/api/datasharing"
	feTicketPb "github.com/epifi/gamma/api/frontend/cx/ticket"
	"github.com/epifi/gamma/api/frontend/inapphelp/faq"
	userDeclarationPb "github.com/epifi/gamma/api/insights/user_declaration"
	salaryestimationpb "github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/frontend/datasharing"
	"github.com/epifi/gamma/frontend/digilocker"
	insightsCalculator "github.com/epifi/gamma/frontend/insights/calculator"
	formbuilderFactory "github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/factory"
	networthFromBuilder "github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/networth"
	"github.com/epifi/gamma/frontend/insights/secrets/componentbuilder"
	wealthAnalyserFe "github.com/epifi/gamma/frontend/insights/secrets/wealth_analyser_report"
	fePaySearch "github.com/epifi/gamma/frontend/pay/search/provider"
	consent2 "github.com/epifi/gamma/frontend/preapprovedloan/datacollectors/consent"
	palFeStockGuradianDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/stockguardian"
	"github.com/epifi/gamma/frontend/salaryestimation"
	"github.com/epifi/gamma/frontend/smsfetcher"
	epfSmsStore "github.com/epifi/gamma/frontend/smsfetcher/epf"
	"github.com/epifi/gamma/frontend/stockguardian/matrix"
	"github.com/epifi/gamma/frontend/tiering/data_collector"
	insightsPkg "github.com/epifi/gamma/insights/pkg"
	iftPkg "github.com/epifi/gamma/pkg/internationalfundtransfer"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/a2form"
	usStocksPkg "github.com/epifi/gamma/pkg/usstocks"
	verifiPkg "github.com/epifi/gamma/verifi/pkg"

	"github.com/epifi/gamma/analyser/creditscore/params_fetcher"
	issueConfigPb "github.com/epifi/gamma/api/cx/issue_config"
	fcmFePb "github.com/epifi/gamma/api/frontend/fcm"
	epfPb "github.com/epifi/gamma/api/insights/epf"
	vgIftPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	homeOrchScrollableComponent "github.com/epifi/gamma/frontend/home/<USER>/components/scrollable"
	homeOrchScrollableDashboardComponent "github.com/epifi/gamma/frontend/home/<USER>/components/scrollable/dashboard"
	homeOrchStickyBottomComponent "github.com/epifi/gamma/frontend/home/<USER>/components/stickybottom"
	homeOrchStickyTopComponent "github.com/epifi/gamma/frontend/home/<USER>/components/stickytop"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/epifitech"
	processor2 "github.com/epifi/gamma/frontend/user/update_form_details_processor"
	iftGSTPkg "github.com/epifi/gamma/pkg/internationalfundtransfer/gst"
	iftInvoicePkg "github.com/epifi/gamma/pkg/internationalfundtransfer/invoice"
	iftTCSPkg "github.com/epifi/gamma/pkg/internationalfundtransfer/tcs"

	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	irPb "github.com/epifi/gamma/api/inapphelp/issue_reporting"
	journeyPb "github.com/epifi/gamma/api/nudge/journey"
	"github.com/epifi/gamma/frontend/nudge/journey"

	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	kycDocsPb "github.com/epifi/gamma/api/kyc/docs"
	kycUqudoPb "github.com/epifi/gamma/api/kyc/uqudo"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	"github.com/epifi/gamma/frontend/analyser/spends/top_categories"
	"github.com/epifi/gamma/frontend/firefly/rewards"
	"github.com/epifi/gamma/frontend/kyc/uqudo"
	"github.com/epifi/gamma/frontend/vkyccall"
	userPkg "github.com/epifi/gamma/pkg/user"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	idGen "github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/retry"
	questSdkGenConf "github.com/epifi/be-common/quest/sdk/config/genconf"

	dashboardSections "github.com/epifi/gamma/frontend/card/dashboard_sections"
	"github.com/epifi/gamma/frontend/document_upload/document_exchange/salaryprogram"
	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>/attributes"
	"github.com/epifi/gamma/frontend/home/<USER>/sort"
	"github.com/epifi/gamma/frontend/insights/secrets"
	secretsConfig "github.com/epifi/gamma/frontend/insights/secrets/config"
	secretsFilter "github.com/epifi/gamma/frontend/insights/secrets/filter"
	secretsBuilderFactory "github.com/epifi/gamma/frontend/insights/secrets/secret_builder/factory"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	statementPb "github.com/epifi/gamma/api/accounts/statement"
	beAccrualPb "github.com/epifi/gamma/api/accrual"
	beActorPb "github.com/epifi/gamma/api/actor"
	cxAaPb "github.com/epifi/gamma/api/actor_activity"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/analyser/investment"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	authPb "github.com/epifi/gamma/api/auth"
	beAuthPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/biometrics"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/auth/location"
	authV2Pb "github.com/epifi/gamma/api/auth/orchestrator"
	bePartnerSDKPb "github.com/epifi/gamma/api/auth/partnersdk"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	bcPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	billpayPb "github.com/epifi/gamma/api/billpay"
	"github.com/epifi/gamma/api/budgeting/reminder"
	beCardCtrl "github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/card/provisioning"
	beCard "github.com/epifi/gamma/api/card/provisioning"
	beCasperPb "github.com/epifi/gamma/api/casper"
	beExchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	beRedemptionPb "github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/api/categorizer"
	beCategorizerPb "github.com/epifi/gamma/api/categorizer"
	beConnectedAccPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/securities"
	"github.com/epifi/gamma/api/consent"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	beALPb "github.com/epifi/gamma/api/cx/app_log"
	beCallRoutingPb "github.com/epifi/gamma/api/cx/call_routing"
	beChatPb "github.com/epifi/gamma/api/cx/chat"
	beCxPb "github.com/epifi/gamma/api/cx/customer_auth"
	disputePb "github.com/epifi/gamma/api/cx/dispute"
	beTicketPb "github.com/epifi/gamma/api/cx/ticket"
	beDepositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/employment"
	beEmploymentPb "github.com/epifi/gamma/api/employment"
	beFireflyPb "github.com/epifi/gamma/api/firefly"
	ffBePb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	fireflyaccpb "github.com/epifi/gamma/api/firefly/accounting"
	ffBillPb "github.com/epifi/gamma/api/firefly/billing"
	ffRePb "github.com/epifi/gamma/api/firefly/card_recommendation"
	ffLmsPb "github.com/epifi/gamma/api/firefly/lms"
	ffPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"
	fitttPb "github.com/epifi/gamma/api/fittt"
	"github.com/epifi/gamma/api/fittt/sports"
	feActorActivityPb "github.com/epifi/gamma/api/frontend/actoractivity"
	analyserFePb "github.com/epifi/gamma/api/frontend/analyser"
	feCardPb "github.com/epifi/gamma/api/frontend/card"
	categorizerFePb "github.com/epifi/gamma/api/frontend/categorizer"
	feCategorizerPb "github.com/epifi/gamma/api/frontend/categorizer"
	connectedAccountFePb "github.com/epifi/gamma/api/frontend/connected_account"
	feConnectedAccPb "github.com/epifi/gamma/api/frontend/connected_account"
	cxHomeFePb "github.com/epifi/gamma/api/frontend/cx/home"
	deFePb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	dynamicElementsPb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	fireflyFePb "github.com/epifi/gamma/api/frontend/firefly"
	homeFePb "github.com/epifi/gamma/api/frontend/home"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	investmentFePb "github.com/epifi/gamma/api/frontend/investment/aggregator"
	nudgeFePb "github.com/epifi/gamma/api/frontend/nudge"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	payFeTxnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	frontendPB "github.com/epifi/gamma/api/frontend/recurringpayment"
	referralFePb "github.com/epifi/gamma/api/frontend/referral"
	rewardsFePb "github.com/epifi/gamma/api/frontend/rewards"
	search2 "github.com/epifi/gamma/api/frontend/search"
	searchFePb "github.com/epifi/gamma/api/frontend/search"
	feTimelinePb "github.com/epifi/gamma/api/frontend/timeline"
	feWoPb "github.com/epifi/gamma/api/frontend/wealthonboarding"
	beGoalsPb "github.com/epifi/gamma/api/goals"
	goalsPb "github.com/epifi/gamma/api/goals"
	"github.com/epifi/gamma/api/health_engine"
	appFeedbackPb "github.com/epifi/gamma/api/inapphelp/app_feedback"
	beFaqPb "github.com/epifi/gamma/api/inapphelp/faq/serving"
	befePb "github.com/epifi/gamma/api/inapphelp/feedback_engine"
	mediaPb "github.com/epifi/gamma/api/inapphelp/media"
	beRaPb "github.com/epifi/gamma/api/inapphelp/recent_activity"
	beInAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	inappReferralPb "github.com/epifi/gamma/api/inappreferral"
	seasonPb "github.com/epifi/gamma/api/inappreferral/season"
	beInsightsPb "github.com/epifi/gamma/api/insights"
	beAccessinfoPb "github.com/epifi/gamma/api/insights/accessinfo"
	beEmailParserPb "github.com/epifi/gamma/api/insights/emailparser"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	insightKubairPb "github.com/epifi/gamma/api/insights/kubair"
	beNetWorthPb "github.com/epifi/gamma/api/insights/networth"
	beStoryPb "github.com/epifi/gamma/api/insights/story"
	invAggrPb "github.com/epifi/gamma/api/investment/aggregator"
	auth2 "github.com/epifi/gamma/api/investment/auth"
	beDePbSvc "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	dynamicElementUIPb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	investmentEventProcessorPb "github.com/epifi/gamma/api/investment/event_processor"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	fundcatalogpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	mfNotiPb "github.com/epifi/gamma/api/investment/mutualfund/notifications"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	investpaypb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	investmentProfilePb "github.com/epifi/gamma/api/investment/profile"
	"github.com/epifi/gamma/api/kyc"
	agentPb "github.com/epifi/gamma/api/kyc/agent"
	beVkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	mPb "github.com/epifi/gamma/api/merchant"
	nudgeBePb "github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/order"
	orderSerivcePb "github.com/epifi/gamma/api/order"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	actorActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	p2pPbBe "github.com/epifi/gamma/api/p2pinvestment"
	panPb "github.com/epifi/gamma/api/pan"
	payPb "github.com/epifi/gamma/api/pay"
	bmsPb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	bePiPb "github.com/epifi/gamma/api/paymentinstrument"
	beAccountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	securedLoansPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	"github.com/epifi/gamma/api/product"
	questManagerPb "github.com/epifi/gamma/api/quest/manager"
	beRecurringPayment "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	luckydrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	riskPb "github.com/epifi/gamma/api/risk"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/rms/ui"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	beSalaryReferralsPb "github.com/epifi/gamma/api/salaryprogram/referrals"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/search"
	beSearchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/segment"
	segmentPb "github.com/epifi/gamma/api/segment"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	beTimelinePb "github.com/epifi/gamma/api/timeline"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/upcomingtransactions"
	beUPIPb "github.com/epifi/gamma/api/upi"
	mandatePb "github.com/epifi/gamma/api/upi/mandate"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/upi/simulation"
	"github.com/epifi/gamma/api/user"
	beUserPb "github.com/epifi/gamma/api/user"
	userContactPb "github.com/epifi/gamma/api/user/contact"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	location2 "github.com/epifi/gamma/api/user/location"
	obfuscatorPb "github.com/epifi/gamma/api/user/obfuscator"
	userOnboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/useractions"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	usstocksAccountPb "github.com/epifi/gamma/api/usstocks/account"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
	usstocksPortfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	ussRewardsPb "github.com/epifi/gamma/api/usstocks/rewards"
	ipPb "github.com/epifi/gamma/api/vendordata/ip"
	vgEmploymentPb "github.com/epifi/gamma/api/vendorgateway/employment"
	"github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	employerNameMatchVgPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	"github.com/epifi/gamma/api/vendorgateway/ocr"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	savingsVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	"github.com/epifi/gamma/api/vendormapping"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	commstypes "github.com/epifi/gamma/comms/wire/types"
	connectedAccountHelper "github.com/epifi/gamma/connectedaccount/datafetcher"
	dewiretypes "github.com/epifi/gamma/dynamicelements/wire/types"
	"github.com/epifi/gamma/frontend/account/saclosure"
	"github.com/epifi/gamma/frontend/account/saclosure/criteria/group"
	"github.com/epifi/gamma/frontend/account/saclosure/criteria/item"
	"github.com/epifi/gamma/frontend/account/saclosure/orchestrator"
	"github.com/epifi/gamma/frontend/account/saclosure/savings"
	"github.com/epifi/gamma/frontend/account/saclosure/screen"
	"github.com/epifi/gamma/frontend/account/screening"
	"github.com/epifi/gamma/frontend/account/signup"
	feStatement "github.com/epifi/gamma/frontend/account/statement"
	"github.com/epifi/gamma/frontend/account/upi"
	"github.com/epifi/gamma/frontend/acquisition"
	feActorActivityService "github.com/epifi/gamma/frontend/actoractivity"
	"github.com/epifi/gamma/frontend/alfred"
	"github.com/epifi/gamma/frontend/analyser"
	analyserConfig "github.com/epifi/gamma/frontend/analyser/config"
	analyserDataProvider "github.com/epifi/gamma/frontend/analyser/dataprovider"
	creditScoreAnalyser "github.com/epifi/gamma/frontend/analyser/debt/credit_score"
	"github.com/epifi/gamma/frontend/analyser/deeplink"
	helper2 "github.com/epifi/gamma/frontend/analyser/helper"
	"github.com/epifi/gamma/frontend/analyser/helper/category"
	"github.com/epifi/gamma/frontend/analyser/helper/mutualfunds"
	"github.com/epifi/gamma/frontend/analyser/helper/time"
	"github.com/epifi/gamma/frontend/analyser/insights/loans"
	mfAnalyser "github.com/epifi/gamma/frontend/analyser/investments/mutualfunds"
	analyserExecutor "github.com/epifi/gamma/frontend/analyser/processor/analyser/executor"
	analyserFactory "github.com/epifi/gamma/frontend/analyser/processor/analyser/factory"
	analyserRequestHandler "github.com/epifi/gamma/frontend/analyser/processor/analyser/handler"
	analyserBannerFactory "github.com/epifi/gamma/frontend/analyser/processor/banner/factory"
	analyserCardFactory "github.com/epifi/gamma/frontend/analyser/processor/card/factory"
	"github.com/epifi/gamma/frontend/analyser/processor/feedback"
	"github.com/epifi/gamma/frontend/analyser/processor/feedback/reference_id_mapping"
	filter "github.com/epifi/gamma/frontend/analyser/processor/filter/factory"
	analyserFilterGenerator "github.com/epifi/gamma/frontend/analyser/processor/filter/generator"
	filterWidget "github.com/epifi/gamma/frontend/analyser/processor/filterwidget/factory"
	factory3 "github.com/epifi/gamma/frontend/analyser/processor/hub/banner/factory"
	analyserSection "github.com/epifi/gamma/frontend/analyser/processor/hub/section"
	analyserWidget "github.com/epifi/gamma/frontend/analyser/processor/hub/widget"
	factory2 "github.com/epifi/gamma/frontend/analyser/processor/hub/widget/factory"
	analyserLandingPage "github.com/epifi/gamma/frontend/analyser/processor/landingpage"
	analyserLandingPageFactory "github.com/epifi/gamma/frontend/analyser/processor/landingpage/factory"
	"github.com/epifi/gamma/frontend/analyser/processor/status"
	analyserRelease "github.com/epifi/gamma/frontend/analyser/release"
	"github.com/epifi/gamma/frontend/analyser/util"
	analyserVisualcomponents "github.com/epifi/gamma/frontend/analyser/visualcomponents"
	"github.com/epifi/gamma/frontend/apikeys"
	feAuth "github.com/epifi/gamma/frontend/auth"
	feLiv "github.com/epifi/gamma/frontend/auth/liveness"
	feAuthV2 "github.com/epifi/gamma/frontend/auth/orchestrator"
	fePartnerSDK "github.com/epifi/gamma/frontend/auth/partnersdk"
	"github.com/epifi/gamma/frontend/bank_customer"
	"github.com/epifi/gamma/frontend/billpay"
	feReminders "github.com/epifi/gamma/frontend/budgeting/reminders"
	feCard "github.com/epifi/gamma/frontend/card"
	categorizerFe "github.com/epifi/gamma/frontend/categorizer"
	"github.com/epifi/gamma/frontend/clientlogger"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/configsvc"
	"github.com/epifi/gamma/frontend/connected_account"
	caFactory "github.com/epifi/gamma/frontend/connected_account/factory"
	"github.com/epifi/gamma/frontend/connected_account/factory/processor"
	caFiToFi "github.com/epifi/gamma/frontend/connected_account/fi_to_fi"
	caFiToFiHelper "github.com/epifi/gamma/frontend/connected_account/fi_to_fi_helper"
	feConsent "github.com/epifi/gamma/frontend/consent"
	"github.com/epifi/gamma/frontend/consent/ack"
	ackFactory "github.com/epifi/gamma/frontend/consent/ack/factory"
	ack_manager "github.com/epifi/gamma/frontend/consent/ack/factory/ack_manager"
	feContacts "github.com/epifi/gamma/frontend/contacts"
	"github.com/epifi/gamma/frontend/credit_report"
	"github.com/epifi/gamma/frontend/cx/app_logs"
	"github.com/epifi/gamma/frontend/cx/call"
	"github.com/epifi/gamma/frontend/cx/chat"
	feCx "github.com/epifi/gamma/frontend/cx/customer_auth"
	"github.com/epifi/gamma/frontend/cx/dispute"
	cxHome "github.com/epifi/gamma/frontend/cx/home"
	"github.com/epifi/gamma/frontend/cx/ticket"
	feDeposit "github.com/epifi/gamma/frontend/deposit"
	"github.com/epifi/gamma/frontend/deposit/accessor"
	asg "github.com/epifi/gamma/frontend/deposit/auto_save_suggestions"
	depositCreation "github.com/epifi/gamma/frontend/deposit/creation"
	"github.com/epifi/gamma/frontend/docs"
	fileUploadFactory "github.com/epifi/gamma/frontend/docs/factory"
	fileUploadManager "github.com/epifi/gamma/frontend/docs/factory/file_upload_manager"
	"github.com/epifi/gamma/frontend/document_upload"
	"github.com/epifi/gamma/frontend/document_upload/document_exchange/usstocks"
	feDe "github.com/epifi/gamma/frontend/dynamic_elements"
	"github.com/epifi/gamma/frontend/fcm"
	"github.com/epifi/gamma/frontend/firefly"
	billInfoValidators "github.com/epifi/gamma/frontend/firefly/billinfo_v2"
	"github.com/epifi/gamma/frontend/firefly/fees_and_benefits"
	"github.com/epifi/gamma/frontend/firefly/homedashboard"
	"github.com/epifi/gamma/frontend/firefly/homedashboard/generator"
	ffDashboardWarning "github.com/epifi/gamma/frontend/firefly/homedashboard/warning"
	"github.com/epifi/gamma/frontend/fittt"
	"github.com/epifi/gamma/frontend/fittt/client_metrics"
	feGenie "github.com/epifi/gamma/frontend/genie"
	feGoals "github.com/epifi/gamma/frontend/goals"
	"github.com/epifi/gamma/frontend/home"
	homeOrch "github.com/epifi/gamma/frontend/home/<USER>"
	feInapphelpActorActivityService "github.com/epifi/gamma/frontend/inapphelp/actor_activity"
	feAppFeedback "github.com/epifi/gamma/frontend/inapphelp/app_feedback"
	feContact "github.com/epifi/gamma/frontend/inapphelp/contact_us"
	feFaq "github.com/epifi/gamma/frontend/inapphelp/faq"
	feedbackEngine "github.com/epifi/gamma/frontend/inapphelp/feedback_engine"
	helpMedia "github.com/epifi/gamma/frontend/inapphelp/media"
	"github.com/epifi/gamma/frontend/insights"
	feAccessInfo "github.com/epifi/gamma/frontend/insights/accessinfo"
	feEmailParser "github.com/epifi/gamma/frontend/insights/emailparser"
	feEpfPb "github.com/epifi/gamma/frontend/insights/epf"
	"github.com/epifi/gamma/frontend/insights/networth"
	networthFe "github.com/epifi/gamma/frontend/insights/networth"
	networthConfig "github.com/epifi/gamma/frontend/insights/networth/config"
	"github.com/epifi/gamma/frontend/insights/networth/data_fetcher"
	"github.com/epifi/gamma/frontend/insights/networth/generator/section"
	networthVisFactory "github.com/epifi/gamma/frontend/insights/networth/generator/visualisation/factory"
	"github.com/epifi/gamma/frontend/insights/networth/generator/widget/factory"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/asset_dashboard"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/user_declaration"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator"
	dataTypes "github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator/input_type_validator/data_type_validator"
	multiEditValidator "github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator/input_type_validator/multi_edit_option_validator"
	wealthLandingDashboard "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/dashboard"
	wealthScrollable "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/scrollable"
	wealthScrollableDashboard "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/scrollable/dashboard"
	collectionProcessor "github.com/epifi/gamma/frontend/insights/secrets/collections/processor"
	"github.com/epifi/gamma/frontend/insights/secrets/secret_builder/secret_provider"
	"github.com/epifi/gamma/frontend/insights/story"
	feInvestmentAgg "github.com/epifi/gamma/frontend/investment/aggregator"
	ip "github.com/epifi/gamma/frontend/investment/aggregator/recommendation_plugins"
	retentionInvestment "github.com/epifi/gamma/frontend/investment/aggregator/retention"
	brokerDetails "github.com/epifi/gamma/frontend/investment/broker_details"
	"github.com/epifi/gamma/frontend/investment/indianstocks"
	"github.com/epifi/gamma/frontend/investment/indianstocks/dashboard"
	filter2 "github.com/epifi/gamma/frontend/investment/indianstocks/dashboard/filter"
	dataFetcher2 "github.com/epifi/gamma/frontend/investment/indianstocks/data_fetcher"
	feInvestment "github.com/epifi/gamma/frontend/investment/mutualfund"
	feInvestmentProfileService "github.com/epifi/gamma/frontend/investment/profile"
	"github.com/epifi/gamma/frontend/kubair"
	"github.com/epifi/gamma/frontend/kyc/vkyc"
	"github.com/epifi/gamma/frontend/media"
	"github.com/epifi/gamma/frontend/nudge"
	"github.com/epifi/gamma/frontend/nudge/widget"
	"github.com/epifi/gamma/frontend/otp"
	"github.com/epifi/gamma/frontend/otp/investment/mutual_fund"
	p2p "github.com/epifi/gamma/frontend/p2pinvestment"
	jumpBenefits "github.com/epifi/gamma/frontend/p2pinvestment/benefits"
	"github.com/epifi/gamma/frontend/p2pinvestment/deeplinks"
	"github.com/epifi/gamma/frontend/p2pinvestment/helper"
	"github.com/epifi/gamma/frontend/pan"
	"github.com/epifi/gamma/frontend/pay"
	fePay "github.com/epifi/gamma/frontend/pay/simulator"
	fePayTransactions "github.com/epifi/gamma/frontend/pay/transaction"
	dataCollector "github.com/epifi/gamma/frontend/pay/transaction/payment_options/data_collector"
	payValidatorWire "github.com/epifi/gamma/frontend/pay/transaction/validator"
	fePreApprovedLoan "github.com/epifi/gamma/frontend/preapprovedloan"
	deeplinks2 "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	palFeAggregatedDeeplinks "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/aggregated_deeplinks"
	palFeAbflDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/abfl"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	palFeFederalDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/federal"
	palFeFfDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/fiftyfin"
	palFeIdfcDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/idfc"
	palLdcDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/lenden"
	palFeLLDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/liquiloans"
	palFeMvDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/moneyview"
	palRteDeeplink "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/realtimeetb"
	helper4 "github.com/epifi/gamma/frontend/preapprovedloan/helper"
	plDashboard "github.com/epifi/gamma/frontend/preapprovedloan/home/<USER>"
	plNavigation "github.com/epifi/gamma/frontend/preapprovedloan/home/<USER>"
	"github.com/epifi/gamma/frontend/prompt"
	"github.com/epifi/gamma/frontend/prompt/processors"
	"github.com/epifi/gamma/frontend/qr"
	feRecurringPayment "github.com/epifi/gamma/frontend/recurringpayment"
	"github.com/epifi/gamma/frontend/referral"
	feRewards "github.com/epifi/gamma/frontend/rewards"
	"github.com/epifi/gamma/frontend/rewards/earnedrewardshistory"
	rewardsNavigation "github.com/epifi/gamma/frontend/rewards/home/<USER>"
	feRewardsOfferDisplay "github.com/epifi/gamma/frontend/rewards/offerdisplay"
	feRewardsOfferWidget "github.com/epifi/gamma/frontend/rewards/offerwidget"
	feRewardsTags "github.com/epifi/gamma/frontend/rewards/tags"
	feSalaryPgPb "github.com/epifi/gamma/frontend/salaryprogram"
	aaSalary "github.com/epifi/gamma/frontend/salaryprogram/aa_salary"
	salaryBenefits "github.com/epifi/gamma/frontend/salaryprogram/benefits"
	feSavings "github.com/epifi/gamma/frontend/savings"
	feSearch "github.com/epifi/gamma/frontend/search"
	"github.com/epifi/gamma/frontend/tiering"
	"github.com/epifi/gamma/frontend/tiering/cta"
	tieringDeeplink "github.com/epifi/gamma/frontend/tiering/deeplink"
	earnedBenefits "github.com/epifi/gamma/frontend/tiering/earned_benefits"
	"github.com/epifi/gamma/frontend/tiering/feedback_flow_integration"
	tieringRelease "github.com/epifi/gamma/frontend/tiering/release"
	feTimeline "github.com/epifi/gamma/frontend/timeline"
	feUpi "github.com/epifi/gamma/frontend/upi"
	feUpiOnb "github.com/epifi/gamma/frontend/upi/onboarding"
	feUser "github.com/epifi/gamma/frontend/user"
	usstocksFePb "github.com/epifi/gamma/frontend/usstocks"
	"github.com/epifi/gamma/frontend/usstocks/activity"
	"github.com/epifi/gamma/frontend/usstocks/dropoff"
	usstocksOrderDetails "github.com/epifi/gamma/frontend/usstocks/orderdetails"
	waitlistPb "github.com/epifi/gamma/frontend/waitlist"
	feWo "github.com/epifi/gamma/frontend/wealth/onboarding"
	"github.com/epifi/gamma/frontend/wire/provider"
	"github.com/epifi/gamma/frontend/wire/types"
	pkgSourceAndIntent "github.com/epifi/gamma/pkg/acquisition/sourceandintent"
	"github.com/epifi/gamma/pkg/deeplinkv2"
	"github.com/epifi/gamma/pkg/dmf/datafetcher"
	txnHelper "github.com/epifi/gamma/pkg/dmf/txnaggregates"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgFittt "github.com/epifi/gamma/pkg/fittt"
	qrPkg "github.com/epifi/gamma/pkg/qr"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	questSdk "github.com/epifi/gamma/quest/sdk"
	questSdkInit "github.com/epifi/gamma/quest/sdk/init"
	helper3 "github.com/epifi/gamma/upcomingtransactions/helper"
)

func BKYCQRConfigProvider(gconf *genconf.Config) *gencfg.QRCode {
	return gconf.BKYC().QRConfig()
}

func QuestSDKClientConfProvider(conf *genconf.Config) *questSdkGenConf.Config {
	return conf.QuestSdk()
}

func QuestCacheStorageProvider(cacheStorage pkgTypes.QuestCacheStorage) cache.CacheStorage {
	return cacheStorage
}

func CcDashboardWarningSvcProvider(ffAccountingClient ffAccountsPb.AccountingClient, ffBeClient ffBePb.FireflyClient) types.CcDashboardWarningSvc {
	return ffDashboardWarning.NewService(ffAccountingClient, ffBeClient)
}

func PlDashboardWarningSvcProvider(
	palClient palBePb.PreApprovedLoanClient,
	conf *genconf.Config,
	rpcHelper helper4.IRpcHelper,
) types.PlDashboardWarningSvc {
	return plDashboard.NewDashboardWarningService(
		palClient,
		conf,
		rpcHelper,
	)
}

func PlNavigationBarHighlightSvcProvider(
	palClient palBePb.PreApprovedLoanClient,
	conf *genconf.Config,
	rpcHelper helper4.IRpcHelper,
	segmentationClient segment.SegmentationServiceClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	savingsClient savingsPb.SavingsClient,
) types.PlNavigationBarHighlightSvc {
	return plNavigation.NewNavigationBarHighlightService(palClient, conf, rpcHelper, segmentationClient)
}

func USSNavigationBarHighlightSvcProvider(conf *genconf.Config) types.USSNavigationBarHighlightSvc {
	return usstocksFePb.NewNavigationBarHighlightService(conf)
}

func NetworthNavBarHighlightSvcProvider(conf *genconf.Config) types.NetworthNavBarHighlightSvc {
	return networthFe.NewNavigationBarHighlightService(conf)
}

func RewardsNavigationBarHighlightSvcProvider(conf *genconf.Config, rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient) types.RewardsNavigationBarHighlightSvc {
	return rewardsNavigation.NewNavigationBarHighlightService(conf, rewardsGeneratorClient)
}

func InitializeSignupService(authClient beAuthPb.AuthClient, usersClient beUserPb.UsersClient,
	kycClient kyc.KycClient, livenessClient liveness.LivenessClient, actorClient beActorPb.ActorClient,
	consentClient consent.ConsentClient, onboardingClient userOnboardingPb.OnboardingClient, cardClient provisioning.CardProvisioningClient,
	broker events.Broker, deviceTokenClient commstypes.FCMDeviceTokenClientWithInterceptors,
	vendorMappingClient vendormapping.VendorMappingServiceClient, vkycClient beVkycPb.VKYCClient,
	location location.LocationClient, extacctClient extacct.ExternalAccountsClient, genConfig *genconf.Config,
	uaClient useractions.UserActionsClient, empClient employment.EmploymentClient,
	obfuscatorClient obfuscatorPb.ObfuscatorClient, savClient savingsPb.SavingsClient,
	bcClient bankCustPb.BankCustomerServiceClient, opStatusClient operationalStatusPb.OperationalStatusServiceClient,
	panClient panPb.PanClient, caClient beConnectedAccPb.ConnectedAccountClient, userGroupClient userGroupPb.GroupClient,
	productClient product.ProductClient,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient, riskClient riskPb.RiskClient, ipClient ipPb.IpServiceClient, userCommsPrefClient upPb.UserPreferenceClient) (*signup.Service, error) {
	wire.Build(
		commstypes.FCMDeviceTokenClientProvider,
		signup.NewService,
		idGen.NewClock,
		idGen.WireSet,
		qrPkg.BasicQRCodeWireSet,
		datetime.WireDefaultTimeSet,
		pkgSourceAndIntent.IdentifierWireSet,
		types.NewAcqSourceIntentDecryptionKeysProvider,
		BKYCQRConfigProvider,
		provider.GNOARetryStrategyProvider,
		retry.NewStrategyFromConfig,
		processor2.NewNsdlPanValidator,
		processor2.NewFederalOnboardingPan,
		signup.NewPANFormProcessors,
	)
	return &signup.Service{}, nil
}

func InitialiseAcquisitionService(broker events.Broker, genConfig *genconf.Config, authClient beAuthPb.AuthClient) *acquisition.Service {
	wire.Build(
		acquisition.NewService,
		pkgSourceAndIntent.IdentifierWireSet,
		types.NewAcqSourceIntentDecryptionKeysProvider,
	)
	return &acquisition.Service{}
}

func InitializeUPIService(actorClient beActorPb.ActorClient, savingsClient savingsPb.SavingsClient,
	piClient bePiPb.PiClient, accountPiClient beAccountPiPb.AccountPIRelationClient, upiClient beUPIPb.UPIClient,
	feConfig *config.Config, bcClient bankCustPb.BankCustomerServiceClient, usersClient beUserPb.UsersClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient, onboardingClient userOnboardingPb.OnboardingClient) *upi.Service {
	wire.Build(upi.NewService)
	return &upi.Service{}
}

func InitializeSavingsService(usersClient beUserPb.UsersClient, savingsClient savingsPb.SavingsClient,
	authClient beAuthPb.AuthClient, cardClient beCard.CardProvisioningClient,
	accountPiClient beAccountPiPb.AccountPIRelationClient, piClient bePiPb.PiClient, actorClient beActorPb.ActorClient,
	conf *config.Config, onboardingClient userOnboardingPb.OnboardingClient, accountBalanceClient accountBalancePb.BalanceClient, kycClient kyc.KycClient, bcClient bankCustPb.BankCustomerServiceClient) *feSavings.Service {
	wire.Build(feSavings.NewSavingsService,
		datetime.WireDefaultTimeSet)
	return &feSavings.Service{}
}

func InitializeStatementService(savingsClient savingsPb.SavingsClient,
	statementClient statementPb.AccountStatementClient, conf *config.Config, depositClient beDepositPb.DepositClient,
	actorClient beActorPb.ActorClient) *feStatement.Service {
	wire.Build(feStatement.NewStatementService)
	return &feStatement.Service{}
}

func InitializeConsentService(authClient beAuthPb.AuthClient,
	beConsentClient consent.ConsentClient,
	userCommsPrefClient commstypes.UserPreferenceClientWithInterceptors,
	client userOnboardingPb.OnboardingClient,
	actorClient beActorPb.ActorClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	userClient beUserPb.UsersClient) *feConsent.Service {
	wire.Build(commstypes.UserPreferenceClientProvider, feConsent.NewConsentService)
	return &feConsent.Service{}
}

func InitialiseAckService(vkycFeClient vkycPb.VKYCFeClient, onbClient userOnboardingPb.OnboardingClient, compClient compliancePb.ComplianceClient) *ack.AckService {
	wire.Build(
		ack.NewAckService,
		ackFactory.AckFactoryWireSet,
		ack_manager.NewVKYCAckManager,
		ack_manager.NewOnbAckManager,
		ack_manager.NewBankCustomerAckManager,
	)
	return &ack.AckService{}
}

func InitializePayTransactionService(
	conf *genconf.Config,
	orderClient order.OrderServiceClient,
	paymentClient paymentPb.PaymentClient,
	actorClient beActorPb.ActorClient,
	savingsClient savingsPb.SavingsClient,
	categorizerClient categorizer.TxnCategorizerClient,
	accountPIRelationClient beAccountPiPb.AccountPIRelationClient,
	piClient bePiPb.PiClient,
	decisionEngineClient paymentPb.DecisionEngineClient,
	authClient beAuthPb.AuthClient,
	upiClient beUPIPb.UPIClient,
	timeline beTimelinePb.TimelineServiceClient,
	userClient beUserPb.UsersClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	broker events.Broker,
	rewardOfferClient beRewardOffersPb.RewardOffersClient,
	userGroupClient userGroupPb.GroupClient,
	payClient payPb.PayClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	tieringClient tieringPb.TieringClient,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	beSalaryClient beSalaryPb.SalaryProgramClient,
	healthEngineClient health_engine.HealthEngineServiceClient,
	p2pInvestmentClient p2pPbBe.P2PInvestmentClient,
	serverConf *config.Config,
	inappreferralClient inappReferralPb.InAppReferralClient,
	userIntelClient userIntelPb.UserIntelServiceClient,
	operationStatusSvcClient operationalStatusPb.OperationalStatusServiceClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	cardClient beCard.CardProvisioningClient,
	alfredClient alfredPb.AlfredClient,
	rewardsClient beRewardsPb.RewardsGeneratorClient,
	projectionClient rewardsProjectionPb.ProjectorServiceClient,
	txnAggregateClient txnAggregatesPb.TxnAggregatesClient,
	segmentationClient segment.SegmentationServiceClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	recurringPaymentClient beRecurringPayment.RecurringPaymentServiceClient,
	pgServiceClient paymentgateway.PaymentGatewayServiceClient,
	accountManagerClient usstocksAccountPb.AccountManagerClient,
	employmentClient beEmploymentPb.EmploymentClient,
	healthInsuranceClient healthinsurancePb.HealthInsuranceClient,
	ffAccountingClient ffAccountsPb.AccountingClient,
	ffClientv2 fireflyV2Pb.FireflyV2Client,
) (*fePayTransactions.Service, error) {
	wire.Build(
		fePayTransactions.NewTransactionService,
		data_collector.DataCollectorWireSet,
		tieringDeeplink.DeeplinkWireSet,
		dataCollector.Wireset,
		tiering.TieringAddFundsManagerWireSet,
		payValidatorWire.WireSet,
		release.EvaluatorWireSet, provider.FeatureReleaseConfigProvider,
		provider.PaymentOptionsProvider,
		jumpBenefits.JumpBenefitsWireset, feedback_flow_integration.TierMovementBasedFeedbackFlowIntegrationWireSet,
	)

	return &fePayTransactions.Service{}, nil
}

func InitializeContactsService() *feContacts.Service {
	wire.Build(feContacts.SyncContactsService)
	return &feContacts.Service{}
}

func CommsConfigProvider(conf *config.Config) *config.Comms { return conf.Comms }
func InitializeDeviceTokenService(deviceTokenClient commstypes.FCMDeviceTokenClientWithInterceptors, commsClient commstypes.CommsClientWithInterceptors,
	conf *config.Config) *fcm.Service {
	wire.Build(commstypes.FCMDeviceTokenClientProvider, commstypes.CommsClientProvider, CommsConfigProvider, fcm.NewFCMDeviceTokenService)
	return &fcm.Service{}
}

func InitializeReminderService(beReminderClient reminder.ReminderServiceClient) *feReminders.Service {
	wire.Build(feReminders.NewRemindersService)
	return &feReminders.Service{}
}

func InitializeSearchService(searchClient beSearchPb.ActionBarClient, client beTimelinePb.TimelineServiceClient,
	actorClient beActorPb.ActorClient, cpClient beCard.CardProvisioningClient, insightsAccessInfoClient beAccessinfoPb.AccessInfoClient,
	accountPiClient beAccountPiPb.AccountPIRelationClient, accountClient connectedAccountFePb.ConnectedAccountClient, broker events.Broker, genConfig *genconf.Config,
	usersClient beUserPb.UsersClient, groupClient userGroupPb.GroupClient, preApprovedLoanClient palBePb.PreApprovedLoanClient,
	beConnectedAccClient beConnectedAccPb.ConnectedAccountClient, savingsClient savingsPb.SavingsClient, onboardingClient userOnboardingPb.OnboardingClient,
) *feSearch.Service {
	wire.Build(
		deeplinkv2.NewDeepLinkBuilder,
		provider.ABFeatureReleaseConfigProvider,
		feSearch.NewSearchService,
		caFiToFi.ConnectedAccountFeatureFiToFiWireSet,
		caFiToFiHelper.ConnectedAccountFeatureFiToFiHelperWireSet,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
	)
	return &feSearch.Service{}
}

func InitializeCardService(
	cardClient beCard.CardProvisioningClient,
	cardCtrlClient beCardCtrl.CardControlClient,
	upiClient beUPIPb.UPIClient,
	conf *config.Config,
	userClient beUserPb.UsersClient,
	actorClient beActorPb.ActorClient,
	userGroupClient userGroupPb.GroupClient,
	onbClient userOnboardingPb.OnboardingClient,
	livenessclient liveness.LivenessClient,
	savingsClient savingsPb.SavingsClient,
	kycClient kyc.KycClient,
	vkycClient beVkycPb.VKYCClient,
	dynamicConf *genconf.Config,
	beTieringClient beTieringPb.TieringClient,
	salaryProgramClient beSalaryPb.SalaryProgramClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	offersListingClient beCasperPb.OfferListingServiceClient,
	orderActorActivityClient actorActivityPb.ActorActivityClient,
	rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	segmentationClient segment.SegmentationServiceClient,
	authClient authPb.AuthClient,
	actorActivityFeClient feActorActivityPb.ActorActivityClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	inAppTargetedCommsClient tcPb.InAppTargetedCommsClient,
	eventBroker events.Broker,
	panClient panPb.PanClient,
	cardCiClient cardcipb.CurrencyInsightsClient,
	payClient payPb.PayClient,
	questCacheStorage pkgTypes.QuestCacheStorage,
	questManagerClient questManagerPb.ManagerClient,
	networthClient beNetWorthPb.NetWorthClient,
) (*feCard.Service, error) {
	wire.Build(
		provider.FeatureFlagProvider,
		provider.CardProvider,
		dashboardSections.UiSectionBuilderWireSet,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
		feCard.NewCardService,
		userPkg.WireSet,
		InitializeQuestSdkClient,
	)
	return &feCard.Service{}, nil
}

func InitializeTimelineService(
	conf *config.Config,
	piClient bePiPb.PiClient,
	actorClient beActorPb.ActorClient,
	timelineClient beTimelinePb.TimelineServiceClient,
	orderClient order.OrderServiceClient,
	upiClient beUPIPb.UPIClient,
	userClient beUserPb.UsersClient,
	savingsClient savingsPb.SavingsClient,
	merchantClient mPb.MerchantServiceClient,
	userGroupClient userGroupPb.GroupClient,
	dynConf *genconf.Config,
	ffAccClient ffAccountsPb.AccountingClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	searchClient beSearchPb.ActionBarClient,
	ovgPaymentClient vgPaymentPb.PaymentClient,
	userOnboardingClient userOnboardingPb.OnboardingClient,
	eventsBroker events.Broker,
) *feTimeline.Service {
	wire.Build(feTimeline.NewService,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider)
	return &feTimeline.Service{}
}

func InitializeUserService(
	config *config.Config,
	onboardingClient userOnboardingPb.OnboardingClient,
	userClient beUserPb.UsersClient,
	savingsClient savingsPb.SavingsClient,
	depositsClient beDepositPb.DepositClient,
	commsClient commstypes.CommsClientWithInterceptors,
	piClient bePiPb.PiClient,
	accountPiClient beAccountPiPb.AccountPIRelationClient,
	actorClient beActorPb.ActorClient,
	upiClient beUPIPb.UPIClient,
	userContactClient userContactPb.ContactClient,
	cardClient provisioning.CardProvisioningClient,
	broker events.Broker, kycClient kyc.KycClient, userGroupClient userGroupPb.GroupClient,
	salaryProgramClient beSalaryPb.SalaryProgramClient,
	vmClient vendormapping.VendorMappingServiceClient, client beAuthPb.AuthClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	employmentClient beEmploymentPb.EmploymentClient,
	extAcctClient extacct.ExternalAccountsClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	genConfig *genconf.Config,
	ffAccountingClient ffAccountsPb.AccountingClient,
	ffClient ffBePb.FireflyClient,
	beTieringClient beTieringPb.TieringClient,
	vKycFeClient beVkycPb.VKYCFeClient,
	feEmpClient employment.EmploymentFeClient,
	bcClient bcPb.BankCustomerServiceClient,
	wealthOnbClient woPb.WealthOnboardingClient,
	feConnectedAccClient feConnectedAccPb.ConnectedAccountClient,
	complianceClient compliancePb.ComplianceClient,
	panClient panPb.PanClient,
	consentClient consent.ConsentClient,
	managerClient creditReportV2Pb.CreditReportManagerClient,
	userLocationClient location2.LocationClient,
	storage pkgTypes.QuestCacheStorage,
	questManagerClient questManagerPb.ManagerClient,
	segmentationClient segment.SegmentationServiceClient,
	docExtractionClient kycDocsPb.DocExtractionClient,
	productClient product.ProductClient,
	savingsVgClient savingsVgPb.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	alfredClient alfredPb.AlfredClient,
	rewardsClient beRewardsPb.RewardsGeneratorClient,
	projectionClient rewardsProjectionPb.ProjectorServiceClient,
	txnAggregateClient txnAggregatesPb.TxnAggregatesClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	orderClient order.OrderServiceClient,
	accountManagerClient usstocksAccountPb.AccountManagerClient,
	healthInsuranceClient healthinsurancePb.HealthInsuranceClient,
	rewardOffersClient beRewardOffersPb.RewardOffersClient,
	networthClient beNetWorthPb.NetWorthClient,
) (*feUser.Service, error) {
	wire.Build(release.EvaluatorWireSet, provider.FeatureReleaseConfigProvider,
		commstypes.CommsClientProvider,
		userPkg.WireSet,
		InitializeQuestSdkClient,
		genconf.GetDeviceIdsForSafetynetV2Flow,
		feUser.FeProfileDataCollectorWireSet,
		data_collector.DataCollectorWireSet,
		feUser.NewUserService,
		insightsPkg.InsightsPanProcessorWireSet)
	return &feUser.Service{}, nil
}

func InitializeSimulationService(orderClient order.OrderServiceClient, savingsClient savingsPb.SavingsClient,
	piClient bePiPb.PiClient, upiConsumer beUPIPb.ConsumerClient, upiSimulationClient simulation.SimulationClient) *fePay.Service {
	wire.Build(fePay.NewService)
	return &fePay.Service{}
}

func InitializeCustomerAuthCallBackService(cxClient beCxPb.CustomerAuthCallbackClient) *feCx.Service {
	wire.Build(feCx.NewService)
	return &feCx.Service{}
}

func InitializeFaqService(Faqclient beFaqPb.ServeFAQClient, beRaClient beRaPb.RecentActivityClient, genconf *genconf.Config,
	eventBroker events.Broker) *feFaq.Service {
	wire.Build(feFaq.NewService)
	return &feFaq.Service{}
}

func InitializeContactUsService(issueReportingClient irPb.ServiceClient, genconf *genconf.Config,
	ticketClient beTicketPb.TicketClient, eventBroker events.Broker, userClient user.UsersClient,
	issueConfigManagementClient issueConfigPb.IssueConfigManagementClient, userGroupClient userGroupPb.GroupClient,
	actorClient beActorPb.ActorClient, customerAuthClient beCxPb.CustomerAuthenticationClient,
	faqClient faq.FAQClient, feTicketClient feTicketPb.TicketClient, riskProfileClient profilePb.ProfileClient) *feContact.Service {
	wire.Build(feContact.NewService, cxConfigProvider, contactUsConfigProvider, release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider)
	return &feContact.Service{}
}

func contactUsConfigProvider(dynConf *genconf.Config) *genconf.InAppContactUsFlowConfig {
	return dynConf.InAppContactUsFlowConfig()
}

func InitializeHomeService(
	conf *config.Config,
	genconf *genconf.Config,
	savClient savingsPb.SavingsClient,
	searchClient beSearchPb.ActionBarClient,
	orderClient order.OrderServiceClient,
	actorClient beActorPb.ActorClient,
	timelineClient beTimelinePb.TimelineServiceClient,
	piClient bePiPb.PiClient,
	accountPiClient beAccountPiPb.AccountPIRelationClient,
	depositClient beDepositPb.DepositClient,
	userGroupClient userGroupPb.GroupClient,
	userClient beUserPb.UsersClient,
	txnAggClient txnaggregates.TxnAggregatesClient,
	rmsRuleManagerClient manager.RuleManagerClient,
	mfCatalogManagerClient fundcatalogpb.CatalogManagerClient,
	recurringPaymentClient beRecurringPayment.RecurringPaymentServiceClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	nudgesClient nudgeBePb.NudgeServiceClient,
	kycClient kyc.KycClient,
	salaryProgramClient beSalaryPb.SalaryProgramClient,
	ffAccountingClient ffAccountsPb.AccountingClient,
	vkycFeClient beVkycPb.VKYCFeClient,
	beTieringCient beTieringPb.TieringClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	storyClient beStoryPb.StoryClient,
	employmentClient employment.EmploymentClient,
	commsClient commstypes.CommsClientWithInterceptors,
	beConnectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	segmentationClient segment.SegmentationServiceClient,
	ffBeClient ffBePb.FireflyClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	homeRedisStore types.HomeRedisStore,
	upcomingTxnsClient upcomingtransactions.UpcomingTransactionsClient,
	merchantClient mPb.MerchantServiceClient,
	questManagerClient questManagerPb.ManagerClient,
	authClient beAuthPb.AuthClient,
	vendorMappingClient vmPb.VendorMappingServiceClient,
	storage pkgTypes.QuestCacheStorage,
	eventsBroker events.Broker,
	palBeClient palBePb.PreApprovedLoanClient,
	consentClient consent.ConsentClient,
	cardProvisioningClient beCard.CardProvisioningClient,
	offerCatalogServiceClient beCasperPb.OfferCatalogServiceClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	productClient product.ProductClient,
	alfredClient alfredPb.AlfredClient,
	rewardAggregateClient rewardspinotpb.RewardsAggregatesClient,
	rewardProjectionClient rewardsProjectionPb.ProjectorServiceClient,
	rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient,
	accountManagerClient usstocksAccountPb.AccountManagerClient,
	crossAttachClient crossAttachPb.CrossAttachClient,
	healthInsuranceClient healthinsurancePb.HealthInsuranceClient,
	rewardOffersClient beRewardOffersPb.RewardOffersClient,
	networthClient beNetWorthPb.NetWorthClient,
	lendabilityClient lendabilityPb.LendabilityClient,
	variableGeneratorClient analyserVariablesPb.VariableGeneratorClient,
	beFireflyV2Client ffBeV2Pb.FireflyV2Client,
) *home.Service {
	wire.Build(home.NewService, home.NewRecentActivitiesDataCollector, release.EvaluatorWireSet,
		commstypes.CommsClientProvider,
		datetime.WireDefaultTimeSet,
		helper3.UpcomingTransactionsHelperWireSet,
		CcDashboardWarningSvcProvider,
		PlDashboardWarningSvcProvider,
		PlNavigationBarHighlightSvcProvider,
		USSNavigationBarHighlightSvcProvider,
		RewardsNavigationBarHighlightSvcProvider,
		NetworthNavBarHighlightSvcProvider,
		provider.FeatureReleaseConfigProvider,
		types.HomeRedisClientProvider,
		InitializeQuestSdkClient,
		helper4.RpcHelperWireSet,
		layoutconfiguration.EngineWireSet,
		attributes.AttributeEvaluatorFactoryWireSet,
		attributes.NewSegmentExprAttributeEvaluator,
		attributes.NewFeatureLifecycleAttributeEvaluator,
		attributes.NewUserGroupAttributeEvaluator,
		attributes.NewCrossAttachAttributeEvaluator,
		sort.SortingStrategyFactoryWireSet,
		sort.NewPrioritySortingStrategy,
		userPkg.WireSet,
		wire.NewSet(tieringRelease.NewFeReleaseManagerService, wire.Bind(new(tieringRelease.FeManager), new(*tieringRelease.FeManagerService))),
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		data_collector.DataCollectorWireSet,
		deeplink_builder.DeeplinkBuilderWireset,
	)
	return &home.Service{}
}

func InitializeHomeOrchestratorService(
	homeFeClient homeFePb.HomeClient,
	fcmFeClient fcmFePb.FCMClient,
	investmentFeClient investmentFePb.InvestmentAggregatorClient,
	fireflyFeClient fireflyFePb.FireflyClient,
	palFeClient palFePb.PreApprovedLoanClient,
	networthFeClient networthFePb.NetWorthClient,
	caFeClient connectedAccountFePb.ConnectedAccountClient,
	analyserFeClient analyserFePb.AnalyserServiceClient,
	searchFeClient searchFePb.SearchClient,
	referralFeClient referralFePb.ReferralClient,
	deFeClient deFePb.DynamicElementsClient,
	nudgeFeClient nudgeFePb.NudgeServiceClient,
	rewardsFeClient rewardsFePb.RewardsClient,
	cxHomeFeClient cxHomeFePb.HomeClient,
	secretsFeClient secretsFePb.SecretsClient,
	journeyFeClient nudgeFePb.JourneyServiceClient,
	cardFePb feCardPb.CardClient,
) *homeOrch.Service {
	wire.Build(homeOrch.NewService,
		homeOrchScrollableDashboardComponent.DashboardComponentViewGetterFactoryWireSet,
		homeOrchScrollableDashboardComponent.AllDashboardComponentViewGettersWireSet,
		homeOrchScrollableComponent.ComponentGeneratorFactoryWireSet,
		homeOrchScrollableComponent.AllComponentGeneratorWireSet,
		homeOrchStickyBottomComponent.ComponentGeneratorFactoryWireSet,
		homeOrchStickyBottomComponent.AllComponentGeneratorWireSet,
		homeOrchStickyTopComponent.ComponentGeneratorFactoryWireSet,
		homeOrchStickyTopComponent.AllComponentGeneratorWireSet,
	)
	return &homeOrch.Service{}
}

func InitializeQuestSdkClient(
	questCacheStorage pkgTypes.QuestCacheStorage,
	genconf *genconf.Config,
	segmentClient segment.SegmentationServiceClient,
	actorClient beActorPb.ActorClient,
	usersClient beUserPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	questManagerCl questManagerPb.ManagerClient,
	eventsBroker events.Broker) *questSdk.Client {
	wire.Build(
		QuestCacheStorageProvider,
		QuestSDKClientConfProvider,
		questSdkInit.GetQuestSDKClient,
	)
	return &questSdk.Client{}
}

func InitializeDepositService(depositClient beDepositPb.DepositClient, orderClient order.OrderServiceClient,
	piClient bePiPb.PiClient, savingsClient savingsPb.SavingsClient, usersClient beUserPb.UsersClient,
	upiClient beUPIPb.UPIClient, accountPiClient beAccountPiPb.AccountPIRelationClient,
	userGroupClient userGroupPb.GroupClient, actorClient beActorPb.ActorClient, goalsClient goalsPb.GoalsClient,
	ruleManagerClient manager.RuleManagerClient,
	accountStatementClient statementPb.AccountStatementClient,
	heClient health_engine.HealthEngineServiceClient,
	conf *genconf.Config, fireflyAccountingClient ffAccountsPb.AccountingClient, accountBalanceClient accountBalancePb.BalanceClient) *feDeposit.Service {
	wire.Build(
		feDeposit.NewService,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
		provider.ABFeatureReleaseConfigProvider,
		datetime.WireDefaultTimeSet,
		wire.NewSet(asg.NewAutoSaveSuggestionsImpl, wire.Bind(new(asg.AutoSaveSuggestions), new(*asg.AutoSaveSuggestionsImpl))),
		accessor.NewFireFlyAccessor,
		accessor.NewUserAccessor,
		depositCreation.WireSet,
	)
	return &feDeposit.Service{}
}

func InitializeChatService(cxChatClient beChatPb.ChatsClient, conf *genconf.Config) *chat.Service {
	wire.Build(chat.NewService)
	return &chat.Service{}
}

func InitializeCallService(conf *genconf.Config, cxCallRoutingClient beCallRoutingPb.CallRoutingClient, customerAuthClient beCxPb.CustomerAuthenticationClient) *call.Service {
	wire.Build(call.NewService)
	return &call.Service{}
}

func InitializeActorActivitiesService(
	actorActivityClient actorActivityPb.ActorActivityClient,
	savingsClient savingsPb.SavingsClient,
	cardProvisioningClient provisioning.CardProvisioningClient,
	piClient bePiPb.PiClient,
	conf *config.Config,
	depositClient beDepositPb.DepositClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	categorizerClient beCategorizerPb.TxnCategorizerClient,
	actorClient beActorPb.ActorClient,
	userGroupClient userGroupPb.GroupClient,
	userClient beUserPb.UsersClient,
	accountPiRelationClient beAccountPiPb.AccountPIRelationClient,
	dynconf *genconf.Config,
	ffAccountingClient ffAccountsPb.AccountingClient,
	timelineClient beTimelinePb.TimelineServiceClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	orderClient orderSerivcePb.OrderServiceClient,
	rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	feSearchClient search2.SearchClient,
	fireflyClient2 fireflyV2Pb.FireflyV2Client,
	tieringClient beTieringPb.TieringClient,
) *feActorActivityService.Service {
	wire.Build(
		feActorActivityService.NewService,
		caFiToFi.ConnectedAccountFeatureFiToFiWireSet,
		caFiToFiHelper.ConnectedAccountFeatureFiToFiHelperWireSet,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider)
	return &feActorActivityService.Service{}
}

func InitializeRecurrentPaymentsService(
	recurringPaymentClient beRecurringPayment.RecurringPaymentServiceClient,
	actorClient beActorPb.ActorClient,
	piClient bePiPb.PiClient,
	accountPiClient beAccountPiPb.AccountPIRelationClient,
	orderClient order.OrderServiceClient,
	savingClient savingsPb.SavingsClient,
	payClient paymentPb.PaymentClient,
	dynconf *genconf.Config,
	upiClient beUPIPb.UPIClient,
	mandateClient mandatePb.MandateServiceClient,
	upiOnbClient upiOnboardingPb.UpiOnboardingClient,
	ticketClient beTicketPb.TicketClient,
	usersClient user.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	palClient preapprovedloan.PreApprovedLoanClient,
) *feRecurringPayment.Service {
	wire.Build(feRecurringPayment.NewService,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider)
	return &feRecurringPayment.Service{}
}

func InitializeAuthService(
	authClient beAuthPb.AuthClient,
	biometricClient biometrics.BiometricsServiceClient,
	conf *genconf.Config,
	authOrchClient authV2Pb.OrchestratorClient,
	usersClient beUserPb.UsersClient,
	livClient liveness.LivenessClient,
	actorClient beActorPb.ActorClient,
) *feAuth.Service {
	wire.Build(feAuth.NewAuthService)
	return &feAuth.Service{}
}

func InitializeRewardsService(
	rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient,
	rewardOffersClient beRewardOffersPb.RewardOffersClient,
	offerRedemptionClient beRedemptionPb.OfferRedemptionServiceClient,
	accrualClient beAccrualPb.AccrualClient,
	tieringClient beTieringPb.TieringClient,
	offerListingClient beCasperPb.OfferListingServiceClient,
	offerCatalogClient beCasperPb.OfferCatalogServiceClient,
	salaryProgramClient beSalaryPb.SalaryProgramClient,
	savingsClient savingsPb.SavingsClient,
	luckyDrawClient luckydrawPb.LuckyDrawServiceClient,
	usersClient beUserPb.UsersClient,
	depositClient beDepositPb.DepositClient,
	exchangerOfferClient beExchangerPb.ExchangerOfferServiceClient,
	actorServiceClient beActorPb.ActorClient,
	conf *config.Config,
	dyconf *genconf.Config,
	offerInventoryServiceClient beCasperPb.OfferInventoryServiceClient,
	bcClient bankCustPb.BankCustomerServiceClient,
	fireflyClient beFireflyPb.FireflyClient,
	vendorMappingClient vendormappingPb.VendorMappingServiceClient,
	cardClient provisioning.CardProvisioningClient,
	userGroupClient userGroupPb.GroupClient,
	externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient,
	segmentationServiceClient segment.SegmentationServiceClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	preApprovedLoanClient palBePb.PreApprovedLoanClient,
	questCacheStorage pkgTypes.QuestCacheStorage,
	questManagerClient questManagerPb.ManagerClient,
	eventBroker events.Broker,
	networthClient beNetWorthPb.NetWorthClient,
	fireflyAccountingClient fireflyaccpb.AccountingClient,
	fireflyClient2 fireflyV2Pb.FireflyV2Client,
	rewardAggregateClient rewardspinotpb.RewardsAggregatesClient,
) *feRewards.RewardService {
	wire.Build(
		feRewards.NewRewardsService,
		RewardsFrontendMetaProvider,
		feRewardsOfferDisplay.EngineWireSet,
		feRewardsOfferDisplay.CriteriaEvaluatorFactoryWireSet,
		feRewardsOfferDisplay.NewCreditCardCriteriaEvaluator,
		feRewardsOfferDisplay.NewSalaryCriteriaEvaluator,
		feRewardsOfferDisplay.NewCatalogNewUserCriteriaEvaluator,
		feRewardsOfferDisplay.NewSavingsAccountCriteriaEvaluator,
		feRewardsOfferWidget.OfferWidgetGeneratorFactoryWireSet,
		feRewardsOfferWidget.NewHomeOfferWidgetGenerator,
		feRewardsOfferWidget.NewHomeCatalogOffersWidgetGenerator,
		feRewardsOfferWidget.NewHomeCardOffersWidgetGenerator,
		feRewardsOfferWidget.NewRewardDetailsOfferWidgetGenerator,
		feRewardsOfferWidget.NewCCMerchantRewardsOfferWidgetGenerator,
		feRewardsOfferWidget.NewCCDashboardOfferWidgetGenerator,
		feRewardsOfferWidget.NewMyRewardsOffersWidgetGenerator,
		feRewardsOfferWidget.NewSalaryBenefitsOfferWidgetGenerator,
		feRewardsOfferWidget.NewMappingsManager,
		earnedrewardshistory.WireSet,
		feRewardsTags.NewManager,
		NewInMemoryCacheStorage,
		provider.FeatureReleaseConfigProvider,
		release.EvaluatorWireSet,
		userPkg.WireSet,
		InitializeQuestSdkClient,
	)
	return &feRewards.RewardService{}
}

func NewInMemoryCacheStorage() types.RewardsCacheStorage {
	return cache.NewInMemoryCacheService()
}

func InitializeSalaryProgramService(
	salaryProgramClient beSalaryPb.SalaryProgramClient,
	salaryReferralsClient beSalaryReferralsPb.ReferralsClient,
	rewardGeneratorClient beRewardsPb.RewardsGeneratorClient,
	healthInsuranceClient healthinsurancePb.HealthInsuranceClient,
	rewardOffersClient beRewardOffersPb.RewardOffersClient,
	employmentClient beEmploymentPb.EmploymentClient,
	actorClient beActorPb.ActorClient,
	savingsClient savingsPb.SavingsClient,
	usersClient beUserPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	alfredClient alfredPb.AlfredClient,
	provisioningClient beCard.CardProvisioningClient,
	projectionClient rewardsProjectionPb.ProjectorServiceClient,
	txnAggregateClient txnAggregatesPb.TxnAggregatesClient,
	conf *config.Config,
	epifiIconsBucketS3Client types.EpifiIconsS3Client,
	salaryProgramBucketS3Client types.SalaryProgramS3Client,
	dconf *genconf.Config,
	actorActivityClient actorActivityPb.ActorActivityClient,
	inAppReferralClient beInAppReferralPb.InAppReferralClient,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	vgEmploymentClient vgEmploymentPb.EmploymentClient,
	vgEmployerNameMatchClient employerNameMatchVgPb.EmployerNameMatchClient,
	preApprovedLoanClient palBePb.PreApprovedLoanClient,
	recurringPaymentClient beRecurringPayment.RecurringPaymentServiceClient,
	enachClient enachPb.EnachServiceClient,
	orderServiceClient orderSerivcePb.OrderServiceClient,
	txnCategorizerClient categorizer.TxnCategorizerClient,
	segmentClient segment.SegmentationServiceClient,
	upiOnbClient upiOnboardingPb.UpiOnboardingClient,
	accountPiRelationClient beAccountPiPb.AccountPIRelationClient,
	caClient beConnectedAccPb.ConnectedAccountClient,
	tieringClient tieringPb.TieringClient,
	rewardAggregateClient rewardspinotpb.RewardsAggregatesClient,
	consentClient consent.ConsentClient,
	accountManagerClient usstocksAccountPb.AccountManagerClient,
) *feSalaryPgPb.Service {
	wire.Build(
		feSalaryPgPb.NewService,
		provider.FeatureReleaseConfigProvider,
		types.EpifiIconsS3ClientProvider,
		release.EvaluatorWireSet,
		salaryBenefits.SalaryProgramBenefitsWireSet,
		aaSalary.AaSalaryFeWireSet,
		data_collector.DataCollectorWireSet,
	)
	return &feSalaryPgPb.Service{}
}

func InitializePartnerSDKService(partnerSDKClient bePartnerSDKPb.PartnerSDKClient) *fePartnerSDK.Service {
	wire.Build(fePartnerSDK.NewService)
	return &fePartnerSDK.Service{}
}

func InitializeLivenessService(livenessClient liveness.LivenessClient) *feLiv.Service {
	wire.Build(feLiv.NewService)
	return &feLiv.Service{}
}

func InitializeAccessInfoService(accessInfoClient beAccessinfoPb.AccessInfoClient,
	authClient beAuthPb.AuthClient, actorClient beActorPb.ActorClient, userClient beUserPb.UsersClient) *feAccessInfo.Service {
	wire.Build(feAccessInfo.NewService)
	return &feAccessInfo.Service{}
}

func InitializeEmailParserService(emailParserClient beEmailParserPb.EmailParserClient) *feEmailParser.Service {
	wire.Build(feEmailParser.NewService)
	return &feEmailParser.Service{}
}

func getDisputeConf(genConf *genconf.Config) *genconf.Dispute {
	return genConf.Dispute()
}
func InitializeDisputeService(
	disputeClient disputePb.DisputeClient,
	paymentClient paymentPb.PaymentClient,
	disputeConf *genconf.Config,
	orderClient orderSerivcePb.OrderServiceClient,
	actorClient beActorPb.ActorClient,
	userClient user.UsersClient,
	userGroupClient userGroupPb.GroupClient,
) *dispute.Service {
	wire.Build(
		getDisputeConf,
		dispute.NewService,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
	)
	return &dispute.Service{}
}

func InitializeAppLogService(appLogClient beALPb.AppLogClient, authClient beAuthPb.AuthClient, genConf *genconf.Config) *app_logs.Service {
	wire.Build(app_logs.NewService)
	return &app_logs.Service{}
}

func FlagsProvider(conf *config.Config) *config.Flags { return conf.Flags }

func InitializeVkycService(vkycClient beVkycPb.VKYCClient, onbClient userOnboardingPb.OnboardingClient,
	userClient beUserPb.UsersClient, actorClient beActorPb.ActorClient, userGroupClient userGroupPb.GroupClient,
	genConf *genconf.Config, kycClient kyc.KycClient, savingsClient savingsPb.SavingsClient, locationClient location2.LocationClient, conf *config.Config,
	bcClient bankCustPb.BankCustomerServiceClient, panClient panPb.PanClient, accountBalanceClient accountBalancePb.BalanceClient) *vkyc.Service {
	wire.Build(vkyc.NewService, FlagsProvider, release.EvaluatorWireSet, provider.FeatureReleaseConfigProvider,
		datetime.WireDefaultTimeSet)
	return &vkyc.Service{}
}

func InitializeFitttService(
	conf *genconf.Config,
	rmsClient manager.RuleManagerClient,
	depositClient beDepositPb.DepositClient,
	fitttClient fitttPb.FitttClient,
	userClient beUserPb.UsersClient,
	actorClient beActorPb.ActorClient,
	userGroupClient userGroupPb.GroupClient,
	searchClient search.ActionBarClient,
	fundCatalogClient fundcatalogpb.CatalogManagerClient,
	wealthOnboardingClient feWoPb.WealthOnboardingClient,
	investPayClient investpaypb.PaymentHandlerClient,
	investOrderClient orderPb.OrderManagerClient,
	broker events.Broker,
	fitttSportsManagerClient sports.SportsManagerClient,
	rmUIClient ui.RuleUIManagerClient,
	rewardsClient beRewardsPb.RewardsGeneratorClient,
	luckyDrawClient luckydrawPb.LuckyDrawServiceClient,
	savingsClient savingsPb.SavingsClient,
	recurringPaymentClient beRecurringPayment.RecurringPaymentServiceClient,
	timelineClient feTimelinePb.TimelineServiceClient,
	userOnboardingClient userOnboardingPb.OnboardingClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	usStocksCatalogManagerClient usstocksCatalogPb.CatalogManagerClient,
	bankCustomerServiceClient bankCustPb.BankCustomerServiceClient,
	vgIftClient vgIftPb.InternationalFundTransferClient,
	iftClient iftPb.InternationalFundTransferClient,
	payClient payPb.PayClient,
) (*fittt.Service, error) {
	wire.Build(
		release.EvaluatorWireSet,
		pkgFittt.NewStandingInstructionFitttAggregator,
		provider.FeatureReleaseConfigProvider,
		iftGSTPkg.SlabRateBasedGSTCalculatorWireSet,
		iftTCSPkg.APIBasedTCSCalculatorWireSet,
		iftTCSPkg.SlabRateBasedTCSCalculatorWireSet,
		iftInvoicePkg.SIPInvoiceCalculatorWireSet,
		iftPkg.RecurringOutwardRemittanceWireSet,
		pkgFittt.USStocksWireSet,
		fittt.NewFitttService,
		a2form.FederalA2FormWireSet,
		usStocksPkg.SIPParamsValidationWireSet,
	)
	return &fittt.Service{}, nil
}

func InitializeClientLoggerService(authClient beAuthPb.AuthClient) *clientlogger.Service {
	wire.Build(clientlogger.NewService)
	return &clientlogger.Service{}
}
func ReferralConfigProvider(conf *config.Config) *config.Referrals { return conf.Referrals }
func ReferralsColourMapProvider(conf *config.Config) *config.ReferralsColourMap {
	return conf.ReferralsColourMap
}
func InitializeReferralService(
	actorClient beActorPb.ActorClient, onbClient userOnboardingPb.OnboardingClient,
	inAppReferralClient beInAppReferralPb.InAppReferralClient, seasonsClient seasonPb.SeasonServiceClient,
	rewardsClient beRewardsPb.RewardsGeneratorClient, userClient beUserPb.UsersClient, userGrpClient userGroupPb.GroupClient,
	userContactClient userContactPb.ContactClient, nudgeClient nudgeBePb.NudgeServiceClient, conf *config.Config, dyconf *genconf.Config, broker events.Broker,
) *referral.Service {
	wire.Build(provider.FeatureReleaseConfigProvider, ReferralConfigProvider, ReferralsColourMapProvider, release.EvaluatorWireSet, referral.NewService)
	return &referral.Service{}
}

func InitializeInsightsService(conf *config.Config, beInsightsClient beInsightsPb.InsightsClient) *insights.Service {
	wire.Build(insights.NewService)
	return &insights.Service{}
}

func InitializeScreeningService(employmnetClient beEmploymentPb.EmploymentClient,
	onbClient userOnboardingPb.OnboardingClient, actorClient beActorPb.ActorClient,
	consentClient consent.ConsentClient, usersClient beUserPb.UsersClient, scrClient screener.ScreenerClient, broker events.Broker, genConf *genconf.Config, config2 *config.Config,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient) *screening.Service {
	wire.Build(screening.NewService, ScreeningConfigProvider)
	return &screening.Service{}
}

func InitializeConnectedAccService(
	beConnectedAccClient beConnectedAccPb.ConnectedAccountClient,
	beUserClient beUserPb.UsersClient,
	beActorClient beActorPb.ActorClient,
	beSavingsClient savingsPb.SavingsClient,
	beAuthClient beAuthPb.AuthClient,
	beConsentClient consent.ConsentClient,
	userGroupClient userGroupPb.GroupClient,
	onbClient userOnboardingPb.OnboardingClient,
	scrClient screener.ScreenerClient,
	conf *config.Config,
	dynconf *genconf.Config,
	celestialClient celestialPb.CelestialClient,
	accountManagerClient usstocksAccountPb.AccountManagerClient,
	segmentationClient segment.SegmentationServiceClient,
	depositClient beDepositPb.DepositClient,
	netWorthClient beNetWorthPb.NetWorthClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	palClient palBePb.PreApprovedLoanClient,
	eventBroker events.Broker,
	epfClient epfPb.EpfClient,
	investmentAnalyticsClient investment.InvestmentAnalyticsClient,
	creditReportClient creditReportV2Pb.CreditReportManagerClient,
) *connected_account.Service {
	wire.Build(
		release.EvaluatorWireSet,
		connected_account.NewService,
		provider.FeatureReleaseConfigProvider,
		ConnectedAccountConfigProvider,
		processor.NewCaFlowDefaultConnectedAccountProcessor,
		processor.NewCaFlowScreenerProcessor,
		processor.NewCaFlowUsStocksBuyProcessor,
		processor.NewCaFlowUsStocksOnboardingProcessor,
		processor.NewCaFlowConnectedAccountRenewalProcessor,
		userPkg.WireSet,
		insightsPkg.InsightsPanProcessorWireSet,
		deeplink_builder.DeeplinkBuilderWireset,
		caFiToFiHelper.ConnectedAccountFeatureFiToFiHelperWireSet,
		wire.NewSet(
			caFactory.NewCaFlowDLFactorySvc,
			wire.Bind(new(caFactory.ICaFlowDLFactory), new(*caFactory.CaFlowDLFactorySvc)),
			processor.NewCaFlowConnectFiToFiProcessor,
			processor.NewCAFlowNetWorthProcessor,
			processor.NewCAFlowNetWorthIndStocksProcessor,
			processor.NewCAFlowLoanEligibilityProcessor,
			processor.NewCaFlowAaSalaryProcessor,
			processor.NewCAFlowNetWorthNpsProcessor,
			processor.NewCAUnifiedFlowProcessor,
			processor.NewCaFlowSalaryEstimationProcessor,
			processor.NewCaFlowWealthBuilderOnboardingProcessor,
		),
	)
	return &connected_account.Service{}
}

func InitializeClientMetricsService(rmsClient manager.RuleManagerClient, fitttClient fitttPb.FitttClient,
	rmsUIClient ui.RuleUIManagerClient, conf *config.Config) *client_metrics.ClientMetricsService {
	wire.Build(client_metrics.NewClientMetricsService)
	return &client_metrics.ClientMetricsService{}
}

func InitializeQRService(
	cardProvisioningClient provisioning.CardProvisioningClient,
	feCardClient feCardPb.CardClient,
	feTimelineClient feTimelinePb.TimelineServiceClient,
	conf *config.Config,
	recurringPaymentClient beRecurringPayment.RecurringPaymentServiceClient,
	upiClient beUPIPb.UPIClient,
	piClient bePiPb.PiClient,
	actorClient beActorPb.ActorClient,
	merchantServiceClient mPb.MerchantServiceClient,
	orderServiceClient orderSerivcePb.OrderServiceClient,
	mandateServiceClient mandatePb.MandateServiceClient,
	timelineClient beTimelinePb.TimelineServiceClient,
	accountPiRelationClient beAccountPiPb.AccountPIRelationClient,
	fireflyClient ffBePb.FireflyClient,
	dynConf *genconf.Config,
	upiOnbClient upiOnboardingPb.UpiOnboardingClient,
	userOnboardingClient userOnboardingPb.OnboardingClient,
	savingsClient savingsPb.SavingsClient,
	eventBroker events.Broker,
) *qr.Service {
	wire.Build(qr.NewService)
	return &qr.Service{}
}

func InitializeWealthOnboardingService(beWealthOnboardingClient woPb.WealthOnboardingClient, consentClient consent.ConsentClient,
	actorClient beActorPb.ActorClient, userClient beUserPb.UsersClient, config2 *genconf.Config,
	authClient beAuthPb.AuthClient, eventBroker events.Broker, investmentProflileClient investmentProfilePb.InvestmentProfileServiceClient) *feWo.WealthOnboardingService {
	wire.Build(feWo.NewWealthOnboardingService)
	return &feWo.WealthOnboardingService{}
}

func InitializeAppFeedbackService(beClient appFeedbackPb.AppFeedbackClient) *feAppFeedback.Service {
	wire.Build(
		feAppFeedback.NewAppFeedbackService,
	)
	return &feAppFeedback.Service{}
}

func InitializeApiKeysService(conf *config.Config, genConfig *genconf.Config) (*apikeys.Service, error) {
	wire.Build(
		genconf.GetDeviceIdsForSafetynetV2Flow,
		apikeys.NewApiKeysService,
	)
	return &apikeys.Service{}, nil
}

func InitializeInAppHelpMediaService(beClient mediaPb.InAppHelpMediaClient) *helpMedia.Service {
	wire.Build(
		helpMedia.NewInAppHelpMediaService,
	)
	return &helpMedia.Service{}
}

func InitializeInvestmentService(
	catalogClient catalogPb.CatalogManagerClient,
	rmsClient manager.RuleManagerClient,
	phClient phPb.PaymentHandlerClient,
	orderManagerClient orderPb.OrderManagerClient,
	wealthOnBoardingClient woPb.WealthOnboardingClient,
	feWealthOnBoardingClient feWoPb.WealthOnboardingClient,
	vkycClient beVkycPb.VKYCClient,
	transactionFeClient feTransactionPb.TransactionClient,
	payClient payPb.PayClient,
	savingsClient savingsPb.SavingsClient,
	omsClient order.OrderServiceClient,
	timeline beTimelinePb.TimelineServiceClient,
	actorClient beActorPb.ActorClient,
	authClient beAuthPb.AuthClient,
	investmentAuth auth2.AuthClient,
	config2 *genconf.Config,
	userClient beUserPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	eventBroker events.Broker,
	mfNotificationClient mfNotiPb.NotificationsClient,
	fitttClient fitttPb.FitttClient,
	consentClient consent.ConsentClient,
	mfExternalClient mfExternalPb.MFExternalOrdersClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
	creditReportClient creditReportV2Pb.CreditReportManagerClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	networthClient beNetWorthPb.NetWorthClient,
) *feInvestment.Service {
	wire.Build(
		release.EvaluatorWireSet,
		feInvestment.NewInvestmentService,
		provider.FeatureReleaseConfigProvider,
		datetime.WireDefaultTimeSet,
		insightsPkg.InsightsPanProcessorWireSet)
	return &feInvestment.Service{}
}

func InitializeDynamicElementsService(beDynamicElementsClient dewiretypes.DynamicElementsClientWithInterceptors, config *genconf.Config) *feDe.Service {
	wire.Build(
		dewiretypes.DynamicElementsClientProvider,
		feDe.NewDynamicElementsService,
	)
	return &feDe.Service{}
}

func InitializeP2PInvestmentService(p2PInvestmentClient p2pPbBe.P2PInvestmentClient, userClient beUserPb.UsersClient,
	actorClient beActorPb.ActorClient, omsClient order.OrderServiceClient, riskProfileClient profilePb.ProfileClient, consentClient consent.ConsentClient, userGroupClient userGroupPb.GroupClient,
	conf *genconf.Config, client userOnboardingPb.OnboardingClient, tieringClient tieringPb.TieringClient) *p2p.Service {
	wire.Build(
		release.EvaluatorWireSet,
		wire.NewSet(helper.NewRpcHelper, wire.Bind(new(helper.IRpcHelper), new(*helper.RpcHelper)), provider.GetZeroStateDashboardABEvaluatorProvider),
		deeplinks.GeneratorWireSet,
		p2p.NewService,
		provider.FeatureReleaseConfigProvider,
		provider.ABFeatureReleaseConfigProvider,
	)
	return &p2p.Service{}
}

func InitializePreApprovedLoanService(preApprovedLoanClient palBePb.PreApprovedLoanClient, consentClient consent.ConsentClient,
	vkycClient beVkycPb.VKYCClient, savingsClient savingsPb.SavingsClient, actorClient beActorPb.ActorClient, eventsBroker events.Broker,
	conf *config.Config, usersClient beUserPb.UsersClient, employmentClient beEmploymentPb.EmploymentClient, genconf *genconf.Config,
	onbClient userOnboardingPb.OnboardingClient, segmentationServiceClient segment.SegmentationServiceClient, userGroupClient userGroupPb.GroupClient,
	accountBalanceClient accountBalancePb.BalanceClient, mfCatalogClient catalogPb.CatalogManagerClient,
	authClient beAuthPb.AuthClient, ffVgClient fiftyfin.FiftyFinClient, client securedLoansPb.SecuredLoansClient, searchClient beSearchPb.ActionBarClient, userLocationClient location2.LocationClient, frontendClient frontendPB.RecurringPaymentServiceClient, payClient payPb.PayClient) *fePreApprovedLoan.Service {
	wire.Build(
		baseprovider.WireSet,
		palFeAggregatedDeeplinks.NewAggregatedDeeplinkProvider,
		deeplinks2.WireDeeplinkProviderFactorySet,
		palFeLLDeeplink.NewLiquiloansProvider,
		palFeFederalDeeplink.NewFederalLoansProvider,
		palFeFederalDeeplink.NewFederalRealTimeProvider,
		palFeFederalDeeplink.NewRealTimeNtbFedProvider,
		palFeLLDeeplink.NewLiquiloansEarlySalaryProvider,
		palFeIdfcDeeplink.NewIdfcProvider,
		palFeLLDeeplink.NewFldgProvider,
		palFeLLDeeplink.NewStplProvider,
		palFeLLDeeplink.NewFiLiteProvider,
		palFeFfDeeplink.NewLamfProvider,
		palFeAbflDeeplink.NewAbflProvider,
		palFeAbflDeeplink.NewPwaJourneyProvider,
		palFeStockGuradianDeeplink.NewStockGuardianProvider,
		palFeStockGuradianDeeplink.NewStockGuardianProviderEarlySalary,
		palLdcDeeplink.NewLendenProvider,
		palFeLLDeeplink.NewRealTimeSubventionProvider,
		palFeLLDeeplink.NewRealTimeStplProvider,
		palFeLLDeeplink.NewNonFiCoreStplProvider,
		palFeLLDeeplink.NewNonFiCoreSubventionProvider,
		helper4.RpcHelperWireSet,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
		fePreApprovedLoan.NewService,
		palFeLLDeeplink.NewAcqToLendProvider,
		datetime.WireDefaultTimeSet,
		getPreApprovedLoanConfig,
		palFeMvDeeplink.NewMvProvider,
		palFeMvDeeplink.NewNonFiCoreMvProvider,
		palRteDeeplink.NewRealTimeEtbProvider,
		palFeLLDeeplink.NewRealTimeDistProvider,
		getLendingConfig,
		epifitech.NewEligibilityProvider,
		consent2.RecordP2PPreBreConsentWireSet,
	)
	return &fePreApprovedLoan.Service{}
}

func getPreApprovedLoanConfig(conf *genconf.Config) *genconf.PreApprovedLoan {
	return conf.Lending().PreApprovedLoan()
}

func getLendingConfig(conf *genconf.Config) *genconf.Lending {
	return conf.Lending()
}

func InitializeCategorizerService(txnCategorizerClient categorizer.TxnCategorizerClient, orderClient order.OrderServiceClient,
	aaOrderClient aaOrderPb.AccountAggregatorClient, conf *config.Config) *categorizerFe.Service {
	wire.Build(categorizerFe.NewTxnCategorizerService, TxnCatParamsProvider, SimilarActivityParamsProvider)
	return &categorizerFe.Service{}
}

func InitializeAnalyserService(config *config.Config, dynConf *genconf.Config,
	actorClient beActorPb.ActorClient,
	userGroupClient userGroupPb.GroupClient,
	caClient beConnectedAccPb.ConnectedAccountClient,
	savingsClient savingsPb.SavingsClient, txnAggregatesClient txnAggregatesPb.TxnAggregatesClient,
	feCategoriserClient feCategorizerPb.TxnCategorizerClient, eventsBroker events.Broker,
	merchantClient mPb.MerchantServiceClient,
	categorizerBeClient categorizer.TxnCategorizerClient,
	userClient beUserPb.UsersClient,
	creditReportClient creditReportV2Pb.CreditReportManagerClient,
	appFeedbackClient appFeedbackPb.AppFeedbackClient,
	beStoryClient beStoryPb.StoryClient, investAnalyticsClient investment.InvestmentAnalyticsClient,
	mfCatalogManagerClient catalogPb.CatalogManagerClient, client ffPinotPb.TxnAggregatesClient,
	fireflyClient ffBePb.FireflyClient, accountingClient accounting.AccountingClient,
	mfExternalClient mfExternalPb.MFExternalOrdersClient, upcomingTxnsClient upcomingtransactions.UpcomingTransactionsClient,
	depositClient beDepositPb.DepositClient, piClient bePiPb.PiClient, onboardingClient userOnboardingPb.OnboardingClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	palClient palBePb.PreApprovedLoanClient,
	tieringClient tieringPb.TieringClient,
	reminderClient reminder.ReminderServiceClient,
	segmentClient segment.SegmentationServiceClient,
	portfolioManagerClient usstocksPortfolioPb.PortfolioManagerClient,
) (*analyser.Service, error) {
	wire.Build(
		creditScoreAnalyserConfigProvider,
		filter.WireFilterProcessorFactorySet,
		filterWidget.WireFilterWidgetGeneratorFactorySet,
		deeplink.WireDeeplinkProviderSet,
		top_categories.NewCategoryAggregateFetcher,
		analyserFactory.WireAnalyserProcessorFactorySet,
		mfAnalyser.WireMFAnalyticsCollectorSet,
		analyserBannerFactory.WireBannerProcessorFactorySet,
		time.WireAccountsHelperSet,
		release.EvaluatorWireSet,
		datafetcher.ActorAccountsWireSet,
		txnHelper.TxnAggregatesWireSet,
		datafetcher.IMerchantsWireSet,
		analyserSection.WireSectionGeneratorSet,
		factory2.WidgetGeneratorFactoryWireSet,
		util.ExperimentEvaluatorWireSet,
		params_fetcher.CreditScoreParamsFetcherWireset,
		idGen.UuidGeneratorWireSet,
		feedback.AnalyserFeedbackWireSet,
		analyserWidget.WireGenericWidgetEnricherImplSet,
		reference_id_mapping.AnalyserFeedbackRefIdMapWireSet,
		analyserDataProvider.UserGroupHelperWireSet,
		analyserRelease.AnalyserReleaseEvaluatorWireSet,
		analyserConfig.WireAnalyserConfigSet,
		analyserLandingPageFactory.WireLandingPageProcessorFactorySet,
		analyserVisualcomponents.WireParallelGeneratorEvaluatorSet,
		analyserLandingPage.WirePreviewGeneratorSet,
		analyser.NewService,
		AnalyserFeatureFlagProvider,
		provider.FeatureReleaseConfigProvider,
		datetime.WireDefaultTimeSet,
		factory3.HubBannerFactoryWireSet,
		status.AnalyserStatusWireSet,
		helper2.WireAccountsHelperSet,
		helper2.WireCreditCardHelperSet,
		analyserFilterGenerator.WireFilterValueGeneratorSet,
		dataprovider.WireMutualFundCatalogProviderSet,
		analyserCardFactory.WireCardProcessorFactorySet,
		helper2.WireUpcomingTxnsHelperSet,
		mutualfunds.WireMutualFundHelperSet,
		category.WireCategoryHelperSet,
		creditScoreAnalyser.WireCreditReportFetcherSet,
		analyserExecutor.WireAnalyserRequestExecutorImplSet,
		analyserRequestHandler.WireAnalyserRequestHandlerImplSet,
		loans.WireLendingInsightsSet,
		caFiToFi.ConnectedAccountFeatureFiToFiWireSet,
		caFiToFiHelper.ConnectedAccountFeatureFiToFiHelperWireSet,
	)
	return &analyser.Service{}, nil
}

func creditScoreAnalyserConfigProvider(cfg *genconf.Config) *genconf.CreditScoreAnalyserConfig {
	return cfg.AnalyserParams().CreditScoreAnalyserConfig()
}

func AnalyserFeatureFlagProvider(conf *config.Config) *cfg.FeatureReleaseConfig {
	return conf.Flags.AnalyserFeatureFlag
}

func InitializeConfigService(cfg *genconf.Config) (*configsvc.ConfigSvc, error) {
	wire.Build(
		genconf.GetDeviceIdsForSafetynetV2Flow,
		configsvc.NewConfigSvc,
	)
	return &configsvc.ConfigSvc{}, nil
}

func InitializeGoalsService(goalsClient beGoalsPb.GoalsClient, usersClient beUserPb.UsersClient,
	depositClient beDepositPb.DepositClient, actorClient beActorPb.ActorClient, userGroupClient userGroupPb.GroupClient,
	conf *genconf.Config, bcClient bankCustPb.BankCustomerServiceClient) *feGoals.Service {
	wire.Build(feGoals.NewService)
	return &feGoals.Service{}
}

func InitializeTicketService(ticketClient beTicketPb.TicketClient, config *genconf.Config) *ticket.Service {
	wire.Build(ticket.NewService)
	return &ticket.Service{}
}

func InitializeInvestmentAggregatorService(
	invAggrClient invAggrPb.InvestmentAggregatorClient,
	vkycClient beVkycPb.VKYCClient,
	savingsClient savingsPb.SavingsClient,
	actorClient beActorPb.ActorClient,
	investPayClient investpaypb.PaymentHandlerClient,
	config2 *genconf.Config,
	userClient beUserPb.UsersClient,
	p2PInvestmentClient p2pPbBe.P2PInvestmentClient,
	dynamicElementsFeClient dynamicElementsPb.DynamicElementsClient,
	userGroupClient userGroupPb.GroupClient,
	segmentationServiceClient segment.SegmentationServiceClient,
	mfCatalogManagerClient catalogPb.CatalogManagerClient,
	usStocksCatalogManagerClient usstocksCatalogPb.CatalogManagerClient,
	eventBroker events.Broker,
	rmsRuleManagerClient manager.RuleManagerClient,
	invEventProcessorClient investmentEventProcessorPb.EventProcessorClient,
	depositClient beDepositPb.DepositClient,
	usStocksOrderManagerClient usstocksOrderPb.OrderManagerClient,
	onbClient userOnboardingPb.OnboardingClient,
	usStocksAccountManager usstocksAccountPb.AccountManagerClient,
	dynamicUIElementSvc dynamicElementUIPb.DynamicUIElementServiceClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
) *feInvestmentAgg.Service {
	wire.Build(
		release.EvaluatorWireSet,
		provider.ABFeatureReleaseConfigProvider,
		pkgFittt.NewStandingInstructionFitttAggregator,
		feInvestmentAgg.NewService,
		retentionInvestment.RetentionWireset,
		provider.FeatureReleaseConfigProvider,
		ip.NewCollectionPlugin,
		ip.NewUSStocksPlugin,
		ip.NewHybridInstrumentsPlugin,
		ip.NewSDPlugin,
		ip.NewMutualFundsPlugin,
		ip.NewFDPlugin,
		ip.NewP2PPlugin,
		datetime.WireDefaultTimeSet,
		provider.GetInvestmentLandingMutualFundDeeplinkABEvaluatorProvider,
		provider.GetInvestmentHomeComponentABEvaluatorProvider,
		deeplink_builder.DeeplinkBuilderWireset,
	)
	return &feInvestmentAgg.Service{}
}

func InitializeInvestmentProfileService(
	investmentProfileServiceClient investmentProfilePb.InvestmentProfileServiceClient,
) *feInvestmentProfileService.Service {
	wire.Build(feInvestmentProfileService.NewService)
	return &feInvestmentProfileService.Service{}
}

func InitializeUSStocksService(
	accountManager usstocksAccountPb.AccountManagerClient,
	conf *genconf.Config,
	iftClient iftPb.InternationalFundTransferClient,
	actorClient beActorPb.ActorClient,
	catalogManagerClient usstocksCatalogPb.CatalogManagerClient,
	catalogManagerStreamClient types.USSCatalogMgrStreamClient,
	orderManagerClient usstocksOrderPb.OrderManagerClient,
	portfolioManagerClient usstocksPortfolioPb.PortfolioManagerClient,
	savingsClient savingsPb.SavingsClient,
	authClient beAuthPb.AuthClient,
	usersClient beUserPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	payClient payPb.PayClient,
	bcClient bcPb.BankCustomerServiceClient,
	broker events.Broker,
	dynamicUiSvcClient beDePbSvc.DynamicUIElementServiceClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	preApprovedLoanClient palBePb.PreApprovedLoanClient,
	ussRewardsClient ussRewardsPb.UssRewardManagerClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	rmsClient manager.RuleManagerClient,
	fitttClient fitttPb.FitttClient,
	salaryClient beSalaryPb.SalaryProgramClient,
	beTieringClient beTieringPb.TieringClient,
	provisioningClient beCard.CardProvisioningClient,
	alfredClient alfredPb.AlfredClient,
	rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	rewardProjectionClient rewardsProjectionPb.ProjectorServiceClient,
	txnAggregateClient txnAggregatesPb.TxnAggregatesClient,
	orderServiceClient orderSerivcePb.OrderServiceClient,
	employmentClient beEmploymentPb.EmploymentClient,
	healthInsuranceClient healthinsurancePb.HealthInsuranceClient,
	rewardOffersClient beRewardOffersPb.RewardOffersClient,
) (*usstocksFePb.Service, error) {
	wire.Build(
		usstocksFePb.NewUSStocksService,
		usstocksOrderDetails.NewBuyDetailsGenerator,
		usstocksOrderDetails.NewSellDetailsGenerator,
		retry.NewStrategyFromConfig,
		provider.PriceUpdatesRetryStrategyProvider,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
		activity.ActivityListWireSet,
		dropoff.BottomSheetGeneratorWireSet,
		a2form.FederalA2FormWireSet,
		data_collector.DataCollectorWireSet,
	)
	return &usstocksFePb.Service{}, nil
}

func InitializeUpiOnboardingService(
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	actorClient beActorPb.ActorClient,
	consentClient consent.ConsentClient,
	conf *config.Config,
	dynConf *genconf.Config,
	savingsClient savingsPb.SavingsClient,
	authClient beAuthPb.AuthClient,
	payClient paymentPb.PaymentClient,
	userOnboardingClient userOnboardingPb.OnboardingClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	userGroupClient userGroupPb.GroupClient,
	userClient beUserPb.UsersClient,
	eventBroker events.Broker,
	accountPiClient beAccountPiPb.AccountPIRelationClient,
) *feUpiOnb.Service {
	wire.Build(feUpiOnb.NewService)
	return &feUpiOnb.Service{}
}

func InitialiseUpiService(
	actorClient beActorPb.ActorClient,
	savingsClient savingsPb.SavingsClient,
	upiClient beUPIPb.UPIClient,
) *feUpi.Service {
	wire.Build(feUpi.NewService)
	return &feUpi.Service{}
}

func InitialiseFireflyService(fireflyClient ffBePb.FireflyClient, fireflyAccountingClient ffAccountsPb.AccountingClient,
	txnCategorizerFeClient categorizerFePb.TxnCategorizerClient, ffBillingClient ffBillPb.BillingClient, actorClient beActorPb.ActorClient,
	rewardsClient beRewardsPb.RewardsGeneratorClient, savingsClient savingsPb.SavingsClient,
	txnCategorizerBeClient beCategorizerPb.TxnCategorizerClient, userClient beUserPb.UsersClient, merchantClient mPb.MerchantServiceClient,
	fireflyLmsClient ffLmsPb.LoanManagementSystemClient, consentClient consent.ConsentClient, conf *config.Config,
	dynConf *genconf.Config, pinotClient ffPinotPb.TxnAggregatesClient, limitEstimator limitEstimatorPb.CreditLimitEstimatorClient,
	onbClient userOnboardingPb.OnboardingClient, depositClient beDepositPb.DepositClient, accountBalanceClient accountBalancePb.BalanceClient,
	rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient, cardRecommendationClient ffRePb.CardRecommendationServiceClient,
	payFeTxnClient payFeTxnPb.TransactionClient, payBeClient payPb.PayClient, client userGroupPb.GroupClient, creditReportClient creditReportV2Pb.CreditReportManagerClient,
	segmentationServiceClient segment.SegmentationServiceClient, rewardsListingClient beCasperPb.OfferListingServiceClient, redemptionClient beRedemptionPb.OfferRedemptionServiceClient,
	fireflyV2Client ffBeV2Pb.FireflyV2Client,
	questCacheStorage pkgTypes.QuestCacheStorage,
	questManagerClient questManagerPb.ManagerClient,
	eventsBroker events.Broker, rewardAggrClient rewardspinotpb.RewardsAggregatesClient, networthClient beNetWorthPb.NetWorthClient, tieringClient tieringPb.TieringClient) *firefly.Service {
	wire.Build(firefly.NewService,
		homedashboard.NewFactory,
		generator.NewApplicationInProgress,
		generator.NewApplicationNotApproved,
		generator.NewBillDue,
		generator.NewBillDuePastDueDate,
		generator.NewCardBlockedAndBillDue,
		generator.NewCardBlockedAndNoBillDue,
		generator.NewCompleteApplication,
		generator.NewGetYourCard,
		provider.FeatureReleaseConfigProvider,
		release.EvaluatorWireSet,
		generator.NewHopOnWaitlist,
		generator.NewNoBillDue,
		generator.NewNotEligibleForCard,
		generator.NewCreditCardClosed,
		billInfoValidators.BillInfoValidatorWireSet,
		billInfoValidators.NewBillInfoValidationProcessor,
		rewards.RewardProviderWireSet,
		rewards.NewRewardProvider,
		fees_and_benefits.FeesAndBenefitsProviderWireSet,
		fees_and_benefits.NewFeesAndBenefitsProvider,
		userPkg.WireSet,
		InitializeQuestSdkClient,
		firefly.NewCcIntroScreenBuilder,
	)
	return &firefly.Service{}
}

func InitializeAuthOrchestratorService(authV2Client authV2Pb.OrchestratorClient, dynConf *genconf.Config) *feAuthV2.Service {
	wire.Build(feAuthV2.NewService)
	return &feAuthV2.Service{}
}

func InitializeNudgeService(
	nudgeClient nudgeBePb.NudgeServiceClient,
	userClient beUserPb.UsersClient,
	conf *config.Config,
	dynConf *genconf.Config,
	actorClient beActorPb.ActorClient,
	userGroupClient userGroupPb.GroupClient,
	onbClient userOnboardingPb.OnboardingClient,
	segmentationClient segment.SegmentationServiceClient,
	questCacheStorage pkgTypes.QuestCacheStorage,
	questManagerClient questManagerPb.ManagerClient,
	eventBroker events.Broker,
	networthClient beNetWorthPb.NetWorthClient,
) *nudge.Service {
	wire.Build(
		nudge.NewService,
		widget.NewHomeNudgeWidgetGenerator,
		widget.NudgeWidgetGeneratorFactoryWireSet,
		widget.NewInvestmentLandingInvestedNudgeWidgetGenerator,
		HomeNudgeParamsProvider,
		userPkg.WireSet,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
		InitializeQuestSdkClient,
	)
	return &nudge.Service{}
}

func InitializePromptService(conf *config.Config, genConf *genconf.Config, consentClient consent.ConsentClient, employmentClient beEmploymentPb.EmploymentClient,
	actorClient beActorPb.ActorClient, userClient beUserPb.UsersClient, vkycClient beVkycPb.VKYCClient,
	userGroupClient userGroupPb.GroupClient, kycClient kyc.KycClient, savingsClient savingsPb.SavingsClient,
	bcClient bankCustPb.BankCustomerServiceClient, feEmpClient employment.EmploymentFeClient) *prompt.Service {
	wire.Build(
		prompt.NewService,
		processors.NewIncEmpDiscrepancyProc,
		release.EvaluatorWireSet, provider.FeatureReleaseConfigProvider,
	)
	return &prompt.Service{}
}

func InitializeCreditReportService(creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	dynConf *genconf.Config) *credit_report.Service {
	wire.Build(
		CreditReportCommonConfigProvider,
		credit_report.NewCreditReportService,
	)
	return &credit_report.Service{}
}

func CreditReportCommonConfigProvider(conf *genconf.Config) *commonGenConf.CreditReportConfig {
	return conf.CreditReportConfig()
}

func RewardsFrontendMetaProvider(conf *config.Config) *config.RewardsFrontendMeta {
	return conf.RewardsFrontendMeta
}
func ScreeningConfigProvider(conf *config.Config) *config.Screening { return conf.Screening }
func ConnectedAccountConfigProvider(conf *config.Config) *config.ConnectedAccount {
	return conf.ConnectedAccount
}
func TxnCatParamsProvider(conf *config.Config) *config.TxnCatParams { return conf.TxnCatParams }
func SimilarActivityParamsProvider(conf *config.Config) *config.SimilarActivityParams {
	return conf.SimilarActivityParams
}
func CxProvider(conf *config.Config) *config.Cx { return conf.Cx }
func HomeNudgeParamsProvider(conf *config.Config) *config.HomeNudgeParams {
	return conf.HomeRevampParams.HomeNudgeParams
}

func InitializeDocumentUploadService(
	conf *genconf.Config,
	beWealthOnboardingClient woPb.WealthOnboardingClient,
	accountsManagerClient usstocksAccountPb.AccountManagerClient,
	actorClient beActorPb.ActorClient,
	savingsClient savingsPb.SavingsClient,
	accountStatementClient statementPb.AccountStatementClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	iftClient iftPb.InternationalFundTransferClient,
	celestialClient celestialPb.CelestialClient,
	salaryClient beSalaryPb.SalaryProgramClient,
) *document_upload.Service {
	wire.Build(
		usStocksConfigProvider,
		usstocks.NewUSStocksDocumentExchange,
		salaryprogram.NewSalaryProgramDocumentExchange,
		document_upload.NewDocumentExchangeFactory,
		document_upload.NewDocumentUploadService,
	)
	return &document_upload.Service{}
}

func InitializeTieringService(genConf *genconf.Config,
	beTieringClient beTieringPb.TieringClient,
	beSavingsClient savingsPb.SavingsClient,
	beActorClient beActorPb.ActorClient,
	beBankCustClient bankCustPb.BankCustomerServiceClient,
	beSalaryClient beSalaryPb.SalaryProgramClient,
	client beRewardOffersPb.RewardOffersClient,
	p2PInvestmentClient p2pPbBe.P2PInvestmentClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	usersClient beUserPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	segmentationClient segment.SegmentationServiceClient,
	provisioningClient beCard.CardProvisioningClient,
	alfredClient alfredPb.AlfredClient,
	rewardsClient beRewardsPb.RewardsGeneratorClient,
	projectionClient rewardsProjectionPb.ProjectorServiceClient,
	txnAggregateClient txnAggregatesPb.TxnAggregatesClient,
	orderClient orderSerivcePb.OrderServiceClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	eventBroker events.Broker,
	ussAccountManagerClient usstocksAccountPb.AccountManagerClient,
	rewardsFeClient rewardsFePb.RewardsClient,
	employmentClient beEmploymentPb.EmploymentClient,
	healthInsuranceClient healthinsurancePb.HealthInsuranceClient,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	feDynamicElementsClient dynamicElementsPb.DynamicElementsClient,
	fireFlyAccClientV2 fireflyV2Pb.FireflyV2Client,
	fireFlyAccClient fireflyaccpb.AccountingClient,

) *tiering.Service {
	wire.Build(
		wire.NewSet(tieringRelease.NewFeReleaseManagerService, wire.Bind(new(tieringRelease.FeManager), new(*tieringRelease.FeManagerService))),
		provider.FeatureReleaseConfigProvider,
		provider.AMBDataProviderProvider,
		provider.AMBScreenBuilderProvider,
		release.EvaluatorWireSet,
		tieringDeeplink.DeeplinkWireSet,
		tiering.NewService,
		jumpBenefits.JumpBenefitsWireset,
		feedback_flow_integration.TierMovementBasedFeedbackFlowIntegrationWireSet,
		data_collector.DataCollectorWireSet,
		cta.TieringCtaWire,
		earnedBenefits.EarnedBenefitsWireSet,
	)
	return &tiering.Service{}
}

func InitializeStoryService(genConf *genconf.Config, storyClient beStoryPb.StoryClient) *story.Service {
	wire.Build(
		story.NewService,
	)
	return &story.Service{}
}

func InitializeBankCustomerService(genConf *genconf.Config, bankCustomerClient bcPb.BankCustomerServiceClient, alfredClient alfredPb.AlfredClient, consentClient consent.ConsentClient,
	compClient compliancePb.ComplianceClient) *bank_customer.Service {
	wire.Build(
		bank_customer.NewService,
		datetime.WireDefaultTimeSet,
	)
	return &bank_customer.Service{}
}

func InitializeAlfredService(genConf *genconf.Config, alfredClient alfredPb.AlfredClient, actorClient beActorPb.ActorClient, usersClient beUserPb.UsersClient, userGroupClient userGroupPb.GroupClient) *alfred.Service {
	wire.Build(
		alfred.NewService,
		release.EvaluatorWireSet, provider.FeatureReleaseConfigProvider,
	)
	return &alfred.Service{}
}

func InitializePanService(conf *config.Config, panClient panPb.PanClient) *pan.Service {
	wire.Build(pan.NewService)
	return &pan.Service{}
}

func InitializeOTPService(
	catalogClient catalogPb.CatalogManagerClient,
	wealthOnboardingClient woPb.WealthOnboardingClient,
	rmsClient manager.RuleManagerClient,
	authClient beAuthPb.AuthClient,
	investmentAuth auth2.AuthClient,
) *otp.Service {
	wire.Build(mutual_fund.NewOneTimePurchaseHandler, mutual_fund.NewRegisterSIPHandler, mutual_fund.NewWithdrawalHandler, otp.NewService)
	return &otp.Service{}
}

func InitializeWaitlistService() *waitlistPb.Service {
	wire.Build(
		waitlistPb.NewService,
	)
	return &waitlistPb.Service{}
}

func InitializeDocsService(client alfredPb.AlfredClient, panClient panPb.PanClient, userIntelClient userIntelPb.UserIntelServiceClient, onboardingClient userOnboardingPb.OnboardingClient, palClient palBePb.PreApprovedLoanClient, celestialClient celestialPb.CelestialClient,
	broker events.Broker) *docs.Service {
	wire.Build(
		docs.NewService,
		fileUploadFactory.FileUploadFactoryWireSet,
		fileUploadManager.NewEPANFileUploadManager,
		fileUploadManager.NewITRIntimationFileUploadManager,
		fileUploadManager.NewLendingItrFileUploadManager,
	)
	return &docs.Service{}
}

func InitializeFeedbackEngineService(genConf *genconf.Config, beFeedbackEngineClient befePb.FeedbackEngineClient, actorClient beActorPb.ActorClient, usersClient beUserPb.UsersClient, userGroupClient userGroupPb.GroupClient) *feedbackEngine.Service {
	wire.Build(
		feedbackEngine.NewService,
		release.EvaluatorWireSet, provider.FeatureReleaseConfigProvider,
	)
	return &feedbackEngine.Service{}
}

func InitialiseGenieService(genConf *genconf.Config, authClient beAuthPb.AuthClient,
	userClient beUserPb.UsersClient, bcClient bankCustPb.BankCustomerServiceClient,
	kycClient kyc.KycClient, actorClient beActorPb.ActorClient, agentClient agentPb.KycAgentServiceClient,
	userGroupMappingClient userGroupPb.GroupClient, consentClient consent.ConsentClient, panClient panPb.PanClient,
	empClient employment.EmploymentClient) *feGenie.Service {
	wire.Build(
		feGenie.NewService,
		datetime.WireDefaultTimeSet,
	)
	return &feGenie.Service{}
}

func InitialiseEpfService(genConf *genconf.Config, epfClient beEpfPb.EpfClient, consentClient consent.ConsentClient, userClient beUserPb.UsersClient, actorClient beActorPb.ActorClient, client userGroupPb.GroupClient) *feEpfPb.Service {
	wire.Build(
		deeplink_builder.DeeplinkBuilderWireset,
		provider.FeatureReleaseConfigProvider,
		release.EvaluatorWireSet,
		feEpfPb.NewEpfService,
	)
	return &feEpfPb.Service{}
}

func InitialiseNetWorthService(
	genConf *genconf.Config,
	netWorthClient beNetWorthPb.NetWorthClient,
	userClient beUserPb.UsersClient,
	consentClient consent.ConsentClient,
	epfClient beEpfPb.EpfClient,
	creditReportManagerClient creditReportV2Pb.CreditReportManagerClient,
	userGroupClient userGroupPb.GroupClient,
	actorClient beActorPb.ActorClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	ordersClient mfExternalPb.MFExternalOrdersClient,
	onbClient userOnboardingPb.OnboardingClient,
	secretsFeClient secretsFePb.SecretsClient,
	userDeclarationClient userDeclarationPb.ServiceClient,
	empClient employment.EmploymentClient,
	investAnalyticsClient investment.InvestmentAnalyticsClient,
	mfCatalogManagerClient catalogPb.CatalogManagerClient,
	segmentSrvClient segmentPb.SegmentationServiceClient,
	variableGeneratorClient analyserVariablesPb.VariableGeneratorClient,
	broker events.Broker,
) *networth.Service {
	wire.Build(
		data_fetcher.WireDataFetcherSet,
		section.WireNetWorthSectionGeneratorSet,
		networthVisFactory.WireNetWorthVisualisationGeneratorFactorySet,
		factory.WireNetWorthWidgetGeneratorFactorySet,
		deeplink_builder.DeeplinkBuilderWireset,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
		provider.NetworthConfigProvider,
		insightsPkg.InsightsDobProcessorWireSet,
		networthConfig.LoadNetWorthConfig,
		NetWorthDashboardConfigProvider,
		networth.NewService,
		strategyImpl.NewDailyTracker,
		strategyImpl.NewWeeklyTracker,
		networthFromBuilder.FormBuilderFactoryWireSet,
		formbuilderFactory.FormProcessorWireSet,
		user_declaration.UserDeclarationFormBuilderFactoryWireSet,
		dataTypes.DataTypeHandlerFactoryWireSet,
		multiEditValidator.MultiEditOptionHandlerFactoryWireSet,
		inputvalidator.InputTypeHandlerFactoryWireSet,
		inputvalidator.FormInputValidatorFactoryWireSet,
		asset_dashboard.AssetDashboardGeneratorFactoryWireSet,
		datetime.WireDefaultTimeSet,
		insightsPkg.InsightsPanProcessorWireSet,
		wealthScrollable.WealthComponentGeneratorFactoryWireSet,
		wealthScrollable.AllComponentGeneratorWireSet,
		wealthScrollableDashboard.DashboardComponentViewGetterFactoryWireSet,
		wealthScrollableDashboard.AllDashboardComponentViewGettersWireSet,
		mfAnalyser.WireMFAnalyticsCollectorSet,
		dataprovider.WireMutualFundCatalogProviderSet,
		wealthLandingDashboard.WealthBuilderLandingWireSet,
	)
	return &networth.Service{}
}

func InitialisePayService(
	bmsClient bmsPb.BeneficiaryManagementClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	conf *config.Config,
	actorClient beActorPb.ActorClient,
	upiClient beUPIPb.UPIClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	accountPiClient beAccountPiPb.AccountPIRelationClient,
	savingsClient savingsPb.SavingsClient,
	genconf *genconf.Config,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	payClient payPb.PayClient,
	recurringPaymentClient beRecurringPayment.RecurringPaymentServiceClient,
	userClient user.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	segmentationClient segment.SegmentationServiceClient,
	questCacheStorage pkgTypes.QuestCacheStorage,
	questManagerClient questManagerPb.ManagerClient,
	eventBroker events.Broker,
	networthClient beNetWorthPb.NetWorthClient,
) *pay.Service {
	wire.Build(
		pay.NewService,
		fePaySearch.WireSet,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
		userPkg.WireSet,
		InitializeQuestSdkClient,
	)
	return &pay.Service{}
}

func NetWorthDashboardConfigProvider(config *networthConfig.Config) *networthFePb.NetWorthDashboardConfig {
	networthDashboardConfig, err := config.GetNetworthDashboardConfig()
	if err != nil {
		logger.Panic("failed to get networth dashboard config", zap.Error(err))
	}
	return networthDashboardConfig
}

func InitialiseSavingsAccountClosureService(conf *genconf.Config, savingsClient savingsPb.SavingsClient,
	client beAccrualPb.AccrualClient, aggregatorClient invAggrPb.InvestmentAggregatorClient,
	loanClient palBePb.PreApprovedLoanClient, rmsClient manager.RuleManagerClient,
	accountingClient ffAccountsPb.AccountingClient, fireflyClient ffBePb.FireflyClient,
	balanceClient accountBalancePb.BalanceClient, ticketClient beTicketPb.TicketClient,
	usersClient beUserPb.UsersClient, operationalStatusClient operationalStatusPb.OperationalStatusServiceClient, bcClient bankCustPb.BankCustomerServiceClient,
) *saclosure.SavingsAccountClosureService {
	wire.Build(
		savings.EvaluatorWireSet,
		orchestrator.DeeplinkOrchestratorWireSet,
		screen.FactoryWireSet,
		screen.NewBenefitsScreenBuilder,
		screen.NewFeedbackScreenBuilder,
		screen.NewAccountFreezeScreenBuilder,
		screen.NewCriteriaScreenBuilder,
		screen.NewPanDobScreenBuilder,
		screen.NewSubmitRequestScreenBuilder,
		screen.NewSubmitSuccessScreenBuilder,
		screen.NewFeedbackTicketSubmittedScreenBuilder,
		screen.NewPendingChargesScreenBuilder,
		saclosure.NewSavingsAccountClosureService,
		group.CriteriaGroupBuilderWireSet,
		item.CriteriaItemParallelExecutor,
		item.CriteriaItemFactoryWireSet,
		item.NewFiCoinsItemMaker,
		item.NewInvestmentsItemMaker,
		item.NewPreApprovedLoansItemMaker,
		item.NewAutoPayItemMaker,
		item.NewCreditCardsItemMaker,
		item.NewSaBalanceItemMaker,
		item.NewOperationalStatusItemMaker,
	)
	return &saclosure.SavingsAccountClosureService{}
}

func InitialiseIndianStocksService(dynConf *genconf.Config, connectedAccountBeClient beConnectedAccPb.ConnectedAccountClient,
	securitiesClient securities.SecuritiesClient, actorClient beActorPb.ActorClient, userClient beUserPb.UsersClient, client userGroupPb.GroupClient) *indianstocks.Service {
	wire.Build(
		dataFetcher2.DataFetcherFactoryWireset,
		dataFetcher2.EquityDataFetcherWireset,
		dataFetcher2.EtfDataFetcherWireset,
		dataFetcher2.ReitDataFetcherWireset,
		dataFetcher2.InvitDataFetcherWireset,
		connectedAccountHelper.ConnectedAccountHelperWireset,
		brokerDetails.IndiaStocksBrokerDetailsWireSet,
		filter2.IndianStocksAccountFilterWireSet,
		filter2.IndianStocksSortByFilterWireSet,
		dashboard.IndianStocksDashboardBuilderWireSet,
		deeplink_builder.DeeplinkBuilderWireset,
		datetime.WireDefaultTimeSet,
		provider.FeatureReleaseConfigProvider,
		release.EvaluatorWireSet,
		indianstocks.NewService,
	)
	return &indianstocks.Service{}
}

func usStocksConfigProvider(dynConf *genconf.Config) *genconf.USStocks {
	return dynConf.USStocks()
}

func InitializeMediaService(ocrClient ocr.OCRClient, panClient panPb.PanClient, conf *genconf.Config, onbClient userOnboardingPb.OnboardingClient) *media.Service {
	wire.Build(
		media.NewMediaService,
	)
	return &media.Service{}
}

func InitializeCXHomeService(ticketClient beTicketPb.TicketClient, dynConf *genconf.Config, actorClient beActorPb.ActorClient,
	userClient beUserPb.UsersClient, userGroupClient userGroupPb.GroupClient) *cxHome.Service {
	wire.Build(
		cxHome.NewCXHomeService,
		cxConfigProvider,
		contactUsConfigProvider,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
	)
	return &cxHome.Service{}
}

func cxConfigProvider(dynConf *genconf.Config) *genconf.Cx {
	return dynConf.Cx()
}

func InitializeKubairService(dynConf *genconf.Config, insightKubairClient insightKubairPb.InsightKubairClient, actorClient beActorPb.ActorClient, userClient beUserPb.UsersClient, userGroupClient userGroupPb.GroupClient) *kubair.Service {
	wire.Build(
		deeplink_builder.DeeplinkBuilderWireset,
		release.EvaluatorWireSet,
		provider.FeatureReleaseConfigProvider,
		kubair.NewService,
	)
	return &kubair.Service{}
}

func InitializerInsightSecretsService(
	dynConf *genconf.Config,
	userClient beUserPb.UsersClient,
	actorClient beActorPb.ActorClient,
	userGroupClient userGroupPb.GroupClient,
	epfClient beEpfPb.EpfClient,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	segmentationClient segment.SegmentationServiceClient,
	feCategoriserClient feCategorizerPb.TxnCategorizerClient,
	categorizerBeClient categorizer.TxnCategorizerClient,
	savingsClient savingsPb.SavingsClient,
	txnAggregatesClient txnAggregatesPb.TxnAggregatesClient,
	fireflyClient ffBePb.FireflyClient,
	accountingClient accounting.AccountingClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient,
	ccTxnAggClient ffPinotPb.TxnAggregatesClient,
	investAnalyticsClient investment.InvestmentAnalyticsClient,
	mfCatalogManagerClient catalogPb.CatalogManagerClient,
	networthClient beNetWorthPb.NetWorthClient,
	mfExternalClient mfExternalPb.MFExternalOrdersClient,
	redisClient types.AnalyserRedisStore,
	consentService consent.ConsentClient,
	userDeclarationClient userDeclarationPb.ServiceClient,
	nudgeClient nudgeBePb.NudgeServiceClient,
	onboardingClient userOnboardingPb.OnboardingClient,
	palClient palBePb.PreApprovedLoanClient,
	variableGeneratorClient analyserVariablesPb.VariableGeneratorClient,
) *secrets.Service {
	wire.Build(
		creditScoreAnalyserConfigProvider,
		provider.FeatureReleaseConfigProvider,
		loans.WireLendingInsightsSet,
		release.EvaluatorWireSet,
		helper2.WireAccountsHelperSet,
		helper2.WireCreditCardHelperSet,
		filter.WireFilterProcessorFactorySet,
		filterWidget.WireFilterWidgetGeneratorFactorySet,
		analyserFilterGenerator.WireFilterValueGeneratorSet,
		secretsFilter.FilterProcessorWireSet,
		deeplink_builder.DeeplinkBuilderWireset,
		dataprovider.WireMutualFundCatalogProviderSet,
		secretsBuilderFactory.SecretBuilderFactoryWireSet,
		secret_provider.SecretSummariesWireSet,
		insightsCalculator.MonthlyCompoundedInvestmentCalculatorWireSet,
		componentbuilder.ComponentBuilderFactoryWireSet,
		datetime.WireDefaultTimeSet,
		mfAnalyser.WireMFAnalyticsCollectorSet,
		types.AnalyserRedisStoreRedisClientProvider,
		cache.RedisStorageWireSet,
		epfSmsStore.EpfSMSAttributesStoreWireSet,
		networthConfig.LoadNetWorthConfig,
		secretsConfig.NewConfig,
		insightsConfigProvider,
		moneySecretsConfigProvider,
		secrets.NewService,
		strategyImpl.NewDailyTracker,
		strategyImpl.NewWeeklyTracker,
		wealthAnalyserFe.WealthAnalyserReportWireSet,
		wealthAnalyserFe.MfDataProviderWireSet,
		wealthAnalyserFe.EpfDataProviderWireSet,
		wealthAnalyserFe.AssetDataProviderWireSet,
		collectionProcessor.WireCollectionProcessorFactorySet,
		portfolioTrackerBuilder.WirePortfolioTrackerBuilderSet,
		portfolioTrackerUi.WirePortfolioTrackerFactorySet,
		assetWiseDistributionBuilder.WireAssetWiseDistributionFactorySet,
	)
	return &secrets.Service{}
}

func moneySecretsConfigProvider(conf *genconf.Config) *genconf.MoneySecretsConfig {
	return conf.MoneySecretsConfig()
}

func insightsConfigProvider(conf *genconf.Config) *genconf.InsightsParams {
	return conf.InsightsParams()
}

func VKYCCallConfigProvider(conf *genconf.Config) *genconf.VKYCCall {
	return conf.VKYCCall()
}

func InitializeVKYCCallService(dynConf *genconf.Config, vkycCallClientEpifiTech verifiPkg.VkycCallClientToOnboardingServer, vkycCallClientStockguardian verifiPkg.VkycCallClientToSGApiGatewayServer, obfuscatorClient obfuscatorPb.ObfuscatorClient, locationClient location.LocationClient) (*vkyccall.Service, error) {
	wire.Build(
		VKYCCallConfigProvider,
		verifiPkg.NewVkycCallClientWrapper,
		vkyccall.NewService,
	)
	return &vkyccall.Service{}, nil
}

func InitializeUqudoService(dynConf *genconf.Config, kycUqudoClient kycUqudoPb.UqudoClient,
	docExtClient kycDocsPb.DocExtractionClient) *uqudo.Service {
	wire.Build(
		uqudo.NewService,
	)
	return &uqudo.Service{}
}

func InitializeJourneyService(dynConf *genconf.Config, journeyClient journeyPb.JourneyServiceClient) *journey.Service {
	wire.Build(
		journey.NewService,
	)
	return &journey.Service{}
}

func InitializeInAppHelpActorActivityService(genConf *genconf.Config, cxActorActivityClient cxAaPb.ActorActivityClient, actorClient beActorPb.ActorClient, usersClient beUserPb.UsersClient, userGroupClient userGroupPb.GroupClient) *feInapphelpActorActivityService.Service {
	wire.Build(
		release.EvaluatorWireSet, provider.FeatureReleaseConfigProvider,
		feInapphelpActorActivityService.NewService,
	)
	return &feInapphelpActorActivityService.Service{}
}

func InitialiseSmsFetcherService(redisClient types.AnalyserRedisStore, consentService consent.ConsentClient, epfClient epfPb.EpfClient) *smsfetcher.Service {
	wire.Build(
		types.AnalyserRedisStoreRedisClientProvider,
		cache.RedisStorageWireSet,
		epfSmsStore.EpfSMSAttributesStoreWireSet,
		smsfetcher.NewService,
	)
	return &smsfetcher.Service{}
}

func InitialiseDataSharingService(
	dataSharingClient datasharingpb.DataSharingClient,
) *datasharing.Service {
	wire.Build(
		datasharing.NewService,
	)
	return &datasharing.Service{}
}

func InitialiseSalaryEstimationService(
	salaryEstimationClient salaryestimationpb.SalaryEstimationClient,
	consentClient consent.ConsentClient,
	eventBroker events.Broker,
) *salaryestimation.Service {
	wire.Build(
		salaryestimation.NewService,
	)
	return &salaryestimation.Service{}
}

func InitializeDigilockerService(conf *config.Config) *digilocker.Service {
	wire.Build(digilocker.NewService)
	return &digilocker.Service{}
}

func InitializeStockguardianMatrixService(conf *config.Config) *matrix.Service {
	wire.Build(matrix.NewService)
	return &matrix.Service{}
}

func InitializeTotpService(client totpPb.TotpClient) *totp.Service {
	wire.Build(totp.NewService)
	return &totp.Service{}
}

func InitializeBillpayService(billpayClient billpayPb.BillPayClient) *billpay.Service {
	wire.Build(billpay.NewService)
	return &billpay.Service{}
}
