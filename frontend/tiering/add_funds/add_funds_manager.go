//go:generate mockgen -source=add_funds_manager.go -destination=../test/mocks/mock_add_funds_manager.go -package=mocks
package add_funds

import (
	"context"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifigrpc"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/release"
)

type TieringAddFundsManager interface {
	// Get details for add funds v2 screen which are specific to tiering
	// Returns whether pitch is enabled or not and details related to tiering pitch if enabled
	GetAddFundsDetails(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersion uint32,
		uiEntryPoint feTransactionPb.UIEntryPoint, trialsResponse *beTieringPb.GetTrialDetailsResponse) (bool, *beTieringPb.TieringPitchAddFundsDetails, error)
}

type TieringAddFundsManagerService struct {
	conf            *genconf.Config
	beTieringClient beTieringPb.TieringClient
	dataCollector   data_collector.DataCollector
	releaseManager  release.FeManager
}

func NewTieringAddFundsManagerService(conf *genconf.Config, beTieringClient beTieringPb.TieringClient, dataCollector data_collector.DataCollector,
	releaseManager release.FeManager) *TieringAddFundsManagerService {
	return &TieringAddFundsManagerService{
		conf:            conf,
		beTieringClient: beTieringClient,
		dataCollector:   dataCollector,
		releaseManager:  releaseManager,
	}
}
func (s *TieringAddFundsManagerService) GetAddFundsDetails(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersion uint32,
	uiEntryPoint feTransactionPb.UIEntryPoint, trialsResponse *beTieringPb.GetTrialDetailsResponse) (bool, *beTieringPb.TieringPitchAddFundsDetails, error) {
	res, err := s.beTieringClient.GetTieringPitchV2(ctx, &beTieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		if res.GetStatus().GetCode() != uint32(beTieringPb.GetTieringPitchV2Response_DISABLED) {
			return false, nil, errors.Wrap(rpcErr, "error getting tiering pitch for add funds")
		}
	}
	tieringEnabled, releaseErr := s.releaseManager.IsTieringEnabledForActor(res, appVersion, appPlatform)
	if releaseErr != nil {
		return false, nil, errors.Wrap(releaseErr, "error in release manager")
	}
	if !tieringEnabled {
		return false, nil, nil
	}

	isPitchEnabled, addFundsDetails, getDetailsErr := s.getDetailsForAddFundsPitch(ctx, actorId, res.GetCurrentTier(), res.GetMovementDetailsList(), uiEntryPoint, res.GetActorBaseTier(), trialsResponse)
	if getDetailsErr != nil {
		return false, nil, errors.Wrap(getDetailsErr, "error getting details for add funds pitch")
	}

	return isPitchEnabled, addFundsDetails, nil
}
