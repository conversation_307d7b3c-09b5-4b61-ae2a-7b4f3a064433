package add_funds

import (
	"context"
	"math"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/kyc"
	tieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/config/genconf"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
	feTieringHelper "github.com/epifi/gamma/frontend/tiering/helper"
	tieringErrors "github.com/epifi/gamma/tiering/errors"
)

// helper method which returns whether pitch is enabled or not and the corresponding details if pitch is enabled
func (s *TieringAddFundsManagerService) getDetailsForAddFundsPitch(ctx context.Context, actorId string, currentTier tieringExtPb.Tier,
	mmtDetails []*tieringExtPb.MovementExternalDetails, uiEntryPoint feTransactionPb.UIEntryPoint, actorBaseTier tieringExtPb.Tier, trialsResponse *tieringPb.GetTrialDetailsResponse) (bool, *tieringPb.TieringPitchAddFundsDetails, error) {
	if isActorWhitelistedForAddFundsV2(s.conf, actorId) {
		return getAddFundsDetailsForWhitelistedActor()
	}

	kycLevel, kycLevelErr := s.getKycLevelForActor(ctx, actorId)
	if kycLevelErr != nil {
		return false, nil, errors.Wrap(kycLevelErr, "error getting kyc level of actor")
	}
	if kycLevel == kyc.KYCLevel_MIN_KYC {
		return false, nil, nil
	}

	if trialsResponse.GetIsEligibleForTrial() && feTieringHelper.UIEntryPointToExternalTierMap[uiEntryPoint] == trialsResponse.GetEligibleTrialTier() {
		return false, nil, nil
	}

	// check whether add funds should be pitched for the actor or not
	// checks include cool off check and max tier check
	pitchTierForActor, getPitchDecisionErr := s.pitchTierForActor(ctx, actorId, currentTier, mmtDetails)
	// extra handling is done below for cool off error
	if getPitchDecisionErr != nil && !errors.Is(getPitchDecisionErr, tieringErrors.ErrCooloff) {
		return false, nil, errors.Wrap(getPitchDecisionErr, "error deciding whether tiering should be pitched for actor or not")
	}
	if getPitchDecisionErr == nil && !pitchTierForActor {
		return false, nil, nil
	}
	// get current balance for actor
	currentBalance, getCurrentBalanceErr := s.dataCollector.GetBalance(ctx, actorId)
	if getCurrentBalanceErr != nil {
		return false, nil, errors.Wrap(getCurrentBalanceErr, "error getting current balance for actor")
	}
	// get current and upgradable tier next to it
	startTier, endTier, isRetentionPitch, err := s.getStartAndEndTiersForAddFundsPitch(ctx, actorId, currentBalance, currentTier, mmtDetails, uiEntryPoint, actorBaseTier)
	if err != nil {
		return false, nil, errors.Wrap(err, "failed to get current and next tiers for add funds pitch")
	}
	// If user is in cool off and this is not a retention pitch, do not pitch add funds
	if errors.Is(getPitchDecisionErr, tieringErrors.ErrCooloff) && !isRetentionPitch {
		return false, nil, nil
	}

	// Add funds pitch is only enabled for the below tiers
	tiersEnabledForAddFundsPitch := []tieringExtPb.Tier{tieringExtPb.Tier_TIER_FI_PLUS, tieringExtPb.Tier_TIER_FI_INFINITE, tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3}
	if !lo.Contains(tiersEnabledForAddFundsPitch, endTier) {
		return false, nil, nil
	}

	// get balance and amount details
	currentTierMinBalance, nextTierMinBalance, minAmountToAdd, suggestedAmountToAdd, balanceAmountErr := s.getBalanceAndAmountDetailsForTiers(ctx, startTier, endTier, currentBalance, mmtDetails)
	if balanceAmountErr != nil {
		return false, nil, errors.Wrap(balanceAmountErr, "error getting balance and amount details for tiers")
	}

	// return pitch details
	return true, &tieringPb.TieringPitchAddFundsDetails{
		CurrentTier: startTier, NextTier: endTier,
		CurrentTierMinAmount: currentTierMinBalance, NextTierMinAmount: nextTierMinBalance,
		CurrentBalanceAmount: currentBalance, MinAmount: minAmountToAdd, SuggestedAmount: suggestedAmountToAdd,
		IsRetentionPitch: isRetentionPitch,
	}, nil
}

func (s *TieringAddFundsManagerService) pitchTierForActor(ctx context.Context, actorId string, currentTier tieringExtPb.Tier, mmtDetails []*tieringExtPb.MovementExternalDetails) (bool, error) {
	// If the current tier is unspecified or not found, pitch is disabled
	if currentTier == tieringExtPb.Tier_TIER_UNSPECIFIED {
		return false, errors.New("current tier of actor is unspecified")
	}

	// check if user is in cool off or not
	isUserInCooloff, cooloffErr := feTieringHelper.IsUserInCoolOff(currentTier, mmtDetails)
	if cooloffErr != nil {
		return false, errors.Wrap(cooloffErr, "error determining whether user is in cooloff")
	}
	// if user is in cool off, pitch is disabled
	if isUserInCooloff {
		logger.Debug(ctx, "user is in cool off")
		return false, tieringErrors.ErrCooloff
	}

	return true, nil
}

func (s *TieringAddFundsManagerService) getStartAndEndTiersForAddFundsPitch(ctx context.Context, actorId string, currentBalance *gmoney.Money, currentTier tieringExtPb.Tier,
	movementDetails []*tieringExtPb.MovementExternalDetails, uiEntryPoint feTransactionPb.UIEntryPoint, actorBaseTier tieringExtPb.Tier) (tieringExtPb.Tier, tieringExtPb.Tier, bool, error) {
	// default values for start and end tier to be pitched
	startTier, endTier, tierFromDistanceErr := getStartAndEndTiersFromDistance(currentBalance, movementDetails, actorBaseTier)
	if tierFromDistanceErr != nil {
		return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, tierFromDistanceErr
	}

	if uiEntryPoint == feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_INFINITE {
		endTier = tieringExtPb.Tier_TIER_FI_INFINITE
	}

	if uiEntryPoint == feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PRIME {
		endTier = tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3
	}

	return getFinalStartAndEndTiers(currentTier, startTier, endTier, movementDetails)
}

// Get final start and end tiers by checking whether this is a retention pitch or not
func getFinalStartAndEndTiers(currentTier, startTier, endTier tieringExtPb.Tier, movementDetails []*tieringExtPb.MovementExternalDetails) (tieringExtPb.Tier, tieringExtPb.Tier, bool, error) {
	// get current and start tier options
	currentTierOptions, getTierOptionsErr := feTieringHelper.GetTierOptions(currentTier, movementDetails)
	if getTierOptionsErr != nil {
		return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, errors.Wrap(getTierOptionsErr, "error getting tier options for current tier")
	}
	startTierOptions, getTierOptionsErr := feTieringHelper.GetTierOptions(startTier, movementDetails)
	if getTierOptionsErr != nil {
		return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, errors.Wrap(getTierOptionsErr, "error getting tier options for start tier")
	}
	endTierOptions, getTierOptionsErr := feTieringHelper.GetTierOptions(endTier, movementDetails)
	if getTierOptionsErr != nil {
		return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, errors.Wrap(getTierOptionsErr, "error getting tier options for end tier")
	}

	minBalanceForStartTier, minBalanceForCurrentTier, minBalanceForEndTier := &gmoney.Money{CurrencyCode: "INR", Units: 0}, &gmoney.Money{CurrencyCode: "INR", Units: 0}, &gmoney.Money{CurrencyCode: "INR", Units: 0}
	minBalanceStartTierErr, minBalanceCurrentTierErr, minBalanceEndTierErr := error(nil), error(nil), error(nil)

	if !currentTier.IsBaseTier() {
		// get the min balance of current tier
		minBalanceForCurrentTier, minBalanceCurrentTierErr = feTieringHelper.GetMinBalanceFromOptions(currentTierOptions)
		if minBalanceCurrentTierErr != nil {
			return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, errors.Wrap(minBalanceCurrentTierErr, "error getting min and max balance for current tier")
		}
	}

	if !startTier.IsBaseTier() {
		// get the min balance of the start tier being pitched
		minBalanceForStartTier, minBalanceStartTierErr = feTieringHelper.GetMinBalanceFromOptions(startTierOptions)
		if minBalanceStartTierErr != nil {
			return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, errors.Wrap(minBalanceStartTierErr, "error getting min and max balance for left tier")
		}
	}

	if !endTier.IsBaseTier() {
		// get the min balance of current tier
		minBalanceForEndTier, minBalanceEndTierErr = feTieringHelper.GetMinBalanceFromOptions(endTierOptions)
		if minBalanceEndTierErr != nil {
			return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, errors.Wrap(minBalanceEndTierErr, "error getting min and max balance for right tier")
		}
	}

	// If current tier balance is greater than start tier balance
	// User needs to be shown the current tier for retention purposes
	if money.Compare(minBalanceForCurrentTier, minBalanceForStartTier) > 0 {
		// If the end tier is higher than current tier, pitch end tier
		// This should not be a retention pitch
		if money.Compare(minBalanceForEndTier, minBalanceForCurrentTier) > 0 {
			return startTier, endTier, false, nil
		}
		return startTier, currentTier, true, nil
	}

	if startTier == tieringExtPb.Tier_TIER_UNSPECIFIED {
		return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, errors.New("start tier could not be determined")
	}
	if endTier == tieringExtPb.Tier_TIER_UNSPECIFIED {
		return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, false, tieringErrors.ErrNoEligibleTiersForActor
	}
	return startTier, endTier, false, nil
}

// Gets start and end tier from distance which is the absolute difference between balances
//
//nolint:dupl
func getStartAndEndTiersFromDistance(currentBalance *gmoney.Money, movementDetails []*tieringExtPb.MovementExternalDetails, actorBaseTier tieringExtPb.Tier) (tieringExtPb.Tier, tieringExtPb.Tier, error) {
	// TODO (sayan) : change this to lowest external active FE tier
	startTier, endTier := tieringExtPb.Tier_TIER_FI_BASIC, tieringExtPb.Tier_TIER_UNSPECIFIED
	if actorBaseTier == tieringExtPb.Tier_TIER_FI_REGULAR {
		startTier = tieringExtPb.Tier_TIER_FI_REGULAR
	}

	minPositiveDistanceForStartTier, minPositiveDistanceForEndTier := &gmoney.Money{CurrencyCode: "INR", Units: math.MaxInt64, Nanos: 0}, &gmoney.Money{CurrencyCode: "INR", Units: math.MaxInt64, Nanos: 0}

	for _, detail := range movementDetails {
		// store the current tier, this will be required later while assigning the right tier
		// if detail.GetTierName() == currentTier {
		//	currentTier = detail.GetTierName()
		// }
		for _, option := range detail.GetOptions() {
			for _, action := range option.GetActions() {
				actionCriteria := action.GetActionDetails().GetCriteria()
				switch actionCriteria := actionCriteria.(type) {
				case *criteriaPb.QualifyingCriteria_Balance:
					// Find the distance between candidate tier balance and current balance
					// If the distance is +ve and is lesser than the minPositiveDistanceForStartTier, this is the nearest lower tier
					// If the distance is -ve and its absolute value is lesser than the minPositiveDistanceForEndTier, this is the nearest upper tier
					distance, subErr := money.Subtract(currentBalance, actionCriteria.Balance.GetMinBalance())
					if subErr != nil {
						return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, errors.Wrap(subErr, "error subtracting balance")
					}
					if (distance.GetUnits() > 0 || (distance.GetUnits() == 0 && distance.GetNanos() >= 0)) && money.Compare(distance, minPositiveDistanceForStartTier) == -1 {
						minPositiveDistanceForStartTier = distance
						startTier = detail.GetTierName()
					}
					if (distance.GetUnits() < 0 || (distance.GetUnits() == 0 && distance.GetNanos() < 0)) && money.Compare(money.Negate(distance), minPositiveDistanceForEndTier) == -1 {
						minPositiveDistanceForEndTier = money.Negate(distance)
						endTier = detail.GetTierName()
					}
				case *criteriaPb.QualifyingCriteria_BalanceV2:
					// Find the distance between candidate tier balance and current balance
					// If the distance is +ve and is lesser than the minPositiveDistanceForStartTier, this is the nearest lower tier
					// If the distance is -ve and its absolute value is lesser than the minPositiveDistanceForEndTier, this is the nearest upper tier
					distance, subErr := money.Subtract(currentBalance, actionCriteria.BalanceV2.GetMinBalanceForUpgrade())
					if subErr != nil {
						return tieringExtPb.Tier_TIER_UNSPECIFIED, tieringExtPb.Tier_TIER_UNSPECIFIED, errors.Wrap(subErr, "error subtracting balance")
					}
					if (distance.GetUnits() > 0 || (distance.GetUnits() == 0 && distance.GetNanos() >= 0)) && money.Compare(distance, minPositiveDistanceForStartTier) == -1 {
						minPositiveDistanceForStartTier = distance
						startTier = detail.GetTierName()
					}
					if (distance.GetUnits() < 0 || (distance.GetUnits() == 0 && distance.GetNanos() < 0)) && money.Compare(money.Negate(distance), minPositiveDistanceForEndTier) == -1 {
						minPositiveDistanceForEndTier = money.Negate(distance)
						endTier = detail.GetTierName()
					}
				}
			}
		}
	}
	return startTier, endTier, nil
}

func (s *TieringAddFundsManagerService) getBalanceAndAmountDetailsForTiers(ctx context.Context, startTier, endTier tieringExtPb.Tier, currentBalance *gmoney.Money, mmtDetails []*tieringExtPb.MovementExternalDetails) (*gmoney.Money, *gmoney.Money, *gmoney.Money, *gmoney.Money, error) {
	startTierOptions, getTierOptionsErr := feTieringHelper.GetTierOptions(startTier, mmtDetails)
	if getTierOptionsErr != nil {
		return nil, nil, nil, nil, errors.Wrap(getTierOptionsErr, "error getting tier options for current tier")
	}
	endTierOptions, getTierOptionsErr := feTieringHelper.GetTierOptions(endTier, mmtDetails)
	if getTierOptionsErr != nil {
		return nil, nil, nil, nil, errors.Wrap(getTierOptionsErr, "error getting tier options for start tier")
	}

	// get min balance for current tier
	currentTierMinBalance, getBalanceErr := feTieringHelper.GetMinBalanceFromOptions(startTierOptions)
	if getBalanceErr != nil {
		if errors.Is(getBalanceErr, feTieringErrors.ErrTierHasNoMinBalanceCriteria) {
			currentTierMinBalance = &gmoney.Money{CurrencyCode: "INR", Units: 0, Nanos: 0}
		} else {
			return nil, nil, nil, nil, errors.Wrap(getBalanceErr, "error getting min and max balance from qualifying criteria list")
		}
	}
	// get min balance for next tier
	nextTierMinBalance, getBalanceErr := feTieringHelper.GetMinBalanceFromOptions(endTierOptions)
	if getBalanceErr != nil {
		if errors.Is(getBalanceErr, tieringErrors.ErrTierHasNoMinBalanceCriteria) {
			nextTierMinBalance = &gmoney.Money{CurrencyCode: "INR", Units: 0, Nanos: 0}
		} else {
			return nil, nil, nil, nil, errors.Wrap(getBalanceErr, "error getting min and max balance from qualifying criteria list")
		}
	}

	// Find the difference in amount to next tier
	differenceToNextTier, subtractErr := money.Subtract(nextTierMinBalance, currentBalance)
	if subtractErr != nil {
		return nil, nil, nil, nil, errors.Wrap(subtractErr, "error subtracting current and next tier min balance")
	}
	// get min amount to add
	minAmountToAdd := differenceToNextTier
	// get suggested amount to add
	suggestedAmountToAdd := differenceToNextTier

	return currentTierMinBalance, nextTierMinBalance, minAmountToAdd, suggestedAmountToAdd, nil
}

func (s *TieringAddFundsManagerService) getKycLevelForActor(ctx context.Context, actorId string) (kyc.KYCLevel, error) {
	// get current kyc level of the actor
	kycLevel, kycLevelErr := s.dataCollector.GetKycLevel(ctx, actorId)
	if kycLevelErr != nil {
		return kyc.KYCLevel_UNSPECIFIED, errors.Wrap(kycLevelErr, "error getting implementation for balance collector")
	}

	return kycLevel, nil
}

func getAddFundsDetailsForWhitelistedActor() (bool, *tieringPb.TieringPitchAddFundsDetails, error) {
	zeroMoney := &gmoney.Money{CurrencyCode: "INR", Units: 0}
	suggestedAmount := &gmoney.Money{CurrencyCode: "INR", Units: 10000}
	return true, &tieringPb.TieringPitchAddFundsDetails{
		CurrentTier: tieringExtPb.Tier_TIER_FI_SALARY, NextTier: tieringExtPb.Tier_TIER_FI_SALARY,
		CurrentTierMinAmount: zeroMoney, NextTierMinAmount: zeroMoney,
		CurrentBalanceAmount: zeroMoney, MinAmount: zeroMoney, SuggestedAmount: suggestedAmount,
		IsRetentionPitch: false,
	}, nil
}

func isActorWhitelistedForAddFundsV2(conf *genconf.Config, actorId string) bool {
	for _, whitelistedActorId := range conf.AddFundsV2Params().WhitelistedActorIds() {
		if actorId == whitelistedActorId {
			return true
		}
	}
	return false
}
