// Code generated by MockGen. DO NOT EDIT.
// Source: add_funds_manager.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	common "github.com/epifi/be-common/api/typesv2/common"
	transaction "github.com/epifi/gamma/api/frontend/pay/transaction"
	tiering "github.com/epifi/gamma/api/tiering"
	gomock "github.com/golang/mock/gomock"
)

// MockTieringAddFundsManager is a mock of TieringAddFundsManager interface.
type MockTieringAddFundsManager struct {
	ctrl     *gomock.Controller
	recorder *MockTieringAddFundsManagerMockRecorder
}

// MockTieringAddFundsManagerMockRecorder is the mock recorder for MockTieringAddFundsManager.
type MockTieringAddFundsManagerMockRecorder struct {
	mock *MockTieringAddFundsManager
}

// NewMockTieringAddFundsManager creates a new mock instance.
func NewMockTieringAddFundsManager(ctrl *gomock.Controller) *MockTieringAddFundsManager {
	mock := &MockTieringAddFundsManager{ctrl: ctrl}
	mock.recorder = &MockTieringAddFundsManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTieringAddFundsManager) EXPECT() *MockTieringAddFundsManagerMockRecorder {
	return m.recorder
}

// GetAddFundsDetails mocks base method.
func (m *MockTieringAddFundsManager) GetAddFundsDetails(ctx context.Context, actorId string, appPlatform common.Platform, appVersion uint32, uiEntryPoint transaction.UIEntryPoint, trialsResponse *tiering.GetTrialDetailsResponse) (bool, *tiering.TieringPitchAddFundsDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddFundsDetails", ctx, actorId, appPlatform, appVersion, uiEntryPoint, trialsResponse)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*tiering.TieringPitchAddFundsDetails)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAddFundsDetails indicates an expected call of GetAddFundsDetails.
func (mr *MockTieringAddFundsManagerMockRecorder) GetAddFundsDetails(ctx, actorId, appPlatform, appVersion, uiEntryPoint, trialsResponse interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddFundsDetails", reflect.TypeOf((*MockTieringAddFundsManager)(nil).GetAddFundsDetails), ctx, actorId, appPlatform, appVersion, uiEntryPoint, trialsResponse)
}
