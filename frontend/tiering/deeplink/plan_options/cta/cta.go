package cta

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	gmoney "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/pkg/tiering"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	ctaExternalPb "github.com/epifi/gamma/api/frontend/tiering/external/cta"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/config/genconf"
	aaSalary "github.com/epifi/gamma/frontend/salaryprogram/aa_salary"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/data"
	"github.com/epifi/gamma/frontend/tiering/helper"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
	"github.com/epifi/gamma/tiering/tiermappings"
)

var (
	ReasonStandardTier           = "CTA not available for standard and regular tier"
	ReasonSameTier               = "CTA not available for current tier"
	ReasonNotSalaried            = "User is not salaried"
	ReasonLowerTier              = "CTA not available for lower tier"
	ReasonInsufficientFunds      = "Insufficient funds"
	ReasonInsufficientFundsGrace = "Insufficient funds, psudeo grace"
	ReasonNotEligible            = "User not eligible for upgrade"
	salaryProgramV1Deeplink      = func() *deeplinkPb.Deeplink {
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_SALARY_PROGRAM_INTRO_SCREEN,
		}
	}
)

type Response struct {
	Cta           *deeplinkPb.Cta
	CtaProperties *ctaExternalPb.CtaProperties
}

func (c *Response) GetCta() *deeplinkPb.Cta {
	if c != nil {
		return c.Cta
	}
	return nil
}

func (c *Response) GetCtaProperties() *ctaExternalPb.CtaProperties {
	if c != nil {
		return c.CtaProperties
	}
	return nil
}

// Manager manages bottom cta part of the plan option screen
type Manager interface {
	// GetCta returns bottom cta based on different conditions such as
	// grace period and whether user has sufficient funds for upgrade or not
	GetCta(ctx context.Context, data *data.PlanOptionsData) (*Response, error)
}

// Service implements Manager
type Service struct {
	gconf *genconf.Config
}

// NewCtaManagerService to be used in dependency injection
func NewCtaManagerService(
	gconf *genconf.Config,
) *Service {
	return &Service{
		gconf: gconf,
	}
}

var _ Manager = &Service{}

// GetCta returns bottom cta based on different conditions such as
// grace period and whether user has sufficient funds for upgrade or not
func (s *Service) GetCta(ctx context.Context, planOptionsData *data.PlanOptionsData) (*Response, error) {
	feTier := planOptionsData.GetFeTier()
	if feTier == beTieringExtPb.Tier_TIER_FI_BASIC || feTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
		return &Response{
			CtaProperties: &ctaExternalPb.CtaProperties{
				IsCtaAvailable:         false,
				DidTierCriteriaMet:     false,
				ReasonIfCriteriaNotMet: ReasonStandardTier,
				CtaType:                ctaExternalPb.CtaType_CTA_TYPE_UNSPECIFIED.String(),
			},
		}, nil
	}
	if planOptionsData.GetKycLevel() != kyc.KYCLevel_FULL_KYC {
		return &Response{
			Cta: s.getCtaForFullKyc(),
			CtaProperties: &ctaExternalPb.CtaProperties{
				IsCtaAvailable:         true,
				DidTierCriteriaMet:     false,
				ReasonIfCriteriaNotMet: ctaExternalPb.CriteriaNotMetReason_CRITERIA_NOT_MET_REASON_MIN_KYC.String(),
				CtaType:                ctaExternalPb.CtaType_CTA_TYPE_MIN_KYC.String(),
			},
		}, nil
	}
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS, beTieringExtPb.Tier_TIER_FI_INFINITE:
		return s.getCtaWithMinBalanceCheck(planOptionsData)
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return s.getCtaForSalaryLite(planOptionsData)
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		if planOptionsData.GetCurrentTier() == feTier {
			return &Response{
				CtaProperties: &ctaExternalPb.CtaProperties{
					IsCtaAvailable:         false,
					DidTierCriteriaMet:     false,
					ReasonIfCriteriaNotMet: ReasonSameTier,
					CtaType:                ctaExternalPb.CtaType_CTA_TYPE_UNSPECIFIED.String(),
				},
			}, nil
		}
		if planOptionsData.GetIsSalaried() {
			return &Response{
				Cta: s.getUpgradeCta(),
				CtaProperties: &ctaExternalPb.CtaProperties{
					IsCtaAvailable:     true,
					DidTierCriteriaMet: true,
					CtaType:            ctaExternalPb.CtaType_CTA_TYPE_FREE_UPGRADE.String(),
				},
			}, nil
		}
		return s.getCtaWithoutMinBalanceCheck(planOptionsData)
	default:
		if feTier.IsAaSalaryTier() {
			return s.getCtaForAaSalary(planOptionsData)
		}

	}
	return nil, fmt.Errorf("tier %s not handled", feTier.String())
}

func (s *Service) getCtaForSalaryLite(planOptionsData *data.PlanOptionsData) (*Response, error) {
	salaryDeeplink, getSalDlErr := s.getSalaryDeeplink(planOptionsData.GetAppPlatform(), planOptionsData.GetAppVersion())
	if getSalDlErr != nil {
		return nil, errors.Wrap(getSalDlErr, "error getting salary deeplink")
	}
	return &Response{
		Cta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         DeeplinkOptionFiSalaryLiteCtaText,
			Deeplink:     salaryDeeplink,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
		CtaProperties: &ctaExternalPb.CtaProperties{
			IsCtaAvailable:     true,
			DidTierCriteriaMet: true,
			CtaType:            ctaExternalPb.CtaType_CTA_TYPE_SHARE_ACCOUNT_DETAILS.String(),
		},
	}, nil
}

func (s *Service) getCtaForAaSalary(planOptionsData *data.PlanOptionsData) (*Response, error) {
	if planOptionsData.GetCurrentTier().IsAaSalaryTier() {
		return &Response{
			Cta: nil,
			CtaProperties: &ctaExternalPb.CtaProperties{
				IsCtaAvailable:         false,
				DidTierCriteriaMet:     false,
				ReasonIfCriteriaNotMet: ReasonSameTier,
				CtaType:                ctaExternalPb.CtaType_CTA_TYPE_UNSPECIFIED.String(),
			},
		}, nil
	}

	currentStage := planOptionsData.GetAaSalaryDetails().GetCurrentStage()
	var ctaText string

	switch currentStage {
	case salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_INCOME_ESTIMATED:
		{
			salaryBandList := planOptionsData.GetAmountRangeForSalaryBand()
			// salary found but does not satisfy applicable aa salary criteria for the actor
			if len(salaryBandList) == 0 {
				ctaText = DeeplinkOptionFiAaSalaryCtaText
			} else {
				cashbackPercent := aaSalary.AaSalaryBandToCashbackPercent[salaryBandList[len(salaryBandList)-1].GetSalaryBand()]
				ctaText = fmt.Sprintf(DeeplinkOptionFiAaSalaryCashbackCtaText, cashbackPercent)
			}
		}
	case salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_SALARY_COMMITTED:
		{
			committedAmount := planOptionsData.GetAaSalaryDetails().GetLatestSalaryTxnVerificationRequest().GetSalaryAmountCommitted().GetUnits()
			salaryBandList := planOptionsData.GetAmountRangeForSalaryBand()
			// salary found but does not satisfy applicable aa salary criteria for the actor
			if len(salaryBandList) == 0 {
				ctaText = DeeplinkOptionFiAaSalaryCtaText
			} else {
				var cashbackPercent string
				for i := len(salaryBandList) - 1; i >= 0; i-- {
					if committedAmount >= salaryBandList[i].GetMinAmount().GetUnits() {
						cashbackPercent = aaSalary.AaSalaryBandToCashbackPercent[salaryBandList[i].GetSalaryBand()]
						break
					}
				}
				ctaText = fmt.Sprintf(DeeplinkOptionFiAaSalaryCashbackCtaText, cashbackPercent)
			}
		}
	default:
		ctaText = DeeplinkOptionFiAaSalaryCtaText
	}

	return &Response{
		Cta: &deeplinkPb.Cta{
			Type: deeplinkPb.Cta_CUSTOM,
			Text: ctaText,
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_AA_SALARY_LANDING_SCREEN,
			},
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
		CtaProperties: &ctaExternalPb.CtaProperties{
			IsCtaAvailable:     true,
			DidTierCriteriaMet: false,
			CtaType:            ctaExternalPb.CtaType_CTA_TYPE_AA_SALARY.String(),
		},
	}, nil
}

// getCtaWithoutMinBalanceCheck returns bottom cta for tiers without minimum balance criteria
// eg: Salary
func (s *Service) getCtaWithoutMinBalanceCheck(planOptionsData *data.PlanOptionsData) (*Response, error) {
	salaryDeeplink, getSalaryDeeplinkErr := s.getSalaryDeeplink(planOptionsData.GetAppPlatform(), planOptionsData.GetAppVersion())
	if getSalaryDeeplinkErr != nil {
		return nil, errors.Wrap(getSalaryDeeplinkErr, "error fetching salary deeplink")
	}
	feTier := planOptionsData.GetFeTier()
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return &Response{
			Cta: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         DeeplinkOptionFiSalaryCtaText,
				Deeplink:     salaryDeeplink,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
			CtaProperties: &ctaExternalPb.CtaProperties{
				IsCtaAvailable:         true,
				DidTierCriteriaMet:     false,
				ReasonIfCriteriaNotMet: ReasonNotSalaried,
				CtaType:                ctaExternalPb.CtaType_CTA_TYPE_JOIN_SALARY_AND_UPGRADE.String(),
			},
		}, nil
	}
	return nil, fmt.Errorf("cta for tier %s not handles", feTier.String())
}

// getSalaryDeeplink redirects to v1 deeplink for lower app versions(app version without v2 deeplink screen)
// Otherwise redirects to any salary program screen based on user salary registration state
// ex- redirect to employer confirmation screen if user has not already selected employer.
func (s *Service) getSalaryDeeplink(appPlatform commontypes.Platform, appVersion uint32) (*deeplinkPb.Deeplink, error) {
	v2Deeplink, deeplinkErr := helper.SalaryProgramV2Deeplink()
	if deeplinkErr != nil {
		return nil, errors.Wrap(deeplinkErr, "error fetching salary program v2 deeplink")
	}
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		if appVersion < s.gconf.SalaryProgram().MinAndroidAppVersionSupportingLandingScreenRedirection() {
			return salaryProgramV1Deeplink(), nil
		}
		return v2Deeplink, nil
	case commontypes.Platform_IOS:
		if appVersion < s.gconf.SalaryProgram().MinIosAppVersionSupportingLandingScreenRedirection() {
			return salaryProgramV1Deeplink(), nil
		}
		return v2Deeplink, nil
	default:
		return nil, fmt.Errorf("platform not handled: %s", appPlatform.String())
	}
}

// getCtaWithMinBalanceCheck returns bottom cta for tiers with minimum balance criteria
// eg: Plus, Infinite
// nolint: funlen
func (s *Service) getCtaWithMinBalanceCheck(planOptionsData *data.PlanOptionsData) (*Response, error) {
	feTier := planOptionsData.GetFeTier()
	feInternalTier, feInternalTierErr := tiermappings.GetInternalTierFromExternalTier(feTier)
	if feInternalTierErr != nil {
		return nil, errors.Wrap(feInternalTierErr, "error getting internal tier from external tier")
	}
	currentTier := planOptionsData.GetCurrentTier()
	currentInternalTier, currentInternalTierErr := tiermappings.GetInternalTierFromExternalTier(currentTier)
	if currentInternalTierErr != nil {
		return nil, errors.Wrap(currentInternalTierErr, "error getting internal tier from external tier")
	}
	// no need to check if key present or not
	minBalance, ok := planOptionsData.GetMinBalanceMap()[feTier]
	if !ok {
		return nil, fmt.Errorf("min balance is not present for tier %s", feTier.String())
	}
	cmpVal := moneyPkg.Compare(planOptionsData.GetCurrentBalance(), minBalance)
	diffMoney, moneyErr := moneyPkg.Subtract(minBalance, planOptionsData.GetCurrentBalance())
	if moneyErr != nil {
		return nil, errors.Wrap(moneyErr, "error calculating diff money")
	}
	// sameTierButAddFundsNeeded previously grace
	sameTierButAddFundsNeeded := cmpVal < 0 && currentTier == feTier
	if currentInternalTier.Number() > feInternalTier.Number() || (currentTier == feTier && cmpVal >= 0) {
		return &Response{
			CtaProperties: &ctaExternalPb.CtaProperties{
				IsCtaAvailable:         false,
				DidTierCriteriaMet:     false,
				ReasonIfCriteriaNotMet: ReasonLowerTier,
				CtaType:                ctaExternalPb.CtaType_CTA_TYPE_UNSPECIFIED.String(),
			},
		}, nil
	}
	if sameTierButAddFundsNeeded {
		return &Response{
			Cta: s.getCtaForGracePeriod(diffMoney, feTier),
			CtaProperties: &ctaExternalPb.CtaProperties{
				IsCtaAvailable:         true,
				DidTierCriteriaMet:     false,
				ReasonIfCriteriaNotMet: ReasonInsufficientFundsGrace,
				CtaType:                ctaExternalPb.CtaType_CTA_TYPE_ADD_FUNDS_PSUEDO_GRACE.String(),
			},
		}, nil
	}
	if !planOptionsData.GetIsEligibleForTierUpgrade() {
		return &Response{
			Cta: s.getCtaForNotEligible(),
			CtaProperties: &ctaExternalPb.CtaProperties{
				IsCtaAvailable:         true,
				DidTierCriteriaMet:     false,
				ReasonIfCriteriaNotMet: ReasonNotEligible,
				CtaType:                ctaExternalPb.CtaType_CTA_TYPE_NOT_ELIGIBLE.String(),
			},
		}, nil
	}
	// if user have sufficient funds
	if cmpVal >= 0 {
		return &Response{
			Cta: s.getUpgradeCta(),
			CtaProperties: &ctaExternalPb.CtaProperties{
				IsCtaAvailable:     true,
				DidTierCriteriaMet: true,
				CtaType:            ctaExternalPb.CtaType_CTA_TYPE_FREE_UPGRADE.String(),
			},
		}, nil
	}
	return &Response{
		Cta: s.getCtaForInSufficientFunds(diffMoney, feTier),
		CtaProperties: &ctaExternalPb.CtaProperties{
			IsCtaAvailable:         true,
			DidTierCriteriaMet:     false,
			ReasonIfCriteriaNotMet: ReasonInsufficientFunds,
			CtaType:                ctaExternalPb.CtaType_CTA_TYPE_ADD_FUNDS_AND_UPGRADE.String(),
		},
	}, nil
}

// getCtaForGracePeriod returned when user's current tier is the one we are pitching but
// user doesn't have sufficient funds to maintain the tier
func (s *Service) getCtaForGracePeriod(diffMoney *gmoney.Money, feTier beTieringExtPb.Tier) *deeplinkPb.Cta {
	if diffMoney != nil && diffMoney.GetNanos() > 0 {
		diffMoney.Units = diffMoney.GetUnits() + 1
	}
	return &deeplinkPb.Cta{
		Type: deeplinkPb.Cta_CUSTOM,
		// Text:         fmt.Sprintf(DeeplinkOptionFiPlusGraceCtaText, moneyPkg.ToDisplayStringInIndianFormat(diffMoney, 0, true)),
		Text:         DeeplinkOptionFiPlusGraceCtaText,
		Deeplink:     tiering.GetDeeplinkForAddFunds(feTier, nil),
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
}

// getCtaForInSufficientFunds returned when user doesn't have sufficient funds
func (s *Service) getCtaForInSufficientFunds(_ *gmoney.Money, feTier beTieringExtPb.Tier) *deeplinkPb.Cta {
	return &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         GetCtaTextForInSuffFunds(feTier),
		Deeplink:     tiering.GetDeeplinkForAddFunds(feTier, nil),
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
}

// getUpgradeCta returned when user has sufficient funds
func (s *Service) getUpgradeCta() *deeplinkPb.Cta {
	return &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         DeeplinkOptionFiPlusSufFundsCtaText,
		Deeplink:     s.getDeeplinkForManualUpgrade(),
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
}

// getDeeplinkForManualUpgrade returns with a deeplink to manually upgrade
// if user has sufficient funds and eligible for upgrade
func (s *Service) getDeeplinkForManualUpgrade() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_TIER_MANUAL_UPGRADE,
		ScreenOptions: &deeplinkPb.Deeplink_TierManualUpgradeOptions{
			TierManualUpgradeOptions: &deeplinkPb.TierManualUpgradeOptions{
				ErrorDetail: s.getErrorDetailForManualUpgrade(ManualUpgradeErrLeftSubTitleText),
				Cta: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_RETRY,
					Text:         ManualUpgradeRetryCtaText,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
				},
				NoOfRetries:         s.gconf.Tiering().MaxNumberOfManualUpgradeRetries(),
				MaxRetryErrorDetail: s.getErrorDetailForManualUpgrade(ManaulUpgradeMaxRetryLeftSubTitleText),
				MaxRetryCta: &deeplinkPb.Cta{
					Text:         ManualUpgradeMaxRetryCtaText,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_DISABLED,
				},
			},
		},
	}
}

func (s *Service) getErrorDetailForManualUpgrade(leftSubtitleText string) *deeplinkPb.TierPlanBottomInfo {
	return &deeplinkPb.TierPlanBottomInfo{
		LeftTitle: commontypes.GetTextFromStringFontColourFontStyle(
			ManualUpgradeErrLeftTitleText,
			ManualUpgradeErrLeftTitleTextFontColor,
			commontypes.FontStyle_SUBTITLE_S,
		),
		LeftSubTitle: commontypes.GetTextFromStringFontColourFontStyle(
			leftSubtitleText,
			ManualUpgradeErrLeftSubTitleTextFontColor,
			commontypes.FontStyle_BODY_XS,
		),
		BackgroundColour: ManualUpgradeErrBgColor,
		RightIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  ManualUpgradeErrRightIconUrl,
			Width:     int32(ManualUpgradeErrRightIconWidth),
			Height:    int32(ManualUpgradeErrRightIconHeight),
		},
	}
}

// getCtaForNotEligible returned when user is not eligible for upgrade
func (s *Service) getCtaForNotEligible() *deeplinkPb.Cta {
	return &deeplinkPb.Cta{
		Text:         NotEligibleCtaText,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
		Status:       deeplinkPb.Cta_CTA_STATUS_DISABLED,
	}
}

// getCtaForFullKyc returns when user is in min_kyc
func (s *Service) getCtaForFullKyc() *deeplinkPb.Cta {
	vkycDeeplinkCreation, _ := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
		EntryPoint: vkyc.EntryPoint_ENTRY_POINT_TIER_ALL_PLANS,
	})
	return &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         UpgradeToFullKycCtaText,
		Deeplink:     vkycDeeplinkCreation,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
}
