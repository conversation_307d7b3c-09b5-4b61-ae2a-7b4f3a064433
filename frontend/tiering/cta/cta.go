package cta

import (
	"github.com/google/wire"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	"github.com/epifi/gamma/frontend/config/genconf"
	feTieringHelper "github.com/epifi/gamma/frontend/tiering/helper"
	tieringPkg "github.com/epifi/gamma/pkg/tiering"
)

var TieringCtaWire = wire.NewSet(NewService, wire.Bind(new(IManager), new(*Service)))

type IManager interface {
	// GetUpgradeCta returns
	// 1. Add funds deeplink if user balance is less than min balance of target tier
	// 2. Tier Upgrade deeplink if user balance already satisfies balance condition
	// 3. Salary landing page if target tier is salary / salary lite
	// 4. Aa salary landing screen if target tier is aa-salary
	GetUpgradeCta(data *UpgradeCtaData) (*deeplink.Deeplink, error)
}

func NewService(gconf *genconf.Config) *Service {
	return &Service{gconf: gconf}
}

type Service struct {
	gconf *genconf.Config
}

// nolint: dogsled
func (s *Service) GetUpgradeCta(data *UpgradeCtaData) (*deeplink.Deeplink, error) {
	if data.GetUpgradeTier().IsSalaryOrSalaryLiteTier() {
		return feTieringHelper.SalaryProgramV2Deeplink()
	}

	if feTieringHelper.IsFeTieLowerThanOrEqualToCurrentEvaluatedTier(data.GetUpgradeTier(), data.GetEvaluatedTier()) {
		// redirect to tier all plans to upgrade if the user already satisfied criterias
		return tiering.AllPlansDeeplink(data.GetUpgradeTier(), data.GetConfigParamsResp().GetIsMultipleWaysToEnterTieringEnabledForActor()), nil
	}

	// take user to add funds screen if user didn't satisfy the balance criteria
	return tieringPkg.GetDeeplinkForAddFunds(data.GetUpgradeTier(), nil), nil
}
