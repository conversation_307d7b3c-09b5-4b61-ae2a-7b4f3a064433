// nolint: funlen
package baseprovider

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	money2 "github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkpb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	uiFe "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	loansPkg "github.com/epifi/gamma/pkg/loans"
)

func (bdp *BaseDeeplinkProvider) GetLoanAccountDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, li *palBePb.LoanInfo) (*provider.GetDashboardLoanAccountDetailsResponse, error) {
	if li.GetLoanAccount().GetStatus() != palBePb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
		return nil, nil
	}
	res := &provider.GetDashboardLoanAccountDetailsResponse{}

	vendorImage, vendorImageW, vendorImageH, _ := uiFe.GetVendorImageAndText(li.GetLoanAccount().GetVendor())

	if li.GetLoanAccount().GetStatus() != palBePb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
		return nil, nil
	}

	loanOverviewDl, err := bdp.GetLoanDetailsV2Screen(li.GetLoanAccount().GetId(), lh)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("unable to get loan overview deeplink, loan_account_id: %s", li.GetLoanAccount().GetId()))
	}
	if datetime.IsBetweenTimestamp(datetime.TimestampToTime(li.GetLoanAccount().GetCreatedAt()).In(datetime.IST), time.Now().In(datetime.IST).Add(time.Hour*-24), time.Now().In(datetime.IST)) {
		res.DisbursalBanner = &palTypesPb.LoanDashboardBottomSection_SectionDivision{
			Title:        nil,
			RedirectText: nil,
			Components: []*palTypesPb.LoanDashboardBottomSection_Component{
				{
					Component: &palTypesPb.LoanDashboardBottomSection_Component_BannerWithLeftIconAndBottomRightCta{
						BannerWithLeftIconAndBottomRightCta: &palTypesPb.BannerWithLeftIconAndBottomRightCta{
							LeftIcon:                provider.GetVisualElementPng("https://epifi-icons.pointz.in/preapprovedloan/dashboard-3d-tick.png", 50, 50),
							Title:                   ui.NewITC().WithTexts(provider.GetText("Your loan has been transferred", "#313234", commontypes.FontStyle_HEADLINE_M)),
							Description:             ui.NewITC().WithTexts(provider.GetText("View details", "#00B899", commontypes.FontStyle_BUTTON_S)).WithDeeplink(loanOverviewDl),
							BottomRightCta:          nil,
							BottomRightSecondaryCta: nil,
							BgColor:                 "#FFFFFF",
						},
					},
				},
			},
		}
	}

	var nextEmiDate *date.Date
	var nextEmiAmount *money.Money
	paidEmiCount := 0
	totalEmiCount := 0
	var accountInDefaultStateBanner *palTypesPb.CardWithLineProgress_CtaRow
	var nextPaymentVisualElementIcon *commontypes.VisualElement
	if len(li.GetInstallmentInfoList()) > 0 {
		lii := li.GetInstallmentInfoList()[0]
		nextEmiAmount = lii.GetLoanInstallmentInfo().GetDetails().GetNextEmiAmount()
		nextEmiDate = lii.GetLoanInstallmentInfo().GetNextInstallmentDate()
		totalEmiCount = int(li.GetInstallmentInfoList()[0].GetLoanInstallmentInfo().GetTotalInstallmentCount())
		if accountInDefaultStateBanner = bdp.activeAccountInDefaultStateBanner(ctx, li.GetLoanAccount(), lii.GetLoanInstallmentInfo()); accountInDefaultStateBanner != nil {
			nextPaymentVisualElementIcon = provider.GetVisualElementPng("https://epifi-icons.pointz.in/preapprovedloan/pld_dashboard_red_failed_triangle.png", 13, 13)
		}
		for _, lip := range lii.GetLoanInstallmentPayouts() {
			if lip.GetStatus() == palBePb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS ||
				lip.GetStatus() == palBePb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PRE_CLOSURE {
				paidEmiCount += 1
			}
		}
	}

	progressTextColor := "#2D5E6E"
	if totalEmiCount == paidEmiCount {
		progressTextColor = "#648E4D"
	}
	loanAmountString := money2.ToDisplayStringInIndianFormat(li.GetLoanAccount().GetLoanAmountInfo().GetLoanAmount(), 0, false)
	loanEmiAmountString := money2.ToDisplayStringInIndianFormat(helper.CeilMoney(nextEmiAmount), 0, false)
	loanEmiDateString := datetime.DateToString(nextEmiDate, "2 Jan", datetime.IST)
	res.ActiveLoanCard = &palTypesPb.LoanDashboardBottomSection_Component{
		Component: &palTypesPb.LoanDashboardBottomSection_Component_CardWithLineProgress{
			CardWithLineProgress: &palTypesPb.CardWithLineProgress{
				CtaTitleRow: &palTypesPb.CardWithLineProgress_CtaRow{
					Title: ui.NewITC().WithDeeplink(loanOverviewDl).
						WithLeftVisualElement(provider.GetVisualElementPng(vendorImage, vendorImageW, vendorImageH)).WithLeftImagePadding(8),
					Cta: &ui.IconTextComponent{
						Texts:    []*commontypes.Text{provider.GetText("Loan details", "#00B899", commontypes.FontStyle_HEADLINE_S)},
						Deeplink: loanOverviewDl,
					},
					BgColor: "#FFFFFF",
				},
				Rows: []*palTypesPb.CardWithLineProgress_Row{
					{
						LeftItem: &palTypesPb.CardWithLineProgress_ColumnItem{
							Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
								Title: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Amount", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
								Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("₹ ", colors.ColorSlate, commontypes.FontStyle_HEADLINE_XS),
									commontypes.GetTextFromStringFontColourFontStyle(loanAmountString, colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
							}},
						},
						RightItem: &palTypesPb.CardWithLineProgress_ColumnItem{
							Item: &palTypesPb.CardWithLineProgress_ColumnItem_LineProgressVisualiser{LineProgressVisualiser: &palTypesPb.CardWithLineProgress_LineProgressVisualiser{
								ProgressVisualisationTitle: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("EMI progress", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
								ProgressVisualisationValue: ui.NewITC().WithTexts(provider.GetText(fmt.Sprintf("%d of %d", paidEmiCount, totalEmiCount), progressTextColor, commontypes.FontStyle_HEADLINE_S)),
								ProgressVisualisation: &palTypesPb.ProgressVisualisation{
									CurrentProgress:  int64(paidEmiCount),
									MaxProgress:      int64(totalEmiCount),
									TrackWidth:       10,
									ProgressBarWidth: 6,
									TrackColor:       &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#EFF2F6"}},
									ProgressBarColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
										Degree: 80,
										LinearColorStops: []*widget.ColorStop{
											{
												Color:          "#6294A6",
												StopPercentage: 80,
											},
											{
												Color:          "#648E4D",
												StopPercentage: 100,
											},
										},
									}}},
									VisualisationType: palTypesPb.ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_LINE,
									Visualisation: &palTypesPb.ProgressVisualisation_LineProgressVisualisation{LineProgressVisualisation: &palTypesPb.LineProgressVisualisation{
										ProgressValue: nil,
									}},
								},
							}},
						},
					},
					{
						LeftItem: &palTypesPb.CardWithLineProgress_ColumnItem{
							Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
								Title: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Upcoming payment", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
								Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("₹ ", colors.ColorSlate, commontypes.FontStyle_HEADLINE_XS),
									commontypes.GetTextFromStringFontColourFontStyle(loanEmiAmountString, colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
							}},
						},
						RightItem: &palTypesPb.CardWithLineProgress_ColumnItem{
							Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
								Title: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Next payment", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
								Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(loanEmiDateString, colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)).
									WithLeftVisualElement(nextPaymentVisualElementIcon).WithLeftImagePadding(3),
							}},
						},
					},
				},
				BgColor:      "#FFFFFF",
				CtaBottomRow: accountInDefaultStateBanner,
				// BannerWithStageProgress: &palTypesPb.CardWithLineProgress_BannerWithStageProgress{
				//	BgColor: "#F6F9FD",
				//	Title:   ui.NewITC().WithTexts(provider.GetText("Processing payment of ₹9,594", "#929599", commontypes.FontStyle_SUBTITLE_XS)),
				//	SectionTypeProgress: &palTypesPb.SectionTypeProgress{ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
				//		{
				//			Icon:               provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loans_dashboard_tick.png", 20, 20),
				//			Text:               provider.GetText("Amount debit", "#313234", commontypes.FontStyle_SUBTITLE_XS),
				//			IsCompleted:        true,
				//			FollowUpLineColour: "#00B899",
				//		},
				//		{
				//			Icon:               provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loans_dashboard_tick.png", 20, 20),
				//			Text:               provider.GetText("Amount processed", "#313234", commontypes.FontStyle_SUBTITLE_XS),
				//			IsCompleted:        true,
				//			FollowUpLineColour: "#00B899",
				//		},
				//		{
				//			Icon:               provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loans_dashboard_clock_pending.png", 20, 20),
				//			Text:               provider.GetText("Transferred", "#B2B5B9", commontypes.FontStyle_SUBTITLE_XS),
				//			IsCompleted:        false,
				//			FollowUpLineColour: "#6A6D70",
				//		},
				//	}},
				// },
			},
		},
	}
	return res, nil
}

func (bdp *BaseDeeplinkProvider) getLoanEligibilityDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, lr *palBePb.LoanRequest, lses []*palBePb.LoanStepExecution) (*provider.GetDashboardLoanApplicationDetailsResponse, error) {
	statusPollDl, err := bdp.GetApplicationStatusPollScreenDeepLink(ctx, lh, lr.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "unable to get status poll screen")
	}
	cardText := ""
	cta := ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Continue", colors.ColorSnow, commontypes.FontStyle_BUTTON_S)).WithDeeplink(statusPollDl).WithContainerBackgroundColor("#00B899").
		WithContainerPadding(6, 12, 6, 12).WithContainerCornerRadius(13)
	switch {
	case lr.GetStatus() == palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED:
		cardText = "Your last application was cancelled"
		cta = nil
	case lr.IsFailedTerminal():
		cardText = "Your last application has failed"
		cta = nil
	case lr.GetStatus() == palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION:
		cardText = "Your loan application could not be processed. Please contact support."
		cta = nil
	case len(lses) > 0 && lses[0].GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION:
		cardText = "Connect your salary account"
	default:
		cardText = "Add details and check your eligibility"
	}
	return &provider.GetDashboardLoanApplicationDetailsResponse{
		ActiveApplicationCards: &palTypesPb.LoanDashboardBottomSection_SectionDivision{
			Title: ui.NewITC().WithTexts(provider.GetText("Continue Application", "#6A6D70", commontypes.FontStyle_HEADLINE_S)),
			Components: []*palTypesPb.LoanDashboardBottomSection_Component{
				{
					Component: &palTypesPb.LoanDashboardBottomSection_Component_BannerWithLeftIconAndRightCta{
						BannerWithLeftIconAndRightCta: &palTypesPb.BannerWithLeftIconAndRightCta{
							Title:    ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(cardText, "#004E2D", commontypes.FontStyle_SUBTITLE_XS)),
							RightCta: cta,
							BgColor:  "#DCF3EE",
						},
					},
				},
			},
		},
	}, nil
}

func (bdp *BaseDeeplinkProvider) GetLoanApplicationDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, lr *palBePb.LoanRequest, lses []*palBePb.LoanStepExecution, loanOffer *palBePb.LoanOffer) (*provider.GetDashboardLoanApplicationDetailsResponse, error) {
	if lr == nil || lr.GetStatus() == palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
		return nil, nil
	}
	if lr.GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
		return bdp.getLoanEligibilityDetailsForDashboard(ctx, lh, lr, lses)
	}
	res := &provider.GetDashboardLoanApplicationDetailsResponse{
		ActiveApplicationCards: &palTypesPb.LoanDashboardBottomSection_SectionDivision{
			Title:      ui.NewITC().WithTexts(provider.GetText("Application Status", "#6A6D70", commontypes.FontStyle_HEADLINE_S)),
			Components: []*palTypesPb.LoanDashboardBottomSection_Component{},
		},
	}

	// TODO: Add group stage based logic here for consolidated view
	vendorImage, vendorImageW, vendorImageH, _ := uiFe.GetVendorImageAndText(lr.GetVendor())
	loanAmountString := money2.ToDisplayStringInIndianFormat(lr.GetDetails().GetLoanInfo().GetAmount(), 0, false)

	if len(lses) >= 1 {
		latestLse := lses[0]
		if (latestLse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL ||
			latestLse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION) &&
			latestLse.GetStatus() != palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED {
			res.GetActiveApplicationCards().Components = append(res.GetActiveApplicationCards().GetComponents(), GetLoanDisbursalBannerForDashboard(loanAmountString, lr, latestLse))
			return res, nil
		}
	}

	appDetailsFromV2, enrichedData := enrichLoanApplicationDetailsV2(lh, lr, lses)
	textAndCtaColor := ""
	switch appDetailsFromV2.GetInProgressMessage().GetBgColour() {
	case ColorLimeYellow:
		textAndCtaColor = ColorDarkOrange
		appDetailsFromV2.GetInProgressMessage().BgColour = ColorLightOrange
	case ColorMintGreen:
		textAndCtaColor = ColorDarkGreen
		appDetailsFromV2.GetInProgressMessage().BgColour = ColorLightGreen
	case ColorLightRed:
		textAndCtaColor = ColorDarkMaroon
		appDetailsFromV2.GetInProgressMessage().BgColour = ColorLightMaroon
	default:
		textAndCtaColor = ColorDarkOrange
		appDetailsFromV2.GetInProgressMessage().BgColour = ColorLightOrange
	}

	var ctaNextAction *ui.IconTextComponent
	if appDetailsFromV2.GetInProgressMessage().GetNextStep() != nil {
		ctaNextAction = ui.NewITC().WithTexts(provider.GetText(appDetailsFromV2.GetInProgressMessage().GetNextStep().GetText(), colors.ColorSnow, commontypes.FontStyle_BUTTON_XS)).
			WithDeeplink(appDetailsFromV2.GetInProgressMessage().GetNextStep().GetDeeplink()).WithContainerBackgroundColor(textAndCtaColor).
			WithContainerPadding(6, 12, 6, 12).WithContainerCornerRadius(13)
	}

	lhFe := helper.GetFeLoanHeaderByBeLoanHeader(&palBePb.LoanHeader{
		LoanProgram: lr.GetLoanProgram(),
		Vendor:      lr.GetVendor(),
	})

	var overflowItems []*ui.IconTextComponent
	if appDetailsFromV2.GetReloadCta() != nil {
		// TODO(@prasoon): Add refresh icon
	}
	if cancelOverflow := getCancelItemV2(lhFe, lr, enrichedData.GetCanCancel()); cancelOverflow != nil {
		overflowItems = append(overflowItems, cancelOverflow)
	}
	var hamburgerComponent *palTypesPb.HamburgerComponent
	if len(overflowItems) > 0 {
		hamburgerComponent = &palTypesPb.HamburgerComponent{
			// TODO(@prasoon): Add hamburger icon when available
			DisplayItem: ui.NewITC().WithLeftVisualElement(provider.GetVisualElementPng("https://epifi-icons.pointz.in/preapprovedloan/overflow-icon.png", 20, 20)),
			Options:     overflowItems,
		}
	}

	res.GetActiveApplicationCards().Components = append(res.GetActiveApplicationCards().GetComponents(), &palTypesPb.LoanDashboardBottomSection_Component{
		Component: &palTypesPb.LoanDashboardBottomSection_Component_CardWithLineProgress{
			CardWithLineProgress: &palTypesPb.CardWithLineProgress{
				CtaTitleRow: &palTypesPb.CardWithLineProgress_CtaRow{
					Title: &ui.IconTextComponent{
						LeftVisualElement: provider.GetVisualElementPng(vendorImage, vendorImageW, vendorImageH),
					},
					Cta:                     nil,
					BgColor:                 "#FFFFFF",
					RightHamburgerComponent: hamburgerComponent,
				},
				Rows:    getLoanApplicationBannerRowsForDashboard(lr, loanAmountString, loanOffer),
				BgColor: "#FFFFFF",
				CtaBottomRow: &palTypesPb.CardWithLineProgress_CtaRow{
					Title:   ui.NewITC().WithTexts(provider.GetText(appDetailsFromV2.GetInProgressMessage().GetMessage(), textAndCtaColor, commontypes.FontStyle_SUBTITLE_XS)),
					Cta:     ctaNextAction,
					BgColor: appDetailsFromV2.GetInProgressMessage().GetBgColour(),
				},
				/*
					BannerWithStageProgress: &palTypesPb.CardWithLineProgress_BannerWithStageProgress{
						BgColor: "#FFFFFF",
						Title:   nil,
						SectionTypeProgress: &palTypesPb.SectionTypeProgress{ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
							{
								Icon:               provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loans_dashboard_tick.png", 20, 20),
								Text:               provider.GetText("Complete\nKYC", "#313234", commontypes.FontStyle_SUBTITLE_XS),
								IsCompleted:        true,
								FollowUpLineColour: "#00B899",
							},
							{
								Icon:               provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loans_dashboard_tick.png", 20, 20),
								Text:               provider.GetText("Share\nDetails", "#313234", commontypes.FontStyle_SUBTITLE_XS),
								IsCompleted:        true,
								FollowUpLineColour: "#6A6D70",
							},
							{
								Icon:               provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loans_dashboard_clock_pending.png", 20, 20),
								Text:               provider.GetText("Setup\nAuto-pay", "#B2B5B9", commontypes.FontStyle_SUBTITLE_XS),
								IsCompleted:        false,
								FollowUpLineColour: "#6A6D70",
							},
							{
								Icon:        provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loans_dashboard_clock_pending.png", 20, 20),
								Text:        provider.GetText("Sign\nDocument", "#B2B5B9", commontypes.FontStyle_SUBTITLE_XS),
								IsCompleted: false,
							},
						}},
					},
				*/
			},
		},
	})
	// show another offer entry point only if current application is completed
	res.ShowOfferBannerAccordingToLseDashboardMap = lr.GetCompletedAt() != nil
	return res, nil
}

func getLoanApplicationBannerRowsForDashboard(lr *palBePb.LoanRequest, loanAmountString string, loanOffer *palBePb.LoanOffer) []*palTypesPb.CardWithLineProgress_Row {
	title1 := &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Loan Amount", "#929599", commontypes.FontStyle_HEADLINE_XS)}}
	title2 := &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Duration", "#929599", commontypes.FontStyle_HEADLINE_XS)}}
	if loanOffer.GetLoanOfferType() == palBePb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED {
		title1 = &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Max Loan Amount", "#929599", commontypes.FontStyle_HEADLINE_XS)}}
		title2 = &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Max Duration", "#929599", commontypes.FontStyle_HEADLINE_XS)}}
	}
	return []*palTypesPb.CardWithLineProgress_Row{
		{
			LeftItem: &palTypesPb.CardWithLineProgress_ColumnItem{
				Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
					Title: title1,
					Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("₹ ", colors.ColorSlate, commontypes.FontStyle_HEADLINE_XS),
						commontypes.GetTextFromStringFontColourFontStyle(loanAmountString, colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
				}},
			},
			RightItem: &palTypesPb.CardWithLineProgress_ColumnItem{
				Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
					Title: title2,
					Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%d months", lr.GetDetails().GetLoanInfo().GetTenureInMonths()),
						colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
				}},
			},
		},
	}
}

// nolint: goconst
func GetLoanDisbursalBannerForDashboard(loanAmountString string, lr *palBePb.LoanRequest, lse *palBePb.LoanStepExecution) *palTypesPb.LoanDashboardBottomSection_Component {
	vendorImage, vendorImageW, vendorImageH, vendorText := uiFe.GetVendorImageAndText(lr.GetVendor())
	var delayTitle *ui.IconTextComponent
	if datetime.IsBefore(lse.GetCreatedAt(), timestampPb.New(time.Now().In(datetime.IST).Add(time.Hour*-24))) {
		delayTitle = &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Transfer time: Sorry for the delay. You'll receive your loan amount within 24 hours.", "#929599", commontypes.FontStyle_SUBTITLE_XS)}}
	}
	completedTextColor, completedLineColor, completedIconUrl := "#313234", "#00B899", "https://epifi-icons.pointz.in/loans/loans_dashboard_tick.png"
	pendingTextColor, pendingLineColor, pendingIconUrl := "#B2B5B9", "#6A6D70", "https://epifi-icons.pointz.in/loans/loans_dashboard_clock_pending.png"
	processedProgressComponent := &palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
		Icon:               provider.GetVisualElementPng(completedIconUrl, 20, 20),
		Text:               provider.GetText("Processed", completedTextColor, commontypes.FontStyle_SUBTITLE_XS),
		IsCompleted:        true,
		FollowUpLineColour: completedLineColor,
	}
	confirmedProgressComponent := &palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
		Icon:               provider.GetVisualElementPng(pendingIconUrl, 20, 20),
		Text:               provider.GetText("Loan Confirmed", pendingTextColor, commontypes.FontStyle_SUBTITLE_XS),
		IsCompleted:        false,
		FollowUpLineColour: pendingLineColor,
	}
	transferredProgressComponent := &palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
		Icon:               provider.GetVisualElementPng(pendingIconUrl, 20, 20),
		Text:               provider.GetText("Transferred", pendingTextColor, commontypes.FontStyle_SUBTITLE_XS),
		IsCompleted:        false,
		FollowUpLineColour: pendingLineColor,
	}
	if lse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION &&
		(lse.GetStatus() == palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS ||
			lse.GetStatus() == palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS) ||
		lse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION {
		confirmedProgressComponent.Icon = provider.GetVisualElementPng(completedIconUrl, 20, 20)
		confirmedProgressComponent.Text = provider.GetText("Loan Confirmed", completedTextColor, commontypes.FontStyle_SUBTITLE_XS)
		confirmedProgressComponent.IsCompleted = true
		confirmedProgressComponent.FollowUpLineColour = completedLineColor
	}
	if lse.GetStatus() == palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
		transferredProgressComponent.Icon = provider.GetVisualElementPng(completedIconUrl, 20, 20)
		transferredProgressComponent.Text = provider.GetText("Transferred", completedTextColor, commontypes.FontStyle_SUBTITLE_XS)
		transferredProgressComponent.IsCompleted = true
		transferredProgressComponent.FollowUpLineColour = completedLineColor
	}
	return &palTypesPb.LoanDashboardBottomSection_Component{
		Component: &palTypesPb.LoanDashboardBottomSection_Component_CardWithStageProgress{
			CardWithStageProgress: &palTypesPb.CardWithStageProgress{
				VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
					Title: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Amount", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
					Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("₹ ", colors.ColorSlate, commontypes.FontStyle_HEADLINE_XS),
						commontypes.GetTextFromStringFontColourFontStyle(loanAmountString, colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
				},
				StageProgressTitle: delayTitle,
				StageProgress: &palTypesPb.SectionTypeProgress{ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
					processedProgressComponent,
					confirmedProgressComponent,
					transferredProgressComponent,
				}},
				VendorTitleTopRow: &ui.IconTextComponent{
					Texts:             []*commontypes.Text{provider.GetText(vendorText, "#6A6D70", commontypes.FontStyle_HEADLINE_XS)},
					LeftVisualElement: provider.GetVisualElementPng(vendorImage, vendorImageW, vendorImageH),
				},
			},
		},
	}
}

func getCancelItemV2(lh *palPbFeEnums.LoanHeader, req *palBePb.LoanRequest, canCancel bool) *ui.IconTextComponent {
	if canCancel {
		return ui.NewITC().WithTexts(provider.GetText("Cancel application", "#383838", commontypes.FontStyle_SUBTITLE_2)).
			WithDeeplink(&deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_PRE_APPROVED_LOAN_CANCEL_APPLICATION_SCREEN,
				ScreenOptions: &deeplinkpb.Deeplink_PreApprovedLoanCancelApplicationScreenOptions{
					PreApprovedLoanCancelApplicationScreenOptions: &deeplinkpb.PreApprovedLoanCancelApplicationScreenOptions{
						LoanReqId:  req.GetId(),
						Title:      "Cancel Application",
						IconUrl:    "https://epifi-icons.pointz.in/preapprovedloan/cancel-application.png",
						Message:    "Are you sure you want to cancel this loan application?",
						LoanHeader: lh,
					},
				},
			}).WithLeftImageUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/cancel-application-grey-cross.png", 12, 12).WithLeftImagePadding(14)
	}
	return nil
}

func (bdp *BaseDeeplinkProvider) activeAccountInDefaultStateBanner(ctx context.Context, la *palBePb.LoanAccount, lii *palBePb.LoanInstallmentInfo) *palTypesPb.CardWithLineProgress_CtaRow {
	if datetime.IsBefore(timestampPb.New(datetime.DateToTimeV2(lii.GetNextInstallmentDate(), datetime.IST).Add(time.Hour*24*time.Duration(lii.GetDetails().GetGracePeriod()))), timestampPb.New(time.Now().In(datetime.IST))) {
		var ctaItc *ui.IconTextComponent
		if loansPkg.CheckPrePayEnabled(&palPbFeEnums.LoanHeader{
			Vendor:      helper.GetPalFeVendorFromBe(la.GetVendor()),
			LoanProgram: helper.GetFeLoanProgramFromBe(la.GetLoanProgram()),
		}) {
			var dl *deeplinkPb.Deeplink
			if !IsPrePayV2Enabled(ctx, bdp.preApprovedLoanConf.PrePay().V2Config()) {
				// V1 flow
				dl = &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_PRE_PAY_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanPrePayScreenOptions{
						PreApprovedLoanPrePayScreenOptions: &deeplinkPb.PreApprovedLoanPrePayScreenOptions{
							LoanAccountId: la.GetId(),
							LoanHeader: helper.GetFeLoanHeaderByBeLoanHeader(&palBePb.LoanHeader{
								LoanProgram: la.GetLoanProgram(),
								Vendor:      la.GetVendor(),
							}),
						},
					},
				}
			} else {
				// V2 flow
				dl = &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_LOANS_REPAYMENT_METHODS_SCREEN,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&palTypesPb.LoansRepaymentMethodsScreenOptions{
						LoanHeader: helper.GetFeLoanHeaderByBeLoanHeader(&palBePb.LoanHeader{
							LoanProgram: la.GetLoanProgram(),
							Vendor:      la.GetVendor(),
						}),
						LoanAccountId:        la.GetId(),
						RepaymentDetailsType: palPbFeEnums.RepaymentDetailsType_REPAYMENT_DETAILS_OUTSTANDING,
						DefaultPrePayAmount:  types.GetFromBeMoney(helper.CeilMoney(lii.GetDetails().GetNextEmiAmount())),
					}),
				}
			}
			ctaItc = ui.NewITC().WithTexts(provider.GetText("Pay EMI", "#FFFFFF", commontypes.FontStyle_BUTTON_XS)).WithDeeplink(dl).WithContainerBackgroundColor("#6D3149").WithContainerPadding(6, 12, 6, 12).WithContainerCornerRadius(13)
		}
		return &palTypesPb.CardWithLineProgress_CtaRow{
			Title:   ui.NewITC().WithTexts(provider.GetText("Last EMI payment was unsuccessful.\nPlease make sure you have funds", "#6D3149", commontypes.FontStyle_SUBTITLE_XS)),
			Cta:     ctaItc,
			BgColor: "#F8E5EB",
		}
	}
	return nil
}
